package tv.shorthub.event.service.event.paypal;

import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.beans.factory.annotation.Autowired;
import tv.shorthub.dingtalk.service.DingTalkNotificationService;
import tv.shorthub.email.service.EmailService;
import tv.shorthub.event.constant.PayPalEventType;
import tv.shorthub.system.domain.PaypalPaymentLog;
import tv.shorthub.system.domain.PaypalSubscriptionRenewal;
import tv.shorthub.system.mapper.AppUsersMapper;
import tv.shorthub.system.service.IAppOrderInfoService;
import tv.shorthub.system.service.IPaypalPaymentLogService;
import tv.shorthub.system.service.ISysEmailLogService;
import tv.shorthub.paypal.service.PayPalPaymentService;
import tv.shorthub.system.service.IPaypalSubscriptionRenewalService;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.math.BigDecimal;
import java.util.Optional;
import tv.shorthub.dingtalk.model.NotificationExtInfo;
import tv.shorthub.event.utils.NotificationExtInfoUtil;
import tv.shorthub.system.domain.AppOrderInfo;
import tv.shorthub.system.mapper.AppOrderInfoMapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

/**
 * [订阅付款] PayPal Payment Sale Completed 事件处理器
 * <p>
 * 此处理器专门处理 v1 订阅相关的所有付款事件（包括首次和续订）。
 * 它是订阅付款通知的唯一来源，以避免与 `SUBSCRIPTION_ACTIVATED` 事件冲突。
 */
@Slf4j
@Component
public class PayPalPaymentSaleCompletedEventHandler extends BasePayPalEventHandler {

    private final DingTalkNotificationService dingTalkNotificationService;
    private final EmailService emailService;
    private final ISysEmailLogService emailLogService;
    private final IAppOrderInfoService appOrderInfoService;
    private final PayPalPaymentService payPalPaymentService;
    private final IPaypalSubscriptionRenewalService renewalService;
    private final AppOrderInfoMapper appOrderInfoMapper;

    @Autowired
    public PayPalPaymentSaleCompletedEventHandler(
            IPaypalPaymentLogService paymentLogService,
            AppUsersMapper appUsersMapper,
            DingTalkNotificationService dingTalkNotificationService,
            EmailService emailService,
            ISysEmailLogService emailLogService,
            IAppOrderInfoService appOrderInfoService,
            PayPalPaymentService payPalPaymentService,
            IPaypalSubscriptionRenewalService renewalService,
            AppOrderInfoMapper appOrderInfoMapper) {
        super(paymentLogService, appUsersMapper);
        this.dingTalkNotificationService = dingTalkNotificationService;
        this.emailService = emailService;
        this.emailLogService = emailLogService;
        this.appOrderInfoService = appOrderInfoService;
        this.payPalPaymentService = payPalPaymentService;
        this.renewalService = renewalService;
        this.appOrderInfoMapper = appOrderInfoMapper;
    }

    @Override
    public String getEventType() {
        return PayPalEventType.PAYMENT_SALE_COMPLETED.getCode();
    }

    @Override
    protected String getResourceId(JSONObject resource) {
        return resource.getString("id");
    }

    @Override
    protected String getPayerEmailFromResource(JSONObject resource) {
        return null;
    }

    @Override
    protected void process(JSONObject event, JSONObject resource) {
        JSONObject sale = resource;

        String saleId = sale.getString("id");
        String saleState = sale.getString("state");

        if (!"completed".equalsIgnoreCase(saleState)) {
            log.warn("[PayPal Sale] Sale 状态不是 'completed' (当前: {}). SaleID: {}, 事件被忽略.", saleState, saleId);
            return;
        }

        String subscriptionId = sale.getString("billing_agreement_id");
        if (StringUtils.isBlank(subscriptionId)) {
            subscriptionId = sale.getString("parent_payment");
        }

        if (StringUtils.isBlank(subscriptionId)) {
            log.error("[PayPal Sale] 事件中缺少 'billing_agreement_id' 或 'parent_payment'，无法关联到任何订阅. SaleID: {}", saleId);
            return;
        }

        Optional<PaypalPaymentLog> paymentLogOpt = findPaymentLogByPaypalOrderId(subscriptionId);

        if (!paymentLogOpt.isPresent()) {
            log.error("[PayPal Sale] 错误: 未能通过订阅ID '{}' 找到关联的支付日志. SaleID: {}", subscriptionId, saleId);
            return;
        }

        PaypalPaymentLog paymentLog = paymentLogOpt.get();
        log.info("[PayPal Sale] 找到关联支付日志. 系统订单号: {}, 订阅ID: {}, SaleID: {}", paymentLog.getOrderNo(), subscriptionId, saleId);

        log.info("[PayPal Sale] 支付完成, 开始更新订单状态. 系统订单号: {}", paymentLog.getOrderNo());
        int updatedCount = appOrderInfoService.updateOrderStatus(paymentLog.getOrderNo());
        log.info("[PayPal Sale] 订单状态更新完成. 系统订单号: {}, 更新了 {} 条记录", paymentLog.getOrderNo(), updatedCount);

        try {
            boolean isFirstPayment = isFirstPayment(subscriptionId, sale.getString("create_time"));
            triggerNotifications(paymentLog, sale, isFirstPayment);
        } catch (Exception e) {
            log.error("[PayPal Sale] 判断是否为首次付款或发送通知时出错. SaleID: {}", saleId, e);
            triggerNotifications(paymentLog, sale, false);
        }
    }

    private boolean isFirstPayment(String subscriptionId, String saleCreateTimeStr) throws Exception {
        JSONObject subscriptionDetails = payPalPaymentService.getSubscriptionDetails(subscriptionId);
        String subscriptionCreateTimeStr = subscriptionDetails.getString("create_time");

        if (StringUtils.isAnyBlank(subscriptionCreateTimeStr, saleCreateTimeStr)) {
            log.warn("[PayPal Sale] 无法比较时间，因为订阅或Sale的创建时间为空. subscriptionId: {}", subscriptionId);
            return false;
        }

        ZonedDateTime subscriptionCreateTime = ZonedDateTime.parse(subscriptionCreateTimeStr);
        ZonedDateTime saleCreateTime = ZonedDateTime.parse(saleCreateTimeStr);

        long minutesDifference = ChronoUnit.MINUTES.between(subscriptionCreateTime, saleCreateTime);

        log.info("[PayPal Sale] 订阅创建时间: {}, 付款创建时间: {}. 时间差: {} 分钟",
                subscriptionCreateTime, saleCreateTime, minutesDifference);

        return minutesDifference >= 0 && minutesDifference <= 5;
    }

    private void triggerNotifications(PaypalPaymentLog paymentLog, JSONObject sale, boolean isFirstPayment) {
        String saleId = sale.getString("id");

        // 从sale中获取create_time，如果事件时间超过24小时则不再发送通知
        String saleCreateTimeStr = sale.getString("create_time");
        if (StringUtils.isNotBlank(saleCreateTimeStr)) {
            try {
                ZonedDateTime saleCreateTime = ZonedDateTime.parse(saleCreateTimeStr);
                long hoursDifference = ChronoUnit.HOURS.between(saleCreateTime, ZonedDateTime.now(saleCreateTime.getZone()));
                if (hoursDifference > 24) {
                    log.warn("[PayPal Sale] 事件时间 (create_time: {}) 已超过24小时，跳过发送通知。SaleID: {}", saleCreateTimeStr, saleId);
                    return;
                }
            } catch (Exception e) {
                log.error("[PayPal Sale] 解析 sale create_time 出错，无法校验时间。将继续发送通知。SaleID: {}, Error: {}", saleId, e.getMessage(), e);
            }
        }

        String subscriptionId = sale.getString("billing_agreement_id");
        if (StringUtils.isBlank(subscriptionId)) {
            subscriptionId = sale.getString("parent_payment");
        }

        if (StringUtils.isBlank(subscriptionId)) {
            log.error("[PayPal Sale] 事件中缺少 'billing_agreement_id' 或 'parent_payment'，无法关联到任何订阅. SaleID: {}", saleId);
            return;
        }

        // 简化续费检查逻辑：不再依赖 paypal_subscription_renewal 表
        // 直接根据 isFirstPayment 判断发送哪种通知
        if (!isFirstPayment) {
            log.info("[PayPal Sale] 续费支付检测到，准备发送续费通知。SaleID: {}, SubscriptionID: {}", saleId, subscriptionId);
        } else {
            log.info("[PayPal Sale] 首次付款检测到，准备发送首次订阅通知。SaleID: {}", saleId);
        }

        JSONObject amountObj = sale.getJSONObject("amount");
        BigDecimal amount = amountObj.getBigDecimal("total");
        String currency = amountObj.getString("currency");

        sendDingTalkNotification(paymentLog, sale, saleId, subscriptionId, amount, currency, isFirstPayment);
        sendEmailNotification(paymentLog, sale, isFirstPayment);
    }

    private void sendDingTalkNotification(PaypalPaymentLog paymentLog, JSONObject sale, String saleId, String subscriptionId, BigDecimal amount, String currency, boolean isFirstPayment) {
        // 钉钉通知已注释，改为统一的定时任务处理
        // String userEmail = findUserInfo(paymentLog, sale).map(UserInfo::getEmail).orElse("未知");
        // String planId = paymentLog.getClientId();
        // String paymentTime = ZonedDateTime.parse(sale.getString("create_time"))
        //     .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

        // // 获取订单信息以提取扩展信息
        // NotificationExtInfo extInfo = null;
        // try {
        //     AppOrderInfo orderInfo = appOrderInfoMapper.selectOne(
        //         new QueryWrapper<AppOrderInfo>().eq("order_no", paymentLog.getOrderNo())
        //     );
        //     if (orderInfo != null) {
        //         extInfo = NotificationExtInfoUtil.extractFromOrderInfo(orderInfo);
        //     }
        // } catch (Exception e) {
        //     log.warn("[钉钉通知] 获取订单扩展信息失败. 订单号: {}", paymentLog.getOrderNo(), e);
        // }

        // try {
        //     if (isFirstPayment) {
        //         dingTalkNotificationService.sendSubscriptionSuccessNotification(
        //                 subscriptionId,
        //                 planId,
        //                 amount,
        //                 currency,
        //                 userEmail,
        //                 getEventType(),
        //                 paymentTime,
        //                 extInfo
        //         );
        //         log.info("[钉钉通知] 首次订阅付款成功通知已发送 - SaleID: {}", saleId);
        //     } else {
        //         dingTalkNotificationService.sendSubscriptionRenewalNotification(
        //                 subscriptionId,
        //                 saleId,
        //                 planId,
        //                 amount,
        //                 currency,
        //                 userEmail,
        //                 getEventType(),
        //                 paymentTime,
        //                 extInfo
        //         );
        //         log.info("[钉钉通知] 订阅续订成功通知已发送 - SaleID: {}", saleId);
        //     }
        // } catch (Exception e) {
        //     log.error("[钉钉通知] 发送订阅付款通知时出现异常. SaleID: {}", saleId, e);
        // }
    }

    private void sendEmailNotification(PaypalPaymentLog paymentLog, JSONObject sale, boolean isFirstPayment) {
        findUserInfo(paymentLog, sale).ifPresent(userInfo -> {
            try {
                String saleId = sale.getString("id");
                String planName = paymentLog.getClientId();
                BigDecimal amount = new BigDecimal(sale.getJSONObject("amount").getString("total"));

                String paymentTime = ZonedDateTime.parse(sale.getString("create_time"))
                    .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                
                log.info("[邮件通知] 用户信息找到: 邮箱={}, 姓名={}, 语言={}", userInfo.getEmail(), userInfo.getName(), userInfo.getLanguageCode());

                if (isFirstPayment) {
                     emailService.sendSubscriptionSuccessEmail(
                        userInfo.getEmail(),
                        userInfo.getName(),
                        paymentLog.getOrderNo(),
                        planName,
                        amount,
                        paymentTime,
                        userInfo.getLanguageCode(),
                        null); // 此处传递null，将使用默认URL
                } else {
                    log.info("[邮件通知] 检测到为续费付款，续费不发送邮件通知。SaleID: {}", saleId);
                }
            } catch (Exception e) {
                log.error("[邮件通知] 发送订阅邮件通知时出现未预期的异常。订单号: {}", paymentLog.getOrderNo(), e);
            }
        });
    }
}
