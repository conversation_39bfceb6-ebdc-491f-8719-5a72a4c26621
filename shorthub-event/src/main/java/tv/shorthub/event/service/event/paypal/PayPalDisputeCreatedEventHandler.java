package tv.shorthub.event.service.event.paypal;

import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import tv.shorthub.dingtalk.service.DingTalkNotificationService;
import tv.shorthub.event.constant.PayPalEventType;
import tv.shorthub.common.core.redis.RedisCache;
import tv.shorthub.system.domain.PaypalDispute;
import tv.shorthub.system.service.IPaypalDisputeService;
import tv.shorthub.paypal.service.PayPalDisputeService;
import tv.shorthub.system.service.IPaypalPaymentLogService;


@Slf4j
@Component
public class PayPalDisputeCreatedEventHandler extends BaseDisputeEventHandler {

    private final DingTalkNotificationService dingTalkNotificationService;
    private final RedisCache redisCache;

    public PayPalDisputeCreatedEventHandler(IPaypalDisputeService disputeService, DingTalkNotificationService dingTalkNotificationService, RedisCache redisCache) {
        super(disputeService);
        this.dingTalkNotificationService = dingTalkNotificationService;
        this.redisCache = redisCache;
    }

    @Override
    protected void processDispute(PaypalDispute dispute, JSONObject disputeDetail) {
        // 评估风险等级
        String riskLevel = getRiskLevel(dispute);
        log.info("争议风险等级评估 - 争议ID: {}, 风险等级: {}, 金额: {} {}, 原因: {}",
                dispute.getDisputeId(),
                riskLevel,
                dispute.getAmount(),
                dispute.getCurrency(),
                dispute.getReason());

        // 保存争议记录
        if (dispute.getId() == null) {
            disputeService.insert(dispute);
            log.info("争议记录保存成功 - 争议ID: {}", dispute.getDisputeId());
        } else {
            disputeService.update(dispute);
            log.warn("收到已存在争议的创建事件，记录已更新 - 争议ID: {}", dispute.getDisputeId());
        }

        // 检查是否在短时间内已经为这个争议发送过通知
        String eventType = getEventType();
        String lockKey = String.format("dispute:notify_lock:%s", dispute.getDisputeId());
        if (redisCache.getCacheObject(lockKey) != null) {
            log.info("[钉钉通知] 忽略重复的争议创建通知 - 争议ID: {}", dispute.getDisputeId());
            return;
        }

        // 发送钉钉通知
        try {
            dingTalkNotificationService.sendDisputeNotification(
                    "【新争议】" + riskLevel,
                    dispute.getDisputeId(),
                    getReasonInChinese(dispute.getReason()),
                    getStatusInChinese(dispute.getStatus()),
                    dispute.getAmount(),
                    dispute.getCurrency(),
                    eventType
            );
            // 标记已发送，设置一个合理的过期时间，例如5分钟
            redisCache.setCacheObject(lockKey, "sent", 5, java.util.concurrent.TimeUnit.MINUTES);
        } catch (Exception e) {
            log.error("[钉钉通知] 发送新争议通知时出现未预期的异常。争议ID: {}", dispute.getDisputeId(), e);
        }

        // 检查是否需要人工干预
        if (needsManualIntervention(dispute)) {
            String reason = getManualInterventionReason(dispute);
            log.warn("争议需要人工干预 - 争议ID: {}, 原因: {}, 风险等级: {}",
                    dispute.getDisputeId(), reason, riskLevel);
            // TODO: 后续接入钉钉后启用通知
            // sendManualInterventionNotification(dispute);
        }
    }

    @Override
    public String getEventType() {
        return PayPalEventType.DISPUTE_CREATED.getCode();
    }
}
