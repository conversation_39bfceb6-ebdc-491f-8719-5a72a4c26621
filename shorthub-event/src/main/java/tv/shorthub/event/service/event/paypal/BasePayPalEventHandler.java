package tv.shorthub.event.service.event.paypal;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import tv.shorthub.event.service.event.PayPalWebhookEventHandler;
import tv.shorthub.system.domain.AppUsers;
import tv.shorthub.system.domain.PaypalPaymentLog;
import tv.shorthub.system.mapper.AppUsersMapper;
import tv.shorthub.system.service.IPaypalPaymentLogService;

import java.util.Optional;

/**
 * PayPal事件处理器的抽象基类
 * 提供了统一的日志记录、错误处理和模板方法
 */
@Slf4j
@RequiredArgsConstructor
public abstract class BasePayPalEventHandler implements PayPalWebhookEventHandler {

    protected final IPaypalPaymentLogService paymentLogService;
    protected final AppUsersMapper appUsersMapper;

    @Getter
    public static class UserInfo {
        private final String email;
        private final String name;
        private final String languageCode;

        public UserInfo(String email, String name, String languageCode) {
            this.email = email;
            this.name = name;
            this.languageCode = languageCode;
        }
    }

    /**
     * 主处理方法模板
     */
    @Override
    public final void handle(JSONObject event) {
        String eventType = getEventType();
        JSONObject resource = event.getJSONObject("resource");
        String resourceId = getResourceId(resource);

        log.info("[PayPal Event] 开始处理 - 类型: {}, ID: {}", eventType, resourceId);
        try {
            process(event, resource);
            log.info("[PayPal Event] 处理成功 - 类型: {}, ID: {}", eventType, resourceId);
        } catch (Exception e) {
            log.error("[PayPal Event] 处理失败 - 类型: {}, ID: {}. 事件数据: {}",
                    eventType, resourceId, event.toJSONString(), e);
            // 重新抛出异常，以便重试机制能够捕获
            throw new RuntimeException("处理PayPal事件失败 " + eventType, e);
        }
    }

    /**
     * 由子类实现的具体处理逻辑
     * @param event 完整的事件对象
     * @param resource 事件的核心资源对象
     */
    protected abstract void process(JSONObject event, JSONObject resource);

    /**
     * 从资源对象中获取主ID（如OrderID, SubscriptionID等）
     * @param resource 事件的核心资源对象
     * @return 资源ID
     */
    protected abstract String getResourceId(JSONObject resource);

    /**
     * 从资源对象中获取付款人邮箱，由子类实现具体路径
     * @param resource 事件的核心资源对象
     * @return 付款人邮箱地址
     */
    protected abstract String getPayerEmailFromResource(JSONObject resource);

    /**
     * 查找用户信息，包含数据库查找和从事件回退的逻辑
     * @param paymentLog 支付日志对象，用于获取 relatedUserId
     * @param resource 事件资源对象，用于回退查找邮箱
     * @return 包含用户信息的Optional对象
     */
    protected Optional<UserInfo> findUserInfo(PaypalPaymentLog paymentLog, JSONObject resource) {
        String email = null;
        String name = "Valued User";
        String languageCode = "en-US";

        String relatedUserId = paymentLog.getRelatedUserId();
        if (StringUtils.isNotEmpty(relatedUserId)) {
            AppUsers userQuery = new AppUsers();
            userQuery.setUserId(relatedUserId);
            Optional<AppUsers> userOpt = Optional.ofNullable(appUsersMapper.selectOne(new QueryWrapper<>(userQuery)));
            if (userOpt.isPresent()) {
                AppUsers user = userOpt.get();
                email = user.getEmail();
                name = user.getUsername();
                if (StringUtils.isNotEmpty(user.getLanguageCode())) {
                    languageCode = user.getLanguageCode();
                }
            }
        }

        if (StringUtils.isEmpty(email) || !email.contains("@")) {
            String fallbackEmail = getPayerEmailFromResource(resource);

            // 二级后备：如果从resource中获取失败，则尝试从paymentLog的rawData中获取
            if (StringUtils.isEmpty(fallbackEmail) && paymentLog.getRawData() != null) {
                log.info("[PayPal Event] 从 resource 获取邮箱失败, 尝试从 paymentLog.rawData 中解析");
                JSONObject rawData = paymentLog.getRawData();
                String emailFromRawData = null;

                // 尝试 v2 结构: payer.email_address
                JSONObject payer = rawData.getJSONObject("payer");
                if (payer != null && StringUtils.isNotEmpty(payer.getString("email_address"))) {
                    emailFromRawData = payer.getString("email_address");
                }

                // 如果没找到, 尝试 v1 结构: subscriber.email_address
                if (StringUtils.isEmpty(emailFromRawData)) {
                    JSONObject subscriber = rawData.getJSONObject("subscriber");
                    if (subscriber != null && StringUtils.isNotEmpty(subscriber.getString("email_address"))) {
                        emailFromRawData = subscriber.getString("email_address");
                    }
                }

                if (StringUtils.isNotEmpty(emailFromRawData)) {
                    fallbackEmail = emailFromRawData;
                    log.info("[PayPal Event] 成功从 rawData 中解析到邮箱: {}", fallbackEmail);
                }
            }

            if (StringUtils.isNotEmpty(fallbackEmail)) {
                if (StringUtils.isEmpty(email)) {
                    log.info("[PayPal Event] 系统内未找到用户邮箱，使用PayPal事件中的后备邮箱: {}", fallbackEmail);
                } else {
                    log.info("[PayPal Event] 系统内邮箱 '{}' 无效，使用PayPal事件中的后备邮箱: {}", email, fallbackEmail);
                }
                email = fallbackEmail;
            }
        }

        if (StringUtils.isEmpty(email) || !email.contains("@")) {
            log.error("[PayPal Event] 邮件发送失败 - 无法从系统或PayPal事件中获取有效邮箱。系统订单号: {}", paymentLog.getOrderNo());
            return Optional.empty();
        }

        return Optional.of(new UserInfo(email, name, languageCode));
    }

    /**
     * 根据PayPal订单ID（非系统订单号）查找支付日志
     */
    protected Optional<PaypalPaymentLog> findPaymentLogByPaypalOrderId(String orderId) {
        if (orderId == null || orderId.isEmpty()) {
            return Optional.empty();
        }
        PaypalPaymentLog query = new PaypalPaymentLog();
        query.setPaymentId(orderId); // PayPal的OrderID存储在paymentId字段
        return paymentLogService.selectList(query).stream().findFirst();
    }

    /**
     * 根据系统订单号查找支付日志
     */
    protected Optional<PaypalPaymentLog> findPaymentLogByOrderNo(String orderNo) {
        if (orderNo == null || orderNo.isEmpty()) {
            return Optional.empty();
        }
        PaypalPaymentLog query = new PaypalPaymentLog();
        query.setOrderNo(orderNo);
        return paymentLogService.selectList(query).stream().findFirst();
    }
}
