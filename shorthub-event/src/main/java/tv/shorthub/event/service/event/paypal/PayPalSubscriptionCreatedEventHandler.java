package tv.shorthub.event.service.event.paypal;

import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import tv.shorthub.event.constant.PayPalEventType;
import tv.shorthub.system.mapper.AppUsersMapper;
import tv.shorthub.system.service.IAppOrderInfoService;
import tv.shorthub.system.service.IPaypalPaymentLogService;

/**
 * PayPal 订阅创建事件处理器
 * 只记录日志，不做其他操作
 */
@Slf4j
@Component
public class PayPalSubscriptionCreatedEventHandler extends BasePayPalEventHandler {
    IAppOrderInfoService appOrderInfoService;
    public PayPalSubscriptionCreatedEventHandler(IPaypalPaymentLogService paymentLogService, AppUsersMapper appUsersMapper) {
        super(paymentLogService, appUsersMapper
        );
    }


    @Override
    protected void process(JSONObject event, JSONObject resource) {
        String subscriptionId = getResourceId(resource);
        String planId = resource.getString("plan_id");
        String status = resource.getString("status");

        JSONObject subscriber = resource.getJSONObject("subscriber");
        String subscriberEmail = (subscriber != null) ? subscriber.getString("email_address") : "未知";

        log.info("[PayPal Event] 详情 - 类型: {}, ID: {}. 状态: {}, 计划ID: {}, 订阅者: {}",
                getEventType(), subscriptionId, status, planId, subscriberEmail);
    }

    @Override
    protected String getResourceId(JSONObject resource) {
        return resource.getString("id");
    }


    @Override
    protected String getPayerEmailFromResource(JSONObject resource) {
        // 从 subscriber 对象中提取邮箱地址
        JSONObject subscriber = resource.getJSONObject("subscriber");
        if (subscriber != null) {
            return subscriber.getString("email_address");
        }
        return null;
    }

    @Override
    public String getEventType() {
        return PayPalEventType.SUBSCRIPTION_CREATED.getCode();
    }
}
