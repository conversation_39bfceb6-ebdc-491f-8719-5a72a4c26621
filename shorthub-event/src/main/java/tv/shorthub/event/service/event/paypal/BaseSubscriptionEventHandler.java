package tv.shorthub.event.service.event.paypal;

import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import tv.shorthub.event.constant.PayPalEventType;
import tv.shorthub.system.domain.PaypalPaymentLog;
import tv.shorthub.system.mapper.AppUsersMapper;
import tv.shorthub.system.service.IAppOrderInfoService;
import tv.shorthub.system.service.IPaypalPaymentLogService;

import java.util.List;
import java.util.Optional;

@Slf4j
public abstract class BaseSubscriptionEventHandler extends BasePayPalEventHandler {

    protected final IAppOrderInfoService appOrderInfoService;

    public BaseSubscriptionEventHandler(IPaypalPaymentLogService paymentLogService,
                                        AppUsersMapper appUsersMapper,
                                        IAppOrderInfoService appOrderInfoService) {
        super(paymentLogService, appUsersMapper);
        this.appOrderInfoService = appOrderInfoService;
    }

    @Override
    protected void process(JSONObject event, JSONObject resource) {
        String subscriptionId = getResourceId(resource);
        String status = resource.getString("status");
        String orderNo = resource.getString("custom_id");

        log.info("[PayPal Event] 详情 - 类型: {}, ID: {}. 系统订单号: {}, 状态: {}",
                getEventType(), subscriptionId, orderNo, status);

        // 优先使用 custom_id (系统订单号) 查找
        Optional<PaypalPaymentLog> paymentLogOpt = findPaymentLogByOrderNo(orderNo);

        // 如果找不到，再尝试通过 subscriptionId (payment_id) 查找
        if (!paymentLogOpt.isPresent()) {
            log.warn("[PayPal Event] 警告 - 类型: {}, ID: {}. 未通过系统订单号找到记录, 尝试通过订阅ID查找.",
                    getEventType(), subscriptionId);
            paymentLogOpt = findPaymentLogByPaypalOrderId(subscriptionId);
            if (paymentLogOpt.isPresent()) {
                orderNo = paymentLogOpt.get().getOrderNo();
                log.info("[PayPal Event] 通过订阅ID查到订单号: {}", orderNo);
            }
        }

        // 如果找不到，并且事件中包含 billing_agreement_id，做最后一次尝试
        if (!paymentLogOpt.isPresent() && resource.containsKey("billing_agreement_id")) {
            String billingAgreementId = resource.getString("billing_agreement_id");
            log.warn("[PayPal Event] 警告 - 类型: {}, ID: {}. 尝试通过 billing_agreement_id {} 查找.",
                    getEventType(), subscriptionId, billingAgreementId);
            paymentLogOpt = findPaymentLogByPaypalOrderId(billingAgreementId);
             if (paymentLogOpt.isPresent()) {
                orderNo = paymentLogOpt.get().getOrderNo();
                log.info("[PayPal Event] 通过 billing_agreement_id 查到订单号: {}", orderNo);
            }
        }

        if (!paymentLogOpt.isPresent()) {
            log.error("[PayPal Event] 错误 - 类型: {}, ID: {}. 未能通过任何已知ID找到关联的支付日志. 该事件将被忽略.",
                getEventType(), subscriptionId);
            return;
        }

        PaypalPaymentLog paymentLog = paymentLogOpt.get();

        // 首先，执行特定于事件类型的增强逻辑 (例如，为 'ACTIVATED' 事件创建订阅记录)
        enhancePaymentLog(paymentLog, resource);
        
        // 然后，统一调用 updateOrderStatus 从PayPal同步最新状态，确保数据一致性
        log.info("[PayPal Event] 开始为事件 {}调用 updateOrderStatus - ID: {}", getEventType(), subscriptionId);
        int updatedCount = appOrderInfoService.updateOrderStatus(paymentLog.getOrderNo());
        log.info("[PayPal Event] 详情 - 类型: {}, ID: {}. updateOrderStatus 调用结果: 更新了 {} 条记录",
                getEventType(), subscriptionId, updatedCount);

        // 最后，触发通知（邮件、钉钉等）
        triggerNotification(paymentLog, resource);
    }

    @Override
    protected String getResourceId(JSONObject resource) {
        return resource.getString("id");
    }

    @Override
    protected String getPayerEmailFromResource(JSONObject resource) {
        JSONObject subscriber = resource.getJSONObject("subscriber");
        if (subscriber != null) {
            return subscriber.getString("email_address");
        }
        return null;
    }

    protected void enhancePaymentLog(PaypalPaymentLog paymentLog, JSONObject resource) {
        // 默认无操作
    }

    protected void triggerNotification(PaypalPaymentLog paymentLog, JSONObject resource) {
        // 默认无操作
    }
}
