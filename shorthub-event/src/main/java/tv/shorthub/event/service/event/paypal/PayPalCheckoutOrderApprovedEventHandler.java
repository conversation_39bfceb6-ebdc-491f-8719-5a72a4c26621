package tv.shorthub.event.service.event.paypal;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import tv.shorthub.event.constant.PayPalEventType;
import tv.shorthub.system.domain.PaypalPaymentLog;
import tv.shorthub.system.mapper.AppUsersMapper;
import tv.shorthub.system.service.IPaypalPaymentLogService;
import java.math.BigDecimal;
import java.util.Optional;
import tv.shorthub.paypal.constant.PayPalConstants.PaymentStatus;
import tv.shorthub.system.service.IAppUsersService;
import tv.shorthub.system.service.IAppOrderInfoService;
import tv.shorthub.system.mapper.AppOrderInfoMapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import java.util.Date;
import tv.shorthub.system.domain.AppOrderInfo;

@Slf4j
@Component
/**
 * 处理PayPal checkout订单批准事件的处理器
 * 该处理器专门处理用户批准订单后的支付流程
 */
public class PayPalCheckoutOrderApprovedEventHandler extends BasePayPalEventHandler {

    private final AppOrderInfoMapper appOrderInfoMapper;

    /**
     * 构造函数注入所需的依赖服务
     *
     * @param paymentLogService 支付日志服务，用于操作支付记录
     * @param appUsersMapper 用户信息Mapper，用于获取用户详细信息
     */
    public PayPalCheckoutOrderApprovedEventHandler(
            IPaypalPaymentLogService paymentLogService,
            AppUsersMapper appUsersMapper,
            AppOrderInfoMapper appOrderInfoMapper) {
        super(paymentLogService, appUsersMapper);
        this.appOrderInfoMapper = appOrderInfoMapper;
    }

    @Autowired
    IAppOrderInfoService appOrderInfoService;
    @Override
    protected void process(JSONObject event, JSONObject resource) {
        String orderId = resource.getString("id");

        // 获取支付捕获信息
        JSONArray purchaseUnits = resource.getJSONArray("purchase_units");
        if (purchaseUnits == null || purchaseUnits.isEmpty()) {
            log.error("[PayPal Event] 错误 - 类型: {}, ID: {}. purchase_units 为空.", getEventType(), orderId);
            return;
        }

        JSONObject purchaseUnit = purchaseUnits.getJSONObject(0);
        JSONObject payments = purchaseUnit.getJSONObject("payments");
        if (payments == null) {
            log.warn("[PayPal Event] 警告 - 类型: {}, ID: {}. Payments 对象为空, 可能尚未捕获.", getEventType(), orderId);
            return;
        }

        JSONArray captures = payments.getJSONArray("captures");
        if (captures == null || captures.isEmpty()) {
            log.warn("[PayPal Event] 警告 - 类型: {}, ID: {}. Captures 数组为空.", getEventType(), orderId);
            return;
        }

        JSONObject capture = captures.getJSONObject(0);
        String captureId = capture.getString("id");
        String captureStatus = capture.getString("status");
        JSONObject amount = capture.getJSONObject("amount");
        String value = amount.getString("value");

        log.info("[PayPal Event] 详情 - 类型: {}, ID: {}. 捕获信息: CaptureID={}, 状态={}, 金额={}",
            getEventType(), orderId, captureId, captureStatus, value);

        // 查找支付日志
        Optional<PaypalPaymentLog> paymentLogOpt = findPaymentLogByPaypalOrderId(orderId);
        if (!paymentLogOpt.isPresent()) {
            log.error("[PayPal Event] 错误 - 类型: {}, ID: {}. 未找到支付日志.", getEventType(), orderId);
            return;
        }

        PaypalPaymentLog paymentLogOrderNo = paymentLogOpt.get();
        // updateOrderStatus 更新状态
        appOrderInfoService.updateOrderStatus(paymentLogOrderNo.getOrderNo());
        log.info("[PayPal Event] updateOrderStatus 调用完成 - SubscriptionID: {}", orderId);

    }

    @Override
    protected String getResourceId(JSONObject resource) {
        return resource.getString("id");
    }

    @Override
    protected String getPayerEmailFromResource(JSONObject resource) {
        // 对于订单批准事件，payer信息在顶层
        JSONObject payer = resource.getJSONObject("payer");
        if (payer != null) {
            return payer.getString("email_address");
        }
        return null;
    }

    @Override
    public String getEventType() {
        return PayPalEventType.CHECKOUT_ORDER_APPROVED.getCode();
    }
}
