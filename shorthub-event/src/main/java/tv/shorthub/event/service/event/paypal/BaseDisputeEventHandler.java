package tv.shorthub.event.service.event.paypal;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import tv.shorthub.event.service.event.PayPalWebhookEventHandler;
import tv.shorthub.paypal.config.PaypalConfigStorageHolder;
import tv.shorthub.paypal.constant.PayPalConstants.DisputeType;
import tv.shorthub.paypal.service.PayPalDisputeService;
import tv.shorthub.system.domain.PaypalDispute;
import tv.shorthub.system.domain.PaypalPaymentLog;
import tv.shorthub.system.service.IPaypalDisputeService;
import tv.shorthub.system.service.IPaypalPaymentLogService;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * PayPal争议事件处理器的抽象基类
 * 提供了处理争议事件的通用模板和工具方法
 */
@Slf4j
public abstract class BaseDisputeEventHandler implements PayPalWebhookEventHandler {

    protected final IPaypalDisputeService disputeService;
    private static final Map<String, String> STATUS_MAP = new HashMap<>();
    private static final Map<String, String> REASON_MAP = new HashMap<>();

    public BaseDisputeEventHandler(IPaypalDisputeService disputeService) {
        this.disputeService = disputeService;
    }

    static {
        // 争议状态中文映射
        STATUS_MAP.put("OPEN", "开启");
        STATUS_MAP.put("WAITING_FOR_SELLER_RESPONSE", "等待卖家回应");
        STATUS_MAP.put("WAITING_FOR_BUYER_RESPONSE", "等待买家回应");
        STATUS_MAP.put("UNDER_REVIEW", "审查中");
        STATUS_MAP.put("RESOLVED", "已解决");
        STATUS_MAP.put("OTHER", "其他");
        STATUS_MAP.put("CHARGEBACK", "退单");

        // 争议原因中文映射
        REASON_MAP.put("MERCHANDISE_OR_SERVICE_NOT_RECEIVED", "未收到商品或服务");
        REASON_MAP.put("MERCHANDISE_OR_SERVICE_NOT_AS_DESCRIBED", "商品或服务与描述不符");
        REASON_MAP.put("UNAUTHORISED", "未经授权的交易");
        REASON_MAP.put("CREDIT_NOT_PROCESSED", "信用未处理");
        REASON_MAP.put("DUPLICATE_TRANSACTION", "重复交易");
        REASON_MAP.put("INCORRECT_AMOUNT", "金额不正确");
        REASON_MAP.put("PAYMENT_BY_OTHER_MEANS", "通过其他方式付款");
        REASON_MAP.put("CANCELED_RECURRING_BILLING", "取消了定期付款");
        REASON_MAP.put("PROBLEM_WITH_REMITTANCE", "汇款问题");
        REASON_MAP.put("OTHER", "其他");
    }

    /**
     * 主处理方法模板
     */
    @Override
    public final void handle(JSONObject event) {
        String eventType = getEventType();
        JSONObject resource = event.getJSONObject("resource");

        String disputeId = getResourceId(resource);
        log.info("[PayPal Dispute] 开始处理 - 类型: {}, ID: {}", eventType, disputeId);

        try {
            // 尝试从数据库加载现有争议记录
            PaypalDispute query = new PaypalDispute();
            query.setDisputeId(disputeId);
            List<PaypalDispute> results = disputeService.selectList(query);
            PaypalDispute existingDispute = results.isEmpty() ? null : results.get(0);

            PaypalDispute dispute = convertToDispute(resource, existingDispute);

            // 关键步骤：查找并设置关联的系统订单号
            findAndSetOrderNo(dispute);

            processDispute(dispute, resource);
            log.info("[PayPal Dispute] 处理成功 - 类型: {}, ID: {}", eventType, disputeId);
        } catch (Exception e) {
            log.error("[PayPal Dispute] 处理失败 - 类型: {}, ID: {}. 事件数据: {}",
                    eventType, disputeId, event.toJSONString(), e);
            throw new RuntimeException("处理PayPal争议事件失败: " + eventType, e);
        }
    }

    /**
     * 由子类实现的具体处理逻辑
     * @param dispute 转换后的争议对象
     * @param resource Webhook的原始resource对象
     */
    protected abstract void processDispute(PaypalDispute dispute, JSONObject resource);

    public String getResourceId(JSONObject resource) {
        return resource.getString("dispute_id");
    }

    /**
     * 根据争议信息中的Capture ID，查找并设置关联的系统订单号
     * @param dispute 争议对象
     */
    private void findAndSetOrderNo(PaypalDispute dispute) {
        if (StringUtils.isNotEmpty(dispute.getOrderNo())) {
            log.info("[PayPal Dispute] 系统订单号已存在，跳过查找. DisputeID: {}, OrderNo: {}", dispute.getDisputeId(), dispute.getOrderNo());
            return;
        }

        String captureId = dispute.getPaymentId();
        if (StringUtils.isBlank(captureId)) {
            log.warn("[PayPal Dispute] 无法查找订单号，因为争议信息中缺少支付ID (Capture ID). DisputeID: {}", dispute.getDisputeId());
            return;
        }

        // 通过 disputeService 来执行查询，该查询会访问 paypal_payment_log 表
        String orderNo = disputeService.findOrderNoByCaptureId(captureId);

        if (StringUtils.isNotEmpty(orderNo)) {
            log.info("[PayPal Dispute] 成功找到关联的系统订单号. DisputeID: {}, CaptureID: {}, OrderNo: {}",
                    dispute.getDisputeId(), captureId, orderNo);
            dispute.setOrderNo(orderNo);
        } else {
            log.warn("[PayPal Dispute] 未能通过Capture ID '{}' 找到任何关联的支付日志.", captureId);
        }
    }

    /**
     * 将从PayPal API获取的JSON对象转换为系统内的PaypalDispute实体
     * @param resource 完整的争议详情JSON对象
     * @param existingDispute 数据库中已存在的争议记录，可能为null
     * @return PaypalDispute 实体
     */
    protected PaypalDispute convertToDispute(JSONObject resource, PaypalDispute existingDispute) {
        PaypalDispute dispute = existingDispute != null ? existingDispute : new PaypalDispute();

        if (dispute.getId() == null) { // 只在新建时设置这些值
            dispute.setDisputeId(resource.getString("dispute_id"));
            dispute.setCreateTime(new Date());
        }

        dispute.setStatus(resource.getString("status"));
        dispute.setReason(resource.getString("reason"));
        dispute.setUpdateTime(new Date());
        dispute.setDisputeType(resource.getString("dispute_life_cycle_stage"));


        // 从 dispute_amount 中获取金额和货币
        JSONObject disputeAmount = resource.getJSONObject("dispute_amount");
        if (disputeAmount != null) {
            dispute.setAmount(disputeAmount.getBigDecimal("value"));
            dispute.setCurrency(disputeAmount.getString("currency_code"));
        } else if (dispute.getAmount() == null) { // 只有在原金额为空时才尝试从后备字段获取
            // 作为后备，尝试从 transactions 中获取
            JSONArray transactions = resource.getJSONArray("disputed_transactions");
            if (transactions != null && !transactions.isEmpty()) {
                JSONObject transaction = transactions.getJSONObject(0);
                JSONObject sellerAmount = transaction.getJSONObject("seller_amount");
                if (sellerAmount != null) {
                    dispute.setAmount(sellerAmount.getBigDecimal("value"));
                    dispute.setCurrency(sellerAmount.getString("currency_code"));
                }
            }
        }
        
        // 尝试从disputed_transactions中获取各种ID
        JSONArray transactions = resource.getJSONArray("disputed_transactions");
        if (transactions != null && !transactions.isEmpty()) {
            JSONObject transaction = transactions.getJSONObject(0);
            if (transaction != null) {
                // 设置 payment_id (如果为空)
                if (StringUtils.isBlank(dispute.getPaymentId())) {
                    dispute.setPaymentId(transaction.getString("seller_transaction_id"));
                }

                // 设置 buyer_id (如果为空), 使用 buyer_transaction_id 作为后备
                if (StringUtils.isBlank(dispute.getBuyerId())) {
                    dispute.setBuyerId(transaction.getString("buyer_transaction_id"));
                }

                // 设置 seller_id (如果为空)
                if (StringUtils.isBlank(dispute.getSellerId())) {
                    JSONObject seller = transaction.getJSONObject("seller");
                    if(seller != null) {
                        dispute.setSellerId(seller.getString("merchant_id"));
                    }
                }
            }
        }

        dispute.setRawData(resource);
        return dispute;
    }

    /**
     * 获取争议状态的中文翻译
     * @param status 英文状态
     * @return 中文状态或原始状态
     */
    protected String getStatusInChinese(String status) {
        return STATUS_MAP.getOrDefault(status, status);
    }

    /**
     * 获取争议原因的中文翻译
     * @param reason 英文原因
     * @return 中文原因或原始原因
     */
    protected String getReasonInChinese(String reason) {
        return REASON_MAP.getOrDefault(reason, reason);
    }

    /**
     * 评估争议的风险等级
     * @param dispute 争议对象
     * @return " 高风险", " 中风险", " 低风险"
     */
    protected String getRiskLevel(PaypalDispute dispute) {
        if (isHighRiskDispute(dispute.getDisputeType(), dispute.getReason())) {
            return "高风险";
        } else if (dispute.getAmount() != null && dispute.getAmount().compareTo(new BigDecimal("100")) > 0) {
            return "中风险";
        } else {
            return "低风险";
        }
    }

    /**
     * 判断是否为高风险争议
     * @param disputeType 争议类型
     * @param reason 争议原因
     * @return 如果是高风险则返回 true
     */
    protected boolean isHighRiskDispute(String disputeType, String reason) {
        if (StringUtils.isAnyBlank(disputeType, reason)) {
            return false;
        }

        if (DisputeType.ITEM_NOT_RECEIVED.equals(disputeType) ||
            DisputeType.ITEM_NOT_AS_DESCRIBED.equals(disputeType)) {
            return true;
        }

        return StringUtils.containsIgnoreCase(reason, "fraud") ||
               StringUtils.containsIgnoreCase(reason, "unauthorized") ||
               StringUtils.containsIgnoreCase(reason, "duplicate");
    }

    /**
     * 判断争议是否需要人工干预
     * @param dispute 争议对象
     * @return 如果需要人工干预则返回 true
     */
    protected boolean needsManualIntervention(PaypalDispute dispute) {
        return isHighRiskDispute(dispute.getDisputeType(), dispute.getReason()) ||
               (dispute.getAmount() != null && dispute.getAmount().compareTo(new BigDecimal("50")) > 0);
    }

    /**
     * 获取需要人工干预的原因
     * @param dispute 争议对象
     * @return 描述原因的字符串
     */
    protected String getManualInterventionReason(PaypalDispute dispute) {
        if (isHighRiskDispute(dispute.getDisputeType(), dispute.getReason())) {
            return String.format("高风险争议 - 类型: %s, 原因: %s",
                dispute.getDisputeType(), dispute.getReason());
        } else if (dispute.getAmount() != null && dispute.getAmount().compareTo(new BigDecimal("50")) > 0) {
            return String.format("争议金额较大 - %s %s",
                dispute.getAmount(), dispute.getCurrency());
        }
        return "未知原因";
    }
}
