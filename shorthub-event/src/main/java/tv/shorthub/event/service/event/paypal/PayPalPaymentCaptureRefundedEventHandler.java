package tv.shorthub.event.service.event.paypal;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tv.shorthub.event.constant.PayPalEventType;
import tv.shorthub.system.domain.PaypalPaymentLog;
import tv.shorthub.system.mapper.AppUsersMapper;
import tv.shorthub.system.service.IPaypalPaymentLogService;
import tv.shorthub.dingtalk.service.DingTalkNotificationService;
import tv.shorthub.system.service.IAppOrderInfoService;
import tv.shorthub.system.mapper.AppOrderInfoMapper;
import tv.shorthub.system.domain.AppOrderInfo;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Optional;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import org.apache.commons.lang3.StringUtils;

/**
 * PayPal Payment Capture Refunded 事件处理器
 * 处理 v2 API 的退款完成事件
 */
@Slf4j
@Component
public class PayPalPaymentCaptureRefundedEventHandler extends BasePayPalEventHandler {

    private final DingTalkNotificationService dingTalkNotificationService;
    @Autowired
    IAppOrderInfoService appOrderInfoService;

    public PayPalPaymentCaptureRefundedEventHandler(IPaypalPaymentLogService paymentLogService,
                                                    AppUsersMapper appUsersMapper,
                                                    DingTalkNotificationService dingTalkNotificationService) {
        super(paymentLogService, appUsersMapper);
        this.dingTalkNotificationService = dingTalkNotificationService;
    }

    @Override
    protected void process(JSONObject event, JSONObject resource) {
        String refundId = getResourceId(resource);
        String status = resource.getString("status");
        log.info("[PaymentCaptureRefunded处理] 1. 开始处理. RefundID: {}, Status: {}", refundId, status);
        log.info("[PaymentCaptureRefunded处理] 1a. 原始 Resource 数据: {}", resource.toJSONString());

        // 从 supplementary_data 中获取原始订单ID
        JSONObject supplementaryData = resource.getJSONObject("supplementary_data");
        if (supplementaryData == null || supplementaryData.getJSONObject("related_ids") == null) {
            log.error("[PaymentCaptureRefunded处理] 2. 错误: 'supplementary_data' 或 'related_ids' 为空. RefundID: {}. Resource: {}",
                    refundId, resource.toJSONString());
            return;
        }
        String orderId = supplementaryData.getJSONObject("related_ids").getString("order_id");
        log.info("[PaymentCaptureRefunded处理] 2a. 从 supplementary_data 中解析出 OrderID: {}", orderId);

        if (orderId == null) {
            log.error("[PaymentCaptureRefunded处理] 3. 错误: 未能解析出 'order_id'. RefundID: {}", refundId);
            return;
        }

        Optional<PaypalPaymentLog> paymentLogOpt = findPaymentLogByPaypalOrderId(orderId);
        log.info("[PaymentCaptureRefunded处理] 4. 尝试通过 OrderID {} 查找支付日志. 结果: {}", orderId, paymentLogOpt.isPresent() ? "找到" : "未找到");

        if (!paymentLogOpt.isPresent()) {
            log.error("[PaymentCaptureRefunded处理] 5. 错误: 未找到关联的支付日志. OrderID: {}", orderId);
            return;
        }

        PaypalPaymentLog paymentLog = paymentLogOpt.get();
        log.info("[PaymentCaptureRefunded处理] 5a. 确认使用支付日志. 系统订单号: {}", paymentLog.getOrderNo());

        log.info("[PaymentCaptureRefunded处理] 6. 开始直接更新 app_order_info 的退款状态.");
        appOrderInfoService.updateOrderStatus(paymentLog.getOrderNo());

        log.info("[PaymentCaptureRefunded处理] 7. 开始发送钉钉通知.");
        // 发送钉钉通知
        try {
            JSONObject sellerPayableBreakdown = resource.getJSONObject("seller_payable_breakdown");
            BigDecimal refundAmount = BigDecimal.ZERO;
            String currency = "";
            if(sellerPayableBreakdown != null) {
                refundAmount = sellerPayableBreakdown.getJSONObject("total_refunded_amount").getBigDecimal("value");
                currency = sellerPayableBreakdown.getJSONObject("total_refunded_amount").getString("currency_code");
            }
            log.info("[PaymentCaptureRefunded处理] 7a. 解析退款金额成功. 金额: {} {}", refundAmount, currency);

            String reason = resource.getString("note_to_payer");
            
            // 获取退款时间，保持 UTC 时间格式
            String refundTime = null;
            String createTimeStr = resource.getString("create_time");
            if (StringUtils.isNotBlank(createTimeStr)) {
                try {
                    refundTime = ZonedDateTime.parse(createTimeStr)
                        .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                } catch (Exception e) {
                    log.warn("[PaymentCaptureRefunded处理] 解析退款时间失败，将使用当前时间. Error: {}", e.getMessage());
                }
            }

            dingTalkNotificationService.sendRefundNotification(
                    paymentLog.getOrderNo(),
                    refundId,
                    refundAmount,
                    currency,
                    reason,
                    getEventType(),
                    refundTime
            );
            log.info("[PaymentCaptureRefunded处理] 7b. 调用钉钉服务方法完成.");
        } catch (Exception e) {
            log.error("[PaymentCaptureRefunded处理] 7c. 发送钉钉通知时出现异常.", e);
        }
        log.info("[PaymentCaptureRefunded处理] 8. 事件处理流程结束.");
    }

    @Override
    protected String getPayerEmailFromResource(JSONObject resource) {
        // 退款事件中没有payer信息
        return null;
    }

    @Override
    protected String getResourceId(JSONObject resource) {
        return resource.getString("id");
    }

    @Override
    public String getEventType() {
        return PayPalEventType.PAYMENT_CAPTURE_REFUNDED.getCode();
    }
}
