package tv.shorthub.event.service.event.paypal;

import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tv.shorthub.dingtalk.service.DingTalkNotificationService;
import tv.shorthub.event.constant.PayPalEventType;
import tv.shorthub.system.domain.PaypalPaymentLog;
import tv.shorthub.system.mapper.AppUsersMapper;
import tv.shorthub.system.service.IPaypalPaymentLogService;
import tv.shorthub.system.service.IAppOrderInfoService;
import tv.shorthub.dingtalk.model.NotificationExtInfo;
import tv.shorthub.event.utils.NotificationExtInfoUtil;
import tv.shorthub.system.domain.AppOrderInfo;
import tv.shorthub.system.mapper.AppOrderInfoMapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.apache.commons.lang3.StringUtils;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.ZoneId;
import tv.shorthub.email.service.EmailService;
import java.util.Optional;

@Slf4j
@Component
public class PayPalSubscriptionCancelledEventHandler extends BaseSubscriptionEventHandler {

    private final DingTalkNotificationService dingTalkNotificationService;
    private final AppOrderInfoMapper appOrderInfoMapper;
    private final EmailService emailService;

    @Autowired
    IAppOrderInfoService appOrderInfoService;
    
    @Autowired
    public PayPalSubscriptionCancelledEventHandler(IPaypalPaymentLogService paymentLogService,
                                                   AppUsersMapper appUsersMapper,
                                                   IAppOrderInfoService appOrderInfoService,
                                                   DingTalkNotificationService dingTalkNotificationService,
                                                   AppOrderInfoMapper appOrderInfoMapper,
                                                   EmailService emailService) {
        super(paymentLogService, appUsersMapper, appOrderInfoService);
        this.dingTalkNotificationService = dingTalkNotificationService;
        this.appOrderInfoMapper = appOrderInfoMapper;
        this.emailService = emailService;
    }

    /**
     * 重写 process 方法，为 "CANCELLED" 事件提供专门的、更可靠的查找逻辑。
     * 该事件的核心ID是 subscriptionId，必须确保通过它来查找关联订单。
     */
    @Override
    protected void process(JSONObject event, JSONObject resource) {
        String subscriptionId = getResourceId(resource);
        log.info("[PayPal Event] 开始处理订阅取消事件 - SubscriptionID: {}", subscriptionId);

        // 对于取消事件，唯一可靠的ID是 subscriptionId。直接用它查找。
        Optional<PaypalPaymentLog> paymentLogOpt = findPaymentLogByPaypalOrderId(subscriptionId);

        if (!paymentLogOpt.isPresent()) {
            log.error("[PayPal Event] 错误 - 类型: {}, ID: {}. 未能通过订阅ID找到关联的支付日志. 该事件将被忽略.",
                    getEventType(), subscriptionId);
            return;
        }

        PaypalPaymentLog paymentLog = paymentLogOpt.get();
        // updateOrderStatus 更新状态
        appOrderInfoService.updateOrderStatus(paymentLog.getOrderNo());

        // 直接调用通知逻辑，无需执行父类中其他不相关的步骤
        triggerNotification(paymentLog, resource);
    }

    @Override
    protected void triggerNotification(PaypalPaymentLog paymentLog, JSONObject resource) {
        log.info("[PayPal Event] 触发订阅取消通知 - 系统订单号: {}", paymentLog.getOrderNo());
        // 查找用户信息
        Optional<UserInfo> userInfoOpt = findUserInfo(paymentLog, resource);
        if (!userInfoOpt.isPresent()) {
            log.warn("[通知] 未找到用户信息，无法发送通知。订阅ID: {}", getResourceId(resource));
            return;
        }
        UserInfo userInfo = userInfoOpt.get();

        String subscriptionId = getResourceId(resource);
        String planId = resource.getString("plan_id");
        // 用户取消订阅的原因，可能为空
        String reason = resource.getString("status_change_note");

        // 获取订阅取消时间，保持 UTC 时间格式
        String cancelTime = null;
        String updateTimeStr = resource.getString("status_update_time");
        if (StringUtils.isNotBlank(updateTimeStr)) {
            try {
                cancelTime = ZonedDateTime.parse(updateTimeStr)
                    .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            } catch (Exception e) {
                log.warn("[PayPal Event] 解析订阅取消时间失败，将使用当前时间. Error: {}", e.getMessage());
            }
        }
        final String finalCancelTime = cancelTime;

        // 获取订单信息以提取扩展信息
        NotificationExtInfo extInfo = null;
        try {
            AppOrderInfo orderInfo = appOrderInfoMapper.selectOne(
                new QueryWrapper<AppOrderInfo>().eq("order_no", paymentLog.getOrderNo())
            );
            if (orderInfo != null) {
                extInfo = NotificationExtInfoUtil.extractFromOrderInfo(orderInfo);
            }
        } catch (Exception e) {
            log.warn("[钉钉通知] 获取订单扩展信息失败. 订单号: {}", paymentLog.getOrderNo(), e);
        }

        // 发送钉钉通知
        try {
            dingTalkNotificationService.sendSubscriptionCancelledNotification(
                    subscriptionId,
                    planId,
                    paymentLog.getTotalAmount(),
                    paymentLog.getCurrency(),
                    userInfo.getEmail(),
                    reason,
                    getEventType(),
                    finalCancelTime,
                    extInfo
            );
            log.info("[钉钉通知] 订阅取消通知已发送 - 订阅ID: {}", subscriptionId);
        } catch (Exception e) {
            log.error("[钉钉通知] 发送订阅取消通知时出现未预期的异常。订阅ID: {}", getResourceId(resource), e);
        }

        // 发送邮件通知
        try {
             emailService.sendSubscriptionCancelledEmail(
                userInfo.getEmail(),
                userInfo.getName(),
                planId, // 使用 planId 作为方案名称
                finalCancelTime,
                userInfo.getLanguageCode(),
                subscriptionId,
                null // 此处传递null，将使用默认URL
            );
        } catch (Exception e) {
             log.error("[邮件通知] 发送订阅取消邮件时出现未预期的异常。订阅ID: {}", getResourceId(resource), e);
        }
    }

    @Override
    public String getEventType() {
        return PayPalEventType.SUBSCRIPTION_CANCELLED.getCode();
    }
}
