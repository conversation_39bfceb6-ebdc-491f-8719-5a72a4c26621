package tv.shorthub.event.service.event.paypal;

import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import tv.shorthub.dingtalk.service.DingTalkNotificationService;
import tv.shorthub.event.constant.PayPalEventType;
import tv.shorthub.paypal.constant.PayPalConstants.DisputeOutcome;
import tv.shorthub.paypal.service.PayPalDisputeService;
import tv.shorthub.system.domain.PaypalDispute;
import tv.shorthub.system.service.IPaypalDisputeService;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Component
public class PayPalDisputeResolvedEventHandler extends BaseDisputeEventHandler {

    private final DingTalkNotificationService dingTalkNotificationService;
    private static final Map<String, String> outcomeCodeMap = new HashMap<>();

    static {
        outcomeCodeMap.put("RESOLVED_BUYER_FAVOUR", "买家胜诉");
        outcomeCodeMap.put("RESOLVED_SELLER_FAVOUR", "卖家胜诉");
        outcomeCodeMap.put("RESOLVED_WITH_PAYOUT", "已赔付解决");
        outcomeCodeMap.put("CANCELED_BY_PAYPAL", "PayPal取消");
        outcomeCodeMap.put("ACCEPTED", "已接受");
        outcomeCodeMap.put("DENIED", "已拒绝");
    }

    public PayPalDisputeResolvedEventHandler(IPaypalDisputeService disputeService, DingTalkNotificationService dingTalkNotificationService) {
        super(disputeService);
        this.dingTalkNotificationService = dingTalkNotificationService;
    }

    @Override
    protected void processDispute(PaypalDispute dispute, JSONObject disputeDetail) {
        // 获取解决结果
        JSONObject disputeOutcome = disputeDetail.getJSONObject("dispute_outcome");
        String outcomeCode = disputeOutcome.getString("outcome_code");
        String outcomeCodeInChinese = outcomeCodeMap.getOrDefault(outcomeCode, outcomeCode);
        JSONObject amountRefunded = disputeOutcome.getJSONObject("amount_refunded");

        if (DisputeOutcome.RESOLVED_SELLER_FAVOUR.equals(outcomeCode)) {
            log.info("争议解决结果 - 卖家胜诉，无需退款。争议ID: {}", dispute.getDisputeId());
        } else {
            String refundedAmount = "0";
            String refundedCurrency = "";
            if (amountRefunded != null) {
                refundedAmount = amountRefunded.getString("value");
                refundedCurrency = amountRefunded.getString("currency_code");
            }
            log.info("争议解决结果 - 争议ID: {}, 结果: {}, 退款金额: {} {}",
                    dispute.getDisputeId(), outcomeCodeInChinese, refundedAmount, refundedCurrency);
        }

        // 更新争议记录
        if (dispute.getId() == null) {
            disputeService.insert(dispute);
            log.warn("[PayPal Event] 收到未知争议的解决事件，记录已创建 - 争议ID: {}", dispute.getDisputeId());
        } else {
            disputeService.update(dispute);
        }
        log.info("争议解决处理完成 - 争议ID: {}, 结果: {}", dispute.getDisputeId(), outcomeCodeInChinese);

        // 发送钉钉通知
        try {
            dingTalkNotificationService.sendDisputeNotification(
                    "【争议解决】" + outcomeCodeInChinese,
                    dispute.getDisputeId(),
                    getReasonInChinese(dispute.getReason()),
                    getStatusInChinese(dispute.getStatus()),
                    dispute.getAmount(),
                    dispute.getCurrency(),
                    getEventType()
            );
        } catch (Exception e) {
            log.error("[钉钉通知] 发送争议解决通知时出现未预期的异常。争议ID: {}", dispute.getDisputeId(), e);
        }
    }

    @Override
    public String getEventType() {
        return PayPalEventType.DISPUTE_RESOLVED.getCode();
    }
}
