package tv.shorthub.event.service.event.paypal;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tv.shorthub.event.constant.PayPalEventType;
import tv.shorthub.email.service.EmailService;
import tv.shorthub.email.service.impl.EmailTemplateService;
import tv.shorthub.system.domain.AppOrderInfo;
import tv.shorthub.system.domain.PaypalPaymentLog;
import tv.shorthub.system.mapper.AppOrderInfoMapper;
import tv.shorthub.system.mapper.AppUsersMapper;
import tv.shorthub.system.service.IAppOrderInfoService;
import tv.shorthub.system.service.IAppUsersService;
import tv.shorthub.system.service.IPaypalPaymentLogService;
import tv.shorthub.system.service.ISysEmailLogService;
import tv.shorthub.system.service.IAppConfigService;
import tv.shorthub.system.domain.AppConfig;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.ZoneId;
import java.util.Date;
import java.util.Optional;
import org.apache.commons.lang3.StringUtils;

import tv.shorthub.paypal.constant.PayPalConstants.PaymentStatus;
import tv.shorthub.dingtalk.service.DingTalkNotificationService;
import tv.shorthub.dingtalk.model.NotificationExtInfo;
import tv.shorthub.event.utils.NotificationExtInfoUtil;


/**
 * PayPal Payment Capture Completed 事件处理器
 * 处理 v2 API 的支付完成事件
 */
@Slf4j
@Component
public class PayPalPaymentCaptureCompletedEventHandler extends BasePayPalEventHandler {

    private final EmailService emailService;
    private final ISysEmailLogService emailLogService;
    private final DingTalkNotificationService dingTalkNotificationService;
    private final AppOrderInfoMapper appOrderInfoMapper;
    @Autowired
    IAppOrderInfoService appOrderInfoService;

    @Autowired
    public PayPalPaymentCaptureCompletedEventHandler(IPaypalPaymentLogService paymentLogService,
                                                     AppUsersMapper appUsersMapper,
                                                   EmailService emailService,
                                                   ISysEmailLogService emailLogService,
                                                   DingTalkNotificationService dingTalkNotificationService,
                                                   AppOrderInfoMapper appOrderInfoMapper) {
        super(paymentLogService, appUsersMapper);
        this.emailService = emailService;
        this.emailLogService = emailLogService;
        this.dingTalkNotificationService = dingTalkNotificationService;
        this.appOrderInfoMapper = appOrderInfoMapper;
    }

    @Override
    protected void process(JSONObject event, JSONObject resource) {
        String captureId = getResourceId(resource);
        String status = resource.getString("status");

        // 从 supplementary_data 中获取原始订单ID
        JSONObject supplementaryData = resource.getJSONObject("supplementary_data");
        if (supplementaryData == null || supplementaryData.getJSONObject("related_ids") == null) {
            log.error("[PayPal Event] 错误 - 类型: {}, ID: {}. 事件中缺少 'supplementary_data.related_ids' 无法关联订单.", getEventType(), captureId);
            return;
        }
        String orderId = supplementaryData.getJSONObject("related_ids").getString("order_id");
        if (orderId == null) {
            log.error("[PayPal Event] 错误 - 类型: {}, ID: {}. 在 'supplementary_data' 中未找到 'order_id'.", getEventType(), captureId);
            return;
        }
        Optional<PaypalPaymentLog> paymentLogOpt = findPaymentLogByPaypalOrderId(orderId);
        if (!paymentLogOpt.isPresent()) {
            log.error("[PayPal Event] 错误 - 类型: {}, ID: {}. 未找到关联的支付日志. OrderId: {}", getEventType(), captureId, orderId);
            return;
        }
        // 更新 状态updateOrderStatus
        PaypalPaymentLog paymentLog = paymentLogOpt.get();
        appOrderInfoService.updateOrderStatus(paymentLog.getOrderNo());
        log.info("[PaymentCaptureCompleted处理]订单状态更新完成. 系统订单号: {}", paymentLog.getOrderNo());


        if (PaymentStatus.COMPLETED.equals(status)) {
            // 新增：检查订单支付时间，防止重复通知
            AppOrderInfo orderInfo = null;
            
            // 添加重试机制，最多重试3次，每次间隔500ms
            for (int attempt = 1; attempt <= 3; attempt++) {
                log.info("[PaymentCaptureCompleted处理] 第{}次查询订单信息，订单号: {}", attempt, paymentLog.getOrderNo());
                orderInfo = appOrderInfoMapper.selectOne(
                        new QueryWrapper<AppOrderInfo>().eq("order_no", paymentLog.getOrderNo())
                );
                
                if (orderInfo != null) {
                    log.info("[PaymentCaptureCompleted处理] 第{}次查询成功！订单号: {}, 支付时间: {}, extend_json是否为空: {}", 
                        attempt, orderInfo.getOrderNo(), orderInfo.getPayTime(), orderInfo.getExtendJson() == null ? "是" : "否");
                    break;
                } else {
                    log.warn("[PaymentCaptureCompleted处理] 第{}次查询失败，订单号: {}", attempt, paymentLog.getOrderNo());
                    if (attempt < 3) {
                        try {
                            Thread.sleep(500); // 等待500ms再重试
                        } catch (InterruptedException e) {
                            Thread.currentThread().interrupt();
                            log.error("[PaymentCaptureCompleted处理] 重试等待被中断", e);
                            break;
                        }
                    }
                }
            }
            
            if (orderInfo == null) {
                log.error("[PaymentCaptureCompleted处理] 3次重试后仍未找到订单信息！订单号: {}", paymentLog.getOrderNo());
            }

            // 检查是否为订阅支付：如果是订单表中的首次订阅（pay_type=2），正常处理并发送通知
            // 续费支付不会出现在订单表中，会由 PayPalPaymentSaleCompletedEventHandler 处理
            if (orderInfo != null && "2".equals(orderInfo.getPayType())) {
                log.info("[PaymentCaptureCompleted处理] 检测到首次订阅支付订单 (pay_type=2)，将正常处理并发送通知。系统订单号: {}", paymentLog.getOrderNo());
                // 继续处理，不return
            }

            if (orderInfo != null && orderInfo.getPayTime() != null) {
                long hoursDifference = (new Date().getTime() - orderInfo.getPayTime().getTime()) / (1000 * 60 * 60);
                if (hoursDifference > 24) {
                    log.warn("[PaymentCaptureCompleted处理] 订单支付时间已超过24小时 ({} 小时前)，跳过发送通知。系统订单号: {}", hoursDifference, paymentLog.getOrderNo());
                    return; // 超过24小时，不发送任何通知
                }
                 // 如果检查通过，则发送通知
                sendNotifications(paymentLog, resource, orderInfo);
            } else {
                if(orderInfo == null) {
                    log.warn("[PaymentCaptureCompleted处理] 未找到系统订单信息，将继续发送通知。系统订单号: {}", paymentLog.getOrderNo());
                    sendNotifications(paymentLog, resource, null);
                } else {
                    log.warn("[PaymentCaptureCompleted处理] 订单支付时间(pay_time)为空，但订单存在，将传递订单信息发送通知。系统订单号: {}", paymentLog.getOrderNo());
                    // 订单存在但支付时间为空，仍然传递 orderInfo 以保留扩展信息
                    sendNotifications(paymentLog, resource, orderInfo);
                }
            }
        }
    }

    /**
     * 封装发送钉钉和邮件通知的逻辑
     * @param paymentLog 支付日志
     * @param resource PayPal事件资源
     * @param orderInfo 订单信息，可能为null
     */
    private void sendNotifications(PaypalPaymentLog paymentLog, JSONObject resource, AppOrderInfo orderInfo) {
        String paymentTime = null;

        // 优先从 PayPal 事件 resource 中获取精确的支付完成时间
        // 对于 PAYMENT.CAPTURE.COMPLETED, update_time 代表了状态变为COMPLETED的时间
        String paypalPaymentTime = resource.getString("update_time");
        if (StringUtils.isBlank(paypalPaymentTime)) {
            // 如果没有 update_time，使用 create_time 作为后备
            paypalPaymentTime = resource.getString("create_time");
        }

        if (StringUtils.isNotBlank(paypalPaymentTime)) {
            try {
                // PayPal 时间是 ISO 8601 格式 (带Z)，直接用 ZonedDateTime 解析，保持 UTC 时间
                paymentTime = ZonedDateTime.parse(paypalPaymentTime)
                    .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            } catch (Exception e) {
                log.warn("[PaymentCaptureCompleted处理] 解析PayPal事件时间戳失败. Timestamp: {}, 将尝试回退. Error: {}", paypalPaymentTime, e.getMessage());
            }
        }

        // 如果无法从PayPal事件中获取时间，则回退到使用数据库中的支付时间
        if (StringUtils.isBlank(paymentTime) && orderInfo != null && orderInfo.getPayTime() != null) {
            log.warn("[PaymentCaptureCompleted处理] 无法从PayPal事件中获取时间，回退使用数据库中的 pay_time。");
            paymentTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(orderInfo.getPayTime());
        }

        final String finalPaymentTime = paymentTime;

        // 发送钉钉通知 - 已注释，改为统一的定时任务处理
        // try {
        //     String userEmail = findUserInfo(paymentLog, resource).map(UserInfo::getEmail).orElse("未知");
        //     
        //     // 从订单信息中提取扩展信息
        //     NotificationExtInfo extInfo = null;
        //     if (orderInfo != null) {
        //         log.info("[PayPal事件处理] 开始提取订单扩展信息，订单号: {}", orderInfo.getOrderNo());
        //         extInfo = NotificationExtInfoUtil.extractFromOrderInfo(orderInfo);
        //         if (extInfo != null) {
        //             log.info("[PayPal事件处理] 订单扩展信息提取成功，订单号: {}", orderInfo.getOrderNo());
        //         } else {
        //             log.info("[PayPal事件处理] 订单扩展信息为空，订单号: {}", orderInfo.getOrderNo());
        //         }
        //     } else {
        //         log.info("[PayPal事件处理] 订单信息为null，无法提取扩展信息");
        //     }
        //     
        //     // 根据订单类型选择不同的通知方式
        //     if (orderInfo != null && "2".equals(orderInfo.getPayType())) {
        //         // 订阅支付：调用订阅成功通知
        //         log.info("[PayPal事件处理] 调用订阅成功通知服务，扩展信息: {}", extInfo != null ? "有" : "无");
        //         dingTalkNotificationService.sendSubscriptionSuccessNotification(
        //                 paymentLog.getPaymentId(), // 使用paymentId作为订阅ID
        //                 paymentLog.getClientId(), // 计划ID
        //                 paymentLog.getTotalAmount(),
        //                 paymentLog.getCurrency(),
        //                 userEmail,
        //                 getEventType(),
        //                 finalPaymentTime,
        //                 extInfo
        //         );
        //     } else {
        //         // 普通支付：调用普通支付通知
        //         String paymentTypeDesc = "一次性支付"; // 默认值
        //         if (orderInfo != null && orderInfo.getPayType() != null) {
        //             String payType = orderInfo.getPayType();
        //             if ("0".equals(payType)) {
        //                 paymentTypeDesc = "会员充值";
        //             } else if ("1".equals(payType)) {
        //                 paymentTypeDesc = "金币充值";
        //             }
        //             log.info("[PayPal事件处理] 支付类型确定: {} (pay_type: {})", paymentTypeDesc, payType);
        //         } else {
        //             log.warn("[PayPal事件处理] 无法确定支付类型，使用默认值: {}", paymentTypeDesc);
        //         }
        //         
        //         log.info("[PayPal事件处理] 调用普通支付通知服务，支付类型: {}, 扩展信息: {}", paymentTypeDesc, extInfo != null ? "有" : "无");
        //         dingTalkNotificationService.sendPaymentSuccessNotification(
        //                 paymentLog.getOrderNo(),
        //                 paymentLog.getTotalAmount(),
        //                 paymentLog.getCurrency(),
        //                 userEmail,
        //                 paymentTypeDesc,
        //                 getEventType(),
        //                 finalPaymentTime,
        //                 extInfo
        //         );
        //     }
        // } catch (Exception e) {
        //     log.error("[钉钉通知] 发送支付成功通知时出现未预期的异常。系统订单号: {}", paymentLog.getOrderNo(), e);
        // }

        // 发送邮件通知
        log.info("[PayPal Event] 支付捕获完成，开始发送邮件通知。系统订单号: {}", paymentLog.getOrderNo());
        findUserInfo(paymentLog, resource).ifPresent(userInfo -> {
            log.info("[邮件通知] 开始处理邮件发送流程。订单号: {}", paymentLog.getOrderNo());

            // 解析 websiteUrl
            String websiteUrl = null;
            Object extendJsonObj = orderInfo != null ? orderInfo.getExtendJson() : null;
            String extendJsonStr = null;
            if (extendJsonObj != null) {
                if (extendJsonObj instanceof String) {
                    extendJsonStr = (String) extendJsonObj;
                } else if (extendJsonObj instanceof JSONObject) {
                    extendJsonStr = ((JSONObject) extendJsonObj).toJSONString();
                }
            }
            if (StringUtils.isNotBlank(extendJsonStr)) {
                try {
                    JSONObject extJson = JSONObject.parseObject(extendJsonStr);
                    websiteUrl = extJson.getString("verifyWithLogin");
                } catch (Exception e) {
                    log.warn("[邮件通知] 解析订单扩展字段失败，使用默认官网。订单号: {}", orderInfo.getOrderNo());
                }
            }

            // 根据订单类型选择不同的邮件服务
            if (orderInfo != null && "2".equals(orderInfo.getPayType())) {
                // 订阅支付
                emailService.sendSubscriptionSuccessEmail(
                        userInfo.getEmail(),
                        userInfo.getName(),
                        paymentLog.getOrderNo(),
                        orderInfo.getFeeItemId(), // 使用fee_item_id作为planName
                        paymentLog.getTotalAmount(),
                        finalPaymentTime,
                        userInfo.getLanguageCode(),
                        websiteUrl); // 传递动态链接

            } else {
                // 普通支付
                emailService.sendPaymentSuccessEmail(
                        userInfo.getEmail(),
                        userInfo.getName(),
                        paymentLog.getOrderNo(),
                        paymentLog.getTotalAmount(),
                        finalPaymentTime,
                        userInfo.getLanguageCode(),
                        websiteUrl); // 传递动态链接
            }
        });
    }

    @Override
    protected String getPayerEmailFromResource(JSONObject resource) {
        // 对于支付捕获事件，付款人信息在顶层的 payer 对象中
        JSONObject payer = resource.getJSONObject("payer");
        if (payer != null) {
            return payer.getString("email_address");
        }
        return null;
    }

    @Override
    protected String getResourceId(JSONObject resource) {
        return resource.getString("id");
    }

    @Override
    public String getEventType() {
        return PayPalEventType.PAYMENT_CAPTURE_COMPLETED.getCode();
    }
}
