package tv.shorthub.event.service.event.paypal;

import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import tv.shorthub.dingtalk.service.DingTalkNotificationService;
import tv.shorthub.event.constant.PayPalEventType;
import tv.shorthub.paypal.service.PayPalDisputeService;
import tv.shorthub.common.core.redis.RedisCache;
import tv.shorthub.system.domain.PaypalDispute;
import tv.shorthub.system.service.IPaypalDisputeService;
import tv.shorthub.system.service.IPaypalPaymentLogService;

/**
 * PayPal 争议更新事件处理器
 */
@Slf4j
@Component
public class PayPalDisputeUpdatedEventHandler extends BaseDisputeEventHandler {

    private final DingTalkNotificationService dingTalkNotificationService;
    private final RedisCache redisCache;

    public PayPalDisputeUpdatedEventHandler(IPaypalDisputeService disputeService, DingTalkNotificationService dingTalkNotificationService, RedisCache redisCache) {
        super(disputeService);
        this.dingTalkNotificationService = dingTalkNotificationService;
        this.redisCache = redisCache;
    }

    @Override
    protected void processDispute(PaypalDispute dispute, JSONObject disputeDetail) {
        // 评估风险等级
        String riskLevel = getRiskLevel(dispute);
        log.info("[PayPal Event] 争议风险等级 - 争议ID: {}, 风险等级: {}", dispute.getDisputeId(), riskLevel);

        // 更新或插入争议记录
        if (dispute.getId() == null) {
            disputeService.insert(dispute);
            log.warn("[PayPal Event] 收到未知争议的更新事件，记录已创建 - 争议ID: {}", dispute.getDisputeId());
        } else {
            disputeService.update(dispute);
        }

        // 检查是否在短时间内已经为这个争议状态发送过通知
        String eventType = getEventType();
        String lockKey = String.format("dispute:notify_lock:%s:%s", dispute.getDisputeId(), dispute.getStatus());
        if (redisCache.getCacheObject(lockKey) != null) {
            log.info("[钉钉通知] 忽略重复的争议更新通知 (相同状态) - 争议ID: {}, 状态: {}", dispute.getDisputeId(), dispute.getStatus());
            return;
        }

        // 发送钉钉通知
        try {
            dingTalkNotificationService.sendDisputeNotification(
                    "【争议更新】" + riskLevel,
                    dispute.getDisputeId(),
                    getReasonInChinese(dispute.getReason()),
                    getStatusInChinese(dispute.getStatus()),
                    dispute.getAmount(),
                    dispute.getCurrency(),
                    eventType
            );
            // 标记已发送，设置一个合理的过期时间，例如5分钟
            redisCache.setCacheObject(lockKey, "sent", 5, java.util.concurrent.TimeUnit.MINUTES);
        } catch (Exception e) {
            log.error("[钉钉通知] 发送争议更新通知时出现未预期的异常。争议ID: {}", dispute.getDisputeId(), e);
        }

        // 检查是否需要人工干预
        if (needsManualIntervention(dispute)) {
            String reason = getManualInterventionReason(dispute);
            log.warn("[PayPal Event] 争议需要人工干预 - 争议ID: {}, 原因: {}, 风险等级: {}",
                    dispute.getDisputeId(), reason, riskLevel);
        }

        log.info("[PayPal Event] 争议更新处理完成 - 争议ID: {}, 风险等级: {}", dispute.getDisputeId(), riskLevel);
    }

    @Override
    public String getEventType() {
        return PayPalEventType.DISPUTE_UPDATED.getCode();
    }
}
