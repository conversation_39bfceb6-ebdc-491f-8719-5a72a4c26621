package tv.shorthub.event.service.event.paypal;

import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tv.shorthub.event.constant.PayPalEventType;
import tv.shorthub.system.domain.PaypalPaymentLog;
import tv.shorthub.system.mapper.AppUsersMapper;
import tv.shorthub.system.service.IAppUsersService;
import tv.shorthub.system.service.IPaypalPaymentLogService;
import tv.shorthub.email.service.EmailService;
import tv.shorthub.email.service.impl.EmailTemplateService;
import tv.shorthub.system.service.ISysEmailLogService;
import tv.shorthub.paypal.constant.PayPalConstants.SubscriptionStatus;
import tv.shorthub.dingtalk.service.DingTalkNotificationService;
import tv.shorthub.system.service.IAppOrderInfoService;

@Slf4j
@Component
public class PayPalSubscriptionActivatedEventHandler extends BaseSubscriptionEventHandler {

    private final EmailService emailService;
    private final EmailTemplateService emailTemplateService;
    private final ISysEmailLogService emailLogService;
    private final DingTalkNotificationService dingTalkNotificationService;

    @Autowired
    public PayPalSubscriptionActivatedEventHandler(IPaypalPaymentLogService paymentLogService,
                                                   AppUsersMapper appUsersMapper,
                                                   EmailService emailService,
                                                   EmailTemplateService emailTemplateService,
                                                   ISysEmailLogService emailLogService,
                                                   DingTalkNotificationService dingTalkNotificationService,
                                                   IAppOrderInfoService appOrderInfoService) {
        super(paymentLogService, appUsersMapper, appOrderInfoService);
        this.emailService = emailService;
        this.emailTemplateService = emailTemplateService;
        this.emailLogService = emailLogService;
        this.dingTalkNotificationService = dingTalkNotificationService;
    }

    @Override
    protected void enhancePaymentLog(PaypalPaymentLog paymentLog, JSONObject resource) {

        paymentLog.setState(SubscriptionStatus.ACTIVE);

        JSONObject subscriber = resource.getJSONObject("subscriber");
        if (subscriber != null) {
            paymentLog.setPayerId(subscriber.getString("payer_id"));
        }
        paymentLog.setClientId(resource.getString("plan_id"));
        paymentLog.setPaymentMethod("SUBSCRIPTION");

        JSONObject billingInfo = resource.getJSONObject("billing_info");
        if (billingInfo != null) {
            String nextBillingTime = billingInfo.getString("next_billing_time");
            paymentLog.setDescription("订阅激活 - 下次账单时间: " + nextBillingTime);
        }
    }

    @Override
    protected void triggerNotification(PaypalPaymentLog paymentLog, JSONObject resource) {
        // 此事件处理器不再负责发送任何面向用户的通知，以避免与付款完成事件处理器发生冲突。
        log.info("[PayPal Event] '订阅激活'事件 (ID: {}) 仅用于内部状态更新，跳过发送钉钉和邮件通知。相关支付通知将由 PAYMENT.SALE.COMPLETED 事件处理。", paymentLog.getPaymentId());
    }

    @Override
    public String getEventType() {
        return PayPalEventType.SUBSCRIPTION_ACTIVATED.getCode();
    }
}
