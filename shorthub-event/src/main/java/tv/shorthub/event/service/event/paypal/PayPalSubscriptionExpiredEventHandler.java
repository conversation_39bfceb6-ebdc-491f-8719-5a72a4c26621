package tv.shorthub.event.service.event.paypal;

import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tv.shorthub.event.constant.PayPalEventType;
import tv.shorthub.system.mapper.AppUsersMapper;
import tv.shorthub.system.service.IPaypalPaymentLogService;
import tv.shorthub.system.domain.PaypalPaymentLog;
import tv.shorthub.system.service.IAppOrderInfoService;

/**
 * PayPal 订阅到期事件处理器
 */
@Slf4j
@Component
public class PayPalSubscriptionExpiredEventHandler extends BaseSubscriptionEventHandler {

    @Autowired
    IAppOrderInfoService appOrderInfoService;

    public PayPalSubscriptionExpiredEventHandler(IPaypalPaymentLogService paymentLogService,
                                                 AppUsersMapper appUsersMapper,
                                                 IAppOrderInfoService appOrderInfoService) {
        super(paymentLogService, appUsersMapper, appOrderInfoService);
    }

    @Override
    public String getEventType() {
        return PayPalEventType.SUBSCRIPTION_EXPIRED.getCode();
    }
}
