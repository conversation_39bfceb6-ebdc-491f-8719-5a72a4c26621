package tv.shorthub.event.utils;

import com.alibaba.fastjson2.JSONObject;
import tv.shorthub.dingtalk.model.NotificationExtInfo;
import tv.shorthub.system.domain.AppOrderInfo;
import lombok.extern.slf4j.Slf4j;

/**
 * 通知扩展信息工具类
 * 用于从订单信息中提取地理位置、设备等扩展信息
 */
@Slf4j
public class NotificationExtInfoUtil {
    
    /**
     * 从订单信息中提取扩展信息
     * @param orderInfo 订单信息
     * @return 扩展信息对象，如果无法提取则返回null
     */
    public static NotificationExtInfo extractFromOrderInfo(AppOrderInfo orderInfo) {
        if (orderInfo == null) {
            log.debug("订单信息为null，无法提取扩展信息");
            return null;
        }
        
        if (orderInfo.getExtendJson() == null) {
            log.info("订单 {} 的 extendJson 为空，无法提取扩展信息", orderInfo.getOrderNo());
            return null;
        }
        
        try {
            JSONObject extendJson = orderInfo.getExtendJson();
            log.info("订单 {} 的 extendJson 内容: {}", orderInfo.getOrderNo(), extendJson.toJSONString());
            
            // 提取各个字段
            String country = extendJson.getString("country");
            String clientIp = extendJson.getString("clientIp");
            String clientUserAgent = extendJson.getString("clientUserAgent");
            String language = extendJson.getString("language");
            
            // 如果所有字段都为空，返回null
            if (country == null && clientIp == null && clientUserAgent == null && language == null) {
                log.info("订单 {} 的 extendJson 中没有找到任何扩展信息字段", orderInfo.getOrderNo());
                return null;
            }
            
            NotificationExtInfo extInfo = NotificationExtInfo.builder()
                    .country(country)
                    .clientIp(clientIp)
                    .clientUserAgent(clientUserAgent)
                    .language(language)
                    .build();
            
            log.info("成功提取订单 {} 的扩展信息: country={}, clientIp={}, language={}, userAgent={}",
                    orderInfo.getOrderNo(), country, clientIp, language, 
                    clientUserAgent != null ? clientUserAgent.substring(0, Math.min(clientUserAgent.length(), 50)) + "..." : null);
            
            return extInfo;
                    
        } catch (Exception e) {
            log.error("从订单扩展信息中提取通知扩展信息失败. 订单号: {}, 错误: {}", 
                    orderInfo.getOrderNo(), e.getMessage(), e);
            return null;
        }
    }
} 