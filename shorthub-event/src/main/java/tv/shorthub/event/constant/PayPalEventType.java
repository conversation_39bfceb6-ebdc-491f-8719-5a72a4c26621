package tv.shorthub.event.constant;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * PayPal Webhook 事件类型枚举
 */
@Getter
@RequiredArgsConstructor
public enum PayPalEventType {

    // Order v2 API 事件
    CHECKOUT_ORDER_APPROVED("CHECKOUT.ORDER.APPROVED", "订单已获批准"),

    // Payment v2 API (Capture) 事件
    PAYMENT_CAPTURE_COMPLETED("PAYMENT.CAPTURE.COMPLETED", "支付捕获完成"),
    PAYMENT_CAPTURE_DENIED("PAYMENT.CAPTURE.DENIED", "支付捕获被拒绝"),
    PAYMENT_CAPTURE_PENDING("PAYMENT.CAPTURE.PENDING", "支付捕获待处理"),
    PAYMENT_CAPTURE_REFUNDED("PAYMENT.CAPTURE.REFUNDED", "支付捕获已退款"),
    PAYMENT_CAPTURE_REVERSED("PAYMENT.CAPTURE.REVERSED", "支付捕获已撤销"),

    // Payment v1 API (Sale) 事件 - 也用于订阅续期
    PAYMENT_SALE_COMPLETED("PAYMENT.SALE.COMPLETED", "销售完成（续期）"),

    // Subscription 事件
    SUBSCRIPTION_CREATED("BILLING.SUBSCRIPTION.CREATED", "订阅创建"),
    SUBSCRIPTION_ACTIVATED("BILLING.SUBSCRIPTION.ACTIVATED", "订阅激活"),
    SUBSCRIPTION_EXPIRED("BILLING.SUBSCRIPTION.EXPIRED", "订阅到期"),
    SUBSCRIPTION_CANCELLED("BILLING.SUBSCRIPTION.CANCELLED", "订阅取消"),
    SUBSCRIPTION_SUSPENDED("BILLING.SUBSCRIPTION.SUSPENDED", "订阅暂停"),

    // Dispute 事件
    DISPUTE_CREATED("CUSTOMER.DISPUTE.CREATED", "争议创建"),
    DISPUTE_UPDATED("CUSTOMER.DISPUTE.UPDATED", "争议更新"),
    DISPUTE_RESOLVED("CUSTOMER.DISPUTE.RESOLVED", "争议解决");

    /**
     * 事件类型代码
     */
    private final String code;

    /**
     * 事件类型描述
     */
    private final String description;

    /**
     * 根据事件类型代码获取枚举值
     */
    public static PayPalEventType fromCode(String code) {
        for (PayPalEventType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown PayPal event type: " + code);
    }
}
