<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>shorthub</artifactId>
        <groupId>tv.shorthub</groupId>
        <version>1.0.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <packaging>jar</packaging>
    <artifactId>shorthub-event</artifactId>

    <description>
        事件处理模块，负责处理各类异步事件，如PayPal Webhooks等
    </description>

    <dependencies>
        <!-- 通用工具-->
        <dependency>
            <groupId>tv.shorthub</groupId>
            <artifactId>shorthub-common</artifactId>
        </dependency>

        <!-- 系统模块-->
        <dependency>
            <groupId>tv.shorthub</groupId>
            <artifactId>shorthub-system</artifactId>
        </dependency>

        <!-- 邮件服务 -->
        <dependency>
            <groupId>tv.shorthub</groupId>
            <artifactId>shorthub-email</artifactId>
        </dependency>

        <!-- 钉钉服务 -->
        <dependency>
            <groupId>tv.shorthub</groupId>
            <artifactId>shorthub-dingtalk</artifactId>
        </dependency>

        <!-- PayPal服务-->
        <dependency>
            <groupId>tv.shorthub</groupId>
            <artifactId>shorthub-paypal</artifactId>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
    </dependencies>

</project>
