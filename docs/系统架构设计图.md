# Shorthub-TV 短剧平台系统架构设计图

## 1. 项目概述

**Shorthub-TV** 是一个基于Spring Boot 3.3.0和Java 21构建的现代化短剧视频平台，采用微服务架构设计，支持多支付渠道、多语言国际化、智能视频处理、精准广告投放等核心功能。

### 1.1 核心特性

- 🎬 **智能视频处理**: FFmpeg转码、HLS流媒体、多码率自适应
- 💳 **多支付集成**: PayPal、PayerMax、Airwallex、Google Play
- 🌍 **国际化支持**: 多语言、多货币、多时区
- 📊 **精准广告**: Facebook Ads、Google Ads、TikTok Ads集成
- 🔐 **企业级安全**: JWT+Redis认证、RBAC权限、数据加密
- 📱 **全端支持**: Web、移动端、管理后台

---

## 2. 整体系统架构

```mermaid
graph TB
    subgraph "客户端层 Client Layer"
        A1[Web客户端<br/>Vue.js + TypeScript]
        A2[移动端应用<br/>iOS/Android]
        A3[管理后台<br/>Vue.js + Element UI]
    end

    subgraph "网关层 Gateway Layer"
        B1[API Gateway<br/>Nginx/Kong]
        B2[负载均衡<br/>HAProxy]
        B3[CDN服务<br/>Cloudflare]
    end

    subgraph "应用服务层 Application Layer"
        C1[管理端服务<br/>shorthub-admin:8080]
        C2[API服务<br/>shorthub-api:8081]
        C3[事件服务<br/>shorthub-event]
        C4[统计服务<br/>shorthub-statistics]
        C5[广告服务<br/>shorthub-ad]
    end

    subgraph "支付服务集群 Payment Services"
        D1[PayPal支付<br/>shorthub-paypal]
        D2[PayerMax支付<br/>shorthub-payermax]
        D3[Airwallex支付<br/>shorthub-airwallex]
        D4[Google Play<br/>shorthub-googleplay]
    end

    subgraph "第三方集成层 Integration Layer"
        E1[微信集成<br/>shorthub-wechat]
        E2[钉钉通知<br/>shorthub-dingtalk]
        E3[邮件服务<br/>shorthub-email]
    end

    subgraph "核心框架层 Core Framework"
        F1[业务框架<br/>shorthub-framework]
        F2[系统管理<br/>shorthub-system]
        F3[通用组件<br/>shorthub-common]
        F4[代码生成<br/>shorthub-generator]
    end

    subgraph "中间件层 Middleware Layer"
        G1[Redis缓存<br/>分布式缓存]
        G2[消息队列<br/>Cloudflare Queue]
        G3[对象存储<br/>Cloudflare R2]
        G4[搜索引擎<br/>Elasticsearch]
    end

    subgraph "数据层 Data Layer"
        H1[MySQL主库<br/>业务数据]
        H2[MySQL从库<br/>只读副本]
        H3[数据备份<br/>定时备份]
    end

    A1 --> B1
    A2 --> B1
    A3 --> B1

    B1 --> B2
    B2 --> C1
    B2 --> C2

    C1 --> F1
    C2 --> F1
    C3 --> F1
    C4 --> F1
    C5 --> F1

    C2 --> D1
    C2 --> D2
    C2 --> D3
    C2 --> D4

    C2 --> E1
    C2 --> E2
    C2 --> E3

    F1 --> F2
    F1 --> F3

    F1 --> G1
    F1 --> G2
    F1 --> G3

    F2 --> H1
    H1 --> H2
    H1 --> H3

    style A1 fill:#e1f5fe
    style A2 fill:#e1f5fe
    style A3 fill:#e1f5fe
    style C1 fill:#f3e5f5
    style C2 fill:#f3e5f5
    style D1 fill:#fff3e0
    style D2 fill:#fff3e0
    style D3 fill:#fff3e0
    style D4 fill:#fff3e0
```

---

## 3. 模块依赖关系图

```mermaid
graph LR
    subgraph "应用模块 Applications"
        admin[shorthub-admin<br/>管理后台]
        api[shorthub-api<br/>API服务]
    end

    subgraph "业务服务模块 Business Services"
        event[shorthub-event<br/>事件服务]
        statistics[shorthub-statistics<br/>统计服务]
        ad[shorthub-ad<br/>广告服务]
    end

    subgraph "支付模块 Payment Modules"
        paypal[shorthub-paypal<br/>PayPal支付]
        payermax[shorthub-payermax<br/>PayerMax支付]
        airwallex[shorthub-airwallex<br/>Airwallex支付]
        googleplay[shorthub-googleplay<br/>Google Play]
    end

    subgraph "集成模块 Integration Modules"
        wechat[shorthub-wechat<br/>微信集成]
        dingtalk[shorthub-dingtalk<br/>钉钉通知]
        email[shorthub-email<br/>邮件服务]
    end

    subgraph "核心模块 Core Modules"
        framework[shorthub-framework<br/>核心框架]
        system[shorthub-system<br/>系统管理]
        common[shorthub-common<br/>通用组件]
        generator[shorthub-generator<br/>代码生成器]
    end

    admin --> framework
    api --> framework

    event --> framework
    statistics --> framework
    ad --> framework

    api --> paypal
    api --> payermax
    api --> airwallex
    api --> googleplay

    api --> wechat
    api --> dingtalk
    api --> email

    framework --> system
    framework --> common

    paypal --> common
    payermax --> common
    airwallex --> common
    googleplay --> common

    wechat --> common
    dingtalk --> common
    email --> common

    system --> common
    generator --> common

    style admin fill:#ffebee
    style api fill:#ffebee
    style framework fill:#e8f5e8
    style common fill:#e3f2fd
```

---

## 4. 核心业务流程架构

### 4.1 支付处理流程架构

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant API as API服务
    participant Order as 订单服务
    participant PayGateway as 支付网关
    participant PayProvider as 支付提供商
    participant Queue as 消息队列
    participant Notify as 通知服务

    Client->>API: 创建支付订单
    API->>Order: 生成本地订单
    Order-->>API: 返回订单信息

    API->>PayGateway: 选择支付渠道
    alt PayPal支付
        PayGateway->>PayProvider: PayPal API调用
    else PayerMax支付
        PayGateway->>PayProvider: PayerMax API调用
    else Airwallex支付
        PayGateway->>PayProvider: Airwallex API调用
    else Google Play支付
        PayGateway->>PayProvider: Google Play验证
    end

    PayProvider-->>PayGateway: 返回支付结果
    PayGateway-->>API: 支付响应
    API-->>Client: 返回支付信息

    PayProvider->>API: Webhook回调
    API->>Order: 更新订单状态
    API->>Queue: 发送处理消息
    Queue->>Notify: 触发通知
    Notify->>Client: 发送通知(钉钉/邮件)
```

### 4.2 视频处理工作流

```mermaid
flowchart TD
    A[视频上传] --> B{文件格式检查}
    B -->|支持格式| C[文件预处理]
    B -->|不支持| D[格式转换]

    C --> E{文件大小检查}
    D --> E

    E -->|< 100MB| F[标准压缩]
    E -->|100MB-500MB| G[GPU加速压缩]
    E -->|> 500MB| H[分片处理]

    F --> I[HLS转码]
    G --> I
    H --> I

    I --> J[生成多码率]
    J --> K[生成缩略图]
    K --> L[上传到R2存储]

    L --> M[生成CDN链接]
    M --> N[更新数据库]
    N --> O[发送处理完成通知]

    subgraph "后台任务"
        P[定时压缩队列]
        Q[质量检测]
        R[存储优化]
    end

    P --> F
    Q --> I
    R --> L

    style A fill:#e1f5fe
    style I fill:#fff3e0
    style L fill:#e8f5e8
    style O fill:#f3e5f5
```

### 4.3 用户认证与权限流程

```mermaid
flowchart TD
    A[用户登录请求] --> B{登录方式}

    B -->|邮箱密码| C[密码验证]
    B -->|Google OAuth| D[Google认证]
    B -->|Facebook OAuth| E[Facebook认证]
    B -->|微信登录| F[微信认证]

    C --> G{验证结果}
    D --> G
    E --> G
    F --> G

    G -->|成功| H[生成JWT Token]
    G -->|失败| I[返回错误信息]

    H --> J[存储Session到Redis]
    J --> K[返回Token给客户端]

    K --> L[客户端携带Token访问]
    L --> M[Token验证中间件]
    M --> N{Token有效性}

    N -->|有效| O[从Redis获取Session]
    N -->|无效| P[返回401错误]

    O --> Q[权限检查]
    Q --> R{权限验证}

    R -->|通过| S[执行业务逻辑]
    R -->|拒绝| T[返回403错误]

    style H fill:#e8f5e8
    style J fill:#fff3e0
    style Q fill:#f3e5f5
```

---

## 5. 数据架构设计

### 5.1 数据库ER图

```mermaid
flowchart TB
    subgraph UserModule["用户模块"]
        Users["APP_USERS<br/>用户信息表<br/>---<br/>• id (PK)<br/>• user_id (UK)<br/>• username<br/>• email<br/>• balance_coin<br/>• balance_bonus<br/>• member_level<br/>• provider"]

        SysUser["SYS_USER<br/>系统用户表<br/>---<br/>• user_id (PK)<br/>• user_name (UK)<br/>• nick_name<br/>• email<br/>• password<br/>• status"]
    end

    subgraph ContentModule["内容模块"]
        Drama["APP_DRAMA<br/>剧集信息表<br/>---<br/>• id (PK)<br/>• title<br/>• description<br/>• cover_image<br/>• total_episodes<br/>• genre<br/>• rating<br/>• view_count"]

        Episodes["APP_EPISODES<br/>剧集分集表<br/>---<br/>• id (PK)<br/>• drama_id (FK)<br/>• episode_number<br/>• title<br/>• video_url<br/>• hls_url<br/>• is_free<br/>• coin_cost"]
    end

    subgraph OrderModule["订单模块"]
        OrderInfo["APP_ORDER_INFO<br/>订单信息表<br/>---<br/>• id (PK)<br/>• order_no (UK)<br/>• user_id (FK)<br/>• item_id<br/>• amount<br/>• currency<br/>• pay_method<br/>• status"]

        RechargeItem["APP_RECHARGE_ITEM<br/>充值商品表<br/>---<br/>• id (PK)<br/>• item_id (UK)<br/>• item_name<br/>• price<br/>• coin_amount<br/>• bonus_amount<br/>• status"]
    end

    subgraph AnalyticsModule["统计模块"]
        WatchRecords["APP_USER_WATCH_RECORDS<br/>观看记录表<br/>---<br/>• id (PK)<br/>• user_id (FK)<br/>• drama_id (FK)<br/>• episode_id (FK)<br/>• watch_progress<br/>• is_completed<br/>• last_watch_time"]

        Consumption["APP_CONSUMPTION<br/>消费记录表<br/>---<br/>• id (PK)<br/>• user_id (FK)<br/>• episode_id (FK)<br/>• coin_consumed<br/>• consumption_type<br/>• create_time"]
    end

    %% 关系连接
    Users -->|"1:N 下单"| OrderInfo
    Users -->|"1:N 观看"| WatchRecords
    Users -->|"1:N 消费"| Consumption

    Drama -->|"1:N 包含"| Episodes
    Drama -->|"1:N 被观看"| WatchRecords

    Episodes -->|"1:N 被观看"| WatchRecords
    Episodes -->|"1:N 消费金币"| Consumption

    RechargeItem -->|"1:N 被购买"| OrderInfo

    style Users fill:#e8f5e8,stroke:#4caf50
    style Drama fill:#e1f5fe,stroke:#2196f3
    style OrderInfo fill:#fff3e0,stroke:#ff9800
    style WatchRecords fill:#f3e5f5,stroke:#9c27b0
```

**核心数据表说明：**

| 表名                         | 说明    | 核心字段                                           |
| -------------------------- | ----- | ---------------------------------------------- |
| **APP_USERS**              | 用户信息表 | user_id, email, balance_coin, member_level     |
| **APP_DRAMA**              | 剧集信息表 | title, genre, country, rating, view_count      |
| **APP_EPISODES**           | 剧集分集表 | drama_id, episode_number, video_url, coin_cost |
| **APP_ORDER_INFO**         | 订单信息表 | order_no, user_id, amount, pay_method, status  |
| **APP_RECHARGE_ITEM**      | 充值商品表 | item_id, price, coin_amount, bonus_amount      |
| **APP_USER_WATCH_RECORDS** | 观看记录表 | user_id, drama_id, episode_id, watch_progress  |
| **APP_CONSUMPTION**        | 消费记录表 | user_id, episode_id, coin_consumed             |
| **SYS_USER**               | 系统用户表 | user_name, email, status, login_date           |

### 5.2 Redis缓存架构

```mermaid
flowchart TB
    subgraph Redis["Redis缓存集群架构"]
        subgraph Session["会话缓存层"]
            A1["用户Session<br/>Key: jwt:user:{userId}<br/>TTL: 7天"]
            A2["登录状态<br/>Key: login:{userId}<br/>TTL: 24小时"]
            A3["权限缓存<br/>Key: permission:{userId}<br/>TTL: 1小时"]
        end

        subgraph Business["业务缓存层"]
            B1["热门剧集<br/>Key: drama:hot:*<br/>TTL: 30分钟"]
            B2["用户偏好<br/>Key: user:preference:{userId}<br/>TTL: 1小时"]
            B3["观看记录<br/>Key: watch:record:{userId}<br/>TTL: 30天"]
        end

        subgraph System["系统缓存层"]
            C1["配置信息<br/>Key: config:*<br/>TTL: 1小时"]
            C2["字典数据<br/>Key: dict:*<br/>TTL: 2小时"]
            C3["菜单权限<br/>Key: menu:*<br/>TTL: 1小时"]
        end

        subgraph Temp["临时缓存层"]
            D1["验证码<br/>Key: captcha:{sessionId}<br/>TTL: 5分钟"]
            D2["短信验证<br/>Key: sms:{phone}<br/>TTL: 10分钟"]
            D3["邮件验证<br/>Key: email:{email}<br/>TTL: 10分钟"]
        end
    end

    subgraph Strategy["缓存策略"]
        E1["缓存预热<br/>启动时加载热点数据<br/>避免冷启动问题"]
        E2["缓存穿透防护<br/>布隆过滤器<br/>空值缓存"]
        E3["缓存雪崩防护<br/>随机过期时间<br/>多级缓存"]
        E4["缓存更新策略<br/>旁路缓存模式<br/>双写一致性"]
    end

    A1 -.-> E1
    B1 -.-> E2
    C1 -.-> E3
    D1 -.-> E4

    style A1 fill:#e8f5e8,stroke:#4caf50
    style B1 fill:#fff3e0,stroke:#ff9800
    style C1 fill:#f3e5f5,stroke:#9c27b0
    style D1 fill:#e1f5fe,stroke:#2196f3
```

**Redis缓存分层策略：**

| 缓存层级     | 数据类型     | 过期时间     | 更新策略         |
| -------- | -------- | -------- | ------------ |
| **会话缓存** | 用户认证信息   | 7天-24小时  | 登录时写入，过期自动清理 |
| **业务缓存** | 热点业务数据   | 30分钟-30天 | 写入时更新，定时刷新   |
| **系统缓存** | 配置字典数据   | 1-2小时    | 修改时主动更新      |
| **临时缓存** | 验证码等临时数据 | 5-10分钟   | 一次性使用后删除     |

---

## 6. 技术栈详细架构

### 6.1 后端技术栈

```mermaid
graph TB
    subgraph "Web框架层"
        A1[Spring Boot 3.3.0<br/>应用框架]
        A2[Spring Security<br/>安全框架]
        A3[Spring Web<br/>Web开发]
    end

    subgraph "数据访问层"
        B1[MyBatis Plus 3.5.7<br/>ORM框架]
        B2[Druid 1.2.20<br/>连接池]
        B3[MySQL 8.0+<br/>关系数据库]
    end

    subgraph "缓存与消息"
        C1[Redis 7.0+<br/>分布式缓存]
        C2[Redisson 3.20.0<br/>分布式锁]
        C3[Cloudflare Queue<br/>消息队列]
    end

    subgraph "工具库"
        D1[Hutool 5.8.25<br/>Java工具库]
        D2[FastJSON 2.0.43<br/>JSON处理]
        D3[EasyExcel 3.1.3<br/>Excel处理]
        D4[JWT 0.9.1<br/>Token认证]
    end

    subgraph "第三方集成"
        E1[WeChat SDK 4.4.4.B<br/>微信集成]
        E2[Cloudflare R2<br/>对象存储]
        E3[FFmpeg 6.0+<br/>视频处理]
    end

    subgraph "监控运维"
        F1[Alibaba ARMS<br/>应用监控]
        F2[Docker<br/>容器化]
        F3[Maven 3.9+<br/>构建工具]
    end

    A1 --> B1
    A1 --> C1
    A1 --> D1
    A1 --> E1
    A1 --> F1

    style A1 fill:#e8f5e8
    style B1 fill:#fff3e0
    style C1 fill:#f3e5f5
    style E1 fill:#e1f5fe
```

### 6.2 前端技术栈

```mermaid
graph TB
    subgraph "前端框架"
        A1[Vue.js 3.x<br/>前端框架]
        A2[TypeScript<br/>类型系统]
        A3[Element Plus<br/>UI组件库]
    end

    subgraph "构建工具"
        B1[Vite<br/>构建工具]
        B2[ESLint<br/>代码检查]
        B3[Prettier<br/>代码格式化]
    end

    subgraph "状态管理"
        C1[Pinia<br/>状态管理]
        C2[Vue Router<br/>路由管理]
    end

    subgraph "HTTP客户端"
        D1[Axios<br/>HTTP请求]
        D2[Request拦截器<br/>Token自动注入]
        D3[Response拦截器<br/>错误统一处理]
    end

    A1 --> B1
    A1 --> C1
    A1 --> D1
    C1 --> C2
    D1 --> D2
    D2 --> D3

    style A1 fill:#e8f5e8
    style B1 fill:#fff3e0
    style C1 fill:#f3e5f5
    style D1 fill:#e1f5fe
```

---

## 7. 部署架构

### 7.1 生产环境部署架构

```mermaid
graph TB
    subgraph "负载均衡层"
        LB1[Nginx-1<br/>主节点]
        LB2[Nginx-2<br/>备节点]
        LB3[Nginx-3<br/>备节点]
    end

    subgraph "应用服务层"
        subgraph "管理后台集群"
            APP1[shorthub-admin-1<br/>:8080]
            APP2[shorthub-admin-2<br/>:8080]
        end

        subgraph "API服务集群"
            API1[shorthub-api-1<br/>:8081]
            API2[shorthub-api-2<br/>:8081]
            API3[shorthub-api-3<br/>:8081]
        end
    end

    subgraph "数据服务层"
        subgraph "数据库集群"
            DB1[MySQL主库<br/>写入节点]
            DB2[MySQL从库1<br/>只读节点]
            DB3[MySQL从库2<br/>只读节点]
        end

        subgraph "缓存集群"
            REDIS1[Redis-Master<br/>主节点]
            REDIS2[Redis-Slave<br/>从节点]
            REDIS3[Redis-Sentinel<br/>哨兵节点]
        end
    end

    subgraph "存储服务"
        STORAGE1[Cloudflare R2<br/>对象存储]
        CDN1[Cloudflare CDN<br/>内容分发]
    end

    LB1 --> APP1
    LB1 --> APP2
    LB2 --> API1
    LB2 --> API2
    LB3 --> API3

    APP1 --> DB1
    APP2 --> DB1
    API1 --> DB1
    API2 --> DB2
    API3 --> DB3

    APP1 --> REDIS1
    API1 --> REDIS1
    API2 --> REDIS1
    API3 --> REDIS1

    API1 --> STORAGE1
    API2 --> STORAGE1
    STORAGE1 --> CDN1

    style LB1 fill:#ffebee
    style APP1 fill:#e8f5e8
    style API1 fill:#fff3e0
    style DB1 fill:#f3e5f5
    style REDIS1 fill:#e1f5fe
```

### 7.2 Docker容器化部署

```yaml
# docker-compose.yml 架构示例
version: '3.8'

services:
  # 应用服务
  shorthub-admin:
    image: shorthub/admin:latest
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - MYSQL_HOST=mysql-master
      - REDIS_HOST=redis-master
    depends_on:
      - mysql-master
      - redis-master
    deploy:
      replicas: 2
      resources:
        limits:
          cpus: '1.0'
          memory: 1G

  shorthub-api:
    image: shorthub/api:latest
    ports:
      - "8081-8083:8081"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - MYSQL_HOST=mysql-master
      - REDIS_HOST=redis-master
    depends_on:
      - mysql-master
      - redis-master
    deploy:
      replicas: 3
      resources:
        limits:
          cpus: '2.0'
          memory: 2G

  # 数据库服务
  mysql-master:
    image: mysql:8.0
    environment:
      - MYSQL_ROOT_PASSWORD=shorthub2024
      - MYSQL_DATABASE=shorthub
    volumes:
      - mysql_data:/var/lib/mysql
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "3306:3306"

  # 缓存服务
  redis-master:
    image: redis:7.0-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes

  # 负载均衡
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - shorthub-admin
      - shorthub-api

volumes:
  mysql_data:
  redis_data:
```

---

## 8. 安全架构

### 8.1 多层安全防护体系

```mermaid
graph TB
    subgraph "网络安全层"
        A1[WAF防护<br/>Web应用防火墙]
        A2[DDoS防护<br/>分布式拒绝服务攻击防护]
        A3[IP白名单<br/>访问控制]
        A4[SSL/TLS<br/>传输加密]
    end

    subgraph "应用安全层"
        B1[JWT认证<br/>无状态Token认证]
        B2[RBAC权限<br/>基于角色的访问控制]
        B3[接口限流<br/>API访问频率限制]
        B4[参数校验<br/>输入验证与过滤]
        B5[XSS防护<br/>跨站脚本攻击防护]
        B6[CSRF防护<br/>跨站请求伪造防护]
    end

    subgraph "数据安全层"
        C1[数据加密<br/>敏感数据AES加密]
        C2[SQL注入防护<br/>参数化查询]
        C3[敏感信息脱敏<br/>日志脱敏处理]
        C4[数据库审计<br/>操作日志记录]
        C5[备份加密<br/>数据备份加密存储]
    end

    subgraph "业务安全层"
        D1[支付安全<br/>RSA签名验证]
        D2[订单防重<br/>幂等性保证]
        D3[风控系统<br/>异常行为检测]
        D4[账户安全<br/>多因子认证]
    end

    A1 --> B1
    A2 --> B2
    A3 --> B3
    A4 --> B4

    B1 --> C1
    B2 --> C2
    B3 --> C3
    B4 --> C4

    C1 --> D1
    C2 --> D2
    C3 --> D3
    C4 --> D4

    style A1 fill:#ffebee
    style B1 fill:#e8f5e8
    style C1 fill:#fff3e0
    style D1 fill:#f3e5f5
```

### 8.2 支付安全架构

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Gateway as API网关
    participant PayService as 支付服务
    participant PayProvider as 支付提供商
    participant Webhook as Webhook处理器
    participant OrderService as 订单服务

    Note over Client,OrderService: 支付安全流程

    Client->>Gateway: 1. 支付请求(HTTPS)
    Gateway->>Gateway: 2. 参数校验与过滤
    Gateway->>PayService: 3. 转发请求

    PayService->>PayService: 4. 生成订单号(防重复)
    PayService->>PayService: 5. 签名生成(RSA/HMAC)
    PayService->>PayProvider: 6. 调用支付API(加密传输)

    PayProvider-->>PayService: 7. 返回支付链接
    PayService-->>Client: 8. 返回支付信息

    PayProvider->>Webhook: 9. 支付回调(签名验证)
    Webhook->>Webhook: 10. 验证签名合法性
    Webhook->>Webhook: 11. 验证订单状态
    Webhook->>OrderService: 12. 更新订单(幂等性)

    Note over Webhook: 安全措施:<br/>1. RSA签名验证<br/>2. 时间戳验证<br/>3. 幂等性保证<br/>4. IP白名单验证
```

---

## 9. 性能优化架构

### 9.1 缓存架构优化

```mermaid
graph TB
    subgraph "多级缓存架构"
        subgraph "CDN缓存"
            CDN1[Cloudflare CDN<br/>静态资源缓存]
            CDN2[视频文件缓存<br/>边缘节点分发]
        end

        subgraph "应用缓存"
            L1[本地缓存<br/>Caffeine Cache]
            L2[JVM堆内缓存<br/>热点数据]
        end

        subgraph "分布式缓存"
            REDIS1[Redis集群<br/>会话与业务缓存]
            REDIS2[缓存预热<br/>启动时加载]
            REDIS3[缓存更新<br/>双写一致性]
        end

        subgraph "数据库缓存"
            DB1[MySQL查询缓存<br/>查询结果缓存]
            DB2[InnoDB缓冲池<br/>页面缓存]
        end
    end

    subgraph "缓存策略"
        STRATEGY1[缓存穿透防护<br/>布隆过滤器]
        STRATEGY2[缓存击穿防护<br/>分布式锁]
        STRATEGY3[缓存雪崩防护<br/>随机过期时间]
        STRATEGY4[热点数据识别<br/>访问频率统计]
    end

    CDN1 --> L1
    L1 --> REDIS1
    REDIS1 --> DB1

    STRATEGY1 --> REDIS1
    STRATEGY2 --> REDIS1
    STRATEGY3 --> REDIS1
    STRATEGY4 --> REDIS2

    style CDN1 fill:#e1f5fe
    style L1 fill:#fff3e0
    style REDIS1 fill:#e8f5e8
    style DB1 fill:#f3e5f5
```

### 9.2 数据库性能优化

```mermaid
graph TB
    subgraph "数据库架构优化"
        subgraph "读写分离"
            MASTER[MySQL主库<br/>写入操作]
            SLAVE1[MySQL从库1<br/>读取操作]
            SLAVE2[MySQL从库2<br/>读取操作]
        end

        subgraph "连接池优化"
            POOL1[Druid连接池<br/>连接复用]
            POOL2[连接监控<br/>性能统计]
            POOL3[慢SQL检测<br/>性能分析]
        end

        subgraph "索引优化"
            INDEX1[主键索引<br/>聚簇索引]
            INDEX2[唯一索引<br/>业务唯一约束]
            INDEX3[复合索引<br/>多列查询优化]
            INDEX4[覆盖索引<br/>避免回表查询]
        end

        subgraph "分库分表"
            SHARD1[用户表分片<br/>按用户ID哈希]
            SHARD2[订单表分片<br/>按时间范围]
            SHARD3[日志表分片<br/>按日期分表]
        end
    end

    MASTER --> SLAVE1
    MASTER --> SLAVE2

    POOL1 --> MASTER
    POOL1 --> SLAVE1
    POOL1 --> SLAVE2

    INDEX1 --> SHARD1
    INDEX2 --> SHARD2
    INDEX3 --> SHARD3

    style MASTER fill:#ffebee
    style SLAVE1 fill:#e8f5e8
    style POOL1 fill:#fff3e0
    style INDEX1 fill:#f3e5f5
```

---

## 10. 监控与运维架构

### 10.1 应用监控体系

```mermaid
flowchart TB
    subgraph Collect["监控数据采集层"]
        A1["应用指标采集<br/>• JVM堆内存使用率<br/>• GC频率和耗时<br/>• 线程池状态<br/>• 接口响应时间"]
        A2["业务指标采集<br/>• 用户注册/登录量<br/>• 订单创建/支付成功率<br/>• 视频播放/完播率<br/>• 收入和ARPU"]
        A3["基础设施采集<br/>• CPU使用率<br/>• 内存使用率<br/>• 磁盘I/O<br/>• 网络流量"]
        A4["中间件监控<br/>• Redis连接数/命中率<br/>• MySQL慢查询/连接数<br/>• MQ消息堆积<br/>• CDN流量"]
    end

    subgraph Process["数据处理与存储层"]
        B1["应用监控平台<br/>Alibaba ARMS<br/>• 实时性能监控<br/>• 应用拓扑图<br/>• 异常检测"]
        B2["时序数据库<br/>InfluxDB/Prometheus<br/>• 指标数据存储<br/>• 时间序列查询<br/>• 数据聚合分析"]
        B3["日志聚合平台<br/>ELK Stack<br/>• 日志收集和索引<br/>• 全文搜索<br/>• 日志分析"]
        B4["链路追踪系统<br/>Jaeger/Zipkin<br/>• 分布式调用链<br/>• 性能瓶颈分析<br/>• 错误追踪"]
    end

    subgraph Alert["告警与通知层"]
        C1["告警规则引擎<br/>• 阈值告警配置<br/>• 趋势异常检测<br/>• 多维度告警<br/>• 智能降噪"]
        C2["告警级别管理<br/>• P0: 系统故障<br/>• P1: 核心功能异常<br/>• P2: 性能降级<br/>• P3: 一般告警"]
        C3["多渠道通知<br/>• 钉钉机器人<br/>• 邮件通知<br/>• 短信告警<br/>• 电话告警"]
        C4["告警收敛机制<br/>• 同类告警合并<br/>• 告警风暴抑制<br/>• 静默期设置<br/>• 升级机制"]
    end

    subgraph Visual["可视化展示层"]
        D1["实时监控大屏<br/>• 关键指标实时展示<br/>• 系统健康状态<br/>• 业务数据概览<br/>• 异常事件追踪"]
        D2["业务运营报表<br/>• 用户增长趋势<br/>• 收入分析报告<br/>• 内容消费统计<br/>• 渠道效果分析"]
        D3["技术性能报告<br/>• 系统性能趋势<br/>• 容量规划建议<br/>• 故障复盘报告<br/>• 优化改进建议"]
        D4["自定义监控看板<br/>• 个性化指标配置<br/>• 多维度数据钻取<br/>• 部门级监控视图<br/>• 移动端监控APP"]
    end

    A1 --> B1
    A2 --> B1
    A3 --> B2
    A4 --> B3

    B1 --> C1
    B2 --> C2
    B3 --> C3
    B4 --> C4

    C1 --> D1
    C2 --> D2
    C3 --> D3
    C4 --> D4

    style A1 fill:#e1f5fe,stroke:#2196f3
    style B1 fill:#e8f5e8,stroke:#4caf50
    style C1 fill:#fff3e0,stroke:#ff9800
    style D1 fill:#f3e5f5,stroke:#9c27b0
```

**监控体系核心指标：**

| 监控类型     | 核心指标         | 告警阈值               | 处理方式       |
| -------- | ------------ | ------------------ | ---------- |
| **应用性能** | 响应时间、QPS、错误率 | RT>500ms, 错误率>1%   | 自动扩容、服务降级  |
| **业务指标** | 支付成功率、视频播放率  | 支付成功率<95%          | 紧急处理、业务方通知 |
| **基础设施** | CPU、内存、磁盘使用率 | CPU>80%, 内存>85%    | 自动告警、容量扩展  |
| **数据库**  | 连接数、慢查询、锁等待  | 连接数>80%, 慢查询>100ms | SQL优化、读写分离 |

### 10.2 日志架构体系

```mermaid
graph LR
    subgraph "日志生成"
        APP1[应用日志<br/>业务操作日志]
        APP2[访问日志<br/>HTTP请求日志]
        APP3[错误日志<br/>异常堆栈日志]
        APP4[审计日志<br/>敏感操作日志]
    end

    subgraph "日志收集"
        AGENT1[Filebeat<br/>日志收集代理]
        AGENT2[Logstash<br/>日志处理管道]
    end

    subgraph "日志存储"
        ES1[Elasticsearch<br/>日志搜索引擎]
        ES2[索引管理<br/>按日期分片]
        ES3[数据清理<br/>定期清理过期日志]
    end

    subgraph "日志分析"
        KIBANA1[Kibana<br/>日志可视化分析]
        KIBANA2[自定义报表<br/>业务日志分析]
        KIBANA3[实时监控<br/>错误日志告警]
    end

    APP1 --> AGENT1
    APP2 --> AGENT1
    APP3 --> AGENT1
    APP4 --> AGENT1

    AGENT1 --> AGENT2
    AGENT2 --> ES1

    ES1 --> ES2
    ES2 --> ES3

    ES1 --> KIBANA1
    KIBANA1 --> KIBANA2
    KIBANA2 --> KIBANA3

    style APP1 fill:#e1f5fe
    style AGENT1 fill:#e8f5e8
    style ES1 fill:#fff3e0
    style KIBANA1 fill:#f3e5f5
```

---

## 11. 扩展性设计

### 11.1 模块化架构扩展

```mermaid
flowchart TB
    subgraph Gateway["网关接入层"]
        Nginx["Nginx反向代理<br/>• 负载均衡<br/>• SSL终结<br/>• 静态资源服务"]
        LB["负载均衡器<br/>• 健康检查<br/>• 故障转移<br/>• 流量分发"]
    end

    subgraph AppLayer["应用服务层 - 单体架构"]
        AdminApp["管理后台应用<br/>shorthub-admin<br/>Port: 8080<br/>---<br/>• 内容管理<br/>• 用户管理<br/>• 订单管理<br/>• 系统配置"]

        ApiApp["API服务应用<br/>shorthub-api<br/>Port: 8081<br/>---<br/>• 用户接口<br/>• 内容接口<br/>• 支付接口<br/>• 统计接口"]
    end

    subgraph ModuleLayer["模块化组件层"]
        subgraph PaymentModules["支付模块集群"]
            PayPal["PayPal支付模块<br/>shorthub-paypal"]
            PayerMax["PayerMax支付模块<br/>shorthub-payermax"]
            Airwallex["Airwallex支付模块<br/>shorthub-airwallex"]
            GooglePlay["Google Play模块<br/>shorthub-googleplay"]
        end

        subgraph IntegrationModules["集成模块集群"]
            WeChat["微信集成模块<br/>shorthub-wechat"]
            DingTalk["钉钉通知模块<br/>shorthub-dingtalk"]
            Email["邮件服务模块<br/>shorthub-email"]
        end

        subgraph BusinessModules["业务模块集群"]
            Event["事件处理模块<br/>shorthub-event"]
            Statistics["统计分析模块<br/>shorthub-statistics"]
            Ad["广告服务模块<br/>shorthub-ad"]
        end
    end

    subgraph CoreLayer["核心框架层"]
        Framework["核心框架<br/>shorthub-framework<br/>---<br/>• 权限控制<br/>• 会话管理<br/>• 安全认证"]

        System["系统管理<br/>shorthub-system<br/>---<br/>• 用户管理<br/>• 角色管理<br/>• 菜单管理"]

        Common["通用组件<br/>shorthub-common<br/>---<br/>• 工具类库<br/>• 常量定义<br/>• 枚举类型"]
    end

    subgraph DataLayer["数据服务层"]
        MySQL["MySQL数据库<br/>• 主从复制<br/>• 读写分离"]
        Redis["Redis缓存<br/>• 集群部署<br/>• 数据分片"]
        Storage["Cloudflare R2<br/>• 对象存储<br/>• CDN分发"]
    end

    Gateway --> AppLayer
    AdminApp --> ModuleLayer
    ApiApp --> ModuleLayer
    ModuleLayer --> CoreLayer
    CoreLayer --> DataLayer

    style AdminApp fill:#ffebee,stroke:#f44336
    style ApiApp fill:#ffebee,stroke:#f44336
    style PayPal fill:#fff3e0,stroke:#ff9800
    style WeChat fill:#e8f5e8,stroke:#4caf50
    style Event fill:#f3e5f5,stroke:#9c27b0
    style Framework fill:#e1f5fe,stroke:#2196f3
```

**模块化架构扩展策略：**

| 扩展类型     | 扩展方式   | 实现方案               | 优势        |
| -------- | ------ | ------------------ | --------- |
| **水平扩展** | 增加应用实例 | Docker容器化部署，负载均衡分发 | 处理能力线性增长  |
| **垂直扩展** | 升级硬件配置 | 增加CPU、内存、存储资源      | 单实例性能提升   |
| **模块扩展** | 新增业务模块 | Maven模块化开发，插件式集成   | 功能解耦，易于维护 |
| **数据扩展** | 数据库分片  | 按业务维度或时间维度分库分表     | 突破单库性能瓶颈  |

**模块依赖管理：**

```mermaid
flowchart LR
    subgraph Dependencies["Maven依赖关系"]
        A["shorthub-admin"] --> F["shorthub-framework"]
        B["shorthub-api"] --> F

        F --> S["shorthub-system"]
        F --> C["shorthub-common"]

        B --> P1["shorthub-paypal"]
        B --> P2["shorthub-payermax"]
        B --> P3["shorthub-airwallex"]
        B --> G["shorthub-googleplay"]

        B --> W["shorthub-wechat"]
        B --> D["shorthub-dingtalk"]
        B --> E["shorthub-email"]

        P1 --> C
        P2 --> C
        P3 --> C
        G --> C
        W --> C
        D --> C
        E --> C
        S --> C
    end

    style A fill:#ffcdd2
    style B fill:#ffcdd2
    style F fill:#c8e6c9
    style C fill:#bbdefb
```

### 11.2 容量规划与扩展策略

```mermaid
graph TB
    subgraph "流量预估"
        TRAFFIC1[日活用户: 10万+]
        TRAFFIC2[并发用户: 1万+]
        TRAFFIC3[视频观看: 100万次/天]
        TRAFFIC4[支付订单: 1万笔/天]
    end

    subgraph "容量规划"
        CAPACITY1[应用服务器: 4-8台]
        CAPACITY2[数据库: 1主2从]
        CAPACITY3[Redis集群: 3节点]
        CAPACITY4[存储容量: 10TB+]
    end

    subgraph "扩展策略"
        SCALE1[水平扩展<br/>增加服务器实例]
        SCALE2[垂直扩展<br/>升级服务器配置]
        SCALE3[数据库分片<br/>按业务维度拆分]
        SCALE4[CDN优化<br/>全球节点分布]
    end

    subgraph "性能目标"
        PERF1[响应时间: <200ms]
        PERF2[可用性: >99.9%]
        PERF3[并发处理: 1万QPS]
        PERF4[故障恢复: <5分钟]
    end

    TRAFFIC1 --> CAPACITY1
    TRAFFIC2 --> CAPACITY2
    TRAFFIC3 --> CAPACITY3
    TRAFFIC4 --> CAPACITY4

    CAPACITY1 --> SCALE1
    CAPACITY2 --> SCALE2
    CAPACITY3 --> SCALE3
    CAPACITY4 --> SCALE4

    SCALE1 --> PERF1
    SCALE2 --> PERF2
    SCALE3 --> PERF3
    SCALE4 --> PERF4

    style TRAFFIC1 fill:#e1f5fe
    style CAPACITY1 fill:#e8f5e8
    style SCALE1 fill:#fff3e0
    style PERF1 fill:#f3e5f5
```

---

## 12. 总结

Shorthub-TV短剧平台是一个技术先进、架构完善的现代化视频平台系统。通过采用微服务架构、多级缓存、读写分离、消息队列等技术方案，系统具备了以下核心优势：

### 12.1 技术优势

- ✅ **高性能**: 多级缓存架构，支持万级并发
- ✅ **高可用**: 主从部署，故障自动切换
- ✅ **可扩展**: 微服务架构，水平扩展能力强
- ✅ **安全性**: 多层安全防护，支付安全保障
- ✅ **国际化**: 多语言、多货币、多时区支持

### 12.2 业务特色

- 🎬 **智能视频处理**: FFmpeg转码，多码率自适应播放
- 💳 **多支付集成**: 4大主流支付平台无缝集成
- 📊 **精准营销**: 多平台广告投放，数据驱动优化
- 🔔 **实时通知**: 钉钉、邮件多渠道消息推送
- 📱 **全端覆盖**: Web、移动端、管理后台全覆盖

### 12.3 运维保障

- 📈 **全链路监控**: 从基础设施到业务指标全覆盖
- 🚀 **容器化部署**: Docker容器化，支持快速部署和扩展
- 🔄 **CI/CD流水线**: 自动化构建、测试、部署
- 💾 **数据备份**: 多级备份策略，数据安全保障

该架构设计充分考虑了业务发展需求，能够支撑大规模用户增长和业务扩展，为Shorthub-TV平台的长期发展提供坚实的技术基础。 