<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="${packageName}.mapper.${ClassName}Mapper">

    <resultMap type="${ClassName}" id="${ClassName}Result">
#foreach ($column in $columns)
        <result property="${column.javaField}"    column="${column.columnName}"    />
#end
    </resultMap>
#if($table.sub)

    <resultMap id="${ClassName}${subClassName}Result" type="${ClassName}" extends="${ClassName}Result">
        <collection property="${subclassName}List" notNullColumn="sub_${subTable.pkColumn.columnName}" javaType="java.util.List" resultMap="${subClassName}Result" />
    </resultMap>

    <resultMap type="${subClassName}" id="${subClassName}Result">
#foreach ($column in $subTable.columns)
        <result property="${column.javaField}"    column="sub_${column.columnName}"    />
#end
    </resultMap>
#end

    <sql id="select${ClassName}Vo">
        select#foreach($column in $columns) $column.columnName#if($foreach.count != $columns.size()),#end#end from ${tableName}
    </sql>

    <insert id="batchInsertOrUpdate">
        <foreach item="item" index="index" collection="list" separator=";">
            insert into ${tableName}
            <trim prefix="(" suffix=")" suffixOverrides=",">
                #foreach($column in $columns)
                    #if($column.columnName != $pkColumn.columnName || !$pkColumn.increment)
                        <if test="item.$column.javaField != null#if($column.javaType == 'String' && $column.required) and item.$column.javaField != ''#end">$column.columnName,</if>
                    #end
                #end
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                #foreach($column in $columns)
                    #if($column.columnName != $pkColumn.columnName || !$pkColumn.increment)
                        <if test="item.$column.javaField != null#if($column.javaType == 'String' && $column.required) and item.$column.javaField != ''#end">#{item.$column.javaField},</if>
                    #end
                #end
            </trim>
            on duplicate key update
            <trim suffixOverrides=",">
                #foreach($column in $columns)
                    #if($column.columnName != $pkColumn.columnName)
                        <if test="item.$column.javaField != null#if($column.javaType == 'String' && $column.required) and item.$column.javaField != ''#end">$column.columnName = values($column.columnName),</if>
                    #end
                #end
            </trim>
        </foreach>

    </insert>

    <select id="getSummary" parameterType="${ClassName}" resultMap="${ClassName}Result">
        select
            max(id) as id
        #foreach($column in $columns)
        #if($column.summary)
            ,sum($column.columnName) as $column.columnName
        #end
        #end
        from ${tableName}
        <where>
        #foreach($column in $columns)
        #set($queryType=$column.queryType)
        #set($javaField=$column.javaField)
        #set($javaType=$column.javaType)
        #set($columnName=$column.columnName)
        #set($AttrName=$column.javaField.substring(0,1).toUpperCase() + ${column.javaField.substring(1)})
        #if($column.query)
        #if($column.queryType == "EQ")
                    <if test="$javaField != null #if($javaType == 'String' ) and $javaField.trim() != ''#end"> and $columnName = #{$javaField}</if>
        #elseif($queryType == "NE")
                    <if test="$javaField != null #if($javaType == 'String' ) and $javaField.trim() != ''#end"> and $columnName != #{$javaField}</if>
        #elseif($queryType == "GT")
                    <if test="$javaField != null #if($javaType == 'String' ) and $javaField.trim() != ''#end"> and $columnName &gt; #{$javaField}</if>
        #elseif($queryType == "GTE")
                    <if test="$javaField != null #if($javaType == 'String' ) and $javaField.trim() != ''#end"> and $columnName &gt;= #{$javaField}</if>
        #elseif($queryType == "LT")
                    <if test="$javaField != null #if($javaType == 'String' ) and $javaField.trim() != ''#end"> and $columnName &lt; #{$javaField}</if>
        #elseif($queryType == "LTE")
                    <if test="$javaField != null #if($javaType == 'String' ) and $javaField.trim() != ''#end"> and $columnName &lt;= #{$javaField}</if>
        #elseif($queryType == "LIKE")
                    <if test="$javaField != null #if($javaType == 'String' ) and $javaField.trim() != ''#end"> and $columnName like concat('%', #{$javaField}, '%')</if>
        #elseif($queryType == "BETWEEN")
                    <if test="params.begin$AttrName != null and params.begin$AttrName != '' and params.end$AttrName != null and params.end$AttrName != ''"> and $columnName between #{params.begin$AttrName} and #{params.end$AttrName}</if>
        #end
        #end
        #end
                </where>
    </select>



    <select id="summary" parameterType="tv.shorthub.common.core.domain.SummaryRequest" resultMap="${ClassName}Result">
        #*    <include refid="mapper.common.tv.shorthub.CommonMapper.summarySql">
                <property name="tableName" value="${tableName}"/>
            </include>*#
    </select>
    <select id="allSummary" parameterType="tv.shorthub.common.core.domain.SummaryRequest" resultMap="${ClassName}Result">
        #*    <include refid="mapper.common.tv.shorthub.CommonMapper.allSummarySql">
                <property name="tableName" value="${tableName}"/>
            </include>*#
    </select>

#if($table.sub)
    <delete id="delete${subClassName}By${subTableFkClassName}s" parameterType="String">
        delete from ${subTableName} where ${subTableFkName} in
        <foreach item="${subTableFkclassName}" collection="array" open="(" separator="," close=")">
            #{${subTableFkclassName}}
        </foreach>
    </delete>

    <delete id="delete${subClassName}By${subTableFkClassName}" parameterType="${pkColumn.javaType}">
        delete from ${subTableName} where ${subTableFkName} = #{${subTableFkclassName}}
    </delete>

    <insert id="batch${subClassName}">
        insert into ${subTableName}(#foreach($column in $subTable.columns) $column.columnName#if($foreach.count != $subTable.columns.size()),#end#end) values
		<foreach item="item" index="index" collection="list" separator=",">
            (#foreach($column in $subTable.columns) #{item.$column.javaField}#if($foreach.count != $subTable.columns.size()),#end#end)
        </foreach>
    </insert>
#end
</mapper>
