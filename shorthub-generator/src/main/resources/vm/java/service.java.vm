package ${packageName}.service;

import ${packageName}.mapper.${ClassName}Mapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import ${packageName}.domain.${ClassName};
import tv.shorthub.common.core.service.IBaseService;

/**
 * ${functionName}Service接口
 *
 * <AUTHOR>
 * @date ${datetime}
 */
public interface I${ClassName}Service extends IBaseService<${ClassName}>
{
    /**
     * 查询${functionName}
     *
     * @param ${pkColumn.javaField} ${functionName}主键
     * @return ${functionName}
     */
    public ${ClassName} getBy${pkColumn.capJavaField}(${pkColumn.javaType} ${pkColumn.javaField});

    /**
     * 查询${functionName}数据汇总
     *
     * @param query ${functionName}
     * @return ${functionName}数据汇总
     */
    public ${ClassName} getSummary(${ClassName} query);

    /**
     * 查询${functionName}列表
     *
     * @param query ${functionName}
     * @return ${functionName}集合
     */
    public List<${ClassName}> selectList(${ClassName} query);

    /**
     * 新增${functionName}
     *
     * @param ${className} ${functionName}
     * @return 结果
     */
    public int insert(${ClassName} ${className});

    /**
     * 修改${functionName}
     *
     * @param ${className} ${functionName}
     * @return 结果
     */
    public int update(${ClassName} ${className});

    /**
     * 批量删除${functionName}
     *
     * @param ${pkColumn.javaField}s 需要删除的${functionName}主键集合
     * @return 结果
     */
    public int deleteBy${pkColumn.capJavaField}s(List<${pkColumn.javaType}> ${pkColumn.javaField}s);

    /**
     * 删除${functionName}信息
     *
     * @param ${pkColumn.javaField} ${functionName}主键
     * @return 结果
     */
    public int deleteBy${pkColumn.capJavaField}(${pkColumn.javaType} ${pkColumn.javaField});


    /**
     * 查询自定义分析数据
     *
     * @param query ${functionName}
     * @return ${functionName}集合
     */
    public List<${ClassName}> summary(SummaryRequest query);

    ${ClassName} allSummary(SummaryRequest query);

    ${ClassName}Mapper getMapper();
}
