package tv.shorthub.wechat.config;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.api.impl.WxMaServiceImpl;
import com.github.binarywang.wxpay.service.WxPayService;
import com.github.binarywang.wxpay.service.impl.WxPayServiceImpl;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.api.impl.WxMpServiceImpl;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 加载微信服务
 */
@Configuration
public class WechatConfiguration {

//
//    @Bean
//    public WxPayService wxPayService() {
//        return new WxPayServiceImpl();
//    }
//
//    @Bean
//    public WxMaService wxMaService() {
//        return new WxMaServiceImpl();
//    }
//
//    @Bean
//    public WxMpService wxMpService() {
//        return new WxMpServiceImpl();
//    }

}
