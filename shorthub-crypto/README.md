# ShortHub 虚拟货币支付模块

## 概述

ShortHub-Crypto 是一个支持多链虚拟货币充值的支付模块，专为高并发场景设计，支持 USDC 和 USDT 在 Tron、Base、Solana 三个区块链网络上的充值确认。

## 主要特性

### 1. 多链支持
- **Tron Network**: 支持 TRC20 USDT/USDC
- **Base Network**: 支持 ERC20 USDT/USDC  
- **Solana Network**: 支持 SPL USDT/USDC

### 2. 高并发处理
- 分布式锁保证钱包地址分配的原子性
- Redis 缓存优化查询性能
- 异步交易监听和处理
- 支持水平扩展

### 3. 充值确认机制
- 自动监听区块链交易
- 可配置的确认数要求
- 重复充值防护
- 交易失败重试机制

### 4. 安全性保障
- 钱包地址池管理
- 交易幂等性保证
- 金额精度处理
- 异常处理和恢复

## 系统架构

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   用户客户端     │    │   API 控制层      │    │   业务服务层     │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                        │                        │
         │ 1.创建充值订单           │                        │
         └─────────────────────────┼────────────────────────┤
                                 │                        │
                                 │ 2.分配钱包地址           │
                                 └────────────────────────┤
                                                         │
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   区块链网络     │    │   交易监听层      │    │   数据存储层     │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                        │                        │
         │ 3.用户发起转账           │                        │
         └─────────────────────────┼────────────────────────┤
                                 │                        │
                                 │ 4.监听交易确认           │
                                 └────────────────────────┤
                                                         │
                                 │ 5.处理充值到账           │
                                 └─────────────────────────┘
```

## 核心组件

### 1. 区块链服务层 (BlockchainService)
- 统一的区块链操作接口
- 支持不同区块链的具体实现
- 钱包地址生成和验证
- 交易查询和确认

### 2. 支付服务层 (CryptoPaymentService)  
- 充值订单管理
- 交易监听和处理
- 并发控制和防重复处理
- 用户充值历史查询

### 3. 钱包管理服务 (CryptoWalletService)
- 钱包地址池管理
- 用户钱包分配
- 钱包状态维护
- 预热和清理机制

### 4. 定时任务服务 (CryptoTransactionMonitorService)
- 定时监听新交易
- 钱包池预热
- 过期数据清理
- 统计报告生成

## API 接口

### 创建充值订单
```http
POST /crypto/payment/deposit/create
Content-Type: application/json

{
  "userId": "user123",
  "blockchain": "tron",
  "tokenType": "USDT", 
  "amount": 100.50,
  "deviceType": "web",
  "extendInfo": "用户充值"
}
```

### 查询订单状态
```http
GET /crypto/payment/deposit/query/{orderNo}
```

### 获取用户充值历史
```http
GET /crypto/payment/deposit/history?userId=user123&limit=10
```

### 验证交易确认数
```http
GET /crypto/payment/transaction/confirmations?txHash=xxx&blockchain=tron
```

## 配置说明

在 `application.yml` 中添加以下配置：

```yaml
crypto:
  payment:
    enabled: true
    testnet: false
    wallet-pool-size: 100
    transaction-check-interval: 30
    max-retry-count: 3
    transaction-timeout-minutes: 30
    
    tron:
      enabled: true
      rpc-url: "https://api.trongrid.io"
      confirmations: 19
      token-contracts:
        USDT: "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t"
        USDC: "TEkxiTehnzSmSe2XqrBj4w32RUN966rdz8"
    
    # Base 和 Solana 配置类似...
```

## 数据库表结构

### crypto_payment_config - 支付配置表
- id: 主键
- config_name: 配置名称
- blockchain: 区块链类型
- token_type: 代币类型
- contract_address: 合约地址
- rpc_url: RPC节点地址
- enabled: 是否启用

### crypto_wallet - 钱包地址表
- id: 主键
- address: 钱包地址
- blockchain: 区块链类型
- status: 状态（0-可用，1-已分配，2-已禁用）
- assigned_user_id: 分配的用户ID
- assigned_time: 分配时间

### crypto_deposit_record - 充值记录表
- id: 主键
- order_no: 订单号
- user_id: 用户ID
- tx_hash: 交易哈希
- blockchain: 区块链类型
- token_type: 代币类型
- amount: 充值金额
- status: 状态
- confirmations: 确认数
- process_status: 处理状态

## 并发处理方案

### 1. 钱包地址分配
使用分布式锁确保同一用户在同一区块链上最多只能分配一个钱包地址：
```java
String lockKey = "crypto:lock:wallet:" + blockchain + ":" + userId;
RLock lock = redissonClient.getLock(lockKey);
```

### 2. 交易处理
使用交易哈希作为锁键，避免重复处理同一笔交易：
```java
String lockKey = "crypto:lock:transaction:" + txHash;
RLock lock = redissonClient.getLock(lockKey);
```

### 3. 重复充值防护
通过 Redis 标记已处理的交易，避免重复入账：
```java
String key = "crypto:processed:" + txHash;
redisTemplate.setex(key, 86400, "1"); // 24小时过期
```

## 监控和告警

### 关键指标
- 充值订单创建成功率
- 交易确认平均时间
- 钱包地址可用数量
- 异常交易数量
- 系统处理延迟

### 日志记录
- 订单创建和状态变更
- 交易监听和处理结果  
- 异常情况和错误信息
- 性能指标和统计数据

## 扩展性设计

### 1. 新增区块链支持
实现 `BlockchainService` 接口：
```java
@Service
public class NewBlockchainService implements BlockchainService {
    @Override
    public BlockchainType getSupportedBlockchain() {
        return BlockchainType.NEW_BLOCKCHAIN;
    }
    // 实现其他方法...
}
```

### 2. 新增代币支持
在 `CryptoTokenType` 枚举中添加新代币：
```java
NEW_TOKEN("NEWTOKEN", "New Token", 18)
```

### 3. 自定义业务逻辑
通过继承或实现相关接口，可以自定义：
- 钱包地址生成策略
- 交易确认规则  
- 充值处理逻辑
- 通知机制

## 部署说明

### 1. 依赖环境
- Java 21+
- Spring Boot 3.3+
- Redis 7.0+
- MySQL 8.0+
- Maven 3.8+

### 2. 数据库初始化
执行相应的 SQL 脚本创建表结构。

### 3. 配置文件
根据实际环境修改 `application-crypto.yml` 配置。

### 4. 启动服务
```bash
mvn clean package
java -jar shorthub-crypto-1.0.0.jar --spring.profiles.active=crypto
```

## 注意事项

1. **私钥安全**: 钱包私钥需要加密存储，生产环境建议使用硬件安全模块(HSM)
2. **网络配置**: 确保服务器能够访问对应的区块链RPC节点
3. **监控告警**: 建议配置完善的监控和告警机制
4. **备份恢复**: 定期备份钱包信息和交易记录
5. **测试验证**: 部署前在测试网络进行充分测试

## 常见问题

### Q: 如何处理网络异常导致的交易监听中断？
A: 系统具有自动重试机制，同时定时任务会定期扫描和处理遗漏的交易。

### Q: 钱包地址用完了怎么办？  
A: 系统会自动创建新的钱包地址，也可以通过预热任务提前准备充足的地址池。

### Q: 如何确保充值金额的准确性？
A: 使用 BigDecimal 处理金额计算，并保留原始的区块链数据作为对账依据。

### Q: 支持哪些代币精度？
A: 当前支持 6 位小数精度的 USDT/USDC，可以根据需要扩展支持其他精度。 

# 虚拟货币交易监听优化

## 概述

本次优化主要针对虚拟货币交易监听规则进行了全面改进，实现了基于 `CryptoTokenConfig` 的配置化管理，减少了对外部 RPC 节点的依赖，并增加了轮询交易结果的功能。同时新增了区块监听功能，将原始报文保存到对应的区块日志表中。

## 主要优化内容

### 1. 基于 CryptoTokenConfig 的监听功能

- **配置化管理**: 所有代币相关的配置（精度、合约地址、最小/最大充值金额、所需确认数等）都通过 `CryptoTokenConfig` 表进行管理
- **减少 RPC 依赖**: 通过缓存和配置化方式，减少了对区块链 RPC 节点的频繁调用
- **统一配置源**: 所有涉及精度、地址的获取都统一使用 `CryptoTokenConfig` 配置

### 2. 轮询交易结果功能

- **定时轮询**: 每60秒自动轮询待确认的交易状态
- **确认数检查**: 根据配置的所需确认数检查交易是否已确认
- **超时处理**: 自动处理超时的交易，避免长时间等待
- **重试机制**: 支持失败交易的重试功能

### 3. 区块监听功能

- **监听所有区块**: 每30秒自动监听所有区块链的新区块
- **保存原始报文**: 将完整的区块数据保存到对应的区块日志表（`crypto_tron_block`、`crypto_base_block`、`crypto_solana_block`）
- **避免重复拉取**: 只保存链上有效的区块，避免重复处理
- **数据持久化**: 区块数据持久化存储，便于后续查询和分析

### 4. 优化交易监听流程

- **从区块日志表获取数据**: `monitorTokenTransactions` 方法现在从区块日志表获取数据，而不是直接从区块链服务获取
- **解析区块交易**: 从保存的区块数据中解析出相关的代币转账交易
- **减少RPC调用**: 通过本地数据库查询替代频繁的RPC调用
- **提高处理效率**: 批量处理区块数据，提高交易监听效率

### 5. 新增服务类

#### CryptoBlockMonitorService
专门处理区块监听和保存：

```java
// 监听所有区块（定时任务）
void monitorAllBlocks()

// 监听TRON区块
void monitorTronBlocks()

// 监听BASE区块  
void monitorBaseBlocks()

// 监听SOLANA区块
void monitorSolanaBlocks()

// 获取指定范围的区块数据
List<CryptoTronBlock> getTronBlocks(Long fromBlock, Long toBlock)
List<CryptoBaseBlock> getBaseBlocks(Long fromBlock, Long toBlock)
List<CryptoSolanaBlock> getSolanaBlocks(Long fromBlock, Long toBlock)
```

#### CryptoBlockController
提供区块监听相关的 API 接口：

```java
// 手动触发区块监听
POST /crypto/block/monitor

// 获取区块列表
GET /crypto/block/tron?fromBlock=1000&toBlock=1010
GET /crypto/block/base?fromBlock=1000&toBlock=1010  
GET /crypto/block/solana?fromBlock=1000&toBlock=1010

// 获取最新区块高度
GET /crypto/block/latest/{blockchain}
```

### 6. 优化的现有服务

#### CryptoTransactionMonitorService
优化了交易监听逻辑：

```java
// 从区块日志表获取新区块数据
List<Object> getNewBlocksFromDatabase(String blockchain, Long fromBlock, Long toBlock)

// 处理区块中的交易
void processBlockTransactions(Object block, List<CryptoWallet> wallets, CryptoTokenConfig tokenConfig)

// 从区块中提取交易信息
List<TransactionInfo> extractTransactionsFromBlock(Object block, CryptoTokenConfig tokenConfig)

// 从不同区块链区块提取交易
List<TransactionInfo> extractTronTransactions(CryptoTronBlock block, CryptoTokenConfig tokenConfig)
List<TransactionInfo> extractBaseTransactions(CryptoBaseBlock block, CryptoTokenConfig tokenConfig)
List<TransactionInfo> extractSolanaTransactions(CryptoSolanaBlock block, CryptoTokenConfig tokenConfig)
```

#### CryptoBlockMonitorService
优化了区块监听逻辑：

```java
// 监听所有区块（定时任务）
void monitorAllBlocks()

// 监听TRON区块
void monitorTronBlocks()

// 监听BASE区块  
void monitorBaseBlocks()

// 监听SOLANA区块
void monitorSolanaBlocks()

// 获取区块信息（通用方法）
JSONObject getBlockInfo(BlockchainService blockchainService, Long blockNumber)

// 获取指定范围的区块数据
List<CryptoTronBlock> getTronBlocks(Long fromBlock, Long toBlock)
List<CryptoBaseBlock> getBaseBlocks(Long fromBlock, Long toBlock)
List<CryptoSolanaBlock> getSolanaBlocks(Long fromBlock, Long toBlock)
```

### 7. 接口优化

#### BlockchainService接口
新增了获取区块信息的通用方法：

```java
/**
 * 获取区块信息
 * @param blockNumber 区块高度
 * @return 区块信息JSON对象
 */
JSONObject getBlockInfo(Long blockNumber);
```

#### 具体实现示例
在具体的区块链服务实现中，需要实现`getBlockInfo`方法：

##### BASE区块链服务实现
```java
@Override
public JSONObject getBlockInfo(Long blockNumber) {
    try {
        String rpcUrl = config.isTestnet() ? config.getBase().getTestnetRpcUrl() : config.getBase().getRpcUrl();
        
        // 构建RPC请求
        JSONObject requestBody = new JSONObject();
        requestBody.put("jsonrpc", "2.0");
        requestBody.put("method", "eth_getBlockByNumber");
        requestBody.put("params", Arrays.asList("0x" + Long.toHexString(blockNumber), true));
        requestBody.put("id", 1);

        // 发送HTTP请求获取区块信息
        RequestBody body = RequestBody.create(requestBody.toJSONString(), MediaType.parse("application/json"));
        Request request = new Request.Builder().url(rpcUrl).post(body).build();
        
        try (Response response = httpClient.newCall(request).execute()) {
            if (response.isSuccessful() && response.body() != null) {
                String responseBody = response.body().string();
                JSONObject result = JSON.parseObject(responseBody);
                
                if (result.containsKey("result")) {
                    JSONObject blockInfo = result.getJSONObject("result");
                    if (blockInfo != null) {
                        log.debug("获取BASE区块信息成功: blockNumber={}", blockNumber);
                        return blockInfo;
                    }
                }
            }
        }
    } catch (Exception e) {
        log.error("获取BASE区块信息异常: blockNumber={}", blockNumber, e);
    }
    return null;
}
```

##### TRON区块链服务实现
```java
@Override
public JSONObject getBlockInfo(Long blockNumber) {
    try {
        String rpcUrl = config.isTestnet() ? config.getTron().getTestnetRpcUrl() : config.getTron().getRpcUrl();
        
        // 构建RPC请求
        JSONObject requestBody = new JSONObject();
        requestBody.put("jsonrpc", "2.0");
        requestBody.put("method", "wallet/getblockbynum");
        requestBody.put("params", Arrays.asList(blockNumber));
        requestBody.put("id", 1);

        // 发送HTTP请求获取区块信息
        RequestBody body = RequestBody.create(requestBody.toJSONString(), MediaType.parse("application/json"));
        Request request = new Request.Builder().url(rpcUrl).post(body).build();
        
        try (Response response = httpClient.newCall(request).execute()) {
            if (response.isSuccessful() && response.body() != null) {
                String responseBody = response.body().string();
                JSONObject result = JSON.parseObject(responseBody);
                
                if (result.containsKey("result")) {
                    JSONObject blockInfo = result.getJSONObject("result");
                    if (blockInfo != null) {
                        log.debug("获取TRON区块信息成功: blockNumber={}", blockNumber);
                        return blockInfo;
                    }
                }
            }
        }
    } catch (Exception e) {
        log.error("获取TRON区块信息异常: blockNumber={}", blockNumber, e);
    }
    return null;
}
```

##### SOLANA区块链服务实现
```java
@Override
public JSONObject getBlockInfo(Long blockNumber) {
    try {
        String rpcUrl = config.isTestnet() ? config.getSolana().getTestnetRpcUrl() : config.getSolana().getRpcUrl();
        
        // 构建RPC请求
        JSONObject requestBody = new JSONObject();
        requestBody.put("jsonrpc", "2.0");
        requestBody.put("method", "getBlock");
        
        JSONObject config = new JSONObject();
        config.put("encoding", "json");
        config.put("transactionDetails", "full");
        
        requestBody.put("params", Arrays.asList(blockNumber, config));
        requestBody.put("id", 1);

        // 发送HTTP请求获取区块信息
        RequestBody body = RequestBody.create(requestBody.toJSONString(), MediaType.parse("application/json"));
        Request request = new Request.Builder().url(rpcUrl).post(body).build();
        
        try (Response response = httpClient.newCall(request).execute()) {
            if (response.isSuccessful() && response.body() != null) {
                String responseBody = response.body().string();
                JSONObject result = JSON.parseObject(responseBody);
                
                if (result.containsKey("result")) {
                    JSONObject blockInfo = result.getJSONObject("result");
                    if (blockInfo != null) {
                        log.debug("获取SOLANA区块信息成功: blockNumber={}", blockNumber);
                        return blockInfo;
                    }
                }
            }
        }
    } catch (Exception e) {
        log.error("获取SOLANA区块信息异常: blockNumber={}", blockNumber, e);
    }
    return null;
}
```

## 配置说明

### 定时任务配置

在 `application.yml` 中可以配置以下参数：

```yaml
crypto:
  payment:
    enabled: true
    transaction-check-interval: 30      # 交易监听间隔（秒）
    confirmation-check-interval: 60     # 交易确认检查间隔（秒）
    block-monitor-interval: 30         # 区块监听间隔（秒）
    transaction-timeout-minutes: 30    # 交易超时时间（分钟）
    max-retry-count: 3                 # 最大重试次数
```

### 区块日志表结构

#### crypto_tron_block - TRON区块表
```sql
CREATE TABLE crypto_tron_block (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    block_hash VARCHAR(100) NOT NULL COMMENT '区块哈希',
    block_number BIGINT NOT NULL COMMENT '区块高度',
    parent_hash VARCHAR(100) COMMENT '父区块哈希',
    timestamp BIGINT COMMENT '区块时间戳',
    transactions_count BIGINT COMMENT '交易数量',
    witness_address VARCHAR(100) COMMENT '见证人地址',
    witness_signature VARCHAR(500) COMMENT '见证人签名',
    size BIGINT COMMENT '区块大小(字节)',
    gas_used BIGINT COMMENT 'Gas使用量',
    gas_limit BIGINT COMMENT 'Gas限制',
    difficulty VARCHAR(50) COMMENT '难度值',
    nonce VARCHAR(50) COMMENT '随机数',
    merkle_root VARCHAR(100) COMMENT '默克尔根',
    state_root VARCHAR(100) COMMENT '状态根',
    receipts_root VARCHAR(100) COMMENT '收据根',
    extra_data TEXT COMMENT '额外数据',
    raw_data TEXT COMMENT '原始区块数据(JSON)',
    full_content TEXT COMMENT '完整区块内容(JSON)',
    is_confirmed BOOLEAN DEFAULT TRUE COMMENT '是否已确认',
    confirmations BIGINT DEFAULT 1 COMMENT '确认数',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### crypto_base_block - BASE区块表
```sql
CREATE TABLE crypto_base_block (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    block_hash VARCHAR(100) NOT NULL COMMENT '区块哈希',
    block_number BIGINT NOT NULL COMMENT '区块高度',
    parent_hash VARCHAR(100) COMMENT '父区块哈希',
    timestamp BIGINT COMMENT '区块时间戳',
    transactions_count BIGINT COMMENT '交易数量',
    miner VARCHAR(100) COMMENT '矿工地址',
    difficulty VARCHAR(50) COMMENT '难度值',
    total_difficulty VARCHAR(50) COMMENT '总难度',
    size BIGINT COMMENT '区块大小(字节)',
    gas_used BIGINT COMMENT 'Gas使用量',
    gas_limit BIGINT COMMENT 'Gas限制',
    base_fee_per_gas DECIMAL(20,8) COMMENT '基础Gas费用',
    extra_data TEXT COMMENT '额外数据',
    logs_bloom TEXT COMMENT '日志布隆过滤器',
    receipts_root VARCHAR(100) COMMENT '收据根',
    state_root VARCHAR(100) COMMENT '状态根',
    transactions_root VARCHAR(100) COMMENT '交易根',
    uncles TEXT COMMENT '叔块列表',
    raw_data TEXT COMMENT '原始区块数据(JSON)',
    full_content TEXT COMMENT '完整区块内容(JSON)',
    is_confirmed BOOLEAN DEFAULT TRUE COMMENT '是否已确认',
    confirmations BIGINT DEFAULT 1 COMMENT '确认数',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### crypto_solana_block - SOLANA区块表
```sql
CREATE TABLE crypto_solana_block (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    block_hash VARCHAR(100) NOT NULL COMMENT '区块哈希',
    block_number BIGINT NOT NULL COMMENT '区块高度',
    parent_slot BIGINT COMMENT '父槽位',
    timestamp BIGINT COMMENT '区块时间戳',
    transactions_count BIGINT COMMENT '交易数量',
    leader VARCHAR(100) COMMENT '领导者地址',
    rewards TEXT COMMENT '奖励信息(JSON)',
    block_time BIGINT COMMENT '区块时间',
    block_height BIGINT COMMENT '区块高度',
    previous_blockhash VARCHAR(100) COMMENT '前一个区块哈希',
    blockhash VARCHAR(100) COMMENT '当前区块哈希',
    parent_blockhash VARCHAR(100) COMMENT '父区块哈希',
    executed_transaction_count BIGINT COMMENT '已执行交易数量',
    transaction_count BIGINT COMMENT '总交易数量',
    validators TEXT COMMENT '验证者列表(JSON)',
    meta TEXT COMMENT '元数据(JSON)',
    raw_data TEXT COMMENT '原始区块数据(JSON)',
    full_content TEXT COMMENT '完整区块内容(JSON)',
    is_confirmed BOOLEAN DEFAULT TRUE COMMENT '是否已确认',
    confirmations BIGINT DEFAULT 1 COMMENT '确认数',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

## 使用示例

### 1. 区块监听

```java
// 自动监听（定时任务）
@Scheduled(fixedDelayString = "${crypto.payment.block-monitor-interval:30}000")
public void monitorAllBlocks() {
    // 监听TRON区块
    if (config.getTron().isEnabled()) {
        monitorTronBlocks();
    }
    
    // 监听BASE区块
    if (config.getBase().isEnabled()) {
        monitorBaseBlocks();
    }
    
    // 监听SOLANA区块
    if (config.getSolana().isEnabled()) {
        monitorSolanaBlocks();
    }
}
```

### 2. 从区块数据解析交易

```java
// 从区块日志表获取新区块数据
List<Object> newBlocks = getNewBlocksFromDatabase(tokenConfig.getBlockchain(), lastBlockHeight, currentBlockHeight);

// 处理新区块中的交易
for (Object block : newBlocks) {
    List<TransactionInfo> transactions = extractTransactionsFromBlock(block, tokenConfig);
    
    for (TransactionInfo txInfo : transactions) {
        if (isValidIncomingTransaction(txInfo, wallets, tokenConfig)) {
            processIncomingTransaction(txInfo, tokenConfig);
        }
    }
}
```

### 3. API接口使用

```bash
# 手动触发区块监听
curl -X POST http://localhost:8080/crypto/block/monitor

# 获取TRON区块列表
curl "http://localhost:8080/crypto/block/tron?fromBlock=1000&toBlock=1010"

# 获取BASE区块列表
curl "http://localhost:8080/crypto/block/base?fromBlock=1000&toBlock=1010"

# 获取SOLANA区块列表
curl "http://localhost:8080/crypto/block/solana?fromBlock=1000&toBlock=1010"

# 获取最新区块高度
curl http://localhost:8080/crypto/block/latest/TRON
curl http://localhost:8080/crypto/block/latest/BASE
curl http://localhost:8080/crypto/block/latest/SOLANA
```

## 优势

1. **配置化管理**: 所有代币相关配置统一管理，便于维护和扩展
2. **减少 RPC 依赖**: 通过缓存和配置化减少对外部节点的频繁调用
3. **区块数据持久化**: 完整的区块数据保存到数据库，便于查询和分析
4. **轮询机制**: 自动检查交易确认状态，提高充值成功率
5. **错误处理**: 完善的错误处理和重试机制
6. **API 接口**: 提供完整的 REST API 接口，便于前端集成
7. **可扩展性**: 支持多种区块链和代币类型
8. **数据完整性**: 避免重复拉取区块数据，只保存有效的区块
9. **代码复用**: 通过统一的`getBlockInfo`方法，减少重复代码，提高维护性
10. **接口标准化**: 在`BlockchainService`接口中定义统一的区块信息获取方法

## 注意事项

1. 确保 `CryptoTokenConfig` 表中的配置信息准确无误
2. 根据实际需求调整定时任务的执行频率
3. 监控区块监听状态，及时处理异常情况
4. 定期清理过期的区块数据，避免数据库过大
5. 确保区块链服务接口的实现正确，能够正确获取区块信息
6. 注意区块数据的存储空间，根据实际情况调整保留策略
7. **十六进制字段处理**: 区块链返回的某些字段（如size、gasUsed、gasLimit、timestamp、baseFeePerGas等）可能是十六进制格式（如"0x6891c61e"），系统会自动安全地解析这些字段，避免NumberFormatException异常 