package tv.shorthub.crypto.service.blockchain;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tv.shorthub.crypto.config.CryptoPaymentConfiguration;
import tv.shorthub.crypto.dto.TransactionInfo;
import tv.shorthub.crypto.enums.BlockchainType;
import tv.shorthub.system.domain.CryptoTokenConfig;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * Base区块链服务实现
 */
@Slf4j
@Service
public class BaseBlockchainService implements BlockchainService {

    @Autowired
    private CryptoPaymentConfiguration config;

    private final OkHttpClient httpClient;

    public BaseBlockchainService() {
        this.httpClient = new OkHttpClient.Builder()
                .connectTimeout(30, TimeUnit.SECONDS)
                .readTimeout(60, TimeUnit.SECONDS)
                .writeTimeout(60, TimeUnit.SECONDS)
                .build();
    }

    @Override
    public BlockchainType getSupportedBlockchain() {
        return BlockchainType.BASE;
    }

    @Override
    public String generateWalletAddress() {
        return generateWalletAddress(null);
    }

    @Override
    public String generateWalletAddress(String privateKey) {
        try {
            org.web3j.crypto.ECKeyPair keyPair;
            
            if (privateKey != null && !privateKey.isEmpty()) {
                log.debug("处理Base私钥: length={}, starts with 0x={}", privateKey.length(), privateKey.startsWith("0x"));
                
                // 处理十六进制私钥（BIP44生成的格式）
                String cleanPrivateKey = privateKey.startsWith("0x") ? privateKey.substring(2) : privateKey;
                try {
                    // 验证是否为有效的十六进制字符串
                    if (!cleanPrivateKey.matches("^[a-fA-F0-9]{64}$")) {
                        throw new IllegalArgumentException("Invalid hex private key format");
                    }
                    
                    BigInteger privateKeyBigInt = new BigInteger(cleanPrivateKey, 16);
                    keyPair = org.web3j.crypto.ECKeyPair.create(privateKeyBigInt);
                    log.debug("成功从十六进制私钥创建密钥对");
                } catch (Exception e) {
                    log.error("处理私钥失败: {}", e.getMessage());
                    throw new RuntimeException("无效的私钥格式", e);
                }
            } else {
                // 生成新的私钥（32字节）
                byte[] privateKeyBytes = new byte[32];
                java.security.SecureRandom secureRandom = new java.security.SecureRandom();
                secureRandom.nextBytes(privateKeyBytes);
                keyPair = org.web3j.crypto.ECKeyPair.create(privateKeyBytes);
                log.debug("生成新的Base密钥对");
            }
            
            // 使用Web3j标准方法生成以太坊兼容地址（带校验和）
            String address = org.web3j.crypto.Keys.getAddress(keyPair);
            // 确保地址格式正确（包含0x前缀和正确的校验和）
            if (!address.startsWith("0x")) {
                address = "0x" + address;
            }
            // 应用EIP-55校验和格式
            address = org.web3j.crypto.Keys.toChecksumAddress(address);
            
            log.info("生成Base钱包地址成功: {}", address);
            return address;
            
        } catch (Exception e) {
            log.error("生成Base钱包地址失败", e);
            throw new RuntimeException("生成Base钱包地址失败", e);
        }
    }

    @Override
    public boolean isValidAddress(String address) {
        if (address == null || !address.startsWith("0x") || address.length() != 42) {
            return false;
        }
        return address.matches("^0x[a-fA-F0-9]{40}$");
    }

    @Override
    public BigDecimal getBalance(String address, CryptoTokenConfig tokenConfig) {
        try {
            String rpcUrl = config.isTestnet() ? config.getBase().getTestnetRpcUrl() : config.getBase().getRpcUrl();
            
            // ERC-20代币余额查询
            String data = "0x70a08231" + "000000000000000000000000" + address.substring(2);
            
            JSONObject requestBody = new JSONObject();
            requestBody.put("jsonrpc", "2.0");
            requestBody.put("method", "eth_call");
            requestBody.put("params", Arrays.asList(
                    new JSONObject()
                            .fluentPut("to", tokenConfig.getContractAddress())
                            .fluentPut("data", data),
                    "latest"
            ));
            requestBody.put("id", 1);

            RequestBody body = RequestBody.create(
                    requestBody.toJSONString(),
                    MediaType.parse("application/json")
            );

            Request request = new Request.Builder()
                    .url(rpcUrl)
                    .post(body)
                    .build();

            try (Response response = httpClient.newCall(request).execute()) {
                if (response.isSuccessful() && response.body() != null) {
                    String responseBody = response.body().string();
                    JSONObject result = JSON.parseObject(responseBody);
                    
                    if (result.containsKey("result")) {
                        String balanceHex = result.getString("result");
                        if (balanceHex.startsWith("0x")) {
                            balanceHex = balanceHex.substring(2);
                        }
                        BigInteger balance = new BigInteger(balanceHex, 16);
                        return new BigDecimal(balance).divide(
                                BigDecimal.valueOf(Math.pow(10, tokenConfig.getDecimals()))
                        );
                    }
                }
            }
        } catch (Exception e) {
            log.error("获取Base地址余额失败: address={}, contractAddress={}", address, tokenConfig.getContractAddress(), e);
        }
        return BigDecimal.ZERO;
    }

    /**
     * 获取Base主币（ETH）余额
     * @param address 钱包地址
     * @return 主币余额（以ETH为单位）
     */
    @Override
    public BigDecimal getNativeBalance(String address) {
        try {
            if (!isValidAddress(address)) {
                log.error("无效的Base地址: {}", address);
                return BigDecimal.ZERO;
            }

            String rpcUrl = config.isTestnet() ? config.getBase().getTestnetRpcUrl() : config.getBase().getRpcUrl();
            
            // 查询主币余额
            JSONObject requestBody = new JSONObject();
            requestBody.put("jsonrpc", "2.0");
            requestBody.put("method", "eth_getBalance");
            requestBody.put("params", Arrays.asList(address, "latest"));
            requestBody.put("id", 1);

            RequestBody body = RequestBody.create(
                    requestBody.toJSONString(),
                    MediaType.parse("application/json")
            );

            Request request = new Request.Builder()
                    .url(rpcUrl)
                    .post(body)
                    .build();

            try (Response response = httpClient.newCall(request).execute()) {
                if (response.isSuccessful() && response.body() != null) {
                    String responseBody = response.body().string();
                    JSONObject result = JSON.parseObject(responseBody);
                    
                    if (result.containsKey("result")) {
                        String balanceHex = result.getString("result");
                        if (balanceHex.startsWith("0x")) {
                            balanceHex = balanceHex.substring(2);
                        }
                        BigInteger balance = new BigInteger(balanceHex, 16);
                        // ETH有18位小数
                        return new BigDecimal(balance).divide(BigDecimal.valueOf(Math.pow(10, 18)));
                    } else if (result.containsKey("error")) {
                        JSONObject error = result.getJSONObject("error");
                        log.error("获取Base主币余额失败: code={}, message={}", 
                                error.getInteger("code"), error.getString("message"));
                    }
                } else {
                    log.error("获取Base主币余额HTTP请求失败: code={}", response.code());
                }
            }
        } catch (Exception e) {
            log.error("获取Base主币余额异常: address={}", address, e);
        }
        return BigDecimal.ZERO;
    }

    @Override
    public TransactionInfo getTransactionInfo(String txHash, CryptoTokenConfig tokenConfig) {
        try {
            String rpcUrl = config.isTestnet() ? config.getBase().getTestnetRpcUrl() : config.getBase().getRpcUrl();
            
            // 获取交易收据
            JSONObject requestBody = new JSONObject();
            requestBody.put("jsonrpc", "2.0");
            requestBody.put("method", "eth_getTransactionReceipt");
            requestBody.put("params", Collections.singletonList(txHash));
            requestBody.put("id", 1);

            RequestBody body = RequestBody.create(
                    requestBody.toJSONString(),
                    MediaType.parse("application/json")
            );

            Request request = new Request.Builder()
                    .url(rpcUrl)
                    .post(body)
                    .build();

            try (Response response = httpClient.newCall(request).execute()) {
                if (response.isSuccessful() && response.body() != null) {
                    String responseBody = response.body().string();
                    JSONObject result = JSON.parseObject(responseBody);
                    
                    if (result.containsKey("result")) {
                        JSONObject receipt = result.getJSONObject("result");
                        return parseTransaction(receipt, tokenConfig);
                    }
                }
            }
        } catch (Exception e) {
            log.error("获取Base交易信息失败: txHash={}", txHash, e);
        }
        return null;
    }

    @Override
    public List<TransactionInfo> getTransactionHistory(String address, CryptoTokenConfig tokenConfig, int limit, Long fromBlock) {
        List<TransactionInfo> transactions = new ArrayList<>();
        try {
            String rpcUrl = config.isTestnet() ? config.getBase().getTestnetRpcUrl() : config.getBase().getRpcUrl();
            
            // 获取地址的交易历史
            JSONObject requestBody = new JSONObject();
            requestBody.put("jsonrpc", "2.0");
            requestBody.put("method", "eth_getLogs");
            requestBody.put("params", Arrays.asList(
                    new JSONObject()
                            .fluentPut("address", tokenConfig.getContractAddress())
                            .fluentPut("topics", Arrays.asList(
                                    "0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef", // Transfer event
                                    null,
                                    "0x000000000000000000000000" + address.substring(2) // to address
                            ))
                            .fluentPut("fromBlock", fromBlock != null ? "0x" + Long.toHexString(fromBlock) : "0x0")
                            .fluentPut("toBlock", "latest")
            ));
            requestBody.put("id", 1);

            RequestBody body = RequestBody.create(
                    requestBody.toJSONString(),
                    MediaType.parse("application/json")
            );

            Request request = new Request.Builder()
                    .url(rpcUrl)
                    .post(body)
                    .build();

            try (Response response = httpClient.newCall(request).execute()) {
                if (response.isSuccessful() && response.body() != null) {
                    String responseBody = response.body().string();
                    JSONObject result = JSON.parseObject(responseBody);
                    
                    if (result.containsKey("result")) {
                        JSONArray logs = result.getJSONArray("result");
                        for (int i = 0; i < Math.min(logs.size(), limit); i++) {
                            JSONObject log = logs.getJSONObject(i);
                            TransactionInfo txInfo = parseBaseLog(log, tokenConfig);
                            if (txInfo != null) {
                                transactions.add(txInfo);
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("获取Base交易历史失败: address={}", address, e);
        }
        return transactions;
    }

    @Override
    public Long getCurrentBlockHeight() {
        try {
            String rpcUrl = config.isTestnet() ? config.getBase().getTestnetRpcUrl() : config.getBase().getRpcUrl();
            
            JSONObject requestBody = new JSONObject();
            requestBody.put("jsonrpc", "2.0");
            requestBody.put("method", "eth_blockNumber");
            requestBody.put("params", new ArrayList<>());
            requestBody.put("id", 1);

            RequestBody body = RequestBody.create(
                    requestBody.toJSONString(),
                    MediaType.parse("application/json")
            );

            Request request = new Request.Builder()
                    .url(rpcUrl)
                    .post(body)
                    .build();

            try (Response response = httpClient.newCall(request).execute()) {
                if (response.isSuccessful() && response.body() != null) {
                    String responseBody = response.body().string();
                    JSONObject result = JSON.parseObject(responseBody);
                    
                    if (result.containsKey("result")) {
                        String blockNumberHex = result.getString("result");
                        if (blockNumberHex.startsWith("0x")) {
                            blockNumberHex = blockNumberHex.substring(2);
                        }
                        return Long.parseLong(blockNumberHex, 16);
                    }
                }
            }
        } catch (Exception e) {
            log.error("获取Base当前区块高度失败", e);
        }
        return null;
    }

    @Override
    public Integer getTransactionConfirmations(String txHash, CryptoTokenConfig tokenConfig) {
        try {
            TransactionInfo txInfo = getTransactionInfo(txHash, tokenConfig);
            if (txInfo != null && txInfo.getBlockHeight() != null) {
                Long currentHeight = getCurrentBlockHeight();
                if (currentHeight != null) {
                    return (int) (currentHeight - txInfo.getBlockHeight() + 1);
                }
            }
        } catch (Exception e) {
            log.error("获取Base交易确认数失败: txHash={}", txHash, e);
        }
        return 0;
    }

    @Override
    public List<TransactionInfo> monitorNewTransactions(String address, CryptoTokenConfig tokenConfig, Long fromBlock) {
        // 实现新交易监听逻辑
        return getTransactionHistory(address, tokenConfig, 10, fromBlock);
    }

    @Override
    public boolean isTransactionConfirmed(String txHash, int requiredConfirmations, CryptoTokenConfig tokenConfig) {
        Integer confirmations = getTransactionConfirmations(txHash, tokenConfig);
        return confirmations != null && confirmations >= requiredConfirmations;
    }


    @Override
    public JSONObject getBlockInfo(Long blockNumber) {
        try {
            String rpcUrl = config.isTestnet() ? config.getBase().getTestnetRpcUrl() : config.getBase().getRpcUrl();
            
            // 构建RPC请求
            JSONObject requestBody = new JSONObject();
            requestBody.put("jsonrpc", "2.0");
            requestBody.put("method", "eth_getBlockByNumber");
            requestBody.put("params", Arrays.asList("0x" + Long.toHexString(blockNumber), true));
            requestBody.put("id", 1);

            RequestBody body = RequestBody.create(
                    requestBody.toJSONString(),
                    MediaType.parse("application/json")
            );

            Request request = new Request.Builder()
                    .url(rpcUrl)
                    .post(body)
                    .build();

            try (Response response = httpClient.newCall(request).execute()) {
                if (response.isSuccessful() && response.body() != null) {
                    String responseBody = response.body().string();
                    JSONObject result = JSON.parseObject(responseBody);
                    
                    if (result.containsKey("result")) {
                        JSONObject blockInfo = result.getJSONObject("result");
                        if (blockInfo != null) {
                            log.debug("获取BASE区块信息成功: blockNumber={}", blockNumber);
                            return blockInfo;
                        } else {
                            log.warn("BASE区块不存在: blockNumber={}", blockNumber);
                            return null;
                        }
                    } else if (result.containsKey("error")) {
                        JSONObject error = result.getJSONObject("error");
                        log.error("获取BASE区块信息失败: code={}, message={}", 
                                error.getInteger("code"), error.getString("message"));
                    }
                } else {
                    log.error("获取BASE区块信息HTTP请求失败: code={}", response.code());
                }
            }
        } catch (Exception e) {
            log.error("获取BASE区块信息异常: blockNumber={}", blockNumber, e);
        }
        return null;
    }

    @Override
    public TransactionInfo parseTransaction(JSONObject receipt, CryptoTokenConfig tokenConfig) {
        try {
            TransactionInfo txInfo = new TransactionInfo();
            txInfo.setTxHash(receipt.getString("transactionHash"));
            txInfo.setBlockHeight(Long.parseLong(receipt.getString("blockNumber").substring(2), 16));
            txInfo.setBlockchain(BlockchainType.BASE.getCode());
            txInfo.setTokenSymbol(tokenConfig.getTokenSymbol());
            txInfo.setContractAddress(tokenConfig.getContractAddress());
            
            // 解析日志获取转账信息
            JSONArray logs = receipt.getJSONArray("logs");
            for (int i = 0; i < logs.size(); i++) {
                JSONObject log = logs.getJSONObject(i);
                if (tokenConfig.getContractAddress().equalsIgnoreCase(log.getString("address"))) {
                    JSONArray topics = log.getJSONArray("topics");
                    if (topics.size() >= 3) {
                        // Transfer event: 0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef
                        String eventSignature = topics.getString(0);
                        if ("0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef".equals(eventSignature)) {
                            String fromAddress = "0x" + topics.getString(1).substring(26);
                            String toAddress = "0x" + topics.getString(2).substring(26);
                            txInfo.setFromAddress(fromAddress);
                            txInfo.setToAddress(toAddress);
                            
                            // 解析金额
                            String data = log.getString("data");
                            if (data.startsWith("0x")) {
                                data = data.substring(2);
                            }
                            BigInteger value = new BigInteger(data, 16);
                            txInfo.setAmount(new BigDecimal(value).divide(
                                    BigDecimal.valueOf(Math.pow(10, tokenConfig.getDecimals()))
                            ));
                            txInfo.setRawAmount(value.toString());
                            break;
                        }
                    }
                }
            }
            
            // 设置交易状态
            txInfo.setSuccess("0x1".equals(receipt.getString("status")));
            txInfo.setStatus(txInfo.getSuccess() ? "confirmed" : "failed");
            
            return txInfo;
        } catch (Exception e) {
            log.error("解析Base交易信息失败", e);
            return null;
        }
    }



    /**
     * 从交易中获取合约地址
     */
    private String getContractAddressFromTransaction(JSONObject tx) {
        return tx.getString("to");
    }

    /**
     * 检查是否为代币转账交易
     */
    private boolean isTokenTransferTransaction(JSONObject tx, CryptoTokenConfig tokenConfig) {
        try {
            // 检查合约地址是否匹配
            String contractAddress = getContractAddressFromTransaction(tx);
            if (!tokenConfig.getContractAddress().equalsIgnoreCase(contractAddress)) {
                return false;
            }

            // 检查交易是否成功
            return true;
        } catch (Exception e) {
            log.error("检查代币转账交易失败", e);
            return false;
        }
    }

    /**
     * 检查交易是否成功
     */
    private boolean isTransactionSuccessful(JSONObject tx) {
        return "0x1".equalsIgnoreCase(tx.getString("status"));
    }

    @Override
    public List<TransactionInfo> extractTransactions(JSONObject blockContent, CryptoTokenConfig tokenConfig) {
        List<TransactionInfo> transactions = new ArrayList<>();

        try {
            // 解析区块的完整内容
            JSONArray txArray = blockContent.getJSONArray("transactions");

            for (int i = 0; i < txArray.size(); i++) {
                JSONObject tx = txArray.getJSONObject(i);

                // 检查是否为代币转账交易
                if (isTokenTransferTransaction(tx, tokenConfig)) {
                    TransactionInfo txInfo = getTransactionInfo(tx.getString("hash"), tokenConfig);
                    if (txInfo != null) {
                        transactions.add(txInfo);
                    }
                }
            }
        } catch (Exception e) {
            log.error("从BASE区块提取交易失败", e);
        }

        return transactions;
    }

    public static void main(String[] args) {
        String data = "0x000000000000000000000000000000000000000000000001a055690d9db80000";
        if (data.startsWith("0x")) {
            data = data.substring(2);
        }
        BigInteger value = new BigInteger(data, 16);
        BigDecimal divide = new BigDecimal(value).divide(
                BigDecimal.valueOf(Math.pow(10,18))
        );
        log.info("value={}, divide={}", value, divide);
    }

    private TransactionInfo parseBaseLog(JSONObject txLog, CryptoTokenConfig tokenConfig) {
        try {
            TransactionInfo txInfo = new TransactionInfo();
            txInfo.setTxHash(txLog.getString("transactionHash"));
            txInfo.setBlockHeight(Long.parseLong(txLog.getString("blockNumber").substring(2), 16));
            txInfo.setBlockchain(BlockchainType.BASE.getCode());
            txInfo.setTokenSymbol(tokenConfig.getTokenSymbol());
            txInfo.setContractAddress(tokenConfig.getContractAddress());
            
            JSONArray topics = txLog.getJSONArray("topics");
            if (topics.size() >= 3) {
                String fromAddress = "0x" + topics.getString(1).substring(26);
                String toAddress = "0x" + topics.getString(2).substring(26);
                txInfo.setFromAddress(fromAddress);
                txInfo.setToAddress(toAddress);
                
                // 解析金额
                String data = txLog.getString("data");
                if (data.startsWith("0x")) {
                    data = data.substring(2);
                }
                BigInteger value = new BigInteger(data, 16);
                txInfo.setAmount(new BigDecimal(value).divide(
                        BigDecimal.valueOf(Math.pow(10, tokenConfig.getDecimals()))
                ));
                txInfo.setRawAmount(value.toString());
            }
            
            txInfo.setSuccess(true);
            txInfo.setStatus("confirmed");
            
            return txInfo;
        } catch (Exception e) {
            log.error("解析Base日志失败", e);
            return null;
        }
    }


} 