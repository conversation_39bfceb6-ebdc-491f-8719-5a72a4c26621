package tv.shorthub.crypto.service.impl;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import tv.shorthub.crypto.config.CryptoPaymentConfiguration;
import tv.shorthub.crypto.enums.BlockchainType;
import tv.shorthub.crypto.service.blockchain.BlockchainService;
import tv.shorthub.system.domain.CryptoBaseBlock;
import tv.shorthub.system.domain.CryptoSolanaBlock;
import tv.shorthub.system.domain.CryptoTronBlock;
import tv.shorthub.system.mapper.CryptoBaseBlockMapper;
import tv.shorthub.system.mapper.CryptoSolanaBlockMapper;
import tv.shorthub.system.mapper.CryptoTronBlockMapper;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 虚拟货币区块监听服务
 * 监听所有区块并将原始报文保存到对应的区块日志表
 */
@Slf4j
@Service
public class CryptoBlockMonitorService {

    @Autowired
    private CryptoPaymentConfiguration config;

    @Autowired
    private CryptoTronBlockMapper cryptoTronBlockMapper;

    @Autowired
    private CryptoBaseBlockMapper cryptoBaseBlockMapper;

    @Autowired
    private CryptoSolanaBlockMapper cryptoSolanaBlockMapper;

    @Autowired
    private List<BlockchainService> blockchainServices;

    // 区块链服务映射
    private Map<BlockchainType, BlockchainService> blockchainServiceMap;

    @Autowired
    public void initBlockchainServices(List<BlockchainService> services) {
        this.blockchainServiceMap = services.stream()
                .collect(Collectors.toMap(
                        BlockchainService::getSupportedBlockchain,
                        service -> service
                ));
    }

    /**
     * 监听所有区块
     * 每30秒执行一次
     */
    @Scheduled(fixedDelayString = "${crypto.payment.block-monitor-interval:30}000")
    public void monitorAllBlocks() {
        if (!config.isEnabled()) {
            return;
        }

        try {
            log.debug("开始监听所有区块");
            
//            // 监听TRON区块
//            if (config.getTron().isEnabled()) {
//                monitorTronBlocks();
//            }
            
            // 监听BASE区块
            if (config.getBase().isEnabled()) {
                monitorBaseBlocks();
            }
            
//            // 监听SOLANA区块
//            if (config.getSolana().isEnabled()) {
//                monitorSolanaBlocks();
//            }
            
            log.debug("区块监听完成");
        } catch (Exception e) {
            log.error("监听区块失败", e);
        }
    }

    /**
     * 监听TRON区块
     */
    private void monitorTronBlocks() {
        try {
            BlockchainService blockchainService = blockchainServiceMap.get(BlockchainType.TRON);
            if (blockchainService == null) {
                log.warn("TRON区块链服务未找到");
                return;
            }

            // 获取最后监听的区块高度
            Long lastBlockNumber = getLastTronBlockNumber();
            Long currentBlockNumber = blockchainService.getCurrentBlockHeight();
            
            if (currentBlockNumber == null) {
                log.warn("无法获取TRON当前区块高度");
                return;
            }

            // 从上次监听的区块开始，逐个处理新区块
            for (Long blockNumber = lastBlockNumber + 1; blockNumber <= currentBlockNumber; blockNumber++) {
                try {
                    processTronBlock(blockchainService, blockNumber);
                } catch (Exception e) {
                    log.error("处理TRON区块失败: blockNumber={}", blockNumber, e);
                }
            }
        } catch (Exception e) {
            log.error("监听TRON区块失败", e);
        }
    }

    /**
     * 监听BASE区块
     */
    private void monitorBaseBlocks() {
        try {
            BlockchainService blockchainService = blockchainServiceMap.get(BlockchainType.BASE);
            if (blockchainService == null) {
                log.warn("BASE区块链服务未找到");
                return;
            }

            // 获取最后监听的区块高度
            Long lastBlockNumber = getLastBaseBlockNumber();
            Long currentBlockNumber = blockchainService.getCurrentBlockHeight();
            
            if (currentBlockNumber == null) {
                log.warn("无法获取BASE当前区块高度");
                return;
            }
            if (null == lastBlockNumber) {
                lastBlockNumber = currentBlockNumber - 1;
            }

            // 从上次监听的区块开始，逐个处理新区块
            for (Long blockNumber = lastBlockNumber + 1; blockNumber <= currentBlockNumber; blockNumber++) {
                try {
                    processBaseBlock(blockchainService, blockNumber);
                } catch (Exception e) {
                    log.error("处理BASE区块失败: blockNumber={}", blockNumber, e);
                }
            }
        } catch (Exception e) {
            log.error("监听BASE区块失败", e);
        }
    }

    /**
     * 监听SOLANA区块
     */
    private void monitorSolanaBlocks() {
        try {
            BlockchainService blockchainService = blockchainServiceMap.get(BlockchainType.SOLANA);
            if (blockchainService == null) {
                log.warn("SOLANA区块链服务未找到");
                return;
            }

            // 获取最后监听的区块高度
            Long lastBlockNumber = getLastSolanaBlockNumber();
            Long currentBlockNumber = blockchainService.getCurrentBlockHeight();
            
            if (currentBlockNumber == null) {
                log.warn("无法获取SOLANA当前区块高度");
                return;
            }

            // 从上次监听的区块开始，逐个处理新区块
            for (Long blockNumber = lastBlockNumber + 1; blockNumber <= currentBlockNumber; blockNumber++) {
                try {
                    processSolanaBlock(blockchainService, blockNumber);
                } catch (Exception e) {
                    log.error("处理SOLANA区块失败: blockNumber={}", blockNumber, e);
                }
            }
        } catch (Exception e) {
            log.error("监听SOLANA区块失败", e);
        }
    }

    /**
     * 处理TRON区块
     */
    private void processTronBlock(BlockchainService blockchainService, Long blockNumber) {
        try {
            // 检查区块是否已存在
            if (isTronBlockExists(blockNumber)) {
                return;
            }

            // 获取区块信息
            com.alibaba.fastjson2.JSONObject blockInfo = getBlockInfo(blockchainService, blockNumber);
            if (blockInfo == null) {
                log.warn("无法获取TRON区块信息: blockNumber={}", blockNumber);
                return;
            }

            // 创建区块记录
            CryptoTronBlock block = new CryptoTronBlock();
            block.setBlockNumber(blockNumber);
            block.setBlockHash(blockInfo.getString("blockID"));
            block.setParentHash(blockInfo.getJSONObject("block_header").getString("parentHash"));
            
            // 安全解析timestamp（可能是十六进制）
            String timestampHex = blockInfo.getJSONObject("block_header").getString("timestamp");
            if (timestampHex != null && timestampHex.startsWith("0x")) {
                block.setTimestamp(parseHexToLong(timestampHex));
            } else {
                block.setTimestamp(blockInfo.getJSONObject("block_header").getLong("timestamp"));
            }
            
            block.setTransactionsCount((long) blockInfo.getJSONArray("transactions").size());
            block.setWitnessAddress(blockInfo.getJSONObject("block_header").getString("witness_address"));
            block.setWitnessSignature(blockInfo.getJSONObject("block_header").getString("witness_signature"));
            
            // 安全解析十六进制字段
            String sizeHex = blockInfo.getString("size");
            block.setSize(parseHexToLong(sizeHex));
            
            String gasUsedHex = blockInfo.getString("gasUsed");
            block.setGasUsed(parseHexToLong(gasUsedHex));
            
            String gasLimitHex = blockInfo.getString("gasLimit");
            block.setGasLimit(parseHexToLong(gasLimitHex));
            
            block.setDifficulty(blockInfo.getString("difficulty"));
            block.setNonce(blockInfo.getString("nonce"));
            block.setMerkleRoot(blockInfo.getString("merkleRoot"));
            block.setStateRoot(blockInfo.getString("stateRoot"));
            block.setReceiptsRoot(blockInfo.getString("receiptsRoot"));
            block.setExtraData(blockInfo.getString("extraData"));
            block.setRawData(blockInfo.getString("rawData"));
            block.setFullContent(blockInfo.toJSONString());
            block.setIsConfirmed(true);
            block.setConfirmations(1L);

            // 保存区块记录
            cryptoTronBlockMapper.insert(block);
            
            log.debug("保存TRON区块成功: blockNumber={}, blockHash={}", blockNumber, block.getBlockHash());
            
        } catch (Exception e) {
            log.error("处理TRON区块失败: blockNumber={}", blockNumber, e);
        }
    }

    /**
     * 处理BASE区块
     */
    private void processBaseBlock(BlockchainService blockchainService, Long blockNumber) {
        try {
            // 检查区块是否已存在
            if (isBaseBlockExists(blockNumber)) {
                return;
            }

            // 获取区块信息
            com.alibaba.fastjson2.JSONObject blockInfo = getBlockInfo(blockchainService, blockNumber);
            if (blockInfo == null) {
                log.warn("无法获取BASE区块信息: blockNumber={}", blockNumber);
                return;
            }

            // 创建区块记录
            CryptoBaseBlock block = new CryptoBaseBlock();
            block.setBlockNumber(blockNumber);
            block.setBlockHash(blockInfo.getString("hash"));
            block.setParentHash(blockInfo.getString("parentHash"));
            
            // 安全解析timestamp（可能是十六进制）
            String timestampHex = blockInfo.getString("timestamp");
            if (timestampHex != null && timestampHex.startsWith("0x")) {
                block.setTimestamp(parseHexToLong(timestampHex));
            } else {
                block.setTimestamp(blockInfo.getLong("timestamp"));
            }
            
            block.setTransactionsCount((long) blockInfo.getJSONArray("transactions").size());
            block.setMiner(blockInfo.getString("miner"));
            block.setDifficulty(blockInfo.getString("difficulty"));
            block.setTotalDifficulty(blockInfo.getString("totalDifficulty"));
            
            // 安全解析十六进制字段
            String sizeHex = blockInfo.getString("size");
            block.setSize(parseHexToLong(sizeHex));
            
            String gasUsedHex = blockInfo.getString("gasUsed");
            block.setGasUsed(parseHexToLong(gasUsedHex));
            
            String gasLimitHex = blockInfo.getString("gasLimit");
            block.setGasLimit(parseHexToLong(gasLimitHex));
            
            // 解析baseFeePerGas（可能是十六进制）
            String baseFeePerGasHex = blockInfo.getString("baseFeePerGas");
            if (baseFeePerGasHex != null && baseFeePerGasHex.startsWith("0x")) {
                block.setBaseFeePerGas(parseHexToBigDecimal(baseFeePerGasHex));
            } else {
                block.setBaseFeePerGas(blockInfo.getBigDecimal("baseFeePerGas"));
            }
            
            block.setExtraData(blockInfo.getString("extraData"));
            block.setLogsBloom(blockInfo.getString("logsBloom"));
            block.setReceiptsRoot(blockInfo.getString("receiptsRoot"));
            block.setStateRoot(blockInfo.getString("stateRoot"));
            block.setTransactionsRoot(blockInfo.getString("transactionsRoot"));
            block.setUncles(blockInfo.getString("uncles"));
            block.setRawData(blockInfo.getString("rawData"));
            block.setFullContent(blockInfo.toJSONString());
            block.setIsConfirmed(true);
            block.setConfirmations(1L);

            // 保存区块记录
            cryptoBaseBlockMapper.insert(block);
            
            log.debug("保存BASE区块成功: blockNumber={}, blockHash={}", blockNumber, block.getBlockHash());
            
        } catch (Exception e) {
            log.error("处理BASE区块失败: blockNumber={}", blockNumber, e);
        }
    }

    /**
     * 处理SOLANA区块
     */
    private void processSolanaBlock(BlockchainService blockchainService, Long blockNumber) {
        try {
            // 检查区块是否已存在
            if (isSolanaBlockExists(blockNumber)) {
                return;
            }

            // 获取区块信息
            com.alibaba.fastjson2.JSONObject blockInfo = getBlockInfo(blockchainService, blockNumber);
            if (blockInfo == null) {
                log.warn("无法获取SOLANA区块信息: blockNumber={}", blockNumber);
                return;
            }

            // 创建区块记录
            CryptoSolanaBlock block = new CryptoSolanaBlock();
            block.setBlockNumber(blockNumber);
            block.setBlockHash(blockInfo.getString("blockhash"));
            block.setParentSlot(blockInfo.getLong("parentSlot"));
            block.setTimestamp(blockInfo.getLong("blockTime"));
            block.setTransactionsCount(blockInfo.getLong("transactionCount"));
            block.setLeader(blockInfo.getString("leader"));
            block.setRewards(blockInfo.getString("rewards"));
            block.setBlockTime(blockInfo.getLong("blockTime"));
            block.setBlockHeight(blockInfo.getLong("blockHeight"));
            block.setPreviousBlockhash(blockInfo.getString("previousBlockhash"));
            block.setBlockHash(blockInfo.getString("blockhash"));
            block.setParentBlockhash(blockInfo.getString("parentBlockhash"));
            block.setExecutedTransactionCount(blockInfo.getLong("executedTransactionCount"));
            block.setTransactionCount(blockInfo.getLong("transactionCount"));
            block.setValidators(blockInfo.getString("validators"));
            block.setMeta(blockInfo.getString("meta"));
            block.setRawData(blockInfo.getString("rawData"));
            block.setFullContent(blockInfo.toJSONString());
            block.setIsConfirmed(true);
            block.setConfirmations(1L);

            // 保存区块记录
            cryptoSolanaBlockMapper.insert(block);
            
            log.debug("保存SOLANA区块成功: blockNumber={}, blockHash={}", blockNumber, block.getBlockHash());
            
        } catch (Exception e) {
            log.error("处理SOLANA区块失败: blockNumber={}", blockNumber, e);
        }
    }

    /**
     * 获取最后监听的TRON区块高度
     */
    public Long getLastTronBlockNumber() {
        try {
            CryptoTronBlock lastBlock = cryptoTronBlockMapper.selectOne(
                new LambdaQueryWrapper<CryptoTronBlock>()
                    .orderByDesc(CryptoTronBlock::getBlockNumber)
                    .last("LIMIT 1")
            );
            return lastBlock != null ? lastBlock.getBlockNumber() : 0L;
        } catch (Exception e) {
            log.error("获取最后TRON区块高度失败", e);
            return 0L;
        }
    }

    /**
     * 获取最后监听的BASE区块高度
     */
    public Long getLastBaseBlockNumber() {
        try {
            CryptoBaseBlock lastBlock = cryptoBaseBlockMapper.selectOne(
                new LambdaQueryWrapper<CryptoBaseBlock>()
                    .orderByDesc(CryptoBaseBlock::getBlockNumber)
                    .last("LIMIT 1")
            );
            return lastBlock != null ? lastBlock.getBlockNumber() : null;
        } catch (Exception e) {
            log.error("获取最后BASE区块高度失败", e);
            return null;
        }
    }

    /**
     * 获取最后监听的SOLANA区块高度
     */
    public Long getLastSolanaBlockNumber() {
        try {
            CryptoSolanaBlock lastBlock = cryptoSolanaBlockMapper.selectOne(
                new LambdaQueryWrapper<CryptoSolanaBlock>()
                    .orderByDesc(CryptoSolanaBlock::getBlockNumber)
                    .last("LIMIT 1")
            );
            return lastBlock != null ? lastBlock.getBlockNumber() : 0L;
        } catch (Exception e) {
            log.error("获取最后SOLANA区块高度失败", e);
            return 0L;
        }
    }

    /**
     * 检查TRON区块是否已存在
     */
    private boolean isTronBlockExists(Long blockNumber) {
        try {
            CryptoTronBlock existingBlock = cryptoTronBlockMapper.selectOne(
                new LambdaQueryWrapper<CryptoTronBlock>()
                    .eq(CryptoTronBlock::getBlockNumber, blockNumber)
            );
            return existingBlock != null;
        } catch (Exception e) {
            log.error("检查TRON区块是否存在失败: blockNumber={}", blockNumber, e);
            return false;
        }
    }

    /**
     * 检查BASE区块是否已存在
     */
    private boolean isBaseBlockExists(Long blockNumber) {
        try {
            CryptoBaseBlock existingBlock = cryptoBaseBlockMapper.selectOne(
                new LambdaQueryWrapper<CryptoBaseBlock>()
                    .eq(CryptoBaseBlock::getBlockNumber, blockNumber)
            );
            return existingBlock != null;
        } catch (Exception e) {
            log.error("检查BASE区块是否存在失败: blockNumber={}", blockNumber, e);
            return false;
        }
    }

    /**
     * 检查SOLANA区块是否已存在
     */
    private boolean isSolanaBlockExists(Long blockNumber) {
        try {
            CryptoSolanaBlock existingBlock = cryptoSolanaBlockMapper.selectOne(
                new LambdaQueryWrapper<CryptoSolanaBlock>()
                    .eq(CryptoSolanaBlock::getBlockNumber, blockNumber)
            );
            return existingBlock != null;
        } catch (Exception e) {
            log.error("检查SOLANA区块是否存在失败: blockNumber={}", blockNumber, e);
            return false;
        }
    }

    /**
     * 获取指定范围的TRON区块
     */
    public List<CryptoTronBlock> getTronBlocks(Long fromBlock, Long toBlock) {
        try {
            return cryptoTronBlockMapper.selectList(
                new LambdaQueryWrapper<CryptoTronBlock>()
                    .ge(CryptoTronBlock::getBlockNumber, fromBlock)
                    .le(CryptoTronBlock::getBlockNumber, toBlock)
                    .orderByAsc(CryptoTronBlock::getBlockNumber)
            );
        } catch (Exception e) {
            log.error("获取TRON区块失败: fromBlock={}, toBlock={}", fromBlock, toBlock, e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取指定范围的BASE区块
     */
    public List<CryptoBaseBlock> getBaseBlocks(Long fromBlock, Long toBlock) {
        try {
            return cryptoBaseBlockMapper.selectList(
                new LambdaQueryWrapper<CryptoBaseBlock>()
                    .ge(CryptoBaseBlock::getBlockNumber, fromBlock)
                    .le(CryptoBaseBlock::getBlockNumber, toBlock)
                    .orderByAsc(CryptoBaseBlock::getBlockNumber)
            );
        } catch (Exception e) {
            log.error("获取BASE区块失败: fromBlock={}, toBlock={}", fromBlock, toBlock, e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取指定范围的SOLANA区块
     */
    public List<CryptoSolanaBlock> getSolanaBlocks(Long fromBlock, Long toBlock) {
        try {
            return cryptoSolanaBlockMapper.selectList(
                new LambdaQueryWrapper<CryptoSolanaBlock>()
                    .ge(CryptoSolanaBlock::getBlockNumber, fromBlock)
                    .le(CryptoSolanaBlock::getBlockNumber, toBlock)
                    .orderByAsc(CryptoSolanaBlock::getBlockNumber)
            );
        } catch (Exception e) {
            log.error("获取SOLANA区块失败: fromBlock={}, toBlock={}", fromBlock, toBlock, e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取区块信息
     * 通用方法，根据区块链类型获取对应的区块信息
     */
    private com.alibaba.fastjson2.JSONObject getBlockInfo(BlockchainService blockchainService, Long blockNumber) {
        try {
            if (blockchainService == null) {
                log.warn("区块链服务为空");
                return null;
            }
            
            return blockchainService.getBlockInfo(blockNumber);
        } catch (Exception e) {
            log.error("获取区块信息失败: blockchain={}, blockNumber={}", 
                    blockchainService.getSupportedBlockchain(), blockNumber, e);
            return null;
        }
    }

    /**
     * 安全地解析十六进制字符串为Long值
     * @param hexString 十六进制字符串（可能包含0x前缀）
     * @return Long值，如果解析失败返回null
     */
    private Long parseHexToLong(String hexString) {
        try {
            if (hexString == null || hexString.isEmpty()) {
                return null;
            }
            
            // 移除0x前缀
            String cleanHex = hexString.startsWith("0x") ? hexString.substring(2) : hexString;
            
            // 解析十六进制
            return Long.parseLong(cleanHex, 16);
        } catch (Exception e) {
            log.warn("解析十六进制字符串失败: hexString={}", hexString, e);
            return null;
        }
    }

    /**
     * 安全地解析十六进制字符串为BigDecimal值
     * @param hexString 十六进制字符串（可能包含0x前缀）
     * @return BigDecimal值，如果解析失败返回null
     */
    private BigDecimal parseHexToBigDecimal(String hexString) {
        try {
            if (hexString == null || hexString.isEmpty()) {
                return null;
            }
            
            // 移除0x前缀
            String cleanHex = hexString.startsWith("0x") ? hexString.substring(2) : hexString;
            
            // 解析十六进制
            BigInteger bigInt = new BigInteger(cleanHex, 16);
            return new BigDecimal(bigInt);
        } catch (Exception e) {
            log.warn("解析十六进制字符串为BigDecimal失败: hexString={}", hexString, e);
            return null;
        }
    }
} 