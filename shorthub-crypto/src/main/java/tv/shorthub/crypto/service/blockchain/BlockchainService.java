package tv.shorthub.crypto.service.blockchain;

import com.alibaba.fastjson2.JSONObject;
import tv.shorthub.crypto.dto.TransactionInfo;
import tv.shorthub.crypto.enums.BlockchainType;
import tv.shorthub.system.domain.CryptoTokenConfig;

import java.math.BigDecimal;
import java.util.List;

/**
 * 区块链服务接口
 * 定义统一的区块链操作方法，支持不同区块链的实现
 */
public interface BlockchainService {

    /**
     * 获取支持的区块链类型
     */
    BlockchainType getSupportedBlockchain();

    /**
     * 生成钱包地址
     */
    String generateWalletAddress();

    /**
     * 从私钥生成钱包地址
     */
    String generateWalletAddress(String privateKey);

    /**
     * 验证地址格式是否有效
     * @param address 钱包地址
     * @return 是否有效
     */
    boolean isValidAddress(String address);

    /**
     * 获取地址余额
     * @param address 钱包地址
     * @param tokenConfig 代币类型
     * @return 余额
     */
    BigDecimal getBalance(String address, CryptoTokenConfig tokenConfig);

    /**
     * 获取原生代币余额
     * @param address
     * @return
     */
    BigDecimal getNativeBalance(String address);

    /**
     * 根据交易哈希获取交易信息
     * @param txHash 交易哈希
     * @param tokenConfig 代币类型
     * @return 交易信息
     */
    TransactionInfo getTransactionInfo(String txHash, CryptoTokenConfig tokenConfig);

    /**
     * 获取指定地址的交易列表
     * @param address 钱包地址
     * @param tokenConfig 代币类型
     * @param limit 限制数量
     * @return 交易列表
     */
    List<TransactionInfo> getTransactionHistory(String address, CryptoTokenConfig tokenConfig, int limit, Long fromBlock);

    /**
     * 获取当前区块高度
     * @return 区块高度
     */
    Long getCurrentBlockHeight();

    /**
     * 获取交易确认数
     * @param txHash 交易哈希
     * @return 确认数
     */
    Integer getTransactionConfirmations(String txHash, CryptoTokenConfig tokenConfig);

    /**
     * 监听指定地址的新交易
     * @param address 钱包地址
     * @param tokenConfig 代币类型
     * @param fromBlock 起始区块
     * @return 新交易列表
     */
    List<TransactionInfo> monitorNewTransactions(String address, CryptoTokenConfig tokenConfig, Long fromBlock);

    /**
     * 检查交易是否已确认
     * @param txHash 交易哈希
     * @param requiredConfirmations 需要的确认数
     * @return 是否已确认
     */
    boolean isTransactionConfirmed(String txHash, int requiredConfirmations, CryptoTokenConfig tokenConfig);

    /**
     * 获取区块信息
     * @param blockNumber 区块高度
     * @return 区块信息JSON对象
     */
    com.alibaba.fastjson2.JSONObject getBlockInfo(Long blockNumber);

    TransactionInfo parseTransaction(JSONObject receipt, CryptoTokenConfig tokenConfig);

    List<TransactionInfo> extractTransactions(JSONObject block, CryptoTokenConfig tokenConfig);
}