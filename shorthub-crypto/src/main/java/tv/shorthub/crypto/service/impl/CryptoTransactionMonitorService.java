package tv.shorthub.crypto.service.impl;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import tv.shorthub.common.core.redis.RedisCache;
import tv.shorthub.common.enums.CloudflareQueueEnum;
import tv.shorthub.common.queue.CloudflareQueueService;
import tv.shorthub.crypto.config.CryptoPaymentConfiguration;
import tv.shorthub.crypto.dto.TransactionInfo;
import tv.shorthub.crypto.enums.BlockchainType;
import tv.shorthub.crypto.enums.TransactionStatus;
import tv.shorthub.system.domain.CryptoBaseBlock;
import tv.shorthub.system.domain.CryptoDepositRecord;
import tv.shorthub.system.domain.CryptoMonitorLog;
import tv.shorthub.system.domain.CryptoTokenConfig;
import tv.shorthub.system.domain.CryptoWallet;
import tv.shorthub.system.domain.CryptoSolanaBlock;
import tv.shorthub.system.domain.CryptoTronBlock;
import tv.shorthub.system.mapper.CryptoDepositRecordMapper;
import tv.shorthub.system.mapper.CryptoMonitorLogMapper;
import tv.shorthub.system.mapper.CryptoTokenConfigMapper;
import tv.shorthub.system.mapper.CryptoWalletMapper;
import tv.shorthub.crypto.service.blockchain.BlockchainService;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 虚拟货币交易监听服务
 * 定时任务，用于监听区块链交易、预热钱包池、清理过期数据等
 * 优化版本：基于CryptoTokenConfig配置，减少RPC依赖，实现轮询交易结果
 */
@Slf4j
@Service
@ConditionalOnProperty(prefix = "crypto.payment", name = "enabled", havingValue = "true", matchIfMissing = true)
public class CryptoTransactionMonitorService {

    @Autowired
    private CryptoPaymentConfiguration config;

    @Autowired
    private CryptoPaymentService cryptoPaymentService;

    @Autowired
    private CryptoWalletService walletService;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private CryptoTokenConfigMapper cryptoTokenConfigMapper;

    @Autowired
    private CryptoDepositRecordMapper cryptoDepositRecordMapper;

    @Autowired
    private CryptoMonitorLogMapper cryptoMonitorLogMapper;

    @Autowired
    private CryptoWalletMapper cryptoWalletMapper;

    @Autowired
    private List<BlockchainService> blockchainServices;

    @Autowired
    private CryptoTokenConfigService cryptoTokenConfigService;

    @Autowired
    private CryptoBlockMonitorService cryptoBlockMonitorService;

    // 区块链服务映射
    private Map<BlockchainType, BlockchainService> blockchainServiceMap;

    @Autowired
    CloudflareQueueService cloudflareQueueService;

    @Autowired
    public void initBlockchainServices(List<BlockchainService> services) {
        this.blockchainServiceMap = services.stream()
                .collect(Collectors.toMap(
                        BlockchainService::getSupportedBlockchain,
                        service -> service
                ));
    }

    /**
     * 监听交易 - 基于CryptoTokenConfig配置
     * 每30秒执行一次
     */
    @Scheduled(fixedDelayString = "${crypto.payment.transaction-check-interval:30}000")
    public void monitorTransactions() {
        if (!config.isEnabled()) {
            return;
        }

        try {
            log.debug("开始监听虚拟货币交易");
            
            // 获取所有启用的代币配置
            List<CryptoTokenConfig> activeTokenConfigs = getActiveTokenConfigs();
            
            for (CryptoTokenConfig tokenConfig : activeTokenConfigs) {
                try {
                    monitorTokenTransactions(tokenConfig);
                } catch (Exception e) {
                    log.error("监听代币交易失败: blockchain={}, tokenType={}", 
                            tokenConfig.getBlockchain(), tokenConfig.getTokenType(), e);
                }
            }
            
            log.debug("虚拟货币交易监听完成");
        } catch (Exception e) {
            log.error("监听虚拟货币交易失败", e);
        }
    }

    /**
     * 轮询交易结果 - 检查待确认的交易
     * 每60秒执行一次
     */
//    @Scheduled(fixedDelayString = "${crypto.payment.confirmation-check-interval:60}000")
    public void pollTransactionConfirmations() {
        if (!config.isEnabled()) {
            return;
        }

        try {
            log.debug("开始轮询交易确认状态");
            
            // 获取所有待确认的交易记录
            List<CryptoDepositRecord> pendingRecords = getPendingTransactionRecords();
            
            for (CryptoDepositRecord record : pendingRecords) {
                try {
                    checkTransactionConfirmation(record);
                } catch (Exception e) {
                    log.error("检查交易确认状态失败: txHash={}", record.getTxHash(), e);
                }
            }
            
            log.debug("交易确认状态轮询完成");
        } catch (Exception e) {
            log.error("轮询交易确认状态失败", e);
        }
    }

    /**
     * 预热钱包池
     */
//    @Scheduled(cron = "0/15 * * * * ?")
    public void warmupWalletPools() {
        if (!config.isEnabled()) {
            return;
        }

        try {
            log.info("开始预热钱包池");
            
            // 为每个支持的区块链预热钱包池
            if (config.getTron().isEnabled()) {
                walletService.warmupWalletPool("tron");
            }
            
            if (config.getBase().isEnabled()) {
                walletService.warmupWalletPool("base");
            }
            
            if (config.getSolana().isEnabled()) {
                walletService.warmupWalletPool("solana");
            }
            
            log.info("钱包池预热完成");
        } catch (Exception e) {
            log.error("预热钱包池失败", e);
        }
    }

    /**
     * 获取所有启用的代币配置
     */
    private List<CryptoTokenConfig> getActiveTokenConfigs() {
        try {
            return cryptoTokenConfigMapper.selectList(
                new LambdaQueryWrapper<CryptoTokenConfig>()
                    .eq(CryptoTokenConfig::getIsActive, true)
                    .orderByAsc(CryptoTokenConfig::getBlockchain)
                    .orderByAsc(CryptoTokenConfig::getTokenType)
            );
        } catch (Exception e) {
            log.error("获取启用的代币配置失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 监听指定代币的交易
     */
    private void monitorTokenTransactions(CryptoTokenConfig tokenConfig) {
        try {

            // 获取区块链服务
            BlockchainService blockchainService = cryptoTokenConfigService.getBlockchainService(tokenConfig.getBlockchain());

            // 获取最后监听的区块高度
            Long lastBlockHeight = getLastMonitorBlockHeight(tokenConfig);
            Long currentBlockHeight = blockchainService.getCurrentBlockHeight();
            lastBlockHeight = lastBlockHeight == null ? 0 : lastBlockHeight;

            if (currentBlockHeight == null) {
                log.error("无法获取区块高度: blockchain={}", tokenConfig.getBlockchain());
                return;
            }

            // 从区块日志表获取新区块数据
            List<JSONObject> newBlocks = getNewBlocksFromDatabase(tokenConfig.getBlockchain(), lastBlockHeight, currentBlockHeight);

            if (newBlocks.isEmpty()) {
                log.debug("没有新的区块数据: blockchain={}, lastBlock={}, currentBlock={}",
                        tokenConfig.getBlockchain(), lastBlockHeight, currentBlockHeight);
                return;
            }


            // 获取该代币配置下的所有钱包地址
            List<CryptoWallet> wallets = getWalletsByTokenConfig(tokenConfig);

            if (CollectionUtils.isNotEmpty(wallets)) {

                // 处理新区块中的交易
                for (JSONObject block : newBlocks) {
                    try {
                        processBlockTransactions(block, wallets, tokenConfig);
                    } catch (Exception e) {
                        log.error("处理区块交易失败: blockchain={}, block={}", tokenConfig.getBlockchain(), block, e);
                    }
                }
            }

            // 更新监听日志
            updateMonitorLog(tokenConfig, currentBlockHeight);
            
        } catch (Exception e) {
            log.error("监听代币交易失败: tokenConfig={}", tokenConfig, e);
        }
    }

    /**
     * 从数据库获取新区块数据
     */
    private List<JSONObject> getNewBlocksFromDatabase(String blockchain, Long fromBlock, Long toBlock) {
        try {
            return switch (blockchain.toUpperCase()) {
                case "TRON" -> new ArrayList<>(cryptoBlockMonitorService.getTronBlocks(fromBlock, toBlock).stream().map(block -> JSONObject.parseObject(block.getFullContent())).toList());
                case "BASE" -> new ArrayList<>(cryptoBlockMonitorService.getBaseBlocks(fromBlock, toBlock).stream().map(block -> JSONObject.parseObject(block.getFullContent())).toList());
                case "SOLANA" -> new ArrayList<>(cryptoBlockMonitorService.getSolanaBlocks(fromBlock, toBlock).stream().map(block -> JSONObject.parseObject(block.getFullContent())).toList());
                default -> {
                    log.warn("不支持的区块链类型: {}", blockchain);
                    yield new ArrayList<>();
                }
            };
        } catch (Exception e) {
            log.error("从数据库获取区块数据失败: blockchain={}, fromBlock={}, toBlock={}", 
                    blockchain, fromBlock, toBlock, e);
            return new ArrayList<>();
        }
    }

    /**
     * 处理区块中的交易
     */
    private void processBlockTransactions(JSONObject block, List<CryptoWallet> wallets, CryptoTokenConfig tokenConfig) {
        try {
            BlockchainService blockchainService = cryptoTokenConfigService.getBlockchainService(tokenConfig.getBlockchain());
            List<TransactionInfo> transactionInfos = blockchainService.extractTransactions(block, tokenConfig);
            for (TransactionInfo transactionInfo : transactionInfos) {
                // 检查是否为接收交易且金额符合要求
                CryptoWallet cryptoWallet = getValidWallet(transactionInfo, wallets, tokenConfig);
                if (null == cryptoWallet) {
                    continue;
                }
                processWalletRecharge(transactionInfo, cryptoWallet, tokenConfig);
            }
        } catch (Exception e) {
            log.error("处理区块交易失败: blockchain={}, block={}", tokenConfig.getBlockchain(), block, e);
        }
    }

    /**
     * 从TRON交易创建TransactionInfo
     */
    private TransactionInfo createTransactionInfoFromTronTx(JSONObject tx, CryptoTronBlock block, CryptoTokenConfig tokenConfig) {
        try {
            TransactionInfo txInfo = new TransactionInfo();
            txInfo.setTxHash(tx.getString("txID"));
            txInfo.setFromAddress(tx.getString("from"));
            txInfo.setToAddress(tx.getString("to"));
            txInfo.setContractAddress(tokenConfig.getContractAddress());
            txInfo.setAmount(parseTokenAmount(tx, tokenConfig));
            txInfo.setRawAmount(tx.getString("value"));
            txInfo.setBlockHeight(block.getBlockNumber());
            txInfo.setConfirmations(1);
            txInfo.setStatus("confirmed");
            txInfo.setTimestamp(block.getTimestamp());
            txInfo.setGasFee(parseGasFee(tx));
            txInfo.setSuccess(true);
            txInfo.setTokenSymbol(tokenConfig.getTokenType());
            txInfo.setBlockchain(tokenConfig.getBlockchain());
            
            return txInfo;
        } catch (Exception e) {
            log.error("从TRON交易创建TransactionInfo失败", e);
            return null;
        }
    }

    /**
     * 从BASE交易创建TransactionInfo
     */
    private TransactionInfo createTransactionInfoFromBaseTx(JSONObject tx, CryptoBaseBlock block, CryptoTokenConfig tokenConfig) {
        try {
            TransactionInfo txInfo = new TransactionInfo();
            txInfo.setTxHash(tx.getString("hash"));
            txInfo.setFromAddress(tx.getString("from"));
            txInfo.setToAddress(tx.getString("to"));
            txInfo.setContractAddress(tokenConfig.getContractAddress());
            txInfo.setAmount(parseTokenAmount(tx, tokenConfig));
            txInfo.setRawAmount(tx.getString("value"));
            txInfo.setBlockHeight(block.getBlockNumber());
            txInfo.setConfirmations(1);
            txInfo.setStatus("confirmed");
            txInfo.setTimestamp(block.getTimestamp());
            txInfo.setGasFee(parseGasFee(tx));
            txInfo.setSuccess(true);
            txInfo.setTokenSymbol(tokenConfig.getTokenType());
            txInfo.setBlockchain(tokenConfig.getBlockchain());
            
            return txInfo;
        } catch (Exception e) {
            log.error("从BASE交易创建TransactionInfo失败", e);
            return null;
        }
    }

    /**
     * 从SOLANA交易创建TransactionInfo
     */
    private TransactionInfo createTransactionInfoFromSolanaTx(JSONObject tx, CryptoSolanaBlock block, CryptoTokenConfig tokenConfig) {
        try {
            TransactionInfo txInfo = new TransactionInfo();
            txInfo.setTxHash(tx.getString("signature"));
            txInfo.setFromAddress(tx.getString("from"));
            txInfo.setToAddress(tx.getString("to"));
            txInfo.setContractAddress(tokenConfig.getContractAddress());
            txInfo.setAmount(parseTokenAmount(tx, tokenConfig));
            txInfo.setRawAmount(tx.getString("amount"));
            txInfo.setBlockHeight(block.getBlockNumber());
            txInfo.setConfirmations(1);
            txInfo.setStatus("confirmed");
            txInfo.setTimestamp(block.getTimestamp());
            txInfo.setGasFee(parseGasFee(tx));
            txInfo.setSuccess(true);
            txInfo.setTokenSymbol(tokenConfig.getTokenType());
            txInfo.setBlockchain(tokenConfig.getBlockchain());
            
            return txInfo;
        } catch (Exception e) {
            log.error("从SOLANA交易创建TransactionInfo失败", e);
            return null;
        }
    }

    /**
     * 解析代币金额
     */
    private BigDecimal parseTokenAmount(JSONObject tx, CryptoTokenConfig tokenConfig) {
        try {
            String rawAmount = tx.getString("value");
            if (rawAmount == null || rawAmount.isEmpty()) {
                return BigDecimal.ZERO;
            }
            
            // 根据代币精度解析金额
            BigDecimal amount = new BigDecimal(rawAmount);
            BigDecimal divisor = BigDecimal.valueOf(Math.pow(10, tokenConfig.getDecimals()));
            return amount.divide(divisor, 8, BigDecimal.ROUND_HALF_UP);
        } catch (Exception e) {
            log.error("解析代币金额失败", e);
            return BigDecimal.ZERO;
        }
    }

    /**
     * 解析Gas费用
     */
    private BigDecimal parseGasFee(JSONObject tx) {
        try {
            String gasPrice = tx.getString("gasPrice");
            String gasUsed = tx.getString("gasUsed");
            
            if (gasPrice != null && gasUsed != null) {
                BigDecimal price = new BigDecimal(gasPrice);
                BigDecimal used = new BigDecimal(gasUsed);
                return price.multiply(used);
            }
            
            return BigDecimal.ZERO;
        } catch (Exception e) {
            log.error("解析Gas费用失败", e);
            return BigDecimal.ZERO;
        }
    }

    /**
     * 验证是否为有效的接收交易
     */
    private CryptoWallet getValidWallet(TransactionInfo txInfo, List<CryptoWallet> wallets, CryptoTokenConfig tokenConfig) {
        try {
            // 检查是否为接收交易
            CryptoWallet cryptoWallet = wallets.stream()
                    .filter(wallet -> wallet.getAddress().equalsIgnoreCase(txInfo.getToAddress())).findFirst().orElse(null);
            
            if (null == cryptoWallet) {
                return null;
            }

            // 检查代币合约地址
            if (!tokenConfig.getContractAddress().equalsIgnoreCase(txInfo.getContractAddress())) {
                log.info("代币合约地址不匹配: expected={}, actual={}",
                        tokenConfig.getContractAddress(), txInfo.getContractAddress());
                return null;
            }

            // 检查金额是否在允许范围内
            BigDecimal amount = txInfo.getAmount();
            if (amount.compareTo(tokenConfig.getMinDepositAmount()) < 0 ||
                amount.compareTo(tokenConfig.getMaxDepositAmount()) > 0) {
                log.debug("交易金额不符合要求: amount={}, min={}, max={}", 
                        amount, tokenConfig.getMinDepositAmount(), tokenConfig.getMaxDepositAmount());
                return null;
            }

            // 检查是否已处理过该交易
            if (isTransactionProcessed(txInfo.getTxHash())) {
                return null;
            }

            return cryptoWallet;
        } catch (Exception e) {
            log.error("验证接收交易失败: txInfo={}", txInfo, e);
            return null;
        }
    }

    /**
     * 处理接收到的交易
     */
    private void processWalletRecharge(TransactionInfo txInfo, CryptoWallet wallet, CryptoTokenConfig tokenConfig) {
        try {

            // 创建充值记录
            CryptoDepositRecord record = cryptoTokenConfigService.createDepositRecord(txInfo, wallet, tokenConfig);
            record.setProcessStatus(1);
            record.setStatus(TransactionStatus.CONFIRMED.getCode());
            
            // 保存记录
            cryptoDepositRecordMapper.insert(record);

            // 标记交易已处理
            markTransactionProcessed(txInfo.getTxHash());

            // 加入充值队列
            JSONObject message = new JSONObject();
            message.put("orderNo", wallet.getAssignedOrderNo());
            cloudflareQueueService.sendMessage(CloudflareQueueEnum.CRYPTO_DEPOSIT.getValue(), message);
            
            log.info("处理接收交易成功: txHash={}, amount={}, wallet={}", 
                    txInfo.getTxHash(), txInfo.getAmount(), wallet.getAddress());
                    
        } catch (Exception e) {
            log.error("处理接收交易失败: txInfo={}", txInfo, e);
        }
    }

    /**
     * 查找钱包地址
     */
    private CryptoWallet findWalletByAddress(String address, String blockchain) {
        try {
            return cryptoWalletMapper.selectOne(
                new LambdaQueryWrapper<CryptoWallet>()
                    .eq(CryptoWallet::getAddress, address)
                    .eq(CryptoWallet::getBlockchain, blockchain)
            );
        } catch (Exception e) {
            log.error("查找钱包地址失败: address={}, blockchain={}", address, blockchain, e);
            return null;
        }
    }

    /**
     * 获取待确认的交易记录
     */
    private List<CryptoDepositRecord> getPendingTransactionRecords() {
        try {
            return cryptoDepositRecordMapper.selectList(
                new LambdaQueryWrapper<CryptoDepositRecord>()
                    .eq(CryptoDepositRecord::getStatus, TransactionStatus.PENDING.getCode())
                    .orderByAsc(CryptoDepositRecord::getCreateTime)
            );
        } catch (Exception e) {
            log.error("获取待确认交易记录失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 检查交易确认状态
     */
    private void checkTransactionConfirmation(CryptoDepositRecord record) {
        try {
            // 获取区块链服务
            BlockchainType blockchainType = BlockchainType.fromCode(record.getBlockchain());
            BlockchainService blockchainService = blockchainServiceMap.get(blockchainType);
            
            if (blockchainService == null) {
                log.warn("未找到对应的区块链服务: blockchain={}", record.getBlockchain());
                return;
            }

            CryptoTokenConfig tokenConfig = cryptoTokenConfigService.getTokenConfigByContractAddress(record.getBlockchain(), record.getContractAddress());

            // 获取当前确认数
            Integer currentConfirmations = blockchainService.getTransactionConfirmations(record.getTxHash(), tokenConfig);
            
            if (currentConfirmations == null) {
                log.warn("无法获取交易确认数: txHash={}", record.getTxHash());
                return;
            }

            // 更新确认数
            record.setConfirmations(currentConfirmations.longValue());

            // 检查是否达到所需确认数
            if (currentConfirmations >= record.getRequiredConfirmations()) {
                // 交易已确认
                record.setStatus(TransactionStatus.CONFIRMED.getCode());
                record.setConfirmedTime(new Date());
                
                // 处理用户充值
                if (processUserDeposit(record)) {
                    record.setProcessStatus(1); // 已处理
                    record.setProcessTime(new Date());
                } else {
                    record.setProcessStatus(2); // 处理失败
                }
            } else if (isTransactionTimeout(record)) {
                // 交易超时
                record.setStatus(TransactionStatus.TIMEOUT.getCode());
            }

            // 更新记录
            cryptoDepositRecordMapper.updateById(record);
            
            log.debug("检查交易确认状态: txHash={}, confirmations={}/{}, status={}", 
                    record.getTxHash(), currentConfirmations, record.getRequiredConfirmations(), record.getStatus());
                    
        } catch (Exception e) {
            log.error("检查交易确认状态失败: record={}", record, e);
        }
    }

    /**
     * 根据代币配置获取钱包列表
     */
    private List<CryptoWallet> getWalletsByTokenConfig(CryptoTokenConfig tokenConfig) {
        try {
            return cryptoWalletMapper.selectList(
                new LambdaQueryWrapper<CryptoWallet>()
                    .eq(CryptoWallet::getContractAddress, tokenConfig.getContractAddress())
                    .eq(CryptoWallet::getBlockchain, tokenConfig.getBlockchain())
                    .eq(CryptoWallet::getStatus, 1) // 1-已分配
            );
        } catch (Exception e) {
            log.error("获取钱包列表失败: blockchain={}", tokenConfig.getBlockchain(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取最后监听的区块高度
     */
    private Long getLastMonitorBlockHeight(CryptoTokenConfig tokenConfig) {
        try {
            CryptoMonitorLog monitorLog = cryptoMonitorLogMapper.selectOne(
                new LambdaQueryWrapper<CryptoMonitorLog>()
                    .eq(CryptoMonitorLog::getBlockchain, tokenConfig.getBlockchain())
                    .eq(CryptoMonitorLog::getContractAddress, tokenConfig.getContractAddress())
                    .eq(CryptoMonitorLog::getMonitorStatus, true)
                    .orderByDesc(CryptoMonitorLog::getLastMonitorTime)
                    .last("LIMIT 1")
            );
            
            return monitorLog != null ? monitorLog.getLastBlockHeight() : null;
        } catch (Exception e) {
            log.error("获取最后监听区块高度失败: blockchain={}", tokenConfig.getBlockchain(), e);
            return null;
        }
    }

    /**
     * 更新监听日志
     */
    private void updateMonitorLog(CryptoTokenConfig tokenConfig, Long currentBlockHeight) {
        try {
            // 更新或创建监听日志
            CryptoMonitorLog monitorLog = cryptoMonitorLogMapper.selectOne(
                new LambdaQueryWrapper<CryptoMonitorLog>()
                    .eq(CryptoMonitorLog::getContractAddress, tokenConfig.getContractAddress())
                    .eq(CryptoMonitorLog::getBlockchain, tokenConfig.getBlockchain())
                    .eq(CryptoMonitorLog::getMonitorStatus, true)
                    .last("LIMIT 1")
            );
            
            if (monitorLog == null) {
                monitorLog = new CryptoMonitorLog();
                monitorLog.setBlockchain(tokenConfig.getBlockchain());
                monitorLog.setContractAddress(tokenConfig.getContractAddress());
                monitorLog.setMonitorStatus(true);
                monitorLog.setErrorCount(0L);
                monitorLog.setLastBlockHeight(currentBlockHeight);
                monitorLog.setLastMonitorTime(new Date());
                cryptoMonitorLogMapper.insert(monitorLog);
            } else {
                monitorLog.setLastBlockHeight(currentBlockHeight);
                monitorLog.setLastMonitorTime(new Date());
                cryptoMonitorLogMapper.updateById(monitorLog);
            }

        } catch (Exception e) {
            log.error("更新监听日志失败: tokenConfig={}", tokenConfig, e);
        }
    }

    /**
     * 处理用户充值
     */
    private boolean processUserDeposit(CryptoDepositRecord record) {
        try {
            // 调用支付服务处理充值
            return cryptoPaymentService.processIncomingTransaction(
                    record.getTxHash(), record.getBlockchain(), record.getTokenType());
        } catch (Exception e) {
            log.error("处理用户充值失败: record={}", record, e);
            return false;
        }
    }

    /**
     * 检查交易是否超时
     */
    private boolean isTransactionTimeout(CryptoDepositRecord record) {
        long timeoutMinutes = config.getTransactionTimeoutMinutes();
        long elapsedMinutes = (System.currentTimeMillis() - record.getCreateTime().getTime()) / (1000 * 60);
        return elapsedMinutes > timeoutMinutes;
    }

    /**
     * 检查交易是否已处理
     */
    private boolean isTransactionProcessed(String txHash) {
        try {
            String key = "crypto:processed:tx:" + txHash;
            return redisCache.hasKey(key);
        } catch (Exception e) {
            log.error("检查交易是否已处理失败: txHash={}", txHash, e);
            return false;
        }
    }

    /**
     * 标记交易已处理
     */
    private void markTransactionProcessed(String txHash) {
        try {
            String key = "crypto:processed:tx:" + txHash;
            redisCache.setCacheObject(key, "1", 24, TimeUnit.HOURS);
        } catch (Exception e) {
            log.error("标记交易已处理失败: txHash={}", txHash, e);
        }
    }
}