package tv.shorthub.crypto.service.blockchain;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tv.shorthub.crypto.config.CryptoPaymentConfiguration;
import tv.shorthub.crypto.dto.TransactionInfo;
import tv.shorthub.crypto.enums.BlockchainType;
import tv.shorthub.system.domain.CryptoTokenConfig;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * Solana区块链服务实现
 */
@Slf4j
@Service
public class SolanaBlockchainService implements BlockchainService {

    @Autowired
    private CryptoPaymentConfiguration config;

    private final OkHttpClient httpClient;

    public SolanaBlockchainService() {
        this.httpClient = new OkHttpClient.Builder()
                .connectTimeout(30, TimeUnit.SECONDS)
                .readTimeout(60, TimeUnit.SECONDS)
                .writeTimeout(60, TimeUnit.SECONDS)
                .build();
    }

    @Override
    public BlockchainType getSupportedBlockchain() {
        return BlockchainType.SOLANA;
    }

    @Override
    public String generateWalletAddress() {
        return generateWalletAddress(null);
    }

    @Override
    public String generateWalletAddress(String privateKey) {
        // 默认使用v3版本（最安全）
        return generateWalletAddress(privateKey, "v2");
    }

    /**
     * 生成Solana钱包地址（支持不同版本）
     * @param privateKey 私钥
     * @param version 钱包版本 (legacy, v2, v3)
     * @return 钱包地址
     */
    public String generateWalletAddress(String privateKey, String version) {
        try {
            byte[] privateKeyBytes;
            
            if (privateKey != null && !privateKey.isEmpty()) {
                log.debug("处理私钥: length={}, first10chars={}", privateKey.length(), privateKey.substring(0, Math.min(10, privateKey.length())));
                
                // 处理Base58编码的私钥（Solana标准格式）
                try {
                    privateKeyBytes = tv.shorthub.crypto.utils.Base58Utils.decode(privateKey.trim());
                    log.debug("Base58解码成功，字节长度: {}", privateKeyBytes.length);
                } catch (Exception e1) {
                    log.debug("Base58解码失败: {}", e1.getMessage());
                    // 如果Base58解码失败，尝试十六进制解码
                    try {
                        privateKey = privateKey.startsWith("0x") ? privateKey.substring(2) : privateKey;
                        privateKeyBytes = org.web3j.utils.Numeric.hexStringToByteArray(privateKey);
                        log.debug("十六进制解码成功，字节长度: {}", privateKeyBytes.length);
                    } catch (Exception e2) {
                        log.debug("十六进制解码失败: {}", e2.getMessage());
                        // 如果都失败，直接使用字符串字节
                        privateKeyBytes = privateKey.getBytes();
                        log.debug("使用字符串字节，长度: {}", privateKeyBytes.length);
                    }
                }
            } else {
                // 生成新的私钥（64字节Ed25519格式）
                privateKeyBytes = new byte[64];
                java.security.SecureRandom secureRandom = new java.security.SecureRandom();
                secureRandom.nextBytes(privateKeyBytes);
            }
            
            String address;
            switch (version.toLowerCase()) {
                case "legacy":
                    // Legacy版本：使用简化算法
                    byte[] publicKeyBytes = generateEd25519PublicKey(privateKeyBytes);
                    address = tv.shorthub.crypto.utils.Base58Utils.encode(publicKeyBytes);
                    break;
                case "v2":
                    // v2版本：使用改进算法
                    address = generateSolanaV2Address(privateKeyBytes);
                    break;
                case "v3":
                default:
                    // v3版本：使用标准BIP44 Ed25519算法
                    address = generateSolanaV3Address(privateKeyBytes);
                    break;
            }
            
            log.info("生成Solana钱包地址成功: version={}, address={}", version, address);
            return address;
            
        } catch (Exception e) {
            log.error("生成Solana钱包地址失败: version={}", version, e);
            throw new RuntimeException("生成Solana钱包地址失败", e);
        }
    }

    /**
     * 简化的Ed25519公钥生成
     * 注意：这是简化实现，生产环境应使用专门的Ed25519库
     */
    private byte[] generateEd25519PublicKey(byte[] privateKeyBytes) {
        try {
            // 使用SHA-256哈希私钥作为种子
            java.security.MessageDigest digest = java.security.MessageDigest.getInstance("SHA-256");
            byte[] seed = digest.digest(privateKeyBytes);
            
            // 生成32字节的公钥（简化实现）
            byte[] publicKey = new byte[32];
            System.arraycopy(seed, 0, publicKey, 0, 32);
            
            return publicKey;
            
        } catch (Exception e) {
            log.error("生成Ed25519公钥失败", e);
            throw new RuntimeException("生成Ed25519公钥失败", e);
        }
    }

    /**
     * 生成Solana v2地址
     */
    private String generateSolanaV2Address(byte[] privateKeyBytes) {
        try {
            // v2版本使用改进的哈希算法
            java.security.MessageDigest digest = java.security.MessageDigest.getInstance("SHA-256");
            byte[] hash1 = digest.digest(privateKeyBytes);
            byte[] hash2 = digest.digest(hash1);
            
            // 取前32字节作为公钥
            byte[] publicKey = Arrays.copyOf(hash2, 32);
            return tv.shorthub.crypto.utils.Base58Utils.encode(publicKey);
        } catch (Exception e) {
            log.error("生成Solana v2地址失败", e);
            throw new RuntimeException("生成Solana v2地址失败", e);
        }
    }

    /**
     * 生成Solana标准地址（严格按照Phantom标准）
     * 支持64字节Solana私钥格式
     */
    private String generateSolanaV3Address(byte[] privateKeyBytes) {
        try {
            log.debug("处理Solana private key, length: {}", privateKeyBytes.length);
            
            net.i2p.crypto.eddsa.spec.EdDSAParameterSpec spec = 
                net.i2p.crypto.eddsa.spec.EdDSANamedCurveTable.getByName("Ed25519");
            
            byte[] seed;
            if (privateKeyBytes.length == 64) {
                // 64字节格式：前32字节是Ed25519种子
                seed = Arrays.copyOf(privateKeyBytes, 32);
                log.debug("使用64字节私钥的前32字节作为Ed25519种子");
            } else if (privateKeyBytes.length == 32) {
                // 32字节格式：直接使用作为种子
                seed = privateKeyBytes;
                log.debug("直接使用32字节作为Ed25519种子");
            } else {
                // 其他长度：尝试标准化处理
                if (privateKeyBytes.length > 64) {
                    // 太长：截取前32字节
                    seed = Arrays.copyOf(privateKeyBytes, 32);
                } else {
                    // 太短：补零到32字节
                    seed = new byte[32];
                    System.arraycopy(privateKeyBytes, 0, seed, 0, Math.min(privateKeyBytes.length, 32));
                }
                log.debug("标准化处理私钥到32字节种子");
            }
            
            // 使用种子创建Ed25519私钥
            net.i2p.crypto.eddsa.spec.EdDSAPrivateKeySpec privKeySpec = 
                new net.i2p.crypto.eddsa.spec.EdDSAPrivateKeySpec(seed, spec);
            net.i2p.crypto.eddsa.EdDSAPrivateKey privKey = 
                new net.i2p.crypto.eddsa.EdDSAPrivateKey(privKeySpec);
            
            // 生成对应的公钥
            net.i2p.crypto.eddsa.EdDSAPublicKey pubKey = 
                new net.i2p.crypto.eddsa.EdDSAPublicKey(
                    new net.i2p.crypto.eddsa.spec.EdDSAPublicKeySpec(privKey.getA(), spec));
            
            // Solana地址就是公钥的Base58编码
            byte[] publicKeyBytes = pubKey.getAbyte();
            String address = tv.shorthub.crypto.utils.Base58Utils.encode(publicKeyBytes);
            
            log.debug("生成Solana地址成功: {}", address);
            return address;
            
        } catch (Exception e) {
            log.error("生成Solana标准地址失败", e);
            throw new RuntimeException("生成Solana标准地址失败", e);
        }
    }

    @Override
    public boolean isValidAddress(String address) {
        if (address == null || address.length() != 44) {
            return false;
        }
        // Solana地址是Base58编码的32字节公钥
        return address.matches("^[1-9A-HJ-NP-Za-km-z]{44}$");
    }

    @Override
    public BigDecimal getBalance(String address, CryptoTokenConfig tokenConfig) {
        try {
            String rpcUrl = config.isTestnet() ? config.getSolana().getTestnetRpcUrl() : config.getSolana().getRpcUrl();
            
            // 获取代币账户地址
            String tokenAccountAddress = getTokenAccountAddress(address, tokenConfig.getContractAddress(), rpcUrl);
            if (tokenAccountAddress == null) {
                return BigDecimal.ZERO;
            }
            
            // 获取代币余额
            JSONObject requestBody = new JSONObject();
            requestBody.put("jsonrpc", "2.0");
            requestBody.put("method", "getTokenAccountBalance");
            requestBody.put("params", Arrays.asList(tokenAccountAddress));
            requestBody.put("id", 1);

            RequestBody body = RequestBody.create(
                    requestBody.toJSONString(),
                    MediaType.parse("application/json")
            );

            Request request = new Request.Builder()
                    .url(rpcUrl)
                    .post(body)
                    .build();

            try (Response response = httpClient.newCall(request).execute()) {
                if (response.isSuccessful() && response.body() != null) {
                    String responseBody = response.body().string();
                    JSONObject result = JSON.parseObject(responseBody);
                    
                    if (result.containsKey("result")) {
                        JSONObject resultData = result.getJSONObject("result");
                        JSONObject value = resultData.getJSONObject("value");
                        if (value != null) {
                            String amount = value.getString("amount");
                            int decimals = value.getInteger("decimals");
                            BigInteger balance = new BigInteger(amount);
                            return new BigDecimal(balance).divide(
                                    BigDecimal.valueOf(Math.pow(10, decimals))
                            );
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("获取Solana地址余额失败: address={}, contractAddress={}", address, tokenConfig.getContractAddress(), e);
        }
        return BigDecimal.ZERO;
    }

    /**
     * 获取Solana主币（SOL）余额
     * @param address Solana钱包地址
     * @return 主币余额（以SOL为单位）
     */
    public BigDecimal getNativeBalance(String address) {
        try {
            if (!isValidAddress(address)) {
                log.error("无效的Solana地址: {}", address);
                return BigDecimal.ZERO;
            }

            String rpcUrl = config.isTestnet() ? config.getSolana().getTestnetRpcUrl() : config.getSolana().getRpcUrl();
            
            // 查询主币余额
            JSONObject requestBody = new JSONObject();
            requestBody.put("jsonrpc", "2.0");
            requestBody.put("method", "getBalance");
            requestBody.put("params", Arrays.asList(address));
            requestBody.put("id", 1);

            RequestBody body = RequestBody.create(
                    requestBody.toJSONString(),
                    MediaType.parse("application/json")
            );

            Request request = new Request.Builder()
                    .url(rpcUrl)
                    .post(body)
                    .build();

            try (Response response = httpClient.newCall(request).execute()) {
                if (response.isSuccessful() && response.body() != null) {
                    String responseBody = response.body().string();
                    JSONObject result = JSON.parseObject(responseBody);
                    
                    if (result.containsKey("result")) {
                        JSONObject resultData = result.getJSONObject("result");
                        Long balanceLamports = resultData.getLong("value");
                        // SOL有9位小数（lamports）
                        return new BigDecimal(balanceLamports).divide(BigDecimal.valueOf(Math.pow(10, 9)));
                    } else if (result.containsKey("error")) {
                        JSONObject error = result.getJSONObject("error");
                        log.error("获取Solana主币余额失败: code={}, message={}", 
                                error.getInteger("code"), error.getString("message"));
                    }
                } else {
                    log.error("获取Solana主币余额HTTP请求失败: code={}", response.code());
                }
            }
        } catch (Exception e) {
            log.error("获取Solana主币余额异常: address={}", address, e);
        }
        return BigDecimal.ZERO;
    }

    @Override
    public TransactionInfo getTransactionInfo(String txHash, CryptoTokenConfig tokenConfig) {
        try {
            String rpcUrl = config.isTestnet() ? config.getSolana().getTestnetRpcUrl() : config.getSolana().getRpcUrl();
            
            JSONObject requestBody = new JSONObject();
            requestBody.put("jsonrpc", "2.0");
            requestBody.put("method", "getTransaction");
            requestBody.put("params", Arrays.asList(
                    txHash,
                    new JSONObject().fluentPut("encoding", "json").fluentPut("maxSupportedTransactionVersion", 0)
            ));
            requestBody.put("id", 1);

            RequestBody body = RequestBody.create(
                    requestBody.toJSONString(),
                    MediaType.parse("application/json")
            );

            Request request = new Request.Builder()
                    .url(rpcUrl)
                    .post(body)
                    .build();

            try (Response response = httpClient.newCall(request).execute()) {
                if (response.isSuccessful() && response.body() != null) {
                    String responseBody = response.body().string();
                    JSONObject result = JSON.parseObject(responseBody);
                    
                    if (result.containsKey("result")) {
                        JSONObject transaction = result.getJSONObject("result");
                        return parseTransaction(transaction, tokenConfig);
                    }
                }
            }
        } catch (Exception e) {
            log.error("获取Solana交易信息失败: txHash={}", txHash, e);
        }
        return null;
    }

    @Override
    public List<TransactionInfo> getTransactionHistory(String address, CryptoTokenConfig tokenConfig, int limit, Long fromBlock) {
        List<TransactionInfo> transactions = new ArrayList<>();
        try {
            String rpcUrl = config.isTestnet() ? config.getSolana().getTestnetRpcUrl() : config.getSolana().getRpcUrl();
            
            // 获取代币账户地址
            String tokenAccountAddress = getTokenAccountAddress(address, tokenConfig.getContractAddress(), rpcUrl);
            if (tokenAccountAddress == null) {
                return transactions;
            }
            
            // 获取交易历史
            JSONObject requestBody = new JSONObject();
            requestBody.put("jsonrpc", "2.0");
            requestBody.put("method", "getSignaturesForAddress");
            requestBody.put("params", Arrays.asList(
                    tokenAccountAddress,
                    new JSONObject().fluentPut("limit", limit)
            ));
            requestBody.put("id", 1);

            RequestBody body = RequestBody.create(
                    requestBody.toJSONString(),
                    MediaType.parse("application/json")
            );

            Request request = new Request.Builder()
                    .url(rpcUrl)
                    .post(body)
                    .build();

            try (Response response = httpClient.newCall(request).execute()) {
                if (response.isSuccessful() && response.body() != null) {
                    String responseBody = response.body().string();
                    JSONObject result = JSON.parseObject(responseBody);
                    
                    if (result.containsKey("result")) {
                        JSONArray signatures = result.getJSONArray("result");
                        for (int i = 0; i < signatures.size(); i++) {
                            JSONObject signature = signatures.getJSONObject(i);
                            String txHash = signature.getString("signature");
                            TransactionInfo txInfo = getTransactionInfo(txHash, tokenConfig);
                            if (txInfo != null) {
                                transactions.add(txInfo);
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("获取Solana交易历史失败: address={}", address, e);
        }
        return transactions;
    }

    @Override
    public Long getCurrentBlockHeight() {
        try {
            String rpcUrl = config.isTestnet() ? config.getSolana().getTestnetRpcUrl() : config.getSolana().getRpcUrl();
            
            JSONObject requestBody = new JSONObject();
            requestBody.put("jsonrpc", "2.0");
            requestBody.put("method", "getSlot");
            requestBody.put("params", new ArrayList<>());
            requestBody.put("id", 1);

            RequestBody body = RequestBody.create(
                    requestBody.toJSONString(),
                    MediaType.parse("application/json")
            );

            Request request = new Request.Builder()
                    .url(rpcUrl)
                    .post(body)
                    .build();

            try (Response response = httpClient.newCall(request).execute()) {
                if (response.isSuccessful() && response.body() != null) {
                    String responseBody = response.body().string();
                    JSONObject result = JSON.parseObject(responseBody);
                    
                    if (result.containsKey("result")) {
                        return result.getLong("result");
                    }
                }
            }
        } catch (Exception e) {
            log.error("获取Solana当前区块高度失败", e);
        }
        return null;
    }

    @Override
    public Integer getTransactionConfirmations(String txHash, CryptoTokenConfig tokenConfig) {
        try {
            TransactionInfo txInfo = getTransactionInfo(txHash, tokenConfig);
            if (txInfo != null && txInfo.getBlockHeight() != null) {
                Long currentHeight = getCurrentBlockHeight();
                if (currentHeight != null) {
                    return (int) (currentHeight - txInfo.getBlockHeight() + 1);
                }
            }
        } catch (Exception e) {
            log.error("获取Solana交易确认数失败: txHash={}", txHash, e);
        }
        return 0;
    }

    @Override
    public List<TransactionInfo> monitorNewTransactions(String address, CryptoTokenConfig tokenConfig, Long fromBlock) {
        // 实现新交易监听逻辑
        return getTransactionHistory(address, tokenConfig, 10, fromBlock);
    }

    @Override
    public boolean isTransactionConfirmed(String txHash, int requiredConfirmations, CryptoTokenConfig tokenConfig) {
        Integer confirmations = getTransactionConfirmations(txHash, tokenConfig);
        return confirmations != null && confirmations >= requiredConfirmations;
    }


    @Override
    public JSONObject getBlockInfo(Long blockNumber) {
        try {
            String rpcUrl = config.isTestnet() ? config.getSolana().getTestnetRpcUrl() : config.getSolana().getRpcUrl();
            
            // 构建RPC请求
            JSONObject requestBody = new JSONObject();
            requestBody.put("jsonrpc", "2.0");
            requestBody.put("method", "getBlock");
            
            JSONObject config = new JSONObject();
            config.put("encoding", "json");
            config.put("transactionDetails", "full");
            
            requestBody.put("params", Arrays.asList(blockNumber, config));
            requestBody.put("id", 1);

            RequestBody body = RequestBody.create(
                    requestBody.toJSONString(),
                    MediaType.parse("application/json")
            );

            Request request = new Request.Builder()
                    .url(rpcUrl)
                    .post(body)
                    .build();

            try (Response response = httpClient.newCall(request).execute()) {
                if (response.isSuccessful() && response.body() != null) {
                    String responseBody = response.body().string();
                    JSONObject result = JSON.parseObject(responseBody);
                    
                    if (result.containsKey("result")) {
                        JSONObject blockInfo = result.getJSONObject("result");
                        if (blockInfo != null) {
                            log.debug("获取SOLANA区块信息成功: blockNumber={}", blockNumber);
                            return blockInfo;
                        } else {
                            log.warn("SOLANA区块不存在: blockNumber={}", blockNumber);
                            return null;
                        }
                    } else if (result.containsKey("error")) {
                        JSONObject error = result.getJSONObject("error");
                        log.error("获取SOLANA区块信息失败: code={}, message={}", 
                                error.getInteger("code"), error.getString("message"));
                    }
                } else {
                    log.error("获取SOLANA区块信息HTTP请求失败: code={}", response.code());
                }
            }
        } catch (Exception e) {
            log.error("获取SOLANA区块信息异常: blockNumber={}", blockNumber, e);
        }
        return null;
    }

    private String getTokenAccountAddress(String walletAddress, String mintAddress, String rpcUrl) {
        try {
            // 获取代币账户地址
            JSONObject requestBody = new JSONObject();
            requestBody.put("jsonrpc", "2.0");
            requestBody.put("method", "getTokenAccountsByOwner");
            requestBody.put("params", Arrays.asList(
                    walletAddress,
                    new JSONObject().fluentPut("mint", mintAddress),
                    new JSONObject().fluentPut("encoding", "jsonParsed")
            ));
            requestBody.put("id", 1);

            RequestBody body = RequestBody.create(
                    requestBody.toJSONString(),
                    MediaType.parse("application/json")
            );

            Request request = new Request.Builder()
                    .url(rpcUrl)
                    .post(body)
                    .build();

            try (Response response = httpClient.newCall(request).execute()) {
                if (response.isSuccessful() && response.body() != null) {
                    String responseBody = response.body().string();
                    JSONObject result = JSON.parseObject(responseBody);
                    
                    if (result.containsKey("result")) {
                        JSONObject resultData = result.getJSONObject("result");
                        JSONArray value = resultData.getJSONArray("value");
                        if (value.size() > 0) {
                            JSONObject account = value.getJSONObject(0);
                            return account.getString("pubkey");
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("获取Solana代币账户地址失败: walletAddress={}, mintAddress={}", walletAddress, mintAddress, e);
        }
        return null;
    }

    @Override
    public TransactionInfo parseTransaction(JSONObject transaction, CryptoTokenConfig tokenConfig) {
        try {
            TransactionInfo txInfo = new TransactionInfo();
            txInfo.setTxHash(transaction.getString("transaction"));
            txInfo.setBlockHeight(transaction.getLong("slot"));
            txInfo.setBlockchain(BlockchainType.SOLANA.getCode());
            txInfo.setTokenSymbol(tokenConfig.getTokenSymbol());
            txInfo.setContractAddress(tokenConfig.getContractAddress());
            
            // 解析交易详情
            JSONObject meta = transaction.getJSONObject("meta");
            if (meta != null) {
                // 检查交易是否成功
                boolean success = meta.getInteger("err") == null;
                txInfo.setSuccess(success);
                txInfo.setStatus(success ? "confirmed" : "failed");
                
                // 解析转账信息
                JSONArray postTokenBalances = meta.getJSONArray("postTokenBalances");
                JSONArray preTokenBalances = meta.getJSONArray("preTokenBalances");
                
                if (postTokenBalances != null && preTokenBalances != null) {
                    // 分析代币余额变化
                    for (int i = 0; i < postTokenBalances.size(); i++) {
                        JSONObject postBalance = postTokenBalances.getJSONObject(i);
                        String mint = postBalance.getString("mint");
                        if (tokenConfig.getContractAddress().equalsIgnoreCase(mint)) {
                            String owner = postBalance.getString("owner");
                            String uiTokenAmount = postBalance.getJSONObject("uiTokenAmount").getString("uiAmount");
                            
                            // 查找对应的preBalance
                            for (int j = 0; j < preTokenBalances.size(); j++) {
                                JSONObject preBalance = preTokenBalances.getJSONObject(j);
                                if (mint.equals(preBalance.getString("mint")) && 
                                    owner.equals(preBalance.getString("owner"))) {
                                    
                                    String preAmount = preBalance.getJSONObject("uiTokenAmount").getString("uiAmount");
                                    BigDecimal amount = new BigDecimal(uiTokenAmount).subtract(new BigDecimal(preAmount));
                                    
                                    if (amount.compareTo(BigDecimal.ZERO) > 0) {
                                        // 这是接收方
                                        txInfo.setToAddress(owner);
                                        txInfo.setAmount(amount);
                                        txInfo.setRawAmount(amount.toString());
                                    } else {
                                        // 这是发送方
                                        txInfo.setFromAddress(owner);
                                    }
                                    break;
                                }
                            }
                        }
                    }
                }
            }
            
            return txInfo;
        } catch (Exception e) {
            log.error("解析Solana交易信息失败", e);
            return null;
        }
    }

    /**
     * 安全地解析十六进制字符串为Long值
     */
    private Long parseHexToLong(String hexString) {
        try {
            if (hexString == null || hexString.isEmpty()) {
                return null;
            }
            
            // 移除0x前缀
            String cleanHex = hexString.startsWith("0x") ? hexString.substring(2) : hexString;
            
            // 解析十六进制
            return Long.parseLong(cleanHex, 16);
        } catch (Exception e) {
            log.warn("解析十六进制字符串失败: hexString={}", hexString, e);
            return null;
        }
    }

    /**
     * 安全地解析十六进制字符串为BigDecimal值
     */
    private BigDecimal parseHexToBigDecimal(String hexString) {
        try {
            if (hexString == null || hexString.isEmpty()) {
                return null;
            }
            
            // 移除0x前缀
            String cleanHex = hexString.startsWith("0x") ? hexString.substring(2) : hexString;
            
            // 解析十六进制
            BigInteger bigInt = new BigInteger(cleanHex, 16);
            return new BigDecimal(bigInt);
        } catch (Exception e) {
            log.warn("解析十六进制字符串为BigDecimal失败: hexString={}", hexString, e);
            return null;
        }
    }


    @Override
    public List<TransactionInfo> extractTransactions(JSONObject blockContent, CryptoTokenConfig tokenConfig) {
        List<TransactionInfo> transactions = new ArrayList<>();

        try {
            // 解析区块的完整内容
            JSONArray txArray = blockContent.getJSONArray("transactions");

            for (int i = 0; i < txArray.size(); i++) {
                JSONObject tx = txArray.getJSONObject(i);

                // 检查是否为代币转账交易
                if (isTokenTransferTransaction(tx, tokenConfig)) {
                    TransactionInfo txInfo = parseTransaction(tx, tokenConfig);
                    if (txInfo != null) {
                        transactions.add(txInfo);
                    }
                }
            }
        } catch (Exception e) {
            log.error("从BASE区块提取交易失败", e);
        }

        return transactions;
    }



    /**
     * 从交易中获取合约地址
     */
    private String getContractAddressFromTransaction(JSONObject tx) {
        return tx.getString("programId");
    }

    /**
     * 检查是否为代币转账交易
     */
    private boolean isTokenTransferTransaction(JSONObject tx, CryptoTokenConfig tokenConfig) {
        try {
            // 检查合约地址是否匹配
            String contractAddress = getContractAddressFromTransaction(tx);
            if (!tokenConfig.getContractAddress().equalsIgnoreCase(contractAddress)) {
                return false;
            }

            // 检查交易是否成功
            return isTransactionSuccessful(tx);
        } catch (Exception e) {
            log.error("检查代币转账交易失败", e);
            return false;
        }
    }

    /**
     * 检查交易是否成功
     */
    private boolean isTransactionSuccessful(JSONObject tx) {
        return tx.getBoolean("success");
    }
} 