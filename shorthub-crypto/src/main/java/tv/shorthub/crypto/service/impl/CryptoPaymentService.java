package tv.shorthub.crypto.service.impl;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tv.shorthub.common.core.cache.CacheKeyUtils;
import tv.shorthub.common.core.cache.CacheService;
import tv.shorthub.common.core.redis.RedisCache;
import tv.shorthub.common.enums.CloudflareQueueEnum;
import tv.shorthub.common.enums.PayStatus;
import tv.shorthub.common.queue.CloudflareQueueService;
import tv.shorthub.common.utils.uuid.Seq;
import tv.shorthub.crypto.config.CryptoPaymentConfiguration;
import tv.shorthub.crypto.constant.CryptoConstants;
import tv.shorthub.system.domain.CryptoDepositRecord;
import tv.shorthub.system.domain.CryptoTokenConfig;
import tv.shorthub.system.domain.CryptoWallet;
import tv.shorthub.crypto.dto.CryptoDepositRequest;
import tv.shorthub.crypto.dto.CryptoDepositResponse;
import tv.shorthub.crypto.dto.TransactionInfo;
import tv.shorthub.crypto.enums.BlockchainType;
import tv.shorthub.crypto.enums.CryptoTokenType;
import tv.shorthub.crypto.enums.TransactionStatus;
import tv.shorthub.system.mapper.CryptoDepositRecordMapper;
import tv.shorthub.crypto.service.blockchain.BlockchainService;
import tv.shorthub.system.domain.AppOrderInfo;
import tv.shorthub.crypto.service.impl.CryptoTokenConfigService;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 虚拟货币支付服务实现
 */
@Slf4j
@Service
public class CryptoPaymentService {

    @Autowired
    private CryptoPaymentConfiguration config;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private CryptoWalletService walletService;

    @Autowired
    private CryptoDepositRecordMapper cryptoDepositRecordMapper;

    @Autowired
    private CryptoTokenConfigService cryptoTokenConfigService;

    @Autowired
    private List<BlockchainService> blockchainServices;

    @Autowired
    CloudflareQueueService cloudflareQueueService;

    @Autowired
    private CacheService cacheService;

    // 区块链服务映射
    private Map<BlockchainType, BlockchainService> blockchainServiceMap;

    public CryptoPaymentService() {
        this.blockchainServiceMap = new HashMap<>();
    }

    @Autowired
    public void initBlockchainServices(List<BlockchainService> services) {
        this.blockchainServiceMap = services.stream()
                .collect(Collectors.toMap(
                        BlockchainService::getSupportedBlockchain,
                        service -> service
                ));
    }

    @Transactional
    public CryptoDepositResponse createDepositOrder(CryptoDepositRequest request) {
        try {
            // 验证请求参数
            if (!validateRequest(request)) {
                throw new RuntimeException("请求参数无效");
            }

            // 获取代币配置
            CryptoTokenConfig tokenConfig = cryptoTokenConfigService.getTokenConfig(
                    request.getBlockchain(), request.getTokenType());
            
            if (tokenConfig == null) {
                throw new RuntimeException("代币配置不存在");
            }

            // 验证最小充值金额
            if (request.getAmount().compareTo(tokenConfig.getMinDepositAmount()) < 0) {
                throw new RuntimeException("充值金额不能小于 " + tokenConfig.getMinDepositAmount());
            }

            // 验证最大充值金额
            if (request.getAmount().compareTo(tokenConfig.getMaxDepositAmount()) > 0) {
                throw new RuntimeException("充值金额不能大于 " + tokenConfig.getMaxDepositAmount());
            }

            // 获取可用钱包
            CryptoWallet wallet = walletService.assignWalletToUser(request.getUserId(), request.getBlockchain());
            if (wallet == null) {
                throw new RuntimeException("没有可用的钱包地址");
            }

            // 生成订单号
            String orderNo = generateOrderNo(request.getBlockchain(), request.getTokenType());
            
            // 检查订单是否已存在
            if (isOrderExists(orderNo)) {
                throw new RuntimeException("订单已存在");
            }

            // 创建充值记录
            CryptoDepositRecord record = createDepositRecord(request, orderNo, wallet, tokenConfig);
            
            // 保存充值记录
            cryptoDepositRecordMapper.insert(record);

            // 创建应用订单
            createAppOrder(request, orderNo);

            // 缓存订单信息
            cacheOrderInfo(orderNo, record);

            // 构建响应
            CryptoDepositResponse response = CryptoDepositResponse.success(
                    orderNo,
                    wallet.getAddress(),
                    request.getBlockchain(),
                    request.getTokenType(),
                    request.getAmount()
            );

            log.info("创建充值订单成功: orderNo={}, wallet={}, amount={}", 
                    orderNo, wallet.getAddress(), request.getAmount());

            return response;

        } catch (Exception e) {
            log.error("创建充值订单失败: request={}", request, e);
            throw new RuntimeException("创建充值订单失败: " + e.getMessage());
        }
    }

    public CryptoDepositRecord queryDepositOrder(String orderNo) {
        // 先从缓存查询
        String cacheKey = CryptoConstants.TRANSACTION_CACHE_KEY + orderNo;
        CryptoDepositRecord cached = redisCache.getCacheObject(cacheKey);
        if (cached != null) {
            return cached;
        }

        // 从数据库查询
        try {
            CryptoDepositRecord record = cryptoDepositRecordMapper.selectOne(
                new LambdaQueryWrapper<CryptoDepositRecord>()
                    .eq(CryptoDepositRecord::getOrderNo, orderNo)
            );
            
            if (record != null) {
                // 更新缓存
                redisCache.setCacheObject(cacheKey, record, CryptoConstants.DEFAULT_TIMEOUT_MINUTES, TimeUnit.MINUTES);
                return record;
            }
        } catch (Exception e) {
            log.error("查询充值订单失败: orderNo={}", orderNo, e);
        }
        return null;
    }

    @Transactional
    public boolean processIncomingTransaction(String txHash, String blockchain, String tokenType) {
        try {
            // 检查交易是否已处理
            if (isTransactionProcessed(txHash)) {
                log.debug("交易已处理: txHash={}", txHash);
                return true;
            }

            // 获取代币配置
            CryptoTokenConfig tokenConfig = cryptoTokenConfigService.getTokenConfig(blockchain, tokenType);
            if (tokenConfig == null) {
                log.error("代币配置不存在: blockchain={}, tokenType={}", blockchain, tokenType);
                return false;
            }

            // 获取区块链服务
            BlockchainService blockchainService = cryptoTokenConfigService.getBlockchainService(blockchain);
            if (blockchainService == null) {
                log.error("区块链服务不存在: blockchain={}", blockchain);
                return false;
            }

            // 获取交易信息
            TransactionInfo txInfo = blockchainService.getTransactionInfo(txHash, tokenConfig);
            if (txInfo == null) {
                log.error("获取交易信息失败: txHash={}", txHash);
                return false;
            }

            // 验证交易
            if (!cryptoTokenConfigService.validateTransaction(txInfo, tokenConfig)) {
                log.warn("交易验证失败: txHash={}", txHash);
                return false;
            }

            // 查找对应的钱包
            CryptoWallet wallet = walletService.findWalletByAddress(txInfo.getToAddress(), blockchain);
            if (wallet == null) {
                log.warn("未找到对应的钱包: address={}", txInfo.getToAddress());
                return false;
            }

            // 检查交易是否已存在
            if (cryptoTokenConfigService.isTransactionExists(txHash)) {
                log.debug("交易记录已存在: txHash={}", txHash);
                return true;
            }

            // 创建充值记录
            CryptoDepositRecord record = cryptoTokenConfigService.createDepositRecord(txInfo, wallet, tokenConfig);
            
            // 保存记录
            cryptoDepositRecordMapper.insert(record);

            // 标记交易已处理
            markTransactionProcessed(txHash);

            log.info("处理接收交易成功: txHash={}, amount={}, wallet={}", 
                    txHash, txInfo.getAmount(), wallet.getAddress());

            return true;

        } catch (Exception e) {
            log.error("处理接收交易失败: txHash={}, blockchain={}, tokenType={}", 
                    txHash, blockchain, tokenType, e);
            return false;
        }
    }

    public void monitorAndProcessTransactions() {
        log.info("开始监听虚拟货币交易");
        
        try {
            // 获取所有启用的代币配置
            List<CryptoTokenConfig> activeTokenConfigs = cryptoTokenConfigService.getActiveTokenConfigs();
            
            for (CryptoTokenConfig tokenConfig : activeTokenConfigs) {
                try {
                    monitorTokenTransactions(tokenConfig);
                } catch (Exception e) {
                    log.error("监听代币交易失败: blockchain={}, tokenType={}", 
                            tokenConfig.getBlockchain(), tokenConfig.getTokenType(), e);
                }
            }
        } catch (Exception e) {
            log.error("监听虚拟货币交易失败", e);
        }
    }

    public List<CryptoDepositRecord> getUserDepositHistory(String userId, int limit) {
        try {
            List<CryptoDepositRecord> records = cryptoDepositRecordMapper.selectList(
                new LambdaQueryWrapper<CryptoDepositRecord>()
                    .eq(CryptoDepositRecord::getUserId, userId)
                    .orderByDesc(CryptoDepositRecord::getCreateTime)
                    .last("LIMIT " + limit)
            );
            
            return records;
        } catch (Exception e) {
            log.error("获取用户充值历史失败: userId={}", userId, e);
            return new ArrayList<>();
        }
    }

    public boolean retryFailedTransaction(Long recordId) {
        try {
            CryptoDepositRecord record = cryptoDepositRecordMapper.selectById(recordId);
            if (record != null && TransactionStatus.FAILED.getCode().equals(record.getStatus())) {
                // 重新处理交易
                return processIncomingTransaction(record.getTxHash(), record.getBlockchain(), record.getTokenType());
            }
            
            log.info("重试失败交易: recordId={}", recordId);
            return false;
        } catch (Exception e) {
            log.error("重试失败交易失败: recordId={}", recordId, e);
            return false;
        }
    }

    public Integer verifyTransactionConfirmations(String txHash, String blockchain, CryptoTokenConfig tokenConfig) {
        try {
            BlockchainType blockchainType = BlockchainType.fromCode(blockchain);
            BlockchainService blockchainService = blockchainServiceMap.get(blockchainType);
            
            if (blockchainService != null) {
                return blockchainService.getTransactionConfirmations(txHash, tokenConfig);
            }
        } catch (Exception e) {
            log.error("验证交易确认数失败: txHash={}", txHash, e);
        }
        return null;
    }

    private boolean validateRequest(CryptoDepositRequest request) {
        try {
            BlockchainType.fromCode(request.getBlockchain());
            CryptoTokenType.fromSymbol(request.getTokenType());
            return true;
        } catch (IllegalArgumentException e) {
            log.error("请求参数验证失败", e);
            return false;
        }
    }

    private String generateOrderNo(String blockchain, String tokenType) {
        return "CRYPTO_" + blockchain.toUpperCase() + "_" + tokenType + "_" + Seq.getId();
    }

    private boolean isOrderExists(String orderNo) {
        // 检查数据库中是否已存在订单
        try {
            CryptoDepositRecord existingRecord = cryptoDepositRecordMapper.selectOne(
                new LambdaQueryWrapper<CryptoDepositRecord>()
                    .eq(CryptoDepositRecord::getOrderNo, orderNo)
            );
            return existingRecord != null;
        } catch (Exception e) {
            log.error("检查订单是否存在失败: orderNo={}", orderNo, e);
            return false;
        }
    }

    private CryptoDepositRecord createDepositRecord(CryptoDepositRequest request, String orderNo, 
                                                  CryptoWallet wallet, CryptoTokenConfig tokenConfig) {
        CryptoDepositRecord record = new CryptoDepositRecord();
        record.setOrderNo(orderNo);
        record.setUserId(request.getUserId());
        record.setBlockchain(request.getBlockchain());
        record.setTokenType(request.getTokenType());
        record.setToAddress(wallet.getAddress());
        record.setAmount(request.getAmount());
        record.setStatus(TransactionStatus.PENDING.getCode());
        record.setRequiredConfirmations(tokenConfig.getRequiredConfirmations());
        record.setProcessStatus(0); // 未处理
        record.setRetryCount(0L);
        record.setRemark("用户充值订单");
        
        // 保存到数据库
        cryptoDepositRecordMapper.insert(record);
        
        return record;
    }

    private void createAppOrder(CryptoDepositRequest request, String orderNo) {
        AppOrderInfo order = new AppOrderInfo();
        order.setOrderNo(orderNo);
        order.setUserId(request.getUserId());
        order.setOrderChannel("crypto");
        order.setOrderStatus(PayStatus.WAIT_PAY.getValue().longValue());
        order.setOrderAmount(request.getAmount());
        order.setCurrency("USD");
        order.setPayType("1"); // 金币充值
        order.setDeviceType(request.getDeviceType());
        
        JSONObject extendJson = new JSONObject();
        extendJson.put("blockchain", request.getBlockchain());
        extendJson.put("tokenType", request.getTokenType());
        extendJson.put("extendInfo", request.getExtendInfo());
        order.setExtendJson(extendJson);
    }

    private void cacheOrderInfo(String orderNo, CryptoDepositRecord record) {
        String cacheKey = CryptoConstants.TRANSACTION_CACHE_KEY + orderNo;
        redisCache.setCacheObject(cacheKey, record, CryptoConstants.DEFAULT_TIMEOUT_MINUTES, TimeUnit.MINUTES);
    }

    private boolean isTransactionProcessed(String txHash) {
        String key = CryptoConstants.PROCESSED_TX_KEY + txHash;
        return redisTemplate.hasKey(key);
    }

    private void markTransactionProcessed(String txHash) {
        String key = CryptoConstants.PROCESSED_TX_KEY + txHash;
        redisTemplate.opsForValue().set(key, "1", 24, TimeUnit.HOURS);
    }

    private Long getRequiredConfirmations(BlockchainType blockchain) {
        switch (blockchain) {
            case TRON:
                return (long) config.getTron().getConfirmations();
            case BASE:
                return (long) config.getBase().getConfirmations();
            case SOLANA:
                return (long) config.getSolana().getConfirmations();
            default:
                return (long) CryptoConstants.DEFAULT_CONFIRMATION_COUNT;
        }
    }

    private CryptoDepositRecord createOrUpdateDepositRecord(TransactionInfo txInfo, CryptoWallet wallet, 
                                                          CryptoTokenType tokenType) {
        // 先查询是否已存在记录
        CryptoDepositRecord existingRecord = cryptoDepositRecordMapper.selectOne(
            new LambdaQueryWrapper<CryptoDepositRecord>()
                .eq(CryptoDepositRecord::getTxHash, txInfo.getTxHash())
        );
        
        CryptoDepositRecord record = new CryptoDepositRecord();
        record.setTxHash(txInfo.getTxHash());
        record.setUserId(wallet.getAssignedUserId());
        record.setBlockchain(txInfo.getBlockchain());
        record.setTokenType(tokenType.getSymbol());
        record.setFromAddress(txInfo.getFromAddress());
        record.setToAddress(txInfo.getToAddress());
        record.setAmount(txInfo.getAmount());
        record.setRawAmount(txInfo.getRawAmount());
        record.setBlockHeight(txInfo.getBlockHeight());
        record.setConfirmations(txInfo.getConfirmations().longValue());
        record.setStatus(TransactionStatus.CONFIRMED.getCode());
        record.setTransactionTime(new Date(txInfo.getTimestamp()));
        record.setConfirmedTime(new Date());
        
        // 保存或更新
        if (existingRecord != null) {
            record.setId(existingRecord.getId());
            cryptoDepositRecordMapper.updateById(record);
        } else {
            cryptoDepositRecordMapper.insert(record);
        }
        
        return record;
    }

    private boolean processUserDeposit(CryptoDepositRecord record) {
        try {
            // 实现用户充值处理逻辑
            record.setProcessStatus(1); // 已处理
            record.setProcessTime(new Date());
            
            // 更新处理状态
            cryptoDepositRecordMapper.updateById(record);

            // 充值成功队列
            
            log.info("用户充值处理成功: userId={}, amount={}, txHash={}", 
                    record.getUserId(), record.getAmount(), record.getTxHash());
            
            return true;
        } catch (Exception e) {
            log.error("处理用户充值失败: record={}", record, e);
            record.setProcessStatus(2); // 处理失败
            record.setFailureReason(e.getMessage());
            
            // 更新失败状态
            cryptoDepositRecordMapper.updateById(record);
            
            return false;
        }
    }

    private void monitorTokenTransactions(CryptoTokenConfig tokenConfig) {
        try {
            // 获取该代币配置下的所有钱包地址
            List<CryptoWallet> wallets = cryptoTokenConfigService.getWalletsByTokenConfig(tokenConfig);
            
            if (wallets.isEmpty()) {
                log.debug("代币配置下没有钱包地址: blockchain={}, tokenType={}", 
                        tokenConfig.getBlockchain(), tokenConfig.getTokenType());
                return;
            }

            // 获取区块链服务
            BlockchainService blockchainService = cryptoTokenConfigService.getBlockchainService(tokenConfig.getBlockchain());
            
            if (blockchainService == null) {
                log.warn("未找到对应的区块链服务: blockchain={}", tokenConfig.getBlockchain());
                return;
            }

            // 获取代币类型
            CryptoTokenType tokenType = cryptoTokenConfigService.getTokenType(tokenConfig.getTokenType());
            
            if (tokenType == null) {
                log.warn("未找到对应的代币类型: tokenType={}", tokenConfig.getTokenType());
                return;
            }

            // 监听新交易
            for (CryptoWallet wallet : wallets) {
                try {
                    List<TransactionInfo> transactions = blockchainService.monitorNewTransactions(
                            wallet.getAddress(), tokenConfig, null);
                    
                    for (TransactionInfo txInfo : transactions) {
                        // 检查是否为接收交易且金额符合要求
                        if (wallet.getAddress().equalsIgnoreCase(txInfo.getToAddress()) &&
                            cryptoTokenConfigService.validateTransaction(txInfo, tokenConfig)) {
                            processIncomingTransaction(txInfo.getTxHash(), tokenConfig.getBlockchain(), tokenConfig.getTokenType());
                        }
                    }
                } catch (Exception e) {
                    log.error("监听钱包交易失败: wallet={}, tokenConfig={}", 
                            wallet.getAddress(), tokenConfig.getTokenType(), e);
                }
            }
        } catch (Exception e) {
            log.error("监听代币交易失败: tokenConfig={}", tokenConfig, e);
        }
    }
} 