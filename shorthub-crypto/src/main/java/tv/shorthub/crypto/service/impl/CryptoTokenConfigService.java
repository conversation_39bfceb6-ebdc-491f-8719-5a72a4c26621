package tv.shorthub.crypto.service.impl;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tv.shorthub.crypto.dto.TransactionInfo;
import tv.shorthub.crypto.enums.BlockchainType;
import tv.shorthub.crypto.enums.CryptoTokenType;
import tv.shorthub.crypto.enums.TransactionStatus;
import tv.shorthub.system.domain.CryptoDepositRecord;
import tv.shorthub.system.domain.CryptoTokenConfig;
import tv.shorthub.system.domain.CryptoWallet;
import tv.shorthub.system.mapper.CryptoDepositRecordMapper;
import tv.shorthub.system.mapper.CryptoTokenConfigMapper;
import tv.shorthub.system.mapper.CryptoWalletMapper;
import tv.shorthub.crypto.service.blockchain.BlockchainService;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 虚拟货币代币配置服务
 * 基于CryptoTokenConfig进行交易监听和配置管理
 */
@Slf4j
@Service
public class CryptoTokenConfigService {

    @Autowired
    private CryptoTokenConfigMapper cryptoTokenConfigMapper;

    @Autowired
    private CryptoWalletMapper cryptoWalletMapper;

    @Autowired
    private CryptoDepositRecordMapper cryptoDepositRecordMapper;

    @Autowired
    private List<BlockchainService> blockchainServices;

    // 区块链服务映射
    private Map<BlockchainType, BlockchainService> blockchainServiceMap;

    @Autowired
    public void initBlockchainServices(List<BlockchainService> services) {
        this.blockchainServiceMap = services.stream()
                .collect(Collectors.toMap(
                        BlockchainService::getSupportedBlockchain,
                        service -> service
                ));
    }

    /**
     * 获取所有启用的代币配置
     */
    public List<CryptoTokenConfig> getActiveTokenConfigs() {
        try {
            return cryptoTokenConfigMapper.selectList(
                new LambdaQueryWrapper<CryptoTokenConfig>()
                    .eq(CryptoTokenConfig::getIsActive, true)
                    .orderByAsc(CryptoTokenConfig::getBlockchain)
                    .orderByAsc(CryptoTokenConfig::getTokenType)
            );
        } catch (Exception e) {
            log.error("获取启用的代币配置失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 根据区块链类型获取代币配置
     */
    public List<CryptoTokenConfig> getTokenConfigsByBlockchain(String blockchain) {
        try {
            return cryptoTokenConfigMapper.selectList(
                new LambdaQueryWrapper<CryptoTokenConfig>()
                    .eq(CryptoTokenConfig::getBlockchain, blockchain)
                    .eq(CryptoTokenConfig::getIsActive, true)
                    .orderByAsc(CryptoTokenConfig::getTokenType)
            );
        } catch (Exception e) {
            log.error("根据区块链类型获取代币配置失败: blockchain={}", blockchain, e);
            return new ArrayList<>();
        }
    }

    /**
     * 根据代币类型获取配置
     */
    public CryptoTokenConfig getTokenConfig(String blockchain, String tokenType) {
        try {
            return cryptoTokenConfigMapper.selectOne(
                new LambdaQueryWrapper<CryptoTokenConfig>()
                    .eq(CryptoTokenConfig::getBlockchain, blockchain)
                    .eq(CryptoTokenConfig::getTokenType, tokenType)
                    .eq(CryptoTokenConfig::getIsActive, true)
            );
        } catch (Exception e) {
            log.error("获取代币配置失败: blockchain={}, tokenType={}", blockchain, tokenType, e);
            return null;
        }
    }

    /**
     * 根据代币地址获取配置
     */
    public CryptoTokenConfig getTokenConfigByContractAddress(String blockchain, String contractAddress) {
        try {
            return cryptoTokenConfigMapper.selectOne(
                    new LambdaQueryWrapper<CryptoTokenConfig>()
                            .eq(CryptoTokenConfig::getBlockchain, blockchain)
                            .eq(CryptoTokenConfig::getContractAddress, contractAddress)
                            .eq(CryptoTokenConfig::getIsActive, true)
            );
        } catch (Exception e) {
            log.error("获取代币配置失败: blockchain={}, contractAddress={}", blockchain, contractAddress, e);
            return null;
        }
    }

    /**
     * 验证交易是否符合代币配置要求
     */
    public boolean validateTransaction(TransactionInfo txInfo, CryptoTokenConfig tokenConfig) {
        try {
            // 检查代币合约地址
            if (!tokenConfig.getContractAddress().equalsIgnoreCase(txInfo.getContractAddress())) {
                log.debug("代币合约地址不匹配: expected={}, actual={}", 
                        tokenConfig.getContractAddress(), txInfo.getContractAddress());
                return false;
            }

            // 检查金额是否在允许范围内
            BigDecimal amount = txInfo.getAmount();
            if (amount.compareTo(tokenConfig.getMinDepositAmount()) < 0 || 
                amount.compareTo(tokenConfig.getMaxDepositAmount()) > 0) {
                log.debug("交易金额不符合要求: amount={}, min={}, max={}", 
                        amount, tokenConfig.getMinDepositAmount(), tokenConfig.getMaxDepositAmount());
                return false;
            }

            return true;
        } catch (Exception e) {
            log.error("验证交易失败: txInfo={}, tokenConfig={}", txInfo, tokenConfig, e);
            return false;
        }
    }

    /**
     * 根据代币配置获取钱包列表
     */
    public List<CryptoWallet> getWalletsByTokenConfig(CryptoTokenConfig tokenConfig) {
        try {
            return cryptoWalletMapper.selectList(
                new LambdaQueryWrapper<CryptoWallet>()
                    .eq(CryptoWallet::getBlockchain, tokenConfig.getBlockchain())
                    .eq(CryptoWallet::getStatus, 0) // 0-可用状态
            );
        } catch (Exception e) {
            log.error("获取钱包列表失败: blockchain={}", tokenConfig.getBlockchain(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 创建充值记录
     */
    public CryptoDepositRecord createDepositRecord(TransactionInfo txInfo, CryptoWallet wallet, 
                                                 CryptoTokenConfig tokenConfig) {
        CryptoDepositRecord record = new CryptoDepositRecord();
        record.setOrderNo(wallet.getAssignedOrderNo());
        record.setUserId(wallet.getAssignedUserId());
        record.setTxHash(txInfo.getTxHash());
        record.setBlockchain(tokenConfig.getBlockchain());
        record.setTokenType(tokenConfig.getTokenType());
        record.setContractAddress(tokenConfig.getContractAddress());
        record.setFromAddress(txInfo.getFromAddress());
        record.setToAddress(txInfo.getToAddress());
        record.setAmount(txInfo.getAmount());
        record.setRawAmount(txInfo.getRawAmount());
        record.setStatus(TransactionStatus.PENDING.getCode());
        record.setBlockHeight(txInfo.getBlockHeight());
//        record.setConfirmations(txInfo.getConfirmations().longValue());
        record.setRequiredConfirmations(tokenConfig.getRequiredConfirmations());
//        record.setTransactionTime(new Date(txInfo.getTimestamp()));
        record.setProcessStatus(0); // 未处理
        record.setRetryCount(0L);
        record.setGasFee(txInfo.getGasFee());
        record.setRawData(JSONObject.parseObject(JSONObject.toJSONString(txInfo)));
        
        return record;
    }

    /**
     * 获取区块链服务
     */
    public BlockchainService getBlockchainService(String blockchain) {
        try {
            BlockchainType blockchainType = BlockchainType.fromCode(blockchain);
            return blockchainServiceMap.get(blockchainType);
        } catch (Exception e) {
            log.error("获取区块链服务失败: blockchain={}", blockchain, e);
            return null;
        }
    }

    /**
     * 获取代币类型
     */
    public CryptoTokenType getTokenType(String tokenSymbol) {
        try {
            return CryptoTokenType.fromSymbol(tokenSymbol);
        } catch (Exception e) {
            log.error("获取代币类型失败: tokenSymbol={}", tokenSymbol, e);
            return null;
        }
    }

    /**
     * 检查交易是否已存在
     */
    public boolean isTransactionExists(String txHash) {
        try {
            CryptoDepositRecord existingRecord = cryptoDepositRecordMapper.selectOne(
                new LambdaQueryWrapper<CryptoDepositRecord>()
                    .eq(CryptoDepositRecord::getTxHash, txHash)
            );
            return existingRecord != null;
        } catch (Exception e) {
            log.error("检查交易是否存在失败: txHash={}", txHash, e);
            return false;
        }
    }

    /**
     * 获取待确认的交易记录
     */
    public List<CryptoDepositRecord> getPendingTransactionRecords() {
        try {
            return cryptoDepositRecordMapper.selectList(
                new LambdaQueryWrapper<CryptoDepositRecord>()
                    .eq(CryptoDepositRecord::getStatus, TransactionStatus.PENDING.getCode())
                    .orderByAsc(CryptoDepositRecord::getCreateTime)
            );
        } catch (Exception e) {
            log.error("获取待确认交易记录失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 更新交易确认状态
     */
    public boolean updateTransactionConfirmation(CryptoDepositRecord record, Integer currentConfirmations) {
        try {
            // 更新确认数
            record.setConfirmations(currentConfirmations.longValue());

            // 检查是否达到所需确认数
            if (currentConfirmations >= record.getRequiredConfirmations()) {
                // 交易已确认
                record.setStatus(TransactionStatus.CONFIRMED.getCode());
                record.setConfirmedTime(new Date());
            }

            // 更新记录
            return cryptoDepositRecordMapper.updateById(record) > 0;
        } catch (Exception e) {
            log.error("更新交易确认状态失败: record={}", record, e);
            return false;
        }
    }

    /**
     * 生成订单号
     */
    private String generateOrderNo(String txHash) {
        return "CRYPTO_" + txHash.substring(0, 8) + "_" + System.currentTimeMillis();
    }

    /**
     * 获取代币精度
     */
    public Long getTokenDecimals(String blockchain, String tokenType) {
        try {
            CryptoTokenConfig tokenConfig = getTokenConfig(blockchain, tokenType);
            return tokenConfig != null ? tokenConfig.getDecimals() : null;
        } catch (Exception e) {
            log.error("获取代币精度失败: blockchain={}, tokenType={}", blockchain, tokenType, e);
            return null;
        }
    }

    /**
     * 获取代币合约地址
     */
    public String getTokenContractAddress(String blockchain, String tokenType) {
        try {
            CryptoTokenConfig tokenConfig = getTokenConfig(blockchain, tokenType);
            return tokenConfig != null ? tokenConfig.getContractAddress() : null;
        } catch (Exception e) {
            log.error("获取代币合约地址失败: blockchain={}, tokenType={}", blockchain, tokenType, e);
            return null;
        }
    }

    /**
     * 获取最小充值金额
     */
    public BigDecimal getMinDepositAmount(String blockchain, String tokenType) {
        try {
            CryptoTokenConfig tokenConfig = getTokenConfig(blockchain, tokenType);
            return tokenConfig != null ? tokenConfig.getMinDepositAmount() : null;
        } catch (Exception e) {
            log.error("获取最小充值金额失败: blockchain={}, tokenType={}", blockchain, tokenType, e);
            return null;
        }
    }

    /**
     * 获取最大充值金额
     */
    public BigDecimal getMaxDepositAmount(String blockchain, String tokenType) {
        try {
            CryptoTokenConfig tokenConfig = getTokenConfig(blockchain, tokenType);
            return tokenConfig != null ? tokenConfig.getMaxDepositAmount() : null;
        } catch (Exception e) {
            log.error("获取最大充值金额失败: blockchain={}, tokenType={}", blockchain, tokenType, e);
            return null;
        }
    }

    /**
     * 获取所需确认数
     */
    public Long getRequiredConfirmations(String blockchain, String tokenType) {
        try {
            CryptoTokenConfig tokenConfig = getTokenConfig(blockchain, tokenType);
            return tokenConfig != null ? tokenConfig.getRequiredConfirmations() : null;
        } catch (Exception e) {
            log.error("获取所需确认数失败: blockchain={}, tokenType={}", blockchain, tokenType, e);
            return null;
        }
    }
} 