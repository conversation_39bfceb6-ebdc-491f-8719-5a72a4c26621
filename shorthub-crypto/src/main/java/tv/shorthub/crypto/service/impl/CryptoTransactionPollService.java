package tv.shorthub.crypto.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import tv.shorthub.crypto.config.CryptoPaymentConfiguration;
import tv.shorthub.crypto.dto.TransactionPollResult;
import tv.shorthub.crypto.enums.TransactionStatus;
import tv.shorthub.system.domain.CryptoDepositRecord;
import tv.shorthub.system.domain.CryptoTokenConfig;
import tv.shorthub.system.mapper.CryptoDepositRecordMapper;

import java.util.Date;
import java.util.List;

/**
 * 虚拟货币交易轮询服务
 * 专门处理交易确认状态的轮询检查
 */
@Slf4j
@Service
public class CryptoTransactionPollService {

    @Autowired
    private CryptoPaymentConfiguration config;

    @Autowired
    private CryptoDepositRecordMapper cryptoDepositRecordMapper;

    @Autowired
    private CryptoTokenConfigService cryptoTokenConfigService;

    @Autowired
    private CryptoPaymentService cryptoPaymentService;

    /**
     * 轮询交易确认状态
     * 每60秒执行一次
     */
//    @Scheduled(fixedDelayString = "${crypto.payment.confirmation-check-interval:60}000")
    public void pollTransactionConfirmations() {
        if (!config.isEnabled()) {
            return;
        }

        try {
            log.debug("开始轮询交易确认状态");
            
            // 获取所有待确认的交易记录
            List<CryptoDepositRecord> pendingRecords = getPendingTransactionRecords();
            
            for (CryptoDepositRecord record : pendingRecords) {
                try {
                    TransactionPollResult pollResult = checkTransactionConfirmation(record);
                    
                    if (pollResult != null) {
                        updateTransactionStatus(record, pollResult);
                    }
                } catch (Exception e) {
                    log.error("检查交易确认状态失败: txHash={}", record.getTxHash(), e);
                }
            }
            
            log.debug("交易确认状态轮询完成，处理记录数: {}", pendingRecords.size());
        } catch (Exception e) {
            log.error("轮询交易确认状态失败", e);
        }
    }

    /**
     * 获取待确认的交易记录
     */
    private List<CryptoDepositRecord> getPendingTransactionRecords() {
        try {
            return cryptoDepositRecordMapper.selectList(
                new LambdaQueryWrapper<CryptoDepositRecord>()
                    .eq(CryptoDepositRecord::getStatus, TransactionStatus.PENDING.getCode())
                    .orderByAsc(CryptoDepositRecord::getCreateTime)
            );
        } catch (Exception e) {
            log.error("获取待确认交易记录失败", e);
            return List.of();
        }
    }

    /**
     * 检查交易确认状态
     */
    private TransactionPollResult checkTransactionConfirmation(CryptoDepositRecord record) {
        try {
            // 获取区块链服务
            var blockchainService = cryptoTokenConfigService.getBlockchainService(record.getBlockchain());
            
            if (blockchainService == null) {
                log.warn("未找到对应的区块链服务: blockchain={}", record.getBlockchain());
                return null;
            }

            CryptoTokenConfig tokenConfig = cryptoTokenConfigService.getTokenConfigByContractAddress(record.getBlockchain(), record.getContractAddress());


            // 获取当前确认数
            Integer currentConfirmations = blockchainService.getTransactionConfirmations(record.getTxHash(), tokenConfig);
            
            if (currentConfirmations == null) {
                log.warn("无法获取交易确认数: txHash={}", record.getTxHash());
                return null;
            }

            // 构建轮询结果
            TransactionPollResult pollResult = new TransactionPollResult();
            pollResult.setTxHash(record.getTxHash());
            pollResult.setBlockchain(record.getBlockchain());
            pollResult.setTokenType(record.getTokenType());
            pollResult.setCurrentConfirmations(currentConfirmations);
            pollResult.setRequiredConfirmations(record.getRequiredConfirmations());
            pollResult.setStatus(record.getStatus());
            pollResult.setAmount(record.getAmount());
            pollResult.setFromAddress(record.getFromAddress());
            pollResult.setToAddress(record.getToAddress());
            pollResult.setBlockHeight(record.getBlockHeight());
            pollResult.setTransactionTime(record.getTransactionTime());
            pollResult.setProcessStatus(record.getProcessStatus());
            pollResult.setRetryCount(record.getRetryCount());
            pollResult.setLastCheckTime(new Date());

            // 检查是否达到确认要求
            if (pollResult.isConfirmationReached()) {
                pollResult.setIsConfirmed(true);
                pollResult.setConfirmedTime(new Date());
            } else {
                pollResult.setIsConfirmed(false);
            }

            // 检查是否超时
            if (pollResult.shouldTimeout(config.getTransactionTimeoutMinutes())) {
                pollResult.setIsTimeout(true);
            } else {
                pollResult.setIsTimeout(false);
            }

            return pollResult;

        } catch (Exception e) {
            log.error("检查交易确认状态失败: record={}", record, e);
            return null;
        }
    }

    /**
     * 更新交易状态
     */
    private void updateTransactionStatus(CryptoDepositRecord record, TransactionPollResult pollResult) {
        try {
            // 更新确认数
            record.setConfirmations(pollResult.getCurrentConfirmations().longValue());
            record.setUpdateTime(pollResult.getLastCheckTime());

            // 检查是否达到确认要求
            if (pollResult.isConfirmationReached()) {
                // 交易已确认
                record.setStatus(TransactionStatus.CONFIRMED.getCode());
                record.setConfirmedTime(pollResult.getConfirmedTime());
                
                // 处理用户充值
                if (processUserDeposit(record)) {
                    record.setProcessStatus(1); // 已处理
                    record.setProcessTime(new Date());
                } else {
                    record.setProcessStatus(2); // 处理失败
                    record.setFailureReason("充值处理失败");
                }
            } else if (pollResult.getIsTimeout()) {
                // 交易超时
                record.setStatus(TransactionStatus.TIMEOUT.getCode());
                record.setProcessStatus(2); // 处理失败
                record.setFailureReason("交易超时");
            } else {
                // 增加重试次数
                record.setRetryCount(record.getRetryCount() + 1);
            }

            // 更新记录
            cryptoDepositRecordMapper.updateById(record);
            
            log.debug("更新交易状态: txHash={}, confirmations={}/{}, status={}", 
                    record.getTxHash(), pollResult.getCurrentConfirmations(), 
                    record.getRequiredConfirmations(), record.getStatus());
                    
        } catch (Exception e) {
            log.error("更新交易状态失败: record={}", record, e);
        }
    }

    /**
     * 处理用户充值
     */
    private boolean processUserDeposit(CryptoDepositRecord record) {
        try {
            // 调用支付服务处理充值
            return cryptoPaymentService.processIncomingTransaction(
                    record.getTxHash(), record.getBlockchain(), record.getTokenType());
        } catch (Exception e) {
            log.error("处理用户充值失败: record={}", record, e);
            return false;
        }
    }

    /**
     * 手动检查指定交易的确认状态
     */
    public TransactionPollResult checkSpecificTransaction(String txHash) {
        try {
            CryptoDepositRecord record = cryptoDepositRecordMapper.selectOne(
                new LambdaQueryWrapper<CryptoDepositRecord>()
                    .eq(CryptoDepositRecord::getTxHash, txHash)
            );
            
            if (record == null) {
                log.warn("未找到交易记录: txHash={}", txHash);
                return null;
            }

            return checkTransactionConfirmation(record);
        } catch (Exception e) {
            log.error("检查指定交易确认状态失败: txHash={}", txHash, e);
            return null;
        }
    }

    /**
     * 重试失败的交易
     */
    public boolean retryFailedTransaction(String txHash) {
        try {
            CryptoDepositRecord record = cryptoDepositRecordMapper.selectOne(
                new LambdaQueryWrapper<CryptoDepositRecord>()
                    .eq(CryptoDepositRecord::getTxHash, txHash)
            );
            
            if (record == null) {
                log.warn("未找到交易记录: txHash={}", txHash);
                return false;
            }

            if (TransactionStatus.FAILED.getCode().equals(record.getStatus()) ||
                TransactionStatus.TIMEOUT.getCode().equals(record.getStatus())) {
                
                // 重置状态为待确认
                record.setStatus(TransactionStatus.PENDING.getCode());
                record.setProcessStatus(0);
                record.setRetryCount(0L);
                record.setFailureReason(null);
                
                cryptoDepositRecordMapper.updateById(record);
                
                log.info("重试失败交易: txHash={}", txHash);
                return true;
            }
            
            return false;
        } catch (Exception e) {
            log.error("重试失败交易失败: txHash={}", txHash, e);
            return false;
        }
    }
} 