package tv.shorthub.crypto.service.blockchain;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tv.shorthub.crypto.config.CryptoPaymentConfiguration;
import tv.shorthub.crypto.dto.TransactionInfo;
import tv.shorthub.crypto.enums.BlockchainType;
import tv.shorthub.system.domain.CryptoTokenConfig;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * Tron区块链服务实现
 */
@Slf4j
@Service
public class TronBlockchainService implements BlockchainService {

    @Autowired
    private CryptoPaymentConfiguration config;

    private final OkHttpClient httpClient;

    public TronBlockchainService() {
        this.httpClient = new OkHttpClient.Builder()
                .connectTimeout(30, TimeUnit.SECONDS)
                .readTimeout(60, TimeUnit.SECONDS)
                .writeTimeout(60, TimeUnit.SECONDS)
                .build();
    }

    @Override
    public BlockchainType getSupportedBlockchain() {
        return BlockchainType.TRON;
    }

    @Override
    public String generateWalletAddress() {
        return generateWalletAddress(null);
    }

    @Override
    public String generateWalletAddress(String privateKey) {
        try {
            org.web3j.crypto.ECKeyPair keyPair;
            
            if (privateKey != null && !privateKey.isEmpty()) {
                log.debug("处理Tron私钥: length={}, starts with 0x={}", privateKey.length(), privateKey.startsWith("0x"));
                
                // 处理十六进制私钥（BIP44生成的格式）
                String cleanPrivateKey = privateKey.startsWith("0x") ? privateKey.substring(2) : privateKey;
                try {
                    // 验证是否为有效的十六进制字符串
                    if (!cleanPrivateKey.matches("^[a-fA-F0-9]{64}$")) {
                        throw new IllegalArgumentException("Invalid hex private key format");
                    }
                    
                    BigInteger privateKeyBigInt = new BigInteger(cleanPrivateKey, 16);
                    keyPair = org.web3j.crypto.ECKeyPair.create(privateKeyBigInt);
                    log.debug("成功从十六进制私钥创建密钥对");
                } catch (Exception e) {
                    log.error("处理Tron私钥失败: {}", e.getMessage());
                    throw new RuntimeException("无效的私钥格式", e);
                }
            } else {
                // 生成新的私钥（32字节）
                byte[] privateKeyBytes = new byte[32];
                java.security.SecureRandom secureRandom = new java.security.SecureRandom();
                secureRandom.nextBytes(privateKeyBytes);
                keyPair = org.web3j.crypto.ECKeyPair.create(privateKeyBytes);
                log.debug("生成新的Tron密钥对");
            }
            
            // 获取未压缩公钥（65字节，以0x04开头）
            BigInteger publicKey = keyPair.getPublicKey();
            byte[] publicKeyBytes = publicKey.toByteArray();
            
            // 确保公钥格式正确（移除可能的符号位）
            if (publicKeyBytes.length == 65 && publicKeyBytes[0] == 0x00) {
                publicKeyBytes = Arrays.copyOfRange(publicKeyBytes, 1, publicKeyBytes.length);
            }
            
            // 移除0x04前缀（如果存在）
            byte[] publicKeyForHashing;
            if (publicKeyBytes.length == 65 && publicKeyBytes[0] == 0x04) {
                publicKeyForHashing = Arrays.copyOfRange(publicKeyBytes, 1, publicKeyBytes.length);
            } else if (publicKeyBytes.length == 64) {
                publicKeyForHashing = publicKeyBytes;
            } else {
                // 确保公钥是64字节
                publicKeyForHashing = new byte[64];
                byte[] xBytes = keyPair.getPublicKey().shiftRight(256).toByteArray();
                byte[] yBytes = keyPair.getPublicKey().and(new BigInteger("FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF", 16)).toByteArray();
                
                System.arraycopy(xBytes, Math.max(0, xBytes.length - 32), publicKeyForHashing, Math.max(0, 32 - xBytes.length), Math.min(32, xBytes.length));
                System.arraycopy(yBytes, Math.max(0, yBytes.length - 32), publicKeyForHashing, Math.max(32, 64 - yBytes.length), Math.min(32, yBytes.length));
            }
            
            // 使用Keccak-256哈希（Tron使用与以太坊相同的哈希算法）
            byte[] hash = org.web3j.crypto.Hash.sha3(publicKeyForHashing);
            
            // 取后20字节作为以太坊风格地址
            byte[] addressBytes = Arrays.copyOfRange(hash, 12, 32);
            
            // 添加Tron地址前缀0x41
            byte[] tronAddressBytes = new byte[21];
            tronAddressBytes[0] = 0x41;
            System.arraycopy(addressBytes, 0, tronAddressBytes, 1, 20);
            
            // 计算校验和（双SHA-256）
            java.security.MessageDigest sha256 = java.security.MessageDigest.getInstance("SHA-256");
            byte[] hash1 = sha256.digest(tronAddressBytes);
            byte[] hash2 = sha256.digest(hash1);
            
            // 添加校验和（前4字节）
            byte[] addressWithChecksum = new byte[25];
            System.arraycopy(tronAddressBytes, 0, addressWithChecksum, 0, 21);
            System.arraycopy(hash2, 0, addressWithChecksum, 21, 4);
            
            // Base58编码
            String address = tv.shorthub.crypto.utils.Base58Utils.encode(addressWithChecksum);
            
            log.info("生成Tron钱包地址成功: {}", address);
            return address;
            
        } catch (Exception e) {
            log.error("生成Tron钱包地址失败", e);
            throw new RuntimeException("生成Tron钱包地址失败", e);
        }
    }

    @Override
    public boolean isValidAddress(String address) {
        if (address == null || address.length() != 34) {
            return false;
        }
        return address.startsWith("T");
    }

    @Override
    public BigDecimal getBalance(String address, CryptoTokenConfig tokenConfig) {
        try {
            String rpcUrl = config.isTestnet() ? config.getTron().getTestnetRpcUrl() : config.getTron().getRpcUrl();
            
            JSONObject requestBody = new JSONObject();
            requestBody.put("contract_address", tokenConfig.getContractAddress());
            requestBody.put("owner_address", address);
            requestBody.put("visible", true);

            RequestBody body = RequestBody.create(
                    requestBody.toJSONString(),
                    MediaType.parse("application/json")
            );

            Request request = new Request.Builder()
                    .url(rpcUrl + "/v1/contracts/" + tokenConfig.getContractAddress() + "/trc20")
                    .post(body)
                    .build();

            try (Response response = httpClient.newCall(request).execute()) {
                if (response.isSuccessful() && response.body() != null) {
                    String responseBody = response.body().string();
                    JSONObject result = JSON.parseObject(responseBody);
                    
                    if (result.containsKey("balance")) {
                        String balanceStr = result.getString("balance");
                        BigInteger balance = new BigInteger(balanceStr);
                        return new BigDecimal(balance).divide(
                                BigDecimal.valueOf(Math.pow(10, tokenConfig.getDecimals()))
                        );
                    }
                }
            }
        } catch (Exception e) {
            log.error("获取Tron地址余额失败: address={}, contractAddress={}", address, tokenConfig.getContractAddress(), e);
        }
        return BigDecimal.ZERO;
    }

    /**
     * 获取Tron主币（TRX）余额
     * @param address Tron钱包地址
     * @return 主币余额（以TRX为单位）
     */
    public BigDecimal getNativeBalance(String address) {
        try {
            if (!isValidAddress(address)) {
                log.error("无效的Tron地址: {}", address);
                return BigDecimal.ZERO;
            }

            String rpcUrl = config.isTestnet() ? config.getTron().getTestnetRpcUrl() : config.getTron().getRpcUrl();
            
            // 查询主币余额
            Request request = new Request.Builder()
                    .url(rpcUrl + "/v1/accounts/" + address)
                    .get()
                    .build();

            try (Response response = httpClient.newCall(request).execute()) {
                if (response.isSuccessful() && response.body() != null) {
                    String responseBody = response.body().string();
                    JSONObject result = JSON.parseObject(responseBody);
                    
                    if (result.containsKey("balance")) {
                        Long balanceSun = result.getLong("balance");
                        // TRX有6位小数（sun）
                        return new BigDecimal(balanceSun).divide(BigDecimal.valueOf(Math.pow(10, 6)));
                    } else if (result.containsKey("Error")) {
                        log.error("获取Tron主币余额失败: {}", result.getString("Error"));
                    }
                } else {
                    log.error("获取Tron主币余额HTTP请求失败: code={}", response.code());
                }
            }
        } catch (Exception e) {
            log.error("获取Tron主币余额异常: address={}", address, e);
        }
        return BigDecimal.ZERO;
    }

    @Override
    public TransactionInfo getTransactionInfo(String txHash, CryptoTokenConfig tokenConfig) {
        try {
            String rpcUrl = config.isTestnet() ? config.getTron().getTestnetRpcUrl() : config.getTron().getRpcUrl();
            
            Request request = new Request.Builder()
                    .url(rpcUrl + "/v1/transactions/" + txHash)
                    .get()
                    .build();

            try (Response response = httpClient.newCall(request).execute()) {
                if (response.isSuccessful() && response.body() != null) {
                    String responseBody = response.body().string();
                    JSONObject result = JSON.parseObject(responseBody);
                    
                    return parseTransaction(result, tokenConfig);
                }
            }
        } catch (Exception e) {
            log.error("获取Tron交易信息失败: txHash={}", txHash, e);
        }
        return null;
    }

    @Override
    public List<TransactionInfo> getTransactionHistory(String address, CryptoTokenConfig tokenConfig, int limit, Long fromBlock) {
        List<TransactionInfo> transactions = new ArrayList<>();
        try {
            String rpcUrl = config.isTestnet() ? config.getTron().getTestnetRpcUrl() : config.getTron().getRpcUrl();
            
            String url = String.format("%s/v1/accounts/%s/transactions/trc20?limit=%d&contract_address=%s",
                    rpcUrl, address, limit, tokenConfig.getContractAddress());
            
            Request request = new Request.Builder()
                    .url(url)
                    .get()
                    .build();

            try (Response response = httpClient.newCall(request).execute()) {
                if (response.isSuccessful() && response.body() != null) {
                    String responseBody = response.body().string();
                    JSONObject result = JSON.parseObject(responseBody);
                    
                    if (result.containsKey("data")) {
                        JSONArray data = result.getJSONArray("data");
                        for (int i = 0; i < data.size(); i++) {
                            JSONObject tx = data.getJSONObject(i);
                            TransactionInfo txInfo = parseTransaction(tx, tokenConfig);
                            if (txInfo != null) {
                                transactions.add(txInfo);
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("获取Tron交易历史失败: address={}", address, e);
        }
        return transactions;
    }

    @Override
    public Long getCurrentBlockHeight() {
        try {
            String rpcUrl = config.isTestnet() ? config.getTron().getTestnetRpcUrl() : config.getTron().getRpcUrl();
            
            Request request = new Request.Builder()
                    .url(rpcUrl + "/wallet/getnowblock")
                    .post(RequestBody.create("{}", MediaType.parse("application/json")))
                    .build();

            try (Response response = httpClient.newCall(request).execute()) {
                if (response.isSuccessful() && response.body() != null) {
                    String responseBody = response.body().string();
                    JSONObject result = JSON.parseObject(responseBody);
                    
                    if (result.containsKey("block_header")) {
                        JSONObject blockHeader = result.getJSONObject("block_header");
                        if (blockHeader.containsKey("raw_data")) {
                            JSONObject rawData = blockHeader.getJSONObject("raw_data");
                            return rawData.getLong("number");
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("获取Tron当前区块高度失败", e);
        }
        return null;
    }

    @Override
    public Integer getTransactionConfirmations(String txHash, CryptoTokenConfig tokenConfig) {
        try {
            TransactionInfo txInfo = getTransactionInfo(txHash, tokenConfig);
            if (txInfo != null && txInfo.getBlockHeight() != null) {
                Long currentHeight = getCurrentBlockHeight();
                if (currentHeight != null) {
                    return (int) (currentHeight - txInfo.getBlockHeight() + 1);
                }
            }
        } catch (Exception e) {
            log.error("获取Tron交易确认数失败: txHash={}", txHash, e);
        }
        return 0;
    }

    @Override
    public List<TransactionInfo> monitorNewTransactions(String address, CryptoTokenConfig tokenConfig, Long fromBlock) {
        // 实现新交易监听逻辑
        return getTransactionHistory(address, tokenConfig, 10, fromBlock);
    }

    @Override
    public boolean isTransactionConfirmed(String txHash, int requiredConfirmations,  CryptoTokenConfig tokenConfig) {
        Integer confirmations = getTransactionConfirmations(txHash, tokenConfig);
        return confirmations != null && confirmations >= requiredConfirmations;
    }


    @Override
    public JSONObject getBlockInfo(Long blockNumber) {
        try {
            String rpcUrl = config.isTestnet() ? config.getTron().getTestnetRpcUrl() : config.getTron().getRpcUrl();
            
            // 构建RPC请求
            JSONObject requestBody = new JSONObject();
            requestBody.put("jsonrpc", "2.0");
            requestBody.put("method", "wallet/getblockbynum");
            requestBody.put("params", Arrays.asList(blockNumber));
            requestBody.put("id", 1);

            RequestBody body = RequestBody.create(
                    requestBody.toJSONString(),
                    MediaType.parse("application/json")
            );

            Request request = new Request.Builder()
                    .url(rpcUrl)
                    .post(body)
                    .build();

            try (Response response = httpClient.newCall(request).execute()) {
                if (response.isSuccessful() && response.body() != null) {
                    String responseBody = response.body().string();
                    JSONObject result = JSON.parseObject(responseBody);
                    
                    if (result.containsKey("result")) {
                        JSONObject blockInfo = result.getJSONObject("result");
                        if (blockInfo != null) {
                            log.debug("获取TRON区块信息成功: blockNumber={}", blockNumber);
                            return blockInfo;
                        } else {
                            log.warn("TRON区块不存在: blockNumber={}", blockNumber);
                            return null;
                        }
                    } else if (result.containsKey("error")) {
                        JSONObject error = result.getJSONObject("error");
                        log.error("获取TRON区块信息失败: code={}, message={}", 
                                error.getInteger("code"), error.getString("message"));
                    }
                } else {
                    log.error("获取TRON区块信息HTTP请求失败: code={}", response.code());
                }
            }
        } catch (Exception e) {
            log.error("获取TRON区块信息异常: blockNumber={}", blockNumber, e);
        }
        return null;
    }

    @Override
    public TransactionInfo parseTransaction(JSONObject tx, CryptoTokenConfig tokenConfig) {
        try {
            TransactionInfo txInfo = new TransactionInfo();
            txInfo.setTxHash(tx.getString("transaction_id"));
            txInfo.setBlockHeight(tx.getLong("block_number"));
            txInfo.setTimestamp(tx.getLong("block_timestamp"));
            txInfo.setBlockchain(BlockchainType.TRON.getCode());
            txInfo.setTokenSymbol(tokenConfig.getTokenSymbol());
            txInfo.setContractAddress(tokenConfig.getContractAddress());
            
            // 解析转账信息
            if (tx.containsKey("from")) {
                txInfo.setFromAddress(tx.getString("from"));
            }
            if (tx.containsKey("to")) {
                txInfo.setToAddress(tx.getString("to"));
            }
            if (tx.containsKey("value")) {
                String valueStr = tx.getString("value");
                BigInteger value = new BigInteger(valueStr);
                txInfo.setAmount(new BigDecimal(value).divide(
                        BigDecimal.valueOf(Math.pow(10, tokenConfig.getDecimals()))
                ));
                txInfo.setRawAmount(valueStr);
            }
            
            // 设置交易状态
            txInfo.setSuccess(true);
            txInfo.setStatus("confirmed");
            
            return txInfo;
        } catch (Exception e) {
            log.error("解析Tron交易信息失败", e);
            return null;
        }
    }


    @Override
    public List<TransactionInfo> extractTransactions(JSONObject blockContent, CryptoTokenConfig tokenConfig) {
        List<TransactionInfo> transactions = new ArrayList<>();

        try {
            // 解析区块的完整内容
            JSONArray txArray = blockContent.getJSONArray("transactions");

            for (int i = 0; i < txArray.size(); i++) {
                JSONObject tx = txArray.getJSONObject(i);

                // 检查是否为代币转账交易
                if (isTokenTransferTransaction(tx, tokenConfig)) {
                    TransactionInfo txInfo = parseTransaction(tx, tokenConfig);
                    if (txInfo != null) {
                        transactions.add(txInfo);
                    }
                }
            }
        } catch (Exception e) {
            log.error("从BASE区块提取交易失败", e);
        }

        return transactions;
    }



    /**
     * 从交易中获取合约地址
     */
    private String getContractAddressFromTransaction(JSONObject tx) {
        return tx.getString("contract_address");
    }

    /**
     * 检查是否为代币转账交易
     */
    private boolean isTokenTransferTransaction(JSONObject tx, CryptoTokenConfig tokenConfig) {
        try {
            // 检查合约地址是否匹配
            String contractAddress = getContractAddressFromTransaction(tx);
            if (!tokenConfig.getContractAddress().equalsIgnoreCase(contractAddress)) {
                return false;
            }

            // 检查交易是否成功
            return isTransactionSuccessful(tx);
        } catch (Exception e) {
            log.error("检查代币转账交易失败", e);
            return false;
        }
    }

    /**
     * 检查交易是否成功
     */
    private boolean isTransactionSuccessful(JSONObject tx) {
        return "SUCCESS".equalsIgnoreCase(tx.getString("ret"));
    }
} 