package tv.shorthub.crypto.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tv.shorthub.common.core.redis.RedisCache;
import tv.shorthub.crypto.config.CryptoPaymentConfiguration;
import tv.shorthub.crypto.constant.CryptoConstants;
import tv.shorthub.system.domain.CryptoWallet;
import tv.shorthub.crypto.enums.BlockchainType;
import tv.shorthub.system.mapper.CryptoWalletMapper;
import tv.shorthub.crypto.service.blockchain.BlockchainService;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import org.springframework.scheduling.annotation.Scheduled;

/**
 * 虚拟货币钱包管理服务实现
 */
@Slf4j
@Service
public class CryptoWalletService {

    @Autowired
    private CryptoPaymentConfiguration config;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private CryptoWalletMapper cryptoWalletMapper;

    @Autowired
    private List<BlockchainService> blockchainServices;

    // 区块链服务映射
    private Map<BlockchainType, BlockchainService> blockchainServiceMap;

    public CryptoWalletService() {
        this.blockchainServiceMap = new HashMap<>();
    }

    @Autowired
    public void initBlockchainServices(List<BlockchainService> services) {
        this.blockchainServiceMap = services.stream()
                .collect(Collectors.toMap(
                        BlockchainService::getSupportedBlockchain,
                        service -> service
                ));
    }

    @Transactional
    public CryptoWallet assignWalletToUser(String userId, String blockchain) {
        String lockKey = CryptoConstants.WALLET_LOCK_KEY + blockchain + ":" + userId;
        RLock lock = redissonClient.getLock(lockKey);
        
        try {
            if (lock.tryLock(10, 30, TimeUnit.SECONDS)) {
                // 检查用户是否已有钱包
                CryptoWallet existingWallet = getUserWallet(userId, blockchain);
                if (existingWallet != null) {
                    log.info("用户已有钱包地址: userId={}, address={}", userId, existingWallet.getAddress());
                    return existingWallet;
                }

                // 获取可用钱包
                CryptoWallet availableWallet = getAvailableWallet(blockchain);
                if (availableWallet == null) {
                    // 尝试创建新钱包
                    List<CryptoWallet> newWallets = createWallets(blockchain, 1);
                    if (!newWallets.isEmpty()) {
                        availableWallet = newWallets.get(0);
                    }
                }

                if (availableWallet != null) {
                    // 分配钱包给用户
                    availableWallet.setAssignedUserId(userId);
                    availableWallet.setStatus(1); // 已分配
                    availableWallet.setAssignedTime(new Date());
                    
                    // 保存到数据库
                    cryptoWalletMapper.updateById(availableWallet);
                    
                    // 更新缓存
                    updateWalletCache(availableWallet);
                    
                    log.info("为用户分配钱包成功: userId={}, address={}, blockchain={}", 
                            userId, availableWallet.getAddress(), blockchain);
                    
                    return availableWallet;
                } else {
                    log.error("无可用钱包地址: blockchain={}", blockchain);
                    return null;
                }
            } else {
                log.warn("获取钱包分配锁失败: userId={}, blockchain={}", userId, blockchain);
                return null;
            }
        } catch (Exception e) {
            log.error("分配钱包失败: userId={}, blockchain={}", userId, blockchain, e);
            return null;
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    @Cacheable(value = "crypto:wallet", key = "#address + ':' + #blockchain")
    public CryptoWallet findWalletByAddress(String address, String blockchain) {
        return cryptoWalletMapper.selectOne(
            new LambdaQueryWrapper<CryptoWallet>()
                .eq(CryptoWallet::getAddress, address)
                .eq(CryptoWallet::getBlockchain, blockchain)
        );
    }

    @Cacheable(value = "crypto:user-wallet", key = "#userId + ':' + #blockchain")
    public CryptoWallet getUserWallet(String userId, String blockchain) {
        return cryptoWalletMapper.selectOne(
            new LambdaQueryWrapper<CryptoWallet>()
                .eq(CryptoWallet::getAssignedUserId, userId)
                .eq(CryptoWallet::getBlockchain, blockchain)
                .eq(CryptoWallet::getStatus, 1) // 已分配状态
        );
    }

    public List<CryptoWallet> getActiveWallets() {
        try {
            return cryptoWalletMapper.selectList(
                new LambdaQueryWrapper<CryptoWallet>()
                    .eq(CryptoWallet::getStatus, 1) // 已分配状态
            );
        } catch (Exception e) {
            log.error("获取活跃钱包失败", e);
            return new ArrayList<>();
        }
    }

    @Transactional
    public boolean releaseUserWallet(String userId, String blockchain) {
        String lockKey = CryptoConstants.WALLET_LOCK_KEY + blockchain + ":" + userId;
        RLock lock = redissonClient.getLock(lockKey);
        
        try {
            if (lock.tryLock(5, 15, TimeUnit.SECONDS)) {
                CryptoWallet wallet = getUserWallet(userId, blockchain);
                if (wallet != null) {
                    wallet.setAssignedUserId(null);
                    wallet.setStatus(0); // 可用
                    wallet.setLastUsedTime(new Date());
                    
                    // 更新数据库
                    cryptoWalletMapper.updateById(wallet);
                    
                    // 清除缓存
                    clearWalletCache(wallet);
                    
                    log.info("释放用户钱包成功: userId={}, address={}", userId, wallet.getAddress());
                    return true;
                }
            }
        } catch (Exception e) {
            log.error("释放用户钱包失败: userId={}, blockchain={}", userId, blockchain, e);
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
        return false;
    }

    @Transactional
    public List<CryptoWallet> createWallets(String blockchain, int count) {
        List<CryptoWallet> wallets = new ArrayList<>();
        
        try {
            BlockchainType blockchainType = BlockchainType.fromCode(blockchain);
            BlockchainService blockchainService = blockchainServiceMap.get(blockchainType);
            
            if (blockchainService == null) {
                log.error("不支持的区块链类型: {}", blockchain);
                return wallets;
            }

            for (int i = 0; i < count; i++) {
                try {
                    // 生成完整的钱包信息
                    tv.shorthub.crypto.utils.WalletGenerator.WalletInfo walletInfo = 
                        tv.shorthub.crypto.utils.WalletGenerator.generateWallet(blockchain, blockchainService);
                    
                    CryptoWallet wallet = new CryptoWallet();
                    wallet.setVersion(walletInfo.getVersion());
                    wallet.setAddress(walletInfo.getAddress());
                    wallet.setBlockchain(blockchain);
                    wallet.setPrivateKey(walletInfo.getPrivateKey()); // 不加密
                    wallet.setMnemonic(walletInfo.getMnemonic()); // 不加密
                    wallet.setStatus(0); // 可用
                    wallet.setIsHotWallet(true);
                    wallet.setWalletTag("AUTO_GENERATED");
                    wallet.setRemark("系统自动生成");
                    
                    // 保存到数据库
                    cryptoWalletMapper.insert(wallet);
                    
                    wallets.add(wallet);
                    log.info("创建钱包成功: address={}, blockchain={}", walletInfo.getAddress(), blockchain);
                } catch (Exception e) {
                    log.error("创建钱包失败: blockchain={}, index={}", blockchain, i, e);
                }
            }
        } catch (Exception e) {
            log.error("批量创建钱包失败: blockchain={}, count={}", blockchain, count, e);
        }

        return wallets;
    }

    /**
     * 生成私钥
     */
    private String generatePrivateKey(String blockchain) {
        try {
            byte[] privateKeyBytes = new byte[32];
            java.security.SecureRandom secureRandom = new java.security.SecureRandom();
            secureRandom.nextBytes(privateKeyBytes);
            
            return org.web3j.utils.Numeric.toHexStringNoPrefix(privateKeyBytes);
        } catch (Exception e) {
            log.error("生成私钥失败", e);
            throw new RuntimeException("生成私钥失败", e);
        }
    }

    /**
     * 生成助记词
     */
    private String generateMnemonic() {
        try {
            // 生成128位熵
            byte[] entropy = new byte[16];
            java.security.SecureRandom secureRandom = new java.security.SecureRandom();
            secureRandom.nextBytes(entropy);
            
            // 简化的助记词生成（实际应使用专门的BIP39库）
            StringBuilder mnemonic = new StringBuilder();
            String[] words = {
                "abandon", "ability", "able", "about", "above", "absent", "absorb", "abstract", "absurd", "abuse",
                "access", "accident", "account", "accuse", "achieve", "acid", "acoustic", "acquire", "across", "act",
                "action", "actor", "actual", "adapt", "add", "addict", "address", "adjust", "admit", "adult",
                "advance", "advice", "aerobic", "affair", "afford", "afraid", "again", "age", "agent", "agree",
                "ahead", "aim", "air", "airport", "aisle", "alarm", "album", "alcohol", "alert", "alien",
                "all", "alley", "allow", "almost", "alone", "alpha", "already", "also", "alter", "always",
                "amateur", "amazing", "among", "amount", "amused", "analyst", "anchor", "ancient", "anger", "angle",
                "angry", "animal", "ankle", "announce", "annual", "another", "answer", "antenna", "antique", "anxiety",
                "any", "apart", "apology", "appear", "apple", "approve", "april", "arch", "arctic", "area",
                "arena", "argue", "arm", "armed", "armor", "army", "around", "arrange", "arrest", "arrive",
                "arrow", "art", "artefact", "artist", "artwork", "ask", "aspect", "assault", "asset", "assist",
                "assume", "asthma", "athlete", "atom", "attack", "attend", "attitude", "attract", "auction", "audit",
                "autumn", "average", "avocado", "avoid", "awake", "aware", "away", "awesome", "awful", "awkward",
                "axis", "baby", "bachelor", "bacon", "badge", "bag", "balance", "balcony", "ball", "bamboo",
                "banana", "banner", "bar", "barely", "bargain", "barrel", "base", "basic", "basket", "battle",
                "beach", "bean", "beauty", "because", "become", "beef", "before", "begin", "behave", "behind",
                "believe", "below", "belt", "bench", "benefit", "best", "betray", "better", "between", "beyond",
                "bicycle", "bid", "bike", "bind", "biology", "bird", "birth", "bitter", "black", "blade",
                "blame", "blanket", "blast", "bleak", "bless", "blind", "blood", "blossom", "blouse", "blue",
                "blur", "blush", "board", "boat", "body", "boil", "bomb", "bone", "bonus", "book",
                "boost", "border", "boring", "borrow", "boss", "bottom", "bounce", "box", "boy", "bracket",
                "brain", "brand", "brass", "brave", "bread", "breeze", "brick", "bridge", "brief", "bright",
                "bring", "brisk", "broccoli", "broken", "bronze", "broom", "brother", "brown", "brush", "bubble",
                "buddy", "budget", "buffalo", "build", "bulb", "bulk", "bullet", "bundle", "bunker", "burden",
                "burger", "burst", "bus", "business", "busy", "butter", "buyer", "buzz", "cabbage", "cabin",
                "cable", "cactus", "cage", "cake", "call", "calm", "camera", "camp", "can", "canal",
                "cancel", "candy", "cannon", "canoe", "canvas", "canyon", "capable", "capital", "captain", "car",
                "carbon", "card", "cargo", "carpet", "carry", "cart", "case", "cash", "casino", "castle",
                "casual", "cat", "catalog", "catch", "category", "cause", "caution", "cave", "ceiling", "celery",
                "cement", "census", "century", "cereal", "certain", "chair", "chalk", "champion", "change", "chaos",
                "chapter", "charge", "chase", "cheap", "check", "cheese", "chef", "cherry", "chest", "chicken",
                "chief", "child", "chimney", "choice", "choose", "chronic", "chuckle", "chunk", "churn", "cigar",
                "cinnamon", "circle", "citizen", "city", "civil", "claim", "clap", "clarify", "claw", "clay",
                "clean", "clerk", "clever", "click", "client", "cliff", "climb", "clinic", "clip", "clock",
                "clog", "close", "cloth", "cloud", "clown", "club", "clump", "cluster", "clutch", "coach",
                "coast", "coconut", "code", "coffee", "coil", "coin", "collect", "color", "column", "combine",
                "come", "comfort", "comic", "common", "company", "concert", "conduct", "confirm", "congress", "connect",
                "consider", "control", "convince", "cook", "cool", "copper", "copy", "coral", "core", "corn",
                "correct", "cost", "cotton", "couch", "country", "couple", "course", "cousin", "cover", "coyote",
                "crack", "cradle", "craft", "cram", "crane", "crash", "crater", "crawl", "crazy", "cream",
                "credit", "creek", "crew", "cricket", "crime", "crisp", "critic", "crop", "cross", "crouch",
                "crowd", "crucial", "cruel", "cruise", "crumble", "crunch", "crush", "cry", "crystal", "cube",
                "culture", "cup", "cupboard", "curious", "current", "curtain", "curve", "cushion", "custom", "cute",
                "cycle", "dad", "damage", "dance", "danger", "daring", "dash", "daughter", "dawn", "day",
                "deal", "debate", "debris", "decade", "december", "decide", "decline", "decorate", "decrease", "deer",
                "defense", "define", "defy", "degree", "delay", "deliver", "demand", "demise", "denial", "dentist",
                "deny", "depart", "depend", "deposit", "depth", "deputy", "derive", "describe", "desert", "design",
                "desk", "despair", "destroy", "detail", "detect", "develop", "device", "devote", "diagram", "dial",
                "diamond", "diary", "dice", "diesel", "diet", "differ", "digital", "dignity", "dilemma", "dinner",
                "dinosaur", "direct", "dirt", "disagree", "discover", "disease", "dish", "dismiss", "disorder", "display",
                "distance", "divert", "divide", "divorce", "dizzy", "doctor", "document", "dog", "doll", "dolphin",
                "domain", "donate", "donkey", "donor", "door", "dose", "double", "dove", "draft", "dragon",
                "drama", "drastic", "draw", "dream", "dress", "drift", "drill", "drink", "drip", "drive",
                "drop", "drum", "dry", "duck", "dumb", "dune", "during", "dust", "dutch", "duty",
                "dwarf", "dynamic", "eager", "eagle", "early", "earn", "earth", "easily", "east", "easy",
                "echo", "ecology", "economy", "edge", "edit", "educate", "effort", "egg", "eight", "either",
                "elbow", "elder", "electric", "elegant", "element", "elephant", "elevator", "elite", "else", "embark",
                "embody", "embrace", "emerge", "emotion", "employ", "empower", "empty", "enable", "enact", "end",
                "endless", "endorse", "enemy", "energy", "enforce", "engage", "engine", "enhance", "enjoy", "enlist",
                "enough", "enrich", "enroll", "ensure", "enter", "entire", "entry", "envelope", "episode", "equal",
                "equip", "era", "erase", "erode", "erosion", "error", "erupt", "escape", "essay", "essence",
                "estate", "eternal", "ethics", "evidence", "evil", "evoke", "evolve", "exact", "example", "excess",
                "exchange", "excite", "exclude", "excuse", "execute", "exercise", "exhaust", "exhibit", "exile", "exist",
                "exit", "exotic", "expand", "expect", "expire", "explain", "expose", "express", "extend", "extra",
                "eye", "eyebrow", "fabric", "face", "faculty", "fade", "faint", "faith", "fall", "false",
                "fame", "family", "famous", "fan", "fancy", "fantasy", "farm", "fashion", "fat", "fatal",
                "father", "fatigue", "fault", "favorite", "feature", "february", "federal", "fee", "feed", "feel",
                "female", "fence", "festival", "fetch", "fever", "few", "fiber", "fiction", "field", "figure",
                "file", "film", "filter", "final", "find", "fine", "finger", "finish", "fire", "firm",
                "first", "fiscal", "fish", "fit", "five", "fix", "flag", "flame", "flash", "flat",
                "flavor", "flee", "flight", "flip", "float", "flock", "floor", "flower", "fluid", "flush",
                "fly", "foam", "focus", "fog", "foil", "fold", "follow", "food", "foot", "force",
                "forest", "forget", "fork", "fortune", "forum", "forward", "fossil", "foster", "found", "fox",
                "fragile", "frame", "frequent", "fresh", "friend", "fringe", "frog", "front", "frost", "frown",
                "frozen", "fruit", "fuel", "fun", "funny", "furnace", "fury", "future", "gadget", "gain",
                "galaxy", "gallery", "game", "gap", "garage", "garbage", "garden", "garlic", "garment", "gas",
                "gasp", "gate", "gather", "gauge", "gaze", "general", "genius", "genre", "gentle", "genuine",
                "gesture", "ghost", "giant", "gift", "giggle", "ginger", "giraffe", "girl", "give", "glad",
                "glance", "glare", "glass", "gleam", "glee", "glide", "glimpse", "globe", "gloom", "glory",
                "glove", "glow", "glue", "goat", "goddess", "gold", "good", "goose", "gorilla", "grace",
                "grain", "grant", "grape", "grass", "gravity", "great", "green", "grid", "grief", "grit",
                "grocery", "group", "grow", "grunt", "guard", "guess", "guide", "guilt", "guitar", "gun",
                "gym", "habit", "hair", "half", "hammer", "hamster", "hand", "happy", "harbor", "hard",
                "harsh", "harvest", "hat", "have", "hawk", "hazard", "head", "health", "heart", "heavy",
                "hedgehog", "height", "hello", "helmet", "help", "hen", "hero", "hidden", "high", "hill",
                "hint", "hip", "hire", "history", "hobby", "hockey", "hold", "hole", "holiday", "hollow",
                "home", "honey", "hood", "hope", "horn", "horror", "horse", "hospital", "host", "hotel",
                "hour", "hover", "hub", "huge", "human", "humble", "humor", "hundred", "hungry", "hunt",
                "hurdle", "hurry", "hurt", "husband", "hybrid", "ice", "icon", "idea", "identify", "idle",
                "ignore", "ill", "illegal", "illness", "image", "imitate", "immense", "immune", "impact", "impose",
                "improve", "impulse", "inch", "include", "income", "increase", "index", "indicate", "indoor", "industry",
                "infant", "inflict", "inform", "inhale", "inherit", "initial", "inject", "injury", "inmate", "inner",
                "innocent", "input", "inquiry", "insane", "insect", "inside", "inspire", "install", "intact", "interest",
                "into", "invest", "invite", "involve", "iron", "island", "isolate", "issue", "item", "ivory",
                "jacket", "jaguar", "jar", "jazz", "jealous", "jeans", "jelly", "jewel", "job", "join",
                "joke", "journey", "joy", "judge", "juice", "jump", "jungle", "junior", "junk", "just",
                "kangaroo", "keen", "keep", "ketchup", "key", "kick", "kid", "kidney", "kind", "kingdom",
                "kiss", "kit", "kitchen", "kite", "kitten", "kiwi", "knee", "knife", "knock", "know",
                "lab", "label", "labor", "ladder", "lady", "lake", "lamp", "language", "laptop", "large",
                "later", "latin", "laugh", "laundry", "lava", "law", "lawn", "lawsuit", "layer", "lazy",
                "leader", "leaf", "learn", "leave", "lecture", "left", "leg", "legal", "legend", "leisure",
                "lemon", "lend", "length", "lens", "leopard", "lesson", "letter", "level", "liar", "liberty",
                "library", "license", "life", "lift", "light", "like", "limb", "limit", "link", "lion",
                "liquid", "list", "little", "live", "lizard", "load", "loan", "lobster", "local", "lock",
                "log", "logic", "lonely", "long", "loop", "lottery", "loud", "lounge", "love", "loyal",
                "lucky", "luggage", "lumber", "lunar", "lunch", "luxury", "lyrics", "machine", "mad", "magic",
                "magnet", "maid", "mail", "main", "major", "make", "mammal", "man", "manage", "mandate",
                "mango", "mansion", "manual", "maple", "marble", "march", "margin", "marine", "market", "marriage",
                "mask", "mass", "master", "match", "material", "math", "matrix", "matter", "maximum", "maze",
                "meadow", "mean", "measure", "meat", "mechanic", "medal", "media", "melody", "melt", "member",
                "memory", "mention", "menu", "mercy", "merge", "merit", "merry", "mesh", "message", "metal",
                "method", "middle", "midnight", "milk", "million", "mimic", "mind", "minimum", "minor", "minute",
                "miracle", "mirror", "misery", "miss", "mistake", "mix", "mixed", "mixture", "mobile", "model",
                "modify", "mom", "moment", "monitor", "monkey", "monster", "month", "moon", "moral", "more",
                "morning", "mosquito", "mother", "motion", "motor", "mountain", "mouse", "move", "movie", "much",
                "muffin", "mule", "multiply", "muscle", "museum", "mushroom", "music", "must", "mutual", "myself",
                "mystery", "myth", "naive", "name", "napkin", "narrow", "nasty", "nation", "nature", "near",
                "neck", "need", "negative", "neglect", "neither", "nephew", "nerve", "nest", "net", "network",
                "neutral", "never", "news", "next", "nice", "night", "noble", "nod", "noise", "nominee",
                "noodle", "normal", "north", "nose", "notable", "note", "nothing", "notice", "novel", "now",
                "nuclear", "number", "nurse", "nut", "oak", "obey", "object", "oblige", "obscure", "observe",
                "obtain", "obvious", "occur", "ocean", "october", "odor", "off", "offer", "office", "often",
                "oil", "okay", "old", "olive", "olympic", "omit", "once", "one", "onion", "online",
                "only", "open", "opera", "opinion", "oppose", "option", "orange", "orbit", "orchard", "order",
                "ordinary", "organ", "orient", "original", "orphan", "ostrich", "other", "outdoor", "outer", "output",
                "outside", "oval", "oven", "over", "own", "owner", "oxygen", "oyster", "ozone", "pact",
                "paddle", "page", "pair", "palace", "palm", "panda", "panel", "panic", "panther", "paper",
                "parade", "parent", "park", "parrot", "party", "pass", "patch", "path", "patient", "patrol",
                "pattern", "pause", "pave", "payment", "peace", "peanut", "pear", "peasant", "pelican", "pen",
                "penalty", "pencil", "people", "pepper", "perfect", "permit", "person", "pet", "phone", "photo",
                "phrase", "physical", "piano", "picnic", "picture", "piece", "pig", "pigeon", "pill", "pilot",
                "pink", "pioneer", "pipe", "pistol", "pitch", "pitcher", "pizza", "place", "planet", "plastic",
                "plate", "play", "please", "pledge", "pluck", "plug", "plunge", "poem", "poet", "point",
                "polar", "pole", "police", "pond", "pony", "pool", "poor", "popular", "portion", "position",
                "possible", "post", "potato", "pottery", "poverty", "powder", "power", "practice", "praise", "predict",
                "prefer", "prepare", "present", "pretty", "prevent", "price", "pride", "primary", "print", "priority",
                "prison", "private", "prize", "problem", "process", "produce", "profit", "program", "project", "promote",
                "proof", "property", "prosper", "protect", "proud", "provide", "public", "pudding", "pull", "pulp",
                "pulse", "pumpkin", "punch", "pupil", "puppy", "purchase", "purity", "purpose", "purse", "push",
                "put", "puzzle", "pyramid", "quality", "quantum", "quarter", "question", "quick", "quit", "quiz",
                "quote", "rabbit", "raccoon", "race", "rack", "radar", "radio", "rail", "rain", "raise",
                "rally", "ramp", "ranch", "random", "range", "rapid", "rare", "rate", "rather", "raven",
                "raw", "razor", "ready", "real", "reason", "rebel", "rebuild", "recall", "receive", "recipe",
                "record", "recycle", "reduce", "reflect", "reform", "refuse", "region", "regret", "regular", "reject",
                "relax", "release", "relief", "rely", "remain", "remember", "remind", "remove", "render", "renew",
                "rent", "reopen", "repair", "repeat", "replace", "report", "require", "rescue", "resemble", "resist",
                "resource", "response", "result", "retire", "retreat", "return", "reunion", "reveal", "review", "reward",
                "rhythm", "rib", "ribbon", "rice", "rich", "ride", "ridge", "rifle", "right", "rigid",
                "ring", "riot", "ripple", "risk", "ritual", "rival", "river", "road", "roast", "robot",
                "robust", "rocket", "romance", "roof", "rookie", "room", "rose", "rotate", "rough", "round",
                "route", "royal", "rubber", "rude", "rug", "rule", "run", "runway", "rural", "rush",
                "sad", "saddle", "sadness", "safe", "sail", "salad", "salmon", "salon", "salt", "salute",
                "same", "sample", "sand", "satisfy", "satoshi", "sauce", "sausage", "save", "say", "scale",
                "scan", "scare", "scatter", "scene", "scheme", "school", "science", "scissors", "scorpion", "scout",
                "scrap", "screen", "script", "scrub", "sea", "search", "season", "seat", "second", "secret",
                "section", "security", "seed", "seek", "segment", "select", "sell", "seminar", "senior", "sense",
                "sentence", "series", "service", "seven", "shadow", "shaft", "shallow", "share", "shed", "shell",
                "sheriff", "shield", "shift", "shine", "ship", "shiver", "shock", "shoe", "shoot", "shop",
                "shore", "short", "shoulder", "shove", "shrimp", "shrug", "shuffle", "shy", "sibling", "sick",
                "side", "siege", "sight", "sign", "silent", "silk", "silly", "silver", "similar", "simple",
                "since", "sing", "siren", "sister", "situate", "six", "size", "skate", "sketch", "ski",
                "skill", "skin", "skirt", "skull", "slab", "slam", "sleep", "slender", "slice", "slide",
                "slight", "slim", "slogan", "slot", "slow", "slush", "small", "smart", "smile", "smoke",
                "smooth", "snack", "snake", "snap", "sniff", "snow", "soap", "soccer", "social", "sock",
                "soda", "soft", "solar", "soldier", "solid", "solution", "solve", "someone", "song", "soon",
                "sorry", "sort", "soul", "sound", "soup", "source", "south", "space", "spare", "spatial",
                "spawn", "speak", "speed", "spell", "spend", "sphere", "spice", "spider", "spike", "spin",
                "spirit", "split", "spoil", "sponsor", "spoon", "sport", "spot", "spray", "spread", "spring",
                "spy", "square", "squeeze", "squirrel", "stable", "stadium", "staff", "stage", "stain", "stair",
                "stake", "stale", "stamp", "stand", "start", "state", "stay", "steak", "steel", "stem",
                "step", "stereo", "stick", "still", "sting", "stomach", "stone", "stool", "story", "stove",
                "strategy", "street", "strike", "strong", "struggle", "student", "stuff", "stumble", "style", "subject",
                "submit", "subway", "success", "such", "sudden", "suffer", "sugar", "suggest", "suit", "summer",
                "sun", "sunny", "sunset", "super", "supply", "supreme", "sure", "surface", "surge", "surprise",
                "surround", "survey", "suspect", "sustain", "swallow", "swamp", "swap", "swarm", "swear", "sweet",
                "swift", "swim", "swing", "switch", "sword", "symbol", "symptom", "syrup", "system", "table",
                "tackle", "tag", "tail", "talent", "talk", "tank", "tape", "target", "task", "taste",
                "tattoo", "taxi", "tell", "ten", "tenant", "tennis", "tent", "term", "test", "text",
                "thank", "that", "theme", "then", "theory", "there", "they", "thing", "this", "thought",
                "three", "thrive", "throw", "thumb", "thunder", "ticket", "tide", "tiger", "tilt", "timber",
                "time", "tiny", "tip", "tired", "tissue", "title", "toast", "tobacco", "today", "toddler",
                "toe", "together", "toilet", "token", "tomato", "tomorrow", "tone", "tongue", "tonight", "tool",
                "tooth", "top", "topic", "topple", "torch", "tornado", "tortoise", "toss", "total", "tourist",
                "toward", "tower", "town", "toy", "track", "trade", "traffic", "tragic", "train", "transfer",
                "trap", "trash", "travel", "tray", "treat", "tree", "trend", "trial", "tribe", "trick",
                "trigger", "trim", "trip", "trophy", "trouble", "truck", "true", "truly", "trumpet", "trust",
                "truth", "try", "tube", "tuition", "tumble", "tuna", "tunnel", "turkey", "turn", "turtle",
                "twelve", "twenty", "twice", "two", "type", "typical", "ugly", "umbrella", "unable", "unaware",
                "uncle", "uncover", "under", "undo", "unfair", "unfold", "unhappy", "uniform", "unique", "unit",
                "universe", "unknown", "unlock", "until", "unusual", "unveil", "update", "upgrade", "uphold", "upright",
                "uprising", "uproar", "upset", "urban", "urge", "usage", "use", "used", "useful", "useless",
                "usual", "utility", "vacant", "vacuum", "vague", "valid", "valley", "valve", "van", "vanish",
                "vapor", "various", "vast", "vault", "vehicle", "velvet", "vendor", "venue", "verb", "verify",
                "version", "very", "vessel", "veteran", "viable", "vibrant", "vicious", "victory", "video", "view",
                "village", "vintage", "violin", "virtual", "virus", "visa", "visit", "visual", "vital", "vivid",
                "vocal", "voice", "void", "volcano", "volume", "vote", "voyage", "wage", "wagon", "wait",
                "walk", "wall", "walnut", "want", "warfare", "warm", "warrior", "wash", "wasp", "waste",
                "water", "wave", "way", "wealth", "weapon", "wear", "weasel", "weather", "web", "wedding",
                "weed", "week", "weird", "welcome", "west", "wet", "whale", "what", "whatever", "wheat",
                "wheel", "when", "whenever", "where", "whereas", "wherever", "whether", "which", "whichever", "while",
                "whisper", "wide", "width", "wife", "wild", "will", "willing", "win", "window", "wine",
                "wing", "wink", "winner", "winter", "wire", "wisdom", "wise", "wish", "witness", "wolf",
                "woman", "wonder", "wood", "wool", "word", "work", "world", "worry", "worth", "would",
                "wound", "wrap", "wreck", "wrestle", "wrist", "write", "wrong", "yard", "year", "yellow",
                "you", "young", "youth", "zebra", "zero", "zone", "zoo"
            };
            
            // 从熵生成助记词（简化实现）
            for (int i = 0; i < 12; i++) {
                if (i > 0) mnemonic.append(" ");
                int index = (entropy[i % entropy.length] & 0xFF) % words.length;
                mnemonic.append(words[index]);
            }
            
            return mnemonic.toString();
            
        } catch (Exception e) {
            log.error("生成助记词失败", e);
            throw new RuntimeException("生成助记词失败", e);
        }
    }

    /**
     * 加密私钥
     */
    private String encryptPrivateKey(String privateKey) {
        // 这里应该使用强加密算法，暂时使用简单编码
        try {
            return java.util.Base64.getEncoder().encodeToString(privateKey.getBytes("UTF-8"));
        } catch (Exception e) {
            log.error("加密私钥失败", e);
            return privateKey;
        }
    }

    /**
     * 加密助记词
     */
    private String encryptMnemonic(String mnemonic) {
        // 这里应该使用强加密算法，暂时使用简单编码
        try {
            return java.util.Base64.getEncoder().encodeToString(mnemonic.getBytes("UTF-8"));
        } catch (Exception e) {
            log.error("加密助记词失败", e);
            return mnemonic;
        }
    }

    public int getAvailableWalletCount(String blockchain) {
        try {
            return cryptoWalletMapper.selectCount(
                new LambdaQueryWrapper<CryptoWallet>()
                    .eq(CryptoWallet::getBlockchain, blockchain)
                    .eq(CryptoWallet::getStatus, 0) // 可用状态
            ).intValue();
        } catch (Exception e) {
            log.error("获取可用钱包数量失败: blockchain={}", blockchain, e);
            return 0;
        }
    }

    public void warmupWalletPool(String blockchain) {
        try {
            int availableCount = getAvailableWalletCount(blockchain);
            int requiredCount = config.getWalletPoolSize();
            
            if (availableCount < requiredCount / 2) {
                int createCount = requiredCount - availableCount;
                log.info("开始预热钱包池: blockchain={}, 当前可用={}, 需要创建={}", 
                        blockchain, availableCount, createCount);
                
                List<CryptoWallet> newWallets = createWallets(blockchain, createCount);
                log.info("钱包池预热完成: blockchain={}, 创建数量={}", blockchain, newWallets.size());
            }
        } catch (Exception e) {
            log.error("预热钱包池失败: blockchain={}", blockchain, e);
        }
    }

    private CryptoWallet getAvailableWallet(String blockchain) {
        try {
            return cryptoWalletMapper.selectOne(
                new LambdaQueryWrapper<CryptoWallet>()
                    .eq(CryptoWallet::getBlockchain, blockchain)
                    .eq(CryptoWallet::getStatus, 0) // 可用状态
                    .last("LIMIT 1")
            );
        } catch (Exception e) {
            log.error("获取可用钱包失败: blockchain={}", blockchain, e);
            return null;
        }
    }

    private void updateWalletCache(CryptoWallet wallet) {
        try {
            // 更新地址缓存
            String addressCacheKey = "crypto:wallet:" + wallet.getAddress() + ":" + wallet.getBlockchain();
            redisCache.setCacheObject(addressCacheKey, wallet, 1, TimeUnit.HOURS);
            
            // 更新用户钱包缓存
            if (wallet.getAssignedUserId() != null) {
                String userCacheKey = "crypto:user-wallet:" + wallet.getAssignedUserId() + ":" + wallet.getBlockchain();
                redisCache.setCacheObject(userCacheKey, wallet, 1, TimeUnit.HOURS);
            }
        } catch (Exception e) {
            log.error("更新钱包缓存失败", e);
        }
    }

    private void clearWalletCache(CryptoWallet wallet) {
        try {
            // 清除地址缓存
            String addressCacheKey = "crypto:wallet:" + wallet.getAddress() + ":" + wallet.getBlockchain();
            redisCache.deleteObject(addressCacheKey);
            
            // 清除用户钱包缓存
            if (wallet.getAssignedUserId() != null) {
                String userCacheKey = "crypto:user-wallet:" + wallet.getAssignedUserId() + ":" + wallet.getBlockchain();
                redisCache.deleteObject(userCacheKey);
            }
        } catch (Exception e) {
            log.error("清除钱包缓存失败", e);
        }
    }

    /**
     * 自动释放长时间未使用的钱包
     * 每10秒执行一次
     */
    @Scheduled(cron = "0/10 * * * * ?")
    public void autoReleaseInactiveWallets() {
        try {
            // 查找超过15分钟未使用的已分配钱包
            Date inactiveTime = new Date(System.currentTimeMillis() - 15 * 60 * 1000L);
            
            List<CryptoWallet> inactiveWallets = cryptoWalletMapper.selectList(
                new LambdaQueryWrapper<CryptoWallet>()
                    .eq(CryptoWallet::getStatus, 1) // 已分配状态
                    .lt(CryptoWallet::getAssignedTime, inactiveTime)
            );

            if (inactiveWallets.isEmpty()) {
                return;
            }
            
            for (CryptoWallet wallet : inactiveWallets) {
                try {
                    wallet.setStatus(0); // 可用
                    wallet.setLastUsedTime(new Date());
                    
                    cryptoWalletMapper.updateById(wallet);
                    clearWalletCache(wallet);
                    
                    log.info("自动释放长时间未使用的钱包: address={}, userId={}", 
                            wallet.getAddress(), wallet.getAssignedUserId());
                } catch (Exception e) {
                    log.error("自动释放钱包失败: address={}", wallet.getAddress(), e);
                }
            }
            
            log.info("自动释放钱包完成，释放数量: {}", inactiveWallets.size());
        } catch (Exception e) {
            log.error("自动释放钱包失败", e);
        }
    }
} 