package tv.shorthub.crypto.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 虚拟货币支付配置
 */
@Data
@Component
public class CryptoPaymentConfiguration {

    /** 是否启用 */
    private boolean enabled = true;

    /** 是否为测试环境 */
    private boolean testnet = true;

    /** 钱包池大小 */
    private int walletPoolSize = 3;

    /** 交易监听间隔（秒） */
    private int transactionCheckInterval = 30;

    /** 交易确认检查间隔（秒） */
    private int confirmationCheckInterval = 60;

    /** 区块监听间隔（秒） */
    private int blockMonitorInterval = 30;

    /** 最大重试次数 */
    private int maxRetryCount = 3;

    /** 交易超时时间（分钟） */
    private int transactionTimeoutMinutes = 30;

    /** Tron配置 */
    private TronConfig tron = new TronConfig();

    /** Base配置 */
    private BaseConfig base = new BaseConfig();

    /** Solana配置 */
    private SolanaConfig solana = new SolanaConfig();

    @Data
    public static class TronConfig {
        private boolean enabled = true;
        private String rpcUrl = "https://api.trongrid.io";
        private String testnetRpcUrl = "https://api.shasta.trongrid.io";
        private int confirmations = 19;
        private Map<String, String> tokenContracts;
    }

    @Data
    public static class BaseConfig {
        private boolean enabled = true;
        private String rpcUrl = "https://mainnet.base.org";
//        private String testnetRpcUrl = "https://sepolia.base.org";
        private String testnetRpcUrl = "http://localhost:54875";
        private int confirmations = 12;
        private Map<String, String> tokenContracts;
    }

    @Data
    public static class SolanaConfig {
        private boolean enabled = true;
        private String rpcUrl = "https://api.mainnet-beta.solana.com";
        private String testnetRpcUrl = "https://api.testnet.solana.com";
        private int confirmations = 32;
        private Map<String, String> tokenContracts;
    }
} 