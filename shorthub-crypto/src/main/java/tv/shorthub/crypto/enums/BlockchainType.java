package tv.shorthub.crypto.enums;

import lombok.Getter;

/**
 * 区块链类型枚举
 */
@Getter
public enum BlockchainType {
    TRON("tron", "Tron Network", "TRX"),
    BASE("base", "Base Network", "ETH"),
    SOLANA("solana", "Solana Network", "SOL");

    private final String code;
    private final String name;
    private final String nativeCurrency;

    BlockchainType(String code, String name, String nativeCurrency) {
        this.code = code;
        this.name = name;
        this.nativeCurrency = nativeCurrency;
    }

    public static BlockchainType fromCode(String code) {
        for (BlockchainType type : values()) {
            if (type.code.equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown blockchain type: " + code);
    }
} 