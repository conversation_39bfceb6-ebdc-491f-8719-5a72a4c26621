package tv.shorthub.crypto.enums;

import lombok.Getter;

/**
 * 交易状态枚举
 */
@Getter
public enum TransactionStatus {
    PENDING("pending", "待确认", "Transaction is pending confirmation"),
    CONFIRMED("confirmed", "已确认", "Transaction has been confirmed"),
    FAILED("failed", "失败", "Transaction failed"),
    TIMEOUT("timeout", "超时", "Transaction timeout"),
    PROCESSING("processing", "处理中", "Transaction is being processed");

    private final String code;
    private final String desc;
    private final String description;

    TransactionStatus(String code, String desc, String description) {
        this.code = code;
        this.desc = desc;
        this.description = description;
    }

    public static TransactionStatus fromCode(String code) {
        for (TransactionStatus status : values()) {
            if (status.code.equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("Unknown transaction status: " + code);
    }
} 