package tv.shorthub.crypto.enums;

import lombok.Getter;

/**
 * 加密货币类型枚举
 */
@Getter
public enum CryptoTokenType {
    USDC("USDC", "USD Coin", 18),
    USDT("USDT", "Tether USD", 18);

    private final String symbol;
    private final String name;
    private final int decimals;

    CryptoTokenType(String symbol, String name, int decimals) {
        this.symbol = symbol;
        this.name = name;
        this.decimals = decimals;
    }

    public static CryptoTokenType fromSymbol(String symbol) {
        for (CryptoTokenType type : values()) {
            if (type.symbol.equalsIgnoreCase(symbol)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown token type: " + symbol);
    }
} 