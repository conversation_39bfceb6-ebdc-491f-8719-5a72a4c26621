package tv.shorthub.crypto.constant;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * 虚拟货币支付常量
 */
public class CryptoConstants {

    /** 缓存键前缀 */
    public static final String CACHE_KEY_PREFIX = "crypto:";
    public static final String WALLET_CACHE_KEY = CACHE_KEY_PREFIX + "wallet:";
    public static final String TRANSACTION_CACHE_KEY = CACHE_KEY_PREFIX + "transaction:";
    public static final String PENDING_TX_KEY = CACHE_KEY_PREFIX + "pending:";
    public static final String PROCESSED_TX_KEY = CACHE_KEY_PREFIX + "processed:";

    /** 锁键前缀 */
    public static final String LOCK_KEY_PREFIX = "crypto:lock:";
    public static final String WALLET_LOCK_KEY = LOCK_KEY_PREFIX + "wallet:";
    public static final String TRANSACTION_LOCK_KEY = LOCK_KEY_PREFIX + "transaction:";

    /** 默认确认数 */
    public static final int DEFAULT_CONFIRMATION_COUNT = 12;
    public static final int TRON_CONFIRMATION_COUNT = 19;
    public static final int BASE_CONFIRMATION_COUNT = 12;
    public static final int SOLANA_CONFIRMATION_COUNT = 32;

    /** 默认超时时间（分钟） */
    public static final int DEFAULT_TIMEOUT_MINUTES = 30;

    /** 最小充值金额 */
    public static final BigDecimal MIN_DEPOSIT_AMOUNT = new BigDecimal("1.00");

    /** 代币合约地址 */
    public static class TokenContracts {
        // Tron Network
        public static final Map<String, String> TRON_CONTRACTS = new HashMap<>();
        // Base Network
        public static final Map<String, String> BASE_CONTRACTS = new HashMap<>();
        // Solana Network
        public static final Map<String, String> SOLANA_CONTRACTS = new HashMap<>();

        static {
            // Tron USDT/USDC 合约地址
            TRON_CONTRACTS.put("USDT", "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t");
            TRON_CONTRACTS.put("USDC", "TEkxiTehnzSmSe2XqrBj4w32RUN966rdz8");

            // Base USDT/USDC 合约地址
            BASE_CONTRACTS.put("USDT", "0xCF3444dEbd06BDf5B697E9332D7BA34B59149181");
            BASE_CONTRACTS.put("USDC", "0xCF3444dEbd06BDf5B697E9332D7BA34B59149181");

            // Solana USDT/USDC 代币mint地址
            SOLANA_CONTRACTS.put("USDT", "Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB");
            SOLANA_CONTRACTS.put("USDC", "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v");
        }
    }

    /** RPC 端点 */
    public static class RpcEndpoints {
        // Tron
        public static final String TRON_MAINNET = "https://api.trongrid.io";
        public static final String TRON_TESTNET = "https://api.shasta.trongrid.io";

        // Base
        public static final String BASE_MAINNET = "https://mainnet.base.org";
//        public static final String BASE_TESTNET = "https://sepolia.base.org";
        public static final String BASE_TESTNET = "http://localhost:54875";

        // Solana
        public static final String SOLANA_MAINNET = "https://api.mainnet-beta.solana.com";
        public static final String SOLANA_TESTNET = "https://api.testnet.solana.com";
    }

    /** 交易状态检查间隔（秒） */
    public static final int TRANSACTION_CHECK_INTERVAL = 30;

    /** 最大重试次数 */
    public static final int MAX_RETRY_COUNT = 3;

    /** 钱包地址池大小 */
    public static final int WALLET_POOL_SIZE = 100;
} 