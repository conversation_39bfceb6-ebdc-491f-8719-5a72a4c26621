package tv.shorthub.crypto.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import tv.shorthub.common.core.controller.BaseController;
import tv.shorthub.common.core.domain.AjaxResult;
import tv.shorthub.crypto.service.blockchain.BaseBlockchainService;
import tv.shorthub.crypto.service.blockchain.SolanaBlockchainService;
import tv.shorthub.crypto.service.blockchain.TronBlockchainService;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 主币余额查询控制器
 * 提供Base、Solana、Tron主币余额查询功能
 */
@Slf4j
@RestController
@RequestMapping("/api/crypto/balance")
public class NativeBalanceController extends BaseController {

    @Autowired
    private BaseBlockchainService baseBlockchainService;

    @Autowired
    private SolanaBlockchainService solanaBlockchainService;

    @Autowired
    private TronBlockchainService tronBlockchainService;

    /**
     * 获取Base主币（ETH）余额
     * @param address Base钱包地址
     * @return 余额信息
     */
    @GetMapping("/base/{address}")
    public AjaxResult getBaseNativeBalance(@PathVariable String address) {
        try {
            if (!baseBlockchainService.isValidAddress(address)) {
                return AjaxResult.error("无效的Base地址格式");
            }

            BigDecimal balance = baseBlockchainService.getNativeBalance(address);

            Map<String, Object> result = new HashMap<>();
            result.put("address", address);
            result.put("balance", balance);
            result.put("currency", "ETH");
            result.put("blockchain", "Base");
            result.put("timestamp", System.currentTimeMillis());

            return AjaxResult.success("查询成功", result);
        } catch (Exception e) {
            log.error("获取Base主币余额失败: address={}", address, e);
            return AjaxResult.error("获取Base主币余额失败: " + e.getMessage());
        }
    }

    /**
     * 获取Solana主币（SOL）余额
     * @param address Solana钱包地址
     * @return 余额信息
     */
    @GetMapping("/solana/{address}")
    public AjaxResult getSolanaNativeBalance(@PathVariable String address) {
        try {
            if (!solanaBlockchainService.isValidAddress(address)) {
                return AjaxResult.error("无效的Solana地址格式");
            }

            BigDecimal balance = solanaBlockchainService.getNativeBalance(address);

            Map<String, Object> result = new HashMap<>();
            result.put("address", address);
            result.put("balance", balance);
            result.put("currency", "SOL");
            result.put("blockchain", "Solana");
            result.put("timestamp", System.currentTimeMillis());

            return AjaxResult.success("查询成功", result);
        } catch (Exception e) {
            log.error("获取Solana主币余额失败: address={}", address, e);
            return AjaxResult.error("获取Solana主币余额失败: " + e.getMessage());
        }
    }

    /**
     * 获取Tron主币（TRX）余额
     * @param address Tron钱包地址
     * @return 余额信息
     */
    @GetMapping("/tron/{address}")
    public AjaxResult getTronNativeBalance(@PathVariable String address) {
        try {
            if (!tronBlockchainService.isValidAddress(address)) {
                return AjaxResult.error("无效的Tron地址格式");
            }

            BigDecimal balance = tronBlockchainService.getNativeBalance(address);

            Map<String, Object> result = new HashMap<>();
            result.put("address", address);
            result.put("balance", balance);
            result.put("currency", "TRX");
            result.put("blockchain", "Tron");
            result.put("timestamp", System.currentTimeMillis());

            return AjaxResult.success("查询成功", result);
        } catch (Exception e) {
            log.error("获取Tron主币余额失败: address={}", address, e);
            return AjaxResult.error("获取Tron主币余额失败: " + e.getMessage());
        }
    }

    /**
     * 批量获取Base主币余额
     * @param addresses Base钱包地址列表
     * @return 批量余额信息
     */
    @PostMapping("/base/batch")
    public AjaxResult getBatchBaseNativeBalance(@RequestBody List<String> addresses) {
        try {
            Map<String, Object> result = new HashMap<>();
            Map<String, BigDecimal> balanceMap = new HashMap<>();

            for (String address : addresses) {
                if (baseBlockchainService.isValidAddress(address)) {
                    BigDecimal balance = baseBlockchainService.getNativeBalance(address);
                    balanceMap.put(address, balance);
                } else {
                    log.warn("跳过无效的Base地址: {}", address);
                }
            }

            result.put("balances", balanceMap);
            result.put("currency", "ETH");
            result.put("blockchain", "Base");
            result.put("timestamp", System.currentTimeMillis());

            return AjaxResult.success("批量查询成功", result);
        } catch (Exception e) {
            log.error("批量获取Base主币余额失败", e);
            return AjaxResult.error("批量获取Base主币余额失败: " + e.getMessage());
        }
    }

    /**
     * 批量获取Solana主币余额
     * @param addresses Solana钱包地址列表
     * @return 批量余额信息
     */
    @PostMapping("/solana/batch")
    public AjaxResult getBatchSolanaNativeBalance(@RequestBody List<String> addresses) {
        try {
            Map<String, Object> result = new HashMap<>();
            Map<String, BigDecimal> balanceMap = new HashMap<>();

            for (String address : addresses) {
                if (solanaBlockchainService.isValidAddress(address)) {
                    BigDecimal balance = solanaBlockchainService.getNativeBalance(address);
                    balanceMap.put(address, balance);
                } else {
                    log.warn("跳过无效的Solana地址: {}", address);
                }
            }

            result.put("balances", balanceMap);
            result.put("currency", "SOL");
            result.put("blockchain", "Solana");
            result.put("timestamp", System.currentTimeMillis());

            return AjaxResult.success("批量查询成功", result);
        } catch (Exception e) {
            log.error("批量获取Solana主币余额失败", e);
            return AjaxResult.error("批量获取Solana主币余额失败: " + e.getMessage());
        }
    }

    /**
     * 批量获取Tron主币余额
     * @param addresses Tron钱包地址列表
     * @return 批量余额信息
     */
    @PostMapping("/tron/batch")
    public AjaxResult getBatchTronNativeBalance(@RequestBody List<String> addresses) {
        try {
            Map<String, Object> result = new HashMap<>();
            Map<String, BigDecimal> balanceMap = new HashMap<>();

            for (String address : addresses) {
                if (tronBlockchainService.isValidAddress(address)) {
                    BigDecimal balance = tronBlockchainService.getNativeBalance(address);
                    balanceMap.put(address, balance);
                } else {
                    log.warn("跳过无效的Tron地址: {}", address);
                }
            }

            result.put("balances", balanceMap);
            result.put("currency", "TRX");
            result.put("blockchain", "Tron");
            result.put("timestamp", System.currentTimeMillis());

            return AjaxResult.success("批量查询成功", result);
        } catch (Exception e) {
            log.error("批量获取Tron主币余额失败", e);
            return AjaxResult.error("批量获取Tron主币余额失败: " + e.getMessage());
        }
    }

    /**
     * 检查地址是否有足够的主币余额
     * @param blockchain 区块链类型 (base/solana/tron)
     * @param address 钱包地址
     * @param requiredAmount 需要的金额
     * @return 余额检查结果
     */
    @GetMapping("/check")
    public AjaxResult checkSufficientBalance(@RequestParam String blockchain,
                                             @RequestParam String address,
                                             @RequestParam BigDecimal requiredAmount) {
        try {
            BigDecimal currentBalance = BigDecimal.ZERO;
            String currency = "";

            switch (blockchain.toLowerCase()) {
                case "base":
                    if (!baseBlockchainService.isValidAddress(address)) {
                        return AjaxResult.error("无效的Base地址格式");
                    }
                    currentBalance = baseBlockchainService.getNativeBalance(address);
                    currency = "ETH";
                    break;
                case "solana":
                    if (!solanaBlockchainService.isValidAddress(address)) {
                        return AjaxResult.error("无效的Solana地址格式");
                    }
                    currentBalance = solanaBlockchainService.getNativeBalance(address);
                    currency = "SOL";
                    break;
                case "tron":
                    if (!tronBlockchainService.isValidAddress(address)) {
                        return AjaxResult.error("无效的Tron地址格式");
                    }
                    currentBalance = tronBlockchainService.getNativeBalance(address);
                    currency = "TRX";
                    break;
                default:
                    return AjaxResult.error("不支持的区块链类型");
            }

            boolean hasSufficient = currentBalance.compareTo(requiredAmount) >= 0;

            Map<String, Object> result = new HashMap<>();
            result.put("address", address);
            result.put("blockchain", blockchain);
            result.put("currentBalance", currentBalance);
            result.put("requiredAmount", requiredAmount);
            result.put("currency", currency);
            result.put("hasSufficientBalance", hasSufficient);
            result.put("timestamp", System.currentTimeMillis());

            return AjaxResult.success("检查完成", result);
        } catch (Exception e) {
            log.error("检查余额充足性失败: blockchain={}, address={}", blockchain, address, e);
            return AjaxResult.error("检查余额充足性失败: " + e.getMessage());
        }
    }

    /**
     * 获取支持的区块链信息
     * @return 支持的区块链列表
     */
    @GetMapping("/supported")
    public AjaxResult getSupportedBlockchains() {
        Map<String, Object> result = new HashMap<>();
        result.put("blockchains", new String[]{"base", "solana", "tron"});
        result.put("currencies", new String[]{"ETH", "SOL", "TRX"});
        result.put("description", "支持Base(ETH)、Solana(SOL)、Tron(TRX)主币余额查询");

        return AjaxResult.success("获取成功", result);
    }
}