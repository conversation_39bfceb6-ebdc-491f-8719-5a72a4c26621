package tv.shorthub.crypto.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import tv.shorthub.common.core.domain.AjaxResult;
import tv.shorthub.crypto.dto.TransactionPollResult;
import tv.shorthub.crypto.service.impl.CryptoTokenConfigService;
import tv.shorthub.crypto.service.impl.CryptoTransactionPollService;
import tv.shorthub.system.domain.CryptoDepositRecord;
import tv.shorthub.system.mapper.CryptoDepositRecordMapper;

import java.util.List;

/**
 * 虚拟货币交易控制器
 * 提供交易监听和轮询相关的API接口
 */
@Slf4j
@RestController
@RequestMapping("/crypto/transaction")
public class CryptoTransactionController {

    @Autowired
    private CryptoTokenConfigService cryptoTokenConfigService;

    @Autowired
    private CryptoTransactionPollService cryptoTransactionPollService;

    @Autowired
    private CryptoDepositRecordMapper cryptoDepositRecordMapper;

    /**
     * 获取所有启用的代币配置
     */
    @GetMapping("/token-configs")
    public AjaxResult getActiveTokenConfigs() {
        try {
            var configs = cryptoTokenConfigService.getActiveTokenConfigs();
            return AjaxResult.success(configs);
        } catch (Exception e) {
            log.error("获取启用的代币配置失败", e);
            return AjaxResult.error("获取代币配置失败");
        }
    }

    /**
     * 根据区块链类型获取代币配置
     */
    @GetMapping("/token-configs/{blockchain}")
    public AjaxResult getTokenConfigsByBlockchain(@PathVariable String blockchain) {
        try {
            var configs = cryptoTokenConfigService.getTokenConfigsByBlockchain(blockchain);
            return AjaxResult.success(configs);
        } catch (Exception e) {
            log.error("根据区块链类型获取代币配置失败: blockchain={}", blockchain, e);
            return AjaxResult.error("获取代币配置失败");
        }
    }

    /**
     * 获取指定代币配置
     */
    @GetMapping("/token-config/{blockchain}/{tokenType}")
    public AjaxResult getTokenConfig(@PathVariable String blockchain, @PathVariable String tokenType) {
        try {
            var config = cryptoTokenConfigService.getTokenConfig(blockchain, tokenType);
            if (config == null) {
                return AjaxResult.error("代币配置不存在");
            }
            return AjaxResult.success(config);
        } catch (Exception e) {
            log.error("获取代币配置失败: blockchain={}, tokenType={}", blockchain, tokenType, e);
            return AjaxResult.error("获取代币配置失败");
        }
    }

    /**
     * 检查指定交易的确认状态
     */
    @GetMapping("/check/{txHash}")
    public AjaxResult checkTransactionConfirmation(@PathVariable String txHash) {
        try {
            TransactionPollResult result = cryptoTransactionPollService.checkSpecificTransaction(txHash);
            if (result == null) {
                return AjaxResult.error("未找到交易记录");
            }
            return AjaxResult.success(result);
        } catch (Exception e) {
            log.error("检查交易确认状态失败: txHash={}", txHash, e);
            return AjaxResult.error("检查交易确认状态失败");
        }
    }

    /**
     * 重试失败的交易
     */
    @PostMapping("/retry/{txHash}")
    public AjaxResult retryFailedTransaction(@PathVariable String txHash) {
        try {
            boolean success = cryptoTransactionPollService.retryFailedTransaction(txHash);
            if (success) {
                return AjaxResult.success("重试交易成功");
            } else {
                return AjaxResult.error("重试交易失败");
            }
        } catch (Exception e) {
            log.error("重试失败交易失败: txHash={}", txHash, e);
            return AjaxResult.error("重试交易失败");
        }
    }

    /**
     * 获取用户的充值记录
     */
    @GetMapping("/deposit-records/{userId}")
    public AjaxResult getUserDepositRecords(@PathVariable String userId, 
                                          @RequestParam(defaultValue = "10") int limit) {
        try {
            List<CryptoDepositRecord> records = cryptoDepositRecordMapper.selectList(
                new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<CryptoDepositRecord>()
                    .eq(CryptoDepositRecord::getUserId, userId)
                    .orderByDesc(CryptoDepositRecord::getCreateTime)
                    .last("LIMIT " + limit)
            );
            return AjaxResult.success(records);
        } catch (Exception e) {
            log.error("获取用户充值记录失败: userId={}", userId, e);
            return AjaxResult.error("获取充值记录失败");
        }
    }

    /**
     * 获取待确认的交易记录
     */
    @GetMapping("/pending-records")
    public AjaxResult getPendingRecords() {
        try {
            List<CryptoDepositRecord> records = cryptoDepositRecordMapper.selectList(
                new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<CryptoDepositRecord>()
                    .eq(CryptoDepositRecord::getStatus, "pending")
                    .orderByAsc(CryptoDepositRecord::getCreateTime)
            );
            return AjaxResult.success(records);
        } catch (Exception e) {
            log.error("获取待确认交易记录失败", e);
            return AjaxResult.error("获取待确认记录失败");
        }
    }

    /**
     * 获取代币精度
     */
    @GetMapping("/decimals/{blockchain}/{tokenType}")
    public AjaxResult getTokenDecimals(@PathVariable String blockchain, @PathVariable String tokenType) {
        try {
            Long decimals = cryptoTokenConfigService.getTokenDecimals(blockchain, tokenType);
            if (decimals == null) {
                return AjaxResult.error("代币配置不存在");
            }
            return AjaxResult.success(decimals);
        } catch (Exception e) {
            log.error("获取代币精度失败: blockchain={}, tokenType={}", blockchain, tokenType, e);
            return AjaxResult.error("获取代币精度失败");
        }
    }

    /**
     * 获取代币合约地址
     */
    @GetMapping("/contract-address/{blockchain}/{tokenType}")
    public AjaxResult getTokenContractAddress(@PathVariable String blockchain, @PathVariable String tokenType) {
        try {
            String contractAddress = cryptoTokenConfigService.getTokenContractAddress(blockchain, tokenType);
            if (contractAddress == null) {
                return AjaxResult.error("代币配置不存在");
            }
            return AjaxResult.success(contractAddress);
        } catch (Exception e) {
            log.error("获取代币合约地址失败: blockchain={}, tokenType={}", blockchain, tokenType, e);
            return AjaxResult.error("获取代币合约地址失败");
        }
    }

    /**
     * 获取最小充值金额
     */
    @GetMapping("/min-amount/{blockchain}/{tokenType}")
    public AjaxResult getMinDepositAmount(@PathVariable String blockchain, @PathVariable String tokenType) {
        try {
            var minAmount = cryptoTokenConfigService.getMinDepositAmount(blockchain, tokenType);
            if (minAmount == null) {
                return AjaxResult.error("代币配置不存在");
            }
            return AjaxResult.success(minAmount);
        } catch (Exception e) {
            log.error("获取最小充值金额失败: blockchain={}, tokenType={}", blockchain, tokenType, e);
            return AjaxResult.error("获取最小充值金额失败");
        }
    }

    /**
     * 获取最大充值金额
     */
    @GetMapping("/max-amount/{blockchain}/{tokenType}")
    public AjaxResult getMaxDepositAmount(@PathVariable String blockchain, @PathVariable String tokenType) {
        try {
            var maxAmount = cryptoTokenConfigService.getMaxDepositAmount(blockchain, tokenType);
            if (maxAmount == null) {
                return AjaxResult.error("代币配置不存在");
            }
            return AjaxResult.success(maxAmount);
        } catch (Exception e) {
            log.error("获取最大充值金额失败: blockchain={}, tokenType={}", blockchain, tokenType, e);
            return AjaxResult.error("获取最大充值金额失败");
        }
    }

    /**
     * 获取所需确认数
     */
    @GetMapping("/required-confirmations/{blockchain}/{tokenType}")
    public AjaxResult getRequiredConfirmations(@PathVariable String blockchain, @PathVariable String tokenType) {
        try {
            Long confirmations = cryptoTokenConfigService.getRequiredConfirmations(blockchain, tokenType);
            if (confirmations == null) {
                return AjaxResult.error("代币配置不存在");
            }
            return AjaxResult.success(confirmations);
        } catch (Exception e) {
            log.error("获取所需确认数失败: blockchain={}, tokenType={}", blockchain, tokenType, e);
            return AjaxResult.error("获取所需确认数失败");
        }
    }
} 