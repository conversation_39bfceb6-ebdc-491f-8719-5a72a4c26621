package tv.shorthub.crypto.controller;

import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import tv.shorthub.common.core.controller.BaseController;
import tv.shorthub.common.core.domain.AjaxResult;
import tv.shorthub.crypto.dto.CryptoDepositRequest;
import tv.shorthub.crypto.dto.CryptoDepositResponse;
import tv.shorthub.crypto.service.impl.CryptoPaymentService;
import tv.shorthub.system.domain.CryptoDepositRecord;

import java.util.List;

/**
 * 虚拟货币支付控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/crypto/payment")
@Validated
public class CryptoPaymentController extends BaseController {

    @Autowired
    private CryptoPaymentService cryptoPaymentService;

    /**
     * 创建虚拟货币充值订单
     */
    @PostMapping("/deposit/create")
    public AjaxResult createDepositOrder(@Valid @RequestBody CryptoDepositRequest request) {
        try {

            CryptoDepositResponse response = cryptoPaymentService.createDepositOrder(request);
            
            if ("success".equals(response.getStatus())) {
                return AjaxResult.success("创建充值订单成功", response);
            } else {
                return AjaxResult.error(response.getMessage());
            }
        } catch (Exception e) {
            log.error("创建虚拟货币充值订单失败", e);
            return AjaxResult.error("创建充值订单失败: " + e.getMessage());
        }
    }

    /**
     * 查询充值订单状态
     */
    @GetMapping("/deposit/query/{orderNo}")
    public AjaxResult queryDepositOrder(@PathVariable String orderNo) {
        try {
            CryptoDepositRecord record = cryptoPaymentService.queryDepositOrder(orderNo);
            if (record != null) {
                return AjaxResult.success("查询成功", record);
            } else {
                return AjaxResult.error("订单不存在");
            }
        } catch (Exception e) {
            log.error("查询充值订单失败: orderNo={}", orderNo, e);
            return AjaxResult.error("查询订单失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户充值历史
     */
    @GetMapping("/deposit/history")
    public AjaxResult getUserDepositHistory(@RequestParam String userId,
                                          @RequestParam(defaultValue = "10") int limit) {
        try {
            List<CryptoDepositRecord> records = cryptoPaymentService.getUserDepositHistory(userId, limit);
            return AjaxResult.success("查询成功", records);
        } catch (Exception e) {
            log.error("获取用户充值历史失败: userId={}", userId, e);
            return AjaxResult.error("获取充值历史失败: " + e.getMessage());
        }
    }

    /**
     * 验证交易确认数
     */
    @GetMapping("/transaction/confirmations")
    public AjaxResult verifyTransactionConfirmations(@RequestParam String txHash,
                                                   @RequestParam String blockchain) {
        try {
            Integer confirmations = cryptoPaymentService.verifyTransactionConfirmations(txHash, blockchain, null);
            if (confirmations != null) {
                return AjaxResult.success("查询成功", confirmations);
            } else {
                return AjaxResult.error("无法获取交易确认数");
            }
        } catch (Exception e) {
            log.error("验证交易确认数失败: txHash={}, blockchain={}", txHash, blockchain, e);
            return AjaxResult.error("验证交易确认数失败: " + e.getMessage());
        }
    }

    /**
     * 重新处理失败的交易（管理员功能）
     */
    @PostMapping("/transaction/retry/{recordId}")
    public AjaxResult retryFailedTransaction(@PathVariable Long recordId) {
        try {
            boolean success = cryptoPaymentService.retryFailedTransaction(recordId);
            if (success) {
                return AjaxResult.success("重试成功");
            } else {
                return AjaxResult.error("重试失败");
            }
        } catch (Exception e) {
            log.error("重试失败交易失败: recordId={}", recordId, e);
            return AjaxResult.error("重试交易失败: " + e.getMessage());
        }
    }

    /**
     * 手动处理交易（管理员功能）
     */
    @PostMapping("/transaction/process")
    public AjaxResult processTransaction(@RequestParam String txHash,
                                       @RequestParam String blockchain,
                                       @RequestParam String tokenType) {
        try {
            boolean success = cryptoPaymentService.processIncomingTransaction(txHash, blockchain, tokenType);
            if (success) {
                return AjaxResult.success("处理成功");
            } else {
                return AjaxResult.error("处理失败");
            }
        } catch (Exception e) {
            log.error("手动处理交易失败: txHash={}", txHash, e);
            return AjaxResult.error("处理交易失败: " + e.getMessage());
        }
    }

    /**
     * 获取支持的区块链和代币列表
     */
    @GetMapping("/supported")
    public AjaxResult getSupportedTokens() {
        try {
            // 返回支持的区块链和代币信息
            return AjaxResult.success("查询成功", new Object() {
                public final String[] blockchains = {"tron", "base"};
                public final String[] tokens = {"USDT", "USDC"};
                public final String minDepositAmount = "1.00";
            });
        } catch (Exception e) {
            log.error("获取支持的代币列表失败", e);
            return AjaxResult.error("获取支持信息失败: " + e.getMessage());
        }
    }
}