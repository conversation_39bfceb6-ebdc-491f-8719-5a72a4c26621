package tv.shorthub.crypto.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import tv.shorthub.common.core.domain.AjaxResult;
import tv.shorthub.crypto.service.impl.CryptoBlockMonitorService;
import tv.shorthub.system.domain.CryptoBaseBlock;
import tv.shorthub.system.domain.CryptoSolanaBlock;
import tv.shorthub.system.domain.CryptoTronBlock;

import java.util.List;

/**
 * 虚拟货币区块监听控制器
 * 提供区块监听相关的API接口
 */
@Slf4j
@RestController
@RequestMapping("/crypto/block")
public class CryptoBlockController {

    @Autowired
    private CryptoBlockMonitorService cryptoBlockMonitorService;

    /**
     * 手动触发区块监听
     */
    @PostMapping("/monitor")
    public AjaxResult monitorBlocks() {
        try {
            cryptoBlockMonitorService.monitorAllBlocks();
            return AjaxResult.success("区块监听执行成功");
        } catch (Exception e) {
            log.error("区块监听执行失败", e);
            return AjaxResult.error("区块监听执行失败");
        }
    }

    /**
     * 获取TRON区块列表
     */
    @GetMapping("/tron")
    public AjaxResult getTronBlocks(@RequestParam(defaultValue = "0") Long fromBlock,
                                   @RequestParam(defaultValue = "0") Long toBlock) {
        try {
            if (fromBlock == 0 && toBlock == 0) {
                // 获取最新的10个区块
                toBlock = Long.MAX_VALUE;
                fromBlock = Math.max(0, toBlock - 10);
            }
            
            List<CryptoTronBlock> blocks = cryptoBlockMonitorService.getTronBlocks(fromBlock, toBlock);
            return AjaxResult.success(blocks);
        } catch (Exception e) {
            log.error("获取TRON区块失败: fromBlock={}, toBlock={}", fromBlock, toBlock, e);
            return AjaxResult.error("获取TRON区块失败");
        }
    }

    /**
     * 获取BASE区块列表
     */
    @GetMapping("/base")
    public AjaxResult getBaseBlocks(@RequestParam(defaultValue = "0") Long fromBlock,
                                   @RequestParam(defaultValue = "0") Long toBlock) {
        try {
            if (fromBlock == 0 && toBlock == 0) {
                // 获取最新的10个区块
                toBlock = Long.MAX_VALUE;
                fromBlock = Math.max(0, toBlock - 10);
            }
            
            List<CryptoBaseBlock> blocks = cryptoBlockMonitorService.getBaseBlocks(fromBlock, toBlock);
            return AjaxResult.success(blocks);
        } catch (Exception e) {
            log.error("获取BASE区块失败: fromBlock={}, toBlock={}", fromBlock, toBlock, e);
            return AjaxResult.error("获取BASE区块失败");
        }
    }

    /**
     * 获取SOLANA区块列表
     */
    @GetMapping("/solana")
    public AjaxResult getSolanaBlocks(@RequestParam(defaultValue = "0") Long fromBlock,
                                     @RequestParam(defaultValue = "0") Long toBlock) {
        try {
            if (fromBlock == 0 && toBlock == 0) {
                // 获取最新的10个区块
                toBlock = Long.MAX_VALUE;
                fromBlock = Math.max(0, toBlock - 10);
            }
            
            List<CryptoSolanaBlock> blocks = cryptoBlockMonitorService.getSolanaBlocks(fromBlock, toBlock);
            return AjaxResult.success(blocks);
        } catch (Exception e) {
            log.error("获取SOLANA区块失败: fromBlock={}, toBlock={}", fromBlock, toBlock, e);
            return AjaxResult.error("获取SOLANA区块失败");
        }
    }

    /**
     * 获取指定区块链的最新区块高度
     */
    @GetMapping("/latest/{blockchain}")
    public AjaxResult getLatestBlockNumber(@PathVariable String blockchain) {
        try {
            Long latestBlock = null;
            
            switch (blockchain.toUpperCase()) {
                case "TRON":
                    latestBlock = cryptoBlockMonitorService.getLastTronBlockNumber();
                    break;
                case "BASE":
                    latestBlock = cryptoBlockMonitorService.getLastBaseBlockNumber();
                    break;
                case "SOLANA":
                    latestBlock = cryptoBlockMonitorService.getLastSolanaBlockNumber();
                    break;
                default:
                    return AjaxResult.error("不支持的区块链类型");
            }
            
            return AjaxResult.success(latestBlock);
        } catch (Exception e) {
            log.error("获取最新区块高度失败: blockchain={}", blockchain, e);
            return AjaxResult.error("获取最新区块高度失败");
        }
    }
} 