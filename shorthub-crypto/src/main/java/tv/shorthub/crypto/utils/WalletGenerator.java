package tv.shorthub.crypto.utils;

import org.apache.commons.lang3.StringUtils;
import org.web3j.crypto.MnemonicUtils;
import org.bitcoinj.crypto.MnemonicCode;
import org.bitcoinj.crypto.DeterministicKey;
import org.bitcoinj.crypto.HDKeyDerivation;
import org.bitcoinj.crypto.MnemonicCode;
import org.bitcoinj.params.MainNetParams;
import lombok.extern.slf4j.Slf4j;
import net.i2p.crypto.eddsa.EdDSAPrivateKey;
import net.i2p.crypto.eddsa.EdDSAPublicKey;
import net.i2p.crypto.eddsa.spec.EdDSANamedCurveTable;
import net.i2p.crypto.eddsa.spec.EdDSAPrivateKeySpec;
import net.i2p.crypto.eddsa.spec.EdDSAParameterSpec;
import org.bitcoinj.core.ECKey;
import org.bitcoinj.core.NetworkParameters;
import org.web3j.crypto.ECKeyPair;
import org.web3j.crypto.Keys;
import org.web3j.utils.Numeric;
import tv.shorthub.crypto.enums.BlockchainType;
import tv.shorthub.crypto.service.blockchain.BaseBlockchainService;
import tv.shorthub.crypto.service.blockchain.SolanaBlockchainService;

import java.security.SecureRandom;
import java.util.Arrays;
import java.math.BigInteger;

/**
 * 钱包生成器工具类
 * 使用标准BIP39流程：助记词 → 种子 → 私钥 → 地址
 */
@Slf4j
public class WalletGenerator {

    private static final SecureRandom SECURE_RANDOM = new SecureRandom();
    
    /**
     * 将字节数组转换为十六进制字符串（用于调试）
     */
    private static String bytesToHex(byte[] bytes) {
        StringBuilder result = new StringBuilder();
        for (byte b : bytes) {
            result.append(String.format("%02x", b));
        }
        return result.toString();
    }

    /**
     * 生成BIP39助记词
     * @return 12个单词的助记词
     */
    public static String generateMnemonic() {
        try {
            // 生成128位熵（16字节）
            byte[] entropy = new byte[16];
            SECURE_RANDOM.nextBytes(entropy);
            
            // 使用Web3j的BIP39生成助记词
            String mnemonic = MnemonicUtils.generateMnemonic(entropy);
            
            log.info("生成BIP39助记词成功");
            return mnemonic;
        } catch (Exception e) {
            log.error("生成助记词失败", e);
            throw new RuntimeException("生成助记词失败", e);
        }
    }

    /**
     * 从助记词生成种子
     * @param mnemonic 助记词
     * @return 种子字节数组
     */
    public static byte[] generateSeed(String mnemonic) {
        try {
            // 使用Web3j的BIP39从助记词生成种子
            byte[] seed = MnemonicUtils.generateSeed(mnemonic, "");
            log.info("从助记词生成种子成功");
            return seed;
        } catch (Exception e) {
            log.error("从助记词生成种子失败", e);
            throw new RuntimeException("从助记词生成种子失败", e);
        }
    }

    /**
     * 使用BIP44派生路径生成Ethereum/Base私钥
     * BIP44路径: m/44'/60'/0'/0/0 (Ethereum)
     * @param seed 种子
     * @return 私钥（十六进制字符串）
     */
    public static String generateEthereumPrivateKey(byte[] seed) {
        try {
            // 使用BIP44派生路径 m/44'/60'/0'/0/0
            DeterministicKey masterKey = HDKeyDerivation.createMasterPrivateKey(seed);
            DeterministicKey purposeKey = HDKeyDerivation.deriveChildKey(masterKey, 44 | 0x80000000); // 44'
            DeterministicKey coinKey = HDKeyDerivation.deriveChildKey(purposeKey, 60 | 0x80000000);   // 60' (Ethereum)
            DeterministicKey accountKey = HDKeyDerivation.deriveChildKey(coinKey, 0 | 0x80000000);    // 0'
            DeterministicKey changeKey = HDKeyDerivation.deriveChildKey(accountKey, 0);               // 0
            DeterministicKey addressKey = HDKeyDerivation.deriveChildKey(changeKey, 0);               // 0
            
            String privateKey = addressKey.getPrivateKeyAsHex();
            log.info("生成Ethereum私钥成功");
            return privateKey;
        } catch (Exception e) {
            log.error("生成Ethereum私钥失败", e);
            throw new RuntimeException("生成Ethereum私钥失败", e);
        }
    }

    /**
     * 使用BIP44派生路径生成Tron私钥
     * BIP44路径: m/44'/195'/0'/0/0 (Tron)
     * @param seed 种子
     * @return 私钥（十六进制字符串）
     */
    public static String generateTronPrivateKey(byte[] seed) {
        try {
            // 使用BIP44派生路径 m/44'/195'/0'/0/0
            DeterministicKey masterKey = HDKeyDerivation.createMasterPrivateKey(seed);
            DeterministicKey purposeKey = HDKeyDerivation.deriveChildKey(masterKey, 44 | 0x80000000);  // 44'
            DeterministicKey coinKey = HDKeyDerivation.deriveChildKey(purposeKey, 195 | 0x80000000);  // 195' (Tron)
            DeterministicKey accountKey = HDKeyDerivation.deriveChildKey(coinKey, 0 | 0x80000000);    // 0'
            DeterministicKey changeKey = HDKeyDerivation.deriveChildKey(accountKey, 0);               // 0
            DeterministicKey addressKey = HDKeyDerivation.deriveChildKey(changeKey, 0);               // 0
            
            String privateKey = addressKey.getPrivateKeyAsHex();
            log.info("生成Tron私钥成功");
            return privateKey;
        } catch (Exception e) {
            log.error("生成Tron私钥失败", e);
            throw new RuntimeException("生成Tron私钥失败", e);
        }
    }

    /**
     * 使用BIP44派生路径生成Solana私钥
     * BIP44路径: m/44'/501'/${index}'/0' (Phantom标准路径)
     * @param seed 种子
     * @return 私钥（Base58编码的64字节私钥）
     */
    public static String generateSolanaPrivateKey(byte[] seed) {
        return generateSolanaPrivateKey(seed, 0);
    }
    
    /**
     * 使用BIP44派生路径生成Solana私钥（支持账户索引）
     * BIP44路径: m/44'/501'/${index}'/0' (Phantom标准路径)
     * @param seed 种子
     * @param accountIndex 账户索引（通常从0开始）
     * @return 私钥（Base58编码的64字节私钥）
     */
    public static String generateSolanaPrivateKey(byte[] seed, int accountIndex) {
        try {
            // 使用Phantom兼容的BIP44派生路径 m/44'/501'/${index}'/0'
            DeterministicKey masterKey = HDKeyDerivation.createMasterPrivateKey(seed);
            DeterministicKey purposeKey = HDKeyDerivation.deriveChildKey(masterKey, 44 | 0x80000000);           // 44'
            DeterministicKey coinKey = HDKeyDerivation.deriveChildKey(purposeKey, 501 | 0x80000000);           // 501' (Solana)
            DeterministicKey accountKey = HDKeyDerivation.deriveChildKey(coinKey, accountIndex | 0x80000000);  // ${index}' (账户索引)
            DeterministicKey addressKey = HDKeyDerivation.deriveChildKey(accountKey, 0 | 0x80000000);          // 0' (变更地址)
            
            // 获取BIP44派生的32字节私钥
            byte[] bip44PrivateKey = addressKey.getPrivKeyBytes();
            
            // 关键修复：直接使用BIP44私钥作为Ed25519种子
            // 这是Phantom和其他Solana钱包的标准做法
            byte[] ed25519Seed = Arrays.copyOf(bip44PrivateKey, 32);
            
            // 调试信息
            log.debug("BIP44 Private Key (hex): {}", bytesToHex(bip44PrivateKey));
            log.debug("Ed25519 Seed (hex): {}", bytesToHex(ed25519Seed));
            
            // 生成Ed25519私钥对
            EdDSAParameterSpec spec = EdDSANamedCurveTable.getByName("Ed25519");
            EdDSAPrivateKeySpec privKeySpec = new EdDSAPrivateKeySpec(ed25519Seed, spec);
            EdDSAPrivateKey privKey = new EdDSAPrivateKey(privKeySpec);
            
            // 关键：确保我们使用正确的公钥生成方法
            EdDSAPublicKey pubKey = new EdDSAPublicKey(
                new net.i2p.crypto.eddsa.spec.EdDSAPublicKeySpec(privKey.getA(), spec));
            
            byte[] publicKeyBytes = pubKey.getAbyte();
            log.debug("Ed25519 Public Key (hex): {}", bytesToHex(publicKeyBytes));
            log.debug("Ed25519 Public Key length: {}", publicKeyBytes.length);
            
            // 组合私钥种子和公钥为64字节数组（Solana标准格式）
            byte[] fullPrivateKey = new byte[64];
            System.arraycopy(ed25519Seed, 0, fullPrivateKey, 0, 32);           // 前32字节：私钥种子
            System.arraycopy(publicKeyBytes, 0, fullPrivateKey, 32, 32);       // 后32字节：公钥
            
            String privateKey = Base58Utils.encode(fullPrivateKey);
            log.info("生成Solana私钥成功 (账户索引={}，路径=m/44'/501'/{}'/0')，长度: {}, 前10字符: {}", 
                    accountIndex, accountIndex, privateKey.length(), privateKey.substring(0, Math.min(10, privateKey.length())));
            
            // 验证生成的私钥是否为有效的Base58字符串
            try {
                byte[] decoded = Base58Utils.decode(privateKey);
                log.debug("私钥Base58验证成功，解码后长度: {}", decoded.length);
            } catch (Exception e) {
                log.error("生成的私钥Base58验证失败", e);
                throw new RuntimeException("生成的私钥无效", e);
            }
            
            return privateKey;
        } catch (Exception e) {
            log.error("生成Solana私钥失败: accountIndex={}", accountIndex, e);
            throw new RuntimeException("生成Solana私钥失败", e);
        }
    }

    /**
     * 生成完整的钱包信息
     * @param blockchain 区块链类型
     * @param blockchainService 区块链服务
     * @return 钱包信息
     */
    public static WalletInfo generateWallet(String blockchain, tv.shorthub.crypto.service.blockchain.BlockchainService blockchainService) {
        // Solana默认使用v3版本（最安全的版本）
        String defaultVersion = blockchain.toLowerCase().equals("solana") ? "v2" : null;
        return generateWallet(blockchain, blockchainService, defaultVersion);
    }

    public static void main(String[] args) {
        String testMnemonic = "autumn wink trash organ catalog enrich thing student hundred mobile face flame";
        String phantomAddress = "482BcArEm6vAFXHwUv6MGEyudhZsiEFakH4Hsfd9hJu1";
        
        System.out.println("=== Solana Address Debug ===");
        System.out.println("Mnemonic: " + testMnemonic);
        System.out.println("Expected (Phantom): " + phantomAddress);
        System.out.println();
        
        // 尝试不同的方法
        debugSolanaAddressGeneration(testMnemonic, 0);
    }
    
    /**
     * 调试Solana地址生成过程
     */
    public static void debugSolanaAddressGeneration(String mnemonic, int accountIndex) {
        try {
            System.out.println("=== Debug Solana Address Generation ===");
            System.out.println("Account Index: " + accountIndex);
            
            // 1. 生成种子
            byte[] seed = generateSeed(mnemonic);
            System.out.println("Seed length: " + seed.length);
            System.out.println("Seed (first 32 bytes hex): " + bytesToHex(Arrays.copyOf(seed, 32)));
            
            // 2. BIP44派生
            DeterministicKey masterKey = HDKeyDerivation.createMasterPrivateKey(seed);
            System.out.println("Master key created");
            
            DeterministicKey purposeKey = HDKeyDerivation.deriveChildKey(masterKey, 44 | 0x80000000);
            System.out.println("Purpose key (44') created");
            
            DeterministicKey coinKey = HDKeyDerivation.deriveChildKey(purposeKey, 501 | 0x80000000);
            System.out.println("Coin key (501') created");
            
            DeterministicKey accountKey = HDKeyDerivation.deriveChildKey(coinKey, accountIndex | 0x80000000);
            System.out.println("Account key (" + accountIndex + "') created");
            
            DeterministicKey addressKey = HDKeyDerivation.deriveChildKey(accountKey, 0 | 0x80000000);
            System.out.println("Address key (0') created");
            
            byte[] bip44PrivateKey = addressKey.getPrivKeyBytes();
            System.out.println("BIP44 Private Key length: " + bip44PrivateKey.length);
            System.out.println("BIP44 Private Key (hex): " + bytesToHex(bip44PrivateKey));
            
            // 3. 尝试方法1：直接使用BIP44私钥
            testMethod1(bip44PrivateKey, "Method 1: Direct BIP44 as Ed25519 seed");
            
            // 4. 尝试方法2：HMAC-SHA512处理
            testMethod2(bip44PrivateKey, "Method 2: HMAC-SHA512");
            
            // 5. 尝试方法3：使用seed直接派生（不用BIP44）
            testMethod3(seed, accountIndex, "Method 3: Direct seed derivation");
            
            // 6. 尝试不同的BIP44路径
            testDifferentPaths(seed, accountIndex);
            
            // 7. 尝试使用passphrase
            testWithPassphrase(mnemonic, accountIndex);
            
            // 8. 尝试不同的助记词处理方式
            testDifferentSeedGeneration(mnemonic, accountIndex);
            
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
    private static void testMethod1(byte[] bip44PrivateKey, String methodName) {
        try {
            System.out.println("\n--- " + methodName + " ---");
            
            byte[] ed25519Seed = Arrays.copyOf(bip44PrivateKey, 32);
            EdDSAParameterSpec spec = EdDSANamedCurveTable.getByName("Ed25519");
            EdDSAPrivateKeySpec privKeySpec = new EdDSAPrivateKeySpec(ed25519Seed, spec);
            EdDSAPrivateKey privKey = new EdDSAPrivateKey(privKeySpec);
            EdDSAPublicKey pubKey = new EdDSAPublicKey(
                new net.i2p.crypto.eddsa.spec.EdDSAPublicKeySpec(privKey.getA(), spec));
            
            byte[] publicKeyBytes = pubKey.getAbyte();
            String address = Base58Utils.encode(publicKeyBytes);
            
            System.out.println("Public Key (hex): " + bytesToHex(publicKeyBytes));
            System.out.println("Address: " + address);
            
        } catch (Exception e) {
            System.out.println("Method failed: " + e.getMessage());
        }
    }
    
    private static void testMethod2(byte[] bip44PrivateKey, String methodName) {
        try {
            System.out.println("\n--- " + methodName + " ---");
            
            // 使用HMAC-SHA512，这可能是一些钱包使用的方法
            javax.crypto.Mac hmac = javax.crypto.Mac.getInstance("HmacSHA512");
            javax.crypto.spec.SecretKeySpec keySpec = new javax.crypto.spec.SecretKeySpec("ed25519 seed".getBytes(), "HmacSHA512");
            hmac.init(keySpec);
            byte[] hash = hmac.doFinal(bip44PrivateKey);
            
            byte[] ed25519Seed = Arrays.copyOf(hash, 32);
            EdDSAParameterSpec spec = EdDSANamedCurveTable.getByName("Ed25519");
            EdDSAPrivateKeySpec privKeySpec = new EdDSAPrivateKeySpec(ed25519Seed, spec);
            EdDSAPrivateKey privKey = new EdDSAPrivateKey(privKeySpec);
            EdDSAPublicKey pubKey = new EdDSAPublicKey(
                new net.i2p.crypto.eddsa.spec.EdDSAPublicKeySpec(privKey.getA(), spec));
            
            byte[] publicKeyBytes = pubKey.getAbyte();
            String address = Base58Utils.encode(publicKeyBytes);
            
            System.out.println("HMAC result (first 32 hex): " + bytesToHex(ed25519Seed));
            System.out.println("Public Key (hex): " + bytesToHex(publicKeyBytes));
            System.out.println("Address: " + address);
            
        } catch (Exception e) {
            System.out.println("Method failed: " + e.getMessage());
        }
    }
    
    private static void testMethod3(byte[] seed, int accountIndex, String methodName) {
        try {
            System.out.println("\n--- " + methodName + " ---");
            
            // 直接从master seed派生，而不使用BIP44
            // 这种方法有些钱包可能使用
            byte[] indexBytes = java.nio.ByteBuffer.allocate(4).putInt(accountIndex).array();
            java.security.MessageDigest sha256 = java.security.MessageDigest.getInstance("SHA-256");
            sha256.update(seed);
            sha256.update(indexBytes);
            byte[] ed25519Seed = Arrays.copyOf(sha256.digest(), 32);
            
            EdDSAParameterSpec spec = EdDSANamedCurveTable.getByName("Ed25519");
            EdDSAPrivateKeySpec privKeySpec = new EdDSAPrivateKeySpec(ed25519Seed, spec);
            EdDSAPrivateKey privKey = new EdDSAPrivateKey(privKeySpec);
            EdDSAPublicKey pubKey = new EdDSAPublicKey(
                new net.i2p.crypto.eddsa.spec.EdDSAPublicKeySpec(privKey.getA(), spec));
            
            byte[] publicKeyBytes = pubKey.getAbyte();
            String address = Base58Utils.encode(publicKeyBytes);
            
            System.out.println("Direct seed (hex): " + bytesToHex(ed25519Seed));
            System.out.println("Public Key (hex): " + bytesToHex(publicKeyBytes));
            System.out.println("Address: " + address);
            
        } catch (Exception e) {
            System.out.println("Method failed: " + e.getMessage());
        }
    }
    
    private static void testDifferentPaths(byte[] seed, int accountIndex) {
        System.out.println("\n=== Testing Different BIP44 Paths ===");
        
        // 手动测试每个路径，确保正确性
        testManualPath(seed, "m/44'/501'/0'/0'", accountIndex);
        testManualPath(seed, "m/44'/501'/0/0", accountIndex);  
        testManualPath(seed, "m/44'/501'/0'/0/0", accountIndex);
        testManualPath(seed, "m/501'/0'/0/0", accountIndex);
        testManualPath(seed, "m/501'/0'", accountIndex);
        testManualPath(seed, "m/44'/501'", accountIndex);
        
        // 尝试一些其他可能的路径
        testManualPath(seed, "m/44'/501'/0", accountIndex);
        testManualPath(seed, "m/44'/501'/0'/0'/0", accountIndex);
    }
    
    private static void testManualPath(byte[] seed, String pathDesc, int accountIndex) {
        try {
            System.out.println("\n--- Testing Path: " + pathDesc + " (account " + accountIndex + ") ---");
            
            DeterministicKey key = HDKeyDerivation.createMasterPrivateKey(seed);
            
            if (pathDesc.equals("m/44'/501'/0'/0'")) {
                key = HDKeyDerivation.deriveChildKey(key, 44 | 0x80000000);   // 44'
                key = HDKeyDerivation.deriveChildKey(key, 501 | 0x80000000);  // 501'
                key = HDKeyDerivation.deriveChildKey(key, accountIndex | 0x80000000); // 0'
                key = HDKeyDerivation.deriveChildKey(key, 0 | 0x80000000);    // 0'
            }
            else if (pathDesc.equals("m/44'/501'/0/0")) {
                key = HDKeyDerivation.deriveChildKey(key, 44 | 0x80000000);   // 44'
                key = HDKeyDerivation.deriveChildKey(key, 501 | 0x80000000);  // 501'
                key = HDKeyDerivation.deriveChildKey(key, accountIndex);      // 0 (non-hardened)
                key = HDKeyDerivation.deriveChildKey(key, 0);                 // 0 (non-hardened)
            }
            else if (pathDesc.equals("m/44'/501'/0'/0/0")) {
                key = HDKeyDerivation.deriveChildKey(key, 44 | 0x80000000);   // 44'
                key = HDKeyDerivation.deriveChildKey(key, 501 | 0x80000000);  // 501'
                key = HDKeyDerivation.deriveChildKey(key, accountIndex | 0x80000000); // 0'
                key = HDKeyDerivation.deriveChildKey(key, 0 | 0x80000000);    // 0'
                key = HDKeyDerivation.deriveChildKey(key, 0);                 // 0 (non-hardened)
            }
            else if (pathDesc.equals("m/501'/0'/0/0")) {
                key = HDKeyDerivation.deriveChildKey(key, 501 | 0x80000000);  // 501'
                key = HDKeyDerivation.deriveChildKey(key, accountIndex | 0x80000000); // 0'
                key = HDKeyDerivation.deriveChildKey(key, 0);                 // 0
                key = HDKeyDerivation.deriveChildKey(key, 0);                 // 0
            }
            else if (pathDesc.equals("m/501'/0'")) {
                key = HDKeyDerivation.deriveChildKey(key, 501 | 0x80000000);  // 501'
                key = HDKeyDerivation.deriveChildKey(key, accountIndex | 0x80000000); // 0'
            }
            else if (pathDesc.equals("m/44'/501'")) {
                key = HDKeyDerivation.deriveChildKey(key, 44 | 0x80000000);   // 44'
                key = HDKeyDerivation.deriveChildKey(key, 501 | 0x80000000);  // 501'
            }
            else if (pathDesc.equals("m/44'/501'/0")) {
                key = HDKeyDerivation.deriveChildKey(key, 44 | 0x80000000);   // 44'
                key = HDKeyDerivation.deriveChildKey(key, 501 | 0x80000000);  // 501'
                key = HDKeyDerivation.deriveChildKey(key, accountIndex);      // 0 (non-hardened)
            }
            else if (pathDesc.equals("m/44'/501'/0'/0'/0")) {
                key = HDKeyDerivation.deriveChildKey(key, 44 | 0x80000000);   // 44'
                key = HDKeyDerivation.deriveChildKey(key, 501 | 0x80000000);  // 501'
                key = HDKeyDerivation.deriveChildKey(key, accountIndex | 0x80000000); // 0'
                key = HDKeyDerivation.deriveChildKey(key, 0 | 0x80000000);    // 0'
                key = HDKeyDerivation.deriveChildKey(key, 0 | 0x80000000);    // 0'
            }
            
            byte[] privateKey = key.getPrivKeyBytes();
            System.out.println("Private Key (hex): " + bytesToHex(privateKey));
            
            // 测试Ed25519方法
            testEd25519Method(privateKey, "Direct");
            testEd25519Method2(privateKey, "HMAC");
            
        } catch (Exception e) {
            System.out.println("Path failed: " + e.getMessage());
        }
    }
    
    private static void testWithPassphrase(String mnemonic, int accountIndex) {
        System.out.println("\n=== Testing with Passphrase ===");
        
        String[] passphrases = {"", "solana", "phantom", "wallet"};
        
        for (String passphrase : passphrases) {
            try {
                System.out.println("\n--- Passphrase: '" + passphrase + "' ---");
                
                byte[] seed = MnemonicUtils.generateSeed(mnemonic, passphrase);
                System.out.println("Seed (first 32 hex): " + bytesToHex(Arrays.copyOf(seed, 32)));
                
                // 使用标准路径测试
                DeterministicKey key = HDKeyDerivation.createMasterPrivateKey(seed);
                key = HDKeyDerivation.deriveChildKey(key, 44 | 0x80000000);
                key = HDKeyDerivation.deriveChildKey(key, 501 | 0x80000000);
                key = HDKeyDerivation.deriveChildKey(key, accountIndex | 0x80000000);
                key = HDKeyDerivation.deriveChildKey(key, 0 | 0x80000000);
                
                byte[] privateKey = key.getPrivKeyBytes();
                testEd25519Method(privateKey, "Direct");
                testEd25519Method2(privateKey, "HMAC");
                
            } catch (Exception e) {
                System.out.println("Passphrase test failed: " + e.getMessage());
            }
        }
    }
    
    private static void testDifferentSeedGeneration(String mnemonic, int accountIndex) {
        System.out.println("\n=== Testing Different Seed Generation Methods ===");
        
        try {
            // 方法1：使用BitcoinJ的MnemonicCode
            System.out.println("\n--- Method: BitcoinJ MnemonicCode ---");
            MnemonicCode mnemonicCode = MnemonicCode.INSTANCE;
            byte[] entropy = mnemonicCode.toEntropy(Arrays.asList(mnemonic.split(" ")));
            byte[] seed1 = mnemonicCode.toSeed(Arrays.asList(mnemonic.split(" ")), "");
            System.out.println("BitcoinJ Seed (first 32 hex): " + bytesToHex(Arrays.copyOf(seed1, 32)));
            testSeedWithStandardPath(seed1, accountIndex, "BitcoinJ");
            
        } catch (Exception e) {
            System.out.println("BitcoinJ method failed: " + e.getMessage());
        }
        
        try {
            // 方法2：手动PBKDF2实现
            System.out.println("\n--- Method: Manual PBKDF2 ---");
            byte[] seed2 = pbkdf2(mnemonic.getBytes("UTF-8"), ("mnemonic" + "").getBytes("UTF-8"), 2048, 64);
            System.out.println("Manual PBKDF2 Seed (first 32 hex): " + bytesToHex(Arrays.copyOf(seed2, 32)));
            testSeedWithStandardPath(seed2, accountIndex, "Manual");
            
        } catch (Exception e) {
            System.out.println("Manual PBKDF2 method failed: " + e.getMessage());
        }
    }
    
    private static void testSeedWithStandardPath(byte[] seed, int accountIndex, String methodName) {
        try {
            DeterministicKey key = HDKeyDerivation.createMasterPrivateKey(seed);
            key = HDKeyDerivation.deriveChildKey(key, 44 | 0x80000000);
            key = HDKeyDerivation.deriveChildKey(key, 501 | 0x80000000);
            key = HDKeyDerivation.deriveChildKey(key, accountIndex | 0x80000000);
            key = HDKeyDerivation.deriveChildKey(key, 0 | 0x80000000);
            
            byte[] privateKey = key.getPrivKeyBytes();
            System.out.println(methodName + " Private Key: " + bytesToHex(privateKey));
            testEd25519Method(privateKey, methodName + "-Direct");
            testEd25519Method2(privateKey, methodName + "-HMAC");
            
        } catch (Exception e) {
            System.out.println(methodName + " failed: " + e.getMessage());
        }
    }
    
    private static byte[] pbkdf2(byte[] password, byte[] salt, int iterations, int keyLength) {
        try {
            javax.crypto.spec.PBEKeySpec spec = new javax.crypto.spec.PBEKeySpec(
                new String(password, "UTF-8").toCharArray(), salt, iterations, keyLength * 8);
            javax.crypto.SecretKeyFactory factory = javax.crypto.SecretKeyFactory.getInstance("PBKDF2WithHmacSHA512");
            return factory.generateSecret(spec).getEncoded();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
    
    private static void testEd25519Method(byte[] privateKey, String suffix) {
        try {
            EdDSAParameterSpec spec = EdDSANamedCurveTable.getByName("Ed25519");
            EdDSAPrivateKeySpec privKeySpec = new EdDSAPrivateKeySpec(Arrays.copyOf(privateKey, 32), spec);
            EdDSAPrivateKey privKey = new EdDSAPrivateKey(privKeySpec);
            EdDSAPublicKey pubKey = new EdDSAPublicKey(
                new net.i2p.crypto.eddsa.spec.EdDSAPublicKeySpec(privKey.getA(), spec));
            
            byte[] publicKeyBytes = pubKey.getAbyte();
            String address = Base58Utils.encode(publicKeyBytes);
            
            System.out.println(suffix + " - Address: " + address);
            
            // 检查是否匹配Phantom
            if ("482BcArEm6vAFXHwUv6MGEyudhZsiEFakH4Hsfd9hJu1".equals(address)) {
                System.out.println("*** MATCH FOUND! ***");
            }
            
        } catch (Exception e) {
            System.out.println(suffix + " failed: " + e.getMessage());
        }
    }
    
    private static void testEd25519Method2(byte[] privateKey, String suffix) {
        try {
            javax.crypto.Mac hmac = javax.crypto.Mac.getInstance("HmacSHA512");
            javax.crypto.spec.SecretKeySpec keySpec = new javax.crypto.spec.SecretKeySpec("ed25519 seed".getBytes(), "HmacSHA512");
            hmac.init(keySpec);
            byte[] hash = hmac.doFinal(privateKey);
            
            EdDSAParameterSpec spec = EdDSANamedCurveTable.getByName("Ed25519");
            EdDSAPrivateKeySpec privKeySpec = new EdDSAPrivateKeySpec(Arrays.copyOf(hash, 32), spec);
            EdDSAPrivateKey privKey = new EdDSAPrivateKey(privKeySpec);
            EdDSAPublicKey pubKey = new EdDSAPublicKey(
                new net.i2p.crypto.eddsa.spec.EdDSAPublicKeySpec(privKey.getA(), spec));
            
            byte[] publicKeyBytes = pubKey.getAbyte();
            String address = Base58Utils.encode(publicKeyBytes);
            
            System.out.println(suffix + " - Address: " + address);
            
            // 检查是否匹配Phantom
            if ("482BcArEm6vAFXHwUv6MGEyudhZsiEFakH4Hsfd9hJu1".equals(address)) {
                System.out.println("*** MATCH FOUND! ***");
            }
            
        } catch (Exception e) {
            System.out.println(suffix + " failed: " + e.getMessage());
        }
    }
    
    public static WalletInfo generateWallet(String blockchain, tv.shorthub.crypto.service.blockchain.BlockchainService blockchainService, String version) {
        return generateWallet(blockchain, blockchainService, version, null, 0);
    }
    
    /**
     * 生成完整的钱包信息（支持账户索引，特别适用于Solana）
     * @param blockchain 区块链类型
     * @param blockchainService 区块链服务
     * @param version 钱包版本
     * @param accountIndex 账户索引（Solana使用m/44'/501'/${index}'/0'路径）
     * @return 钱包信息
     */
    public static WalletInfo generateWallet(String blockchain, tv.shorthub.crypto.service.blockchain.BlockchainService blockchainService, String version, int accountIndex) {
        return generateWallet(blockchain, blockchainService, version, null, accountIndex);
    }
    
    /**
     * 生成完整的钱包信息（支持版本）
     * @param blockchain 区块链类型
     * @param blockchainService 区块链服务
     * @param version 钱包版本
     * @return 钱包信息
     */
    public static WalletInfo generateWallet(String blockchain, tv.shorthub.crypto.service.blockchain.BlockchainService blockchainService, String version, String mnemonic) {
        return generateWallet(blockchain, blockchainService, version, mnemonic, 0);
    }
    
    /**
     * 生成完整的钱包信息（完整版本，支持所有参数）
     * @param blockchain 区块链类型
     * @param blockchainService 区块链服务
     * @param version 钱包版本
     * @param mnemonic 助记词（可选，null时自动生成）
     * @param accountIndex 账户索引（Solana专用）
     * @return 钱包信息
     */
    public static WalletInfo generateWallet(String blockchain, tv.shorthub.crypto.service.blockchain.BlockchainService blockchainService, String version, String mnemonic, int accountIndex) {
        try {
            // 1. 生成助记词
            if (StringUtils.isEmpty(mnemonic)) {
                mnemonic = generateMnemonic();
            }

            // 2. 从助记词生成种子
            byte[] seed = generateSeed(mnemonic);
            
            // 3. 根据区块链类型生成私钥
            String privateKey;
            BlockchainType blockchainType = BlockchainType.fromCode(blockchain.toLowerCase());
            
            switch (blockchainType) {
                case TRON:
                    privateKey = generateTronPrivateKey(seed);
                    break;
                case BASE:
                    privateKey = generateEthereumPrivateKey(seed);
                    break;
                case SOLANA:
                    privateKey = generateSolanaPrivateKey(seed, accountIndex);
                    break;
                default:
                    throw new RuntimeException("不支持的区块链类型: " + blockchain);
            }
            
            // 4. 从私钥生成钱包地址
            String address;
            if (blockchainType == BlockchainType.SOLANA) {
                if (StringUtils.isEmpty(version)) {
                    version = "v2";
                }
                tv.shorthub.crypto.service.blockchain.SolanaBlockchainService solanaService =
                    (tv.shorthub.crypto.service.blockchain.SolanaBlockchainService) blockchainService;
                address = solanaService.generateWalletAddress(privateKey, version);
            } else {
                address = blockchainService.generateWalletAddress(privateKey);
            }
            
            if (address == null || address.isEmpty()) {
                throw new RuntimeException("生成钱包地址失败");
            }
            
            WalletInfo walletInfo = new WalletInfo();
            walletInfo.setPrivateKey(privateKey);
            walletInfo.setMnemonic(mnemonic);
            walletInfo.setAddress(address);
            walletInfo.setBlockchain(blockchain);
            walletInfo.setVersion(version);
            
            if (blockchainType == BlockchainType.SOLANA) {
                log.info("生成Solana钱包成功: version={}, accountIndex={}, 路径=m/44'/501'/{}'/0', address={}", version, accountIndex, accountIndex, address);
            } else {
                log.info("生成钱包成功: blockchain={}, version={}, address={}", blockchain, version, address);
            }
            return walletInfo;
            
        } catch (Exception e) {
            log.error("生成钱包失败: blockchain={}, version={}", blockchain, version, e);
            throw new RuntimeException("生成钱包失败", e);
        }
    }

    /**
     * 钱包信息类
     */
    public static class WalletInfo {
        private String privateKey;
        private String mnemonic;
        private String address;
        private String blockchain;
        private String version;
        
        // Getters and Setters
        public String getPrivateKey() { return privateKey; }
        public void setPrivateKey(String privateKey) { this.privateKey = privateKey; }
        
        public String getMnemonic() { return mnemonic; }
        public void setMnemonic(String mnemonic) { this.mnemonic = mnemonic; }
        
        public String getAddress() { return address; }
        public void setAddress(String address) { this.address = address; }
        
        public String getBlockchain() { return blockchain; }
        public void setBlockchain(String blockchain) { this.blockchain = blockchain; }
        
        public String getVersion() { return version; }
        public void setVersion(String version) { this.version = version; }
    }
} 