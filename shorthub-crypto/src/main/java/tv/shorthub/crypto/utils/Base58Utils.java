package tv.shorthub.crypto.utils;

import java.math.BigInteger;

/**
 * Base58编码工具类
 */
public class Base58Utils {
    
    private static final String ALPHABET = "**********************************************************";
    private static final BigInteger BASE = BigInteger.valueOf(58);

    /**
     * Base58编码
     */
    public static String encode(byte[] input) {
        if (input.length == 0) {
            return "";
        }
        
        // 转换为BigInteger
        BigInteger value = new BigInteger(1, input);
        StringBuilder result = new StringBuilder();
        
        while (value.compareTo(BigInteger.ZERO) > 0) {
            BigInteger[] divmod = value.divideAndRemainder(BASE);
            value = divmod[0];
            result.insert(0, ALPHABET.charAt(divmod[1].intValue()));
        }
        
        // 处理前导零
        for (byte b : input) {
            if (b == 0) {
                result.insert(0, ALPHABET.charAt(0));
            } else {
                break;
            }
        }
        
        return result.toString();
    }

    /**
     * Base58解码
     */
    public static byte[] decode(String input) {
        if (input == null || input.trim().length() == 0) {
            return new byte[0];
        }
        
        // 去除空格和换行符
        input = input.trim().replaceAll("\\s+", "");
        
        BigInteger value = BigInteger.ZERO;
        for (char c : input.toCharArray()) {
            int digit = ALPHABET.indexOf(c);
            if (digit == -1) {
                throw new IllegalArgumentException("Invalid Base58 character: '" + c + "' (ASCII: " + (int)c + ") in string: " + input.substring(0, Math.min(20, input.length())));
            }
            value = value.multiply(BASE).add(BigInteger.valueOf(digit));
        }
        
        byte[] result = value.toByteArray();
        
        // 处理前导零
        int leadingZeros = 0;
        for (char c : input.toCharArray()) {
            if (c == ALPHABET.charAt(0)) {
                leadingZeros++;
            } else {
                break;
            }
        }
        
        if (leadingZeros > 0) {
            byte[] newResult = new byte[result.length + leadingZeros];
            System.arraycopy(result, 0, newResult, leadingZeros, result.length);
            return newResult;
        }
        
        return result;
    }
} 