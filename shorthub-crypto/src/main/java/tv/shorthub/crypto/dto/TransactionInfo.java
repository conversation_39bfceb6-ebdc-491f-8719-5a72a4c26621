package tv.shorthub.crypto.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 区块链交易信息
 */
@Data
public class TransactionInfo {

    /** 交易哈希 */
    private String txHash;

    /** 发送方地址 */
    private String fromAddress;

    /** 接收方地址 */
    private String toAddress;

    /** 代币合约地址 */
    private String contractAddress;

    /** 金额 */
    private BigDecimal amount;

    /** 原始金额字符串 */
    private String rawAmount;

    /** 区块高度 */
    private Long blockHeight;

    /** 确认数 */
    private Integer confirmations;

    /** 交易状态 */
    private String status;

    /** 交易时间戳 */
    private Long timestamp;

    /** Gas费用 */
    private BigDecimal gasFee;

    /** 是否成功 */
    private Boolean success;

    /** 错误信息 */
    private String errorMessage;

    /** 代币符号 */
    private String tokenSymbol;

    /** 区块链类型 */
    private String blockchain;
} 