package tv.shorthub.crypto.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 虚拟货币充值响应
 */
@Data
public class CryptoDepositResponse {

    /** 订单号 */
    private String orderNo;

    /** 充值地址 */
    private String depositAddress;

    /** 区块链类型 */
    private String blockchain;

    /** 代币类型 */
    private String tokenType;

    /** 充值金额 */
    private BigDecimal amount;

    /** 二维码内容（用于生成支付二维码） */
    private String qrCodeData;

    /** 交易有效期（分钟） */
    private Integer validityMinutes;

    /** 状态 */
    private String status;

    /** 消息 */
    private String message;

    /** 创建时间 */
    private String createTime;

    public static CryptoDepositResponse success(String orderNo, String depositAddress, 
                                              String blockchain, String tokenType, 
                                              BigDecimal amount) {
        CryptoDepositResponse response = new CryptoDepositResponse();
        response.setOrderNo(orderNo);
        response.setDepositAddress(depositAddress);
        response.setBlockchain(blockchain);
        response.setTokenType(tokenType);
        response.setAmount(amount);
        response.setStatus("success");
        response.setValidityMinutes(15);
        return response;
    }

    public static CryptoDepositResponse failure(String message) {
        CryptoDepositResponse response = new CryptoDepositResponse();
        response.setStatus("failure");
        response.setMessage(message);
        return response;
    }
} 