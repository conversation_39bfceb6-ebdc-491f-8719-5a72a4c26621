package tv.shorthub.crypto.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 交易轮询结果
 */
@Data
public class TransactionPollResult {

    /** 交易哈希 */
    private String txHash;

    /** 区块链类型 */
    private String blockchain;

    /** 代币类型 */
    private String tokenType;

    /** 当前确认数 */
    private Integer currentConfirmations;

    /** 所需确认数 */
    private Long requiredConfirmations;

    /** 交易状态 */
    private String status;

    /** 是否已确认 */
    private Boolean isConfirmed;

    /** 是否超时 */
    private Boolean isTimeout;

    /** 交易金额 */
    private BigDecimal amount;

    /** 发送方地址 */
    private String fromAddress;

    /** 接收方地址 */
    private String toAddress;

    /** 区块高度 */
    private Long blockHeight;

    /** 交易时间 */
    private Date transactionTime;

    /** 确认时间 */
    private Date confirmedTime;

    /** 处理状态 */
    private Integer processStatus;

    /** 错误信息 */
    private String errorMessage;

    /** 重试次数 */
    private Long retryCount;

    /** 最后检查时间 */
    private Date lastCheckTime;

    /**
     * 检查是否达到确认要求
     */
    public boolean isConfirmationReached() {
        return currentConfirmations != null && 
               requiredConfirmations != null && 
               currentConfirmations >= requiredConfirmations;
    }

    /**
     * 检查是否需要重试
     */
    public boolean needsRetry() {
        return retryCount != null && retryCount < 3 && !isConfirmed && !isTimeout;
    }

    /**
     * 检查是否应该标记为超时
     */
    public boolean shouldTimeout(long timeoutMinutes) {
        if (transactionTime == null) {
            return false;
        }
        long elapsedMinutes = (System.currentTimeMillis() - transactionTime.getTime()) / (1000 * 60);
        return elapsedMinutes > timeoutMinutes;
    }
} 