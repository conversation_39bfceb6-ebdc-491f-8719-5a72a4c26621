package tv.shorthub.crypto.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 虚拟货币充值请求
 */
@Data
public class CryptoDepositRequest {

    /** 用户ID */
    @NotBlank(message = "用户ID不能为空")
    private String userId;

    /** 区块链类型 */
    @NotBlank(message = "区块链类型不能为空")
    private String blockchain;

    /** 代币类型 */
    @NotBlank(message = "代币类型不能为空")
    private String tokenType;

    /** 充值金额 */
    @NotNull(message = "充值金额不能为空")
    @Positive(message = "充值金额必须大于0")
    private BigDecimal amount;

    /** 订单号（可选，如果不提供会自动生成） */
    private String orderNo;

    /** 回调URL */
    private String callbackUrl;

    /** 扩展信息 */
    private String extendInfo;

    /** 设备类型 */
    private String deviceType;

    /** 客户端IP */
    private String clientIp;
} 