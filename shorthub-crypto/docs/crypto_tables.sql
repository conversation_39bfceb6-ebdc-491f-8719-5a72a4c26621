-- 虚拟货币支付模块数据库表结构

-- 1. 虚拟货币支付配置表
DROP TABLE IF EXISTS `crypto_payment_config`;
CREATE TABLE `crypto_payment_config` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `config_name` varchar(100) NOT NULL COMMENT '配置名称',
    `blockchain` varchar(20) NOT NULL COMMENT '区块链类型(tron,base,solana)',
    `token_type` varchar(20) NOT NULL COMMENT '代币类型(USDT,USDC)',
    `contract_address` varchar(200) DEFAULT NULL COMMENT '代币合约地址',
    `rpc_url` varchar(500) NOT NULL COMMENT 'RPC节点地址',
    `enabled` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用(0-禁用,1-启用)',
    `testnet` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否为测试网(0-主网,1-测试网)',
    `confirmations` int(11) NOT NULL DEFAULT '12' COMMENT '确认数要求',
    `extend_config` json DEFAULT NULL COMMENT '扩展配置',
    `remark` varchar(500) DEFAULT NULL COMMENT '备注',
    `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_blockchain_token` (`blockchain`, `token_type`, `testnet`),
    KEY `idx_blockchain` (`blockchain`),
    KEY `idx_enabled` (`enabled`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='虚拟货币支付配置表';

-- 2. 虚拟货币钱包地址表
DROP TABLE IF EXISTS `crypto_wallet`;
CREATE TABLE `crypto_wallet` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `address` varchar(200) NOT NULL COMMENT '钱包地址',
    `blockchain` varchar(20) NOT NULL COMMENT '区块链类型(tron,base,solana)',
    `private_key` text COMMENT '私钥(加密存储)',
    `mnemonic` text COMMENT '助记词(加密存储)',
    `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '钱包状态(0-可用,1-已分配,2-已禁用)',
    `assigned_user_id` varchar(64) DEFAULT NULL COMMENT '分配给的用户ID',
    `assigned_time` datetime DEFAULT NULL COMMENT '分配时间',
    `last_used_time` datetime DEFAULT NULL COMMENT '最后使用时间',
    `wallet_tag` varchar(100) DEFAULT NULL COMMENT '钱包标签',
    `is_hot_wallet` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否为热钱包(0-冷钱包,1-热钱包)',
    `extend_data` json DEFAULT NULL COMMENT '扩展信息',
    `remark` varchar(500) DEFAULT NULL COMMENT '备注',
    `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_address_blockchain` (`address`, `blockchain`),
    KEY `idx_blockchain_status` (`blockchain`, `status`),
    KEY `idx_assigned_user` (`assigned_user_id`),
    KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='虚拟货币钱包地址表';

-- 3. 虚拟货币充值记录表
DROP TABLE IF EXISTS `crypto_deposit_record`;
CREATE TABLE `crypto_deposit_record` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `order_no` varchar(64) NOT NULL COMMENT '本地订单号',
    `user_id` varchar(64) NOT NULL COMMENT '用户ID',
    `tx_hash` varchar(200) DEFAULT NULL COMMENT '交易哈希',
    `blockchain` varchar(20) NOT NULL COMMENT '区块链类型(tron,base,solana)',
    `token_type` varchar(20) NOT NULL COMMENT '代币类型(USDT,USDC)',
    `contract_address` varchar(200) DEFAULT NULL COMMENT '代币合约地址',
    `from_address` varchar(200) DEFAULT NULL COMMENT '发送方地址',
    `to_address` varchar(200) NOT NULL COMMENT '接收方地址',
    `amount` decimal(20,6) NOT NULL COMMENT '充值金额',
    `raw_amount` varchar(100) DEFAULT NULL COMMENT '原始金额(包含小数位)',
    `status` varchar(20) NOT NULL DEFAULT 'pending' COMMENT '交易状态(pending,confirmed,failed,timeout)',
    `block_height` bigint(20) DEFAULT NULL COMMENT '区块高度',
    `confirmations` int(11) DEFAULT '0' COMMENT '当前确认数',
    `required_confirmations` int(11) NOT NULL DEFAULT '12' COMMENT '需要确认数',
    `transaction_time` datetime DEFAULT NULL COMMENT '交易时间',
    `confirmed_time` datetime DEFAULT NULL COMMENT '确认时间',
    `process_status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '处理状态(0-未处理,1-已处理,2-处理失败)',
    `process_time` datetime DEFAULT NULL COMMENT '处理时间',
    `failure_reason` varchar(500) DEFAULT NULL COMMENT '失败原因',
    `retry_count` int(11) NOT NULL DEFAULT '0' COMMENT '重试次数',
    `gas_fee` decimal(20,8) DEFAULT NULL COMMENT 'Gas费用',
    `raw_data` json DEFAULT NULL COMMENT '交易原始数据',
    `remark` varchar(500) DEFAULT NULL COMMENT '备注',
    `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_order_no` (`order_no`),
    UNIQUE KEY `uk_tx_hash` (`tx_hash`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_blockchain_token` (`blockchain`, `token_type`),
    KEY `idx_to_address` (`to_address`),
    KEY `idx_status` (`status`),
    KEY `idx_process_status` (`process_status`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='虚拟货币充值记录表';

-- 4. 虚拟货币交易监听记录表(可选，用于监听状态管理)
DROP TABLE IF EXISTS `crypto_monitor_log`;
CREATE TABLE `crypto_monitor_log` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `blockchain` varchar(20) NOT NULL COMMENT '区块链类型',
    `wallet_address` varchar(200) NOT NULL COMMENT '监听的钱包地址',
    `last_block_height` bigint(20) DEFAULT NULL COMMENT '最后监听的区块高度',
    `last_monitor_time` datetime DEFAULT NULL COMMENT '最后监听时间',
    `monitor_status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '监听状态(0-停止,1-运行)',
    `error_count` int(11) NOT NULL DEFAULT '0' COMMENT '错误次数',
    `last_error` varchar(1000) DEFAULT NULL COMMENT '最后错误信息',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_blockchain_address` (`blockchain`, `wallet_address`),
    KEY `idx_blockchain` (`blockchain`),
    KEY `idx_monitor_status` (`monitor_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='虚拟货币交易监听记录表';

-- 插入初始配置数据
INSERT INTO `crypto_payment_config` (`config_name`, `blockchain`, `token_type`, `contract_address`, `rpc_url`, `enabled`, `testnet`, `confirmations`) VALUES
('Tron USDT 主网', 'tron', 'USDT', 'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t', 'https://api.trongrid.io', 1, 0, 19),
('Tron USDC 主网', 'tron', 'USDC', 'TEkxiTehnzSmSe2XqrBj4w32RUN966rdz8', 'https://api.trongrid.io', 1, 0, 19),
('Base USDT 主网', 'base', 'USDT', '******************************************', 'https://mainnet.base.org', 1, 0, 12),
('Base USDC 主网', 'base', 'USDC', '******************************************', 'https://mainnet.base.org', 1, 0, 12),
('Solana USDT 主网', 'solana', 'USDT', 'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB', 'https://api.mainnet-beta.solana.com', 1, 0, 32),
('Solana USDC 主网', 'solana', 'USDC', 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v', 'https://api.mainnet-beta.solana.com', 1, 0, 32);

-- 更新 common.enums.OrderChannelEnums 枚举
-- 需要在 OrderChannelEnums.java 中添加: CRYPTO("crypto"), 