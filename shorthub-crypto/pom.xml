<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>shorthub</artifactId>
        <groupId>tv.shorthub</groupId>
        <version>1.0.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>shorthub-crypto</artifactId>

    <description>
        虚拟货币支付模块
    </description>

    <dependencies>
        <!-- 通用工具-->
        <dependency>
            <groupId>tv.shorthub</groupId>
            <artifactId>shorthub-common</artifactId>
        </dependency>

        <!-- 系统模块-->
        <dependency>
            <groupId>tv.shorthub</groupId>
            <artifactId>shorthub-system</artifactId>
        </dependency>

        <!-- SpringBoot Web容器 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <!-- SpringBoot 拦截器 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>

        <!-- SpringBoot 测试 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- Apache HttpClient 5.x -->
        <dependency>
            <groupId>org.apache.httpcomponents.client5</groupId>
            <artifactId>httpclient5</artifactId>
            <version>5.2.1</version>
        </dependency>

        <!-- Web3j for Ethereum-based chains -->
        <dependency>
            <groupId>org.web3j</groupId>
            <artifactId>core</artifactId>
            <version>4.9.8</version>
        </dependency>

        <!-- OkHttp for HTTP requests -->
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
            <version>4.11.0</version>
        </dependency>

        <!-- Bouncycastle for crypto operations -->
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcprov-jdk15on</artifactId>
            <version>1.70</version>
        </dependency>

        <!-- Base58 encoding -->
        <dependency>
            <groupId>io.github.novacrypto</groupId>
            <artifactId>Base58</artifactId>
            <version>2022.01.17</version>
        </dependency>

        <!-- BitcoinJ for Bitcoin-like address generation -->
        <dependency>
            <groupId>org.bitcoinj</groupId>
            <artifactId>bitcoinj-core</artifactId>
            <version>0.16.2</version>
        </dependency>

        <!-- Redis -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>

        <!-- Redisson for distributed locks -->
        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson</artifactId>
        </dependency>

        <!-- JSON processing -->
        <dependency>
            <groupId>com.alibaba.fastjson2</groupId>
            <artifactId>fastjson2</artifactId>
        </dependency>

        <!-- Caffeine cache -->
        <dependency>
            <groupId>com.github.ben-manes.caffeine</groupId>
            <artifactId>caffeine</artifactId>
        </dependency>

        <!-- Web3j Crypto includes BIP39 support -->
        <dependency>
            <groupId>org.web3j</groupId>
            <artifactId>crypto</artifactId>
            <version>4.9.8</version>
        </dependency>

        <!-- Ed25519 for Solana -->
        <dependency>
            <groupId>net.i2p.crypto</groupId>
            <artifactId>eddsa</artifactId>
            <version>0.3.0</version>
        </dependency>

        <!-- Alternative BIP39 implementation -->
        <dependency>
            <groupId>org.bitcoinj</groupId>
            <artifactId>bitcoinj-core</artifactId>
            <version>0.16.2</version>
        </dependency>

        <!-- HD Wallet support -->
        <dependency>
            <groupId>org.web3j</groupId>
            <artifactId>utils</artifactId>
            <version>4.9.8</version>
        </dependency>
        
        <!-- Curve25519 for better Ed25519 support -->
        <dependency>
            <groupId>org.whispersystems</groupId>
            <artifactId>curve25519-java</artifactId>
            <version>0.5.0</version>
        </dependency>
    </dependencies>

</project> 