# 本地开发规范

## 数据层规范

### MyBatis-Plus 通用Mapper
```java
public interface CommonMapper<T> extends BaseMapper<T> {
    // 获取汇总数据
    T getSummary(T entity);
    
    // 汇总查询列表
    List<T> summary(SummaryRequest request);
    
    // 全量汇总
    T allSummary(SummaryRequest request);
    
    // 批量插入或更新
    int batchInsertOrUpdate(List<T> list);
}
```

### 开发要点
- **使用 MyBatis-Plus**：基于 BaseMapper 扩展通用方法
- **拦截器处理**：CommonMapper 方法有专门拦截器，XML 无需手动实现
- **无 SQL 脚本**：避免编写复杂 .sql 文件，使用 MyBatis-Plus 注解和方法
- **高可用原则**：代码可重复利用，避免冗长实现

## 编码规范
- 优先使用现有公共组件和工具类
- 保持代码简洁，避免重复造轮子
- 遵循框架设计模式和最佳实践
