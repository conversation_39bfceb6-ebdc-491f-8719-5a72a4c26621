package tv.shorthub.common.dto;

import lombok.Data;

@Data
public class ContentInfo {
    private String contentId;
    /** 剧目 */
    private String title;

    private String description;

    /** 封面 */
    private String image;

    /** 总集数 */
    private Long total;

    /** 第几集开始收费 */
    private Long feeBegin;

    /** 每集多少金币 */
    private Long feeCoin;

    /** 最后播放到第几集 */
    private Long lastWatchNumber;

    // 是否已收藏
    private Boolean collect;

    // 是否已启用
    private Boolean enabled;
}
