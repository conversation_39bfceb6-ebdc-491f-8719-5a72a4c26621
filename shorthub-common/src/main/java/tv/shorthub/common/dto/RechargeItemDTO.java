package tv.shorthub.common.dto;

import lombok.Data;
import tv.shorthub.common.annotation.Excel;

import java.math.BigDecimal;

@Data
public class RechargeItemDTO {

    /** 模板ID */
    @Excel(name = "模板ID")
    private String itemId;


    /** 父类模板ID */
    private String parentItemId;

    /** 金额, 统一美金计价 */
    @Excel(name = "金额, 统一美金计价")
    private BigDecimal price;

    /** 首期优惠金额, 统一美金计价 */
    @Excel(name = "首期优惠金额, 统一美金计价")
    private BigDecimal firstPrice;

    /** 金币/天数 */
    @Excel(name = "金币/天数")
    private Long number;

    /** 赠送金币/天数 */
    @Excel(name = "赠送金币/天数")
    private Long bonusNumber;

    /** 金币/天数 */
    @Excel(name = "赠送比例")
    private Long bonusPercent;

    /** 解锁的番剧数量 */
    @Excel(name = "解锁的番剧数量")
    private Long unlockDramas;

    /** 0是会员，1是金币充值类型 */
    @Excel(name = "0是会员，1是金币充值类型")
    private Long type;

    /** 订阅周期 */
    @Excel(name = "订阅周期")
    private String subscriptionPeriod;

    /** 展示顺序 */
    @Excel(name = "展示顺序")
    private Long sort;

    private Boolean hot;

    private Boolean compute;
    private Boolean active;

    // 是否必须登录
    private Boolean login;

    // 谷歌支付订阅ID
    private String subscriptionId;

    // 谷歌支付产品ID
    private String productId;
}
