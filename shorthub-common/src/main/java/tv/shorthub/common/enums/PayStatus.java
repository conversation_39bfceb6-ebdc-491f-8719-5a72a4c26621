package tv.shorthub.common.enums;

public enum PayStatus {

    WAIT_PAY(0, "待支付"),
    PAYED(1, "已支付"),
    REFUND(2, "退款中"),
    REFUNDED(3, "已退款"),
    REFUND_ERR(4, "退款异常"),
    REFUND_NONE(5, "未知"),
    PROCESSING(6, "处理中"),
    TIMEOUT(7, "超时未支付"),
    FAIL(8, "支付失败"),
    ;

    private final Integer value;
    private final String desc;

    PayStatus(Integer value, String desc)
    {
        this.value = value;
        this.desc = desc;
    }

    public Integer getValue()
    {
        return value;
    }
    public String getDesc()
    {
        return desc;
    }

    public static PayStatus get(Integer value) {
        PayStatus r = null;
        for (PayStatus v: values()) {
            if (v.getValue().compareTo(value) == 0) {
                r = v;
                break;
            }
        }
        return r;
    }
}
