package tv.shorthub.common.enums;

public enum InputStateEnum {
    WAIT_STATE("-1", "等待状态", -1),
    IMPUTING("0", "导入中", 0),
    INPUT("1", "导入完成", 1),
    ;

    private final String value;
    private final String desc;
    private final Integer state;

    InputStateEnum(String value, String desc, Integer state)
    {
        this.value = value;
        this.desc = desc;
        this.state = state;
    }

    public String getValue()
    {
        return value;
    }
    public String getDesc()
    {
        return desc;
    }

    public Integer getState() {
        return state;
    }

    public static InputStateEnum ofValue(String value) {
        InputStateEnum[] values = values();
        for (InputStateEnum stateEnum : values) {
            if (stateEnum.value.equals(value)) {
                return stateEnum;
            }
        }
        return null;
    }
}
