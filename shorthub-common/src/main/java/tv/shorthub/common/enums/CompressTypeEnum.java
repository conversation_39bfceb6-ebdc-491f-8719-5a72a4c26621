package tv.shorthub.common.enums;

import lombok.Getter;

@Getter
public enum CompressTypeEnum {
    ORIGINAL("original", "原始视频"),
    COMPRESSED("compressed", "压缩视频, 格式跟原视频一样"),
    MP4("mp4", "mp4压缩"),
    HLS("hls", "hls压缩"),
    ;

    private final String value;
    private final String desc;

    CompressTypeEnum(String value, String desc)
    {
        this.value = value;
        this.desc = desc;
    }

    public static CompressTypeEnum ofValue(String value) {
        CompressTypeEnum[] values = values();
        for (CompressTypeEnum stateEnum : values) {
            if (stateEnum.value.equals(value)) {
                return stateEnum;
            }
        }
        return null;
    }
}
