package tv.shorthub.common.enums;

/**
 * 系统环境
 *
 * <AUTHOR>
 */
public enum SysEnv
{
    LOCAL("local", "本地环境"),
    DEV("dev", "开发环境"),
    SIT("sit", "测试环境"),
    PROD("prod", "后管使用"),
    API("api", "客户端使用"),
    CALLBACK("callback", "第三方回调"),
    OPENAPI("openapi", "对外开放"),
    ;

    private final String value;
    private final String desc;

    SysEnv(String value, String desc)
    {
        this.value = value;
        this.desc = desc;
    }

    public String getValue()
    {
        return value;
    }

    public String getDesc()
    {
        return desc;
    }
}
