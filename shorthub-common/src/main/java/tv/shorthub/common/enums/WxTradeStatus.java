package tv.shorthub.common.enums;

/**
 * 微信支付交易状态
 * 
 * <AUTHOR>
 */
public enum WxTradeStatus
{
    SUCCESS("SUCCESS", "支付成功"),
    REFUND("REFUND", "转入退款"),
    NOTPAY("NOTPAY", "未支付"),
    CLOSED("CLOSED", "已关闭"),
    ;

    private final String type;
    private final String desc;

    WxTradeStatus(String type, String desc)
    {
        this.type = type;
        this.desc = desc;
    }

    public String getType()
    {
        return type;
    }

    public String getDesc()
    {
        return desc;
    }
}
