package tv.shorthub.common.enums;

public enum OrderChannelEnums {
    PAYPAL("paypal"),
    PAYERMAX("payermax"),
    GOOGLEPLAY("googleplay"),
    AIRWALLEX("airwallex"),
    CRYPTO("crypto"),
    ;

    private final String value;

    OrderChannelEnums(String value)
    {
        this.value = value;
    }

    public String getValue()
    {
        return value;
    }

    public static OrderChannelEnums get(String value) {
        OrderChannelEnums r = null;
        for (OrderChannelEnums v: values()) {
            if (v.getValue().equals(value)) {
                r = v;
                break;
            }
        }
        return r;
    }
}
