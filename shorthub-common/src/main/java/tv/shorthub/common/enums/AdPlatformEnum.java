package tv.shorthub.common.enums;

import lombok.Getter;

@Getter
public enum AdPlatformEnum {
    Facebook("facebook"),
    Google("google"),
    Tiktok("tiktok"),
    ;

    private final String value;

    AdPlatformEnum(String value)
    {
        this.value = value;
    }

    public static AdPlatformEnum ofValue(String value) {
        AdPlatformEnum[] values = values();
        for (AdPlatformEnum stateEnum : values) {
            if (stateEnum.value.equals(value)) {
                return stateEnum;
            }
        }
        return null;
    }
}
