package tv.shorthub.common.enums;

import lombok.Getter;

@Getter
public enum CloudflareQueueEnum {
    PAYPAL_ORDER_UPDATE("paypal-order-update", "PAYPAL订单更新"),
    PAYERMAX_ORDER_UPDATE("payermax-order-update", "PAYERMAX订单更新"),
    PAYPAL_WEBHOOK("paypal-webhook", "PAYPAL webhook事件"),
    PROMOTION_MESSAGE("promotion-message", "推广链接访问消息"),
    LOGIN_RECORDS("login-records", "用户登录记录"),
    ORDER_LISTEN("order-listen", "订单监听"),
    CRYPTO_DEPOSIT("crypto-deposit", "虚拟交易确认"),
    ;

    private final String value;
    private final String desc;

    CloudflareQueueEnum(String value, String desc)
    {
        this.value = value;
        this.desc = desc;
    }

    public static CloudflareQueueEnum ofValue(String value) {
        CloudflareQueueEnum[] values = values();
        for (CloudflareQueueEnum stateEnum : values) {
            if (stateEnum.value.equals(value)) {
                return stateEnum;
            }
        }
        return null;
    }
}
