package tv.shorthub.common.enums;

public enum ExportStateEnum {
    WAIT_STATE("-1", "等待状态", -1),
    EXPORTING("0", "导出中", 0),
    EXPORTED("1", "导出完成", 1),
    ;

    private final String value;
    private final String desc;
    private final Integer state;

    ExportStateEnum(String value, String desc, Integer state)
    {
        this.value = value;
        this.desc = desc;
        this.state = state;
    }

    public String getValue()
    {
        return value;
    }
    public String getDesc()
    {
        return desc;
    }

    public Integer getState() {
        return state;
    }

    public static ExportStateEnum ofValue(String value) {
        ExportStateEnum[] values = values();
        for (ExportStateEnum stateEnum : values) {
            if (stateEnum.value.equals(value)) {
                return stateEnum;
            }
        }
        return null;
    }
}
