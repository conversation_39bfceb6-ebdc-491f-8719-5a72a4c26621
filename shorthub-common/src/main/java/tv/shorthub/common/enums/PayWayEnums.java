package tv.shorthub.common.enums;

public enum PayWayEnums {
    WECHAT(0, "微信支付"),
    HAND(1, "手动补单"),
    DOUYIN(2, "抖音支付"),
    ALIPAY(3, "支付宝"),
    WX_VP(4, "微信虚拟支付"),
    KUAISHOU(5, "快手支付"),
    ;

    private final Integer value;
    private final String desc;

    PayWayEnums(Integer value, String desc)
    {
        this.value = value;
        this.desc = desc;
    }

    public Integer getValue()
    {
        return value;
    }
    public String getDesc()
    {
        return desc;
    }

    public static PayWayEnums get(Integer value) {
        PayWayEnums r = null;
        for (PayWayEnums v: values()) {
            if (v.getValue().compareTo(value) == 0) {
                r = v;
                break;
            }
        }
        return r;
    }
}
