package tv.shorthub.common.core.redis;


import tv.shorthub.common.core.cache.CacheKeyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class RedisCacheNotify {

    @Autowired
    RedisCache redisCache;

    public void notify(String key) {
        log.info("缓存刷新通知 {}", key);
        // 缓存刷新通知
        redisCache.convertAndSend(
                CacheKeyUtils.TOPIC_REFRESH_TABLE,
                key
        );
    }
}
