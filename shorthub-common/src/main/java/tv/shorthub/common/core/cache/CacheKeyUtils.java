package tv.shorthub.common.core.cache;

public class CacheKeyUtils {
    public static final String TOPIC_PP_REFRESH = "PP_REFRESH";
    public static final String TOPIC_REFRESH_TABLE = "TOPIC_REFRESH_TABLE";
    public static final String MAP_PAYPAL_PAYMENT_CONFIG = "MAP_PAYPAL_PAYMENT_CONFIG";
    public static final String MAP_PAYPAL_PAYMENT_CONFIG_BY_ID = "MAP_PAYPAL_PAYMENT_CONFIG_BY_ID";
    public static final String LOCK_PAYPAL_AUTO_SUBSCRIPTION = "LOCK_PAYPAL_AUTO_SUBSCRIPTION";
    public static final String LOCK_AIRWALLEX_AUTO_SUBSCRIPTION = "LOCK_AIRWALLEX_AUTO_SUBSCRIPTION";
    public static final String APP_CONFIG_MAP = "APP_CONFIG_MAP";

    private static final String PREFIX_SERIAL_URL = "serial_url:%s:%s:%s";

    public static String getSerialUrl(String contentId, Long number, String compressType) {
        return String.format(PREFIX_SERIAL_URL, contentId, number, compressType);
    }

    private static final String DRAMAS = "dramas:%s";
    public static String getDramas(String language) {
        return String.format(DRAMAS, language);
    }

    private static final String SERIAL_DETAIL = "serial:detail:%s:%s";

    public static String getSerialDetail(String contentId, Long number) {
        return String.format(SERIAL_DETAIL, contentId, number);
    }

    private static final String CONTENT_SERIAL_MAP = "content:serial:%s";

    public static String getContentSerialMap(String contentId) {
        return String.format(CONTENT_SERIAL_MAP, contentId);
    }

    private static final String CONTENT_DETAIL  = "content:detail:%s";
    public static String getContent(String contentId) {
        return String.format(CONTENT_DETAIL, contentId);
    }

    public static final String MAP_CONTENT_DETAIL_DTO  = "content:detail_dto";

    public static final String RECHARGE_TEMPLATE_SYSTEM = "recharge:template:system";
    private static final String RECHARGE_TEMPLATE = "recharge:template:%s";
    public static String getRechargeTemplate(String templateId) {
        return String.format(RECHARGE_TEMPLATE, templateId);
    }
    private static final String RECHARGE_TEMPLATE_ITEM = "recharge:template_item:%s%s";
    public static String getRechargeTemplateItem(String templateId, String userType) {
        return String.format(RECHARGE_TEMPLATE_ITEM, templateId, userType);
    }

    public static final String MAP_APP_DRAMA_CONTENTS = "app_drama_contents:map";
    public static final String MAP_APP_RECHARGE = "app_recharge:map";

    public static final String MAP_BANNER = "banner:language";
    public static final String MAP_AD_RETARGETING_STRATEGY = "MAP_AD_RETARGETING_STRATEGY";

    private static final String APP_PROMOTION = "app_promotion:%s";
    public static String getAppPromotion(String tfid) {
        return String.format(APP_PROMOTION, tfid);
    }

    /**
     * PayPal token缓存键前缀
     */
    private static final String PAYPAL_TOKEN_PREFIX = "paypal:token:";

    /**
     * 获取PayPal token缓存键
     *
     * @param accountId 账户ID
     * @return 缓存键
     */
    public static String getPayPalTokenCacheKey(String accountId) {
        return PAYPAL_TOKEN_PREFIX + accountId;
    }

    private static final String DRAMA_COUNTRY_LANG = "dramas:country:%s:lang:%s";

    public static String getDramasCountryLang(String countryCode, String language) {
        return String.format(DRAMA_COUNTRY_LANG, countryCode, language);
    }

    private static final String ATTRIBUTION_BY_IP = "attribution:ip:%s";

    public static String getAttributionByIp(String ip) {
        return String.format(ATTRIBUTION_BY_IP, ip);
    }


    private static final String ATTRIBUTION_BY_DEVICE = "attribution:device:%s";

    public static String getAttributionByDevice(String device) {
        return String.format(ATTRIBUTION_BY_DEVICE, device);
    }

    private static final String ORDER_LISTEN_QUEUE = "order:listen:queue:%s";

    public static String getOrderListenQueue(String orderNo) {
        return String.format(ORDER_LISTEN_QUEUE, orderNo);
    }

    private static final String USER_ID_WITH_ACCESS_TOKEN = "user:access_token:%s";

    public static String getUserIdWithAccessToken(String accessToken) {
        return String.format(USER_ID_WITH_ACCESS_TOKEN, accessToken);
    }

    private static final String JUMP_TEMP_CONFIG = "jump:temp:config:%s";
    public static String jumpTempConfig(String uid) {
        return String.format(JUMP_TEMP_CONFIG, uid);
    }

    private static final String AIRWALLEX_TOKEN = "airwallex:token:%s";
    public static String getAirwallexToken(String id) {
        return String.format(AIRWALLEX_TOKEN, id);
    }

    private static final String BIND_USER_DEVICE = "device:userId:%s";
    public static String getDeviceIdUserId(String deviceId) {
        return String.format(BIND_USER_DEVICE, deviceId);
    }

    public static final String AIRWALLEX_PAYMENT_CONFIG_MAP = "AIRWALLEX_PAYMENT_CONFIG_MAP";

    private static final String RECHARGE_TO_GOOGLE_PLAY_LOCK = "RECHARGE_TO_GOOGLE_PLAY_LOCK:%s";
    public static String getRechargeToGooglePlayLock(String templateId) {
        return String.format(RECHARGE_TO_GOOGLE_PLAY_LOCK, templateId);
    }

    private static final String AUTO_SUBSCRIPTION_REMIND_EMAIL = "AUTO_SUBSCRIPTION_REMIND_EMAIL:%s:%s";
    public static String getAutoSubscriptionRemindEmail(String orderNo, Long count) {
        return String.format(AUTO_SUBSCRIPTION_REMIND_EMAIL, orderNo, count);
    }

    public static final String REMIND_DAYS = "REMIND_DAYS";

    public static final String COMPRESS_START = "COMPRESS_START";
    public static final String COMPRESS_START_ING = "COMPRESS_START_ING";


    private static final String ITEM_DISCOUNT_ITEM_BY_PARENT_ITEM_ID = "item:parent_item_id:%s";

    public static String getDiscountItemMap(String parentItemId) {
        return String.format(ITEM_DISCOUNT_ITEM_BY_PARENT_ITEM_ID, parentItemId);
    }
    private static final String ITEM_BY_ITEM_ID = "item:item_id:%s";

    public static String getItemMap(String itemId) {
        return String.format(ITEM_BY_ITEM_ID, itemId);
    }


    private static final String APP_USER_CLOSE_PAY_PAGE_RECORD = "app_user_close_pay_page_record:%s";

    public static String getAppUserClosePayPageRecord(String userId) {
        return String.format(APP_USER_CLOSE_PAY_PAGE_RECORD, userId);
    }

    private static final String CRYPTO_TOKEN = "crypto:token:%s";

    public static String getCryptoToken(String address) {
        return String.format(CRYPTO_TOKEN, address);
    }


    private static final String BLACKLIST_PREFIX = "blacklist:%s";

    public static final String BLACKLIST_DEVICE_ID = String.format(BLACKLIST_PREFIX, "deviceIds");
    public static final String BLACKLIST_USER_ID = String.format(BLACKLIST_PREFIX, "userIds");
    public static final String BLACKLIST_IP = String.format(BLACKLIST_PREFIX, "ips");
    public static final String BLACKLIST_EMAIL = String.format(BLACKLIST_PREFIX, "emails");

}
