package tv.shorthub.common.core.cache;

import tv.shorthub.common.core.redis.RedisCache;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;

@Component
@Data
@Slf4j
public class CacheService {

    @Autowired
    private RedisCache redisCache;

    /**
     * 仅支持获取整个Map缓存
     */
    @Autowired
    private LocalMapCache localMapCache;

    /**
     * 仅支持获取有hKey的缓存
     */
    @Autowired
    private LocalMapValueCache localMapValueCache;

    @Autowired
    private LocalListCache localListCache;

    @Autowired
    private LocalObjectCache localObjectCache;

    public <T> Map<String, T> getCacheMap(String key) {
        Map<String, T> cacheMap = localMapCache.getCacheMap(key);
        if (null == cacheMap || cacheMap.isEmpty()) {
            cacheMap = redisCache.getCacheMap(key);
            if (null != cacheMap) {
                localMapCache.setCacheMap(key, cacheMap);
            }
            return cacheMap;
        }
        return cacheMap;
    }
    public <T> T getCacheMapValue(String key, String hKey) {
        T value = localMapValueCache.getCacheMapValue(key, hKey);
        if (null == value) {
            value = redisCache.getCacheMapValue(key, hKey);
            if (null != value) {
                localMapValueCache.setCacheMapValue(key, hKey, value);
            }
        }
        return value;
    }

    public <T> T getCacheObject(String key) {
        T cacheObject = localObjectCache.getCacheObject(key);
        if (null == cacheObject) {
            cacheObject = redisCache.getCacheObject(key);
            if (null != cacheObject) {
                localObjectCache.setCacheObject(key, cacheObject);
            }
        }
        return cacheObject;
    }

    public <T> List<T> getCacheList(String key) {
        List<T> cacheList = localListCache.getCacheList(key);
        if (CollectionUtils.isEmpty(cacheList)) {
            cacheList = redisCache.getCacheList(key);
            if (null != cacheList) {
                localListCache.setCacheList(key, cacheList);
            }
        }
        return cacheList;
    }

    public <T> void setLocalCacheList(String key, List<T> value) {
        localListCache.setCacheList(key, value);
    }

    public <T> void setLocalCacheMapValue(String key, String hKey, T value) {
        localMapValueCache.setCacheMapValue(key, hKey, value);
    }
    public <T> void setLocalObjectValue(String key, T value) {
        localObjectCache.setCacheObject(key, value);
    }
}
