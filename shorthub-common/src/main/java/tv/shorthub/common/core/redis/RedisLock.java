package tv.shorthub.common.core.redis;


import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class RedisLock {
    @Autowired
    RedisCache redisCache;

    long stayTime = 50;

    public boolean lock(String key, String value, long timeout, long waitTime) {
        Boolean lock = redisCache.redisTemplate.opsForValue().setIfAbsent(
                key, value,
                timeout,
                TimeUnit.MILLISECONDS
        );
        if (null != lock && !lock) {
            log.info("事件锁被占用: 尝试等待...key {}", key);
            while(waitTime > 0 && (waitTime = waitTime - stayTime) > 0 && !lock) {
                lock = redisCache.redisTemplate.opsForValue().setIfAbsent(
                        key, value,
                        timeout,
                        TimeUnit.MILLISECONDS
                );
                try {
                    Thread.sleep(stayTime);
                } catch (InterruptedException e) {
                }
            }
            log.info("事件锁获取结果...key {}, r {}", key, lock);
        }
        return null == lock ? true : lock;
    }
}
