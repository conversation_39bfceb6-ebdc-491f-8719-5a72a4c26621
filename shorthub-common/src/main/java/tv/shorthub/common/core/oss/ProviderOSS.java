package tv.shorthub.common.core.oss;

import tv.shorthub.common.utils.spring.SpringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.File;
import java.time.Duration;

@Slf4j
@Component
public class ProviderOSS implements IProviderOSS {
    IProviderOSS providerOSS;

    public ProviderOSS() {
        providerOSS = SpringUtils.getBean(S3Uploader.class);
    }

    @Override
    public String upload(String storagePath, String fileName) {
        return providerOSS.upload(storagePath, fileName);
    }

    @Override
    public String uploadToPrivate(String storagePath, String fileName) {
        return providerOSS.uploadToPrivate(storagePath, fileName);
    }

    @Override
    public String uploadToPrivate(String storagePath, File file) {
        return providerOSS.uploadToPrivate(storagePath, file);
    }

    @Override
    public String generateUrl(String storagePath, Duration duration) {
        return providerOSS.generateUrl(storagePath, duration);
    }

}
