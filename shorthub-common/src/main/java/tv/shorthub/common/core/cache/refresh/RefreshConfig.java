package tv.shorthub.common.core.cache.refresh;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Function;
import java.util.function.Supplier;

@Data
public class RefreshConfig {
    private String key;
    // 排序
    private int sort;
    // 初始化完成
    private boolean init;
    private boolean async;
    private int seconds;

    private RefreshService refreshService;

    public RefreshConfig() {}

}
