package tv.shorthub.common.core.oss;

import java.io.File;
import java.time.Duration;

public interface IProviderOSS {

    /**
     * 上传文件到公共目录
     * @param fileName 本地文件路径
     * @return
     */
    String upload(String storagePath, String fileName);

    /**
     * 上传文件到私有目录
     * @param fileName 本地文件路径
     * @return
     */
    String uploadToPrivate(String storagePath, String fileName);

    /**
     * 上传文件到私有目录
     * @param file 本地文件
     * @return
     */
    String uploadToPrivate(String storagePath, File file);

    /**
     * 获取文件加密链接
     * @param storagePath
     * @return
     */
    String generateUrl(String storagePath, Duration duration);
}
