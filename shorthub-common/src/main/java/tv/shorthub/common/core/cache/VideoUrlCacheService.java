package tv.shorthub.common.core.cache;

import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tv.shorthub.common.core.redis.RedisCache;

import java.util.concurrent.TimeUnit;

@Component
@Data
public class VideoUrlCacheService {

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private VideoUrlLocalMapCache localMapCache;


    public <T> T getCacheObject(String localKey, String redisKey) {
        T value = localMapCache.getCacheMapValue(localKey, redisKey);
        if (null == value) {
            value = redisCache.getCacheObject(redisKey);
            if (null != value) {
                localMapCache.setCacheMapValue(localKey, redisKey, value);
            }
        }
        return value;
    }

    public <T> void putCacheObject(String localKey, String redisKey, T v, final Integer timeout, final TimeUnit timeUnit) {
        if (null != v) {
            localMapCache.setCacheMapValue(localKey, redisKey, v);
            redisCache.setCacheObject(redisKey, v, timeout, timeUnit);
        }
    }
}
