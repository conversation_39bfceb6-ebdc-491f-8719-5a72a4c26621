package tv.shorthub.common.core.cache;

import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.Arrays;

@Component
@Slf4j
public class LocalObjectCache {

    private LoadingCache<String, Object> localCache = Caffeine.newBuilder()
            .expireAfterWrite(Duration.ofSeconds(30))
            .build(key -> null)
            ;

    public boolean isExists(String key) {
        return null != localCache.getIfPresent(key);
    }

    public <T> T getCacheObject(String key) {
        return (T) localCache.get(key);
    }

    public <T> void setCacheObject(String key, T value) {
        localCache.put(key, value);
    }

}
