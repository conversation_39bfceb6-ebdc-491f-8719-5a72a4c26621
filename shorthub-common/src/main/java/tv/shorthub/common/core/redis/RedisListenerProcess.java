package tv.shorthub.common.core.redis;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tv.shorthub.common.core.cache.refresh.CacheRefreshExecutor;

@Component
@Slf4j
public class RedisListenerProcess {

    @Autowired
    CacheRefreshExecutor cacheRefresh;

    /**
     * 处理缓存刷新订阅消息
     * @param message
     */
    public void refreshCache(String message) {
        if (StringUtils.isEmpty(message)) {
            log.info("订阅消息为空");
            return;
        }
        message = message.replaceAll("\"", "");
        log.info("收到订阅消息, 缓存刷新 {}", message);
        cacheRefresh.refresh(message);
    }
}
