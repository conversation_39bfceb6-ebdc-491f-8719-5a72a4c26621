package tv.shorthub.common.core.oss;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.core.sync.ResponseTransformer;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.GetObjectRequest;
import software.amazon.awssdk.services.s3.model.PutObjectRequest;
import software.amazon.awssdk.services.s3.model.PutObjectResponse;
import software.amazon.awssdk.services.s3.presigner.S3Presigner;
import software.amazon.awssdk.services.s3.presigner.model.GetObjectPresignRequest;
import software.amazon.awssdk.services.s3.presigner.model.PresignedGetObjectRequest;
import tv.shorthub.common.utils.UploadProgressPool;

import jakarta.annotation.PostConstruct;
import javax.xml.bind.DatatypeConverter;
import java.io.File;
import java.io.IOException;
import java.net.URI;
import java.nio.file.Files;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Component
public class S3Uploader implements IProviderOSS {
    @Value("${cloudflare.r2.account-id}")
    private String ACCOUNT_ID;
    @Value("${cloudflare.r2.access.key.id}")
    private String ACCESS_KEY_ID;
    @Value("${cloudflare.r2.access.key.secret}")
    private String SECRET_ACCESS_KEY;
    @Value("${cloudflare.r2.url.endpoint}")
    private String ENDPOINT_URL;
    @Value("${cloudflare.r2.url.bucket-of-sign}")
    private String URL_BUCKET_OF_SIGN;
    // 私有库, 需签名, 配置Worker
    @Value("${cloudflare.r2.bucket.private.name}")
    private String bucketNameShorthub;
    @Value("${cloudflare.r2.bucket.private.domain}")
    private String private_domain;

    // 公开访问资源库
    @Value("${cloudflare.r2.bucket.public.name}")
    private String bucketNameShorthubPublic;
    @Value("${cloudflare.r2.bucket.public.domain}")
    private String public_domain;


    private S3Client s3Client;
    private S3Presigner presigner;


    private static final Map<String, String> domains = new HashMap<>();

    @PostConstruct
    public void init() {
        
        domains.put(bucketNameShorthubPublic, public_domain);
        domains.put(bucketNameShorthub, private_domain);
        // Hash the secret access key using SHA-256
        String hashedSecretKey;
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hash = digest.digest(SECRET_ACCESS_KEY.getBytes());
            hashedSecretKey = DatatypeConverter.printHexBinary(hash).toLowerCase();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("SHA-256 algorithm not available", e);
        }

        // Configure the S3 client
        AwsBasicCredentials credentials = AwsBasicCredentials.create(ACCESS_KEY_ID, hashedSecretKey);
        this.s3Client = S3Client.builder()
                .credentialsProvider(StaticCredentialsProvider.create(credentials))
                .endpointOverride(URI.create(ENDPOINT_URL))
                .region(Region.US_EAST_1) // R2 ignores region, but SDK requires a value
                .build();

        this.presigner = S3Presigner.builder()
                .credentialsProvider(StaticCredentialsProvider.create(credentials))
                .endpointOverride(URI.create(ENDPOINT_URL))
                .region(Region.of("auto"))
                .build();
    }

    public void putObject(String bucketName, String storagePath, byte[] fileContent, String contentType) {
        try {
            // Build the PutObject request
            PutObjectRequest putObjectRequest = PutObjectRequest.builder()
                    .bucket(bucketName)
                    .key(storagePath)
                    .contentType(contentType)
                    .build();

            // Upload the object
            PutObjectResponse response = s3Client.putObject(
                    putObjectRequest,
                    RequestBody.fromBytes(fileContent)
            );

            System.out.println("Successfully uploaded the object: " + storagePath);
        } catch (Exception e) {
            System.err.println("Error uploading object: " + e.getMessage());
            throw e;
        }
    }
    public String putObject(String bucketName, String storagePath, File file, String uploadId) {
        try {
            // Validate and read the video file
            if (!file.exists() || !file.isFile()) {
                throw new IllegalArgumentException("Video file does not exist or is not a file: " + file.getAbsolutePath());
            }

            log.info("开始上传文件到R2 - bucket: {}, path: {}, file: {}", bucketName, storagePath, file.getAbsolutePath());

            // Determine content type based on file extension
            String contentType = getContentType(file);
            log.info("文件Content-Type: {}", contentType);

            // Build the PutObject request
            PutObjectRequest putObjectRequest = PutObjectRequest.builder()
                    .bucket(bucketName)
                    .key(storagePath)
                    .contentType(contentType)
                    .build();

            // 实现带进度回调的 InputStream
            class ProgressInputStream extends java.io.FileInputStream {
                private final long total;
                private long read = 0;
                private int lastPercent = 0;
                private final java.util.function.IntConsumer progressCallback;

                public ProgressInputStream(File file, java.util.function.IntConsumer progressCallback) throws java.io.FileNotFoundException {
                    super(file);
                    this.total = file.length();
                    this.progressCallback = progressCallback;
                }

                @Override
                public int read(byte[] b, int off, int len) throws java.io.IOException {
                    int n = super.read(b, off, len);
                    if (n > 0) {
                        read += n;
                        int percent = (int) (read * 100 / total);
                        if (percent != lastPercent) {
                            lastPercent = percent;
                            progressCallback.accept(percent);
                        }
                    }
                    return n;
                }
            }

            // 创建带进度回调的 InputStream
            ProgressInputStream pis = new ProgressInputStream(file, percent -> {
                if (uploadId != null) {
                    UploadProgressPool.updateUploadProgress(uploadId, percent, UploadProgressPool.STAGE_OSS_UPLOAD);
                    log.info("S3上传进度 [{}]: {}% ({})", uploadId, percent, UploadProgressPool.getCurrentStage(uploadId));
                }
            });
            RequestBody requestBody = RequestBody.fromInputStream(pis, file.length());

            // Upload the file
            PutObjectResponse response = s3Client.putObject(
                    putObjectRequest,
                    requestBody
            );

            log.info("R2上传响应 - ETag: {}, 请求ID: {}", response.eTag(), response.responseMetadata().requestId());

            // 获取加密链接
            String url = domains.get(bucketName) + storagePath;

            log.info("文件上传成功 - URL: {}", url);

            return url;
        } catch (Exception e) {
            log.error("上传文件到R2失败 - bucket: {}, path: {}, error: {}", bucketName, storagePath, e.getMessage(), e);
            throw new RuntimeException("Failed to upload video", e);
        }
    }

    // Helper method to determine content type based on file extension
    private String getContentType(File file) {
        String fileName = file.getName().toLowerCase();
        if (fileName.endsWith(".mp4")) {
            return "video/mp4";
        } else if (fileName.endsWith(".txt")) {
            return "text/plain";
        } else if (fileName.endsWith(".pdf")) {
            return "application/pdf";
        } else {
            // Fallback to probing content type or default
            try {
                String probedType = Files.probeContentType(file.toPath());
                return probedType != null ? probedType : "application/octet-stream";
            } catch (IOException e) {
                return "application/octet-stream";
            }
        }
    }

    // 生成预签名访问链接
    public String generatePresignedGetUrl(String  bucketName, String objectKey, Duration expiration) {
        GetObjectRequest getObjectRequest = GetObjectRequest.builder()
                .bucket(bucketName)
                .key(objectKey)
                .build();

        GetObjectPresignRequest presignRequest = GetObjectPresignRequest.builder()
                .signatureDuration(expiration)
                .getObjectRequest(getObjectRequest)
                .build();

        PresignedGetObjectRequest presignedRequest = presigner.presignGetObject(presignRequest);
        String url = presignedRequest.url().toString().replaceAll(String.format(URL_BUCKET_OF_SIGN, bucketName), domains.get(bucketName));
        log.info("生成的预签名访问链接: {}", url);
        return url;
    }

    public static void main(String[] args) {
        S3Uploader uploader = new S3Uploader();
        String url = uploader.generatePresignedGetUrl("shorthub", "temp/1920675022816104448.mp4", Duration.ofMinutes(5));
        System.out.println(url);
//        uploader.putObject("uploads/test.txt", "Hello from Cloudflare R2!".getBytes(), "text/plain");
//        uploader.putObject("uploads/test.mp4", new File("D:\\Administrator\\Downloads\\578668219.720p.narrowv3.mp4"));
    }

    @Override
    public String upload(String storagePath, String fileName) {
        return putObject(bucketNameShorthubPublic, storagePath, new File(fileName), null);
    }

    @Override
    public String uploadToPrivate(String storagePath, String fileName) {
        return putObject(bucketNameShorthub, storagePath, new File(fileName), null);
    }

    @Override
    public String uploadToPrivate(String storagePath, File file) {
        return putObject(bucketNameShorthub, storagePath, file, null);
    }

    // 添加带进度跟踪的上传方法
    public String uploadToPrivateWithProgress(String storagePath, File file, String uploadId) {
        return putObject(bucketNameShorthub, storagePath, file, uploadId);
    }

    @Override
    public String generateUrl(String storagePath, Duration duration) {
        return generatePresignedGetUrl(bucketNameShorthub, storagePath, duration);
    }

    /**
     * 获取S3Client实例
     */
    public S3Client getS3Client() {
        return this.s3Client;
    }

    /**
     * 从预签名URL中提取存储路径
     */
    private String extractStoragePath(String url) {
        try {
            URI uri = new URI(url);
            String path = uri.getPath();
            // 移除开头的斜杠
            if (path.startsWith("/")) {
                path = path.substring(1);
            }
            return path;
        } catch (Exception e) {
            log.error("解析URL失败: {}", url, e);
            return null;
        }
    }

    /**
     * 下载文件到指定路径
     * @param url 预签名URL
     * @param targetPath 目标文件路径
     * @return 是否下载成功
     */
    public boolean downloadFile(String url, String targetPath) {
        try {
            // 从URL中提取存储路径
            String storagePath = extractStoragePath(url);
            if (storagePath == null) {
                log.error("无法从URL中提取存储路径: {}", url);
                return false;
            }

            // 创建目标文件
            File targetFile = new File(targetPath);
            targetFile.getParentFile().mkdirs();

            // 下载文件
            GetObjectRequest getObjectRequest = GetObjectRequest.builder()
                .bucket(bucketNameShorthub)
                .key(storagePath)
                .build();

            s3Client.getObject(getObjectRequest, ResponseTransformer.toFile(targetFile));
            log.info("文件下载完成: {}", targetPath);
            return true;
        } catch (Exception e) {
            log.error("下载文件失败: {}", url, e);
            return false;
        }
    }
}
