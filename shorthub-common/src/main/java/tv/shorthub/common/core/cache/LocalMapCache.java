package tv.shorthub.common.core.cache;

import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

@Component
@Slf4j
public class LocalMapCache {

    private LoadingCache<String, Map<String, Object>> localCache = Caffeine.newBuilder()
            .expireAfterWrite(Duration.ofSeconds(30))
            .build(key -> {
                Map<String, Object> map = new HashMap<>();
                return map;
            });

    public boolean isExists(String key) {
        return null != localCache.getIfPresent(key);
    }

    public <T> void setCacheMap(String key, Map<String, T> dataMap) {
        localCache.put(key, (Map<String, Object>) dataMap);
    }

    public <T> Map<String, T> getCacheMap(String key) {
        return (Map<String, T>) localCache.get(key);
    }

    public <T> void setCacheMapValue(String key, String hKey, T value) {
        Map<String, Map<String, T>> cacheMap = getCacheMap(key);
        if (null == cacheMap) {
            cacheMap = new HashMap<>();
            Map<String, T> entryMap = new HashMap<>();
            cacheMap.put(key, entryMap);
        } else if (!cacheMap.containsKey(key)) {
            Map<String, T> entryMap = new HashMap<>();
            cacheMap.put(key, entryMap);
        }
        cacheMap.get(key).put(hKey, value);
    }

    public <T> T getCacheMapValue(String key, String hKey) {
        Map<String, T> cacheMap = getCacheMap(key);
        if (null == cacheMap || !cacheMap.containsKey(hKey)) {
            return null;
        }
        return cacheMap.get(hKey);
    }
}
