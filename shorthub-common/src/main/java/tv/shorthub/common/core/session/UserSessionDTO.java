package tv.shorthub.common.core.session;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.servlet.http.HttpSession;
import lombok.Data;
import tv.shorthub.common.dto.UserInfo;
import tv.shorthub.common.utils.StringUtils;

import java.io.Serializable;
import java.util.Date;

@Data
public class UserSessionDTO implements Serializable {
    private static final long serialVersionUID = -1;
    private static final String default_language = "en-US";

    private String tfid;
    private String userId;
    private String memberId;
    private String memberLevel;
    private Date memberExpireTime;
    private String appid;
    private String email;
    private String ip;
    private String sessionId;
    private String sessionKey;
    private String country;
    private String language;
    private String userAgent;
    private String os;
    // 设备参数
    private String device;
    private String deviceId;


    @JsonIgnore
    private transient HttpSession httpSession;

    public void refreshUser(UserInfo user) {
        setMemberExpireTime(user.getMemberTime());
        setEmail(user.getEmail());
        setUserId(user.getUserId());
        refreshSession();
    }

    public String getLanguage() {
        if (StringUtils.isEmpty(language)) {
            return default_language;
        }
        return language;
    }

    public void refreshSession() {
        if (null != getHttpSession()) {
            getHttpSession().setAttribute("user", this);
        }
    }
}
