package tv.shorthub.common.core.cache;

import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Component
@Slf4j
public class LocalListCache {

    private LoadingCache<String, List<Object>> localCache = Caffeine.newBuilder()
            .expireAfterWrite(Duration.ofSeconds(30))
            .build(key -> new ArrayList<>());

    public boolean isExists(String key) {
        return null != localCache.getIfPresent(key);
    }

    public <T> List<T> getCacheList(String key) {
        return (List<T>) localCache.get(key);
    }

    public <T> void setCacheList(String key, List<T> value) {
        localCache.put(key, (List<Object>) value);
    }

}
