package tv.shorthub.common.core.redis;

import tv.shorthub.common.core.cache.CacheKeyUtils;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.listener.PatternTopic;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.data.redis.listener.adapter.MessageListenerAdapter;

@Configuration
public class RedisMessageListener {

    @Bean
    RedisMessageListenerContainer container(RedisConnectionFactory connectionFactory, MessageListenerAdapter listenerAdapter) {
        RedisMessageListenerContainer container = new RedisMessageListenerContainer();
        container.setConnectionFactory(connectionFactory);
        container.addMessageListener(listenerAdapter, new PatternTopic(CacheKeyUtils.TOPIC_REFRESH_TABLE));
        return container;
    }

    /**
     * @param listener 自定义接收消息类和方法
     * @return
     */
    @Bean
    MessageListenerAdapter listenerAdapter(RedisListenerProcess listener) {
        return new MessageListenerAdapter(listener, "refreshCache");
    }
}
