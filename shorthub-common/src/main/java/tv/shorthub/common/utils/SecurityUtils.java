package tv.shorthub.common.utils;

import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;
import tv.shorthub.common.constant.Constants;
import tv.shorthub.common.constant.HttpStatus;
import tv.shorthub.common.core.domain.model.UserApp;
import tv.shorthub.common.exception.ServiceException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.util.PatternMatchUtils;
import tv.shorthub.common.core.domain.entity.SysRole;
import tv.shorthub.common.core.domain.model.LoginUser;

/**
 * 安全服务工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class SecurityUtils
{

    private static final List<String> systemAdmin = List.of("admin", "o_admin", "dev_admin");
    private static final List<String> businessAdmin = List.of("promotion_master", "finance");

    /**
     * 获取所有特权角色（系统管理员 + 业务主账号）
     * @return 角色键列表
     */
    public static List<String> getPrivilegedRoleKeys() {
        List<String> privilegedRoles = new java.util.ArrayList<>();
        privilegedRoles.addAll(systemAdmin);
        privilegedRoles.addAll(businessAdmin);
        return privilegedRoles;
    }

    /**
     * 用户ID
     **/
    public static Long getUserId()
    {
        try
        {
            return getLoginUser().getUserId();
        }
        catch (Exception e)
        {
            throw new ServiceException("获取用户ID异常", HttpStatus.UNAUTHORIZED);
        }
    }

    /**
     * 获取部门ID
     **/
    public static Long getDeptId()
    {
        try
        {
            return getLoginUser().getDeptId();
        }
        catch (Exception e)
        {
            throw new ServiceException("获取部门ID异常", HttpStatus.UNAUTHORIZED);
        }
    }

    /**
     * 获取用户账户
     **/
    public static String getUsername()
    {
        try
        {
            return getLoginUser().getUsername();
        }
        catch (Exception e)
        {
            throw new ServiceException("获取用户账户异常", HttpStatus.UNAUTHORIZED);
        }
    }

    public static String getAppid() {
        try
        {
            return getLoginUser().getUser().getAppid();
        }
        catch (Exception e)
        {
            throw new ServiceException("获取Appid异常", HttpStatus.UNAUTHORIZED);
        }
    }

    public static void switchApp(String appid) {
        long find = getAppList().stream().filter(f -> f.getAppid().equals(appid)).count();
        if(find == 0) {
            throw new ServiceException("App不存在", HttpStatus.UNAUTHORIZED);
        }
        getLoginUser().getUser().setAppid(appid);
        log.info("切换App: {}, {}", getUsername(), appid);
    }

    public static UserApp getApp() {
        return getAppList().stream().filter(f -> f.getAppid().equals(getAppid())).findFirst().get();
    }

    public static List<UserApp> getAppList() {
        try
        {
            return getLoginUser().getAppList();
        }
        catch (Exception e)
        {
            throw new ServiceException("获取授权app异常", HttpStatus.UNAUTHORIZED);
        }
    }

    public static String getAccessUsername() {
        if (isAdmin(getUserId())) {
            return null;
        }
        return getUsername();
    }

    /**
     * 获取用户
     **/
    public static LoginUser getLoginUser()
    {
        try
        {
            return (LoginUser) getAuthentication().getPrincipal();
        }
        catch (Exception e)
        {
            throw new ServiceException("获取用户信息异常", HttpStatus.UNAUTHORIZED);
        }
    }

    /**
     * 获取Authentication
     */
    public static Authentication getAuthentication()
    {
        return SecurityContextHolder.getContext().getAuthentication();
    }

    /**
     * 生成BCryptPasswordEncoder密码
     *
     * @param password 密码
     * @return 加密字符串
     */
    public static String encryptPassword(String password)
    {
        BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
        return passwordEncoder.encode(password);
    }

    /**
     * 判断密码是否相同
     *
     * @param rawPassword 真实密码
     * @param encodedPassword 加密后字符
     * @return 结果
     */
    public static boolean matchesPassword(String rawPassword, String encodedPassword)
    {
        BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
        return passwordEncoder.matches(rawPassword, encodedPassword);
    }

    /**
     * 是否为管理员
     *
     * @param userId 用户ID
     * @return 结果
     */
    public static boolean isAdmin(Long userId)
    {
        return userId != null && 1L == userId;
    }

    /**
     * 是否为管理员
     *
     * @return 结果
     */
    public static boolean isSystemAdmin()
    {
        for (String role : systemAdmin) {
            if (hasRole(role)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 是否为业务管理员
     *
     * @return 结果
     */
    public static boolean isBusinessAdmin()
    {
        for (String role : businessAdmin) {
            if (hasRole(role)) {
                return true;
            }
        }
        return isSystemAdmin();
    }

    /**
     * 验证用户是否具备某权限
     *
     * @param permission 权限字符串
     * @return 用户是否具备某权限
     */
    public static boolean hasPermi(String permission)
    {
        return hasPermi(getLoginUser().getPermissions(), permission);
    }

    /**
     * 判断是否包含权限
     *
     * @param authorities 权限列表
     * @param permission 权限字符串
     * @return 用户是否具备某权限
     */
    public static boolean hasPermi(Collection<String> authorities, String permission)
    {
        return authorities.stream().filter(StringUtils::hasText)
                .anyMatch(x -> Constants.ALL_PERMISSION.equals(x) || PatternMatchUtils.simpleMatch(x, permission));
    }

    /**
     * 验证用户是否拥有某个角色
     *
     * @param role 角色标识
     * @return 用户是否具备某角色
     */
    public static boolean hasRole(String role)
    {
        List<SysRole> roleList = getLoginUser().getUser().getRoles();
        Collection<String> roles = roleList.stream().map(SysRole::getRoleKey).collect(Collectors.toSet());
        return hasRole(roles, role);
    }

    /**
     * 判断是否包含角色
     *
     * @param roles 角色列表
     * @param role 角色
     * @return 用户是否具备某角色权限
     */
    public static boolean hasRole(Collection<String> roles, String role)
    {
        return roles.stream().filter(StringUtils::hasText)
                .anyMatch(x -> Constants.SUPER_ADMIN.equals(x) || PatternMatchUtils.simpleMatch(x, role));
    }


    public static Long safeGetUserId() {
        LoginUser loginUser = null;
        try {
            loginUser = getLoginUser();
        } catch (Exception exception) {}
        if (null == loginUser) {
            return -1L;
        }
        return loginUser.getUserId();
    }

}
