package tv.shorthub.common.utils;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 上传进度池
 */
public class UploadProgressPool {
    private static final Map<String, Integer> progressMap = new ConcurrentHashMap<>();
    private static final Map<String, String> stageMap = new ConcurrentHashMap<>();

    // 定义上传阶段
    public static final String STAGE_SERVER_PROCESS = "服务器处理中";    // 服务器处理阶段
    public static final String STAGE_OSS_UPLOAD = "云端存储上传中";      // 云端存储上传阶段

    /**
     * 更新上传进度
     * @param uploadId 上传ID
     * @param progress 进度(0-100)
     * @param stage 当前阶段
     */
    public static void updateUploadProgress(String uploadId, int progress, String stage) {
        progressMap.put(uploadId, progress);
        stageMap.put(uploadId, stage);
    }

    /**
     * 获取当前进度
     * @param uploadId 上传ID
     * @return 当前进度(0-100)
     */
    public static int getTotalProgress(String uploadId) {
        Integer progress = progressMap.get(uploadId);
        return progress != null ? progress : 0;
    }

    /**
     * 获取当前阶段
     * @param uploadId 上传ID
     * @return 当前阶段描述
     */
    public static String getCurrentStage(String uploadId) {
        String stage = stageMap.get(uploadId);
        return stage != null ? stage : "等待上传";
    }

    /**
     * 移除上传进度
     * @param uploadId 上传ID
     */
    public static void remove(String uploadId) {
        progressMap.remove(uploadId);
        stageMap.remove(uploadId);
    }
}
