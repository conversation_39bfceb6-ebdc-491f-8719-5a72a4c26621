package tv.shorthub.common.utils;

import java.math.BigDecimal;
import java.math.RoundingMode;

public class BigDecimalUtils {

    public static BigDecimal valueOf(String value) {
        if (StringUtils.isEmpty(value)) {
            return null;
        }
        return new BigDecimal(value);
    }


    public static BigDecimal valueOf(Double value) {
        if (null == value) {
            return null;
        }
        return new BigDecimal(value);
    }

    public static BigDecimal sub(BigDecimal value1, BigDecimal value2) {
        if (null == value1 && null == value2) {
            return null;
        } else if (null == value2) {
            value2 = BigDecimal.ZERO;
        } else if (null == value1) {
            value1 = BigDecimal.ZERO;
        }
        return value1.subtract(value2);
    }
    public static BigDecimal divide(Long value1, Long value2) {
        return divide(BigDecimal.valueOf(value1), BigDecimal.valueOf(value2));
    }

    public static BigDecimal divide(BigDecimal value1, BigDecimal value2) {
        if (null == value1 || null == value2) {
            return BigDecimal.ZERO;
        } else if (value1.compareTo(BigDecimal.ZERO) == 0 || value2.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }
        return value1.divide(value2, 2, RoundingMode.CEILING).multiply(BigDecimal.valueOf(100));
    }

}
