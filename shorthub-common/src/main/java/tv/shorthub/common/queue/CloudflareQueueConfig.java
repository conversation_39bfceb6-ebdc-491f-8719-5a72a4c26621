package tv.shorthub.common.queue;

import lombok.Data;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@Data
public class CloudflareQueueConfig {

    @Value("${cloudflare.queue.account-id:}")
    private String accountId;

    @Value("${cloudflare.queue.api-token:}")
    private String apiToken;

    @Value("${cloudflare.queue.enabled:true}")
    private Boolean enabled;

    @Bean
    public CloseableHttpClient httpClient() {
        return HttpClients.createDefault();
    }

    public String getAccountId() {
        return accountId;
    }

    public String getApiToken() {
        return apiToken;
    }
}
