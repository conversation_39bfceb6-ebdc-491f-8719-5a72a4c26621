package tv.shorthub.common.queue;

import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpDelete;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@Service
public class CloudflareQueueService {

    private static final String CLOUDFLARE_API_BASE = "https://api.cloudflare.com/client/v4/accounts/%s/queues";
    private final CloseableHttpClient httpClient = HttpClients.createDefault();

    @Autowired
    private CloudflareQueueConfig config;

    // 存储队列ID的映射
    private final Map<String, String> queueIdMap = new ConcurrentHashMap<>();
    // 存储队列处理器的映射
    private final Map<String, QueueMessageHandler> queueHandlers = new ConcurrentHashMap<>();

    /**
     * 注册队列处理器
     * @param queueName 队列名称
     * @param handler 消息处理器
     */
    public void registerQueueHandler(String queueName, QueueMessageHandler handler) {
        queueHandlers.put(queueName, handler);
        if (StringUtils.isEmpty(config.getAccountId())) {
            log.info("Cloudflare API token not configured. Please set the cloudflare.queue.api-token property in application.yml.");
            return;
        }
        // 初始化队列ID
        getQueueId(queueName);
        log.info("Registered queue handler for queue: {}, queueId: {}, handler: {}", queueName, queueIdMap.get(queueName), handler.getClass().getName());
    }

    /**
     * 获取队列ID
     * @param queueName 队列名称
     * @return 队列ID
     */
    private String getQueueId(String queueName) {
        return queueIdMap.computeIfAbsent(queueName, name -> {
            try {
                String url = String.format(CLOUDFLARE_API_BASE, config.getAccountId());
                HttpGet request = new HttpGet(url);
                request.setHeader("Authorization", "Bearer " + config.getApiToken());

                try (CloseableHttpResponse response = httpClient.execute(request)) {
                    String responseBody = EntityUtils.toString(response.getEntity());
                    if (response.getStatusLine().getStatusCode() == 200) {
                        JSONObject responseMap = JSONObject.parseObject(responseBody);
                        if (responseMap.containsKey("result")) {
                            JSONArray queues = responseMap.getJSONArray("result");
                            for (int i = 0; i < queues.size(); i++) {
                                JSONObject queue = queues.getJSONObject(i);
                                if (name.equals(queue.getString("queue_name"))) {
                                    return queue.getString("queue_id");
                                }
                            }
                        }
                    }
                    log.error("Failed to get queue ID for queue: {}. Response: {}", name, responseBody);
                    return null;
                }
            } catch (Exception e) {
                log.error("Error getting queue ID for queue: {}", name, e);
                return null;
            }
        });
    }

    /**
     * 发送消息到指定队列
     * @param queueName 队列名称
     * @param message 消息对象
     */
    public void sendMessage(String queueName, JSONObject message) {
        // 不存在队列时，直接调用handler处理任务
        if (StringUtils.isEmpty(config.getAccountId()) && queueHandlers.containsKey(queueName)) {
            queueHandlers.get(queueName).handleMessage(message);
            return;
        }
        try {
            String queueId = getQueueId(queueName);
            if (queueId == null) {
                throw new RuntimeException("Queue ID not found for queue: " + queueName);
            }

            String url = String.format(CLOUDFLARE_API_BASE, config.getAccountId()) + "/" + queueId + "/messages";
            HttpPost request = new HttpPost(url);
            request.setHeader("Authorization", "Bearer " + config.getApiToken());
            request.setHeader("Content-Type", "application/json");

            JSONObject payload = new JSONObject();
            JSONObject messageBody = new JSONObject();
            messageBody.put("content", message);
            messageBody.put("timestamp", System.currentTimeMillis());
            payload.put("body", messageBody);
            String jsonPayload = payload.toJSONString();
            request.setEntity(new StringEntity(jsonPayload, StandardCharsets.UTF_8));

            try (CloseableHttpResponse response = httpClient.execute(request)) {
                String responseBody = EntityUtils.toString(response.getEntity());
                if (response.getStatusLine().getStatusCode() != 200) {
                    throw new RuntimeException("Failed to send message: " + responseBody);
                }
                log.info("Successfully sent message to queue {}: {}", queueName, message);
            }
        } catch (Exception e) {
            log.error("Failed to send message to queue {}: {}", queueName, message, e);
            throw new RuntimeException("Failed to send message to queue: " + queueName, e);
        }
    }

    /**
     * 从指定队列接收消息
     * @param queueName 队列名称
     * @return 消息数组
     */
    public JSONArray receiveMessage(String queueName) {
        try {
            String queueId = getQueueId(queueName);
            if (queueId == null) {
                throw new RuntimeException("Queue ID not found for queue: " + queueName);
            }

            String url = String.format(CLOUDFLARE_API_BASE, config.getAccountId()) + "/" + queueId + "/messages/pull";
            HttpPost request = new HttpPost(url);
            request.setHeader("Authorization", "Bearer " + config.getApiToken());
            request.setHeader("Content-Type", "application/json");

            JSONObject payload = new JSONObject();
            payload.put("visibility_timeout", 10000);
            payload.put("batch_size", 2);
            String jsonPayload = payload.toJSONString();
            request.setEntity(new StringEntity(jsonPayload, StandardCharsets.UTF_8));

            try (CloseableHttpResponse response = httpClient.execute(request)) {
                String responseBody = EntityUtils.toString(response.getEntity());

                if (response.getStatusLine().getStatusCode() != 200) {
                    log.error("Failed to receive message. Status: {}, Response: {}",
                        response.getStatusLine().getStatusCode(), responseBody);
                    return null;
                }

                JSONObject responseMap = JSONObject.parseObject(responseBody);
                if (responseMap.containsKey("result")) {
                    JSONObject result = responseMap.getJSONObject("result");
                    if (result.containsKey("messages") && !result.getJSONArray("messages").isEmpty()) {
                        JSONArray messages = result.getJSONArray("messages");
                        log.info("Successfully parsed messages body from queue {}: {}", queueName, messages);
                        return messages;
                    }
                }
                return null;
            }
        } catch (Exception e) {
            log.error("Failed to receive message from queue {}", queueName, e);
            throw new RuntimeException("Failed to receive message from queue: " + queueName, e);
        }
    }

    /**
     * 确认消息已处理
     * @param queueName 队列名称
     * @param leaseId 租约ID
     */
    public void acknowledgeMessage(String queueName, String leaseId) {
        try {
            String queueId = getQueueId(queueName);
            if (queueId == null) {
                throw new RuntimeException("Queue ID not found for queue: " + queueName);
            }

            String url = String.format(CLOUDFLARE_API_BASE, config.getAccountId()) + "/" + queueId + "/messages/ack";
            HttpPost request = new HttpPost(url);
            request.setHeader("Authorization", "Bearer " + config.getApiToken());
            request.setHeader("Content-Type", "application/json");

            JSONObject payload = new JSONObject();
            JSONArray acks = new JSONArray();
            JSONObject ack = new JSONObject();
            ack.put("lease_id", leaseId);
            acks.add(ack);
            payload.put("acks", acks);
            String jsonPayload = payload.toJSONString();
            log.info("Acknowledging message with payload: {}", jsonPayload);
            request.setEntity(new StringEntity(jsonPayload, StandardCharsets.UTF_8));

            try (CloseableHttpResponse response = httpClient.execute(request)) {
                String responseBody = EntityUtils.toString(response.getEntity());
                if (response.getStatusLine().getStatusCode() != 200) {
                    log.error("Failed to acknowledge message. Response: {}", responseBody);
                    throw new RuntimeException("Failed to acknowledge message: " + responseBody);
                }
                log.info("Successfully acknowledged message with lease_id {} from queue {}", leaseId, queueName);
            }
        } catch (Exception e) {
            log.error("Failed to acknowledge message with lease_id {} from queue {}", leaseId, queueName, e);
            throw new RuntimeException("Failed to acknowledge message", e);
        }
    }

    /**
     * 获取所有注册的队列处理器
     * @return 队列处理器映射
     */
    public Map<String, QueueMessageHandler> getQueueHandlers() {
        // 未配置表示在开发环境，不需要处理队列任务
        if (StringUtils.isEmpty(config.getAccountId())) {
            return MapUtil.empty();
        }
        return queueHandlers;
    }
}
