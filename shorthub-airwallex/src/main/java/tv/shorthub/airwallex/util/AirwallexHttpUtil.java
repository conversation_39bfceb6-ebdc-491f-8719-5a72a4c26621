package tv.shorthub.airwallex.util;

import com.alibaba.fastjson2.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.*;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;
import tv.shorthub.airwallex.constant.AirwallexConstants;
import tv.shorthub.airwallex.model.AirwallexResponse;
import tv.shorthub.common.exception.ApiException;

import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.HashMap;

/**
 * Airwallex HTTP工具类
 */
@Slf4j
public class AirwallexHttpUtil {

    private static final RestTemplate restTemplate = new RestTemplate();

    /**
     * 发送GET请求
     */
    public static <T> T get(String url, String accessToken, Class<T> responseClass) {
        return get(url, accessToken, null, responseClass);
    }

    /**
     * 发送GET请求
     */
    public static <T> T get(String url, String accessToken, Map<String, String> params, Class<T> responseClass) {
        try {
            HttpHeaders headers = createHeaders(accessToken);
            HttpEntity<String> entity = new HttpEntity<>(headers);

            // 构建URL参数
            if (params != null && !params.isEmpty()) {
                StringBuilder paramBuilder = new StringBuilder();
                for (Map.Entry<String, String> entry : params.entrySet()) {
                    if (paramBuilder.length() > 0) {
                        paramBuilder.append("&");
                    }
                    paramBuilder.append(entry.getKey()).append("=").append(entry.getValue());
                }
                url += "?" + paramBuilder.toString();
            }

            log.info("发送GET请求: {}", url);
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.GET, entity, String.class);
            
            return handleResponse(response, responseClass);
        } catch (Exception e) {
            log.error("GET请求失败: {}", e.getMessage(), e);
            throw new ApiException("GET请求失败: " + e.getMessage());
        }
    }

    /**
     * 发送POST请求
     */
    public static <T> T post(String url, String accessToken, Object requestBody, Class<T> responseClass) {
        try {
            HttpHeaders headers = createHeaders(accessToken);
            String jsonBody = JSON.toJSONString(requestBody);
            HttpEntity<String> entity = new HttpEntity<>(jsonBody, headers);

            log.info("发送POST请求: {}, 请求体: {}", url, jsonBody);
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.POST, entity, String.class);
            
            return handleResponse(response, responseClass);
        } catch (Exception e) {
            log.error("POST请求失败: {}", e.getMessage(), e);
            throw new ApiException("POST请求失败: " + e.getMessage());
        }
    }

    /**
     * 发送PUT请求
     */
    public static <T> T put(String url, String accessToken, Object requestBody, Class<T> responseClass) {
        try {
            HttpHeaders headers = createHeaders(accessToken);
            String jsonBody = JSON.toJSONString(requestBody);
            HttpEntity<String> entity = new HttpEntity<>(jsonBody, headers);

            log.info("发送PUT请求: {}, 请求体: {}", url, jsonBody);
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.PUT, entity, String.class);
            
            return handleResponse(response, responseClass);
        } catch (Exception e) {
            log.error("PUT请求失败: {}", e.getMessage(), e);
            throw new ApiException("PUT请求失败: " + e.getMessage());
        }
    }

    /**
     * 发送DELETE请求
     */
    public static <T> T delete(String url, String accessToken, Class<T> responseClass) {
        try {
            HttpHeaders headers = createHeaders(accessToken);
            HttpEntity<String> entity = new HttpEntity<>(headers);

            log.info("发送DELETE请求: {}", url);
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.DELETE, entity, String.class);
            
            return handleResponse(response, responseClass);
        } catch (Exception e) {
            log.error("DELETE请求失败: {}", e.getMessage(), e);
            throw new ApiException("DELETE请求失败: " + e.getMessage());
        }
    }

    /**
     * 创建请求头
     */
    private static HttpHeaders createHeaders(String accessToken) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set(StandardCharsets.UTF_8.name(), "UTF-8");
        
        if (accessToken != null && !accessToken.isEmpty()) {
            headers.set(AirwallexConstants.Headers.AUTHORIZATION, AirwallexConstants.Headers.BEARER + accessToken);
        }
        
        return headers;
    }

    /**
     * 处理响应
     */
    private static <T> T handleResponse(ResponseEntity<String> response, Class<T> responseClass) {
        String responseBody = response.getBody();
        log.info("响应状态码: {}, 响应体: {}", response.getStatusCode(), responseBody);

        if (response.getStatusCode().is2xxSuccessful()) {
            if (responseClass == String.class) {
                return (T) responseBody;
            }
            return JSON.parseObject(responseBody, responseClass);
        } else {
            // 解析错误响应
            try {
                AirwallexResponse errorResponse = JSON.parseObject(responseBody, AirwallexResponse.class);
                throw new ApiException("API请求失败: " + errorResponse.getMessage());
            } catch (Exception e) {
                throw new ApiException("API请求失败: " + responseBody);
            }
        }
    }

    /**
     * 获取认证Token
     */
    public static String getAuthToken(String baseUrl, String clientId, String clientSecret) {
        try {
            String url = baseUrl + AirwallexConstants.AUTH_PATH;
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            
            // 根据Airwallex API文档，x-client-id和x-api-key应该作为请求头发送
            headers.set("x-client-id", clientId);
            headers.set("x-api-key", clientSecret);
            
            // 请求体可以为空或者发送空的JSON对象
            String authBody = "{}";
            
            HttpEntity<String> entity = new HttpEntity<>(authBody, headers);
            
            log.info("获取认证Token: {}", url);
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.POST, entity, String.class);
            
            if (response.getStatusCode().is2xxSuccessful()) {
                Map<String, Object> responseMap = JSON.parseObject(response.getBody(), Map.class);
                return (String) responseMap.get("token");
            } else {
                throw new ApiException("获取认证Token失败: " + response.getBody());
            }
        } catch (HttpClientErrorException e) {
            log.error("获取认证Token失败: {}", e.getResponseBodyAsString(), e);
            throw new ApiException("获取认证Token失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("获取认证Token失败: {}", e.getMessage(), e);
            throw new ApiException("获取认证Token失败: " + e.getMessage());
        }
    }
} 