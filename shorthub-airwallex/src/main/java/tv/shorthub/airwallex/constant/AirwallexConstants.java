package tv.shorthub.airwallex.constant;

/**
 * Airwallex API 常量
 */
public class AirwallexConstants {

    /**
     * 正式环境API域名
     */
    public static final String PROD_API_BASE_URL = "https://api.airwallex.com";

    /**
     * 测试环境API域名
     */
    public static final String DEMO_API_BASE_URL = "https://api-demo.airwallex.com";

    /**
     * API版本
     */
    public static final String API_VERSION = "2021-04-06";

    /**
     * 认证接口路径
     */
    public static final String AUTH_PATH = "/api/v1/authentication/login";

    /**
     * 支付意向创建接口路径
     */
    public static final String PAYMENT_INTENT_CREATE_PATH = "/api/v1/pa/payment_intents/create";

    /**
     * 支付意向确认接口路径
     */
    public static final String PAYMENT_INTENT_CONFIRM_PATH = "/api/v1/pa/payment_intents/%s/confirm";

    /**
     * 支付意向查询接口路径
     */
    public static final String PAYMENT_INTENT_QUERY_PATH = "/api/v1/pa/payment_intents/%s";

    /**
     * 支付意向取消接口路径
     */
    public static final String PAYMENT_INTENT_CANCEL_PATH = "/api/v1/pa/payment_intents/%s/cancel";

    /**
     * 支付意向捕获接口路径
     */
    public static final String PAYMENT_INTENT_CAPTURE_PATH = "/api/v1/pa/payment_intents/%s/capture";

    /**
     * 客户创建接口路径
     */
    public static final String CUSTOMER_CREATE_PATH = "/api/v1/pa/customers/create";

    /**
     * 客户生成客户端密钥接口路径
     */
    public static final String CUSTOMER_GENERATE_CLIENT_SECRET_PATH = "/api/v1/pa/customers/%s/generate_client_secret";

    /**
     * 客户查询接口路径
     */
    public static final String CUSTOMER_QUERY_PATH = "/api/v1/pa/customers/%s";

    /**
     * 客户查询接口路径
     */
    public static final String CUSTOMER_QUERY_LIST_PATH = "/api/v1/pa/customers";

    /**
     * 支付方式创建接口路径
     */
    public static final String PAYMENT_METHOD_PATH = "/api/v1/pa/payment_methods";

    /**
     * 支付方式查询接口路径
     */
    public static final String PAYMENT_METHOD_QUERY_PATH = "/api/v1/pa/payment_methods/%s";

    /**
     * 退款创建接口路径
     */
    public static final String REFUND_PATH = "/api/v1/pa/refunds";

    /**
     * 退款查询接口路径
     */
    public static final String REFUND_QUERY_PATH = "/api/v1/pa/refunds/%s";

    /**
     * 支付同意书创建接口路径
     */
    public static final String PAYMENT_CONSENT_CREATE_PATH = "/api/v1/pa/payment_consents/create";

    /**
     * 支付同意书更新接口路径
     */
    public static final String PAYMENT_CONSENT_UPDATE_PATH = "/api/v1/pa/payment_consents/%s/update";

    /**
     * 支付同意书验证接口路径
     */
    public static final String PAYMENT_CONSENT_VERIFY_PATH = "/api/v1/pa/payment_consents/%s/verify";

    /**
     * 支付同意书验证继续接口路径
     */
    public static final String PAYMENT_CONSENT_VERIFY_CONTINUE_PATH = "/api/v1/pa/payment_consents/%s/verify_continue";

    /**
     * 支付同意书禁用接口路径
     */
    public static final String PAYMENT_CONSENT_DISABLE_PATH = "/api/v1/pa/payment_consents/%s/disable";

    /**
     * 支付同意书查询接口路径
     */
    public static final String PAYMENT_CONSENT_QUERY_PATH = "/api/v1/pa/payment_consents/%s";

    /**
     * 支付同意书列表接口路径
     */
    public static final String PAYMENT_CONSENT_LIST_PATH = "/api/v1/pa/payment_consents";

    /**
     * 支付方式类型
     */
    public static final class PaymentMethodType {
        public static final String CARD = "card";
        public static final String WECHAT = "wechatpay";
        public static final String ALIPAY = "alipay";
        public static final String AIRWALLEX_PAY = "airwallex_pay";
        public static final String APPLE_PAY = "applepay";
        public static final String GOOGLE_PAY = "googlepay";
        public static final String PAYPAL = "paypal";
    }

    /**
     * 支付意向状态
     */
    public static final class PaymentIntentStatus {
        public static final String REQUIRES_PAYMENT_METHOD = "requires_payment_method";
        public static final String REQUIRES_CUSTOMER_ACTION = "requires_customer_action";
        public static final String REQUIRES_CONFIRMATION = "requires_confirmation";
        public static final String REQUIRES_CAPTURE = "requires_capture";
        public static final String SUCCEEDED = "succeeded";
        public static final String CANCELLED = "cancelled";
        public static final String FAILED = "failed";
    }

    /**
     * 订阅状态
     */
    public static final class SubscriptionStatus {
        public static final String ACTIVE = "active";
        public static final String CANCELLED = "cancelled";
        public static final String INCOMPLETE = "incomplete";
        public static final String PAST_DUE = "past_due";
        public static final String TRIALING = "trialing";
        public static final String UNPAID = "unpaid";
        public static final String PAUSED = "paused";
    }

    /**
     * 货币代码
     */
    public static final class Currency {
        public static final String USD = "USD";
        public static final String EUR = "EUR";
        public static final String GBP = "GBP";
        public static final String CNY = "CNY";
        public static final String JPY = "JPY";
        public static final String HKD = "HKD";
        public static final String SGD = "SGD";
        public static final String AUD = "AUD";
        public static final String CAD = "CAD";
        public static final String CHF = "CHF";
        public static final String NZD = "NZD";
        public static final String THB = "THB";
        public static final String KRW = "KRW";
        public static final String MXN = "MXN";
    }

    /**
     * 确认类型
     */
    public static final class ConfirmationType {
        public static final String AUTOMATIC = "automatic";
        public static final String MANUAL = "manual";
    }

    /**
     * 捕获方式
     */
    public static final class CaptureMethod {
        public static final String AUTOMATIC = "automatic";
        public static final String MANUAL = "manual";
    }

    /**
     * 间隔类型
     */
    public static final class IntervalType {
        public static final String DAY = "day";
        public static final String WEEK = "week";
        public static final String MONTH = "month";
        public static final String YEAR = "year";
    }

    /**
     * 未来使用设置 - 用于订阅和递归支付
     */
    public static final class SetupFutureUsage {
        public static final String ON_SESSION = "on_session";
        public static final String OFF_SESSION = "off_session";
        public static final String NONE = "none";
    }

    /**
     * 三方验证设置
     */
    public static final class ThreeDSecure {
        public static final String AUTOMATIC = "automatic";
        public static final String REQUIRED = "required";
        public static final String OPTIONAL = "optional";
    }

    /**
     * 错误代码
     */
    public static final class ErrorCode {
        public static final String INVALID_REQUEST = "invalid_request";
        public static final String AUTHENTICATION_FAILED = "authentication_failed";
        public static final String PAYMENT_FAILED = "payment_failed";
        public static final String INSUFFICIENT_FUNDS = "insufficient_funds";
        public static final String CARD_DECLINED = "card_declined";
        public static final String EXPIRED_CARD = "expired_card";
        public static final String INVALID_CARD = "invalid_card";
        public static final String PROCESSING_ERROR = "processing_error";
        public static final String TRANSACTION_DECLINED = "transaction_declined";
        public static final String INVALID_AMOUNT = "invalid_amount";
        public static final String DUPLICATE_TRANSACTION = "duplicate_transaction";
    }

    /**
     * 支付同意书状态
     */
    public static final class PaymentConsentStatus {
        public static final String PENDING_VERIFICATION = "pending_verification";
        public static final String VERIFIED = "verified";
        public static final String FAILED = "failed";
        public static final String DISABLED = "disabled";
        public static final String EXPIRED = "expired";
    }

    /**
     * 支付同意书类型
     */
    public static final class PaymentConsentType {
        public static final String RECURRING = "recurring";
        public static final String ONE_OFF = "one_off";
        public static final String UNSCHEDULED = "unscheduled";
    }

    /**
     * 支付同意书验证类型
     */
    public static final class PaymentConsentVerificationType {
        public static final String MICRODEPOSITS = "microdeposits";
        public static final String INSTANT = "instant";
        public static final String SKIP = "skip";
    }

    /**
     * Webhook事件类型
     */
    public static final class WebhookEventType {
        public static final String PAYMENT_INTENT_SUCCEEDED = "payment_intent.succeeded";
        public static final String PAYMENT_INTENT_FAILED = "payment_intent.failed";
        public static final String PAYMENT_INTENT_CANCELLED = "payment_intent.cancelled";
        public static final String PAYMENT_INTENT_CREATED = "payment_intent.created";
        public static final String PAYMENT_INTENT_REQUIRES_ACTION = "payment_intent.requires_action";
        public static final String PAYMENT_CONSENT_VERIFIED = "payment_consent.verified";
        public static final String PAYMENT_CONSENT_FAILED = "payment_consent.failed";
        public static final String PAYMENT_CONSENT_DISABLED = "payment_consent.disabled";
    }

    /**
     * 请求头
     */
    public static final class Headers {
        public static final String AUTHORIZATION = "Authorization";
        public static final String CONTENT_TYPE = "Content-Type";
        public static final String CLIENT_SECRET = "x-client-secret";
        public static final String API_KEY = "x-api-key";
        public static final String BEARER = "Bearer ";
    }
} 