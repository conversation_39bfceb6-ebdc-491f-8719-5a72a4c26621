package tv.shorthub.airwallex.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import tv.shorthub.common.utils.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * Airwallex多账户配置
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "airwallex")
@Slf4j
public class AirwallexMultiClientConfig {

    /**
     * 默认账户ID
     */
    private String defaultClientId;

    /**
     * 配置
     */
    private Map<String, ClientConfig> configs;

    /**
     * 获取默认账户配置
     */
    public ClientConfig getDefaultClient() {
        if (defaultClientId != null && configs != null) {
            return configs.get(defaultClientId);
        }
        return null;
    }

    /**
     * 获取指定账户配置
     */
    public ClientConfig getClient(String clientId) {
        if (configs == null) {
            return null;
        }
        return configs.get(clientId);
    }

    public void registry(ClientConfig config) {
        if (null == configs) {
            configs = new HashMap<>();
        }
        configs.put(config.getClientId(), config);
        log.info("Registry airwallex client: {}", config);
    }

    /**
     * 账户配置
     */
    @Data
    public static class ClientConfig {


        /**
         * 客户端ID
         */
        private String clientId;

        /**
         * API密钥
         */
        private String apiKey;

        /**
         * API基础URL
         */
        private String apiBaseUrl;

        /**
         * webhookSecret
         */
        private String webhookSecret;

        /**
         * 是否为测试环境
         */
        private boolean sandbox = true;

        /**
         * 获取实际的API基础URL
         */
        public String getActualApiBaseUrl() {
            if (apiBaseUrl != null && !apiBaseUrl.isEmpty()) {
                return apiBaseUrl;
            }
            return sandbox ? "https://api-demo.airwallex.com" : "https://api.airwallex.com";
        }
    }
} 