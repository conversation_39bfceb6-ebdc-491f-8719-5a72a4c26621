package tv.shorthub.airwallex.service;

public class AirwallexConfigStorageHolder {
    private static final ThreadLocal<String> THREAD_LOCAL = new ThreadLocal<String>() {
        protected String initialValue() {
            return "default";
        }
    };

    public static String get() {
        return (String)THREAD_LOCAL.get();
    }

    public static void set(String label) {
        THREAD_LOCAL.set(label);
    }

    public static void remove() {
        THREAD_LOCAL.remove();
    }
}
