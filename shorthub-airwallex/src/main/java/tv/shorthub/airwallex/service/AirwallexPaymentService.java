package tv.shorthub.airwallex.service;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import tv.shorthub.airwallex.constant.AirwallexConstants;
import tv.shorthub.airwallex.model.PaymentIntentRequest;
import tv.shorthub.airwallex.util.AirwallexHttpUtil;
import tv.shorthub.common.exception.ApiException;
import tv.shorthub.common.utils.StringUtils;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/**
 * Airwallex支付服务
 */
@Slf4j
@Service
public class AirwallexPaymentService extends BaseAirwallexService {

    public JSONObject queryCustomer(String customerId) {
        try {
            String accessToken = getAccessToken();
            String url = buildApiUrl(String.format(AirwallexConstants.CUSTOMER_QUERY_PATH, customerId));

            JSONObject response = AirwallexHttpUtil.get(url, accessToken, JSONObject.class);
            log.info("查询客户信息: customerId={}, res: {}", customerId, response);

            return response;
        } catch (Exception e) {
            log.error("查询客户信息失败: customerId={}, error={}", customerId, e.getMessage(), e);
            throw new ApiException("查询客户信息失败: " + e.getMessage());
        }
    }


    public JSONObject queryCustomerList(String merchant_customer_id) {
        try {
            String accessToken = getAccessToken();
            String url = buildApiUrl(AirwallexConstants.CUSTOMER_QUERY_LIST_PATH) + "?merchant_customer_id=" + merchant_customer_id;

            JSONObject response = AirwallexHttpUtil.get(url, accessToken, JSONObject.class);
            log.info("查询客户信息: merchant_customer_id={}, res: {}", merchant_customer_id, response);

            return response;
        } catch (Exception e) {
            log.error("查询客户信息失败: merchant_customer_id={}, error={}", merchant_customer_id, e.getMessage(), e);
            throw new ApiException("查询客户信息失败: " + e.getMessage());
        }
    }

    /**
     * 创建客户
     */
    public JSONObject createCustomer(String merchantCustomerId) {
        try {
            String accessToken = getAccessToken();
            String url = buildApiUrl(AirwallexConstants.CUSTOMER_CREATE_PATH);

            JSONObject request = new JSONObject();
            request.put("request_id", IdUtil.getSnowflakeNextIdStr());
            request.put("merchant_customer_id", merchantCustomerId);

            JSONObject response = AirwallexHttpUtil.post(url, accessToken, request, JSONObject.class);
            log.info("创建客户成功: merchantCustomerId={}, customerId={}", merchantCustomerId, response.getString("id"));
            
            return response;
        } catch (Exception e) {
            log.error("创建客户失败: merchantCustomerId={}, error={}", merchantCustomerId, e.getMessage(), e);
            throw new ApiException("创建客户失败: " + e.getMessage());
        }
    }

    /** 
     * 生成客户端密钥
     */
    public JSONObject generateClientSecret(String customerId) {
        try {
            String accessToken = getAccessToken();
            String url = buildApiUrl(String.format(AirwallexConstants.CUSTOMER_GENERATE_CLIENT_SECRET_PATH, customerId));

            JSONObject response = AirwallexHttpUtil.get(url, accessToken, JSONObject.class);
            log.info("生成客户端密钥成功: customerId={}, clientSecret={}", customerId, response.getString("client_secret"));

            return response;
        } catch (Exception e) {
            log.error("生成客户端密钥失败: customerId={}, error={}", customerId, e.getMessage(), e);
            throw new ApiException("生成客户端密钥失败: " + e.getMessage());
        }
    }

    /**
     * 创建支付意向
     */
    public JSONObject createPaymentIntent(PaymentIntentRequest request) {
        try {
            String accessToken = getAccessToken();
            String url = buildApiUrl(AirwallexConstants.PAYMENT_INTENT_CREATE_PATH);

            // 构建请求体
            Map<String, Object> requestBody = buildPaymentIntentRequestBody(request);

            JSONObject response = AirwallexHttpUtil.post(url, accessToken, requestBody, JSONObject.class);
            log.info("创建支付意向成功: merchantOrderId={}, paymentIntentId={}", 
                request.getMerchantOrderId(), response.getString("id"));
            
            return response;
        } catch (Exception e) {
            log.error("创建支付意向失败: merchantOrderId={}, error={}", 
                request.getMerchantOrderId(), e.getMessage(), e);
            throw new ApiException("创建支付意向失败: " + e.getMessage());
        }
    }

    /**
     * 确认支付意向
     */
    public JSONObject confirmPaymentIntent(String paymentIntentId, String payment_consent_id) {
        try {
            String accessToken = getAccessToken();
            String url = buildApiUrl(String.format(AirwallexConstants.PAYMENT_INTENT_CONFIRM_PATH, paymentIntentId));

            JSONObject requestBody = new JSONObject();
            if (StringUtils.isNotEmpty(payment_consent_id)) {
                requestBody.put("payment_consent_id", payment_consent_id);
            }
            requestBody.put("request_id", IdUtil.getSnowflakeNextIdStr());

            JSONObject response = AirwallexHttpUtil.post(url, accessToken, requestBody, JSONObject.class);
            log.info("确认支付意向成功: response={}", response);
            
            return response;
        } catch (Exception e) {
            log.error("确认支付意向失败: paymentIntentId={}, error={}", paymentIntentId, e.getMessage(), e);
            throw new ApiException("确认支付意向失败: " + e.getMessage());
        }
    }

    /**
     * 查询支付意向
     */
    public JSONObject queryPaymentIntent(String paymentIntentId) {
        try {
            String accessToken = getAccessToken();
            String url = buildApiUrl(String.format(AirwallexConstants.PAYMENT_INTENT_QUERY_PATH, paymentIntentId));

            JSONObject response = AirwallexHttpUtil.get(url, accessToken, JSONObject.class);
            log.info("查询支付意向成功: paymentIntentId={}, status={}", 
                paymentIntentId, response.getString("status"));
            
            return response;
        } catch (Exception e) {
            log.error("查询支付意向失败: paymentIntentId={}, error={}", paymentIntentId, e.getMessage(), e);
            throw new ApiException("查询支付意向失败: " + e.getMessage());
        }
    }

    /**
     * 取消支付意向
     */
    public JSONObject cancelPaymentIntent(String paymentIntentId) {
        try {
            String accessToken = getAccessToken();
            String url = buildApiUrl(String.format(AirwallexConstants.PAYMENT_INTENT_CANCEL_PATH, paymentIntentId));

            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("cancellation_reason", "user_cancelled");

            JSONObject response = AirwallexHttpUtil.post(url, accessToken, requestBody, JSONObject.class);
            log.info("取消支付意向成功: paymentIntentId={}", paymentIntentId);
            
            return response;
        } catch (Exception e) {
            log.error("取消支付意向失败: paymentIntentId={}, error={}", paymentIntentId, e.getMessage(), e);
            throw new ApiException("取消支付意向失败: " + e.getMessage());
        }
    }

    /**
     * 创建Drop-in支付意向
     */
    public JSONObject createDropInPaymentIntent(PaymentIntentRequest request) {
        // 设置Drop-in模式的默认参数
        request.setConfirmationType(AirwallexConstants.ConfirmationType.MANUAL);
        request.setPaymentMethodTypes(Collections.singletonList(AirwallexConstants.PaymentMethodType.CARD));
        
        // 设置支付方式选项
        PaymentIntentRequest.PaymentMethodOptions options = new PaymentIntentRequest.PaymentMethodOptions();
        PaymentIntentRequest.CardOptions cardOptions = new PaymentIntentRequest.CardOptions();
        cardOptions.setAutoCapture(true);
        cardOptions.setAllowSavePaymentMethod(true);
        options.setCard(cardOptions);
        request.setPaymentMethodOptions(options);

        return createPaymentIntent(request);
    }

    /**
     * 创建订阅支付意向（递归支付）
     */
    public JSONObject createSubscriptionPaymentIntent(PaymentIntentRequest request) {
        // 设置订阅模式的默认参数
        request.setConfirmationType(AirwallexConstants.ConfirmationType.MANUAL);
        request.setSetupFutureUsage(AirwallexConstants.SetupFutureUsage.OFF_SESSION);
        request.setOffSession(false); // 首次支付为在线会话
        
        // 设置支付方式选项
        PaymentIntentRequest.PaymentMethodOptions options = new PaymentIntentRequest.PaymentMethodOptions();
        PaymentIntentRequest.CardOptions cardOptions = new PaymentIntentRequest.CardOptions();
        cardOptions.setAutoCapture(true);
        cardOptions.setAllowSavePaymentMethod(true);
        cardOptions.setThreeDSecure(AirwallexConstants.ThreeDSecure.AUTOMATIC);
        options.setCard(cardOptions);
        request.setPaymentMethodOptions(options);

        return createPaymentIntent(request);
    }

    /**
     * 创建递归支付意向（后续扣款）
     */
    public JSONObject createRecurringPaymentIntent(PaymentIntentRequest request) {
        // 设置递归支付的默认参数
        request.setConfirmationType(AirwallexConstants.ConfirmationType.AUTOMATIC);
        request.setSetupFutureUsage(AirwallexConstants.SetupFutureUsage.OFF_SESSION);
        request.setOffSession(true); // 后续支付为离线会话
        
        // 设置支付方式选项
        PaymentIntentRequest.PaymentMethodOptions options = new PaymentIntentRequest.PaymentMethodOptions();
        PaymentIntentRequest.CardOptions cardOptions = new PaymentIntentRequest.CardOptions();
        cardOptions.setAutoCapture(true);
        cardOptions.setThreeDSecure(AirwallexConstants.ThreeDSecure.OPTIONAL);
        options.setCard(cardOptions);
        request.setPaymentMethodOptions(options);

        return createPaymentIntent(request);
    }

    /**
     * 构建支付意向请求体
     */
    private Map<String, Object> buildPaymentIntentRequestBody(PaymentIntentRequest request) {
        Map<String, Object> requestBody = new HashMap<>();
        
        // 基本信息
        requestBody.put("amount", request.getAmount());
        requestBody.put("currency", request.getCurrency());
        requestBody.put("merchant_order_id", request.getMerchantOrderId());
        requestBody.put("description", request.getDescription());
        requestBody.put("request_id", IdUtil.getSnowflakeNextIdStr());

        // 支付方式
        if (request.getPaymentMethodTypes() != null && !request.getPaymentMethodTypes().isEmpty()) {
            requestBody.put("payment_method_types", request.getPaymentMethodTypes());
        }
        
        // 确认类型
        if (request.getConfirmationType() != null) {
            requestBody.put("confirmation_type", request.getConfirmationType());
        }
        
        // 捕获方式
        if (request.getCaptureMethod() != null) {
            requestBody.put("capture_method", request.getCaptureMethod());
        }
        
        // 未来使用设置 - 用于订阅
        if (request.getSetupFutureUsage() != null) {
            requestBody.put("setup_future_usage", request.getSetupFutureUsage());
        }
        
        // 是否为离线会话 - 用于递归支付
        if (request.getOffSession() != null) {
            requestBody.put("off_session", request.getOffSession());
        }
        
        // 客户信息
        if (request.getCustomer() != null) {
            requestBody.put("customer", JSON.toJSON(request.getCustomer()));
        }
        
        // 地址信息
        if (request.getShippingAddress() != null) {
            requestBody.put("shipping_address", JSON.toJSON(request.getShippingAddress()));
        }
        
        if (request.getBillingAddress() != null) {
            requestBody.put("billing_address", JSON.toJSON(request.getBillingAddress()));
        }
        
        // 支付方式选项
        if (request.getPaymentMethodOptions() != null) {
            requestBody.put("payment_method_options", JSON.toJSON(request.getPaymentMethodOptions()));
        }
        
        // 订阅间隔信息
        if (request.getSubscriptionInterval() != null) {
            Map<String, Object> intervalMap = new HashMap<>();
            intervalMap.put("type", request.getSubscriptionInterval().getType());
            intervalMap.put("count", request.getSubscriptionInterval().getCount());
            if (request.getSubscriptionInterval().getTrialDays() != null) {
                intervalMap.put("trial_days", request.getSubscriptionInterval().getTrialDays());
            }
            if (request.getSubscriptionInterval().getNextChargeAt() != null) {
                intervalMap.put("next_charge_at", request.getSubscriptionInterval().getNextChargeAt().toString());
            }
            requestBody.put("subscription_interval", intervalMap);
        }
        
        // 回调地址
        if (request.getReturnUrl() != null) {
            requestBody.put("return_url", request.getReturnUrl());
        }
        
        if (request.getCancelUrl() != null) {
            requestBody.put("cancel_url", request.getCancelUrl());
        }
        
        // 元数据
        if (request.getMetadata() != null) {
            requestBody.put("metadata", request.getMetadata());
        }
        
        return requestBody;
    }
} 