package tv.shorthub.airwallex.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tv.shorthub.airwallex.config.AirwallexMultiClientConfig;
import tv.shorthub.airwallex.util.AirwallexHttpUtil;
import tv.shorthub.common.core.cache.CacheKeyUtils;
import tv.shorthub.common.core.redis.RedisCache;
import tv.shorthub.common.exception.ApiException;

import java.util.concurrent.TimeUnit;

/**
 * Airwallex基础服务类
 */
@Slf4j
@Service
public class BaseAirwallexService {

    @Autowired
    private AirwallexMultiClientConfig multiClientConfig;

    @Autowired
    private RedisCache redisCache;

    /**
     * Token缓存过期时间（秒）
     */
    private static final int TOKEN_EXPIRE_SECONDS = 60;

    /**
     * 获取访问令牌
     */
    public String getAccessToken() {
        return getAccessToken(AirwallexConfigStorageHolder.get());
    }

    /**
     * 获取访问令牌
     */
    private String getAccessToken(String clientId) {
        AirwallexMultiClientConfig.ClientConfig client = getClientConfig(clientId);
        if (client == null) {
            throw new ApiException("未找到有效的Airwallex客户端配置");
        }

        // 从缓存获取Token
        String cacheKey = CacheKeyUtils.getAirwallexToken(client.getClientId());
        String cachedToken = redisCache.getCacheObject(cacheKey);
        if (cachedToken != null) {
            log.debug("从缓存获取Airwallex访问令牌: clientId={}", client.getClientId());
            return cachedToken;
        }

        // 重新获取Token
        try {
            String token = AirwallexHttpUtil.getAuthToken(
                client.getActualApiBaseUrl(),
                client.getClientId(),
                client.getApiKey()
            );

            // 缓存Token
            redisCache.setCacheObject(cacheKey, token, TOKEN_EXPIRE_SECONDS, TimeUnit.SECONDS);
            log.info("成功获取Airwallex访问令牌: clientId={}", client.getClientId());
            return token;
        } catch (Exception e) {
            log.error("获取Airwallex访问令牌失败: clientId={}, error={}", client.getClientId(), e.getMessage(), e);
            throw new ApiException("获取Airwallex访问令牌失败: " + e.getMessage());
        }
    }

    public AirwallexMultiClientConfig.ClientConfig getClientConfig() {
        return getClientConfig(AirwallexConfigStorageHolder.get());
    }

    /**
     * 获取客户端配置
     */
    protected AirwallexMultiClientConfig.ClientConfig getClientConfig(String clientId) {
        if (clientId == null || clientId.isEmpty()) {
            return multiClientConfig.getDefaultClient();
        }
        return multiClientConfig.getClient(clientId);
    }

    /**
     * 构建API URL
     */
    protected String buildApiUrl(String path) {
        AirwallexMultiClientConfig.ClientConfig client = getClientConfig(AirwallexConfigStorageHolder.get());
        if (client == null) {
            throw new ApiException("未找到有效的Airwallex客户端配置");
        }
        return client.getActualApiBaseUrl() + path;
    }

    /**
     * 清除访问令牌缓存
     */
    public void clearAccessTokenCache(String clientId) {
        AirwallexMultiClientConfig.ClientConfig client = getClientConfig(clientId);
        if (client != null) {
            redisCache.deleteObject(CacheKeyUtils.getAirwallexToken(client.getClientId()));
            log.info("已清除Airwallex访问令牌缓存: clientId={}", client.getClientId());
        }
    }
} 