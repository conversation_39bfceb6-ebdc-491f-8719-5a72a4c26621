package tv.shorthub.airwallex.service;

import com.alibaba.fastjson2.JSONObject;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import tv.shorthub.airwallex.config.AirwallexMultiClientConfig;
import tv.shorthub.common.exception.ApiException;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.Base64;

/**
 * Airwallex Webhook服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AirwallexWebhookService {

    private final AirwallexMultiClientConfig multiClientConfig;
    private final ApplicationEventPublisher eventPublisher;

    /**
     * 处理支付意向创建事件
     */
    public void handlePaymentIntentCreated(JSONObject webhookData) {
        try {
            JSONObject data = webhookData.getJSONObject("data");
            JSONObject paymentIntent = data.getJSONObject("object");  // 实际数据在data.object中
            
            String paymentIntentId = paymentIntent.getString("id");
            String merchantOrderId = paymentIntent.getString("merchant_order_id");
            String amount = paymentIntent.getString("amount");
            String currency = paymentIntent.getString("currency");
            String status = paymentIntent.getString("status");

            log.info("处理支付意向创建事件: paymentIntentId={}, merchantOrderId={}, amount={}, currency={}, status={}", 
                paymentIntentId, merchantOrderId, amount, currency, status);

            // 这里可以添加业务逻辑，比如：
            // 1. 记录支付意向创建
            // 2. 发送通知
            // 3. 更新订单状态
            // 4. 发布事件给其他模块处理

            log.info("支付意向创建事件处理完成: paymentIntentId={}", paymentIntentId);
        } catch (Exception e) {
            log.error("处理支付意向创建事件失败", e);
            throw new ApiException("处理支付意向创建事件失败: " + e.getMessage());
        }
    }

    /**
     * 处理支付意向成功事件
     */
    public void handlePaymentIntentSucceeded(JSONObject webhookData) {
        try {
            JSONObject data = webhookData.getJSONObject("data");
            JSONObject paymentIntent = data.getJSONObject("object");  // 实际数据在data.object中
            
            String paymentIntentId = paymentIntent.getString("id");
            String merchantOrderId = paymentIntent.getString("merchant_order_id");
            String amount = paymentIntent.getString("amount");
            String currency = paymentIntent.getString("currency");
            String status = paymentIntent.getString("status");

            log.info("处理支付意向成功事件: paymentIntentId={}, merchantOrderId={}, amount={}, currency={}, status={}", 
                paymentIntentId, merchantOrderId, amount, currency, status);

            // 这里可以添加业务逻辑，比如：
            // 1. 更新订单状态
            // 2. 发送确认邮件
            // 3. 触发后续业务流程
            // 4. 发布事件给其他模块处理

            // 示例：发布支付成功事件
            // PaymentSuccessEvent event = new PaymentSuccessEvent(paymentIntentId, merchantOrderId, amount, currency);
            // eventPublisher.publishEvent(event);

            log.info("支付意向成功事件处理完成: paymentIntentId={}", paymentIntentId);
        } catch (Exception e) {
            log.error("处理支付意向成功事件失败", e);
            throw new ApiException("处理支付意向成功事件失败: " + e.getMessage());
        }
    }

    /**
     * 处理支付意向失败事件
     */
    public void handlePaymentIntentFailed(JSONObject webhookData) {
        try {
            JSONObject data = webhookData.getJSONObject("data");
            JSONObject paymentIntent = data.getJSONObject("object");  // 实际数据在data.object中
            
            String paymentIntentId = paymentIntent.getString("id");
            String merchantOrderId = paymentIntent.getString("merchant_order_id");
            String status = paymentIntent.getString("status");
            String failureReason = paymentIntent.getString("failure_reason");

            log.info("处理支付意向失败事件: paymentIntentId={}, merchantOrderId={}, status={}, failureReason={}", 
                paymentIntentId, merchantOrderId, status, failureReason);

            // 这里可以添加业务逻辑，比如：
            // 1. 更新订单状态为失败
            // 2. 发送失败通知
            // 3. 记录失败原因
            // 4. 发布事件给其他模块处理

            log.info("支付意向失败事件处理完成: paymentIntentId={}", paymentIntentId);
        } catch (Exception e) {
            log.error("处理支付意向失败事件失败", e);
            throw new ApiException("处理支付意向失败事件失败: " + e.getMessage());
        }
    }

    /**
     * 处理支付意向取消事件
     */
    public void handlePaymentIntentCancelled(JSONObject webhookData) {
        try {
            JSONObject data = webhookData.getJSONObject("data");
            JSONObject paymentIntent = data.getJSONObject("object");  // 实际数据在data.object中
            
            String paymentIntentId = paymentIntent.getString("id");
            String merchantOrderId = paymentIntent.getString("merchant_order_id");
            String status = paymentIntent.getString("status");

            log.info("处理支付意向取消事件: paymentIntentId={}, merchantOrderId={}, status={}", 
                paymentIntentId, merchantOrderId, status);

            // 这里可以添加业务逻辑，比如：
            // 1. 更新订单状态为取消
            // 2. 释放库存
            // 3. 发送取消通知
            // 4. 发布事件给其他模块处理

            log.info("支付意向取消事件处理完成: paymentIntentId={}", paymentIntentId);
        } catch (Exception e) {
            log.error("处理支付意向取消事件失败", e);
            throw new ApiException("处理支付意向取消事件失败: " + e.getMessage());
        }
    }

    /**
     * 处理支付意向需要操作事件
     */
    public void handlePaymentIntentRequiresAction(JSONObject webhookData) {
        try {
            JSONObject data = webhookData.getJSONObject("data");
            JSONObject paymentIntent = data.getJSONObject("object");  // 实际数据在data.object中
            
            String paymentIntentId = paymentIntent.getString("id");
            String merchantOrderId = paymentIntent.getString("merchant_order_id");
            String status = paymentIntent.getString("status");

            log.info("处理支付意向需要操作事件: paymentIntentId={}, merchantOrderId={}, status={}", 
                paymentIntentId, merchantOrderId, status);

            // 这里可以添加业务逻辑，比如：
            // 1. 通知用户进行 3D Secure 验证
            // 2. 发送操作提醒
            // 3. 更新订单状态
            // 4. 发布事件给其他模块处理

            log.info("支付意向需要操作事件处理完成: paymentIntentId={}", paymentIntentId);
        } catch (Exception e) {
            log.error("处理支付意向需要操作事件失败", e);
            throw new ApiException("处理支付意向需要操作事件失败: " + e.getMessage());
        }
    }

    /**
     * 处理支付同意书验证成功事件
     */
    public void handlePaymentConsentVerified(JSONObject webhookData) {
        try {
            JSONObject data = webhookData.getJSONObject("data");
            JSONObject paymentConsent = data.getJSONObject("object");
            
            String paymentConsentId = paymentConsent.getString("id");
            String status = paymentConsent.getString("status");
            String customerId = paymentConsent.getString("customer_id");
            String paymentMethodType = paymentConsent.getString("payment_method_type");
            
            log.info("处理支付同意书验证成功事件: paymentConsentId={}, status={}, customerId={}, paymentMethodType={}", 
                paymentConsentId, status, customerId, paymentMethodType);
            
            // 这里可以根据业务需要处理支付同意书验证成功
            // 例如：更新客户支付方式状态、启用定期付款等
            
        } catch (Exception e) {
            log.error("处理支付同意书验证成功事件失败", e);
        }
    }

    /**
     * 处理支付同意书验证失败事件
     */
    public void handlePaymentConsentFailed(JSONObject webhookData) {
        try {
            JSONObject data = webhookData.getJSONObject("data");
            JSONObject paymentConsent = data.getJSONObject("object");
            
            String paymentConsentId = paymentConsent.getString("id");
            String status = paymentConsent.getString("status");
            String customerId = paymentConsent.getString("customer_id");
            
            // 获取失败原因
            JSONObject lastError = paymentConsent.getJSONObject("last_error");
            String errorCode = lastError != null ? lastError.getString("code") : null;
            String errorMessage = lastError != null ? lastError.getString("message") : null;
            
            log.info("处理支付同意书验证失败事件: paymentConsentId={}, status={}, customerId={}, errorCode={}, errorMessage={}", 
                paymentConsentId, status, customerId, errorCode, errorMessage);
            
            // 这里可以根据业务需要处理支付同意书验证失败
            // 例如：通知用户重新验证、记录失败原因等
            
        } catch (Exception e) {
            log.error("处理支付同意书验证失败事件失败", e);
        }
    }

    /**
     * 处理支付同意书禁用事件
     */
    public void handlePaymentConsentDisabled(JSONObject webhookData) {
        try {
            JSONObject data = webhookData.getJSONObject("data");
            JSONObject paymentConsent = data.getJSONObject("object");
            
            String paymentConsentId = paymentConsent.getString("id");
            String status = paymentConsent.getString("status");
            String customerId = paymentConsent.getString("customer_id");
            
            log.info("处理支付同意书禁用事件: paymentConsentId={}, status={}, customerId={}", 
                paymentConsentId, status, customerId);
            
            // 这里可以根据业务需要处理支付同意书禁用
            // 例如：停止定期付款、通知用户、更新客户状态等
            
        } catch (Exception e) {
            log.error("处理支付同意书禁用事件失败", e);
        }
    }

    /**
     * 验证Webhook签名
     */
    public boolean verifySignature(String payload, String signature, String timestamp) {
        try {
            // 获取默认客户端的Webhook密钥
            AirwallexMultiClientConfig.ClientConfig client = multiClientConfig.getDefaultClient();
            if (client == null || client.getWebhookSecret() == null) {
                log.info("未配置Webhook密钥，跳过签名验证");
                return true;
            }

            // 检查时间戳是否在容忍范围内（5分钟）
            if (timestamp != null) {
                long currentTime = System.currentTimeMillis();
                long webhookTime = Long.parseLong(timestamp);
                long timeDiff = Math.abs(currentTime - webhookTime);
                if (timeDiff > 300000) { // 5分钟
                    log.info("Webhook时间戳超出容忍范围: timeDiff={}ms", timeDiff);
                    return false;
                }
            }

            // 准备待验证的字符串：timestamp + payload
            String valueToDigest = (timestamp != null ? timestamp : "") + payload;

            // 计算HMAC-SHA256签名
            Mac mac = Mac.getInstance("HmacSHA256");
            SecretKeySpec secretKeySpec = new SecretKeySpec(client.getWebhookSecret().getBytes(StandardCharsets.UTF_8), "HmacSHA256");
            mac.init(secretKeySpec);
            byte[] hash = mac.doFinal(valueToDigest.getBytes(StandardCharsets.UTF_8));
            String expectedSignature = bytesToHex(hash);

            // 比较签名
            return MessageDigest.isEqual(signature.getBytes(StandardCharsets.UTF_8), expectedSignature.getBytes(StandardCharsets.UTF_8));
        } catch (Exception e) {
            log.error("验证Webhook签名失败", e);
            return false;
        }
    }

    /**
     * 字节数组转十六进制字符串
     */
    private String bytesToHex(byte[] bytes) {
        StringBuilder result = new StringBuilder();
        for (byte b : bytes) {
            result.append(String.format("%02x", b));
        }
        return result.toString();
    }
} 