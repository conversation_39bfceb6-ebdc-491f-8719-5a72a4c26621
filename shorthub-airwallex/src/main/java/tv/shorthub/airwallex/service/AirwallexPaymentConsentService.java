package tv.shorthub.airwallex.service;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import tv.shorthub.airwallex.constant.AirwallexConstants;
import tv.shorthub.airwallex.model.PaymentConsentRequest;
import tv.shorthub.airwallex.model.PaymentConsentVerifyRequest;
import tv.shorthub.airwallex.util.AirwallexHttpUtil;
import tv.shorthub.common.exception.ApiException;

import java.util.HashMap;
import java.util.Map;

/**
 * Airwallex 支付同意书服务
 */
@Slf4j
@Service
public class AirwallexPaymentConsentService extends BaseAirwallexService {

    /**
     * 创建支付同意书
     */
    public JSONObject createPaymentConsent(PaymentConsentRequest request) {
        try {
            String accessToken = getAccessToken();
            String url = buildApiUrl(AirwallexConstants.PAYMENT_CONSENT_CREATE_PATH);

            // 构建请求体
            Map<String, Object> requestBody = buildPaymentConsentRequestBody(request);

            return AirwallexHttpUtil.post(url, accessToken, requestBody, JSONObject.class);
        } catch (Exception e) {
            throw new ApiException("创建支付同意书失败: " + e.getMessage());
        }
    }

    /**
     * 更新支付同意书
     */
    public JSONObject updatePaymentConsent(String paymentConsentId, PaymentConsentRequest request) {
        try {
            String accessToken = getAccessToken();
            String url = buildApiUrl(String.format(AirwallexConstants.PAYMENT_CONSENT_UPDATE_PATH, paymentConsentId));

            // 构建请求体
            Map<String, Object> requestBody = buildPaymentConsentRequestBody(request);

            JSONObject response = AirwallexHttpUtil.post(url, accessToken, requestBody, JSONObject.class);
            log.info("更新支付同意书成功: paymentConsentId={}, merchantOrderId={}", 
                paymentConsentId, request.getCustomerId());
            
            return response;
        } catch (Exception e) {
            log.error("更新支付同意书失败: paymentConsentId={}, error={}", 
                paymentConsentId, e.getMessage(), e);
            throw new ApiException("更新支付同意书失败: " + e.getMessage());
        }
    }

    /**
     * 验证支付同意书
     */
    public JSONObject verifyPaymentConsent(String paymentConsentId, JSONObject paymentMethod) {
        try {
            String accessToken = getAccessToken();
            String url = buildApiUrl(String.format(AirwallexConstants.PAYMENT_CONSENT_VERIFY_PATH, paymentConsentId));

            // 构建请求体
            JSONObject requestBody = new JSONObject();
            requestBody.put("request_id", IdUtil.getSnowflakeNextIdStr());
            requestBody.put("payment_method", paymentMethod);


            JSONObject response = AirwallexHttpUtil.post(url, accessToken, requestBody, JSONObject.class);
            log.info("验证支付同意书成功: paymentConsentId={}, response={}",
                paymentConsentId, response);
            
            return response;
        } catch (Exception e) {
            log.error("验证支付同意书失败: paymentConsentId={}, error={}", 
                paymentConsentId, e.getMessage(), e);
            throw new ApiException("验证支付同意书失败: " + e.getMessage());
        }
    }

    /**
     * 继续验证支付同意书
     */
    public JSONObject verifyPaymentConsentContinue(String paymentConsentId, PaymentConsentVerifyRequest request) {
        try {
            String accessToken = getAccessToken();
            String url = buildApiUrl(String.format(AirwallexConstants.PAYMENT_CONSENT_VERIFY_CONTINUE_PATH, paymentConsentId));

            // 构建请求体
            Map<String, Object> requestBody = buildPaymentConsentVerifyRequestBody(request);

            JSONObject response = AirwallexHttpUtil.post(url, accessToken, requestBody, JSONObject.class);
            log.info("继续验证支付同意书成功: paymentConsentId={}, verificationType={}", 
                paymentConsentId, request.getVerificationType());
            
            return response;
        } catch (Exception e) {
            log.error("继续验证支付同意书失败: paymentConsentId={}, error={}", 
                paymentConsentId, e.getMessage(), e);
            throw new ApiException("继续验证支付同意书失败: " + e.getMessage());
        }
    }

    /**
     * 禁用支付同意书
     */
    public JSONObject disablePaymentConsent(String paymentConsentId) {
        try {
            String accessToken = getAccessToken();
            String url = buildApiUrl(String.format(AirwallexConstants.PAYMENT_CONSENT_DISABLE_PATH, paymentConsentId));

            JSONObject requestBody = new JSONObject();
            requestBody.put("request_id", IdUtil.getSnowflakeNextIdStr());

            JSONObject response = AirwallexHttpUtil.post(url, accessToken, requestBody, JSONObject.class);
            log.info("禁用支付同意书成功: paymentConsentId={}, status={}", 
                paymentConsentId, response.getString("status"));
            
            return response;
        } catch (Exception e) {
            log.error("禁用支付同意书失败: paymentConsentId={}, error={}", 
                paymentConsentId, e.getMessage(), e);
            throw new ApiException("禁用支付同意书失败: " + e.getMessage());
        }
    }

    /**
     * 查询支付同意书
     */
    public JSONObject queryPaymentConsent(String paymentConsentId) {
        try {
            String accessToken = getAccessToken();
            String url = buildApiUrl(String.format(AirwallexConstants.PAYMENT_CONSENT_QUERY_PATH, paymentConsentId));

            JSONObject response = AirwallexHttpUtil.get(url, accessToken, JSONObject.class);
            log.info("查询支付同意书成功: paymentConsentId={}, status={}", 
                paymentConsentId, response.getString("status"));
            
            return response;
        } catch (Exception e) {
            log.error("查询支付同意书失败: paymentConsentId={}, error={}", 
                paymentConsentId, e.getMessage(), e);
            throw new ApiException("查询支付同意书失败: " + e.getMessage());
        }
    }

    /**
     * 查询支付同意书列表
     */
    public JSONObject listPaymentConsents(String customerId, String status, Integer pageNum, Integer pageSize) {
        try {
            String accessToken = getAccessToken();
            String url = buildApiUrl(AirwallexConstants.PAYMENT_CONSENT_LIST_PATH);

            // 构建查询参数
            Map<String, String> params = new HashMap<>();
            if (customerId != null) {
                params.put("customer_id", customerId);
            }
            if (status != null) {
                params.put("status", status);
            }
            if (pageNum != null) {
                params.put("page_num", String.valueOf(pageNum));
            }
            if (pageSize != null) {
                params.put("page_size", String.valueOf(pageSize));
            }

            JSONObject response = AirwallexHttpUtil.get(url, accessToken, params, JSONObject.class);
            log.info("查询支付同意书列表成功: customerId={}, status={}, pageNum={}, pageSize={}", 
                customerId, status, pageNum, pageSize);
            
            return response;
        } catch (Exception e) {
            log.error("查询支付同意书列表失败: customerId={}, error={}", 
                customerId, e.getMessage(), e);
            throw new ApiException("查询支付同意书列表失败: " + e.getMessage());
        }
    }

    /**
     * 构建支付同意书请求体
     */
    private Map<String, Object> buildPaymentConsentRequestBody(PaymentConsentRequest request) {
        Map<String, Object> requestBody = new HashMap<>();
        
        // 基本信息
        requestBody.put("request_id", IdUtil.getSnowflakeNextIdStr());
        if (request.getCustomerId() != null) {
            requestBody.put("customer_id", request.getCustomerId());
        }
        if (request.getNextTriggeredBy() != null) {
            requestBody.put("next_triggered_by", request.getNextTriggeredBy());
        }
        // 元数据
        if (request.getMetadata() != null) {
            requestBody.put("metadata", request.getMetadata());
        }
        
        return requestBody;
    }

    /**
     * 构建支付同意书验证请求体
     */
    private Map<String, Object> buildPaymentConsentVerifyRequestBody(PaymentConsentVerifyRequest request) {
        Map<String, Object> requestBody = new HashMap<>();
        
        requestBody.put("request_id", IdUtil.getSnowflakeNextIdStr());

        return requestBody;
    }

    /**
     * 创建定期支付同意书（用于订阅）
     */
    public JSONObject createRecurringPaymentConsent(String customerId, String paymentMethodType, 
                                                   String currency, String merchantOrderId) {
        PaymentConsentRequest request = new PaymentConsentRequest()
            .setCustomerId(customerId);

        return createPaymentConsent(request);
    }

    /**
     * 创建一次性支付同意书
     */
    public JSONObject createOneOffPaymentConsent(String customerId, String paymentMethodType, 
                                                String currency, String merchantOrderId) {
        PaymentConsentRequest request = new PaymentConsentRequest()
            .setCustomerId(customerId);

        return createPaymentConsent(request);
    }

    /**
     * 创建非计划支付同意书
     */
    public JSONObject createUnscheduledPaymentConsent(String customerId, String nextTriggeredBy) {
        PaymentConsentRequest request = new PaymentConsentRequest()
            .setCustomerId(customerId)
            .setNextTriggeredBy(nextTriggeredBy)
            ;

        return createPaymentConsent(request);
    }


    /**
     * 根据客户ID查询支付同意书列表
     */
    public JSONObject getPaymentConsentsByCustomer(String customerId) {
        return listPaymentConsents(customerId, null, null, null);
    }

    /**
     * 根据状态查询支付同意书列表
     */
    public JSONObject getPaymentConsentsByStatus(String status) {
        return listPaymentConsents(null, status, null, null);
    }

    /**
     * 查询已验证的支付同意书列表
     */
    public JSONObject getVerifiedPaymentConsents(String customerId) {
        return listPaymentConsents(customerId, AirwallexConstants.PaymentConsentStatus.VERIFIED, null, null);
    }
} 