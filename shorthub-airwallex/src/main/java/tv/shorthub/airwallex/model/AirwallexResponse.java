package tv.shorthub.airwallex.model;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * Airwallex基础响应模型
 */
@Data
@Accessors(chain = true)
public class AirwallexResponse {

    /**
     * 响应码
     */
    private String code;

    /**
     * 响应消息
     */
    private String message;

    /**
     * 请求ID
     */
    private String requestId;

    /**
     * 是否成功
     */
    private boolean success;

    /**
     * 错误详情
     */
    private ErrorDetail error;

    /**
     * 错误详情
     */
    @Data
    @Accessors(chain = true)
    public static class ErrorDetail {
        private String code;
        private String message;
        private String type;
        private String source;
        private String doc_url;
    }
} 