package tv.shorthub.airwallex.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 支付意向请求
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class PaymentIntentRequest extends AirwallexRequest {

    /**
     * 金额
     */
    private BigDecimal amount;

    /**
     * 货币代码
     */
    private String currency;

    /**
     * 商户订单号
     */
    private String merchantOrderId;

    /**
     * 订单描述
     */
    private String description;

    /**
     * 客户ID
     */
    private String customerId;

    /**
     * 支付方式类型
     */
    private List<String> paymentMethodTypes;

    /**
     * 确认类型
     */
    private String confirmationType;

    /**
     * 捕获方式
     */
    private String captureMethod;

    /**
     * 支付成功回调URL
     */
    private String returnUrl;

    /**
     * 支付取消回调URL
     */
    private String cancelUrl;

    /**
     * 元数据
     */
    private Map<String, Object> metadata;

    /**
     * 客户信息
     */
    private CustomerInfo customer;

    /**
     * 送货地址
     */
    private AddressInfo shippingAddress;

    /**
     * 账单地址
     */
    private AddressInfo billingAddress;

    /**
     * 支付方式选项
     */
    private PaymentMethodOptions paymentMethodOptions;

    /**
     * 未来使用设置 - 用于订阅和递归支付
     */
    private String setupFutureUsage;

    /**
     * 是否为离线会话 - 用于递归支付
     */
    private Boolean offSession;

    /**
     * 订阅间隔 - 用于自动递归支付
     */
    private SubscriptionInterval subscriptionInterval;

    /**
     * 客户信息
     */
    @Data
    @Accessors(chain = true)
    public static class CustomerInfo {
        /**
         * 客户ID
         */
        private String id;

        /**
         * 邮箱
         */
        private String email;

        /**
         * 名字
         */
        private String firstName;

        /**
         * 姓氏
         */
        private String lastName;

        /**
         * 电话
         */
        private String phoneNumber;
    }

    /**
     * 地址信息
     */
    @Data
    @Accessors(chain = true)
    public static class AddressInfo {
        /**
         * 国家代码
         */
        private String countryCode;

        /**
         * 州/省
         */
        private String state;

        /**
         * 城市
         */
        private String city;

        /**
         * 街道地址
         */
        private String street;

        /**
         * 邮编
         */
        private String postcode;
    }

    /**
     * 支付方式选项
     */
    @Data
    @Accessors(chain = true)
    public static class PaymentMethodOptions {
        /**
         * 卡片选项
         */
        private CardOptions card;
    }

    /**
     * 卡片选项
     */
    @Data
    @Accessors(chain = true)
    public static class CardOptions {
        /**
         * 是否自动捕获
         */
        private Boolean autoCapture;

        /**
         * 是否允许保存支付方式
         */
        private Boolean allowSavePaymentMethod;

        /**
         * 三方验证设置
         */
        private String threeDSecure;
    }

    /**
     * 订阅间隔信息
     */
    @Data
    @Accessors(chain = true)
    public static class SubscriptionInterval {
        /**
         * 间隔类型
         */
        private String type;

        /**
         * 间隔数量
         */
        private Integer count;

        /**
         * 试用期（天数）
         */
        private Integer trialDays;

        /**
         * 下次扣款时间
         */
        private LocalDateTime nextChargeAt;
    }
} 