package tv.shorthub.airwallex.model;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * Airwallex基础请求模型
 */
@Data
@Accessors(chain = true)
public class AirwallexRequest {

    /**
     * 请求ID
     */
    private String requestId;

    /**
     * 账户ID
     */
    private String accountId;

    /**
     * 客户端密钥
     */
    private String clientSecret;

    /**
     * API密钥
     */
    private String apiKey;

    /**
     * 访问令牌
     */
    private String accessToken;
} 