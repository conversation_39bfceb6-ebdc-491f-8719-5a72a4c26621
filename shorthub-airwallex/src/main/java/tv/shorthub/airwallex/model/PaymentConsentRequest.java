package tv.shorthub.airwallex.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * 支付同意书请求
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class PaymentConsentRequest extends AirwallexRequest {


    /**
     * 客户ID
     */
    private String customerId;


    /**
     * 下次触发时间
     */
    private String nextTriggeredBy;

    /**
     * 元数据
     */
    private Map<String, Object> metadata;
}