package tv.shorthub.airwallex.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
 * 支付同意书验证请求
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class PaymentConsentVerifyRequest extends AirwallexRequest {

    /**
     * 验证类型
     */
    private String verificationType;

    /**
     * 微存款金额列表（用于微存款验证）
     */
    private List<BigDecimal> amounts;

    /**
     * 验证码（用于某些验证类型）
     */
    private String verificationCode;

    /**
     * 返回URL
     */
    private String returnUrl;

    /**
     * 取消URL
     */
    private String cancelUrl;
} 