import java.security.MessageDigest;
import java.util.Arrays;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import org.bitcoinj.crypto.HDKeyDerivation;
import org.bitcoinj.crypto.DeterministicKey;
import org.bitcoinj.crypto.MnemonicCode;
import org.web3j.crypto.MnemonicUtils;
import net.i2p.crypto.eddsa.spec.EdDSANamedCurveTable;
import net.i2p.crypto.eddsa.spec.EdDSAPrivateKeySpec;
import net.i2p.crypto.eddsa.spec.EdDSAParameterSpec;
import net.i2p.crypto.eddsa.EdDSAPrivateKey;
import net.i2p.crypto.eddsa.EdDSAPublicKey;

public class SolanaFinalTest {
    
    public static void main(String[] args) throws Exception {
        String testMnemonic = "autumn wink trash organ catalog enrich thing student hundred mobile face flame";
        String expectedAddress = "482BcArEm6vAFXHwUv6MGEyudhZsiEFakH4Hsfd9hJu1";
        
        System.out.println("=== Final Comprehensive Solana Address Test ===");
        System.out.println("Mnemonic: " + testMnemonic);
        System.out.println("Expected: " + expectedAddress);
        System.out.println();
        
        // First, verify our BIP44 implementation with known test vectors
        testBIP44Implementation();
        
        // Test different Ed25519 signature schemes
        testEd25519Variants(testMnemonic, expectedAddress);
        
        // Test Phantom-specific derivations
        testPhantomSpecificDerivations(testMnemonic, expectedAddress);
        
        // Test with different salt variations
        testSaltVariations(testMnemonic, expectedAddress);
        
        // Test raw private key variations
        testRawPrivateKeyVariations(testMnemonic, expectedAddress);
    }
    
    private static void testBIP44Implementation() throws Exception {
        System.out.println("--- Verifying BIP44 Implementation ---");
        // Test with known BIP44 test vector
        String testMnemonic = "abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon about";
        byte[] seed = MnemonicUtils.generateSeed(testMnemonic, "");
        
        DeterministicKey masterKey = HDKeyDerivation.createMasterPrivateKey(seed);
        System.out.println("Master key created successfully");
        
        // Derive Ethereum path m/44'/60'/0'/0/0 to verify our derivation is correct
        DeterministicKey purposeKey = HDKeyDerivation.deriveChildKey(masterKey, 44 | 0x80000000);
        DeterministicKey coinKey = HDKeyDerivation.deriveChildKey(purposeKey, 60 | 0x80000000);
        DeterministicKey accountKey = HDKeyDerivation.deriveChildKey(coinKey, 0 | 0x80000000);
        DeterministicKey changeKey = HDKeyDerivation.deriveChildKey(accountKey, 0);
        DeterministicKey addressKey = HDKeyDerivation.deriveChildKey(changeKey, 0);
        
        System.out.println("Ethereum derivation: " + bytesToHex(addressKey.getPrivKeyBytes()));
        System.out.println("BIP44 implementation appears correct");
        System.out.println();
    }
    
    private static void testEd25519Variants(String mnemonic, String expectedAddress) throws Exception {
        System.out.println("--- Testing Ed25519 Variants ---");
        
        byte[] seed = MnemonicUtils.generateSeed(mnemonic, "");
        DeterministicKey masterKey = HDKeyDerivation.createMasterPrivateKey(seed);
        DeterministicKey purposeKey = HDKeyDerivation.deriveChildKey(masterKey, 44 | 0x80000000);
        DeterministicKey coinKey = HDKeyDerivation.deriveChildKey(purposeKey, 501 | 0x80000000);
        DeterministicKey accountKey = HDKeyDerivation.deriveChildKey(coinKey, 0 | 0x80000000);
        DeterministicKey addressKey = HDKeyDerivation.deriveChildKey(accountKey, 0 | 0x80000000);
        byte[] privateKeyBytes = addressKey.getPrivKeyBytes();
        
        // Test 1: Standard Ed25519 with our private key
        try {
            EdDSAParameterSpec spec = EdDSANamedCurveTable.getByName("Ed25519");
            EdDSAPrivateKeySpec privKeySpec = new EdDSAPrivateKeySpec(privateKeyBytes, spec);
            EdDSAPrivateKey privKey = new EdDSAPrivateKey(privKeySpec);
            EdDSAPublicKey pubKey = new EdDSAPublicKey(
                new net.i2p.crypto.eddsa.spec.EdDSAPublicKeySpec(privKey.getA(), spec));
            
            String address = encodeBase58(pubKey.getAbyte());
            System.out.println("Standard Ed25519: " + address);
            System.out.println("Match: " + address.equals(expectedAddress));
        } catch (Exception e) {
            System.out.println("Standard Ed25519 failed: " + e.getMessage());
        }
        
        // Test 2: Try reversing the private key bytes
        try {
            byte[] reversedKey = new byte[privateKeyBytes.length];
            for (int i = 0; i < privateKeyBytes.length; i++) {
                reversedKey[i] = privateKeyBytes[privateKeyBytes.length - 1 - i];
            }
            
            EdDSAParameterSpec spec = EdDSANamedCurveTable.getByName("Ed25519");
            EdDSAPrivateKeySpec privKeySpec = new EdDSAPrivateKeySpec(reversedKey, spec);
            EdDSAPrivateKey privKey = new EdDSAPrivateKey(privKeySpec);
            EdDSAPublicKey pubKey = new EdDSAPublicKey(
                new net.i2p.crypto.eddsa.spec.EdDSAPublicKeySpec(privKey.getA(), spec));
            
            String address = encodeBase58(pubKey.getAbyte());
            System.out.println("Reversed private key: " + address);
            System.out.println("Match: " + address.equals(expectedAddress));
        } catch (Exception e) {
            System.out.println("Reversed private key failed: " + e.getMessage());
        }
        
        // Test 3: Try XOR with constant
        try {
            byte[] xorKey = new byte[privateKeyBytes.length];
            for (int i = 0; i < privateKeyBytes.length; i++) {
                xorKey[i] = (byte) (privateKeyBytes[i] ^ 0x42); // XOR with 0x42
            }
            
            EdDSAParameterSpec spec = EdDSANamedCurveTable.getByName("Ed25519");
            EdDSAPrivateKeySpec privKeySpec = new EdDSAPrivateKeySpec(xorKey, spec);
            EdDSAPrivateKey privKey = new EdDSAPrivateKey(privKeySpec);
            EdDSAPublicKey pubKey = new EdDSAPublicKey(
                new net.i2p.crypto.eddsa.spec.EdDSAPublicKeySpec(privKey.getA(), spec));
            
            String address = encodeBase58(pubKey.getAbyte());
            System.out.println("XOR with 0x42: " + address);
            System.out.println("Match: " + address.equals(expectedAddress));
        } catch (Exception e) {
            System.out.println("XOR with 0x42 failed: " + e.getMessage());
        }
        
        System.out.println();
    }
    
    private static void testPhantomSpecificDerivations(String mnemonic, String expectedAddress) throws Exception {
        System.out.println("--- Testing Phantom-Specific Derivations ---");
        
        // Test 1: Phantom might use a different salt for PBKDF2
        try {
            String[] salts = {"mnemonic", "phantom", "solana", "wallet", ""};
            for (String salt : salts) {
                byte[] seed = generateCustomSeed(mnemonic, salt);
                DeterministicKey masterKey = HDKeyDerivation.createMasterPrivateKey(seed);
                DeterministicKey purposeKey = HDKeyDerivation.deriveChildKey(masterKey, 44 | 0x80000000);
                DeterministicKey coinKey = HDKeyDerivation.deriveChildKey(purposeKey, 501 | 0x80000000);
                DeterministicKey accountKey = HDKeyDerivation.deriveChildKey(coinKey, 0 | 0x80000000);
                DeterministicKey addressKey = HDKeyDerivation.deriveChildKey(accountKey, 0 | 0x80000000);
                
                String address = generateSolanaAddress(addressKey.getPrivKeyBytes());
                System.out.println("Salt '" + salt + "': " + address);
                System.out.println("Match: " + address.equals(expectedAddress));
                
                if (address.equals(expectedAddress)) {
                    System.out.println("*** FOUND MATCH WITH SALT: " + salt + " ***");
                    return;
                }
            }
        } catch (Exception e) {
            System.out.println("Salt variation failed: " + e.getMessage());
        }
        
        System.out.println();
    }
    
    private static void testSaltVariations(String mnemonic, String expectedAddress) throws Exception {
        System.out.println("--- Testing Salt Variations ---");
        
        // Test different PBKDF2 parameters
        String[] passphrases = {"", "phantom", "solana", "wallet"};
        int[] iterations = {2048, 4096, 1024, 100000};
        
        for (String passphrase : passphrases) {
            for (int iter : iterations) {
                try {
                    byte[] seed = generateCustomSeedWithIterations(mnemonic, passphrase, iter);
                    DeterministicKey masterKey = HDKeyDerivation.createMasterPrivateKey(seed);
                    DeterministicKey purposeKey = HDKeyDerivation.deriveChildKey(masterKey, 44 | 0x80000000);
                    DeterministicKey coinKey = HDKeyDerivation.deriveChildKey(purposeKey, 501 | 0x80000000);
                    DeterministicKey accountKey = HDKeyDerivation.deriveChildKey(coinKey, 0 | 0x80000000);
                    DeterministicKey addressKey = HDKeyDerivation.deriveChildKey(accountKey, 0 | 0x80000000);
                    
                    String address = generateSolanaAddress(addressKey.getPrivKeyBytes());
                    System.out.println("Passphrase '" + passphrase + "' iter " + iter + ": " + address);
                    
                    if (address.equals(expectedAddress)) {
                        System.out.println("*** FOUND MATCH WITH PASSPHRASE '" + passphrase + "' AND " + iter + " ITERATIONS ***");
                        return;
                    }
                } catch (Exception e) {
                    // Continue with next combination
                }
            }
        }
        
        System.out.println();
    }
    
    private static void testRawPrivateKeyVariations(String mnemonic, String expectedAddress) throws Exception {
        System.out.println("--- Testing Raw Private Key Variations ---");
        
        byte[] seed = MnemonicUtils.generateSeed(mnemonic, "");
        
        // Test using different parts of the seed directly as private key
        String[] descriptions = {
            "First 32 bytes of seed",
            "Last 32 bytes of seed", 
            "Middle 32 bytes of seed",
            "SHA-256 of seed",
            "SHA-512 first 32 bytes",
            "HMAC-SHA512 with seed as key and mnemonic as data"
        };
        
        byte[][] keys = new byte[descriptions.length][];
        keys[0] = Arrays.copyOf(seed, 32);
        keys[1] = Arrays.copyOfRange(seed, 32, 64);
        keys[2] = Arrays.copyOfRange(seed, 16, 48);
        
        MessageDigest sha256 = MessageDigest.getInstance("SHA-256");
        keys[3] = sha256.digest(seed);
        
        MessageDigest sha512 = MessageDigest.getInstance("SHA-512");
        byte[] sha512Hash = sha512.digest(seed);
        keys[4] = Arrays.copyOf(sha512Hash, 32);
        
        Mac hmac = Mac.getInstance("HmacSHA512");
        SecretKeySpec keySpec = new SecretKeySpec(seed, "HmacSHA512");
        hmac.init(keySpec);
        byte[] hmacResult = hmac.doFinal(mnemonic.getBytes("UTF-8"));
        keys[5] = Arrays.copyOf(hmacResult, 32);
        
        for (int i = 0; i < descriptions.length; i++) {
            try {
                String address = generateSolanaAddress(keys[i]);
                System.out.println(descriptions[i] + ": " + address);
                System.out.println("Match: " + address.equals(expectedAddress));
                
                if (address.equals(expectedAddress)) {
                    System.out.println("*** FOUND MATCH WITH " + descriptions[i] + " ***");
                    return;
                }
            } catch (Exception e) {
                System.out.println(descriptions[i] + " failed: " + e.getMessage());
            }
        }
        
        System.out.println();
    }
    
    private static byte[] generateCustomSeed(String mnemonic, String salt) throws Exception {
        String saltString = "mnemonic" + salt;
        
        javax.crypto.spec.PBEKeySpec spec = new javax.crypto.spec.PBEKeySpec(
            mnemonic.toCharArray(), 
            saltString.getBytes("UTF-8"), 
            2048, 
            512
        );
        
        javax.crypto.SecretKeyFactory factory = javax.crypto.SecretKeyFactory.getInstance("PBKDF2WithHmacSHA512");
        return factory.generateSecret(spec).getEncoded();
    }
    
    private static byte[] generateCustomSeedWithIterations(String mnemonic, String passphrase, int iterations) throws Exception {
        String saltString = "mnemonic" + passphrase;
        
        javax.crypto.spec.PBEKeySpec spec = new javax.crypto.spec.PBEKeySpec(
            mnemonic.toCharArray(), 
            saltString.getBytes("UTF-8"), 
            iterations, 
            512
        );
        
        javax.crypto.SecretKeyFactory factory = javax.crypto.SecretKeyFactory.getInstance("PBKDF2WithHmacSHA512");
        return factory.generateSecret(spec).getEncoded();
    }
    
    private static String generateSolanaAddress(byte[] privateKeyBytes) throws Exception {
        // Ensure we have 32 bytes
        if (privateKeyBytes.length > 32) {
            privateKeyBytes = Arrays.copyOf(privateKeyBytes, 32);
        } else if (privateKeyBytes.length < 32) {
            byte[] padded = new byte[32];
            System.arraycopy(privateKeyBytes, 0, padded, 0, privateKeyBytes.length);
            privateKeyBytes = padded;
        }
        
        EdDSAParameterSpec spec = EdDSANamedCurveTable.getByName("Ed25519");
        EdDSAPrivateKeySpec privKeySpec = new EdDSAPrivateKeySpec(privateKeyBytes, spec);
        EdDSAPrivateKey privKey = new EdDSAPrivateKey(privKeySpec);
        EdDSAPublicKey pubKey = new EdDSAPublicKey(
            new net.i2p.crypto.eddsa.spec.EdDSAPublicKeySpec(privKey.getA(), spec));
        
        return encodeBase58(pubKey.getAbyte());
    }
    
    private static String bytesToHex(byte[] bytes) {
        StringBuilder result = new StringBuilder();
        for (byte b : bytes) {
            result.append(String.format("%02x", b));
        }
        return result.toString();
    }
    
    private static final String ALPHABET = "**********************************************************";
    
    private static String encodeBase58(byte[] input) {
        if (input.length == 0) return "";
        
        int[] digits = new int[input.length * 2];
        int digitslen = 1;
        
        for (int i = 0; i < input.length; i++) {
            int carry = input[i] & 0xFF;
            for (int j = 0; j < digitslen; j++) {
                carry += digits[j] << 8;
                digits[j] = carry % 58;
                carry /= 58;
            }
            while (carry > 0) {
                digits[digitslen++] = carry % 58;
                carry /= 58;
            }
        }
        
        // Handle leading zeros
        int zeroslen = 0;
        while (zeroslen < input.length && input[zeroslen] == 0) {
            zeroslen++;
        }
        
        StringBuilder result = new StringBuilder();
        for (int i = 0; i < zeroslen; i++) {
            result.append(ALPHABET.charAt(0));
        }
        for (int i = digitslen - 1; i >= 0; i--) {
            result.append(ALPHABET.charAt(digits[i]));
        }
        
        return result.toString();
    }
}