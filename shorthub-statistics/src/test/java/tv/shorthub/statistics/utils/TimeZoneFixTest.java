package tv.shorthub.statistics.utils;

import org.junit.jupiter.api.Test;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

/**
 * 时区转换修复验证测试
 */
public class TimeZoneFixTest {

    private final SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @Test
    public void testFixedLogic() {
        System.out.println("=== 修复后的时区转换逻辑测试 ===");

        // 模拟定时任务：统计 2025-08-04 这一天的数据
        Calendar cal = Calendar.getInstance();
        cal.set(2025, Calendar.AUGUST, 4, 0, 0, 0);
        cal.set(Calendar.MILLISECOND, 0);
        Date startTime = cal.getTime(); // 2025-08-04 00:00:00

        cal.add(Calendar.DATE, 1);
        Date endTime = cal.getTime(); // 2025-08-05 00:00:00

        System.out.println("定时任务要统计的日期：2025-08-04");
        System.out.println("原始时间范围（系统时间 UTC+8）:");
        System.out.println("  开始: " + sdf.format(startTime));
        System.out.println("  结束: " + sdf.format(endTime));

        // 测试 UTC-8 时区的转换
        Date[] adjusted = TimeZoneUtils.adjustTimeRangeForQuery(startTime, endTime, "UTC-8");
        System.out.println("\nUTC-8 时区调整后的查询时间范围:");
        System.out.println("  开始: " + sdf.format(adjusted[0]));
        System.out.println("  结束: " + sdf.format(adjusted[1]));

        System.out.println("\n分析：");
        System.out.println("  要统计UTC-8时区的2025-08-04这一天");
        System.out.println("  UTC-8的2025-08-04 00:00 对应 UTC+8的2025-08-04 16:00");
        System.out.println("  UTC-8的2025-08-05 00:00 对应 UTC+8的2025-08-05 16:00");
        System.out.println("  所以应该查询UTC+8的2025-08-04 16:00到2025-08-05 16:00");

        boolean isCorrect = sdf.format(adjusted[0]).equals("2025-08-04 16:00:00") &&
                           sdf.format(adjusted[1]).equals("2025-08-05 16:00:00");
        System.out.println("  修复是否正确：" + (isCorrect ? "✓" : "✗"));

        if (!isCorrect) {
            System.out.println("  期望开始时间：2025-08-04 16:00:00");
            System.out.println("  实际开始时间：" + sdf.format(adjusted[0]));
            System.out.println("  期望结束时间：2025-08-05 16:00:00");
            System.out.println("  实际结束时间：" + sdf.format(adjusted[1]));
        }
    }

    @Test
    public void testAllTimezones() {
        System.out.println("\n=== 测试所有时区转换 ===");

        Calendar cal = Calendar.getInstance();
        cal.set(2025, Calendar.AUGUST, 4, 0, 0, 0);
        cal.set(Calendar.MILLISECOND, 0);
        Date startTime = cal.getTime();

        cal.add(Calendar.DATE, 1);
        Date endTime = cal.getTime();

        String[] timezones = {"UTC+8", "UTC+0", "UTC-8", "UTC+12", "UTC-12"};

        for (String timezone : timezones) {
            Date[] adjusted = TimeZoneUtils.adjustTimeRangeForQuery(startTime, endTime, timezone);
            System.out.println(timezone + " -> " + sdf.format(adjusted[0]) + " 到 " + sdf.format(adjusted[1]));
        }
    }
}
