package tv.shorthub.statistics.utils;

import org.junit.jupiter.api.Test;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

/**
 * 时区工具类测试
 *
 * <AUTHOR>
 * @date 2025-08-01
 */
public class TimeZoneUtilsTest {

    private final SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @Test
    public void testTimezoneOffset() {
        System.out.println("=== 时区偏移量测试 ===");
        
        // 测试各种时区格式
        System.out.println("UTC+8: " + TimeZoneUtils.getTimezoneOffset("UTC+8"));
        System.out.println("UTC-8: " + TimeZoneUtils.getTimezoneOffset("UTC-8"));
        System.out.println("UTC+0: " + TimeZoneUtils.getTimezoneOffset("UTC+0"));
        System.out.println("UTC-12: " + TimeZoneUtils.getTimezoneOffset("UTC-12"));
        System.out.println("UTC+12: " + TimeZoneUtils.getTimezoneOffset("UTC+12"));
        System.out.println("null: " + TimeZoneUtils.getTimezoneOffset(null));
        System.out.println("invalid: " + TimeZoneUtils.getTimezoneOffset("invalid"));
    }

    @Test
    public void testTimeRangeAdjustment() {
        System.out.println("\n=== 时间范围调整测试 ===");
        
        // 创建测试时间：2025-01-01 00:00:00 到 2025-01-02 00:00:00
        Calendar cal = Calendar.getInstance();
        cal.set(2025, Calendar.JANUARY, 1, 0, 0, 0);
        cal.set(Calendar.MILLISECOND, 0);
        Date startTime = cal.getTime();
        
        cal.add(Calendar.DATE, 1);
        Date endTime = cal.getTime();
        
        System.out.println("原始时间范围（UTC+8）:");
        System.out.println("  开始: " + sdf.format(startTime));
        System.out.println("  结束: " + sdf.format(endTime));
        
        // 测试不同时区的调整
        String[] timezones = {"UTC+8", "UTC-8", "UTC+0", "UTC+9", "UTC-5"};
        
        for (String timezone : timezones) {
            Date[] adjusted = TimeZoneUtils.adjustTimeRangeForQuery(startTime, endTime, timezone);
            System.out.println("\n" + timezone + " 调整后的查询时间范围:");
            System.out.println("  开始: " + sdf.format(adjusted[0]));
            System.out.println("  结束: " + sdf.format(adjusted[1]));
            
            // 验证逻辑
            int targetOffset = TimeZoneUtils.getTimezoneOffset(timezone);
            int systemOffset = 8;
            int expectedDiff = systemOffset - targetOffset;
            
            Calendar expectedStart = Calendar.getInstance();
            expectedStart.setTime(startTime);
            expectedStart.add(Calendar.HOUR_OF_DAY, expectedDiff);
            
            System.out.println("  预期开始: " + sdf.format(expectedStart.getTime()));
            System.out.println("  时差: " + expectedDiff + " 小时");
        }
    }

    @Test
    public void testGMTTimezoneString() {
        System.out.println("\n=== GMT 格式转换测试 ===");
        
        String[] timezones = {"UTC+8", "UTC-8", "UTC+0", "UTC+12", "UTC-12"};
        
        for (String timezone : timezones) {
            String gmt = TimeZoneUtils.getGMTTimezoneString(timezone);
            System.out.println(timezone + " -> " + gmt);
        }
    }

    @Test
    public void testRealWorldScenario() {
        System.out.println("\n=== 真实场景测试 ===");
        
        // 模拟美西时间用户查询数据的场景
        System.out.println("场景：美西时间(UTC-8)用户查询 2025-01-01 的数据");
        
        // 用户在 UTC-8 时区查询 2025-01-01 00:00 到 2025-01-02 00:00
        Calendar cal = Calendar.getInstance();
        cal.set(2025, Calendar.JANUARY, 1, 0, 0, 0);
        cal.set(Calendar.MILLISECOND, 0);
        Date userStartTime = cal.getTime();
        
        cal.add(Calendar.DATE, 1);
        Date userEndTime = cal.getTime();
        
        System.out.println("用户查询时间范围（UTC-8 概念）:");
        System.out.println("  开始: " + sdf.format(userStartTime));
        System.out.println("  结束: " + sdf.format(userEndTime));
        
        // 转换为系统查询时间（UTC+8）
        Date[] systemTimeRange = TimeZoneUtils.adjustTimeRangeForQuery(userStartTime, userEndTime, "UTC-8");
        
        System.out.println("系统实际查询时间范围（UTC+8）:");
        System.out.println("  开始: " + sdf.format(systemTimeRange[0]));
        System.out.println("  结束: " + sdf.format(systemTimeRange[1]));
        
        System.out.println("解释：");
        System.out.println("  UTC-8 的 2025-01-01 00:00 对应 UTC+8 的 2025-01-01 16:00");
        System.out.println("  UTC-8 的 2025-01-02 00:00 对应 UTC+8 的 2025-01-02 16:00");
        System.out.println("  所以系统需要查询 UTC+8 时间 16:00 到 16:00 的数据");
    }
}
