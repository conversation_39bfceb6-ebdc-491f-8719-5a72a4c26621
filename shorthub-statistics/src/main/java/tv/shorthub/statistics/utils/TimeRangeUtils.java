package tv.shorthub.statistics.utils;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 时间范围处理工具类
 * 提供统计模块中时间相关的辅助方法
 *
 * <AUTHOR>
 * @date 2025-01-02
 */
public class TimeRangeUtils {

    private static final DateTimeFormatter HOUR_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:00:00");
    private static final DateTimeFormatter DATE_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    /**
     * 获取当前小时的开始时间（整点）
     * 
     * @return 当前小时的开始时间
     */
    public static Date getCurrentHourStart() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    /**
     * 获取指定时间的整点小时
     * 
     * @param date 指定时间
     * @return 整点小时时间
     */
    public static Date getHourStart(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    /**
     * 获取指定时间的下一个整点小时
     * 
     * @param date 指定时间
     * @return 下一个整点小时时间
     */
    public static Date getNextHourStart(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(getHourStart(date));
        calendar.add(Calendar.HOUR_OF_DAY, 1);
        return calendar.getTime();
    }

    /**
     * 获取上一个小时的开始时间
     * 
     * @return 上一个小时的开始时间
     */
    public static Date getPreviousHourStart() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.HOUR_OF_DAY, -1);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    /**
     * 获取今天的开始时间（00:00:00）
     * 
     * @return 今天的开始时间
     */
    public static Date getTodayStart() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    /**
     * 获取今天的结束时间（23:59:59）
     * 
     * @return 今天的结束时间
     */
    public static Date getTodayEnd() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        return calendar.getTime();
    }

    /**
     * 获取指定日期的开始时间（00:00:00）
     * 
     * @param date 指定日期
     * @return 指定日期的开始时间
     */
    public static Date getDayStart(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    /**
     * 获取指定日期的结束时间（23:59:59）
     * 
     * @param date 指定日期
     * @return 指定日期的结束时间
     */
    public static Date getDayEnd(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        return calendar.getTime();
    }

    /**
     * 生成指定时间范围内的小时列表
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 小时时间列表
     */
    public static List<Date> generateHourRange(Date startDate, Date endDate) {
        List<Date> hours = new ArrayList<>();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(getHourStart(startDate));
        
        Date endHour = getHourStart(endDate);
        
        while (!calendar.getTime().after(endHour)) {
            hours.add(calendar.getTime());
            calendar.add(Calendar.HOUR_OF_DAY, 1);
        }
        
        return hours;
    }

    /**
     * 生成指定时间范围内的日期列表
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 日期列表
     */
    public static List<Date> generateDateRange(Date startDate, Date endDate) {
        List<Date> dates = new ArrayList<>();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(getDayStart(startDate));
        
        Date endDay = getDayStart(endDate);
        
        while (!calendar.getTime().after(endDay)) {
            dates.add(calendar.getTime());
            calendar.add(Calendar.DAY_OF_MONTH, 1);
        }
        
        return dates;
    }

    /**
     * 计算两个日期之间的小时数
     * 
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @return 小时数
     */
    public static long getHoursBetween(Date startDate, Date endDate) {
        long diffInMillis = endDate.getTime() - startDate.getTime();
        return diffInMillis / (1000 * 60 * 60);
    }

    /**
     * 计算两个日期之间的天数
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 天数
     */
    public static long getDaysBetween(Date startDate, Date endDate) {
        long diffInMillis = endDate.getTime() - startDate.getTime();
        return diffInMillis / (1000 * 60 * 60 * 24);
    }

    /**
     * 格式化时间为小时格式字符串
     * 
     * @param date 时间
     * @return 格式化后的字符串 (yyyy-MM-dd HH:00:00)
     */
    public static String formatToHour(Date date) {
        LocalDateTime localDateTime = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        return localDateTime.format(HOUR_FORMAT);
    }

    /**
     * 格式化时间为日期格式字符串
     * 
     * @param date 时间
     * @return 格式化后的字符串 (yyyy-MM-dd)
     */
    public static String formatToDate(Date date) {
        LocalDateTime localDateTime = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        return localDateTime.format(DATE_FORMAT);
    }

    /**
     * 获取N天前的日期
     * 
     * @param days 天数
     * @return N天前的日期
     */
    public static Date getDaysAgo(int days) {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, -days);
        return calendar.getTime();
    }

    /**
     * 获取N小时前的时间
     * 
     * @param hours 小时数
     * @return N小时前的时间
     */
    public static Date getHoursAgo(int hours) {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.HOUR_OF_DAY, -hours);
        return calendar.getTime();
    }

    /**
     * 判断时间范围是否合理（开始时间不能晚于结束时间）
     * 
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @return 是否合理
     */
    public static boolean isValidTimeRange(Date startDate, Date endDate) {
        if (startDate == null || endDate == null) {
            return false;
        }
        return !startDate.after(endDate);
    }

    /**
     * 限制时间范围不超过指定天数
     * 
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @param maxDays 最大天数
     * @return 是否在限制范围内
     */
    public static boolean isWithinDayLimit(Date startDate, Date endDate, int maxDays) {
        if (!isValidTimeRange(startDate, endDate)) {
            return false;
        }
        return getDaysBetween(startDate, endDate) <= maxDays;
    }
} 