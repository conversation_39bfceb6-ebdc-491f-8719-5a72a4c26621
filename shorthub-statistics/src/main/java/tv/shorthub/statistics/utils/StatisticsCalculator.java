package tv.shorthub.statistics.utils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

/**
 * 统计计算工具类
 * 提供常用的统计指标计算方法
 *
 * <AUTHOR>
 * @date 2025-01-02
 */
public class StatisticsCalculator {

    /**
     * 计算支付率
     * 
     * @param paidCount 已支付订单数
     * @param totalCount 总订单数
     * @return 支付率（百分比）
     */
    public static BigDecimal calculatePaymentRate(Integer paidCount, Integer totalCount) {
        if (totalCount == null || totalCount == 0) {
            return BigDecimal.ZERO;
        }
        if (paidCount == null) {
            paidCount = 0;
        }
        
        return BigDecimal.valueOf(paidCount)
                .multiply(BigDecimal.valueOf(100))
                .divide(BigDecimal.valueOf(totalCount), 2, RoundingMode.HALF_UP);
    }

    /**
     * 计算客单价（平均订单金额）
     * 
     * @param totalAmount 总金额
     * @param orderCount 订单数量
     * @return 客单价
     */
    public static BigDecimal calculateAverageOrderValue(BigDecimal totalAmount, Integer orderCount) {
        if (orderCount == null || orderCount == 0) {
            return BigDecimal.ZERO;
        }
        if (totalAmount == null) {
            totalAmount = BigDecimal.ZERO;
        }
        
        return totalAmount.divide(BigDecimal.valueOf(orderCount), 2, RoundingMode.HALF_UP);
    }

    /**
     * 计算增长率
     * 
     * @param currentValue 当前值
     * @param previousValue 之前值
     * @return 增长率（百分比）
     */
    public static BigDecimal calculateGrowthRate(BigDecimal currentValue, BigDecimal previousValue) {
        if (previousValue == null || previousValue.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }
        if (currentValue == null) {
            currentValue = BigDecimal.ZERO;
        }
        
        return currentValue.subtract(previousValue)
                .multiply(BigDecimal.valueOf(100))
                .divide(previousValue, 2, RoundingMode.HALF_UP);
    }

    /**
     * 计算占比
     * 
     * @param partValue 部分值
     * @param totalValue 总值
     * @return 占比（百分比）
     */
    public static BigDecimal calculatePercentage(BigDecimal partValue, BigDecimal totalValue) {
        if (totalValue == null || totalValue.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }
        if (partValue == null) {
            partValue = BigDecimal.ZERO;
        }
        
        return partValue.multiply(BigDecimal.valueOf(100))
                .divide(totalValue, 2, RoundingMode.HALF_UP);
    }

    /**
     * 计算续订率
     * 
     * @param renewalCount 续订数量
     * @param totalSubscriptionCount 总订阅数量
     * @return 续订率（百分比）
     */
    public static BigDecimal calculateRenewalRate(Integer renewalCount, Integer totalSubscriptionCount) {
        if (totalSubscriptionCount == null || totalSubscriptionCount == 0) {
            return BigDecimal.ZERO;
        }
        if (renewalCount == null) {
            renewalCount = 0;
        }
        
        return BigDecimal.valueOf(renewalCount)
                .multiply(BigDecimal.valueOf(100))
                .divide(BigDecimal.valueOf(totalSubscriptionCount), 2, RoundingMode.HALF_UP);
    }

    /**
     * 计算ARPU（每用户平均收入）
     * 
     * @param totalRevenue 总收入
     * @param activeUsers 活跃用户数
     * @return ARPU
     */
    public static BigDecimal calculateARPU(BigDecimal totalRevenue, Integer activeUsers) {
        if (activeUsers == null || activeUsers == 0) {
            return BigDecimal.ZERO;
        }
        if (totalRevenue == null) {
            totalRevenue = BigDecimal.ZERO;
        }
        
        return totalRevenue.divide(BigDecimal.valueOf(activeUsers), 2, RoundingMode.HALF_UP);
    }

    /**
     * 安全地将数值转换为BigDecimal
     * 
     * @param value 数值对象
     * @return BigDecimal值，null则返回0
     */
    public static BigDecimal safeToBigDecimal(Object value) {
        if (value == null) {
            return BigDecimal.ZERO;
        }
        if (value instanceof BigDecimal) {
            return (BigDecimal) value;
        }
        if (value instanceof Number) {
            return BigDecimal.valueOf(((Number) value).doubleValue());
        }
        try {
            return new BigDecimal(value.toString());
        } catch (NumberFormatException e) {
            return BigDecimal.ZERO;
        }
    }

    /**
     * 安全地将数值转换为Integer
     * 
     * @param value 数值对象
     * @return Integer值，null则返回0
     */
    public static Integer safeToInteger(Object value) {
        if (value == null) {
            return 0;
        }
        if (value instanceof Integer) {
            return (Integer) value;
        }
        if (value instanceof Number) {
            return ((Number) value).intValue();
        }
        try {
            return Integer.valueOf(value.toString());
        } catch (NumberFormatException e) {
            return 0;
        }
    }
} 