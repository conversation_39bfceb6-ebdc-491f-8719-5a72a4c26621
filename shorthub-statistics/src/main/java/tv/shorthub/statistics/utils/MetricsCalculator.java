package tv.shorthub.statistics.utils;

import tv.shorthub.system.domain.StatisticsHourlyOrderStats;
import tv.shorthub.system.domain.StatisticsHourlySubscriptionStats;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.Map;

/**
 * 指标计算工具类
 * 专门用于计算前端需要的统计指标
 *
 * <AUTHOR>
 * @date 2025-01-02
 */
public class MetricsCalculator {

    /**
     * 计算订单相关的所有指标
     *
     * @param orderStats 订单统计数据
     * @return 包含所有指标的Map
     */
    public static Map<String, Object> calculateOrderMetrics(StatisticsHourlyOrderStats orderStats) {
        Map<String, Object> metrics = new HashMap<>();

        if (orderStats == null) {
            return getEmptyOrderMetrics();
        }

        // 基础数据
        BigDecimal paidAmount = StatisticsCalculator.safeToBigDecimal(orderStats.getPaidOrderAmount());
        Integer paidCount = StatisticsCalculator.safeToInteger(orderStats.getPaidOrderCount());
        Integer totalCount = StatisticsCalculator.safeToInteger(orderStats.getTotalOrderCount());
        Integer unpaidCount = StatisticsCalculator.safeToInteger(orderStats.getUnpaidOrderCount());
        BigDecimal unpaidCoinAmount = StatisticsCalculator.safeToBigDecimal(orderStats.getUnpaidCoinOrderAmount());
        BigDecimal unpaidSubscriptionAmount = StatisticsCalculator.safeToBigDecimal(orderStats.getUnpaidSubscriptionOrderAmount());

        BigDecimal memberAmount = StatisticsCalculator.safeToBigDecimal(orderStats.getMemberOrderAmount());
        Integer memberCount = StatisticsCalculator.safeToInteger(orderStats.getMemberOrderCount());
        BigDecimal coinAmount = StatisticsCalculator.safeToBigDecimal(orderStats.getCoinOrderAmount());
        Integer coinCount = StatisticsCalculator.safeToInteger(orderStats.getCoinOrderCount());
        BigDecimal subscriptionAmount = StatisticsCalculator.safeToBigDecimal(orderStats.getSubscriptionOrderAmount());
        Integer subscriptionCount = StatisticsCalculator.safeToInteger(orderStats.getSubscriptionOrderCount());

        // 核心指标
        metrics.put("paymentAmount", paidAmount);
        metrics.put("orderCount", paidCount);
        metrics.put("totalOrderCount", totalCount);
        metrics.put("unpaidOrderCount", unpaidCount);
        metrics.put("unpaidCoinOrderAmount", unpaidCoinAmount);      // 待扣款金币金额
        metrics.put("unpaidSubscriptionOrderAmount", unpaidSubscriptionAmount);  // 待扣款订阅金额

        // 计算总待扣款金额
        BigDecimal totalUnpaidAmount = unpaidCoinAmount.add(unpaidSubscriptionAmount);
        metrics.put("totalUnpaidAmount", totalUnpaidAmount);  // 总待扣款金额

        // 计算客单价
        BigDecimal averageOrderValue = StatisticsCalculator.calculateAverageOrderValue(paidAmount, paidCount);
        metrics.put("averageOrderValue", averageOrderValue);

        // 计算支付率
        BigDecimal paymentRate = StatisticsCalculator.calculatePaymentRate(paidCount, totalCount);
        metrics.put("rechargeRate", paymentRate);

        // 业务类型数据
        metrics.put("memberOrderCount", memberCount);
        metrics.put("memberOrderAmount", memberAmount);
        metrics.put("coinOrderCount", coinCount);
        metrics.put("coinOrderAmount", coinAmount);
        metrics.put("subscriptionCount", subscriptionCount);
        metrics.put("subscriptionAmount", subscriptionAmount);

        // 业务类型占比
        BigDecimal memberPercentage = StatisticsCalculator.calculatePercentage(memberAmount, paidAmount);
        BigDecimal coinPercentage = StatisticsCalculator.calculatePercentage(coinAmount, paidAmount);
        BigDecimal subscriptionPercentage = StatisticsCalculator.calculatePercentage(subscriptionAmount, paidAmount);

        metrics.put("memberOrderPercentage", memberPercentage);
        metrics.put("coinOrderPercentage", coinPercentage);
        metrics.put("subscriptionOrderPercentage", subscriptionPercentage);

        // 平均金额
        BigDecimal avgMemberAmount = StatisticsCalculator.calculateAverageOrderValue(memberAmount, memberCount);
        BigDecimal avgCoinAmount = StatisticsCalculator.calculateAverageOrderValue(coinAmount, coinCount);
        BigDecimal avgSubscriptionAmount = StatisticsCalculator.calculateAverageOrderValue(subscriptionAmount, subscriptionCount);

        metrics.put("averageMemberOrderAmount", avgMemberAmount);
        metrics.put("averageCoinOrderAmount", avgCoinAmount);
        metrics.put("averageSubscriptionOrderAmount", avgSubscriptionAmount);

        return metrics;
    }

    /**
     * 计算订阅相关的所有指标
     *
     * @param subscriptionStats 订阅统计数据
     * @return 包含所有指标的Map
     */
    public static Map<String, Object> calculateSubscriptionMetrics(StatisticsHourlySubscriptionStats subscriptionStats) {
        Map<String, Object> metrics = new HashMap<>();

        if (subscriptionStats == null) {
            return getEmptySubscriptionMetrics();
        }

        // 基础数据
        Integer newSubscriptionCount = StatisticsCalculator.safeToInteger(subscriptionStats.getNewSubscriptionOrderCount());
        BigDecimal newSubscriptionAmount = StatisticsCalculator.safeToBigDecimal(subscriptionStats.getNewSubscriptionOrderAmount());
        Integer renewalCount = StatisticsCalculator.safeToInteger(subscriptionStats.getSuccessfulRenewalCount());
        BigDecimal renewalAmount = StatisticsCalculator.safeToBigDecimal(subscriptionStats.getSuccessfulRenewalAmount());
        BigDecimal totalAmount = StatisticsCalculator.safeToBigDecimal(subscriptionStats.getTotalOrderAmount());

        // 核心指标
        metrics.put("subscriptionCount", newSubscriptionCount);
        metrics.put("subscriptionAmount", newSubscriptionAmount);
        metrics.put("renewalCount", renewalCount);
        metrics.put("renewalAmount", renewalAmount);
        metrics.put("totalSubscriptionAmount", totalAmount);

        // 计算平均金额
        BigDecimal avgNewSubscriptionAmount = StatisticsCalculator.calculateAverageOrderValue(newSubscriptionAmount, newSubscriptionCount);
        BigDecimal avgRenewalAmount = StatisticsCalculator.calculateAverageOrderValue(renewalAmount, renewalCount);

        metrics.put("averageNewSubscriptionAmount", avgNewSubscriptionAmount);
        metrics.put("averageRenewalAmount", avgRenewalAmount);

        // 计算续订相关指标
        Integer totalSubscriptions = newSubscriptionCount + renewalCount;
        BigDecimal renewalRate = StatisticsCalculator.calculateRenewalRate(renewalCount, totalSubscriptions);
        metrics.put("renewalRate", renewalRate);

        // 金额占比
        BigDecimal newSubscriptionPercentage = StatisticsCalculator.calculatePercentage(newSubscriptionAmount, totalAmount);
        BigDecimal renewalPercentage = StatisticsCalculator.calculatePercentage(renewalAmount, totalAmount);

        metrics.put("newSubscriptionPercentage", newSubscriptionPercentage);
        metrics.put("renewalPercentage", renewalPercentage);

        return metrics;
    }

    /**
     * 合并订单和订阅指标
     *
     * @param orderStats 订单统计数据
     * @param subscriptionStats 订阅统计数据
     * @return 合并后的指标Map
     */
    public static Map<String, Object> calculateCombinedMetrics(StatisticsHourlyOrderStats orderStats,
                                                               StatisticsHourlySubscriptionStats subscriptionStats) {
        Map<String, Object> orderMetrics = calculateOrderMetrics(orderStats);
        Map<String, Object> subscriptionMetrics = calculateSubscriptionMetrics(subscriptionStats);

        // 合并两个Map
        Map<String, Object> combinedMetrics = new HashMap<>(orderMetrics);
        combinedMetrics.putAll(subscriptionMetrics);

        // 计算综合指标
        BigDecimal totalRevenue = StatisticsCalculator.safeToBigDecimal(orderMetrics.get("paymentAmount"))
                .add(StatisticsCalculator.safeToBigDecimal(subscriptionMetrics.get("totalSubscriptionAmount")));
        combinedMetrics.put("totalRevenue", totalRevenue);

        return combinedMetrics;
    }

    /**
     * 计算增长指标
     *
     * @param currentMetrics 当前期间指标
     * @param previousMetrics 上一期间指标
     * @return 增长指标Map
     */
    public static Map<String, Object> calculateGrowthMetrics(Map<String, Object> currentMetrics,
                                                             Map<String, Object> previousMetrics) {
        Map<String, Object> growthMetrics = new HashMap<>();

        // 主要指标的增长率
        String[] keyMetrics = {
                "paymentAmount", "orderCount", "averageOrderValue", "rechargeRate",
                "memberOrderAmount", "coinOrderAmount", "subscriptionAmount",
                "renewalCount", "totalSubscriptionAmount"
        };

        for (String metric : keyMetrics) {
            BigDecimal current = StatisticsCalculator.safeToBigDecimal(currentMetrics.get(metric));
            BigDecimal previous = StatisticsCalculator.safeToBigDecimal(previousMetrics.get(metric));
            BigDecimal growthRate = StatisticsCalculator.calculateGrowthRate(current, previous);
            growthMetrics.put(metric + "GrowthRate", growthRate);
        }

        return growthMetrics;
    }

    /**
     * 获取空的订单指标
     */
    private static Map<String, Object> getEmptyOrderMetrics() {
        Map<String, Object> metrics = new HashMap<>();
        metrics.put("paymentAmount", BigDecimal.ZERO);
        metrics.put("orderCount", 0);
        metrics.put("totalOrderCount", 0);
        metrics.put("unpaidOrderCount", 0);
        metrics.put("unpaidCoinOrderAmount", BigDecimal.ZERO);
        metrics.put("unpaidSubscriptionOrderAmount", BigDecimal.ZERO);
        metrics.put("totalUnpaidAmount", BigDecimal.ZERO);
        metrics.put("averageOrderValue", BigDecimal.ZERO);
        metrics.put("rechargeRate", BigDecimal.ZERO);
        metrics.put("memberOrderCount", 0);
        metrics.put("memberOrderAmount", BigDecimal.ZERO);
        metrics.put("coinOrderCount", 0);
        metrics.put("coinOrderAmount", BigDecimal.ZERO);
        metrics.put("subscriptionCount", 0);
        metrics.put("subscriptionAmount", BigDecimal.ZERO);
        return metrics;
    }

    /**
     * 获取空的订阅指标
     */
    private static Map<String, Object> getEmptySubscriptionMetrics() {
        Map<String, Object> metrics = new HashMap<>();
        metrics.put("subscriptionCount", 0);
        metrics.put("subscriptionAmount", BigDecimal.ZERO);
        metrics.put("renewalCount", 0);
        metrics.put("renewalAmount", BigDecimal.ZERO);
        metrics.put("totalSubscriptionAmount", BigDecimal.ZERO);
        metrics.put("renewalRate", BigDecimal.ZERO);
        return metrics;
    }

    /**
     * 格式化指标数值（保留2位小数）
     *
     * @param metrics 指标Map
     * @return 格式化后的指标Map
     */
    public static Map<String, Object> formatMetrics(Map<String, Object> metrics) {
        Map<String, Object> formattedMetrics = new HashMap<>();

        for (Map.Entry<String, Object> entry : metrics.entrySet()) {
            Object value = entry.getValue();
            if (value instanceof BigDecimal) {
                BigDecimal decimal = (BigDecimal) value;
                formattedMetrics.put(entry.getKey(), decimal.setScale(2, RoundingMode.HALF_UP));
            } else {
                formattedMetrics.put(entry.getKey(), value);
            }
        }

        return formattedMetrics;
    }
}
