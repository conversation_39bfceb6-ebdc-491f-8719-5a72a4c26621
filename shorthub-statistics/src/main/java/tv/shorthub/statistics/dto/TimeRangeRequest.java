package tv.shorthub.statistics.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 时间范围请求参数
 *
 * <AUTHOR>
 * @date 2025-01-02
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TimeRangeRequest {
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;
    
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startDate;
    
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endDate;
    
    private String appid;
    private String tfid;
    private String timezone;
    private String granularity;

    // 便捷构造函数
    public TimeRangeRequest(Date startTime, Date endTime, String appid) {
        this.startTime = startTime;
        this.endTime = endTime;
        this.appid = appid;
    }

    /**
     * 验证时间范围参数
     */
    public boolean isValidTimeRange() {
        if (startTime != null && endTime != null) {
            return !startTime.after(endTime);
        }
        if (startDate != null && endDate != null) {
            return !startDate.after(endDate);
        }
        return false;
    }

    /**
     * 获取有效的开始时间
     */
    public Date getEffectiveStartTime() {
        return startTime != null ? startTime : startDate;
    }

    /**
     * 获取有效的结束时间
     */
    public Date getEffectiveEndTime() {
        return endTime != null ? endTime : endDate;
    }
} 