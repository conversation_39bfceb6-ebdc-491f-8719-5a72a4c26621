package tv.shorthub.statistics.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 批量更新请求参数
 *
 * <AUTHOR>
 * @date 2025-01-02
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BatchUpdateRequest {
    
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startDate;
    
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endDate;
    
    private Integer days;

    // 便捷构造函数
    public BatchUpdateRequest(Date startDate, Date endDate) {
        this.startDate = startDate;
        this.endDate = endDate;
    }

    public BatchUpdateRequest(Integer days) {
        this.days = days;
    }

    /**
     * 验证日期范围参数
     */
    public boolean isValidDateRange() {
        if (startDate != null && endDate != null) {
            return !startDate.after(endDate);
        }
        return days != null && days > 0;
    }

    /**
     * 是否为天数模式
     */
    public boolean isDaysMode() {
        return days != null && days > 0;
    }

    /**
     * 是否为日期范围模式
     */
    public boolean isDateRangeMode() {
        return startDate != null && endDate != null;
    }

    @Override
    public String toString() {
        return "BatchUpdateRequest{" +
                "startDate=" + startDate +
                ", endDate=" + endDate +
                ", days=" + days +
                '}';
    }
}