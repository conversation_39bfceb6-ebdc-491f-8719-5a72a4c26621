package tv.shorthub.statistics.service;

/**
 * 小时级剧目充值统计服务接口
 * 
 * <AUTHOR>
 * @date 2025-07-15
 */
public interface IHourlyDramaRechargeStatsService {
    
    /**
     * 计算并保存当前小时的剧目充值统计数据
     */
    void calculateAndSaveCurrentHourStats();
    
    /**
     * 重新计算昨天的剧目充值统计数据
     */
    void recalculateYesterdayStats();
    
    /**
     * 重新计算最近7天的剧目充值统计数据
     */
    void recalculateWeeklyStats();

    /**
     * 批量更新最近N天的剧目充值统计数据
     * 
     * @param days 天数
     * @return 执行结果描述
     */
    String batchUpdateRecentStats(int days);

    /**
     * 批量更新指定时间范围的剧目充值统计数据
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 执行结果描述
     */
    String batchUpdateRangeStats(String startDate, String endDate);

    /**
     * 全量统计最近N天的剧目充值统计数据
     * 
     * @param days 最近N天
     * @return 统计结果信息
     */
    String batchUpdateRecentDramaRechargeStats(int days);

    /**
     * 全量统计所有时间范围的剧目充值统计数据
     *
     * @return 统计结果信息
     */
    String batchUpdateAllStats();
}