package tv.shorthub.statistics.service;

import java.util.Date;

/**
 * 每小时订阅统计服务接口
 *
 * <AUTHOR>
 * @date 2025-01-02
 */
public interface IHourlySubscriptionStatsService {

    /**
     * 统计指定小时的订阅数据并更新到数据库
     * 
     * @param statHour 统计时间（整点小时）
     */
    void updateHourlySubscriptionStats(Date statHour);

    /**
     * 统计当前小时的订阅数据并更新到数据库
     */
    void updateCurrentHourSubscriptionStats();

    /**
     * 全量统计指定时间范围的订阅数据
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 统计结果信息
     */
    String batchUpdateSubscriptionStats(Date startDate, Date endDate);

    /**
     * 全量统计最近N天的订阅数据
     * 
     * @param days 最近N天
     * @return 统计结果信息
     */
    String batchUpdateRecentSubscriptionStats(int days);

    /**
     * 全量统计所有时间范围的订阅数据
     *
     * @return 统计结果信息
     */
    String batchUpdateAllStats();
} 