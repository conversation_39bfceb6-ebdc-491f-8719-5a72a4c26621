package tv.shorthub.statistics.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tv.shorthub.common.mapper.CommonMapper;
import tv.shorthub.statistics.service.IDailyFunnelStatsService;
import tv.shorthub.statistics.utils.TimeRangeUtils;
import tv.shorthub.statistics.utils.TimeZoneUtils;
import tv.shorthub.system.domain.StatisticsDailyFunnelStats;
import tv.shorthub.system.mapper.StatisticsDailyFunnelStatsMapper;
import tv.shorthub.common.utils.DateUtils;
import tv.shorthub.statistics.config.StatisticsConfig;

import java.util.*;
import java.text.SimpleDateFormat;

/**
 * 每日漏斗分析统计服务实现类
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
@Slf4j
@Service
public class DailyFunnelStatsServiceImpl implements IDailyFunnelStatsService {

    @Autowired
    private StatisticsDailyFunnelStatsMapper statisticsDailyFunnelStatsMapper;

    @Autowired
    private StatisticsConfig statisticsConfig;

    @Override
    public void updateCurrentDayFunnelStats() {
        // 获取当前日期的0点
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        Date currentDay = calendar.getTime();

        log.info("[漏斗分析日统计定时任务] 开始统计当前日期漏斗分析数据 - {}", new SimpleDateFormat("yyyy-MM-dd").format(currentDay));
        updateDailyFunnelStats(currentDay);
    }

    @Override
    public void updateDailyFunnelStats(Date statDate) {
        try {
            log.info("[漏斗分析日统计定时任务] 开始统计日期: {}", new SimpleDateFormat("yyyy-MM-dd").format(statDate));

            // 支持多时区统计
            List<StatisticsDailyFunnelStats> allResults = new ArrayList<>();
            List<String> timezones = statisticsConfig.getSupportedTimezones();

            for (String timezone : timezones) {
                try {
                    // 为每个时区计算该时区的"一天"对应的UTC+8时间范围
                    Calendar calendar = Calendar.getInstance();
                    calendar.setTime(statDate);
                    calendar.set(Calendar.HOUR_OF_DAY, 0);
                    calendar.set(Calendar.MINUTE, 0);
                    calendar.set(Calendar.SECOND, 0);
                    calendar.set(Calendar.MILLISECOND, 0);
                    Date timezoneStartTime = calendar.getTime();
                    calendar.add(Calendar.DATE, 1);
                    Date timezoneEndTime = calendar.getTime();

                    // 根据目标时区调整查询时间范围
                    Date[] adjustedTimeRange = TimeZoneUtils.adjustTimeRangeForQuery(timezoneStartTime, timezoneEndTime, timezone);
                    Date adjustedStartTime = adjustedTimeRange[0];
                    Date adjustedEndTime = adjustedTimeRange[1];

                    log.debug("[漏斗分析日统计定时任务] 时区 {} - 查询时间范围: {} 到 {}",
                        timezone,
                        new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(adjustedStartTime),
                        new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(adjustedEndTime));

                    // 获取时区偏移量
                    int timezoneOffset = TimeZoneUtils.getTimezoneOffset(timezone);
                    
                    List<StatisticsDailyFunnelStats> result = statisticsDailyFunnelStatsMapper.selectStatisticsByTimeRange(adjustedStartTime, adjustedEndTime, timezone, timezoneOffset);
                    if (result != null && !result.isEmpty()) {
                        allResults.addAll(result);
                        log.info("[漏斗分析日统计定时任务] 时区 {} 查询到 {} 条统计数据", timezone, result.size());
                    }
                } catch (Exception e) {
                    log.error("[漏斗分析日统计定时任务] 时区 {} 统计失败: {}", timezone, e.getMessage(), e);
                }
            }

            if (!allResults.isEmpty()) {
                statisticsDailyFunnelStatsMapper.batchInsertOrUpdateV1(allResults);
                log.info("[漏斗分析日统计定时任务] 成功更新 {} 条漏斗分析统计数据", allResults.size());
            } else {
                log.info("[漏斗分析日统计定时任务] 当前日期无漏斗分析数据");
            }
        } catch (Exception e) {
            log.error("[漏斗分析日统计定时任务] 统计失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 计算指定时间范围内的每日漏斗分析统计数据
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计数据列表
     */
    private List<StatisticsDailyFunnelStats> calculateDailyFunnelStats(Date startTime, Date endTime) {
        return executeStatisticsQuery(startTime, endTime);
    }

    /**
     * 执行统计查询
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计数据列表
     */
    private List<StatisticsDailyFunnelStats> executeStatisticsQuery(Date startTime, Date endTime) {
        try {
            // 支持多时区统计
            List<StatisticsDailyFunnelStats> allResults = new ArrayList<>();
            List<String> timezones = statisticsConfig.getSupportedTimezones();

            for (String timezone : timezones) {
                try {
                    // 根据目标时区调整查询时间范围
                    Date[] adjustedTimeRange = TimeZoneUtils.adjustTimeRangeForQuery(startTime, endTime, timezone);
                    Date adjustedStartTime = adjustedTimeRange[0];
                    Date adjustedEndTime = adjustedTimeRange[1];

                    log.debug("[漏斗分析日统计定时任务] 时区 {} - 原始时间范围: {} 到 {}",
                        timezone,
                        new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(startTime),
                        new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(endTime));
                    log.debug("[漏斗分析日统计定时任务] 时区 {} - 调整后时间范围: {} 到 {}",
                        timezone,
                        new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(adjustedStartTime),
                        new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(adjustedEndTime));

                    // 获取时区偏移量
                    int timezoneOffset = TimeZoneUtils.getTimezoneOffset(timezone);
                    
                    List<StatisticsDailyFunnelStats> result = statisticsDailyFunnelStatsMapper.selectStatisticsByTimeRange(adjustedStartTime, adjustedEndTime, timezone, timezoneOffset);
                    if (result != null && !result.isEmpty()) {
                        allResults.addAll(result);
                        log.info("[漏斗分析日统计定时任务] 时区 {} 查询到 {} 条统计数据", timezone, result.size());
                    }
                } catch (Exception e) {
                    log.error("[漏斗分析日统计定时任务] 时区 {} 查询失败: {}", timezone, e.getMessage());
                }
            }

            log.info("[漏斗分析日统计定时任务] 总共查询到 {} 条统计数据", allResults.size());
            return allResults;
        } catch (Exception e) {
            log.error("[漏斗分析日统计定时任务] 查询统计数据失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public String batchUpdateFunnelStats(Date startDate, Date endDate) {
        if (startDate == null || endDate == null) {
            throw new IllegalArgumentException("开始日期和结束日期不能为空");
        }

        if (startDate.after(endDate)) {
            throw new IllegalArgumentException("开始日期不能晚于结束日期");
        }

        StringBuilder result = new StringBuilder();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

        try {
            log.info("[漏斗分析批量统计] 开始批量统计，时间范围：{} 到 {}",
                    sdf.format(startDate), sdf.format(endDate));

            int totalDays = 0;
            int successDays = 0;
            int failedDays = 0;

            Calendar calendar = Calendar.getInstance();
            calendar.setTime(startDate);

            while (!calendar.getTime().after(endDate)) {
                Date currentDate = calendar.getTime();
                totalDays++;

                try {
                    updateDailyFunnelStats(currentDate);
                    successDays++;
                    log.info("[漏斗分析批量统计] 成功统计 {} 的数据", sdf.format(currentDate));
                } catch (Exception e) {
                    failedDays++;
                    log.error("[漏斗分析批量统计] 统计失败：{}", sdf.format(currentDate), e);
                }

                calendar.add(Calendar.DATE, 1);
            }

            result.append(String.format("批量统计完成。总天数：%d，成功：%d，失败：%d",
                                        totalDays, successDays, failedDays));

            log.info("[漏斗分析批量统计] 批量统计完成，结果：{}", result.toString());

        } catch (Exception e) {
            log.error("[漏斗分析批量统计] 批量统计过程中发生错误", e);
            result.append("批量统计失败：").append(e.getMessage());
        }

        return result.toString();
    }

    @Override
    public String batchUpdateRecentFunnelStats(int days) {
        if (days <= 0) {
            throw new IllegalArgumentException("天数必须大于0");
        }

        // 限制最大天数
        if (days > 365) {
            days = 365;
        }

        // 计算开始日期和结束日期
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);

        Date endDate = calendar.getTime();
        calendar.add(Calendar.DATE, -days + 1);
        Date startDate = calendar.getTime();

        log.info("[漏斗分析批量统计] 开始统计最近 {} 天的数据", days);
        return batchUpdateFunnelStats(startDate, endDate);
    }

    @Override
    public String batchUpdateAllStats() {
        log.info("[漏斗分析批量统计] 开始全量统计所有数据");

        // 这里可以根据实际需求设置一个合适的开始日期
        // 例如从系统上线日期开始，或者从有数据的最早日期开始
        Calendar calendar = Calendar.getInstance();
        calendar.set(2024, Calendar.JANUARY, 1); // 从2024年1月1日开始
        Date startDate = calendar.getTime();

        calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        Date endDate = calendar.getTime();

        return batchUpdateFunnelStats(startDate, endDate);
    }
}
