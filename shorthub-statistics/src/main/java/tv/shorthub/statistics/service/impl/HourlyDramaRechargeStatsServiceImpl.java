package tv.shorthub.statistics.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tv.shorthub.statistics.service.IHourlyDramaRechargeStatsService;
import tv.shorthub.system.mapper.StatisticsHourlyDramaRechargeStatsMapper;
import tv.shorthub.system.domain.StatisticsHourlyDramaRechargeStats;
import tv.shorthub.common.utils.DateUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Date;
import java.util.Map;
import java.util.Calendar;
import java.text.SimpleDateFormat;

/**
 * 小时级剧目充值统计服务实现类
 * 
 * <AUTHOR>
 * @date 2025-07-15
 */
@Slf4j
@Service
public class HourlyDramaRechargeStatsServiceImpl implements IHourlyDramaRechargeStatsService {

    @Autowired
    private StatisticsHourlyDramaRechargeStatsMapper statisticsHourlyDramaRechargeStatsMapper;

    @Override
    public void calculateAndSaveCurrentHourStats() {
        log.info("开始计算当前小时的剧目充值统计数据");
        
        // 获取当前小时的开始和结束时间
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime startTime = now.withMinute(0).withSecond(0).withNano(0);
        LocalDateTime endTime = startTime.plusHours(1).minusSeconds(1);
        
        calculateAndSaveStats(startTime, endTime);
        
        log.info("完成计算当前小时的剧目充值统计数据");
    }

    @Override
    public void recalculateYesterdayStats() {
        log.info("开始重新计算昨天的剧目充值统计数据");
        
        // 获取昨天的开始和结束时间
        LocalDateTime yesterday = LocalDateTime.now().minusDays(1);
        LocalDateTime startTime = yesterday.withHour(0).withMinute(0).withSecond(0).withNano(0);
        LocalDateTime endTime = yesterday.withHour(23).withMinute(59).withSecond(59).withNano(999999999);
        
        calculateAndSaveHourlyStats(startTime, endTime);
        
        log.info("完成重新计算昨天的剧目充值统计数据");
    }

    @Override
    public void recalculateWeeklyStats() {
        log.info("开始重新计算最近7天的剧目充值统计数据");
        
        // 获取最近7天的开始和结束时间
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime startTime = now.minusDays(7).withHour(0).withMinute(0).withSecond(0).withNano(0);
        LocalDateTime endTime = now.withHour(23).withMinute(59).withSecond(59).withNano(999999999);
        
        calculateAndSaveHourlyStats(startTime, endTime);
        
        log.info("完成重新计算最近7天的剧目充值统计数据");
    }

    /**
     * 计算并保存指定时间段的统计数据
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     */
    private void calculateAndSaveStats(LocalDateTime startTime, LocalDateTime endTime) {
        String startTimeStr = startTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        String endTimeStr = endTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        
        log.info("计算统计数据，时间范围：{} - {}", startTimeStr, endTimeStr);
        
        try {
            // 调用 Mapper 中的查询方法获取统计数据
            List<StatisticsHourlyDramaRechargeStats> statsList = 
                statisticsHourlyDramaRechargeStatsMapper.selectHourlyDramaRechargeStats(startTimeStr, endTimeStr);
            
            if (statsList != null && !statsList.isEmpty()) {
                // 批量插入或更新统计数据
                statisticsHourlyDramaRechargeStatsMapper.batchInsertOrUpdate(statsList);
                log.info("成功处理 {} 条统计数据", statsList.size());
            } else {
                log.info("没有找到需要处理的统计数据");
            }
        } catch (Exception e) {
            log.error("计算统计数据时发生错误", e);
            throw e;
        }
    }

    /**
     * 按小时计算并保存指定时间段的统计数据
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     */
    private void calculateAndSaveHourlyStats(LocalDateTime startTime, LocalDateTime endTime) {
        LocalDateTime current = startTime;
        
        while (current.isBefore(endTime)) {
            LocalDateTime hourEnd = current.plusHours(1).minusSeconds(1);
            if (hourEnd.isAfter(endTime)) {
                hourEnd = endTime;
            }
            
            calculateAndSaveStats(current, hourEnd);
            current = current.plusHours(1);
        }
    }

    /**
     * 批量更新最近N天的剧目充值统计数据
     */
    @Override
    public String batchUpdateRecentStats(int days) {
        log.info("开始批量更新最近 {} 天的剧目充值统计数据", days);
        
        try {
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime startTime = now.minusDays(days).withHour(0).withMinute(0).withSecond(0).withNano(0);
            LocalDateTime endTime = now.withHour(23).withMinute(59).withSecond(59).withNano(999999999);
            
            calculateAndSaveHourlyStats(startTime, endTime);
            
            String result = String.format("成功批量更新最近 %d 天的剧目充值统计数据", days);
            log.info(result);
            return result;
        } catch (Exception e) {
            String error = String.format("批量更新最近 %d 天的剧目充值统计数据失败: %s", days, e.getMessage());
            log.error(error, e);
            throw new RuntimeException(error, e);
        }
    }

    /**
     * 批量更新指定时间范围的剧目充值统计数据
     */
    @Override
    public String batchUpdateRangeStats(String startDate, String endDate) {
        log.info("开始批量更新 {} 到 {} 的剧目充值统计数据", startDate, endDate);
        
        try {
            LocalDateTime startTime = LocalDateTime.parse(startDate + " 00:00:00", 
                DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            LocalDateTime endTime = LocalDateTime.parse(endDate + " 23:59:59", 
                DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            
            calculateAndSaveHourlyStats(startTime, endTime);
            
            String result = String.format("成功批量更新 %s 到 %s 的剧目充值统计数据", startDate, endDate);
            log.info(result);
            return result;
        } catch (Exception e) {
            String error = String.format("批量更新 %s 到 %s 的剧目充值统计数据失败: %s", startDate, endDate, e.getMessage());
            log.error(error, e);
            throw new RuntimeException(error, e);
        }
    }

    /**
     * 全量统计最近N天的剧目充值统计数据
     */
    @Override
    public String batchUpdateRecentDramaRechargeStats(int days) {
        log.info("开始全量统计最近 {} 天的剧目充值统计数据", days);
        
        try {
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime startTime = now.minusDays(days).withHour(0).withMinute(0).withSecond(0).withNano(0);
            LocalDateTime endTime = now.withHour(23).withMinute(59).withSecond(59).withNano(999999999);
            
            calculateAndSaveHourlyStats(startTime, endTime);
            
            String result = String.format("成功全量统计最近 %d 天的剧目充值统计数据", days);
            log.info(result);
            return result;
        } catch (Exception e) {
            String error = String.format("全量统计最近 %d 天的剧目充值统计数据失败: %s", days, e.getMessage());
            log.error(error, e);
            throw new RuntimeException(error, e);
        }
    }

    /**
     * 全量统计所有时间范围的剧目充值统计数据
     */
    @Override
    public String batchUpdateAllStats() {
        log.info("[剧目充值全量统计] 开始执行...");
        
        try {
            // 获取时间范围
            Map<String, Date> timeRange = statisticsHourlyDramaRechargeStatsMapper.selectTimeRange();
            Date minDate = timeRange != null ? timeRange.get("min_date") : null;
            Date maxDate = timeRange != null ? timeRange.get("max_date") : null;

            // 如果没有找到数据，直接返回
            if (minDate == null || maxDate == null) {
                return "[剧目充值全量统计] 未找到任何数据，任务结束。";
            }

            log.info("[剧目充值全量统计] 数据时间范围: {} 到 {}", 
                    new SimpleDateFormat("yyyy-MM-dd").format(minDate),
                    new SimpleDateFormat("yyyy-MM-dd").format(maxDate));

            int totalHours = 0;
            int successHours = 0;
            int totalRecords = 0;

            // 按小时遍历整个时间范围
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(minDate);
            // 将开始时间设置为当天0点
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            calendar.set(Calendar.MILLISECOND, 0);

            Calendar endCalendar = Calendar.getInstance();
            endCalendar.setTime(maxDate);
            // 将结束时间设置为当天23点
            endCalendar.set(Calendar.HOUR_OF_DAY, 23);
            endCalendar.set(Calendar.MINUTE, 59);
            endCalendar.set(Calendar.SECOND, 59);

            while (calendar.getTime().before(endCalendar.getTime())) {
                try {
                    Date currentHour = calendar.getTime();
                    Date nextHour = new Date(currentHour.getTime() + 3600000); // 加一小时

                    // 统计当前小时
                    String startTimeStr = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(currentHour);
                    String endTimeStr = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(nextHour);
                    
                    List<StatisticsHourlyDramaRechargeStats> hourlyStats = 
                        statisticsHourlyDramaRechargeStatsMapper.selectHourlyDramaRechargeStats(startTimeStr, endTimeStr);
                    
                    if (hourlyStats != null && !hourlyStats.isEmpty()) {
                        statisticsHourlyDramaRechargeStatsMapper.batchInsertOrUpdate(hourlyStats);
                        totalRecords += hourlyStats.size();
                        successHours++;
                    }
                    
                    totalHours++;
                    
                    // 每100个小时输出一次进度
                    if (totalHours % 100 == 0) {
                        log.info("[剧目充值全量统计] 进度: {}/{} 小时，已更新 {} 条记录", 
                                successHours, totalHours, totalRecords);
                    }
                    
                } catch (Exception e) {
                    log.error("[剧目充值全量统计] 统计小时 {} 失败: {}", 
                            new SimpleDateFormat("yyyy-MM-dd HH:00:00").format(calendar.getTime()),
                            e.getMessage());
                }

                // 移动到下一小时
                calendar.add(Calendar.HOUR_OF_DAY, 1);
            }

            String result = String.format("[剧目充值全量统计] 完成！共处理 %d 小时，成功 %d 小时，更新 %d 条记录", 
                    totalHours, successHours, totalRecords);
            log.info(result);
            return result;

        } catch (Exception e) {
            String error = "[剧目充值全量统计] 执行失败: " + e.getMessage();
            log.error(error, e);
            throw new RuntimeException(error, e);
        }
    }
}