package tv.shorthub.statistics.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tv.shorthub.statistics.config.StatisticsConfig;
import tv.shorthub.statistics.service.IDailyDramaEpisodeStatsService;
import tv.shorthub.statistics.utils.TimeZoneUtils;
import tv.shorthub.system.domain.StatisticsDailyDramaEpisodeStats;
import tv.shorthub.system.service.IStatisticsDailyDramaEpisodeStatsService;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 每日剧集分集统计服务实现类
 *
 * <AUTHOR>
 * @date 2025-08-01
 */
@Slf4j
@Service
public class DailyDramaEpisodeStatsServiceImpl implements IDailyDramaEpisodeStatsService {

    @Autowired
    private IStatisticsDailyDramaEpisodeStatsService statisticsDailyDramaEpisodeStatsService;

    @Autowired
    private StatisticsConfig statisticsConfig;

    @Override
    public void updateCurrentDayEpisodeStats() {
        // 获取当前日期的0点
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        Date currentDay = calendar.getTime();

        log.info("[剧集分集日统计定时任务] 开始统计当前日期剧集分集数据 - {}", new SimpleDateFormat("yyyy-MM-dd").format(currentDay));
        updateDailyEpisodeStats(currentDay);
    }

    @Override
    public void updateDailyEpisodeStats(Date statDate) {
        try {
            // 计算统计时间范围（当天0点到次日0点）
            Date startTime = statDate;
            Date endTime = DateUtils.addDays(statDate, 1);

            log.info("[剧集分集日统计定时任务] 统计时间范围: {} 到 {}",
                new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(startTime),
                new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(endTime));

            List<StatisticsDailyDramaEpisodeStats> allResults = new ArrayList<>();

            // 遍历所有支持的时区进行统计
            for (String timezone : statisticsConfig.getSupportedTimezones()) {
                try {
                    // 根据目标时区调整查询时间范围
                    Date[] adjustedTimeRange = TimeZoneUtils.adjustTimeRangeForQuery(startTime, endTime, timezone);
                    Date adjustedStartTime = adjustedTimeRange[0];
                    Date adjustedEndTime = adjustedTimeRange[1];

                    log.debug("[剧集分集日统计定时任务] 时区 {} - 原始时间范围: {} 到 {}",
                        timezone,
                        new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(startTime),
                        new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(endTime));
                    log.debug("[剧集分集日统计定时任务] 时区 {} - 调整后时间范围: {} 到 {}",
                        timezone,
                        new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(adjustedStartTime),
                        new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(adjustedEndTime));

                    // 获取时区偏移量
                    int timezoneOffset = TimeZoneUtils.getTimezoneOffset(timezone);
                    
                    // 调用 Mapper 查询统计数据
                    List<StatisticsDailyDramaEpisodeStats> result = statisticsDailyDramaEpisodeStatsService.getMapper()
                        .selectDailyDramaEpisodeStats(adjustedStartTime, adjustedEndTime, timezone, timezoneOffset);

                    if (result != null && !result.isEmpty()) {
                        // 计算中位数观看时长
                        calculateMedianWatchSeconds(result, adjustedStartTime, adjustedEndTime);

                        // 设置创建和更新信息
                        Date now = new Date();
                        for (StatisticsDailyDramaEpisodeStats stats : result) {
                            stats.setCreateBy("system");
                            stats.setCreateTime(now);
                            stats.setUpdateBy("system");
                            stats.setUpdateTime(now);
                        }

                        allResults.addAll(result);
                        log.info("[剧集分集日统计定时任务] 时区 {} 查询到 {} 条统计数据", timezone, result.size());
                    }
                } catch (Exception e) {
                    log.error("[剧集分集日统计定时任务] 时区 {} 统计失败: {}", timezone, e.getMessage(), e);
                }
            }

            // 批量插入或更新统计数据
            if (!allResults.isEmpty()) {
                statisticsDailyDramaEpisodeStatsService.getMapper().batchInsertOrUpdate(allResults);
                log.info("[剧集分集日统计定时任务] 成功更新 {} 条剧集分集统计数据", allResults.size());
            } else {
                log.info("[剧集分集日统计定时任务] 当前日期无剧集分集数据");
            }
        } catch (Exception e) {
            log.error("[剧集分集日统计定时任务] 统计失败: {}", e.getMessage(), e);
        }
    }

    @Override
    public void batchUpdateRecentEpisodeStats(int days) {
        log.info("[剧集分集日统计定时任务] 开始批量更新最近 {} 天的剧集分集统计数据", days);

        Calendar calendar = Calendar.getInstance();
        for (int i = 0; i < days; i++) {
            Date statDate = calendar.getTime();
            updateDailyEpisodeStats(statDate);
            calendar.add(Calendar.DATE, -1);
        }

        log.info("[剧集分集日统计定时任务] 批量更新最近 {} 天的剧集分集统计数据完成", days);
    }

    @Override
    public String batchUpdateEpisodeStats(Date startDate, Date endDate) {
        log.info("[剧集分集日统计定时任务] 开始批量更新指定时间范围的剧集分集统计数据: {} 到 {}", startDate, endDate);

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(startDate);

        int dayCount = 0;
        while (!calendar.getTime().after(endDate)) {
            Date statDate = calendar.getTime();
            updateDailyEpisodeStats(statDate);
            calendar.add(Calendar.DATE, 1);
            dayCount++;
        }

        log.info("[剧集分集日统计定时任务] 批量更新指定时间范围的剧集分集统计数据完成，共处理 {} 天", dayCount);
        return "批量更新指定时间范围的剧集分集统计数据完成，共处理 " + dayCount + " 天";
    }

    @Override
    public String batchUpdateAllEpisodeStats() {
        log.info("[剧集分集日统计定时任务] 开始全量重算所有历史剧集分集统计数据");

        try {
            // 获取数据的时间范围 - 最近一年
            Date startDate = new Date(System.currentTimeMillis() - 365L * 24 * 60 * 60 * 1000);
            Date endDate = new Date();

            Calendar calendar = Calendar.getInstance();
            calendar.setTime(startDate);

            int processedDays = 0;
            while (calendar.getTime().before(endDate)) {
                Date statDate = calendar.getTime();
                updateDailyEpisodeStats(statDate);
                calendar.add(Calendar.DATE, 1);
                processedDays++;
            }

            String result = String.format("全量重算完成，处理了 %d 天的数据", processedDays);
            log.info("[剧集分集日统计定时任务] {}", result);
            return result;
        } catch (Exception e) {
            String errorMsg = "全量重算失败: " + e.getMessage();
            log.error("[剧集分集日统计定时任务] {}", errorMsg, e);
            return errorMsg;
        }
    }



    /**
     * 计算中位数观看时长
     * @param results 统计结果列表
     * @param startTime 开始时间
     * @param endTime 结束时间
     */
    private void calculateMedianWatchSeconds(List<StatisticsDailyDramaEpisodeStats> results, Date startTime, Date endTime) {
        for (StatisticsDailyDramaEpisodeStats stats : results) {
            try {
                // 获取该剧集分集的所有观看时长数据
                List<Long> watchSeconds = statisticsDailyDramaEpisodeStatsService.getMapper()
                    .getWatchSecondsForMedian(
                        stats.getContentId(),
                        stats.getSerialNumber().intValue(),
                        startTime,
                        endTime,
                        stats.getTfid() != null ? stats.getTfid() : ""
                    );

                if (watchSeconds != null && !watchSeconds.isEmpty()) {
                    // 计算中位数
                    Collections.sort(watchSeconds);
                    int size = watchSeconds.size();
                    long median;
                    if (size % 2 == 0) {
                        median = (watchSeconds.get(size / 2 - 1) + watchSeconds.get(size / 2)) / 2;
                    } else {
                        median = watchSeconds.get(size / 2);
                    }
                    stats.setMedianWatchSeconds(median);
                } else {
                    stats.setMedianWatchSeconds(0L);
                }
            } catch (Exception e) {
                log.warn("[剧集分集日统计定时任务] 计算中位数失败 - contentId: {}, serialNumber: {}, error: {}",
                    stats.getContentId(), stats.getSerialNumber(), e.getMessage());
                stats.setMedianWatchSeconds(0L);
            }
        }
    }
}
