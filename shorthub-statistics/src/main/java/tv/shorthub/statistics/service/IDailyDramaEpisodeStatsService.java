  package tv.shorthub.statistics.service;

  import java.util.Date;

  /**
   * 每日剧集分集统计服务接口
   *
   * <AUTHOR>
   * @date 2025-07-22
   */
  public interface IDailyDramaEpisodeStatsService {

      /**
       * 更新当天的剧集分集统计数据
       */
      void updateCurrentDayEpisodeStats();

      /**
       * 更新指定日期的剧集分集统计数据
       * @param statDate 统计日期
       */
      void updateDailyEpisodeStats(Date statDate);

      /**
       * 批量更新最近N天的剧集分集统计数据
       *
       * @param days 天数
       */
      void batchUpdateRecentEpisodeStats(int days);

      /**
       * 批量更新指定时间范围的剧集分集统计数据
       * @param startDate 开始日期
       * @param endDate 结束日期
       * @return 执行结果描述
       */
      String batchUpdateEpisodeStats(Date startDate, Date endDate);

      /**
       * 批量更新所有历史剧集分集统计数据
       * @return 执行结果描述
       */
      String batchUpdateAllEpisodeStats();
  }
