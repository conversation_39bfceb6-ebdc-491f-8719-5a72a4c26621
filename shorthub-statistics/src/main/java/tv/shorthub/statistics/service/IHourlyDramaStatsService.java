package tv.shorthub.statistics.service;

import java.util.Date;

/**
 * 每小时剧集统计服务接口
 *
 * <AUTHOR>
 * @date 2025-07-11
 */
public interface IHourlyDramaStatsService {

    /**
     * 统计指定小时的剧集数据并更新到数据库
     * 
     * @param statHour 统计时间（整点小时）
     */
    void updateHourlyDramaStats(Date statHour);

    /**
     * 统计当前小时的剧集数据并更新到数据库
     */
    void calculateAndSaveCurrentHourStats();

    /**
     * 重新统计昨天的剧集数据
     */
    void recalculateYesterdayStats();

    /**
     * 重新统计最近7天的剧集数据
     */
    void recalculateWeeklyStats();

    /**
     * 全量统计指定时间范围的剧集数据
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 统计结果信息
     */
    String batchUpdateDramaStats(Date startDate, Date endDate);

    /**
     * 全量统计最近N天的剧集数据
     * 
     * @param days 最近N天
     * @return 统计结果信息
     */
    String batchUpdateRecentDramaStats(int days);

    /**
     * 全量统计所有时间范围的剧集数据
     *
     * @return 统计结果信息
     */
    String batchUpdateAllStats();
}