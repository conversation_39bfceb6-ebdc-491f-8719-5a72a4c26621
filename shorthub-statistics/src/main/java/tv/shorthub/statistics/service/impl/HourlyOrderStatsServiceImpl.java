package tv.shorthub.statistics.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tv.shorthub.statistics.service.IHourlyOrderStatsService;
import tv.shorthub.statistics.utils.TimeRangeUtils;
import tv.shorthub.system.domain.StatisticsHourlyOrderStats;
import tv.shorthub.system.service.IStatisticsHourlyOrderStatsService;
import tv.shorthub.common.utils.DateUtils;

import java.math.BigDecimal;
import java.util.*;
import java.text.SimpleDateFormat;

/**
 * 每小时订单统计服务实现类
 *
 * <AUTHOR>
 * @date 2025-01-02
 */
@Slf4j
@Service
public class HourlyOrderStatsServiceImpl implements IHourlyOrderStatsService {

    @Autowired
    private IStatisticsHourlyOrderStatsService statisticsHourlyOrderStatsService;

    @Override
    public void updateCurrentHourOrderStats() {
        // 获取当前时间的整点小时
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        Date currentHour = calendar.getTime();

        log.info("[订单统计定时任务] 开始统计当前小时订单数据 - {}",
                new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(currentHour));

        updateHourlyOrderStats(currentHour);
    }

    @Override
    public void updateHourlyOrderStats(Date statHour) {
        try {
            // 计算统计时间范围
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(statHour);
            Date startTime = calendar.getTime();

            calendar.add(Calendar.HOUR_OF_DAY, 1);
            Date endTime = calendar.getTime();

            log.info("[订单统计定时任务] 统计时间范围: {} - {}",
                    new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(startTime),
                    new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(endTime));

            // 获取统计数据
            List<StatisticsHourlyOrderStats> hourlyStats = calculateHourlyOrderStats(startTime, endTime);

            if (hourlyStats != null && !hourlyStats.isEmpty()) {
                // 批量更新数据
                statisticsHourlyOrderStatsService.getMapper().batchInsertOrUpdateStats(hourlyStats);
                log.info("[订单统计定时任务] 成功更新 {} 条订单统计数据", hourlyStats.size());
            } else {
                log.info("[订单统计定时任务] 当前时间段无订单数据");
            }

        } catch (Exception e) {
            log.error("[订单统计定时任务] 统计失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 计算指定时间范围内的每小时订单统计数据
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计数据列表
     */
    private List<StatisticsHourlyOrderStats> calculateHourlyOrderStats(Date startTime, Date endTime) {
        // 直接调用 Mapper 查询方法
        return executeStatisticsQuery(startTime, endTime);
    }

    /**
     * 执行统计查询
     */
    private List<StatisticsHourlyOrderStats> executeStatisticsQuery(Date startTime, Date endTime) {
        try {
            // 调用 Mapper 中的自定义查询方法
            List<StatisticsHourlyOrderStats> result = statisticsHourlyOrderStatsService.getMapper()
                    .selectHourlyOrderStats(startTime, endTime);


            log.info("[订单统计定时任务] 查询到 {} 条统计数据", result != null ? result.size() : 0);

            return result != null ? result : new ArrayList<>();

        } catch (Exception e) {
            log.error("[订单统计定时任务] 查询统计数据失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public String batchUpdateOrderStats(Date startDate, Date endDate) {
        try {
            log.info("[订单统计批量任务] 开始全量统计订单数据 - 时间范围: {} 到 {}", 
                    new SimpleDateFormat("yyyy-MM-dd").format(startDate),
                    new SimpleDateFormat("yyyy-MM-dd").format(endDate));

            int totalHours = 0;
            int successHours = 0;
            int totalRecords = 0;

            // 按小时遍历整个时间范围
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(startDate);
            // 将开始时间设置为当天0点
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            calendar.set(Calendar.MILLISECOND, 0);

            Calendar endCalendar = Calendar.getInstance();
            endCalendar.setTime(endDate);
            // 将结束时间设置为当天23点
            endCalendar.set(Calendar.HOUR_OF_DAY, 23);
            endCalendar.set(Calendar.MINUTE, 59);
            endCalendar.set(Calendar.SECOND, 59);

            while (calendar.getTime().before(endCalendar.getTime())) {
                try {
                    Date currentHour = calendar.getTime();
                    Date nextHour = new Date(currentHour.getTime() + 3600000); // 加一小时

                    // 统计当前小时
                    List<StatisticsHourlyOrderStats> hourlyStats = executeStatisticsQuery(currentHour, nextHour);
                    
                    if (hourlyStats != null && !hourlyStats.isEmpty()) {
                        statisticsHourlyOrderStatsService.getMapper().batchInsertOrUpdateStats(hourlyStats);
                        totalRecords += hourlyStats.size();
                        successHours++;
                    }
                    
                    totalHours++;
                    
                    // 每100个小时输出一次进度
                    if (totalHours % 100 == 0) {
                        log.info("[订单统计批量任务] 进度: {}/{} 小时，已更新 {} 条记录", 
                                successHours, totalHours, totalRecords);
                    }
                    
                } catch (Exception e) {
                    log.error("[订单统计批量任务] 统计小时 {} 失败: {}", 
                            new SimpleDateFormat("yyyy-MM-dd HH:00:00").format(calendar.getTime()),
                            e.getMessage());
                }

                // 移动到下一小时
                calendar.add(Calendar.HOUR_OF_DAY, 1);
            }

            String result = String.format("订单统计完成！共处理 %d 小时，成功 %d 小时，更新 %d 条记录", 
                    totalHours, successHours, totalRecords);
            log.info("[订单统计批量任务] {}", result);
            return result;

        } catch (Exception e) {
            String error = "订单批量统计失败: " + e.getMessage();
            log.error("[订单统计批量任务] {}", error, e);
            return error;
        }
    }

    @Override
    public String batchUpdateRecentOrderStats(int days) {
        Date endDate = new Date();
        Date startDate = DateUtils.addDays(endDate, -days);
        return batchUpdateOrderStats(startDate, endDate);
    }

    @Override
    public String batchUpdateAllStats() {
        log.info("[订单全量统计] 开始执行...");
        Map<String, Date> timeRange = statisticsHourlyOrderStatsService.getMapper().selectTimeRange();
        Date minDate = timeRange.get("min_date");
        Date maxDate = timeRange.get("max_date");

        // 如果没有订单数据，直接返回
        if (minDate == null || maxDate == null) {
            return "[订单全量统计] 未找到任何订单数据，任务结束。";
        }

        log.info("[订单全量统计] 数据时间范围: {} 到 {}", minDate, maxDate);

        // 调用按时间范围统计的方法
        return batchUpdateOrderStats(minDate, maxDate);
    }
}
 