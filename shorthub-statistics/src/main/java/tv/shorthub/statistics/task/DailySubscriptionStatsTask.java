package tv.shorthub.statistics.task;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import tv.shorthub.statistics.service.IDailySubscriptionStatsService;

/**
 * 每日订阅统计定时任务
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
@Slf4j
@Component
public class DailySubscriptionStatsTask {

    @Autowired
    private IDailySubscriptionStatsService dailySubscriptionStatsService;

    /**
     * 每3分钟执行一次，更新当天的订阅统计数据
     * cron表达式: 0 0/3 * * * ? 表示每3分钟执行一次，从第0分钟开始
     */
    @Scheduled(cron = "0 0/3 * * * ?")
    public void updateCurrentDaySubscriptionStats() {
        log.info("[订阅日统计定时任务] 开始执行每日订阅统计任务");
        try {
            dailySubscriptionStatsService.updateCurrentDaySubscriptionStats();
            log.info("[订阅日统计定时任务] 每日订阅统计任务执行完成");
        } catch (Exception e) {
            log.error("[订阅日统计定时任务] 执行失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 每日凌晨2:25执行，全量统计昨天一整天的数据，作为数据兜底
     * cron表达式: 0 25 2 * * ?
     */
    @Scheduled(cron = "0 25 2 * * ?")
    public void batchUpdateYesterdayStats() {
        log.info("[订阅日统计每日兜底任务] 开始执行");
        try {
            dailySubscriptionStatsService.batchUpdateRecentSubscriptionStats(1);
            log.info("[订阅日统计每日兜底任务] 成功完成");
        } catch (Exception e) {
            log.error("[订阅日统计每日兜底任务] 执行失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 每周日凌晨3:25执行，全量重算所有历史数据
     * cron表达式: 0 25 3 ? * SUN
     */
    @Scheduled(cron = "0 25 3 ? * SUN")
    public void batchUpdateAllStats() {
        log.info("[订阅日统计每周全量任务] 开始执行");
        try {
            String result = dailySubscriptionStatsService.batchUpdateAllStats();
            log.info("[订阅日统计每周全量任务] 执行完成: {}", result);
        } catch (Exception e) {
            log.error("[订阅日统计每周全量任务] 执行失败: {}", e.getMessage(), e);
        }
    }
}
