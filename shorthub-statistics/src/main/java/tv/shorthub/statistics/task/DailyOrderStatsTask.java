package tv.shorthub.statistics.task;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import tv.shorthub.statistics.service.IDailyOrderStatsService;

/**
 * 每日订单统计定时任务
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
@Slf4j
@Component
public class DailyOrderStatsTask {

    @Autowired
    private IDailyOrderStatsService dailyOrderStatsService;

    /**
     * 每1分钟执行一次，更新当天的订单统计数据
     * cron表达式: 0 0/1 * * * ? 表示每1分钟执行一次
     */
    @Scheduled(cron = "0 0/1 * * * ?")
    public void updateCurrentDayOrderStats() {
        log.info("[订单日统计定时任务] 开始执行每日订单统计任务");
        try {
            dailyOrderStatsService.updateCurrentDayOrderStats();
            log.info("[订单日统计定时任务] 每日订单统计任务执行完成");
        } catch (Exception e) {
            log.error("[订单日统计定时任务] 执行失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 每日凌晨2:10执行，全量统计昨天一整天的数据，作为数据兜底
     * cron表达式: 0 10 2 * * ?
     */
    @Scheduled(cron = "0 10 2 * * ?")
    public void batchUpdateYesterdayStats() {
        log.info("[订单日统计每日兜底任务] 开始执行");
        try {
            dailyOrderStatsService.batchUpdateRecentOrderStats(1);
            log.info("[订单日统计每日兜底任务] 成功完成");
        } catch (Exception e) {
            log.error("[订单日统计每日兜底任务] 执行失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 每周日凌晨3:10执行，全量重算所有历史数据
     * cron表达式: 0 10 3 ? * SUN
     */
    @Scheduled(cron = "0 10 3 ? * SUN")
    public void batchUpdateAllStats() {
        log.info("[订单日统计每周全量任务] 开始执行");
        try {
            String result = dailyOrderStatsService.batchUpdateAllStats();
            log.info("[订单日统计每周全量任务] 执行完成: {}", result);
        } catch (Exception e) {
            log.error("[订单日统计每周全量任务] 执行失败: {}", e.getMessage(), e);
        }
    }
}
