package tv.shorthub.statistics.task;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import tv.shorthub.system.mapper.StatisticsHourlyOrderStatsMapper;
import tv.shorthub.system.mapper.StatisticsDailyOrderStatsMapper;
import tv.shorthub.system.mapper.SysDingtalkLogMapper;
import tv.shorthub.dingtalk.service.DingTalkService;
import tv.shorthub.system.domain.SysDingtalkLog;
import com.alibaba.fastjson2.JSONObject;
import tv.shorthub.system.domain.StatisticsHourlyOrderStats;
import tv.shorthub.system.domain.StatisticsDailyOrderStats;
import tv.shorthub.statistics.utils.TimeZoneUtils;
import tv.shorthub.dingtalk.config.DingTalkProperties;

import java.time.ZonedDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Date;
import java.util.Calendar;
import java.math.BigDecimal;

/**
 * 钉钉通知定时任务
 * 统计订单和订阅数据并发送到钉钉
 *
 * <AUTHOR>
 * @date 2025-01-18
 */
@Slf4j
@Component
public class DingTalkNotificationTask {

    @Autowired
    private StatisticsHourlyOrderStatsMapper hourlyOrderStatsMapper;

    @Autowired
    private StatisticsDailyOrderStatsMapper dailyOrderStatsMapper;



    @Autowired
    private DingTalkService dingTalkService;

    @Autowired
    private SysDingtalkLogMapper sysDingtalkLogMapper;

    @Autowired
    private DingTalkProperties dingTalkProperties;

    /**
     * 每2小时统计订单+订阅数据发送钉钉
     * 
     */
     @Scheduled(cron = "0 0 */2 * * ?")
    public void sendHourlyStatistics() {
        log.info("[钉钉统计通知] 开始执行每2小时统计任务");

        try {
            // 获取当前系统时间（UTC+8）
            ZonedDateTime systemNow = ZonedDateTime.now(ZoneId.of("UTC+8"));

            // 检查是否有订单数据，总订单为0时不发送通知
            if (!hasHourlyOrderData(systemNow)) {
                log.info("[钉钉统计通知] 2小时统计总订单数为0，跳过钉钉推送");
                return;
            }

            // 统计前2小时的订单数据（包含金币和订阅）
            String hourlyOrderStats = getHourlyOrderStatistics(systemNow);

            // 构建钉钉消息
            // 时间段应该是从2小时前到当前整点（完整2小时）
            ZonedDateTime periodStart = systemNow.minusHours(2).withMinute(0).withSecond(0).withNano(0);
            ZonedDateTime periodEnd = systemNow.withMinute(0).withSecond(0).withNano(0);

            String title = String.format("⏰ 2小时统计 - %s至%s",
                periodStart.format(DateTimeFormatter.ofPattern("MM-dd HH:mm")),
                periodEnd.format(DateTimeFormatter.ofPattern("MM-dd HH:mm")));

            StringBuilder content = new StringBuilder();
            content.append("# ⏰ **2小时统计报告**\n\n");
            content.append("📍 **时间段**: ").append(periodStart.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm")))
                .append(" - ").append(periodEnd.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm"))).append(" (UTC+8)\n\n");
            content.append("## 📈 快速概览\n");
            content.append(hourlyOrderStats).append("\n\n");
            content.append("💡 *这是实时2小时监控数据，每2小时自动推送*");

            // 发送钉钉通知
            sendDingTalkNotification(title, content.toString());

            log.info("[钉钉统计通知] 每2小时统计任务执行完成");
        } catch (Exception e) {
            log.error("[钉钉统计通知] 每2小时统计任务执行失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 每日 9:30 (UTC+8) 发送前一天的统计数据
     * cron表达式: 0 30 9 * * ? 表示每天 UTC+8 9:30 执行
     */
    @Scheduled(cron = "0 30 9 * * ?")
    public void sendDailyStatistics() {
        log.info("[钉钉统计通知] 开始执行每日统计任务");

        try {
            // 获取昨天的日期 (UTC+8时区)
            ZonedDateTime utcPlus8Yesterday = ZonedDateTime.now(ZoneId.of("UTC+8")).minusDays(1);

            // 检查是否有订单数据，总订单为0时不发送通知
            if (!hasDailyOrderData(utcPlus8Yesterday)) {
                log.info("[钉钉统计通知] 总订单数为0，跳过钉钉推送");
                return;
            }

            // 统计昨天的订单数据（包含金币和订阅）
            String dailyOrderStats = getDailyOrderStatistics(utcPlus8Yesterday);

            // 构建钉钉消息
            String title = String.format("📊 日报 | %s营收数据",
                utcPlus8Yesterday.format(DateTimeFormatter.ofPattern("MM月dd日")));

            StringBuilder content = new StringBuilder();
            content.append("# 📊 **每日营收统计报告**\n\n");
            content.append("📅 **统计日期**: ").append(utcPlus8Yesterday.format(DateTimeFormatter.ofPattern("yyyy年MM月dd日 (EEEE)"))).append(" (UTC+8)\n");
            content.append("🕘 **报告时间**: ").append(ZonedDateTime.now(ZoneId.of("UTC+8")).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).append("\n\n");
            content.append("---\n\n");
            content.append("## 💰 **核心业务数据**\n");
            content.append(dailyOrderStats).append("\n\n");
            content.append("---\n\n");
            content.append("## 📈 **数据说明**\n");
            content.append("- **总订单数**: 包含所有类型订单（已付款+未付款）\n");
            content.append("- **付款用户数**: 当日实际付费的独立用户数量\n");
            content.append("- **金币充值**: 用户购买金币的订单和金额\n");
            content.append("- **订阅充值**: 用户购买会员订阅的订单和金额\n\n");
            content.append("📌 *这是昨日完整业务数据汇总，每日09:30定时发送*");

            // 发送钉钉通知
            sendDingTalkNotification(title, content.toString());

            log.info("[钉钉统计通知] 每日统计任务执行完成");
        } catch (Exception e) {
            log.error("[钉钉统计通知] 每日统计任务执行失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 检查指定小时是否有订单数据
     */
    private boolean hasHourlyOrderData(ZonedDateTime dateTime) {
        try {
            // 获取2小时前的时间
            ZonedDateTime twoHoursAgoStart = dateTime.minusHours(2);
            // 获取整点时间
            ZonedDateTime hourStart = twoHoursAgoStart.withMinute(0).withSecond(0).withNano(0);
            // 结束时间是当前小时的整点
            ZonedDateTime hourEnd = dateTime.withMinute(0).withSecond(0).withNano(0);
            
            Date statHour = Date.from(hourStart.toInstant());
            Date endTime = Date.from(hourEnd.toInstant());
            StatisticsHourlyOrderStats stats = hourlyOrderStatsMapper.getRangeSummary(statHour, endTime, null, null, null);

            if (stats == null) {
                return false;
            }

            long totalOrders = stats.getTotalOrderCount() != null ? stats.getTotalOrderCount() : 0;
            return totalOrders > 0;
        } catch (Exception e) {
            log.error("[钉钉统计通知] 检查小时订单数据失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 检查指定日期是否有订单数据
     */
    private boolean hasDailyOrderData(ZonedDateTime dateTime) {
        try {
            Date statDate = Date.from(dateTime.toInstant());
            // 只检查指定日期一天的数据，所以开始和结束日期都是同一天
            StatisticsDailyOrderStats stats = dailyOrderStatsMapper.getRangeSummary(statDate, statDate, null, null, null, null);

            if (stats == null) {
                return false;
            }

            long totalOrders = stats.getTotalOrderCount() != null ? stats.getTotalOrderCount() : 0;
            return totalOrders > 0;
        } catch (Exception e) {
            log.error("[钉钉统计通知] 检查订单数据失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 获取指定小时的订单统计数据
     */
    private String getHourlyOrderStatistics(ZonedDateTime dateTime) {
        try {
            // 获取2小时前的时间作为开始时间
            ZonedDateTime twoHoursAgo = dateTime.minusHours(2);
            // 获取开始时间的整点
            ZonedDateTime hourStart = twoHoursAgo.withMinute(0).withSecond(0).withNano(0);
            // 获取结束时间的整点（当前小时）
            ZonedDateTime hourEnd = dateTime.withMinute(0).withSecond(0).withNano(0);
            
            Date statHour = Date.from(hourStart.toInstant());
            Date endTime = Date.from(hourEnd.toInstant());

            // 查询2小时时间段内的订单统计汇总数据（全系统）
            StatisticsHourlyOrderStats stats = hourlyOrderStatsMapper.getRangeSummary(statHour, endTime, null, null,null);

            if (stats == null) {
                return "| 指标 | 数量 | 金额 |\n|-----|-----|-----|\n| 📊 暂无数据 | - | - |";
            }

            // 直接使用汇总数据
            long totalOrders = stats.getTotalOrderCount() != null ? stats.getTotalOrderCount() : 0;
            long paidOrders = stats.getPaidOrderCount() != null ? stats.getPaidOrderCount() : 0;
            long unpaidOrders = totalOrders - paidOrders; // 计算未付款订单数
            long paidUsers = stats.getPaidUserCount() != null ? stats.getPaidUserCount() : 0;
            BigDecimal totalAmount = stats.getPaidOrderAmount() != null ? stats.getPaidOrderAmount() : BigDecimal.ZERO;
            long coinOrders = stats.getCoinOrderCount() != null ? stats.getCoinOrderCount() : 0;
            BigDecimal coinAmount = stats.getCoinOrderAmount() != null ? stats.getCoinOrderAmount() : BigDecimal.ZERO;
            long subscriptionOrders = stats.getSubscriptionOrderCount() != null ? stats.getSubscriptionOrderCount() : 0;
            BigDecimal subscriptionAmount = stats.getSubscriptionOrderAmount() != null ? stats.getSubscriptionOrderAmount() : BigDecimal.ZERO;

            // 计算关键业务指标
            double paymentRate = totalOrders > 0 ? (double) paidOrders / totalOrders * 100 : 0;
            double arpu = paidUsers > 0 ? totalAmount.doubleValue() / paidUsers : 0;
            double avgOrderAmount = paidOrders > 0 ? totalAmount.doubleValue() / paidOrders : 0;
            double coinOrderRate = paidOrders > 0 ? (double) coinOrders / paidOrders * 100 : 0;
            double subscriptionOrderRate = paidOrders > 0 ? (double) subscriptionOrders / paidOrders * 100 : 0;

            StringBuilder result = new StringBuilder();
            result.append("| 指标 | 数量 | 金额 |\n");
            result.append("|-----|-----|-----|\n");
            result.append("| 📊 总订单数 | ").append(totalOrders).append(" | - |\n");
            result.append("| ✅ 已付款订单 | ").append(paidOrders).append(" | $").append(totalAmount.toString()).append(" |\n");
            result.append("| ❌ 未付款订单 | ").append(unpaidOrders).append(" | - |\n");
            result.append("| 👥 付款用户数 | ").append(paidUsers).append(" | - |\n");
            result.append("| 🪙 金币充值 | ").append(coinOrders).append(" | $").append(coinAmount.toString()).append(" |\n");
            result.append("| 📅 订阅充值 | ").append(subscriptionOrders).append(" | $").append(subscriptionAmount.toString()).append(" |\n\n");

            result.append("**📈 核心指标**\n");
            result.append("- 💳 **付款转化率**: ").append(String.format("%.1f%%", paymentRate)).append("\n");
            result.append("- 💰 **ARPU**: $").append(String.format("%.2f", arpu)).append("/用户\n");
            result.append("- 🛒 **平均订单**: $").append(String.format("%.2f", avgOrderAmount)).append("\n");
            result.append("- 🪙 **金币占比**: ").append(String.format("%.1f%%", coinOrderRate)).append("\n");
            result.append("- 📅 **订阅占比**: ").append(String.format("%.1f%%", subscriptionOrderRate));

            return result.toString();

        } catch (Exception e) {
            log.error("[钉钉统计通知] 获取小时订单统计失败: {}", e.getMessage(), e);
            return "| 指标 | 数量 | 金额 |\n|-----|-----|-----|\n| ❌ 获取失败 | - | - |";
        }
    }

    /**
     * 获取指定日期的订单统计数据
     */
    private String getDailyOrderStatistics(ZonedDateTime dateTime) {
        try {
// 将ZonedDateTime转换为Date
            Date statDate = Date.from(dateTime.toInstant());

            // 查询该日期的订单统计汇总数据（全系统，默认UTC+8）
            // 只查询昨天一天的数据，所以开始和结束日期都是昨天
            StatisticsDailyOrderStats stats = dailyOrderStatsMapper.getRangeSummary(statDate, statDate, null, null, null, "UTC+8");
            if (stats == null) {
                return "| 指标 | 数量 | 金额 |\n|-----|-----|-----|\n| 📊 暂无数据 | - | - |";
            }

            // 直接使用汇总数据
            long totalOrders = stats.getTotalOrderCount() != null ? stats.getTotalOrderCount() : 0;
            long paidOrders = stats.getPaidOrderCount() != null ? stats.getPaidOrderCount() : 0;
            long unpaidOrders = totalOrders - paidOrders; // 计算未付款订单数
            long paidUsers = stats.getPaidUserCount() != null ? stats.getPaidUserCount() : 0;
            BigDecimal totalAmount = stats.getPaidOrderAmount() != null ? stats.getPaidOrderAmount() : BigDecimal.ZERO;
            long coinOrders = stats.getCoinOrderCount() != null ? stats.getCoinOrderCount() : 0;
            BigDecimal coinAmount = stats.getCoinOrderAmount() != null ? stats.getCoinOrderAmount() : BigDecimal.ZERO;
            long subscriptionOrders = stats.getSubscriptionOrderCount() != null ? stats.getSubscriptionOrderCount() : 0;
            BigDecimal subscriptionAmount = stats.getSubscriptionOrderAmount() != null ? stats.getSubscriptionOrderAmount() : BigDecimal.ZERO;

            StringBuilder result = new StringBuilder();
            result.append("| 指标 | 数量 | 金额 |\n");
            result.append("|-----|-----|-----|\n");
            result.append("| 📊 总订单数 | ").append(totalOrders).append(" | - |\n");
            result.append("| ✅ 已付款订单 | ").append(paidOrders).append(" | $").append(totalAmount.toString()).append(" |\n");
            result.append("| ❌ 未付款订单 | ").append(unpaidOrders).append(" | - |\n");
            result.append("| 👥 付款用户数 | ").append(paidUsers).append(" | - |\n");
            result.append("| 🪙 金币充值订单 | ").append(coinOrders).append(" | $").append(coinAmount.toString()).append(" |\n");
            result.append("| 📅 订阅充值订单 | ").append(subscriptionOrders).append(" | $").append(subscriptionAmount.toString()).append(" |\n\n");

            // 计算详细业务分析指标
            double paymentRate = totalOrders > 0 ? (double) paidOrders / totalOrders * 100 : 0;
            double arpu = paidUsers > 0 ? totalAmount.doubleValue() / paidUsers : 0;
            double avgOrderAmount = paidOrders > 0 ? totalAmount.doubleValue() / paidOrders : 0;
            double coinOrderRate = paidOrders > 0 ? (double) coinOrders / paidOrders * 100 : 0;
            double subscriptionOrderRate = paidOrders > 0 ? (double) subscriptionOrders / paidOrders * 100 : 0;
            double avgCoinAmount = coinOrders > 0 ? coinAmount.doubleValue() / coinOrders : 0;
            double avgSubscriptionAmount = subscriptionOrders > 0 ? subscriptionAmount.doubleValue() / subscriptionOrders : 0;
            double lostPotentialRevenue = unpaidOrders * avgOrderAmount; // 流失潜在收入估算

            result.append("## 💹 **业务分析指标**\n\n");
            result.append("### 📊 转化表现\n");
            result.append("- 💳 **付款转化率**: ").append(String.format("%.1f%%", paymentRate)).append("\n");
            result.append("- 💰 **ARPU**: $").append(String.format("%.2f", arpu)).append(" (平均每用户收入)\n");
            result.append("- 🛒 **平均订单金额**: $").append(String.format("%.2f", avgOrderAmount)).append("\n\n");

            result.append("### 🎯 产品结构\n");
            result.append("- 🪙 **金币业务占比**: ").append(String.format("%.1f%%", coinOrderRate)).append(" | 平均$").append(String.format("%.2f", avgCoinAmount)).append("/单\n");
            result.append("- 📅 **订阅业务占比**: ").append(String.format("%.1f%%", subscriptionOrderRate)).append(" | 平均$").append(String.format("%.2f", avgSubscriptionAmount)).append("/单\n\n");

            result.append("### ⚠️ 风险指标\n");
            result.append("- 🚨 **未付款订单**: ").append(unpaidOrders).append("个 (").append(String.format("%.1f%%", totalOrders > 0 ? (double) unpaidOrders / totalOrders * 100 : 0)).append(")\n");
            result.append("- 💸 **潜在流失收入**: ~$").append(String.format("%.2f", lostPotentialRevenue)).append(" (估算)");

            return result.toString();

        } catch (Exception e) {
            log.error("[钉钉统计通知] 获取日订单统计失败: {}", e.getMessage(), e);
            return "| 指标 | 数量 | 金额 |\n|-----|-----|-----|\n| ❌ 获取失败 | - | - |";
        }
    }



    /**
     * 发送钉钉通知
     */
    private void sendDingTalkNotification(String title, String content) {
        String webhookUrl = dingTalkProperties.getWebhooks() != null ? dingTalkProperties.getWebhooks().getPayment() : null;

        SysDingtalkLog dingtalkLog = new SysDingtalkLog();
        dingtalkLog.setBusinessId("STATISTICS_" + System.currentTimeMillis());
        dingtalkLog.setBusinessType("STATISTICS");
        dingtalkLog.setEventType("SCHEDULED_STATISTICS");
        dingtalkLog.setWebhookUrl(webhookUrl);
        dingtalkLog.setMessageContent(content);
        dingtalkLog.setCreateTime(new Date());

        try {
            if (webhookUrl == null || webhookUrl.trim().isEmpty()) {
                log.warn("[钉钉统计通知] 钉钉Webhook URL未配置，跳过发送通知");
                dingtalkLog.setStatus("1"); // 失败
                dingtalkLog.setResponseCode("CONFIG_ERROR");
                dingtalkLog.setResponseMessage("钉钉Webhook URL未配置");
                saveDingTalkLog(dingtalkLog);
                return;
            }

            // 调用钉钉服务发送消息
            JSONObject result = dingTalkService.sendMarkdownMessage(webhookUrl, title, content);

            // 检查钉钉API响应
            Integer errcode = result != null ? result.getInteger("errcode") : null;
            if (result != null && errcode != null && errcode == 0) {
                dingtalkLog.setStatus("0"); // 成功
                dingtalkLog.setResponseCode("0");
                dingtalkLog.setResponseMessage("发送成功");
                log.info("[钉钉统计通知] 通知发送成功: {}", title);
            } else {
                dingtalkLog.setStatus("1"); // 失败
                dingtalkLog.setResponseCode(errcode != null ? errcode.toString() : "UNKNOWN");
                dingtalkLog.setResponseMessage(result != null ? result.getString("errmsg") : "未知错误");
                log.error("[钉钉统计通知] 钉钉接口返回错误: {}", result);
            }

            saveDingTalkLog(dingtalkLog);
        } catch (Exception e) {
            dingtalkLog.setStatus("1"); // 失败
            dingtalkLog.setResponseCode("EXCEPTION");
            dingtalkLog.setResponseMessage("发送异常: " + e.getMessage());
            saveDingTalkLog(dingtalkLog);
            log.error("[钉钉统计通知] 发送通知失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 保存钉钉日志到数据库
     */
    private void saveDingTalkLog(SysDingtalkLog dingtalkLog) {
        try {
            sysDingtalkLogMapper.insert(dingtalkLog);
        } catch (Exception e) {
            log.error("[钉钉统计通知] 保存钉钉日志失败: {}", e.getMessage(), e);
        }
    }
}
