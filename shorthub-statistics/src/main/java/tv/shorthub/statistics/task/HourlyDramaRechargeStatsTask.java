package tv.shorthub.statistics.task;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import tv.shorthub.statistics.service.IHourlyDramaRechargeStatsService;

/**
 * 每小时剧目充值统计定时任务
 *
 * <AUTHOR>
 * @date 2025-07-15
 */
@Slf4j
@Component
public class HourlyDramaRechargeStatsTask {

    @Autowired
    private IHourlyDramaRechargeStatsService hourlyDramaRechargeStatsService;

    /**
     * 每10分钟执行一次，更新当前小时的剧目充值统计数据
     * cron表达式: 0/30 * * * * ? 表示每30秒执行一次（开发测试用）
     * 生产环境建议: 0 0/10 * * * ? （每10分钟执行一次）
     */
    @Scheduled(cron = "0 0/10 * * * ?")
    public void updateCurrentHourDramaRechargeStats() {
        log.info("[剧目充值统计定时任务] 开始执行每小时剧目充值统计任务");

        try {
            // 统计当前小时的剧目充值数据
            hourlyDramaRechargeStatsService.calculateAndSaveCurrentHourStats();
            log.info("[剧目充值统计定时任务] 当前小时剧目充值统计任务执行成功");
        } catch (Exception e) {
            log.error("[剧目充值统计定时任务] 当前小时剧目充值统计任务执行失败", e);
        }
    }

    /**
     * 每天凌晨2点执行，重新统计前一天的剧目充值数据
     * cron表达式: 0 0 2 * * ? 表示每天凌晨2点执行
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void recalculateYesterdayDramaRechargeStats() {
        log.info("[剧目充值统计定时任务] 开始执行昨天剧目充值数据重新统计任务");

        try {
            // 重新统计昨天的剧目充值数据
            hourlyDramaRechargeStatsService.recalculateYesterdayStats();
            log.info("[剧目充值统计定时任务] 昨天剧目充值数据重新统计任务执行成功");
        } catch (Exception e) {
            log.error("[剧目充值统计定时任务] 昨天剧目充值数据重新统计任务执行失败", e);
        }
    }

    /**
     * 每周日凌晨3:50执行，全量统计所有时间范围的剧目充值数据
     * cron表达式: 0 50 3 ? * SUN 表示每周日凌晨3:50执行
     */
    @Scheduled(cron = "0 50 3 ? * SUN")
    public void batchUpdateAllDramaRechargeStats() {
        log.info("[剧目充值统计定时任务] 开始执行全量统计所有时间范围的剧目充值数据任务");

        try {
            // 全量统计所有时间范围的剧目充值数据
            String result = hourlyDramaRechargeStatsService.batchUpdateAllStats();
            log.info("[剧目充值统计定时任务] 全量统计任务执行成功: {}", result);
        } catch (Exception e) {
            log.error("[剧目充值统计定时任务] 全量统计任务执行失败", e);
        }
    }
}
