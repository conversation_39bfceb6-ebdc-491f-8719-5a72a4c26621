package tv.shorthub.statistics.task;

  import lombok.extern.slf4j.Slf4j;
  import org.springframework.beans.factory.annotation.Autowired;
  import org.springframework.scheduling.annotation.Scheduled;
  import org.springframework.stereotype.Component;
  import tv.shorthub.statistics.service.IDailyDramaEpisodeStatsService;

  /**
   * 每日剧集分集统计定时任务
   *
   * <AUTHOR>
   * @date 2025-07-22
   */
  @Slf4j
  @Component
  public class DailyDramaEpisodeStatsTask {

      @Autowired
      private IDailyDramaEpisodeStatsService dailyDramaEpisodeStatsService;

      /**
       * 每3分钟执行一次，更新当天的剧集分集统计数据
       * cron表达式: 0 2/3 * * * ? 表示每3分钟执行一次，从第2分钟开始
       */
      @Scheduled(cron = "0 2/3 * * * ?")
      public void updateCurrentDayEpisodeStats() {
          log.info("[剧集分集日统计定时任务] 开始执行每日剧集分集统计任务");
          try {
              dailyDramaEpisodeStatsService.updateCurrentDayEpisodeStats();
              log.info("[剧集分集日统计定时任务] 每日剧集分集统计任务执行完成");
          } catch (Exception e) {
              log.error("[剧集分集日统计定时任务] 执行失败: {}", e.getMessage(), e);
          }
      }

      /**
       * 每日凌晨2:55执行，全量统计昨天一整天的数据，作为数据兜底
       * cron表达式: 0 55 2 * * ?
       */
      @Scheduled(cron = "0 55 2 * * ?")
      public void batchUpdateYesterdayEpisodeStats() {
          log.info("[剧集分集日统计每日兜底任务] 开始执行");
          try {
              dailyDramaEpisodeStatsService.batchUpdateRecentEpisodeStats(1);
              log.info("[剧集分集日统计每日兜底任务] 成功完成");
          } catch (Exception e) {
              log.error("[剧集分集日统计每日兜底任务] 执行失败: {}", e.getMessage(), e);
          }
      }

      /**
       * 每周日凌晨3:55执行，全量重算所有历史数据
       * cron表达式: 0 55 3 ? * SUN
       */
      @Scheduled(cron = "0 55 3 ? * SUN")
//      @Scheduled(cron = "0/30 * * * * ?")
      public void batchUpdateAllEpisodeStats() {
          log.info("[剧集分集日统计每周全量任务] 开始执行");
          try {
              String result = dailyDramaEpisodeStatsService.batchUpdateAllEpisodeStats();
              log.info("[剧集分集日统计每周全量任务] 执行完成: {}", result);
          } catch (Exception e) {
              log.error("[剧集分集日统计每周全量任务] 执行失败: {}", e.getMessage(), e);
          }
      }
  }
