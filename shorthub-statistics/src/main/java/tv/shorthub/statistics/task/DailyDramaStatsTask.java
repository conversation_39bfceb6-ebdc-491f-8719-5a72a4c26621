package tv.shorthub.statistics.task;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import tv.shorthub.statistics.service.IDailyDramaStatsService;

/**
 * 每日剧集统计定时任务
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Slf4j
@Component
public class DailyDramaStatsTask {

    @Autowired
    private IDailyDramaStatsService dailyDramaStatsService;

    /**
     * 每2分钟执行一次，更新当天的剧集统计数据
     * cron表达式: 0 1/2 * * * ? 表示每2分钟执行一次，从第1分钟开始
     */
    @Scheduled(cron = "0 1/2 * * * ?")
    public void updateCurrentDayDramaStats() {
        log.info("[剧集日统计定时任务] 开始执行每日剧集统计任务");
        try {
            dailyDramaStatsService.updateCurrentDayDramaStats();
            log.info("[剧集日统计定时任务] 每日剧集统计任务执行完成");
        } catch (Exception e) {
            log.error("[剧集日统计定时任务] 执行失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 每日凌晨2:20执行，全量统计昨天一整天的数据，作为数据兜底
     * cron表达式: 0 20 2 * * ?
     */
    @Scheduled(cron = "0 20 2 * * ?")
    public void batchUpdateYesterdayStats() {
        log.info("[剧集日统计每日兜底任务] 开始执行");
        try {
            dailyDramaStatsService.batchUpdateRecentDramaStats(1);
            log.info("[剧集日统计每日兜底任务] 成功完成");
        } catch (Exception e) {
            log.error("[剧集日统计每日兜底任务] 执行失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 每周日凌晨3:20执行，全量重算所有历史数据
     * cron表达式: 0 20 3 ? * SUN
     */
    @Scheduled(cron = "0 20 3 ? * SUN")
    public void batchUpdateAllStats() {
        log.info("[剧集日统计每周全量任务] 开始执行");
        try {
            String result = dailyDramaStatsService.batchUpdateAllStats();
            log.info("[剧集日统计每周全量任务] 执行完成: {}", result);
        } catch (Exception e) {
            log.error("[剧集日统计每周全量任务] 执行失败: {}", e.getMessage(), e);
        }
    }
}
