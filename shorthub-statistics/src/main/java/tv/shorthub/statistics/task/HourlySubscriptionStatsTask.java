package tv.shorthub.statistics.task;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import tv.shorthub.statistics.service.IHourlySubscriptionStatsService;

/**
 * 每小时订阅统计定时任务
 *
 * <AUTHOR>
 * @date 2025-01-02
 */
@Slf4j
@Component
public class HourlySubscriptionStatsTask {

    @Autowired
    private IHourlySubscriptionStatsService hourlySubscriptionStatsService;

    /**
     * 每10分钟执行一次，更新当前小时的订阅统计数据
     * cron表达式: 0 3/10 * * * ? 表示每10分钟执行一次，从第3分钟开始
     */
    @Scheduled(cron = "0 3/10 * * * ?")
    public void updateCurrentHourSubscriptionStats() {
        log.info("[订阅统计定时任务] 开始执行每小时订阅统计任务");

        try {
            hourlySubscriptionStatsService.updateCurrentHourSubscriptionStats();
            log.info("[订阅统计定时任务] 每小时订阅统计任务执行完成");
        } catch (Exception e) {
            log.error("[订阅统计定时任务] 执行失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 每小时的第6分钟执行，用于统计上一小时的数据（确保数据完整性）
     * cron表达式: 0 6 * * * ? 表示每小时的第6分钟执行
     */
    @Scheduled(cron = "0 6 * * * ?")
    public void updatePreviousHourSubscriptionStats() {
        log.info("[订阅统计定时任务] 开始执行上一小时订阅统计任务");

        try {
            // 获取上一小时的时间
            java.util.Calendar calendar = java.util.Calendar.getInstance();
            calendar.add(java.util.Calendar.HOUR_OF_DAY, -1);
            calendar.set(java.util.Calendar.MINUTE, 0);
            calendar.set(java.util.Calendar.SECOND, 0);
            calendar.set(java.util.Calendar.MILLISECOND, 0);

            hourlySubscriptionStatsService.updateHourlySubscriptionStats(calendar.getTime());
            log.info("[订阅统计定时任务] 上一小时订阅统计任务执行完成");
        } catch (Exception e) {
            log.error("[订阅统计定时任务] 上一小时订阅统计执行失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 每日凌晨2:30执行，全量统计昨天一整天的数据，作为数据兜底
     * cron表达式: 0 30 2 * * ?
     */
    @Scheduled(cron = "0 30 2 * * ?")
    public void batchUpdateYesterdayStats() {
        log.info("[订阅统计每日兜底任务] 开始执行");
        try {
            hourlySubscriptionStatsService.batchUpdateRecentSubscriptionStats(1);
            log.info("[订阅统计每日兜底任务] 成功完成");
        } catch (Exception e) {
            log.error("[订阅统计每日兜底任务] 执行失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 每周日凌晨3:30执行，全量重算所有历史数据
     * cron表达式: 0 30 3 ? * SUN
     */


    @Scheduled(cron = "0 30 3 ? * SUN")
    public void batchUpdateAllStats() {
        log.info("[订阅统计每周全量任务] 开始执行");
        try {
            String result = hourlySubscriptionStatsService.batchUpdateAllStats();
            log.info("[订阅统计每周全量任务] 执行完成: {}", result);
        } catch (Exception e) {
            log.error("[订阅统计每周全量任务] 执行失败: {}", e.getMessage(), e);
        }
    }
}
