package tv.shorthub.statistics.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 统计配置类
 *
 * <AUTHOR>
 * @date 2025-01-02
 */
@Component
@ConfigurationProperties(prefix = "statistics")
public class StatisticsConfig {

    /**
     * 支持的时区列表（UTC格式，用于数据库存储）
     * 24个标准时区，从 UTC-12 到 UTC+12
     */
    private List<String> supportedTimezones = List.of(
        "UTC-12",  // 国际日期变更线西
        "UTC-11",  // 萨摩亚时间
        "UTC-10",  // 夏威夷时间
        "UTC-9",   // 阿拉斯加时间
        "UTC-8",   // 美西时间
        "UTC-7",   // 美山时间
        "UTC-6",   // 美中时间
        "UTC-5",   // 美东时间
        "UTC-4",   // 大西洋时间
        "UTC-3",   // 巴西/阿根廷时间
        "UTC-2",   // 大西洋中部时间
        "UTC-1",   // 亚速尔时间
        "UTC+0",   // 英国/UTC时间
        "UTC+1",   // 中欧时间
        "UTC+2",   // 东欧时间
        "UTC+3",   // 莫斯科时间
        "UTC+4",   // 阿联酋时间
        "UTC+5",   // 巴基斯坦时间
        "UTC+6",   // 孟加拉国时间
        "UTC+7",   // 东南亚时间
        "UTC+8",   // 中国标准时间
        "UTC+9",   // 日本/韩国时间
        "UTC+10",  // 澳洲东部时间
        "UTC+11",  // 所罗门群岛时间
        "UTC+12"   // 新西兰时间
    );

    public List<String> getSupportedTimezones() {
        return supportedTimezones;
    }

    public void setSupportedTimezones(List<String> supportedTimezones) {
        this.supportedTimezones = supportedTimezones;
    }
} 