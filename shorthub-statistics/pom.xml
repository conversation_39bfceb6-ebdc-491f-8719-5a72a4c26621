<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <parent>
        <artifactId>shorthub</artifactId>
        <groupId>tv.shorthub</groupId>
        <version>1.0.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>shorthub-statistics</artifactId>
    <packaging>jar</packaging>
    <description>
        数据统计模块
    </description>
    <dependencies>
    <!-- 通用工具-->
    <dependency>
        <groupId>tv.shorthub</groupId>
        <artifactId>shorthub-common</artifactId>
    </dependency>

    <!-- 系统模块-->
            <dependency>
                <groupId>tv.shorthub</groupId>
                <artifactId>shorthub-system</artifactId>
            </dependency>

    <!-- SpringBoot Web容器 -->
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-web</artifactId>
    </dependency>

    <!-- SpringBoot 拦截器 -->
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-aop</artifactId>
    </dependency>

    <!-- SpringBoot 测试 -->
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-test</artifactId>
        <scope>test</scope>
    </dependency>
        <dependency>
            <groupId>tv.shorthub</groupId>
            <artifactId>shorthub-dingtalk</artifactId>
        </dependency>
    </dependencies>
</project>
