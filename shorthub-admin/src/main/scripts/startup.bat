@echo off
setlocal enabledelayedexpansion

:: 应用名称
set APP_NAME=shorthub-admin
:: 应用路径
set APP_PATH=%~dp0
:: 应用端口
set APP_PORT=8080
:: 应用参数
set APP_PARAM=-Dspring.profiles.active=prod -Dserver.port=%APP_PORT%
:: 日志文件
set APP_LOG_FILE=..\logs\sys-info.log
:: JAR包路径
set JAR_PATH=.\%APP_NAME%.jar
:: JVM参数
set JVM_PARAM=-Xms512m -Xmx1024m -Xmn256m -XX:MetaspaceSize=128m -XX:MaxMetaspaceSize=256m -XX:MaxDirectMemorySize=512m -XX:ReservedCodeCacheSize=128m
:: PID文件
set PID_FILE=..\pid\%APP_NAME%.pid

:: 确保日志和PID目录存在
if not exist "..\logs" mkdir "..\logs"
if not exist "..\pid" mkdir "..\pid"

:: 检查进程是否在运行
:check_running
if exist "%PID_FILE%" (
    for /f "tokens=1" %%a in ('type "%PID_FILE%"') do set PID=%%a
    tasklist /FI "PID eq !PID!" 2>NUL | find /I /N "!PID!" >NUL
    if "%ERRORLEVEL%"=="0" exit /b 0
)
exit /b 1

:: 启动应用
:start
echo Starting %APP_NAME%...
echo JVM parameters: %JVM_PARAM%
echo Application parameters: %APP_PARAM%
cd /d "%~dp0"
:: 设置类路径
set CLASSPATH=..\lib\*;.\%APP_NAME%.jar
start /b javaw %JVM_PARAM% %APP_PARAM% -cp "%CLASSPATH%" tv.shorthub.ShorthubApplication > %APP_LOG_FILE% 2>&1
for /f "tokens=2" %%a in ('tasklist /FI "IMAGENAME eq javaw.exe" /NH') do set PID=%%a
echo !PID!> %PID_FILE%
echo %APP_NAME% started with PID !PID!
exit /b 0

:: 停止应用
:stop
echo Stopping %APP_NAME%...
call :check_running
if %ERRORLEVEL%==1 (
    echo %APP_NAME% is not running
    exit /b 0
)

taskkill /F /PID !PID!
del /f /q "%PID_FILE%"
echo %APP_NAME% stopped
exit /b 0

:: 重启应用
:restart
call :stop
timeout /t 2 /nobreak > nul
call :start
exit /b 0

:: 查看状态
:status
call :check_running
if %ERRORLEVEL%==0 (
    echo %APP_NAME% is running with PID !PID!
) else (
    echo %APP_NAME% is not running
)
exit /b 0

:: 主程序
if "%1"=="" goto usage
if "%1"=="start" goto start
if "%1"=="stop" goto stop
if "%1"=="restart" goto restart
if "%1"=="status" goto status

:usage
echo Usage: %0 {start^|stop^|restart^|status}
exit /b 1 