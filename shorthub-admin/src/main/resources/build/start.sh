#!/bin/bash

APP_NAME="shorthub-admin"
APP_PATH="/app/build"
APP_PORT=8080
APP_PARAM="-Dspring.profiles.active=prod -Dserver.port=$APP_PORT"
APP_LOG_FILE="/app/logs/sys-info.log"
JAR_PATH="$APP_PATH/*.jar"
JVM_PARAM="-Xms512m -Xmx1024m -Xmn256m -XX:MetaspaceSize=128m -XX:MaxMetaspaceSize=256m -XX:MaxDirectMemorySize=512m -XX:ReservedCodeCacheSize=128m"
PID_FILE="/app/pid/$APP_NAME.pid"
STARTUP_KEYWORD="Application started successfully"
#JAR_LOG_FILE="${APP_PATH}/${APP_NAME}.log"

start() {
    echo "Starting $APP_NAME..."
    nohup java $JVM_PARAM $APP_PARAM -jar $JAR_PATH > $APP_LOG_FILE 2>&1 &
    echo $! > $PID_FILE
    echo "$APP_NAME started with PID $(cat $PID_FILE)"

    # Wait for the application to start
    tail -f $APP_LOG_FILE | while read LINE; do
        echo "$LINE" | grep "$STARTUP_KEYWORD" &> /dev/null
        if [ $? = 0 ]; then
            echo "$APP_NAME started successfully."
            pkill -P $$ tail
            exit 0
        fi
    done

}


case "$1" in
    start)
        start
        ;;
    *)
        echo "Usage: $0 {start}"
        exit 1
esac
