# 项目相关配置
shorthub:
    # 文件路径 示例（ Windows配置D:/ruoyi/uploadPath，Linux配置 /home/<USER>/uploadPath）
    profile: D:/home/<USER>
    # API节点域名
    domainApi: https://ssmapi.shorthub.tv
    # web客户端域名
    domainWeb: https://devfront.shorthub.tv
# 数据源配置
spring:
    datasource:
        type: com.alibaba.druid.pool.DruidDataSource
        driverClassName: com.mysql.cj.jdbc.Driver
        druid:
            # 主库数据源
            master:
                url: *******************************************************************************************************************************************************************************
                username: root
                password: 123456
            # 从库数据源
            slave:
                # 从数据源开关/默认关闭
                enabled: false
                url:
                username:
                password:
            # 初始连接数
            initialSize: 5
            # 最小连接池数量
            minIdle: 10
            # 最大连接池数量
            maxActive: 20
            # 配置获取连接等待超时的时间
            maxWait: 60000
            # 配置连接超时时间
            connectTimeout: 30000
            # 配置网络超时时间
            socketTimeout: 60000
            # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
            timeBetweenEvictionRunsMillis: 60000
            # 配置一个连接在池中最小生存的时间，单位是毫秒
            minEvictableIdleTimeMillis: 300000
            # 配置一个连接在池中最大生存的时间，单位是毫秒
            maxEvictableIdleTimeMillis: 900000
            # 配置检测连接是否有效
            validationQuery: SELECT 1 FROM DUAL
            testWhileIdle: true
            testOnBorrow: false
            testOnReturn: false
            webStatFilter:
                enabled: true
            statViewServlet:
                enabled: true
                # 设置白名单，不填则允许所有访问
                allow:
                url-pattern: /druid/*
                # 控制台管理用户名和密码
                login-username: admin
                login-password: 123456
            filter:
                stat:
                    enabled: true
                    # 慢SQL记录
                    log-slow-sql: true
                    slow-sql-millis: 1000
                    merge-sql: true
                wall:
                    config:
                        multi-statement-allow: true
    # redis 配置
    data:
        redis:
            host: **************
            # 端口，默认为6379
            port: 6379
            # 数据库索引
            database: 0
            # 密码
            password:
            # 连接超时时间
            timeout: 60s
            lettuce:
                pool:
                    # 连接池中的最小空闲连接
                    min-idle: 0
                    # 连接池中的最大空闲连接
                    max-idle: 8
                    # 连接池的最大数据库连接数
                    max-active: 8
                    # #连接池最大阻塞等待时间（使用负值表示没有限制）
                    max-wait: -1ms
xxl:
    job:
        executor:
            logretentiondays: 30
            logpath: /app/docker/xxl/applogs/xxl-job/jobhandler
            port: 9999
            ip:
            appname: shorthub
        accessToken: default_token

        admin:
            addresses: http://*************:8220/xxl-job-admin

ffmpeg:
    path: D:\soft\ffmpeg-n7.1-latest-win64-gpl-7.1\bin\ffmpeg.exe

cloudflare:
    queue:
        account-id: 191594a892dd95613953bcc85c92cfb7
        api-token: vFuH9bd7WSlailwBVsgn8W4nauPOhxzUx6Iz-emS
    r2:
        account-id: 191594a892dd95613953bcc85c92cfb7
        # 5965515384b62115b0836fade254ebbd1d9afe62ef57c3b9c6bc08e3f2b83a52
        access:
            key:
                id: 321a4432b372f1459364921076baa31b
                secret: 2qXAQM2SA03D76dikrZhGP3lunViixDPoxylqBl7
        url:
            endpoint: https://${cloudflare.r2.account-id}.r2.cloudflarestorage.com/
            bucket-of-sign: https://%s.${cloudflare.r2.account-id}.r2.cloudflarestorage.com/
        bucket:
            private:
                name: dev-shorthub
                domain: https://tor2.*********.workers.dev/
            public:
                name: dev-shorthub-public
                domain: https://pub-f93df6143fe34e409e374ac38bb8684e.r2.dev/

payermax:
    merchantNo: SDP01010115688123
    appId: f8c68bc435cb4022a4faf5270d89b4b8
    privateKey: MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQDP+Wdi7bPp71nt7xfnmZVApAUU60uwFGypdDHF0NFbSCXEQZ3HSm7P1uNPYhcIZ+rwH+ty8wrtH/NvZSMoBKmpG+fOU4IXvLGDBDS50uhjQjYjiaARNICXxrHqOQHOE7S2AlfBZ0XtKrzivGW1rcZRED/Rlg2zUeUARhKLcJX/TU4WM5feUOupDokGx9s2dBU/GDx8DPuTPl2TO3jST2CjmQXw9RhpL1ulRX+E4bGhdFxszdOBwzzdTnhHAIGNOFW2EblOaNDLE/TcxIv6mwPQIIjypD5V/MnU4QzFg+lNgR9YdSJy1lpyFkhiu5Zu79RAELCIxEo+jkYN7Jy5QmgpAgMBAAECggEABnbmYYhpc/n3teIIFcUWmFEw2OmyaylLXQi1UQSGCWa988fQBMU5xLXq0Alzaqm46gNX+8qgWjwIwqY7uESi1b8+DpIBGcXzT5L7z5MwcYC58uireXVuvNOqgpEIjxRaOjZq7gz3ewvTHgoZ8D0pTFTrYuidiZIgAojrDntZUjuRb937En1NZrb0+eri0WQUb4+WqqJj08TXJaBFGiuVsuEzedc/JpTwix5Tm4b9LIiOS0oU7ZFSquIdYRAqzrZ6cVyQYHBfofgtbiJ0x5UiVjb+J8kn4cMxXOzbd+nq4NZxaXMGA2t4a7wrSdOZ7HFpC5gu1g0EYivLzRqSV2n2oQKBgQDU8cA3Feoap/+LAUZ0Vc1Xj+49DHfoJ8lMpVvwZHZ/tOM8fx0o2aMWwgMhX0By92mDgTZSD9HirBl/NFGgPn3GLeskOOmGwqKAkhEO/LuvpOGF/LIvOmmbVbZoTxwXjPALHPA5qQ85ZDkbJxGjyFDY9u2UDwj5LGkWKjO9rd8bGQKBgQD6BmT/GWQDV99UNHXiSt2eHOmlrfhm2uUZ1cP2Ktd12xDaVOFJ7a8rK6P2wo73R+rtjLH79FXr5sZfFYab08GcK9sWcJY/EStv9XgmxE4zYKqyDqHJiey0pRy02W1Cf5Ygd6kLSqzfqLhHd5nvdi+I6j+oURMzGI2Eelp9VoVnkQKBgQCGVtFdryyWjn/PsQlCoVK/N+UjjHck9dyvxu5elPKRFIv7AXJi8BJMbC9PYWkmXmGpfd3IHYTazJn1Pwtz3Zi5awSaQHFK/AVKuVubqeO6JnnEbqv20ZIIViWSXr4pOVfCmwoORlJ9cXX5ZEdjnR8sEXPGYUnW8KaHPi6QLl2/GQKBgAHggmmX2zRbOIX9TtVAEqHZPbfMsoyzH/0tqabzc2KjHmFFOdvAOQxt63EyzLf2hkD6SWvGzc5TkTGti7EnydBOU/q7JaWbj2Prx9ciqiOgmid0DKriqjBA/RIuf698HbhorZA0I1AaiCehMjql/H1epNOyqB4+FPHlqUA/ogjBAoGBAKTcXiah6QLYgX0f8zzd8MMVoGQ5V2Ir+PQZ+VSex6iB+IqiBd6KVfUTAwTAP8yU7pMtEOq1IzUFw2+G7wTehiDNBYW0Fo8ijSTfp+0rYHhOmhB0nkVnOio0R3cDwAoGlHXJ00FIkQ47DOHeqUkpbWhcJiVLxaSoJmSCF6iKlPss
    publicKey: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAz/lnYu2z6e9Z7e8X55mVQKQFFOtLsBRsqXQxxdDRW0glxEGdx0puz9bjT2IXCGfq8B/rcvMK7R/zb2UjKASpqRvnzlOCF7yxgwQ0udLoY0I2I4mgETSAl8ax6jkBzhO0tgJXwWdF7Sq84rxlta3GURA/0ZYNs1HlAEYSi3CV/01OFjOX3lDrqQ6JBsfbNnQVPxg8fAz7kz5dkzt40k9go5kF8PUYaS9bpUV/hOGxoXRcbM3TgcM83U54RwCBjThVthG5TmjQyxP03MSL+psD0CCI8qQ+VfzJ1OEMxYPpTYEfWHUictZachZIYruWbu/UQBCwiMRKPo5GDeycuUJoKQIDAQAB
    gatewayUrl: https://pay-gate-uat.payermax.com
    notifyUrl: ${shorthub.domainApi}/payermax/payment/notify
    returnUrl: ${shorthub.domainApi}/payment/result