# 项目相关配置
shorthub:
    # 文件路径 示例（ Windows配置D:/ruoyi/uploadPath，Linux配置 /home/<USER>/uploadPath）
    profile: D:/home/<USER>
    # API节点域名
    domainApi: https://ssmapi.shorthub.tv
    # web客户端域名
    domainWeb: https://devfront.shorthub.tv
# 数据源配置
spring:
    datasource:
        type: com.alibaba.druid.pool.DruidDataSource
        driverClassName: com.mysql.cj.jdbc.Driver
        druid:
            # 主库数据源
            master:
                url: **************************************************************************************************************************************************************************
                username: root
                password: wz940179mn42086iC
            # 从库数据源
            slave:
                # 从数据源开关/默认关闭
                enabled: false
                url:
                username:
                password:
            # 初始连接数
            initialSize: 5
            # 最小连接池数量
            minIdle: 10
            # 最大连接池数量
            maxActive: 20
            # 配置获取连接等待超时的时间
            maxWait: 60000
            # 配置连接超时时间
            connectTimeout: 30000
            # 配置网络超时时间
            socketTimeout: 60000
            # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
            timeBetweenEvictionRunsMillis: 60000
            # 配置一个连接在池中最小生存的时间，单位是毫秒
            minEvictableIdleTimeMillis: 300000
            # 配置一个连接在池中最大生存的时间，单位是毫秒
            maxEvictableIdleTimeMillis: 900000
            # 配置检测连接是否有效
            validationQuery: SELECT 1 FROM DUAL
            testWhileIdle: true
            testOnBorrow: false
            testOnReturn: false
            webStatFilter:
                enabled: true
            statViewServlet:
                enabled: true
                # 设置白名单，不填则允许所有访问
                allow:
                url-pattern: /druid/*
                # 控制台管理用户名和密码
                login-username: admin
                login-password: 123456
            filter:
                stat:
                    enabled: true
                    # 慢SQL记录
                    log-slow-sql: true
                    slow-sql-millis: 1000
                    merge-sql: true
                wall:
                    config:
                        multi-statement-allow: true
    # redis 配置
    data:
        redis:
            host: localhost
            # 端口，默认为6379
            port: 6379
            # 数据库索引
            database: 0
            # 密码
            password: Short20251
            # 连接超时时间
            timeout: 10s
            lettuce:
                pool:
                    # 连接池中的最小空闲连接
                    min-idle: 0
                    # 连接池中的最大空闲连接
                    max-idle: 8
                    # 连接池的最大数据库连接数
                    max-active: 8
                    # #连接池最大阻塞等待时间（使用负值表示没有限制）
                    max-wait: -1ms
xxl:
    job:
        executor:
            logretentiondays: 30
            logpath: /app/docker/xxl/applogs/xxl-job/jobhandler
            port: 9999
            ip:
            appname: shorthub
        accessToken: default_token

        admin:
            addresses: http://*************:8220/xxl-job-admin

cloudflare:
    queue:
        account-id: 191594a892dd95613953bcc85c92cfb7
        api-token: vFuH9bd7WSlailwBVsgn8W4nauPOhxzUx6Iz-emS
    r2:
        account-id: 191594a892dd95613953bcc85c92cfb7
        # 5965515384b62115b0836fade254ebbd1d9afe62ef57c3b9c6bc08e3f2b83a52
        access:
            key:
                id: 321a4432b372f1459364921076baa31b
                secret: 2qXAQM2SA03D76dikrZhGP3lunViixDPoxylqBl7
        url:
            endpoint: https://${cloudflare.r2.account-id}.r2.cloudflarestorage.com/
            bucket-of-sign: https://%s.${cloudflare.r2.account-id}.r2.cloudflarestorage.com/
        bucket:
            private:
                name: dev-shorthub
                domain: https://tor2.*********.workers.dev/
            public:
                name: dev-shorthub-public
                domain: https://pub-f93df6143fe34e409e374ac38bb8684e.r2.dev/

# 钉钉机器人配置
dingtalk:
  webhooks:
    payment: https://connector.dingtalk.com/webhook/flow/10325664439a213f4982000k
