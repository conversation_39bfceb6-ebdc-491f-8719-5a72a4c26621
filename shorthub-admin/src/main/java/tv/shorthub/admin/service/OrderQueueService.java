package tv.shorthub.admin.service;


import com.alibaba.fastjson2.JSONArray;
import lombok.extern.slf4j.Slf4j;
import tv.shorthub.common.queue.CloudflareQueueService;

@Slf4j
public class OrderQueueService {
//    public static void main(String[] args) throws InterruptedException {
//        CloudflareQueueService.sendMessage("456");
//        Thread.sleep(1000);
//        CloudflareQueueService.sendMessage("456");
//        Thread.sleep(3000);
//        JSONArray message = CloudflareQueueService.receiveMessage();
//        log.info("message: {}", message);
//    }
}
