package tv.shorthub.admin.service.googleplay;

import tv.shorthub.system.domain.AppRechargeItem;

/**
 * Google Play同步服务接口
 * 用于同步充值模板配置到Google Play产品
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
public interface IGooglePlaySyncService {

    /**
     * 同步充值模板到Google Play产品
     * @param rechargeItem 充值模板
     * @param packageName 应用包名
     * @return 同步结果
     */
    boolean syncRechargeToGooglePlay(AppRechargeItem rechargeItem, String packageName);

    /**
     * 批量同步充值模板到Google Play产品
     * @param templateId 模板ID
     * @param packageName 应用包名
     * @return 同步结果
     */
    boolean syncRechargeTemplateToGooglePlay(String templateId, String packageName);

    /**
     * 删除Google Play产品
     * @param productId 产品ID
     * @param packageName 应用包名
     * @return 删除结果
     */
    boolean deleteGooglePlayProduct(String productId, String packageName);

    /**
     * 检查Google Play产品是否存在
     * @param productId 产品ID
     * @param packageName 应用包名
     * @return 是否存在
     */
    boolean checkGooglePlayProductExists(String productId, String packageName);
} 