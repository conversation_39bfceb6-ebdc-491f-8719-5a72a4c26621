//package tv.shorthub.admin.service;
//
////import com.aliyun.dingtalk.DingTalkClient;
////import com.aliyun.dingtalk.DingTalkClientBuilder;
////import com.aliyun.dingtalk.api.DefaultDingTalkClient;
////import com.aliyun.dingtalk.api.DingTalkClient;
////import com.aliyun.dingtalk.api.request.OapiRobotSendRequest;
////import com.aliyun.dingtalk.api.response.OapiRobotSendResponse;
////import com.aliyun.teaopenapi.models.Config;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.stereotype.Service;
//
//import javax.annotation.PostConstruct;
//import java.util.Arrays;
//
//@Slf4j
//@Service
//public class DingTalkService {
//
//    @Value("${dingtalk.webhook}")
//    private String webhook;
//
//    @Value("${dingtalk.secret}")
//    private String secret;
//
//    private DingTalkClient client;
//
//    @PostConstruct
//    public void init() {
//        try {
//            Config config = new Config();
//            config.protocol = "https";
//            config.regionId = "central";
//            client = DingTalkClientBuilder.getInstance().withConfig(config).build();
//        } catch (Exception e) {
//            log.error("初始化钉钉客户端失败", e);
//        }
//    }
//
//    /**
//     * 发送文本消息
//     */
//    public void sendText(String content) {
//        try {
//            OapiRobotSendRequest request = new OapiRobotSendRequest();
//            request.setMsgtype("text");
//            OapiRobotSendRequest.Text text = new OapiRobotSendRequest.Text();
//            text.setContent(content);
//            request.setText(text);
//
//            OapiRobotSendResponse response = client.execute(request, webhook, secret);
//            if (!response.isSuccess()) {
//                log.error("发送钉钉消息失败: {}", response.getErrmsg());
//            }
//        } catch (Exception e) {
//            log.error("发送钉钉消息异常", e);
//        }
//    }
//
//    /**
//     * 发送markdown消息
//     */
//    public void sendMarkdown(String title, String content) {
//        try {
//            OapiRobotSendRequest request = new OapiRobotSendRequest();
//            request.setMsgtype("markdown");
//            OapiRobotSendRequest.Markdown markdown = new OapiRobotSendRequest.Markdown();
//            markdown.setTitle(title);
//            markdown.setText(content);
//            request.setMarkdown(markdown);
//
//            OapiRobotSendResponse response = client.execute(request, webhook, secret);
//            if (!response.isSuccess()) {
//                log.error("发送钉钉消息失败: {}", response.getErrmsg());
//            }
//        } catch (Exception e) {
//            log.error("发送钉钉消息异常", e);
//        }
//    }
//}
