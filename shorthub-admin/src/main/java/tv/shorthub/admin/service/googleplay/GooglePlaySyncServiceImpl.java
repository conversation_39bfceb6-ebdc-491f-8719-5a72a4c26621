package tv.shorthub.admin.service.googleplay;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.api.services.androidpublisher.model.BasePlan;
import com.google.api.services.androidpublisher.model.Subscription;
import com.google.api.services.androidpublisher.model.SubscriptionListing;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tv.shorthub.common.utils.SecurityUtils;
import tv.shorthub.googleplay.config.GooglePlayConfig;
import tv.shorthub.googleplay.model.Product;
import tv.shorthub.googleplay.service.GooglePlayService;
import tv.shorthub.googleplay.service.impl.GooglePlaySubscriptionService;
import tv.shorthub.system.domain.*;
import tv.shorthub.system.mapper.*;

import java.math.BigDecimal;
import java.util.*;

/**
 * Google Play同步服务实现类
 * 用于同步充值模板配置到Google Play产品
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
@Slf4j
@Service
public class GooglePlaySyncServiceImpl implements IGooglePlaySyncService {

    @Autowired
    private GooglePlayService googlePlayService;

    @Autowired
    private GooglePlayProductsMapper googlePlayProductsMapper;

    @Autowired
    private GooglePlayProductI18nMapper googlePlayProductI18nMapper;

    @Autowired
    private GooglePlayProductPricesMapper googlePlayProductPricesMapper;

    @Autowired
    private GooglePlaySubscriptionConfigMapper googlePlaySubscriptionConfigMapper;

    @Autowired
    private AppRechargeItemMapper appRechargeItemMapper;

    @Autowired
    private GooglePlaySubscriptionService googlePlaySubscriptionService;
    @Autowired
    private GooglePlayConfig googlePlayConfig;
    @Autowired
    private AppRechargeItemGiftMapper appRechargeItemGiftMapper;

    @Autowired
    AppRechargeSubscriptionGiftMapper appRechargeSubscriptionGiftMapper;

    public boolean syncToGooglePlay(AppRechargeItem rechargeItem, String packageName) {
        boolean success;
        if (rechargeItem.getType() == 2) {
            success = syncSubscriptionToGooglePlay(rechargeItem, packageName);
        } else {
            success = syncRechargeToGooglePlay(rechargeItem, packageName);
        }
        // 4. 同步到本地数据库
        syncToLocalDatabase(rechargeItem, packageName, rechargeItem.getItemId());
        return success;
    }

    public boolean syncSubscriptionToGooglePlay(AppRechargeItem rechargeItem, String packageName) {
        try {
            log.info("开始同步订阅到Google Play: itemId={}, packageName={}", rechargeItem.getItemId(), packageName);

            String billingPeriodDuration;
            String gracePeriodDuration;
            
            switch (rechargeItem.getSubscriptionPeriod()) {
                case "DAY" -> {
                    billingPeriodDuration = "P1D";
                    gracePeriodDuration = "P1D";
                }
                case "WEEK" -> {
                    billingPeriodDuration = "P1W";
                    gracePeriodDuration = "P3D";
                }
                case "MONTH" -> {
                    billingPeriodDuration = "P1M";
                    gracePeriodDuration = "P7D";
                }
                case "QUARTER" -> {
                    billingPeriodDuration = "P3M";
                    gracePeriodDuration = "P7D";
                }
                case "YEAR" -> {
                    billingPeriodDuration = "P1Y";
                    gracePeriodDuration = "P7D";
                }
                default -> throw new RuntimeException("不支持的订阅周期: " + rechargeItem.getSubscriptionPeriod());
            }

            // 验证并调整价格
            BigDecimal originalPrice = rechargeItem.getPrice();

            // 正确计算Money对象的units和nanos
            long priceUnits = originalPrice.longValue(); // 整数部分
            int priceNanos = originalPrice.subtract(new BigDecimal(priceUnits))
                .multiply(new BigDecimal("1000000000")).intValue(); // 小数部分转换为纳秒

            BasePlan basePlan = googlePlaySubscriptionService.builderBasePlan(
                packageName,
                rechargeItem.getItemId(),
                billingPeriodDuration,
                gracePeriodDuration,
                "USD",
                priceUnits,
                priceNanos
            );

            // 检查是否有首期优惠配置
            QueryWrapper<AppRechargeSubscriptionGift> giftQuery = new QueryWrapper<>();
            giftQuery.eq("item_id", rechargeItem.getItemId()).eq("enable", true).last("limit 1");
            AppRechargeSubscriptionGift gift = appRechargeSubscriptionGiftMapper.selectOne(giftQuery);

            Map<String, Map<String, String>> configs = googlePlayConfig.getLanguage().getConfigs();
            List<SubscriptionListing> listings = new ArrayList<>();
            for (String language : configs.keySet()) {
                SubscriptionListing listing = new SubscriptionListing();
                listing.setLanguageCode(language);
                listing.setTitle(configs.get(language).get("subscription_title"));
                listing.setDescription(configs.get(language).get("subscription_description"));
                listings.add(listing);
            }

            Subscription subscription = googlePlaySubscriptionService.createSubscriptionAndActivate(
                packageName,
                rechargeItem.getTemplateId(),
                Collections.singletonList(basePlan),
                listings,
                true
            );

            // 有首期优惠，创建带优惠的订阅产品
            if (gift != null) {
                // 固定金额优惠
                BigDecimal discountedPrice = gift.getGiftPrice();
                if (discountedPrice.compareTo(BigDecimal.ZERO) < 0) {
                    discountedPrice = BigDecimal.ZERO;
                }
                // offerId 只允许小写字母、数字、短横线，不能全数字，不能下划线，不能太长
                String offerId = "intro-offer-" + (rechargeItem.getItemId().hashCode() & 0x7fffffff);
                String discountDuration = getDiscountDurationBySubscriptionPeriod(rechargeItem.getSubscriptionPeriod());
                int freeTrialDays = 0;

                // 创建固定金额首期优惠，确保所有地区都设置
                boolean offerCreated = googlePlaySubscriptionService.createFixedPriceIntroOffer(
                    packageName,
                    rechargeItem.getTemplateId(),
                    rechargeItem.getItemId(),
                    offerId,
                    freeTrialDays,
                    discountDuration,
                    discountedPrice, // 传BigDecimal
                    basePlan // 传BasePlan
                );
                
                // 如果优惠创建成功，激活它
                if (offerCreated) {
                    try {
                        googlePlaySubscriptionService.activateSubscriptionOffer(
                            packageName,
                            rechargeItem.getTemplateId(),
                            rechargeItem.getItemId(),
                            offerId
                        );
                        log.info("✅ 激活订阅优惠成功: {}", offerId);
                    } catch (Exception e) {
                        log.warn("⚠️ 激活订阅优惠失败: {} - {}", offerId, e.getMessage());
                        // 不抛出异常，因为优惠创建成功了
                    }
                }
            }
            
            return true;
        } catch (Exception e) {
            log.error("同步订阅到Google Play失败: itemId={}, error={}", rechargeItem.getItemId(), e.getMessage(), e);
        }

        return false;
    }

    /**
     * 根据充值优惠配置创建Google Play优惠配置
     */
    private GooglePlaySubscriptionService.IntroductoryOfferConfig createOfferConfig(AppRechargeItem rechargeItem, AppRechargeItemGift gift) {
        
        String offerId = "intro_" + rechargeItem.getItemId();
        
        // 计算优惠参数
        int freeTrialDays = 0;
        int discountPercentage = 0;
        String discountDuration = getDiscountDurationBySubscriptionPeriod(rechargeItem.getSubscriptionPeriod());
        
        // 按固定金额计算折扣百分比
        BigDecimal giftAmount = new BigDecimal(gift.getGiftNumber());
        BigDecimal originalPrice = rechargeItem.getPrice();
        
        if (originalPrice.compareTo(BigDecimal.ZERO) > 0) {
            // 计算固定金额占原价的百分比
            discountPercentage = giftAmount.multiply(new BigDecimal(100))
                .divide(originalPrice, 0, BigDecimal.ROUND_HALF_UP).intValue();
            discountPercentage = Math.min(99, Math.max(1, discountPercentage)); // 限制在1-99%之间
        }
        
        log.info("创建优惠配置: offerId={}, 固定金额优惠={}元, 折扣百分比={}%, 优惠期={}, 订阅周期={}", 
            offerId, giftAmount, discountPercentage, discountDuration, rechargeItem.getSubscriptionPeriod());
        
        return new GooglePlaySubscriptionService.IntroductoryOfferConfig(
            offerId, 
            freeTrialDays, 
            discountPercentage, 
            discountDuration
        );
    }
    
    /**
     * 根据订阅周期获取优惠期时长（ISO 8601格式）
     * 优惠期 = 第一个计费周期的长度
     */
    private String getDiscountDurationBySubscriptionPeriod(String subscriptionPeriod) {
        if (subscriptionPeriod == null) {
            return "P1M"; // 默认1个月
        }
        
        return switch (subscriptionPeriod) {
            case "DAY" -> "P1D";      // 日订阅：第一天有优惠
            case "WEEK" -> "P1W";     // 周订阅：第一周有优惠
            case "MONTH" -> "P1M";    // 月订阅：第一个月有优惠
            case "QUARTER" -> "P3M";  // 季度订阅：第一个季度（3个月）有优惠
            case "YEAR" -> "P1Y";     // 年订阅：第一年有优惠
            default -> "P1M";
        };
    }

    /**
     * 验证并调整价格以符合Google Play各地区的最低要求
     */
    private BigDecimal validateAndAdjustPrice(BigDecimal originalPrice, String currency, String regionCode) {
        // Google Play各地区最低价格要求
        Map<String, BigDecimal> minimumPrices = new HashMap<>();
        minimumPrices.put("TWD", new BigDecimal("2.00")); // 台币最低2元
        minimumPrices.put("USD", new BigDecimal("0.99")); // 美元最低0.99元
        minimumPrices.put("HKD", new BigDecimal("8.00")); // 港币最低8元
        
        BigDecimal minimumPrice = minimumPrices.get(currency);
        if (minimumPrice != null && originalPrice.compareTo(minimumPrice) < 0) {
            log.warn("价格 {} {} 低于 {} 地区最低要求 {} {}，自动调整为最低价格", 
                originalPrice, currency, regionCode, minimumPrice, currency);
            return minimumPrice;
        }
        
        return originalPrice;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean syncRechargeToGooglePlay(AppRechargeItem rechargeItem, String packageName) {
        try {
            log.info("开始同步普通充值到Google Play: itemId={}, packageName={}", rechargeItem.getItemId(), packageName);

            // 1. 检查产品是否已存在
            String productId = rechargeItem.getItemId();
            boolean exists = checkGooglePlayProductExists(productId, packageName);

            // 2. 创建或更新Google Play产品
            Product googleProduct = createGooglePlayProduct(rechargeItem, packageName);
            
            if (exists) {
                googlePlayService.updateProduct(packageName, productId, googleProduct);
                log.info("更新Google Play产品成功: {}", productId);
            } else {
                googlePlayService.createProduct(packageName, googleProduct);
                log.info("创建Google Play产品成功: {}", productId);
            }

            log.info("同步充值模板到Google Play完成: {}", productId);
            return true;

        } catch (Exception e) {
            log.error("同步充值模板到Google Play失败: itemId={}, error={}", rechargeItem.getItemId(), e.getMessage(), e);
            throw new RuntimeException("同步失败", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean syncRechargeTemplateToGooglePlay(String templateId, String packageName) {
        try {
            log.info("开始批量同步充值模板到Google Play: templateId={}, packageName={}", templateId, packageName);

            // 查询模板下的所有充值项
            QueryWrapper<AppRechargeItem> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("template_id", templateId);
            List<AppRechargeItem> rechargeItems = appRechargeItemMapper.selectList(queryWrapper);

            // 创建一个初始订阅配置
            if (rechargeItems.stream().anyMatch(item -> item.getType() == 2)) {
                BigDecimal originalPrice = new BigDecimal("9.99");
                // 正确计算Money对象的units和nanos
                long priceUnits = originalPrice.longValue(); // 整数部分
                int priceNanos = originalPrice.subtract(new BigDecimal(priceUnits))
                        .multiply(new BigDecimal("1000000000")).intValue(); // 小数部分转换为纳秒
                BasePlan basePlan = googlePlaySubscriptionService.builderBasePlan(packageName, templateId, "P1M", "P1D", "USD", priceUnits, priceNanos);

                Map<String, Map<String, String>> configs = googlePlayConfig.getLanguage().getConfigs();

                List<SubscriptionListing> listings = new ArrayList<>();
                for (String language : configs.keySet()) {
                    SubscriptionListing listing = new SubscriptionListing();
                    listing.setLanguageCode(language);
                    listing.setTitle(configs.get(language).get("subscription_title"));
                    listing.setDescription(configs.get(language).get("subscription_description"));
                    listings.add(listing);
                }
                googlePlaySubscriptionService.createSubscriptionAndActivate(packageName, templateId, Collections.singletonList(basePlan), listings, false);
            }

            boolean allSuccess = true;
            for (AppRechargeItem item : rechargeItems) {
                try {
                    boolean success = syncToGooglePlay(item, packageName);
                    if (!success) {
                        allSuccess = false;
                        log.error("同步充值项失败: {}", item.getItemId());
                    }
                } catch (Exception e) {
                    allSuccess = false;
                    log.error("同步充值项异常: {}, error={}", item.getItemId(), e.getMessage(), e);
                }
            }

            log.info("批量同步充值模板完成: templateId={}, success={}", templateId, allSuccess);
            return allSuccess;

        } catch (Exception e) {
            log.error("批量同步充值模板失败: templateId={}, error={}", templateId, e.getMessage(), e);
            throw new RuntimeException("批量同步失败", e);
        }
    }

    @Override
    public boolean deleteGooglePlayProduct(String productId, String packageName) {
        try {
            log.info("删除Google Play产品: productId={}, packageName={}", productId, packageName);
            
            // 1. 从Google Play删除
            googlePlayService.deleteProduct(packageName, productId);
            
            // 2. 从本地数据库删除
            QueryWrapper<GooglePlayProducts> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("product_id", productId).eq("package_name", packageName);
            GooglePlayProducts product = googlePlayProductsMapper.selectOne(queryWrapper);
            
            if (product != null) {
                // 删除关联数据
                googlePlayProductI18nMapper.delete(new QueryWrapper<GooglePlayProductI18n>().eq("product_id", product.getId()));
                googlePlayProductPricesMapper.delete(new QueryWrapper<GooglePlayProductPrices>().eq("product_id", product.getId()));
                googlePlaySubscriptionConfigMapper.delete(new QueryWrapper<GooglePlaySubscriptionConfig>().eq("product_id", product.getId()));
                
                // 删除主表数据
                googlePlayProductsMapper.deleteById(product.getId());
            }
            
            log.info("删除Google Play产品成功: {}", productId);
            return true;
            
        } catch (Exception e) {
            log.error("删除Google Play产品失败: productId={}, error={}", productId, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean checkGooglePlayProductExists(String productId, String packageName) {
        try {
            googlePlayService.getProduct(packageName, productId);
            return true;
        } catch (Exception e) {
            return false;
        }
    }


    /**
     * 创建Google Play产品
     */
    private Product createGooglePlayProduct(AppRechargeItem rechargeItem, String packageName) {
        String productId = rechargeItem.getItemId();

        GooglePlayConfig.AppConfig appConfig = googlePlayConfig.getAppConfig(packageName);
        String defaultLanguage = appConfig.getDefaultLanguage();

        String defaultName = getProductName(rechargeItem, defaultLanguage);
        String defaultDescription = getProductDescription(rechargeItem, defaultLanguage);

        return Product.builder()
                .productId(productId)
                .name(defaultName)
                .description(defaultDescription)
                .type(Product.TYPE_INAPP)
                .defaultPrice(rechargeItem.getPrice())
                .defaultPriceCurrencyCode(appConfig.getDefaultCurrency())
                .status(Product.STATUS_ACTIVE)
                .packageName(packageName)
                .build();
    }

    /**
     * 获取产品名称
     */
    private String getProductName(AppRechargeItem rechargeItem, String language) {
        // 从配置获取语言模板
        Map<String, String> templates = googlePlayConfig.getLanguage().getConfigs().get(language);
        
        return switch (rechargeItem.getType().intValue()) {
            case 0 -> // 会员
                    templates.get("member");
            case 1 -> // 金币充值
                    templates.get("coin");
            case 2 -> // 订阅
                    templates.get("subscription");
            default -> templates.get("coin");
        };
    }

    /**
     * 获取产品描述
     */
    private String getProductDescription(AppRechargeItem rechargeItem, String language) {
        // 从配置获取语言模板
        Map<String, String> templates = googlePlayConfig.getLanguage().getConfigs().get(language);
        return switch (rechargeItem.getType().intValue()) {
            case 0 -> // 会员
                    templates.get("member_desc");
            case 1 -> // 金币充值
                    templates.get("coin_desc");
            case 2 -> // 订阅
                    templates.get("subscription_desc");
            default -> templates.get("coin_desc");
        };
    }

    /**
     * 确定产品类型
     */
    private String determineProductType(AppRechargeItem rechargeItem) {
        if (rechargeItem.getType() == null) {
            return Product.TYPE_INAPP;
        }

        return switch (rechargeItem.getType().intValue()) {
            case 0 -> // 会员
                    Product.TYPE_INAPP;
            case 1 -> // 金币充值
                    Product.TYPE_INAPP;
            case 2 -> // 订阅
                    Product.TYPE_SUBS;
            default -> Product.TYPE_INAPP;
        };
    }

    /**
     * 同步到本地数据库
     */
    private void syncToLocalDatabase(AppRechargeItem rechargeItem, String packageName, String productId) {

        String productType = determineProductType(rechargeItem);

        // 1. 检查并创建或更新主表
        QueryWrapper<GooglePlayProducts> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("product_id", productId).eq("package_name", packageName);
        GooglePlayProducts existingProduct = googlePlayProductsMapper.selectOne(queryWrapper);
        
        GooglePlayProducts product;
        if (existingProduct != null) {
            product = existingProduct;
            product.setProductType(productType);
            product.setStatus("active");
            product.setUpdateBy(SecurityUtils.getUsername());
            googlePlayProductsMapper.updateById(product);
        } else {
            product = new GooglePlayProducts();
            product.setProductId(productId);
            product.setPackageName(packageName);
            product.setProductType(productType);
            product.setStatus("active");
            product.setCreateBy(SecurityUtils.getUsername());
            googlePlayProductsMapper.insert(product);
        }
        
        // 2. 同步多语言信息
        syncI18nData(productId, rechargeItem);
        
        // 3. 同步价格信息
        syncPriceData(productId, rechargeItem);
        
        // 4. 如果是订阅产品，同步订阅配置
        if (Product.TYPE_SUBS.equals(productType)) {
            syncSubscriptionData(rechargeItem.getTemplateId(), rechargeItem);
        }
    }

    /**
     * 同步多语言数据
     */
    private void syncI18nData(String productId, AppRechargeItem rechargeItem) {
        // 删除现有数据
        googlePlayProductI18nMapper.delete(new QueryWrapper<GooglePlayProductI18n>().eq("product_id", productId));
        

        // 插入新数据
        for (String language : googlePlayConfig.getLanguage().getConfigs().keySet()) {
            GooglePlayProductI18n i18n = new GooglePlayProductI18n();
            i18n.setProductId(productId);
            i18n.setLanguage(language);
            i18n.setName(getProductName(rechargeItem, language));
            i18n.setDescription(getProductDescription(rechargeItem, language));
            i18n.setCreateBy(SecurityUtils.getUsername());
            googlePlayProductI18nMapper.insert(i18n);
        }
    }

    /**
     * 同步价格数据
     */
    private void syncPriceData(String productId, AppRechargeItem rechargeItem) {
        // 删除现有数据
        googlePlayProductPricesMapper.delete(new QueryWrapper<GooglePlayProductPrices>().eq("product_id", productId));
        
        GooglePlayProductPrices price = new GooglePlayProductPrices();
        price.setProductId(productId);
        price.setCurrencyCode("USD");
        price.setPriceAmount(rechargeItem.getPrice());
        price.setPriceMicros(rechargeItem.getPrice().multiply(new BigDecimal("1000000")).longValue());
        price.setIsDefault(true);
        price.setCreateBy(SecurityUtils.getUsername());
        googlePlayProductPricesMapper.insert(price);
    }

    /**
     * 同步订阅数据
     */
    private void syncSubscriptionData(String subscriptionId, AppRechargeItem rechargeItem) {

        Map<String, Map<String, String>> configs = googlePlayConfig.getLanguage().getConfigs();

        List<GooglePlaySubscriptionConfig> list = new ArrayList<>();
        for (String language : configs.keySet()) {
            String regionCode = configs.get(language).get("region_code");
            // 插入新数据
            GooglePlaySubscriptionConfig config = new GooglePlaySubscriptionConfig();
            config.setBasePlanId(rechargeItem.getItemId());
            config.setSubscriptionId(subscriptionId);
            config.setItemId(rechargeItem.getItemId());
            config.setCreateBy(SecurityUtils.getUsername());
            config.setCreateTime(new Date());
            config.setUpdateBy(SecurityUtils.getUsername());
            config.setUpdateTime(config.getCreateTime());
            list.add(config);
        }
        googlePlaySubscriptionConfigMapper.batchInsertOrUpdate(list);
    }
}