package tv.shorthub.admin.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.concurrent.TimeUnit;
import java.text.DecimalFormat;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.time.Duration;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.Arrays;
import tv.shorthub.common.core.oss.ProviderOSS;

@Service
public class VideoCompress {
    private static final Logger logger = LoggerFactory.getLogger(VideoCompress.class);
    private static final DecimalFormat df = new DecimalFormat("#.##");
    private static final Pattern DURATION_PATTERN = Pattern.compile("Duration: (\\d{2}):(\\d{2}):(\\d{2})\\.(\\d{2})");
    private static final Pattern TIME_PATTERN = Pattern.compile("time=(\\d{2}):(\\d{2}):(\\d{2})\\.(\\d{2})");
    private static final int URL_EXPIRY_HOURS = 24;  // URL过期时间（小时）

    @Value("${ffmpeg.path:ffmpeg}")
    private String ffmpegPath;

    @Value("${ffmpeg.timeout:1800}")  // 默认30分钟超时
    private int ffmpegTimeout;

    @Value("${ffmpeg.use.gpu:true}")  // 默认启用GPU
    private boolean useGpu;

    // 视频质量阈值（MB）
    private static final double LARGE_VIDEO_THRESHOLD = 150.0;  // 150MB
    private static final double MEDIUM_VIDEO_THRESHOLD = 50.0;  // 50MB
    private static final double SMALL_VIDEO_THRESHOLD = 20.0;   // 20MB

    @Autowired
    ProviderOSS providerOSS;

    /**
     * 获取视频时长（秒）
     */
    public long getVideoDuration(String videoPath) {
        Process process = null;
        try {
            ProcessBuilder processBuilder = new ProcessBuilder(
                ffmpegPath,
                "-i", videoPath
            );
            processBuilder.redirectErrorStream(true);
            process = processBuilder.start();

            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    Matcher durationMatcher = DURATION_PATTERN.matcher(line);
                    if (durationMatcher.find()) {
                        return parseTimeToSeconds(durationMatcher);
                    }
                }
            }

            boolean completed = process.waitFor(10, TimeUnit.SECONDS);
            if (!completed) {
                process.destroyForcibly();
                throw new RuntimeException("获取视频时长超时");
            }

            throw new RuntimeException("无法获取视频时长");
        } catch (Exception e) {
            logger.error("获取视频时长失败: {}", e.getMessage());
            throw new RuntimeException("获取视频时长失败: " + e.getMessage());
        } finally {
            if (process != null && process.isAlive()) {
                process.destroyForcibly();
            }
        }
    }

    /**
     * 检查系统是否支持GPU加速
     */
    private boolean isGpuAvailable() {
        if (!useGpu) {
            logger.info("GPU加速已禁用");
            return false;
        }
        try {
            // 检查CUDA库是否存在
            File cudaLib = new File("/usr/lib64/libcuda.so.1");
            if (!cudaLib.exists()) {
                logger.info("CUDA库不存在: {}", cudaLib.getAbsolutePath());
                return false;
            }

            ProcessBuilder processBuilder = new ProcessBuilder(ffmpegPath, "-hide_banner", "-hwaccels");
            Process process = processBuilder.start();
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    if (line.contains("cuda") || line.contains("nvenc") || line.contains("qsv")) {
                        logger.info("检测到GPU加速支持: {}", line.trim());
                        return true;
                    }
                }
            }
            process.waitFor(5, TimeUnit.SECONDS);
            logger.info("未检测到GPU加速支持");
        } catch (Exception e) {
            logger.warn("GPU检测失败: {}", e.getMessage());
        }
        return false;
    }

    /**
     * 压缩视频文件
     * @param inputPath 输入视频路径
     * @param outputPath 输出视频路径
     * @return 压缩后的视频文件路径
     */
    public String compressVideo(String inputPath, String outputPath) {
        Process process = null;
        Instant startTime = Instant.now();
        StringBuilder errorOutput = new StringBuilder();
        try {
            // 验证FFmpeg是否可用
            if (!isFFmpegAvailable()) {
                throw new RuntimeException("FFmpeg不可用，请检查配置");
            }

            // 验证输入文件
            File inputFile = new File(inputPath);
            if (!inputFile.exists()) {
                throw new IllegalArgumentException("输入文件不存在: " + inputPath);
            }

            // 确保输出目录存在
            Path outputDir = Paths.get(outputPath).getParent();
            if (!Files.exists(outputDir)) {
                Files.createDirectories(outputDir);
            }

            // 获取原始文件大小
            double originalSize = getFileSizeInMB(inputPath);
            logger.info("原始视频大小: {} MB", df.format(originalSize));

            // 根据原始文件大小确定压缩参数
            CompressionParams params = determineCompressionParams(originalSize);
            logger.info("使用压缩参数: CRF={}, 预设={}, 最大比特率={}",
                params.crf, params.preset, params.maxBitrate);

            // 检查GPU支持
            boolean gpuAvailable = isGpuAvailable();
            String videoCodec = gpuAvailable ? "h264_nvenc" : "libx264";
            String[] gpuParams = gpuAvailable ?
                new String[]{"-hwaccel", "cuda", "-hwaccel_output_format", "cuda"} :
                new String[]{};

            // 构建FFmpeg命令
            String[] command = new String[]{
                ffmpegPath,
                "-i", inputPath,
                "-c:v", videoCodec,      // 根据GPU支持选择编码器
                "-crf", String.valueOf(params.crf),  // 自适应CRF值
                "-preset", params.preset, // 自适应预设
                "-profile:v", "main",    // 使用main配置，更好的兼容性
                "-level", "4.0",         // 兼容性级别
                "-maxrate", params.maxBitrate,  // 最大比特率
                "-bufsize", params.bufsize,     // 缓冲区大小
                "-c:a", "aac",          // 音频编码
                "-b:a", "96k",          // 降低音频比特率
                "-ar", "44100",         // 音频采样率
                "-movflags", "+faststart", // 优化网络播放
                "-pix_fmt", "yuv420p",  // 确保更好的兼容性
                "-vf", "scale=trunc(iw/2)*2:trunc(ih/2)*2", // 确保分辨率是2的倍数
                "-progress", "pipe:1",   // 输出进度到标准输出
                "-y",                    // 覆盖已存在的文件
                outputPath
            };

            // 如果GPU编码失败，尝试使用CPU编码
            int maxRetries = gpuAvailable ? 2 : 1;
            int retryCount = 0;
            boolean success = false;

            while (retryCount < maxRetries && !success) {
                try {
                    if (retryCount > 0) {
                        logger.info("GPU编码失败，尝试使用CPU编码...");
                        command[3] = "libx264";  // 切换到CPU编码器
                    }

                    // 执行FFmpeg命令
                    ProcessBuilder processBuilder = new ProcessBuilder(command);
                    processBuilder.redirectErrorStream(true);
                    process = processBuilder.start();

                    // 读取FFmpeg输出
                    try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                        String line;
                        long totalDuration = 0;
                        long currentTime = 0;

                        while ((line = reader.readLine()) != null) {
                            errorOutput.append(line).append("\n");

                            if (totalDuration == 0) {
                                Matcher durationMatcher = DURATION_PATTERN.matcher(line);
                                if (durationMatcher.find()) {
                                    totalDuration = parseTimeToSeconds(durationMatcher);
                                }
                            }

                            Matcher timeMatcher = TIME_PATTERN.matcher(line);
                            if (timeMatcher.find()) {
                                currentTime = parseTimeToSeconds(timeMatcher);
                                if (totalDuration > 0) {
                                    double progress = (currentTime * 100.0) / totalDuration;
                                    Duration elapsed = Duration.between(startTime, Instant.now());
                                    logger.info("压缩进度: {}%, 已用时间: {}分{}秒",
                                        df.format(progress),
                                        elapsed.toMinutes(),
                                        elapsed.toSecondsPart());
                                }
                            }
                        }
                    }

                    boolean completed = process.waitFor(ffmpegTimeout, TimeUnit.SECONDS);
                    if (!completed) {
                        process.destroyForcibly();
                        throw new RuntimeException("视频压缩超时");
                    }

                    int exitCode = process.exitValue();
                    if (exitCode == 0) {
                        success = true;
                        break;
                    } else {
                        logger.warn("压缩失败，退出码: {}, 尝试重试...", exitCode);
                    }
                } catch (Exception e) {
                    logger.warn("压缩过程出错: {}, 尝试重试...", e.getMessage());
                } finally {
                    if (process != null && process.isAlive()) {
                        process.destroyForcibly();
                    }
                }
                retryCount++;
            }

            if (!success) {
                logger.error("视频压缩失败，command: {}, 错误输出: {}", String.join(" ", command), errorOutput.toString());
                throw new RuntimeException("视频压缩失败: " + errorOutput.toString());
            }

            // 验证输出文件
            File outputFile = new File(outputPath);
            if (!outputFile.exists()) {
                throw new RuntimeException("压缩后的文件不存在: " + outputPath);
            }

            double compressedSize = getFileSizeInMB(outputPath);
            double compressionRatio = (originalSize - compressedSize) / originalSize * 100;
            Duration totalTime = Duration.between(startTime, Instant.now());

            logger.info("视频压缩完成 - 原始大小: {} MB, 压缩后: {} MB, 压缩率: {}%, 总耗时: {}分{}秒",
                df.format(originalSize),
                df.format(compressedSize),
                df.format(compressionRatio),
                totalTime.toMinutes(),
                totalTime.toSecondsPart());

            // 如果压缩后文件更大或压缩效果不明显，使用原始文件
            if (compressedSize >= originalSize * 0.95) {
                logger.warn("压缩效果不明显（压缩率低于5%），将使用原始文件");
                try {
                    Files.copy(Paths.get(inputPath), Paths.get(outputPath), java.nio.file.StandardCopyOption.REPLACE_EXISTING);
                    logger.info("已使用原始文件替换压缩后的文件");
                    return outputPath;
                } catch (IOException e) {
                    logger.error("替换文件失败: {}", e.getMessage());
                    throw new RuntimeException("替换文件失败: " + e.getMessage());
                }
            }

            return outputPath;

        } catch (IOException e) {
            logger.error("视频压缩过程中发生错误: {}", e.getMessage());
            logger.error("错误输出: {}", errorOutput.toString());
            throw new RuntimeException("视频压缩失败: " + e.getMessage() + "\n错误输出: " + errorOutput.toString());
        } finally {
            if (process != null && process.isAlive()) {
                process.destroyForcibly();
            }
        }
    }

    /**
     * 解析时间字符串为秒数
     */
    private long parseTimeToSeconds(Matcher matcher) {
        int hours = Integer.parseInt(matcher.group(1));
        int minutes = Integer.parseInt(matcher.group(2));
        int seconds = Integer.parseInt(matcher.group(3));
        int milliseconds = Integer.parseInt(matcher.group(4));
        return hours * 3600L + minutes * 60L + seconds;
    }

    /**
     * 检查FFmpeg是否可用
     */
    private boolean isFFmpegAvailable() {
        Process process = null;
        try {
            ProcessBuilder processBuilder = new ProcessBuilder(ffmpegPath, "-version");
            process = processBuilder.start();
            boolean completed = process.waitFor(10, TimeUnit.SECONDS);
            if (!completed) {
                process.destroyForcibly();
                return false;
            }
            return process.exitValue() == 0;
        } catch (Exception e) {
            logger.error("FFmpeg检查失败: {}", e.getMessage());
            return false;
        } finally {
            if (process != null && process.isAlive()) {
                process.destroyForcibly();
            }
        }
    }

    /**
     * 根据原始文件大小确定压缩参数
     */
    private CompressionParams determineCompressionParams(double fileSizeMB) {
        if (fileSizeMB > LARGE_VIDEO_THRESHOLD) {
            // 大文件 (>150MB)
            return new CompressionParams(28, "faster", "3M", "6M");
        } else if (fileSizeMB > MEDIUM_VIDEO_THRESHOLD) {
            // 中等文件 (50-150MB)
            return new CompressionParams(26, "faster", "2M", "4M");
        } else if (fileSizeMB > SMALL_VIDEO_THRESHOLD) {
            // 小文件 (20-50MB)
            return new CompressionParams(24, "faster", "1.5M", "3M");
        } else {
            // 极小文件 (<20MB)
            return new CompressionParams(23, "faster", "1M", "2M");
        }
    }

    /**
     * 获取视频文件大小（MB）
     */
    private double getFileSizeInMB(String filePath) {
        File file = new File(filePath);
        return file.length() / (1024.0 * 1024.0);
    }

    /**
     * 压缩参数类
     */
    private static class CompressionParams {
        final int crf;
        final String preset;
        final String maxBitrate;
        final String bufsize;

        CompressionParams(int crf, String preset, String maxBitrate, String bufsize) {
            this.crf = crf;
            this.preset = preset;
            this.maxBitrate = maxBitrate;
            this.bufsize = bufsize;
        }
    }

    public static class HlsResult {
        public String m3u8Path;
        public List<String> tsPaths = new ArrayList<>();
        public String signedM3u8Url;  // 添加签名后的m3u8 URL
        public Map<String, String> signedTsUrls = new HashMap<>();  // 添加签名后的ts文件URL映射
    }

    /**
     * 压缩为 HLS 格式
     * @param inputPath 输入视频路径
     * @param outputDir 输出目录（会生成 m3u8 和 ts 分片）
     * @param storageBasePath R2存储的基础路径（例如：compressed/123/）
     * @return HlsResult 包含 m3u8 路径、所有 ts 分片路径和签名后的URL
     */
    public HlsResult compressToHls(String inputPath, String outputDir, String storageBasePath) {
        HlsResult result = new HlsResult();
        Process process = null;
        Instant startTime = Instant.now();
        StringBuilder errorOutput = new StringBuilder();

        try {
            // 验证FFmpeg是否可用
            if (!isFFmpegAvailable()) {
                throw new RuntimeException("FFmpeg不可用，请检查配置");
            }

            // 验证输入文件
            File inputFile = new File(inputPath);
            if (!inputFile.exists()) {
                throw new IllegalArgumentException("输入文件不存在: " + inputPath);
            }

            // 确保输出目录存在
            File dir = new File(outputDir);
            if (!dir.exists()) dir.mkdirs();

            // 获取原始文件大小以确定码率
            double originalSize = getFileSizeInMB(inputPath);
            logger.info("原始视频大小: {} MB", df.format(originalSize));

            // 检查GPU支持
            boolean gpuAvailable = isGpuAvailable();
            String videoCodec = gpuAvailable ? "h264_nvenc" : "libx264";
            String[] gpuParams = gpuAvailable ? 
                new String[]{"-hwaccel", "cuda", "-hwaccel_output_format", "cuda"} : 
                new String[]{};

            // m3u8文件路径
            String m3u8Path = outputDir + "/index.m3u8";
            result.m3u8Path = m3u8Path;

            // 构建FFmpeg命令
            List<String> commandList = new ArrayList<>();
            commandList.add(ffmpegPath);
            commandList.addAll(Arrays.asList(gpuParams));
            commandList.addAll(Arrays.asList(
                "-i", inputPath,
                "-c:v", videoCodec,
                "-b:v", "4M",
                "-maxrate", "5M",
                "-bufsize", "8M",
                "-c:a", "aac",
                "-b:a", "128k",
                "-ar", "44100",
                "-preset", "faster",
                "-hls_time", "6",
                "-hls_list_size", "0",
                "-hls_segment_filename", outputDir + "/segment_%03d.ts",
                "-y",
                m3u8Path
            ));

            // 如果GPU编码失败，尝试使用CPU编码
            int maxRetries = gpuAvailable ? 2 : 1;
            int retryCount = 0;
            boolean success = false;

            while (retryCount < maxRetries && !success) {
                try {
                    if (retryCount > 0) {
                        logger.info("GPU编码失败，尝试使用CPU编码...");
                        commandList.set(commandList.indexOf(videoCodec), "libx264");
                    }

                    // 执行FFmpeg命令
                    ProcessBuilder processBuilder = new ProcessBuilder(commandList);
                    processBuilder.redirectErrorStream(true);
                    process = processBuilder.start();

                    // 读取FFmpeg输出
                    try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                        String line;
                        long totalDuration = 0;
                        long currentTime = 0;
                        
                        while ((line = reader.readLine()) != null) {
                            errorOutput.append(line).append("\n");
                            
                            if (totalDuration == 0) {
                                Matcher durationMatcher = DURATION_PATTERN.matcher(line);
                                if (durationMatcher.find()) {
                                    totalDuration = parseTimeToSeconds(durationMatcher);
                                }
                            }
                            
                            Matcher timeMatcher = TIME_PATTERN.matcher(line);
                            if (timeMatcher.find()) {
                                currentTime = parseTimeToSeconds(timeMatcher);
                                if (totalDuration > 0) {
                                    double progress = (currentTime * 100.0) / totalDuration;
                                    Duration elapsed = Duration.between(startTime, Instant.now());
                                    logger.info("压缩进度: {}%, 已用时间: {}分{}秒", 
                                        df.format(progress),
                                        elapsed.toMinutes(),
                                        elapsed.toSecondsPart());
                                }
                            }
                        }
                    }

                    boolean completed = process.waitFor(ffmpegTimeout, TimeUnit.SECONDS);
                    if (!completed) {
                        process.destroyForcibly();
                        throw new RuntimeException("视频压缩超时");
                    }

                    int exitCode = process.exitValue();
                    if (exitCode == 0) {
                        success = true;
                        break;
                    } else {
                        logger.warn("压缩失败，退出码: {}, 尝试重试...", exitCode);
                    }
                } catch (Exception e) {
                    logger.warn("压缩过程出错: {}, 尝试重试...", e.getMessage());
                } finally {
                    if (process != null && process.isAlive()) {
                        process.destroyForcibly();
                    }
                }
                retryCount++;
            }

            if (!success) {
                throw new RuntimeException("视频压缩失败: " + errorOutput);
            }

            // 收集ts分片并生成签名URL
            File[] files = dir.listFiles((d, name) -> name.endsWith(".ts"));
            if (files != null) {
                for (File f : files) {
                    String tsPath = f.getAbsolutePath();
                    result.tsPaths.add(tsPath);
                    
                    // 为每个ts文件生成签名URL
                    String tsFileName = f.getName();
                    String tsStoragePath = storageBasePath + tsFileName;
                    Duration duration = Duration.ofHours(URL_EXPIRY_HOURS);
                    String signedTsUrl = providerOSS.generateUrl(tsStoragePath, duration);
                    result.signedTsUrls.put(tsFileName, signedTsUrl);
                }
            }

            // 修改m3u8文件，将ts文件路径替换为签名URL
            String m3u8Content = new String(Files.readAllBytes(Paths.get(m3u8Path)));
            for (Map.Entry<String, String> entry : result.signedTsUrls.entrySet()) {
                m3u8Content = m3u8Content.replace(entry.getKey(), entry.getValue());
            }
            Files.write(Paths.get(m3u8Path), m3u8Content.getBytes());

            // 为m3u8文件生成签名URL
            String m3u8StoragePath = storageBasePath + "index.m3u8";
            Duration duration = Duration.ofHours(URL_EXPIRY_HOURS);
            result.signedM3u8Url = providerOSS.generateUrl(m3u8StoragePath, duration);

            Duration totalTime = Duration.between(startTime, Instant.now());
            logger.info("HLS压缩完成 - 总耗时: {}分{}秒", totalTime.toMinutes(), totalTime.toSecondsPart());
            
            return result;

        } catch (Exception e) {
            logger.error("HLS压缩失败: {}", e.getMessage());
            logger.error("错误输出: {}", errorOutput.toString());
            throw new RuntimeException("HLS压缩失败: " + e.getMessage() + "\n错误输出: " + errorOutput.toString());
        } finally {
            if (process != null && process.isAlive()) {
                process.destroyForcibly();
            }
        }
    }
}
