//package tv.shorthub.admin.schedule;
//
//import java.util.Date;
//import java.util.List;
//import java.text.SimpleDateFormat;
//import java.text.ParseException;
//
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.scheduling.annotation.Scheduled;
//import org.springframework.stereotype.Component;
//
//import com.alibaba.fastjson2.JSONObject;
//import com.alibaba.fastjson2.JSONArray;
//import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
//
//import lombok.extern.slf4j.Slf4j;
//import tv.shorthub.system.domain.PaypalPaymentLog;
//import tv.shorthub.system.domain.PaypalSubscriptionRenewal;
//import tv.shorthub.system.service.IPaypalPaymentLogService;
//import tv.shorthub.system.service.IPaypalSubscriptionRenewalService;
//
///**
// * 自动订阅续期定时任务
// */
//@Component
//@Slf4j
//public class AutoNextSubscribeRecharge {
//
//    @Autowired
//    IPaypalPaymentLogService paypalPaymentLogService;
//
//    @Autowired
//    IPaypalSubscriptionRenewalService paypalSubscriptionRenewalService;
//
//    // 5秒钟执行一次
//    @Scheduled(cron = "0/5 * * * * ?")
//    public void execute() {
//        // 查询所有状态是完成的订阅类型订单
//        List<PaypalPaymentLog> tasks = paypalPaymentLogService.getMapper().selectList(new LambdaQueryWrapper<PaypalPaymentLog>()
//            .eq(PaypalPaymentLog::getState, "ACTIVE")
//        );
//
//        log.info("自动订阅续期定时任务，查询到的激活订单数量：{}", tasks.size());
//
//        for (PaypalPaymentLog task : tasks) {
//            try {
//                JSONObject rawData = task.getRawData();
//                start(task.getOrderNo(), rawData);
//
//            } catch (ParseException e) {
//                log.error("订单号：{}，解析日期失败", task.getOrderNo(), e);
//            } catch (Exception e) {
//                log.error("订单号：{}，处理续订时发生错误", task.getOrderNo(), e);
//            }
//        }
//    }
//
//    public static void main(String[] args) throws ParseException {
//        JSONObject rawData = JSONObject.parseObject("{\"id\":\"I-RC2BFB1JE169\",\"links\":[{\"rel\":\"cancel\",\"href\":\"https://api.sandbox.paypal.com/v1/billing/subscriptions/I-RC2BFB1JE169/cancel\",\"method\":\"POST\"},{\"rel\":\"edit\",\"href\":\"https://api.sandbox.paypal.com/v1/billing/subscriptions/I-RC2BFB1JE169\",\"method\":\"PATCH\"},{\"rel\":\"self\",\"href\":\"https://api.sandbox.paypal.com/v1/billing/subscriptions/I-RC2BFB1JE169\",\"method\":\"GET\"},{\"rel\":\"suspend\",\"href\":\"https://api.sandbox.paypal.com/v1/billing/subscriptions/I-RC2BFB1JE169/suspend\",\"method\":\"POST\"},{\"rel\":\"capture\",\"href\":\"https://api.sandbox.paypal.com/v1/billing/subscriptions/I-RC2BFB1JE169/capture\",\"method\":\"POST\"}],\"status\":\"ACTIVE\",\"plan_id\":\"P-3CP291526F468612TNBCACFI\",\"quantity\":\"1\",\"start_time\":\"2025-06-07T09:06:29Z\",\"subscriber\":{\"name\":{\"surname\":\"Doe\",\"given_name\":\"John\"},\"tenant\":\"PAYPAL\",\"payer_id\":\"G4985XHB5XV2C\",\"email_address\":\"<EMAIL>\",\"shipping_address\":{\"address\":{\"postal_code\":\"200000\",\"admin_area_1\":\"Shanghai\",\"admin_area_2\":\"Shanghai\",\"country_code\":\"C2\",\"address_line_1\":\"NO 1 Nan Jin Road\"}}},\"create_time\":\"2025-06-07T09:06:57Z\",\"update_time\":\"2025-06-16T13:59:20Z\",\"billing_info\":{\"last_payment\":{\"time\":\"2025-06-16T13:59:19Z\",\"amount\":{\"value\":\"2.0\",\"currency_code\":\"USD\"}},\"cycle_executions\":[{\"sequence\":1,\"tenure_type\":\"REGULAR\",\"total_cycles\":12,\"cycles_completed\":10,\"cycles_remaining\":2,\"current_pricing_scheme_version\":1}],\"next_billing_time\":\"2025-06-17T10:00:00Z\",\"final_payment_time\":\"2025-06-18T10:00:00Z\",\"outstanding_balance\":{\"value\":\"0.0\",\"currency_code\":\"USD\"},\"failed_payments_count\":0},\"plan_overridden\":false,\"shipping_amount\":{\"value\":\"0.0\",\"currency_code\":\"USD\"},\"status_update_time\":\"2025-06-16T13:59:20Z\"}");
//
//        start("I-RC2BFB1JE169", rawData);
//
//    }
//
//    private static boolean start(String orderNo, JSONObject rawData) throws ParseException {
//        if (rawData == null) {
//            log.warn("订单号：{}，rawData为空", orderNo);
//            return true;
//        }
//
//        JSONObject billingInfo = rawData.getJSONObject("billing_info");
//        if (billingInfo == null) {
//            log.info("订单号：{}，未找到billing_info字段", orderNo);
//            return true;
//        }
//
//        // 检查付款失败次数
//        int failedPaymentsCount = billingInfo.getIntValue("failed_payments_count", 0);
//        if (failedPaymentsCount > 3) {
//            log.warn("订单号：{}，存在{}次付款失败记录", orderNo, failedPaymentsCount);
//            return true;
//        }
//
//        // 检查最后付款状态
//        JSONObject lastPayment = billingInfo.getJSONObject("last_payment");
//        if (lastPayment == null) {
//            log.info("订单号：{}，未找到last_payment字段，可能未完成付款", orderNo);
//            return true;
//        }
//
//        // 获取下次付款时间
//        String nextBillingTime = billingInfo.getString("next_billing_time");
//        if (nextBillingTime == null) {
//            log.info("订单号：{}，未找到next_billing_time字段", orderNo);
//            return true;
//        }
//
//        // 解析下次付款时间
//        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'");
//        Date nextBillingDate = sdf.parse(nextBillingTime);
//
//        // 检查最后付款时间
//        String lastPaymentTime = lastPayment.getString("time");
//        if (lastPaymentTime == null) {
//            log.info("订单号：{}，未找到last_payment.time字段", orderNo);
//            return true;
//        }
//
//        // 解析最后付款时间
//        Date lastPaymentDate = sdf.parse(lastPaymentTime);
//        // 如果最后付款时间在下次付款时间之后，说明已经完成续订
//        if (lastPaymentDate.after(nextBillingDate)) {
//            log.info("订单号：{}，已经完成续订付款，最后付款时间：{}", orderNo, lastPaymentDate);
//            return true;
//        }
//
//        // 检查订阅周期执行情况
//        JSONArray cycleExecutions = billingInfo.getJSONArray("cycle_executions");
//        if (cycleExecutions == null || cycleExecutions.isEmpty()) {
//            log.warn("订单号：{}，未找到cycle_executions信息", orderNo);
//            return true;
//        }
//
//        JSONObject currentCycle = cycleExecutions.getJSONObject(0);
//        int cyclesCompleted = currentCycle.getIntValue("cycles_completed", 0);
//        int cyclesRemaining = currentCycle.getIntValue("cycles_remaining", 0);
//
//        if (cyclesCompleted == 0) {
//            log.info("订单号：{}，尚未完成任何付款周期", orderNo);
//            return true;
//        }
//
//        Date now = new Date();
//
//        // 如果下次付款时间已到，执行充值
//        if (nextBillingDate.before(now)) {
//            log.info("订单号：{}，开始执行续订充值，已完成周期：{}，剩余周期：{}",
//                orderNo, cyclesCompleted, cyclesRemaining);
//
////            // 检查是否已经续订过
////            String subscriptionId = rawData.getString("id");
////            PaypalSubscriptionRenewal existingRenewal = paypalSubscriptionRenewalService.getMapper().selectOne(
////                new LambdaQueryWrapper<PaypalSubscriptionRenewal>()
////                    .eq(PaypalSubscriptionRenewal::getSubscriptionId, subscriptionId)
////                    .eq(PaypalSubscriptionRenewal::getCycleNumber, Long.valueOf(cyclesCompleted + 1))
////            );
////
////            if (existingRenewal != null) {
////                log.info("订单号：{}，周期{}已经续订过，跳过处理", orderNo, cyclesCompleted + 1);
////                return true;
////            }
////
////            // 创建续订记录
////            PaypalSubscriptionRenewal renewal = new PaypalSubscriptionRenewal();
////            renewal.setSubscriptionId(subscriptionId);
////            renewal.setOrderNo(orderNo);
////            renewal.setPlanId(rawData.getString("plan_id"));
////            renewal.setStatus("PENDING");
////            renewal.setAmount(lastPayment.getJSONObject("amount").getBigDecimal("value"));
////            renewal.setCurrency(lastPayment.getJSONObject("amount").getString("currency_code"));
////            renewal.setCycleNumber(Long.valueOf(cyclesCompleted + 1));
////            renewal.setTotalCycles(Long.valueOf(cyclesCompleted + cyclesRemaining));
////            renewal.setNextBillingTime(nextBillingDate);
////            renewal.setRenewalTime(now);
////            renewal.setRawData(rawData.toJSONString());
////
////            try {
////                // 保存续订记录
////                paypalSubscriptionRenewalService.getMapper().insert(renewal);
////
////                // TODO: 调用充值接口
////                // 这里需要调用你的充值服务
////                // rechargeService.recharge(orderNo, renewal.getAmount());
////
////                // 更新续订状态为成功
////                renewal.setStatus("SUCCESS");
////                paypalSubscriptionRenewalService.getMapper().updateById(renewal);
////
////                log.info("订单号：{}，续订充值完成", orderNo);
////            } catch (Exception e) {
////                // 更新续订状态为失败
////                renewal.setStatus("FAILED");
////                renewal.setErrorMessage(e.getMessage());
////                paypalSubscriptionRenewalService.getMapper().updateById(renewal);
////                throw e;
////            }
//        } else {
//            log.info("订单号：{}，下次续订时间：{}，尚未到达续订时间",
//                orderNo, nextBillingDate);
//        }
//        return false;
//    }
//}
