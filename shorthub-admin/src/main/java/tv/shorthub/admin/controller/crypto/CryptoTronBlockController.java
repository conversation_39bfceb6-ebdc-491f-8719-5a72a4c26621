package tv.shorthub.admin.controller.crypto;

import com.github.pagehelper.PageHelper;
import tv.shorthub.common.core.domain.SummaryRequest;
import tv.shorthub.common.utils.SummaryColumnUtils;
import org.springframework.web.multipart.MultipartFile;
import java.util.ArrayList;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import tv.shorthub.common.annotation.Log;
import tv.shorthub.common.core.controller.BaseController;
import tv.shorthub.common.core.domain.AjaxResult;
import tv.shorthub.common.enums.BusinessType;
import tv.shorthub.system.domain.CryptoTronBlock;
import tv.shorthub.system.service.ICryptoTronBlockService;
import tv.shorthub.common.utils.poi.ExcelUtil;
import tv.shorthub.common.core.page.TableDataInfo;

/**
 * TRON区块链区块Controller
 *
 * <AUTHOR>
 * @date 2025-08-05
 */
@RestController
@RequestMapping("/crypto/blocktron")
public class CryptoTronBlockController extends BaseController
{
    @Autowired
    private ICryptoTronBlockService cryptoTronBlockService;

    /**
     * 查询TRON区块链区块列表
     */
    @PreAuthorize("@ss.hasPermi('crypto:blocktron:list')")
    @GetMapping("/list")
    public TableDataInfo list(CryptoTronBlock cryptoTronBlock)
    {
        startPage();
        List<CryptoTronBlock> list = cryptoTronBlockService.selectList(cryptoTronBlock);
        return getDataTable(list);
    }

    /**
     * 获取TRON区块链区块数据汇总
     */
    @PreAuthorize("@ss.hasPermi('crypto:blocktron:query')")
    @GetMapping(value = "/getSummary")
    public AjaxResult getSummary(CryptoTronBlock cryptoTronBlock)
    {
        return AjaxResult.success(cryptoTronBlockService.getSummary(cryptoTronBlock));
    }

    /**
     * 导出TRON区块链区块列表
     */
    @PreAuthorize("@ss.hasPermi('crypto:blocktron:export')")
    @Log(title = "TRON区块链区块", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CryptoTronBlock cryptoTronBlock)
    {
        List<CryptoTronBlock> list = cryptoTronBlockService.selectList(cryptoTronBlock);
        ExcelUtil<CryptoTronBlock> util = new ExcelUtil<>(CryptoTronBlock.class);
        util.exportExcel(response, list, "TRON区块链区块数据");
    }

    /**
     * 获取TRON区块链区块详细信息
     */
    @PreAuthorize("@ss.hasPermi('crypto:blocktron:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(cryptoTronBlockService.getById(id));
    }

    /**
     * 新增TRON区块链区块
     */
    @PreAuthorize("@ss.hasPermi('crypto:blocktron:add')")
    @Log(title = "TRON区块链区块", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CryptoTronBlock cryptoTronBlock)
    {
        return toAjax(cryptoTronBlockService.insert(cryptoTronBlock));
    }

    /**
     * 修改TRON区块链区块
     */
    @PreAuthorize("@ss.hasPermi('crypto:blocktron:edit')")
    @Log(title = "TRON区块链区块", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CryptoTronBlock cryptoTronBlock)
    {
        return toAjax(cryptoTronBlockService.update(cryptoTronBlock));
    }

    /**
     * 删除TRON区块链区块
     */
    @PreAuthorize("@ss.hasPermi('crypto:blocktron:remove')")
    @Log(title = "TRON区块链区块", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable List<Long> ids)
    {
        return toAjax(cryptoTronBlockService.deleteByIds(ids));
    }


    /**
     * 查询自定义分析列
     */
    @PreAuthorize("@ss.hasPermi('crypto:blocktron:list')")
    @GetMapping("/columns")
    public AjaxResult columns()
    {
        return AjaxResult.success(SummaryColumnUtils.get(CryptoTronBlock.class));
    }

    /**
     * 查询自定义分析数据
     */
    @PreAuthorize("@ss.hasPermi('crypto:blocktron:list')")
    @RequestMapping(value = "/summary")
    public TableDataInfo summary(@RequestBody SummaryRequest query)
    {
        startPage();
        List<CryptoTronBlock> summary = cryptoTronBlockService.summary(query);
        return getDataTable(summary);
    }

    /**
     * 导出自定义分析数据
     */
    @PreAuthorize("@ss.hasPermi('crypto:blocktron:export')")
    @Log(title = "导出自定义分析数据", businessType = BusinessType.EXPORT)
    @PostMapping("/summaryExport")
    public void summaryExport(HttpServletResponse response, @RequestBody SummaryRequest query)
    {
        PageHelper.startPage(1, Integer.MAX_VALUE);
        if (null == query.getQuery()) {
            query.setQuery(new ArrayList<>());
        }
        List<CryptoTronBlock> list = cryptoTronBlockService.summary(query);
        query.getGroupBy().addAll(query.getColumns());
        ExcelUtil<CryptoTronBlock> util = new ExcelUtil<>(CryptoTronBlock.class);
        util.exportExcel(response, list, "导出数据");
    }



    /**
     * 下载导入模板
     */
    @PreAuthorize("@ss.hasPermi('crypto:blocktron:export')")
    @Log(title = "下载导入模板", businessType = BusinessType.EXPORT)
    @PostMapping("/downloadImportModule")
    public void downloadImportModule()
    {
        cryptoTronBlockService.downloadImportModule();
    }

    /**
     * 导入数据
     */
    @PreAuthorize("@ss.hasPermi('crypto:blocktron:export')")
    @Log(title = "导入数据", businessType = BusinessType.EXPORT)
    @PostMapping("/importData")
    public void importData(MultipartFile file)
    {

        cryptoTronBlockService.importData(file);
    }


    /**
     * 查询自定义分析数据-汇总
     */
    @PreAuthorize("@ss.hasPermi('crypto:blocktron:list')")
    @RequestMapping(value = "/allSummary")
    public AjaxResult allSummary(@RequestBody SummaryRequest query)
    {
        PageHelper.startPage(1, 1);
        return AjaxResult.success(cryptoTronBlockService.allSummary(query));
    }
}
