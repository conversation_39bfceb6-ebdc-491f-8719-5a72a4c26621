package tv.shorthub.admin.controller.crypto;

import com.github.pagehelper.PageHelper;
import tv.shorthub.common.core.domain.SummaryRequest;
import tv.shorthub.common.utils.SummaryColumnUtils;
import org.springframework.web.multipart.MultipartFile;
import java.util.ArrayList;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import tv.shorthub.common.annotation.Log;
import tv.shorthub.common.core.controller.BaseController;
import tv.shorthub.common.core.domain.AjaxResult;
import tv.shorthub.common.enums.BusinessType;
import tv.shorthub.system.domain.CryptoPaymentConfig;
import tv.shorthub.system.service.ICryptoPaymentConfigService;
import tv.shorthub.common.utils.poi.ExcelUtil;
import tv.shorthub.common.core.page.TableDataInfo;

/**
 * 虚拟货币支付配置Controller
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
@RestController
@RequestMapping("/crypto/config")
public class CryptoPaymentConfigController extends BaseController
{
    @Autowired
    private ICryptoPaymentConfigService cryptoPaymentConfigService;

    /**
     * 查询虚拟货币支付配置列表
     */
    @PreAuthorize("@ss.hasPermi('crypto:config:list')")
    @GetMapping("/list")
    public TableDataInfo list(CryptoPaymentConfig cryptoPaymentConfig)
    {
        startPage();
        List<CryptoPaymentConfig> list = cryptoPaymentConfigService.selectList(cryptoPaymentConfig);
        return getDataTable(list);
    }

    /**
     * 获取虚拟货币支付配置数据汇总
     */
    @PreAuthorize("@ss.hasPermi('crypto:config:query')")
    @GetMapping(value = "/getSummary")
    public AjaxResult getSummary(CryptoPaymentConfig cryptoPaymentConfig)
    {
        return AjaxResult.success(cryptoPaymentConfigService.getSummary(cryptoPaymentConfig));
    }

    /**
     * 导出虚拟货币支付配置列表
     */
    @PreAuthorize("@ss.hasPermi('crypto:config:export')")
    @Log(title = "虚拟货币支付配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CryptoPaymentConfig cryptoPaymentConfig)
    {
        List<CryptoPaymentConfig> list = cryptoPaymentConfigService.selectList(cryptoPaymentConfig);
        ExcelUtil<CryptoPaymentConfig> util = new ExcelUtil<>(CryptoPaymentConfig.class);
        util.exportExcel(response, list, "虚拟货币支付配置数据");
    }

    /**
     * 获取虚拟货币支付配置详细信息
     */
    @PreAuthorize("@ss.hasPermi('crypto:config:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(cryptoPaymentConfigService.getById(id));
    }

    /**
     * 新增虚拟货币支付配置
     */
    @PreAuthorize("@ss.hasPermi('crypto:config:add')")
    @Log(title = "虚拟货币支付配置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CryptoPaymentConfig cryptoPaymentConfig)
    {
        return toAjax(cryptoPaymentConfigService.insert(cryptoPaymentConfig));
    }

    /**
     * 修改虚拟货币支付配置
     */
    @PreAuthorize("@ss.hasPermi('crypto:config:edit')")
    @Log(title = "虚拟货币支付配置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CryptoPaymentConfig cryptoPaymentConfig)
    {
        return toAjax(cryptoPaymentConfigService.update(cryptoPaymentConfig));
    }

    /**
     * 删除虚拟货币支付配置
     */
    @PreAuthorize("@ss.hasPermi('crypto:config:remove')")
    @Log(title = "虚拟货币支付配置", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable List<Long> ids)
    {
        return toAjax(cryptoPaymentConfigService.deleteByIds(ids));
    }


    /**
     * 查询自定义分析列
     */
    @PreAuthorize("@ss.hasPermi('crypto:config:list')")
    @GetMapping("/columns")
    public AjaxResult columns()
    {
        return AjaxResult.success(SummaryColumnUtils.get(CryptoPaymentConfig.class));
    }

    /**
     * 查询自定义分析数据
     */
    @PreAuthorize("@ss.hasPermi('crypto:config:list')")
    @RequestMapping(value = "/summary")
    public TableDataInfo summary(@RequestBody SummaryRequest query)
    {
        startPage();
        List<CryptoPaymentConfig> summary = cryptoPaymentConfigService.summary(query);
        return getDataTable(summary);
    }

    /**
     * 导出自定义分析数据
     */
    @PreAuthorize("@ss.hasPermi('crypto:config:export')")
    @Log(title = "导出自定义分析数据", businessType = BusinessType.EXPORT)
    @PostMapping("/summaryExport")
    public void summaryExport(HttpServletResponse response, @RequestBody SummaryRequest query)
    {
        PageHelper.startPage(1, Integer.MAX_VALUE);
        if (null == query.getQuery()) {
            query.setQuery(new ArrayList<>());
        }
        List<CryptoPaymentConfig> list = cryptoPaymentConfigService.summary(query);
        query.getGroupBy().addAll(query.getColumns());
        ExcelUtil<CryptoPaymentConfig> util = new ExcelUtil<>(CryptoPaymentConfig.class);
        util.exportExcel(response, list, "导出数据");
    }



    /**
     * 下载导入模板
     */
    @PreAuthorize("@ss.hasPermi('crypto:config:export')")
    @Log(title = "下载导入模板", businessType = BusinessType.EXPORT)
    @PostMapping("/downloadImportModule")
    public void downloadImportModule()
    {
        cryptoPaymentConfigService.downloadImportModule();
    }

    /**
     * 导入数据
     */
    @PreAuthorize("@ss.hasPermi('crypto:config:export')")
    @Log(title = "导入数据", businessType = BusinessType.EXPORT)
    @PostMapping("/importData")
    public void importData(MultipartFile file)
    {

        cryptoPaymentConfigService.importData(file);
    }


    /**
     * 查询自定义分析数据-汇总
     */
    @PreAuthorize("@ss.hasPermi('crypto:config:list')")
    @RequestMapping(value = "/allSummary")
    public AjaxResult allSummary(@RequestBody SummaryRequest query)
    {
        PageHelper.startPage(1, 1);
        return AjaxResult.success(cryptoPaymentConfigService.allSummary(query));
    }
}
