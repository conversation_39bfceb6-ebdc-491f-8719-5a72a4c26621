package tv.shorthub.admin.controller.app;

import com.github.pagehelper.PageHelper;
import tv.shorthub.common.core.domain.SummaryRequest;
import tv.shorthub.common.utils.SummaryColumnUtils;
import org.springframework.web.multipart.MultipartFile;
import java.util.ArrayList;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import tv.shorthub.common.annotation.Log;
import tv.shorthub.common.core.controller.BaseController;
import tv.shorthub.common.core.domain.AjaxResult;
import tv.shorthub.common.enums.BusinessType;
import tv.shorthub.system.domain.AppDramaContentsSerialUrl;
import tv.shorthub.system.service.IAppDramaContentsSerialUrlService;
import tv.shorthub.common.utils.poi.ExcelUtil;
import tv.shorthub.common.core.page.TableDataInfo;

/**
 * 剧集链接Controller
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@RestController
@RequestMapping("/app/serial/url")
public class AppDramaContentsSerialUrlController extends BaseController
{
    @Autowired
    private IAppDramaContentsSerialUrlService appDramaContentsSerialUrlService;

    /**
     * 查询剧集链接列表
     */
    @PreAuthorize("@ss.hasPermi('app/serial:url:list')")
    @GetMapping("/list")
    public TableDataInfo list(AppDramaContentsSerialUrl appDramaContentsSerialUrl)
    {
        startPage();
        List<AppDramaContentsSerialUrl> list = appDramaContentsSerialUrlService.selectList(appDramaContentsSerialUrl);
        return getDataTable(list);
    }

    /**
     * 获取剧集链接数据汇总
     */
    @PreAuthorize("@ss.hasPermi('app/serial:url:query')")
    @GetMapping(value = "/getSummary")
    public AjaxResult getSummary(AppDramaContentsSerialUrl appDramaContentsSerialUrl)
    {
        return AjaxResult.success(appDramaContentsSerialUrlService.getSummary(appDramaContentsSerialUrl));
    }

    /**
     * 导出剧集链接列表
     */
    @PreAuthorize("@ss.hasPermi('app/serial:url:export')")
    @Log(title = "剧集链接", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AppDramaContentsSerialUrl appDramaContentsSerialUrl)
    {
        List<AppDramaContentsSerialUrl> list = appDramaContentsSerialUrlService.selectList(appDramaContentsSerialUrl);
        ExcelUtil<AppDramaContentsSerialUrl> util = new ExcelUtil<>(AppDramaContentsSerialUrl.class);
        util.exportExcel(response, list, "剧集链接数据");
    }

    /**
     * 获取剧集链接详细信息
     */
    @PreAuthorize("@ss.hasPermi('app/serial:url:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(appDramaContentsSerialUrlService.getById(id));
    }

    /**
     * 新增剧集链接
     */
    @PreAuthorize("@ss.hasPermi('app/serial:url:add')")
    @Log(title = "剧集链接", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AppDramaContentsSerialUrl appDramaContentsSerialUrl)
    {
        return toAjax(appDramaContentsSerialUrlService.insert(appDramaContentsSerialUrl));
    }

    /**
     * 修改剧集链接
     */
    @PreAuthorize("@ss.hasPermi('app/serial:url:edit')")
    @Log(title = "剧集链接", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AppDramaContentsSerialUrl appDramaContentsSerialUrl)
    {
        return toAjax(appDramaContentsSerialUrlService.update(appDramaContentsSerialUrl));
    }

    /**
     * 删除剧集链接
     */
    @PreAuthorize("@ss.hasPermi('app/serial:url:remove')")
    @Log(title = "剧集链接", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable List<Long> ids)
    {
        return toAjax(appDramaContentsSerialUrlService.deleteByIds(ids));
    }


    /**
     * 查询自定义分析列
     */
    @PreAuthorize("@ss.hasPermi('app/serial:url:list')")
    @GetMapping("/columns")
    public AjaxResult columns()
    {
        return AjaxResult.success(SummaryColumnUtils.get(AppDramaContentsSerialUrl.class));
    }

    /**
     * 查询自定义分析数据
     */
    @PreAuthorize("@ss.hasPermi('app/serial:url:list')")
    @RequestMapping(value = "/summary")
    public TableDataInfo summary(@RequestBody SummaryRequest query)
    {
        startPage();
        List<AppDramaContentsSerialUrl> summary = appDramaContentsSerialUrlService.summary(query);
        return getDataTable(summary);
    }

    /**
     * 导出自定义分析数据
     */
    @PreAuthorize("@ss.hasPermi('app/serial:url:export')")
    @Log(title = "导出自定义分析数据", businessType = BusinessType.EXPORT)
    @PostMapping("/summaryExport")
    public void summaryExport(HttpServletResponse response, @RequestBody SummaryRequest query)
    {
        PageHelper.startPage(1, Integer.MAX_VALUE);
        if (null == query.getQuery()) {
            query.setQuery(new ArrayList<>());
        }
        List<AppDramaContentsSerialUrl> list = appDramaContentsSerialUrlService.summary(query);
        query.getGroupBy().addAll(query.getColumns());
        ExcelUtil<AppDramaContentsSerialUrl> util = new ExcelUtil<>(AppDramaContentsSerialUrl.class);
        util.exportExcel(response, list, "导出数据");
    }



    /**
     * 下载导入模板
     */
    @PreAuthorize("@ss.hasPermi('app/serial:url:export')")
    @Log(title = "下载导入模板", businessType = BusinessType.EXPORT)
    @PostMapping("/downloadImportModule")
    public void downloadImportModule()
    {
        appDramaContentsSerialUrlService.downloadImportModule();
    }

    /**
     * 导入数据
     */
    @PreAuthorize("@ss.hasPermi('app/serial:url:export')")
    @Log(title = "导入数据", businessType = BusinessType.EXPORT)
    @PostMapping("/importData")
    public void importData(MultipartFile file)
    {

        appDramaContentsSerialUrlService.importData(file);
    }


    /**
     * 查询自定义分析数据-汇总
     */
    @PreAuthorize("@ss.hasPermi('app/serial:url:list')")
    @RequestMapping(value = "/allSummary")
    public AjaxResult allSummary(@RequestBody SummaryRequest query)
    {
        PageHelper.startPage(1, 1);
        return AjaxResult.success(appDramaContentsSerialUrlService.allSummary(query));
    }
}
