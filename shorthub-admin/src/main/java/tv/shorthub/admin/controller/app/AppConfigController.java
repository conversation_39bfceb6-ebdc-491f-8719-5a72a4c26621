package tv.shorthub.admin.controller.app;

import com.github.pagehelper.PageHelper;
import tv.shorthub.common.core.domain.SummaryRequest;
import tv.shorthub.common.utils.SummaryColumnUtils;
import org.springframework.web.multipart.MultipartFile;
import java.util.ArrayList;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import tv.shorthub.common.annotation.Log;
import tv.shorthub.common.core.controller.BaseController;
import tv.shorthub.common.core.domain.AjaxResult;
import tv.shorthub.common.enums.BusinessType;
import tv.shorthub.system.domain.AppConfig;
import tv.shorthub.system.service.IAppConfigService;
import tv.shorthub.common.utils.poi.ExcelUtil;
import tv.shorthub.common.core.page.TableDataInfo;

/**
 * 客户端配置Controller
 *
 * <AUTHOR>
 * @date 2025-07-21
 */
@RestController
@RequestMapping("/app/config")
public class AppConfigController extends BaseController
{
    @Autowired
    private IAppConfigService appConfigService;

    /**
     * 查询客户端配置列表
     */
    @PreAuthorize("@ss.hasPermi('app:config:list')")
    @GetMapping("/list")
    public TableDataInfo list(AppConfig appConfig)
    {
        startPage();
        List<AppConfig> list = appConfigService.selectList(appConfig);
        return getDataTable(list);
    }

    /**
     * 获取客户端配置数据汇总
     */
    @PreAuthorize("@ss.hasPermi('app:config:query')")
    @GetMapping(value = "/getSummary")
    public AjaxResult getSummary(AppConfig appConfig)
    {
        return AjaxResult.success(appConfigService.getSummary(appConfig));
    }

    /**
     * 导出客户端配置列表
     */
    @PreAuthorize("@ss.hasPermi('app:config:export')")
    @Log(title = "客户端配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AppConfig appConfig)
    {
        List<AppConfig> list = appConfigService.selectList(appConfig);
        ExcelUtil<AppConfig> util = new ExcelUtil<>(AppConfig.class);
        util.exportExcel(response, list, "客户端配置数据");
    }

    /**
     * 获取客户端配置详细信息
     */
    @PreAuthorize("@ss.hasPermi('app:config:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(appConfigService.getById(id));
    }

    /**
     * 新增客户端配置
     */
    @PreAuthorize("@ss.hasPermi('app:config:add')")
    @Log(title = "客户端配置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AppConfig appConfig)
    {
        return toAjax(appConfigService.insert(appConfig));
    }

    /**
     * 修改客户端配置
     */
    @PreAuthorize("@ss.hasPermi('app:config:edit')")
    @Log(title = "客户端配置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AppConfig appConfig)
    {
        return toAjax(appConfigService.update(appConfig));
    }

    /**
     * 删除客户端配置
     */
    @PreAuthorize("@ss.hasPermi('app:config:remove')")
    @Log(title = "客户端配置", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable List<Long> ids)
    {
        return toAjax(appConfigService.deleteByIds(ids));
    }


    /**
     * 查询自定义分析列
     */
    @PreAuthorize("@ss.hasPermi('app:config:list')")
    @GetMapping("/columns")
    public AjaxResult columns()
    {
        return AjaxResult.success(SummaryColumnUtils.get(AppConfig.class));
    }

    /**
     * 查询自定义分析数据
     */
    @PreAuthorize("@ss.hasPermi('app:config:list')")
    @RequestMapping(value = "/summary")
    public TableDataInfo summary(@RequestBody SummaryRequest query)
    {
        startPage();
        List<AppConfig> summary = appConfigService.summary(query);
        return getDataTable(summary);
    }

    /**
     * 导出自定义分析数据
     */
    @PreAuthorize("@ss.hasPermi('app:config:export')")
    @Log(title = "导出自定义分析数据", businessType = BusinessType.EXPORT)
    @PostMapping("/summaryExport")
    public void summaryExport(HttpServletResponse response, @RequestBody SummaryRequest query)
    {
        PageHelper.startPage(1, Integer.MAX_VALUE);
        if (null == query.getQuery()) {
            query.setQuery(new ArrayList<>());
        }
        List<AppConfig> list = appConfigService.summary(query);
        query.getGroupBy().addAll(query.getColumns());
        ExcelUtil<AppConfig> util = new ExcelUtil<>(AppConfig.class);
        util.exportExcel(response, list, "导出数据");
    }



    /**
     * 下载导入模板
     */
    @PreAuthorize("@ss.hasPermi('app:config:export')")
    @Log(title = "下载导入模板", businessType = BusinessType.EXPORT)
    @PostMapping("/downloadImportModule")
    public void downloadImportModule()
    {
        appConfigService.downloadImportModule();
    }

    /**
     * 导入数据
     */
    @PreAuthorize("@ss.hasPermi('app:config:export')")
    @Log(title = "导入数据", businessType = BusinessType.EXPORT)
    @PostMapping("/importData")
    public void importData(MultipartFile file)
    {

        appConfigService.importData(file);
    }


    /**
     * 查询自定义分析数据-汇总
     */
    @PreAuthorize("@ss.hasPermi('app:config:list')")
    @RequestMapping(value = "/allSummary")
    public AjaxResult allSummary(@RequestBody SummaryRequest query)
    {
        PageHelper.startPage(1, 1);
        return AjaxResult.success(appConfigService.allSummary(query));
    }
}
