package tv.shorthub.admin.controller.app;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import tv.shorthub.common.core.controller.BaseController;
import tv.shorthub.common.core.domain.AjaxResult;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * PayPal状态管理Controller
 */
@RestController
@RequestMapping("/app/paypal/state")
public class PaypalStateController extends BaseController {

    /**
     * 获取所有PayPal状态定义
     */
    @GetMapping("/list")
    public AjaxResult list() {
        Map<String, Object> states = new LinkedHashMap<>();

        // 订单状态
        Map<String, Object> orderStates = new LinkedHashMap<>();
        orderStates.put("CREATED", createState("已创建", "info"));
        orderStates.put("SAVED", createState("已保存", "info"));
        orderStates.put("APPROVED", createState("已批准", "success"));
        orderStates.put("VOIDED", createState("已作废", "danger"));
        orderStates.put("COMPLETED", createState("已完成", "success"));
        orderStates.put("PAYER_ACTION_REQUIRED", createState("等待支付者操作", "warning"));
        orderStates.put("APPROVAL_PENDING", createState("等待批准", "warning"));
        orderStates.put("SUSPENDED", createState("已暂停", "warning"));
        orderStates.put("CANCELLED", createState("已取消", "danger"));
        orderStates.put("EXPIRED", createState("已过期", "danger"));
        states.put("orderStates", orderStates);

        // 支付状态
        Map<String, Object> paymentStates = new LinkedHashMap<>();
        paymentStates.put("PENDING", createState("待处理", "warning"));
        paymentStates.put("PROCESSING", createState("处理中", "warning"));
        paymentStates.put("SUCCESS", createState("成功", "success"));
        paymentStates.put("FAILED", createState("失败", "danger"));
        paymentStates.put("DENIED", createState("已拒绝", "danger"));
        paymentStates.put("CANCELLED", createState("已取消", "danger"));
        paymentStates.put("EXPIRED", createState("已过期", "danger"));
        paymentStates.put("BLOCKED", createState("已阻止", "danger"));
        paymentStates.put("PARTIALLY_CAPTURED", createState("部分捕获", "warning"));
        paymentStates.put("CAPTURED", createState("已捕获", "success"));
        paymentStates.put("DECLINED", createState("已拒绝", "danger"));
        paymentStates.put("PARTIALLY_REFUNDED", createState("部分退款", "warning"));
        paymentStates.put("REFUNDED", createState("已退款", "warning"));
        paymentStates.put("REVERSED", createState("已撤销", "danger"));
        paymentStates.put("VOIDED", createState("已作废", "danger"));
        states.put("paymentStates", paymentStates);

        // 订阅状态
        Map<String, Object> subscriptionStates = new LinkedHashMap<>();
        subscriptionStates.put("APPROVAL_PENDING", createState("等待批准", "warning"));
        subscriptionStates.put("APPROVED", createState("已批准", "success"));
        subscriptionStates.put("ACTIVE", createState("活跃", "success"));
        subscriptionStates.put("SUSPENDED", createState("已暂停", "warning"));
        subscriptionStates.put("CANCELLED", createState("已取消", "danger"));
        subscriptionStates.put("EXPIRED", createState("已过期", "danger"));
        subscriptionStates.put("PAYMENT_FAILED", createState("支付失败", "danger"));
        subscriptionStates.put("PAYMENT_PENDING", createState("支付待处理", "warning"));
        subscriptionStates.put("PAYMENT_APPROVED", createState("支付已批准", "success"));
        subscriptionStates.put("PAYMENT_DENIED", createState("支付已拒绝", "danger"));
        subscriptionStates.put("PAYMENT_COMPLETED", createState("支付已完成", "success"));
        subscriptionStates.put("PAYMENT_REFUNDED", createState("支付已退款", "warning"));
        subscriptionStates.put("PAYMENT_PARTIALLY_REFUNDED", createState("支付部分退款", "warning"));
        states.put("subscriptionStates", subscriptionStates);

        return AjaxResult.success(states);
    }

    private Map<String, String> createState(String label, String type) {
        Map<String, String> state = new LinkedHashMap<>();
        state.put("label", label);
        state.put("type", type);
        return state;
    }
}
