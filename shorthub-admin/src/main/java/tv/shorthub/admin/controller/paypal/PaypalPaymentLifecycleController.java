package tv.shorthub.admin.controller.paypal;

import com.github.pagehelper.PageHelper;
import tv.shorthub.common.core.domain.SummaryRequest;
import tv.shorthub.common.utils.SummaryColumnUtils;
import org.springframework.web.multipart.MultipartFile;
import java.util.ArrayList;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import tv.shorthub.common.annotation.Log;
import tv.shorthub.common.core.controller.BaseController;
import tv.shorthub.common.core.domain.AjaxResult;
import tv.shorthub.common.enums.BusinessType;
import tv.shorthub.system.domain.PaypalPaymentLifecycle;
import tv.shorthub.system.service.IPaypalPaymentLifecycleService;
import tv.shorthub.common.utils.poi.ExcelUtil;
import tv.shorthub.common.core.page.TableDataInfo;

/**
 * paypal组件生命周期Controller
 *
 * <AUTHOR>
 * @date 2025-07-04
 */
@RestController
@RequestMapping("/app/paypal/lifecycle")
public class PaypalPaymentLifecycleController extends BaseController
{
    @Autowired
    private IPaypalPaymentLifecycleService paypalPaymentLifecycleService;

    /**
     * 查询paypal组件生命周期列表
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:lifecycle:list')")
    @GetMapping("/list")
    public TableDataInfo list(PaypalPaymentLifecycle paypalPaymentLifecycle)
    {
        startPage();
        List<PaypalPaymentLifecycle> list = paypalPaymentLifecycleService.selectList(paypalPaymentLifecycle);
        return getDataTable(list);
    }

    /**
     * 获取paypal组件生命周期数据汇总
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:lifecycle:query')")
    @GetMapping(value = "/getSummary")
    public AjaxResult getSummary(PaypalPaymentLifecycle paypalPaymentLifecycle)
    {
        return AjaxResult.success(paypalPaymentLifecycleService.getSummary(paypalPaymentLifecycle));
    }

    /**
     * 导出paypal组件生命周期列表
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:lifecycle:export')")
    @Log(title = "paypal组件生命周期", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PaypalPaymentLifecycle paypalPaymentLifecycle)
    {
        List<PaypalPaymentLifecycle> list = paypalPaymentLifecycleService.selectList(paypalPaymentLifecycle);
        ExcelUtil<PaypalPaymentLifecycle> util = new ExcelUtil<>(PaypalPaymentLifecycle.class);
        util.exportExcel(response, list, "paypal组件生命周期数据");
    }

    /**
     * 获取paypal组件生命周期详细信息
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:lifecycle:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(paypalPaymentLifecycleService.getById(id));
    }

    /**
     * 新增paypal组件生命周期
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:lifecycle:add')")
    @Log(title = "paypal组件生命周期", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PaypalPaymentLifecycle paypalPaymentLifecycle)
    {
        return toAjax(paypalPaymentLifecycleService.insert(paypalPaymentLifecycle));
    }

    /**
     * 修改paypal组件生命周期
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:lifecycle:edit')")
    @Log(title = "paypal组件生命周期", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PaypalPaymentLifecycle paypalPaymentLifecycle)
    {
        return toAjax(paypalPaymentLifecycleService.update(paypalPaymentLifecycle));
    }

    /**
     * 删除paypal组件生命周期
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:lifecycle:remove')")
    @Log(title = "paypal组件生命周期", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable List<Long> ids)
    {
        return toAjax(paypalPaymentLifecycleService.deleteByIds(ids));
    }


    /**
     * 查询自定义分析列
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:lifecycle:list')")
    @GetMapping("/columns")
    public AjaxResult columns()
    {
        return AjaxResult.success(SummaryColumnUtils.get(PaypalPaymentLifecycle.class));
    }

    /**
     * 查询自定义分析数据
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:lifecycle:list')")
    @RequestMapping(value = "/summary")
    public TableDataInfo summary(@RequestBody SummaryRequest query)
    {
        startPage();
        List<PaypalPaymentLifecycle> summary = paypalPaymentLifecycleService.summary(query);
        return getDataTable(summary);
    }

    /**
     * 导出自定义分析数据
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:lifecycle:export')")
    @Log(title = "导出自定义分析数据", businessType = BusinessType.EXPORT)
    @PostMapping("/summaryExport")
    public void summaryExport(HttpServletResponse response, @RequestBody SummaryRequest query)
    {
        PageHelper.startPage(1, Integer.MAX_VALUE);
        if (null == query.getQuery()) {
            query.setQuery(new ArrayList<>());
        }
        List<PaypalPaymentLifecycle> list = paypalPaymentLifecycleService.summary(query);
        query.getGroupBy().addAll(query.getColumns());
        ExcelUtil<PaypalPaymentLifecycle> util = new ExcelUtil<>(PaypalPaymentLifecycle.class);
        util.exportExcel(response, list, "导出数据");
    }



    /**
     * 下载导入模板
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:lifecycle:export')")
    @Log(title = "下载导入模板", businessType = BusinessType.EXPORT)
    @PostMapping("/downloadImportModule")
    public void downloadImportModule()
    {
        paypalPaymentLifecycleService.downloadImportModule();
    }

    /**
     * 导入数据
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:lifecycle:export')")
    @Log(title = "导入数据", businessType = BusinessType.EXPORT)
    @PostMapping("/importData")
    public void importData(MultipartFile file)
    {

        paypalPaymentLifecycleService.importData(file);
    }


    /**
     * 查询自定义分析数据-汇总
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:lifecycle:list')")
    @RequestMapping(value = "/allSummary")
    public AjaxResult allSummary(@RequestBody SummaryRequest query)
    {
        PageHelper.startPage(1, 1);
        return AjaxResult.success(paypalPaymentLifecycleService.allSummary(query));
    }
}
