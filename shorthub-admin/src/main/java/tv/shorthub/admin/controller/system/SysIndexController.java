package tv.shorthub.admin.controller.system;

import tv.shorthub.common.config.AppConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import tv.shorthub.common.utils.StringUtils;

/**
 * 首页
 *
 * <AUTHOR>
 */
@RestController
public class SysIndexController
{
    /** 系统基础配置 */
    @Autowired
    private AppConfig ruoyiConfig;

    /**
     * 访问首页，提示语
     */
    @RequestMapping("/")
    public String index()
    {
        return StringUtils.format("name: {}, version：v{}", ruoyiConfig.getName(), ruoyiConfig.getVersion());
    }
}
