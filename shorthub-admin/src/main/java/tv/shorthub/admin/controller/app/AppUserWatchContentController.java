package tv.shorthub.admin.controller.app;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.PageHelper;
import tv.shorthub.common.core.domain.SummaryRequest;
import tv.shorthub.common.utils.SummaryColumnUtils;
import org.springframework.web.multipart.MultipartFile;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import tv.shorthub.common.annotation.Log;
import tv.shorthub.common.core.controller.BaseController;
import tv.shorthub.common.core.domain.AjaxResult;
import tv.shorthub.common.enums.BusinessType;
import tv.shorthub.system.domain.AppDramaContents;
import tv.shorthub.system.domain.AppUserWatchContent;
import tv.shorthub.system.domain.AppUserWatchRecords;
import tv.shorthub.system.service.IAppDramaContentsService;
import tv.shorthub.system.service.IAppUserWatchContentService;
import tv.shorthub.common.utils.poi.ExcelUtil;
import tv.shorthub.common.core.page.TableDataInfo;

/**
 * 观看剧目记录Controller
 *
 * <AUTHOR>
 * @date 2025-05-10
 */
@RestController
@RequestMapping("/app/watch_content")
public class AppUserWatchContentController extends BaseController
{
    @Autowired
    private IAppUserWatchContentService appUserWatchContentService;

    @Autowired
    private IAppDramaContentsService appDramaContentsService;

    /**
     * 查询观看剧目记录列表
     */
    @PreAuthorize("@ss.hasPermi('app:watch_content:list')")
    @GetMapping("/list")
    public TableDataInfo list(AppUserWatchContent appUserWatchContent)
    {
        startPage();
        List<AppUserWatchContent> list = appUserWatchContentService.selectList(appUserWatchContent);
        TableDataInfo dataTable = getDataTable(list);
        List<AppUserWatchContent> rows = (List<AppUserWatchContent>) dataTable.getRows();
        List<AppDramaContents> contents = appDramaContentsService.getMapper().selectList(new QueryWrapper<AppDramaContents>().in("content_id", rows.stream().map(AppUserWatchContent::getContentId).distinct().toList()));
        Map<String, AppDramaContents> contentsMap = contents.stream().collect(
                Collectors.toMap(AppDramaContents::getContentId, v -> v)
        );
        for (AppUserWatchContent row : rows) {
            row.getParams().put("content", contentsMap.get(row.getContentId()));
        }
        dataTable.setRows(rows);
        return dataTable;
    }

    /**
     * 获取观看剧目记录数据汇总
     */
    @PreAuthorize("@ss.hasPermi('app:watch_content:query')")
    @GetMapping(value = "/getSummary")
    public AjaxResult getSummary(AppUserWatchContent appUserWatchContent)
    {
        return AjaxResult.success(appUserWatchContentService.getSummary(appUserWatchContent));
    }

    /**
     * 导出观看剧目记录列表
     */
    @PreAuthorize("@ss.hasPermi('app:watch_content:export')")
    @Log(title = "观看剧目记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AppUserWatchContent appUserWatchContent)
    {
        List<AppUserWatchContent> list = appUserWatchContentService.selectList(appUserWatchContent);
        ExcelUtil<AppUserWatchContent> util = new ExcelUtil<>(AppUserWatchContent.class);
        util.exportExcel(response, list, "观看剧目记录数据");
    }

    /**
     * 获取观看剧目记录详细信息
     */
    @PreAuthorize("@ss.hasPermi('app:watch_content:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(appUserWatchContentService.getById(id));
    }

    /**
     * 新增观看剧目记录
     */
    @PreAuthorize("@ss.hasPermi('app:watch_content:add')")
    @Log(title = "观看剧目记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AppUserWatchContent appUserWatchContent)
    {
        return toAjax(appUserWatchContentService.insert(appUserWatchContent));
    }

    /**
     * 修改观看剧目记录
     */
    @PreAuthorize("@ss.hasPermi('app:watch_content:edit')")
    @Log(title = "观看剧目记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AppUserWatchContent appUserWatchContent)
    {
        return toAjax(appUserWatchContentService.update(appUserWatchContent));
    }

    /**
     * 删除观看剧目记录
     */
    @PreAuthorize("@ss.hasPermi('app:watch_content:remove')")
    @Log(title = "观看剧目记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable List<Long> ids)
    {
        return toAjax(appUserWatchContentService.deleteByIds(ids));
    }


    /**
     * 查询自定义分析列
     */
    @PreAuthorize("@ss.hasPermi('app:watch_content:list')")
    @GetMapping("/columns")
    public AjaxResult columns()
    {
        return AjaxResult.success(SummaryColumnUtils.get(AppUserWatchContent.class));
    }

    /**
     * 查询自定义分析数据
     */
    @PreAuthorize("@ss.hasPermi('app:watch_content:list')")
    @RequestMapping(value = "/summary")
    public TableDataInfo summary(@RequestBody SummaryRequest query)
    {
        startPage();
        List<AppUserWatchContent> summary = appUserWatchContentService.summary(query);
        return getDataTable(summary);
    }

    /**
     * 导出自定义分析数据
     */
    @PreAuthorize("@ss.hasPermi('app:watch_content:export')")
    @Log(title = "导出自定义分析数据", businessType = BusinessType.EXPORT)
    @PostMapping("/summaryExport")
    public void summaryExport(HttpServletResponse response, @RequestBody SummaryRequest query)
    {
        PageHelper.startPage(1, Integer.MAX_VALUE);
        if (null == query.getQuery()) {
            query.setQuery(new ArrayList<>());
        }
        List<AppUserWatchContent> list = appUserWatchContentService.summary(query);
        query.getGroupBy().addAll(query.getColumns());
        ExcelUtil<AppUserWatchContent> util = new ExcelUtil<>(AppUserWatchContent.class);
        util.exportExcel(response, list, "导出数据");
    }



    /**
     * 下载导入模板
     */
    @PreAuthorize("@ss.hasPermi('app:watch_content:export')")
    @Log(title = "下载导入模板", businessType = BusinessType.EXPORT)
    @PostMapping("/downloadImportModule")
    public void downloadImportModule()
    {
        appUserWatchContentService.downloadImportModule();
    }

    /**
     * 导入数据
     */
    @PreAuthorize("@ss.hasPermi('app:watch_content:export')")
    @Log(title = "导入数据", businessType = BusinessType.EXPORT)
    @PostMapping("/importData")
    public void importData(MultipartFile file)
    {

        appUserWatchContentService.importData(file);
    }


    /**
     * 查询自定义分析数据-汇总
     */
    @PreAuthorize("@ss.hasPermi('app:watch_content:list')")
    @RequestMapping(value = "/allSummary")
    public AjaxResult allSummary(@RequestBody SummaryRequest query)
    {
        PageHelper.startPage(1, 1);
        return AjaxResult.success(appUserWatchContentService.allSummary(query));
    }
}
