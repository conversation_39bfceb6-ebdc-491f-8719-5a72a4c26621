package tv.shorthub.admin.controller.common;

import cn.hutool.core.util.IdUtil;
import tv.shorthub.common.config.AppConfig;
import tv.shorthub.common.constant.Constants;
import tv.shorthub.common.core.domain.AjaxResult;
import tv.shorthub.common.core.oss.S3Uploader;
import tv.shorthub.common.utils.StringUtils;
import tv.shorthub.common.utils.UploadProgressPool;
import tv.shorthub.common.utils.file.FileUploadUtils;
import tv.shorthub.common.utils.file.FileUtils;
import tv.shorthub.common.utils.spring.SpringUtils;
import tv.shorthub.framework.config.ServerConfig;
import tv.shorthub.system.service.impl.SnowflakeIdWorker;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.io.File;

/**
 * 通用请求处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/common")
public class CommonController
{
    private static final Logger log = LoggerFactory.getLogger(CommonController.class);

    @Autowired
    private ServerConfig serverConfig;

    private static final String FILE_DELIMETER = ",";

    @Autowired
    S3Uploader s3Uploader;


    /**
     * 通用下载请求
     *
     * @param fileName 文件名称
     * @param delete 是否删除
     */
    @GetMapping("/download")
    public void fileDownload(String fileName, Boolean delete, HttpServletResponse response, HttpServletRequest request)
    {
        try
        {
            if (!FileUtils.checkAllowDownload(fileName))
            {
                throw new Exception(StringUtils.format("文件名称({})非法，不允许下载。 ", fileName));
            }
            String realFileName = System.currentTimeMillis() + fileName.substring(fileName.indexOf("_") + 1);
            String filePath = AppConfig.getDownloadPath() + fileName;

            response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
            FileUtils.setAttachmentResponseHeader(response, realFileName);
            FileUtils.writeBytes(filePath, response.getOutputStream());
            if (delete)
            {
                FileUtils.deleteFile(filePath);
            }
        }
        catch (Exception e)
        {
            log.error("下载文件失败", e);
        }
    }


    /**
     * 通用上传请求（单个）
     */
    @PostMapping("/upload")
    public AjaxResult uploadFile(
            MultipartFile file,
            @RequestHeader(value = "oss", required = false) Boolean oss,
            @RequestHeader(value = "oss-path", required = false) String ossPath,
            @RequestHeader(value = "open", required = false) Boolean open,
            @RequestHeader(value = "upload-id", required = false) String uploadId,
            HttpServletRequest request
    ) throws Exception
    {
        log.info("上传文件请求头: {}, {}, {}, {}", oss, ossPath, open, uploadId);
        try
        {
            // 上传文件路径
            String filePath = AppConfig.getUploadPath();
            // 上传并返回新文件名称
            String fileName = FileUploadUtils.upload(filePath, file, (bytesRead, contentLength) -> {
                // 计算上传进度
                int percent = (int) (bytesRead * 100 / contentLength);
                // 将进度信息存储在 Map 中
                if (uploadId != null) {
                    UploadProgressPool.updateUploadProgress(uploadId, percent, UploadProgressPool.STAGE_SERVER_PROCESS);
//                    log.info("本地文件上传进度 [{}]: {}%", uploadId, percent);
                }
            });
            String url = serverConfig.getUrl() + fileName;
            AjaxResult ajax = AjaxResult.success();
            ajax.put("url", url);
            ajax.put("fileName", fileName);
            ajax.put("newFileName", FileUtils.getName(fileName));
            ajax.put("originalFilename", file.getOriginalFilename());

            if (null != oss && oss) {
                String localFilePath = AppConfig.getProfile() + fileName.replaceFirst(Constants.RESOURCE_PREFIX, "");
                if (StringUtils.isEmpty(ossPath)) {
                    String fileExtension = "";
                    int dotIndex = localFilePath.lastIndexOf(".");
                    if (dotIndex > 0) {
                        fileExtension = localFilePath.substring(dotIndex); // 如 .mp4
                    }
                    // 加上文件后缀
                    ossPath = "temp/" + IdUtil.getSnowflakeNextIdStr() + fileExtension;
                }
                // 上传到OSS, open=是否可以公开访问
                String ossUrl;
                if (uploadId != null) {
                    // 使用带进度跟踪的上传方法
                    ossUrl = s3Uploader.uploadToPrivateWithProgress(ossPath, new File(localFilePath), uploadId);
                } else {
                    ossUrl = null == open ? s3Uploader.uploadToPrivate(ossPath, localFilePath) : s3Uploader.upload(ossPath, localFilePath);
                }
                ajax.put("url", ossUrl);
                ajax.put("ossPath", ossPath);

                // 删除本地文件
                FileUtils.deleteFile(localFilePath);
                log.info("删除本地文件 [{}]", localFilePath);
            }

            // 上传完成后清理进度信息
            if (uploadId != null) {
                UploadProgressPool.remove(uploadId);
            }

            return ajax;
        }
        catch (Exception e)
        {
            // 发生错误时也要清理进度信息
            if (uploadId != null) {
                UploadProgressPool.remove(uploadId);
            }
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 通用上传请求（多个）
     */
    @PostMapping("/uploads")
    public AjaxResult uploadFiles(List<MultipartFile> files) throws Exception
    {
        try
        {
            // 上传文件路径
            String filePath = AppConfig.getUploadPath();
            List<String> urls = new ArrayList<String>();
            List<String> fileNames = new ArrayList<String>();
            List<String> newFileNames = new ArrayList<String>();
            List<String> originalFilenames = new ArrayList<String>();
            for (MultipartFile file : files)
            {
                // 上传并返回新文件名称
                String fileName = FileUploadUtils.upload(filePath, file);
                String url = serverConfig.getUrl() + fileName;
                urls.add(url);
                fileNames.add(fileName);
                newFileNames.add(FileUtils.getName(fileName));
                originalFilenames.add(file.getOriginalFilename());
            }
            AjaxResult ajax = AjaxResult.success();
            ajax.put("urls", StringUtils.join(urls, FILE_DELIMETER));
            ajax.put("fileNames", StringUtils.join(fileNames, FILE_DELIMETER));
            ajax.put("newFileNames", StringUtils.join(newFileNames, FILE_DELIMETER));
            ajax.put("originalFilenames", StringUtils.join(originalFilenames, FILE_DELIMETER));
            return ajax;
        }
        catch (Exception e)
        {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 本地资源通用下载
     */
    @GetMapping("/download/resource")
    public void resourceDownload(String resource, HttpServletRequest request, HttpServletResponse response)
            throws Exception
    {
        try
        {
            if (!FileUtils.checkAllowDownload(resource))
            {
                throw new Exception(StringUtils.format("资源文件({})非法，不允许下载。 ", resource));
            }
            // 本地资源路径
            String localPath = AppConfig.getProfile();
            // 数据库资源地址
            String downloadPath = localPath + StringUtils.substringAfter(resource, Constants.RESOURCE_PREFIX);
            // 下载名称
            String downloadName = StringUtils.substringAfterLast(downloadPath, "/");
            response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
            FileUtils.setAttachmentResponseHeader(response, downloadName);
            FileUtils.writeBytes(downloadPath, response.getOutputStream());
        }
        catch (Exception e)
        {
            log.error("下载文件失败", e);
        }
    }

    /**
     * 生成ID
     * @return
     */
    @GetMapping("/generateId")
    public AjaxResult generateId() {
        return AjaxResult.success("", SpringUtils.getBean(SnowflakeIdWorker.class).nextIdString());
    }

    /**
     * 获取上传进度
     */
    @GetMapping("/upload/progress")
    public AjaxResult getUploadProgress(@RequestParam("uploadId") String uploadId) {
        int totalProgress = UploadProgressPool.getTotalProgress(uploadId);
        String currentStage = UploadProgressPool.getCurrentStage(uploadId);

        AjaxResult ajax = AjaxResult.success();
        ajax.put("progress", totalProgress);
        ajax.put("stage", currentStage);
        return ajax;
    }
}
