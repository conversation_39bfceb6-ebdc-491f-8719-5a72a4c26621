package tv.shorthub.admin.controller.statistics;

import com.github.pagehelper.PageHelper;
import tv.shorthub.common.core.domain.SummaryRequest;
import tv.shorthub.common.utils.SecurityUtils;
import tv.shorthub.common.utils.SummaryColumnUtils;
import org.springframework.web.multipart.MultipartFile;
import java.util.ArrayList;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import tv.shorthub.common.annotation.Log;
import tv.shorthub.common.core.controller.BaseController;
import tv.shorthub.common.core.domain.AjaxResult;
import tv.shorthub.common.enums.BusinessType;
import tv.shorthub.system.domain.StatisticsDailyFunnelStats;
import tv.shorthub.system.mapper.AppPromotionMapper;
import tv.shorthub.system.service.IStatisticsDailyFunnelStatsService;
import tv.shorthub.common.utils.poi.ExcelUtil;
import tv.shorthub.common.core.page.TableDataInfo;
import tv.shorthub.statistics.service.IDailyFunnelStatsService;
import tv.shorthub.statistics.dto.BatchUpdateRequest;
import java.util.Date;
import org.springframework.web.bind.annotation.RequestParam;
import lombok.extern.slf4j.Slf4j;
import tv.shorthub.admin.utils.StatisticsControllerPermissionUtils;

/**
 * 每日漏斗分析统计Controller
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
@Slf4j
@RestController
@RequestMapping("/statistics/dayfunnel")
public class StatisticsDailyFunnelStatsController extends BaseController
{
    @Autowired
    private IStatisticsDailyFunnelStatsService statisticsDailyFunnelStatsService;

    @Autowired
    private IDailyFunnelStatsService dailyFunnelStatsService;

    @Autowired
    private StatisticsControllerPermissionUtils controllerPermissionUtils;


    /**
     * 查询每日漏斗分析统计列表
     */
    @PreAuthorize("@ss.hasPermi('statistics:dayfunnel:list')")
    @GetMapping("/list")
    public TableDataInfo list(StatisticsDailyFunnelStats statisticsDailyFunnelStats)
    {
        startPage();
        List<StatisticsDailyFunnelStats> list = statisticsDailyFunnelStatsService.selectList(statisticsDailyFunnelStats);
        return getDataTable(list);
    }

    /**
     * 获取每日漏斗分析统计数据汇总
     */
    @PreAuthorize("@ss.hasPermi('statistics:dayfunnel:query')")
    @GetMapping(value = "/getSummary")
    public AjaxResult getSummary(StatisticsDailyFunnelStats statisticsDailyFunnelStats)
    {
        return AjaxResult.success(statisticsDailyFunnelStatsService.getSummary(statisticsDailyFunnelStats));
    }

    /**
     * 导出每日漏斗分析统计列表
     */
    @PreAuthorize("@ss.hasPermi('statistics:dayfunnel:export')")
    @Log(title = "每日漏斗分析统计", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, StatisticsDailyFunnelStats statisticsDailyFunnelStats)
    {
        List<StatisticsDailyFunnelStats> list = statisticsDailyFunnelStatsService.selectList(statisticsDailyFunnelStats);
        ExcelUtil<StatisticsDailyFunnelStats> util = new ExcelUtil<>(StatisticsDailyFunnelStats.class);
        util.exportExcel(response, list, "每日漏斗分析统计数据");
    }

    /**
     * 获取每日漏斗分析统计详细信息
     */
    @PreAuthorize("@ss.hasPermi('statistics:dayfunnel:query')")
    @GetMapping(value = "/info/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(statisticsDailyFunnelStatsService.getById(id));
    }

    /**
     * 新增每日漏斗分析统计
     */
    @PreAuthorize("@ss.hasPermi('statistics:dayfunnel:add')")
    @Log(title = "每日漏斗分析统计", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody StatisticsDailyFunnelStats statisticsDailyFunnelStats)
    {
        return toAjax(statisticsDailyFunnelStatsService.insert(statisticsDailyFunnelStats));
    }

    /**
     * 修改每日漏斗分析统计
     */
    @PreAuthorize("@ss.hasPermi('statistics:dayfunnel:edit')")
    @Log(title = "每日漏斗分析统计", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody StatisticsDailyFunnelStats statisticsDailyFunnelStats)
    {
        return toAjax(statisticsDailyFunnelStatsService.update(statisticsDailyFunnelStats));
    }

    /**
     * 删除每日漏斗分析统计
     */
    @PreAuthorize("@ss.hasPermi('statistics:dayfunnel:remove')")
    @Log(title = "每日漏斗分析统计", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable List<Long> ids)
    {
        return toAjax(statisticsDailyFunnelStatsService.deleteByIds(ids));
    }


    /**
     * 查询自定义分析列
     */
    @PreAuthorize("@ss.hasPermi('statistics:dayfunnel:list')")
    @GetMapping("/columns")
    public AjaxResult columns()
    {
        return AjaxResult.success(SummaryColumnUtils.get(StatisticsDailyFunnelStats.class));
    }

    /**
     * 查询自定义分析数据
     */
    @PreAuthorize("@ss.hasPermi('statistics:dayfunnel:list')")
    @RequestMapping(value = "/summary")
    public TableDataInfo summary(@RequestBody SummaryRequest query)
    {
        startPage();
        List<StatisticsDailyFunnelStats> summary = statisticsDailyFunnelStatsService.summary(query);
        return getDataTable(summary);
    }

    /**
     * 导出自定义分析数据
     */
    @PreAuthorize("@ss.hasPermi('statistics:dayfunnel:export')")
    @Log(title = "导出自定义分析数据", businessType = BusinessType.EXPORT)
    @PostMapping("/summaryExport")
    public void summaryExport(HttpServletResponse response, @RequestBody SummaryRequest query)
    {
        PageHelper.startPage(1, Integer.MAX_VALUE);
        if (null == query.getQuery()) {
            query.setQuery(new ArrayList<>());
        }
        List<StatisticsDailyFunnelStats> list = statisticsDailyFunnelStatsService.summary(query);
        query.getGroupBy().addAll(query.getColumns());
        ExcelUtil<StatisticsDailyFunnelStats> util = new ExcelUtil<>(StatisticsDailyFunnelStats.class);
        util.exportExcel(response, list, "导出数据");
    }



    /**
     * 下载导入模板
     */
    @PreAuthorize("@ss.hasPermi('statistics:dayfunnel:export')")
    @Log(title = "下载导入模板", businessType = BusinessType.EXPORT)
    @PostMapping("/downloadImportModule")
    public void downloadImportModule()
    {
        statisticsDailyFunnelStatsService.downloadImportModule();
    }

    /**
     * 导入数据
     */
    @PreAuthorize("@ss.hasPermi('statistics:dayfunnel:export')")
    @Log(title = "导入数据", businessType = BusinessType.EXPORT)
    @PostMapping("/importData")
    public void importData(MultipartFile file)
    {

        statisticsDailyFunnelStatsService.importData(file);
    }


    /**
     * 查询自定义分析数据-汇总
     */
    @PreAuthorize("@ss.hasPermi('statistics:dayfunnel:list')")
    @RequestMapping(value = "/allSummary")
    public AjaxResult allSummary(@RequestBody SummaryRequest query)
    {
        PageHelper.startPage(1, 1);
        return AjaxResult.success(statisticsDailyFunnelStatsService.allSummary(query));
    }

    /**
     * 获取每日漏斗分析统计汇总数据
     */
    @PreAuthorize("@ss.hasPermi('statistics:dayfunnel:query')")
    @GetMapping("/getRangeSummary")
    public AjaxResult getRangeSummary(@RequestParam("startTime") Date startTime,
                                      @RequestParam("endTime") Date endTime,
                                      @RequestParam(value = "appid", required = false) String appid,
                                      @RequestParam(value = "tfid", required = false) String tfid,
                                      @RequestParam(value = "delivererName", required = false) String delivererName,
                                      @RequestParam(value = "timezone", required = false, defaultValue = "UTC+8") String timezone) {
        log.info("[天漏斗统计] 区间汇总查询参数 - startTime: {}, endTime: {}, appid: {}, tfid: {}, delivererName: {}, timezone: {}", startTime, endTime, appid, tfid, delivererName, timezone);

        return AjaxResult.success(statisticsDailyFunnelStatsService.getRangeSummary(startTime, endTime, appid, tfid, timezone));
    }

    /**
     * 全量统计最近N天的漏斗分析数据
     */
    @PreAuthorize("@ss.hasPermi('statistics:dayfunnel:edit')")
    @Log(title = "全量统计每日漏斗分析数据", businessType = BusinessType.UPDATE)
    @PostMapping("/batchUpdate")
    public AjaxResult batchUpdateRecentStats(@RequestBody BatchUpdateRequest request)
    {
        try {
            Integer days = request.getDays();

            // 默认统计最近7天
            if (days == null || days <= 0) {
                days = 7;
            }

            // 限制最多统计365天
            if (days > 365) {
                days = 365;
            }

            String result = dailyFunnelStatsService.batchUpdateRecentFunnelStats(days);
            return AjaxResult.success(result);
        } catch (Exception e) {
            return AjaxResult.error("全量统计失败: " + e.getMessage());
        }
    }

    /**
     * 全量统计指定时间范围的漏斗分析数据
     */
    @PreAuthorize("@ss.hasPermi('statistics:dayfunnel:edit')")
    @Log(title = "全量统计每日漏斗分析数据", businessType = BusinessType.UPDATE)
    @PostMapping("/batchUpdateRange")
    public AjaxResult batchUpdateRangeStats(@RequestBody BatchUpdateRequest request)
    {
        try {
            if (!request.isValidDateRange()) {
                return AjaxResult.error("日期范围参数无效");
            }

            String result = dailyFunnelStatsService.batchUpdateFunnelStats(request.getStartDate(), request.getEndDate());
            return AjaxResult.success(result);
        } catch (Exception e) {
            return AjaxResult.error("全量统计失败: " + e.getMessage());
        }
    }
}
