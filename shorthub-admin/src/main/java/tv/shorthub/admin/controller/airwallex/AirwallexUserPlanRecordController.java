package tv.shorthub.admin.controller.airwallex;

import com.github.pagehelper.PageHelper;
import tv.shorthub.common.core.domain.SummaryRequest;
import tv.shorthub.common.utils.SummaryColumnUtils;
import org.springframework.web.multipart.MultipartFile;
import java.util.ArrayList;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import tv.shorthub.common.annotation.Log;
import tv.shorthub.common.core.controller.BaseController;
import tv.shorthub.common.core.domain.AjaxResult;
import tv.shorthub.common.enums.BusinessType;
import tv.shorthub.system.domain.AirwallexUserPlanRecord;
import tv.shorthub.system.service.IAirwallexUserPlanRecordService;
import tv.shorthub.common.utils.poi.ExcelUtil;
import tv.shorthub.common.core.page.TableDataInfo;

/**
 * 签约续订记录Controller
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
@RestController
@RequestMapping("/pay/airwallex/planrecord")
public class AirwallexUserPlanRecordController extends BaseController
{
    @Autowired
    private IAirwallexUserPlanRecordService airwallexUserPlanRecordService;

    /**
     * 查询签约续订记录列表
     */
    @PreAuthorize("@ss.hasPermi('pay/airwallex:planrecord:list')")
    @GetMapping("/list")
    public TableDataInfo list(AirwallexUserPlanRecord airwallexUserPlanRecord)
    {
        startPage();
        List<AirwallexUserPlanRecord> list = airwallexUserPlanRecordService.selectList(airwallexUserPlanRecord);
        return getDataTable(list);
    }

    /**
     * 获取签约续订记录数据汇总
     */
    @PreAuthorize("@ss.hasPermi('pay/airwallex:planrecord:query')")
    @GetMapping(value = "/getSummary")
    public AjaxResult getSummary(AirwallexUserPlanRecord airwallexUserPlanRecord)
    {
        return AjaxResult.success(airwallexUserPlanRecordService.getSummary(airwallexUserPlanRecord));
    }

    /**
     * 导出签约续订记录列表
     */
    @PreAuthorize("@ss.hasPermi('pay/airwallex:planrecord:export')")
    @Log(title = "签约续订记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AirwallexUserPlanRecord airwallexUserPlanRecord)
    {
        List<AirwallexUserPlanRecord> list = airwallexUserPlanRecordService.selectList(airwallexUserPlanRecord);
        ExcelUtil<AirwallexUserPlanRecord> util = new ExcelUtil<>(AirwallexUserPlanRecord.class);
        util.exportExcel(response, list, "签约续订记录数据");
    }

    /**
     * 获取签约续订记录详细信息
     */
    @PreAuthorize("@ss.hasPermi('pay/airwallex:planrecord:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(airwallexUserPlanRecordService.getById(id));
    }

    /**
     * 新增签约续订记录
     */
    @PreAuthorize("@ss.hasPermi('pay/airwallex:planrecord:add')")
    @Log(title = "签约续订记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AirwallexUserPlanRecord airwallexUserPlanRecord)
    {
        return toAjax(airwallexUserPlanRecordService.insert(airwallexUserPlanRecord));
    }

    /**
     * 修改签约续订记录
     */
    @PreAuthorize("@ss.hasPermi('pay/airwallex:planrecord:edit')")
    @Log(title = "签约续订记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AirwallexUserPlanRecord airwallexUserPlanRecord)
    {
        return toAjax(airwallexUserPlanRecordService.update(airwallexUserPlanRecord));
    }

    /**
     * 删除签约续订记录
     */
    @PreAuthorize("@ss.hasPermi('pay/airwallex:planrecord:remove')")
    @Log(title = "签约续订记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable List<Long> ids)
    {
        return toAjax(airwallexUserPlanRecordService.deleteByIds(ids));
    }


    /**
     * 查询自定义分析列
     */
    @PreAuthorize("@ss.hasPermi('pay/airwallex:planrecord:list')")
    @GetMapping("/columns")
    public AjaxResult columns()
    {
        return AjaxResult.success(SummaryColumnUtils.get(AirwallexUserPlanRecord.class));
    }

    /**
     * 查询自定义分析数据
     */
    @PreAuthorize("@ss.hasPermi('pay/airwallex:planrecord:list')")
    @RequestMapping(value = "/summary")
    public TableDataInfo summary(@RequestBody SummaryRequest query)
    {
        startPage();
        List<AirwallexUserPlanRecord> summary = airwallexUserPlanRecordService.summary(query);
        return getDataTable(summary);
    }

    /**
     * 导出自定义分析数据
     */
    @PreAuthorize("@ss.hasPermi('pay/airwallex:planrecord:export')")
    @Log(title = "导出自定义分析数据", businessType = BusinessType.EXPORT)
    @PostMapping("/summaryExport")
    public void summaryExport(HttpServletResponse response, @RequestBody SummaryRequest query)
    {
        PageHelper.startPage(1, Integer.MAX_VALUE);
        if (null == query.getQuery()) {
            query.setQuery(new ArrayList<>());
        }
        List<AirwallexUserPlanRecord> list = airwallexUserPlanRecordService.summary(query);
        query.getGroupBy().addAll(query.getColumns());
        ExcelUtil<AirwallexUserPlanRecord> util = new ExcelUtil<>(AirwallexUserPlanRecord.class);
        util.exportExcel(response, list, "导出数据");
    }



    /**
     * 下载导入模板
     */
    @PreAuthorize("@ss.hasPermi('pay/airwallex:planrecord:export')")
    @Log(title = "下载导入模板", businessType = BusinessType.EXPORT)
    @PostMapping("/downloadImportModule")
    public void downloadImportModule()
    {
        airwallexUserPlanRecordService.downloadImportModule();
    }

    /**
     * 导入数据
     */
    @PreAuthorize("@ss.hasPermi('pay/airwallex:planrecord:export')")
    @Log(title = "导入数据", businessType = BusinessType.EXPORT)
    @PostMapping("/importData")
    public void importData(MultipartFile file)
    {

        airwallexUserPlanRecordService.importData(file);
    }


    /**
     * 查询自定义分析数据-汇总
     */
    @PreAuthorize("@ss.hasPermi('pay/airwallex:planrecord:list')")
    @RequestMapping(value = "/allSummary")
    public AjaxResult allSummary(@RequestBody SummaryRequest query)
    {
        PageHelper.startPage(1, 1);
        return AjaxResult.success(airwallexUserPlanRecordService.allSummary(query));
    }
}
