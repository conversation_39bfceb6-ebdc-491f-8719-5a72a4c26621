package tv.shorthub.admin.controller.payermax;

import com.github.pagehelper.PageHelper;
import tv.shorthub.common.core.domain.SummaryRequest;
import tv.shorthub.common.utils.SummaryColumnUtils;
import org.springframework.web.multipart.MultipartFile;
import java.util.ArrayList;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import tv.shorthub.common.annotation.Log;
import tv.shorthub.common.core.controller.BaseController;
import tv.shorthub.common.core.domain.AjaxResult;
import tv.shorthub.common.enums.BusinessType;
import tv.shorthub.system.domain.PayermaxPaymentLog;
import tv.shorthub.system.service.IPayermaxPaymentLogService;
import tv.shorthub.common.utils.poi.ExcelUtil;
import tv.shorthub.common.core.page.TableDataInfo;

/**
 * payermax支付日志Controller
 *
 * <AUTHOR>
 * @date 2025-06-16
 */
@RestController
@RequestMapping("/payermax/log")
public class PayermaxPaymentLogController extends BaseController
{
    @Autowired
    private IPayermaxPaymentLogService payermaxPaymentLogService;

    /**
     * 查询payermax支付日志列表
     */
    @PreAuthorize("@ss.hasPermi('payermax:log:list')")
    @GetMapping("/list")
    public TableDataInfo list(PayermaxPaymentLog payermaxPaymentLog)
    {
        startPage();
        List<PayermaxPaymentLog> list = payermaxPaymentLogService.selectList(payermaxPaymentLog);
        return getDataTable(list);
    }

    /**
     * 获取payermax支付日志数据汇总
     */
    @PreAuthorize("@ss.hasPermi('payermax:log:query')")
    @GetMapping(value = "/getSummary")
    public AjaxResult getSummary(PayermaxPaymentLog payermaxPaymentLog)
    {
        return AjaxResult.success(payermaxPaymentLogService.getSummary(payermaxPaymentLog));
    }

    /**
     * 导出payermax支付日志列表
     */
    @PreAuthorize("@ss.hasPermi('payermax:log:export')")
    @Log(title = "payermax支付日志", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PayermaxPaymentLog payermaxPaymentLog)
    {
        List<PayermaxPaymentLog> list = payermaxPaymentLogService.selectList(payermaxPaymentLog);
        ExcelUtil<PayermaxPaymentLog> util = new ExcelUtil<>(PayermaxPaymentLog.class);
        util.exportExcel(response, list, "payermax支付日志数据");
    }

    /**
     * 获取payermax支付日志详细信息
     */
    @PreAuthorize("@ss.hasPermi('payermax:log:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(payermaxPaymentLogService.getById(id));
    }

    /**
     * 新增payermax支付日志
     */
    @PreAuthorize("@ss.hasPermi('payermax:log:add')")
    @Log(title = "payermax支付日志", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PayermaxPaymentLog payermaxPaymentLog)
    {
        return toAjax(payermaxPaymentLogService.insert(payermaxPaymentLog));
    }

    /**
     * 修改payermax支付日志
     */
    @PreAuthorize("@ss.hasPermi('payermax:log:edit')")
    @Log(title = "payermax支付日志", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PayermaxPaymentLog payermaxPaymentLog)
    {
        return toAjax(payermaxPaymentLogService.update(payermaxPaymentLog));
    }

    /**
     * 删除payermax支付日志
     */
    @PreAuthorize("@ss.hasPermi('payermax:log:remove')")
    @Log(title = "payermax支付日志", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable List<Long> ids)
    {
        return toAjax(payermaxPaymentLogService.deleteByIds(ids));
    }


    /**
     * 查询自定义分析列
     */
    @PreAuthorize("@ss.hasPermi('payermax:log:list')")
    @GetMapping("/columns")
    public AjaxResult columns()
    {
        return AjaxResult.success(SummaryColumnUtils.get(PayermaxPaymentLog.class));
    }

    /**
     * 查询自定义分析数据
     */
    @PreAuthorize("@ss.hasPermi('payermax:log:list')")
    @RequestMapping(value = "/summary")
    public TableDataInfo summary(@RequestBody SummaryRequest query)
    {
        startPage();
        List<PayermaxPaymentLog> summary = payermaxPaymentLogService.summary(query);
        return getDataTable(summary);
    }

    /**
     * 导出自定义分析数据
     */
    @PreAuthorize("@ss.hasPermi('payermax:log:export')")
    @Log(title = "导出自定义分析数据", businessType = BusinessType.EXPORT)
    @PostMapping("/summaryExport")
    public void summaryExport(HttpServletResponse response, @RequestBody SummaryRequest query)
    {
        PageHelper.startPage(1, Integer.MAX_VALUE);
        if (null == query.getQuery()) {
            query.setQuery(new ArrayList<>());
        }
        List<PayermaxPaymentLog> list = payermaxPaymentLogService.summary(query);
        query.getGroupBy().addAll(query.getColumns());
        ExcelUtil<PayermaxPaymentLog> util = new ExcelUtil<>(PayermaxPaymentLog.class);
        util.exportExcel(response, list, "导出数据");
    }



    /**
     * 下载导入模板
     */
    @PreAuthorize("@ss.hasPermi('payermax:log:export')")
    @Log(title = "下载导入模板", businessType = BusinessType.EXPORT)
    @PostMapping("/downloadImportModule")
    public void downloadImportModule()
    {
        payermaxPaymentLogService.downloadImportModule();
    }

    /**
     * 导入数据
     */
    @PreAuthorize("@ss.hasPermi('payermax:log:export')")
    @Log(title = "导入数据", businessType = BusinessType.EXPORT)
    @PostMapping("/importData")
    public void importData(MultipartFile file)
    {

        payermaxPaymentLogService.importData(file);
    }


    /**
     * 查询自定义分析数据-汇总
     */
    @PreAuthorize("@ss.hasPermi('payermax:log:list')")
    @RequestMapping(value = "/allSummary")
    public AjaxResult allSummary(@RequestBody SummaryRequest query)
    {
        PageHelper.startPage(1, 1);
        return AjaxResult.success(payermaxPaymentLogService.allSummary(query));
    }
}
