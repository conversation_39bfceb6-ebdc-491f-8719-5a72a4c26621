package tv.shorthub.admin.controller.ad;

import com.alibaba.fastjson2.JSONObject;
import com.github.pagehelper.PageHelper;
import tv.shorthub.ad.config.EventTypeEnum;
import tv.shorthub.ad.config.FacebookEventType;
import tv.shorthub.common.core.domain.SummaryRequest;
import tv.shorthub.common.enums.AdPlatformEnum;
import tv.shorthub.common.exception.ApiException;
import tv.shorthub.common.utils.SummaryColumnUtils;
import org.springframework.web.multipart.MultipartFile;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import tv.shorthub.common.annotation.Log;
import tv.shorthub.common.core.controller.BaseController;
import tv.shorthub.common.core.domain.AjaxResult;
import tv.shorthub.common.enums.BusinessType;
import tv.shorthub.system.domain.AdRetargetingEvent;
import tv.shorthub.system.service.IAdRetargetingEventService;
import tv.shorthub.common.utils.poi.ExcelUtil;
import tv.shorthub.common.core.page.TableDataInfo;

/**
 * 回传事件Controller
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@RestController
@RequestMapping("/ad/retargeting/event")
public class AdRetargetingEventController extends BaseController
{
    @Autowired
    private IAdRetargetingEventService adRetargetingEventService;

    /**
     * 查询回传事件列表
     */
    @PreAuthorize("@ss.hasPermi('ad/retargeting:event:list')")
    @GetMapping("/list")
    public TableDataInfo list(AdRetargetingEvent adRetargetingEvent)
    {
        startPage();
        List<AdRetargetingEvent> list = adRetargetingEventService.selectList(adRetargetingEvent);
        return getDataTable(list);
    }

    /**
     * 获取回传事件数据汇总
     */
    @PreAuthorize("@ss.hasPermi('ad/retargeting:event:query')")
    @GetMapping(value = "/getSummary")
    public AjaxResult getSummary(AdRetargetingEvent adRetargetingEvent)
    {
        return AjaxResult.success(adRetargetingEventService.getSummary(adRetargetingEvent));
    }

    /**
     * 导出回传事件列表
     */
    @PreAuthorize("@ss.hasPermi('ad/retargeting:event:export')")
    @Log(title = "回传事件", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AdRetargetingEvent adRetargetingEvent)
    {
        List<AdRetargetingEvent> list = adRetargetingEventService.selectList(adRetargetingEvent);
        ExcelUtil<AdRetargetingEvent> util = new ExcelUtil<>(AdRetargetingEvent.class);
        util.exportExcel(response, list, "回传事件数据");
    }

    /**
     * 获取回传事件详细信息
     */
    @PreAuthorize("@ss.hasPermi('ad/retargeting:event:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(adRetargetingEventService.getById(id));
    }

    /**
     * 新增回传事件
     */
    @PreAuthorize("@ss.hasPermi('ad/retargeting:event:add')")
    @Log(title = "回传事件", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AdRetargetingEvent adRetargetingEvent)
    {
        return toAjax(adRetargetingEventService.insert(adRetargetingEvent));
    }

    /**
     * 修改回传事件
     */
    @PreAuthorize("@ss.hasPermi('ad/retargeting:event:edit')")
    @Log(title = "回传事件", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AdRetargetingEvent adRetargetingEvent)
    {
        return toAjax(adRetargetingEventService.update(adRetargetingEvent));
    }

    /**
     * 删除回传事件
     */
    @PreAuthorize("@ss.hasPermi('ad/retargeting:event:remove')")
    @Log(title = "回传事件", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable List<Long> ids)
    {
        throw new ApiException("暂不支持删除回传事件");
//        return toAjax(adRetargetingEventService.deleteByIds(ids));
    }


    /**
     * 查询自定义分析列
     */
    @PreAuthorize("@ss.hasPermi('ad/retargeting:event:list')")
    @GetMapping("/columns")
    public AjaxResult columns()
    {
        return AjaxResult.success(SummaryColumnUtils.get(AdRetargetingEvent.class));
    }

    /**
     * 查询自定义分析数据
     */
    @PreAuthorize("@ss.hasPermi('ad/retargeting:event:list')")
    @RequestMapping(value = "/summary")
    public TableDataInfo summary(@RequestBody SummaryRequest query)
    {
        startPage();
        List<AdRetargetingEvent> summary = adRetargetingEventService.summary(query);
        return getDataTable(summary);
    }

    /**
     * 导出自定义分析数据
     */
    @PreAuthorize("@ss.hasPermi('ad/retargeting:event:export')")
    @Log(title = "导出自定义分析数据", businessType = BusinessType.EXPORT)
    @PostMapping("/summaryExport")
    public void summaryExport(HttpServletResponse response, @RequestBody SummaryRequest query)
    {
        PageHelper.startPage(1, Integer.MAX_VALUE);
        if (null == query.getQuery()) {
            query.setQuery(new ArrayList<>());
        }
        List<AdRetargetingEvent> list = adRetargetingEventService.summary(query);
        query.getGroupBy().addAll(query.getColumns());
        ExcelUtil<AdRetargetingEvent> util = new ExcelUtil<>(AdRetargetingEvent.class);
        util.exportExcel(response, list, "导出数据");
    }



    /**
     * 下载导入模板
     */
    @PreAuthorize("@ss.hasPermi('ad/retargeting:event:export')")
    @Log(title = "下载导入模板", businessType = BusinessType.EXPORT)
    @PostMapping("/downloadImportModule")
    public void downloadImportModule()
    {
        adRetargetingEventService.downloadImportModule();
    }

    /**
     * 导入数据
     */
    @PreAuthorize("@ss.hasPermi('ad/retargeting:event:export')")
    @Log(title = "导入数据", businessType = BusinessType.EXPORT)
    @PostMapping("/importData")
    public void importData(MultipartFile file)
    {

        adRetargetingEventService.importData(file);
    }


    /**
     * 查询自定义分析数据-汇总
     */
    @PreAuthorize("@ss.hasPermi('ad/retargeting:event:list')")
    @RequestMapping(value = "/allSummary")
    public AjaxResult allSummary(@RequestBody SummaryRequest query)
    {
        PageHelper.startPage(1, 1);
        return AjaxResult.success(adRetargetingEventService.allSummary(query));
    }

    /**
     * 查询自定义分析数据-汇总
     */
    @PreAuthorize("@ss.hasPermi('ad/retargeting:event:list')")
    @RequestMapping(value = "/adEventType")
    public AjaxResult adEventType() {
        JSONObject adEventType = new JSONObject();
        adEventType.put(AdPlatformEnum.Facebook.getValue(), Arrays.stream(FacebookEventType.values()).map(event -> {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("value", event.getEventName());
            jsonObject.put("label", event.getDescription());
            return jsonObject;
        }).toList());
//        adEventType.put("google",  Arrays.stream(EventTypeEnum.values()).map(event -> {
//            JSONObject jsonObject = new JSONObject();
//            jsonObject.put("value", event.getEventName());
//            jsonObject.put("label", event.getDescription());
//            return jsonObject;
//        }).toList());
        return AjaxResult.success(adEventType);
    }
}
