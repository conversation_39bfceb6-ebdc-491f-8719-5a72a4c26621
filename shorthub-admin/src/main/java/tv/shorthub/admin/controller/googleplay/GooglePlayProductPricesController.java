package tv.shorthub.admin.controller.googleplay;

import com.github.pagehelper.PageHelper;
import tv.shorthub.common.core.domain.SummaryRequest;
import tv.shorthub.common.utils.SummaryColumnUtils;
import org.springframework.web.multipart.MultipartFile;
import java.util.ArrayList;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import tv.shorthub.common.annotation.Log;
import tv.shorthub.common.core.controller.BaseController;
import tv.shorthub.common.core.domain.AjaxResult;
import tv.shorthub.common.enums.BusinessType;
import tv.shorthub.system.domain.GooglePlayProductPrices;
import tv.shorthub.system.service.IGooglePlayProductPricesService;
import tv.shorthub.common.utils.poi.ExcelUtil;
import tv.shorthub.common.core.page.TableDataInfo;

/**
 * Google Play产品价格配置Controller
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
@RestController
@RequestMapping("/google/play/product/prices")
public class GooglePlayProductPricesController extends BaseController
{
    @Autowired
    private IGooglePlayProductPricesService googlePlayProductPricesService;

    /**
     * 查询Google Play产品价格配置列表
     */
    @PreAuthorize("@ss.hasPermi('google/play:product/prices:list')")
    @GetMapping("/list")
    public TableDataInfo list(GooglePlayProductPrices googlePlayProductPrices)
    {
        startPage();
        List<GooglePlayProductPrices> list = googlePlayProductPricesService.selectList(googlePlayProductPrices);
        return getDataTable(list);
    }

    /**
     * 获取Google Play产品价格配置数据汇总
     */
    @PreAuthorize("@ss.hasPermi('google/play:product/prices:query')")
    @GetMapping(value = "/getSummary")
    public AjaxResult getSummary(GooglePlayProductPrices googlePlayProductPrices)
    {
        return AjaxResult.success(googlePlayProductPricesService.getSummary(googlePlayProductPrices));
    }

    /**
     * 导出Google Play产品价格配置列表
     */
    @PreAuthorize("@ss.hasPermi('google/play:product/prices:export')")
    @Log(title = "Google Play产品价格配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, GooglePlayProductPrices googlePlayProductPrices)
    {
        List<GooglePlayProductPrices> list = googlePlayProductPricesService.selectList(googlePlayProductPrices);
        ExcelUtil<GooglePlayProductPrices> util = new ExcelUtil<>(GooglePlayProductPrices.class);
        util.exportExcel(response, list, "Google Play产品价格配置数据");
    }

    /**
     * 获取Google Play产品价格配置详细信息
     */
    @PreAuthorize("@ss.hasPermi('google/play:product/prices:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(googlePlayProductPricesService.getById(id));
    }

    /**
     * 新增Google Play产品价格配置
     */
    @PreAuthorize("@ss.hasPermi('google/play:product/prices:add')")
    @Log(title = "Google Play产品价格配置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody GooglePlayProductPrices googlePlayProductPrices)
    {
        return toAjax(googlePlayProductPricesService.insert(googlePlayProductPrices));
    }

    /**
     * 修改Google Play产品价格配置
     */
    @PreAuthorize("@ss.hasPermi('google/play:product/prices:edit')")
    @Log(title = "Google Play产品价格配置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody GooglePlayProductPrices googlePlayProductPrices)
    {
        return toAjax(googlePlayProductPricesService.update(googlePlayProductPrices));
    }

    /**
     * 删除Google Play产品价格配置
     */
    @PreAuthorize("@ss.hasPermi('google/play:product/prices:remove')")
    @Log(title = "Google Play产品价格配置", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable List<Long> ids)
    {
        return toAjax(googlePlayProductPricesService.deleteByIds(ids));
    }


    /**
     * 查询自定义分析列
     */
    @PreAuthorize("@ss.hasPermi('google/play:product/prices:list')")
    @GetMapping("/columns")
    public AjaxResult columns()
    {
        return AjaxResult.success(SummaryColumnUtils.get(GooglePlayProductPrices.class));
    }

    /**
     * 查询自定义分析数据
     */
    @PreAuthorize("@ss.hasPermi('google/play:product/prices:list')")
    @RequestMapping(value = "/summary")
    public TableDataInfo summary(@RequestBody SummaryRequest query)
    {
        startPage();
        List<GooglePlayProductPrices> summary = googlePlayProductPricesService.summary(query);
        return getDataTable(summary);
    }

    /**
     * 导出自定义分析数据
     */
    @PreAuthorize("@ss.hasPermi('google/play:product/prices:export')")
    @Log(title = "导出自定义分析数据", businessType = BusinessType.EXPORT)
    @PostMapping("/summaryExport")
    public void summaryExport(HttpServletResponse response, @RequestBody SummaryRequest query)
    {
        PageHelper.startPage(1, Integer.MAX_VALUE);
        if (null == query.getQuery()) {
            query.setQuery(new ArrayList<>());
        }
        List<GooglePlayProductPrices> list = googlePlayProductPricesService.summary(query);
        query.getGroupBy().addAll(query.getColumns());
        ExcelUtil<GooglePlayProductPrices> util = new ExcelUtil<>(GooglePlayProductPrices.class);
        util.exportExcel(response, list, "导出数据");
    }



    /**
     * 下载导入模板
     */
    @PreAuthorize("@ss.hasPermi('google/play:product/prices:export')")
    @Log(title = "下载导入模板", businessType = BusinessType.EXPORT)
    @PostMapping("/downloadImportModule")
    public void downloadImportModule()
    {
        googlePlayProductPricesService.downloadImportModule();
    }

    /**
     * 导入数据
     */
    @PreAuthorize("@ss.hasPermi('google/play:product/prices:export')")
    @Log(title = "导入数据", businessType = BusinessType.EXPORT)
    @PostMapping("/importData")
    public void importData(MultipartFile file)
    {

        googlePlayProductPricesService.importData(file);
    }


    /**
     * 查询自定义分析数据-汇总
     */
    @PreAuthorize("@ss.hasPermi('google/play:product/prices:list')")
    @RequestMapping(value = "/allSummary")
    public AjaxResult allSummary(@RequestBody SummaryRequest query)
    {
        PageHelper.startPage(1, 1);
        return AjaxResult.success(googlePlayProductPricesService.allSummary(query));
    }
}
