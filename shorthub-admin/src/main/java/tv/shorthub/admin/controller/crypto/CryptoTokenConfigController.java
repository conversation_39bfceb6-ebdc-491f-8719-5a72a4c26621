package tv.shorthub.admin.controller.crypto;

import com.github.pagehelper.PageHelper;
import tv.shorthub.common.core.domain.SummaryRequest;
import tv.shorthub.common.utils.SummaryColumnUtils;
import org.springframework.web.multipart.MultipartFile;
import java.util.ArrayList;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import tv.shorthub.common.annotation.Log;
import tv.shorthub.common.core.controller.BaseController;
import tv.shorthub.common.core.domain.AjaxResult;
import tv.shorthub.common.enums.BusinessType;
import tv.shorthub.system.domain.CryptoTokenConfig;
import tv.shorthub.system.service.ICryptoTokenConfigService;
import tv.shorthub.common.utils.poi.ExcelUtil;
import tv.shorthub.common.core.page.TableDataInfo;

/**
 * 虚拟货币代币配置Controller
 *
 * <AUTHOR>
 * @date 2025-08-05
 */
@RestController
@RequestMapping("/crypto/tokenconfig")
public class CryptoTokenConfigController extends BaseController
{
    @Autowired
    private ICryptoTokenConfigService cryptoTokenConfigService;

    /**
     * 查询虚拟货币代币配置列表
     */
    @PreAuthorize("@ss.hasPermi('crypto:tokenconfig:list')")
    @GetMapping("/list")
    public TableDataInfo list(CryptoTokenConfig cryptoTokenConfig)
    {
        startPage();
        List<CryptoTokenConfig> list = cryptoTokenConfigService.selectList(cryptoTokenConfig);
        return getDataTable(list);
    }

    /**
     * 获取虚拟货币代币配置数据汇总
     */
    @PreAuthorize("@ss.hasPermi('crypto:tokenconfig:query')")
    @GetMapping(value = "/getSummary")
    public AjaxResult getSummary(CryptoTokenConfig cryptoTokenConfig)
    {
        return AjaxResult.success(cryptoTokenConfigService.getSummary(cryptoTokenConfig));
    }

    /**
     * 导出虚拟货币代币配置列表
     */
    @PreAuthorize("@ss.hasPermi('crypto:tokenconfig:export')")
    @Log(title = "虚拟货币代币配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CryptoTokenConfig cryptoTokenConfig)
    {
        List<CryptoTokenConfig> list = cryptoTokenConfigService.selectList(cryptoTokenConfig);
        ExcelUtil<CryptoTokenConfig> util = new ExcelUtil<>(CryptoTokenConfig.class);
        util.exportExcel(response, list, "虚拟货币代币配置数据");
    }

    /**
     * 获取虚拟货币代币配置详细信息
     */
    @PreAuthorize("@ss.hasPermi('crypto:tokenconfig:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(cryptoTokenConfigService.getById(id));
    }

    /**
     * 新增虚拟货币代币配置
     */
    @PreAuthorize("@ss.hasPermi('crypto:tokenconfig:add')")
    @Log(title = "虚拟货币代币配置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CryptoTokenConfig cryptoTokenConfig)
    {
        return toAjax(cryptoTokenConfigService.insert(cryptoTokenConfig));
    }

    /**
     * 修改虚拟货币代币配置
     */
    @PreAuthorize("@ss.hasPermi('crypto:tokenconfig:edit')")
    @Log(title = "虚拟货币代币配置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CryptoTokenConfig cryptoTokenConfig)
    {
        return toAjax(cryptoTokenConfigService.update(cryptoTokenConfig));
    }

    /**
     * 删除虚拟货币代币配置
     */
    @PreAuthorize("@ss.hasPermi('crypto:tokenconfig:remove')")
    @Log(title = "虚拟货币代币配置", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable List<Long> ids)
    {
        return toAjax(cryptoTokenConfigService.deleteByIds(ids));
    }


    /**
     * 查询自定义分析列
     */
    @PreAuthorize("@ss.hasPermi('crypto:tokenconfig:list')")
    @GetMapping("/columns")
    public AjaxResult columns()
    {
        return AjaxResult.success(SummaryColumnUtils.get(CryptoTokenConfig.class));
    }

    /**
     * 查询自定义分析数据
     */
    @PreAuthorize("@ss.hasPermi('crypto:tokenconfig:list')")
    @RequestMapping(value = "/summary")
    public TableDataInfo summary(@RequestBody SummaryRequest query)
    {
        startPage();
        List<CryptoTokenConfig> summary = cryptoTokenConfigService.summary(query);
        return getDataTable(summary);
    }

    /**
     * 导出自定义分析数据
     */
    @PreAuthorize("@ss.hasPermi('crypto:tokenconfig:export')")
    @Log(title = "导出自定义分析数据", businessType = BusinessType.EXPORT)
    @PostMapping("/summaryExport")
    public void summaryExport(HttpServletResponse response, @RequestBody SummaryRequest query)
    {
        PageHelper.startPage(1, Integer.MAX_VALUE);
        if (null == query.getQuery()) {
            query.setQuery(new ArrayList<>());
        }
        List<CryptoTokenConfig> list = cryptoTokenConfigService.summary(query);
        query.getGroupBy().addAll(query.getColumns());
        ExcelUtil<CryptoTokenConfig> util = new ExcelUtil<>(CryptoTokenConfig.class);
        util.exportExcel(response, list, "导出数据");
    }



    /**
     * 下载导入模板
     */
    @PreAuthorize("@ss.hasPermi('crypto:tokenconfig:export')")
    @Log(title = "下载导入模板", businessType = BusinessType.EXPORT)
    @PostMapping("/downloadImportModule")
    public void downloadImportModule()
    {
        cryptoTokenConfigService.downloadImportModule();
    }

    /**
     * 导入数据
     */
    @PreAuthorize("@ss.hasPermi('crypto:tokenconfig:export')")
    @Log(title = "导入数据", businessType = BusinessType.EXPORT)
    @PostMapping("/importData")
    public void importData(MultipartFile file)
    {

        cryptoTokenConfigService.importData(file);
    }


    /**
     * 查询自定义分析数据-汇总
     */
    @PreAuthorize("@ss.hasPermi('crypto:tokenconfig:list')")
    @RequestMapping(value = "/allSummary")
    public AjaxResult allSummary(@RequestBody SummaryRequest query)
    {
        PageHelper.startPage(1, 1);
        return AjaxResult.success(cryptoTokenConfigService.allSummary(query));
    }
}
