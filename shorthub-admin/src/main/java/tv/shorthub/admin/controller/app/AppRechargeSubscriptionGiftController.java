package tv.shorthub.admin.controller.app;

import com.github.pagehelper.PageHelper;
import tv.shorthub.common.core.domain.SummaryRequest;
import tv.shorthub.common.utils.SummaryColumnUtils;
import org.springframework.web.multipart.MultipartFile;
import java.util.ArrayList;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import tv.shorthub.common.annotation.Log;
import tv.shorthub.common.core.controller.BaseController;
import tv.shorthub.common.core.domain.AjaxResult;
import tv.shorthub.common.enums.BusinessType;
import tv.shorthub.system.domain.AppRechargeSubscriptionGift;
import tv.shorthub.system.service.IAppRechargeSubscriptionGiftService;
import tv.shorthub.common.utils.poi.ExcelUtil;
import tv.shorthub.common.core.page.TableDataInfo;

/**
 * 订阅优惠规则Controller
 *
 * <AUTHOR>
 * @date 2025-05-14
 */
@RestController
@RequestMapping("/app/recharge/subscriptiongift")
public class AppRechargeSubscriptionGiftController extends BaseController
{
    @Autowired
    private IAppRechargeSubscriptionGiftService appRechargeSubscriptionGiftService;

    /**
     * 查询订阅优惠规则列表
     */
    @PreAuthorize("@ss.hasPermi('app/recharge:subscriptiongift:list')")
    @GetMapping("/list")
    public TableDataInfo list(AppRechargeSubscriptionGift appRechargeSubscriptionGift)
    {
        startPage();
        List<AppRechargeSubscriptionGift> list = appRechargeSubscriptionGiftService.selectList(appRechargeSubscriptionGift);
        return getDataTable(list);
    }

    /**
     * 获取订阅优惠规则数据汇总
     */
    @PreAuthorize("@ss.hasPermi('app/recharge:subscriptiongift:query')")
    @GetMapping(value = "/getSummary")
    public AjaxResult getSummary(AppRechargeSubscriptionGift appRechargeSubscriptionGift)
    {
        return AjaxResult.success(appRechargeSubscriptionGiftService.getSummary(appRechargeSubscriptionGift));
    }

    /**
     * 导出订阅优惠规则列表
     */
    @PreAuthorize("@ss.hasPermi('app/recharge:subscriptiongift:export')")
    @Log(title = "订阅优惠规则", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AppRechargeSubscriptionGift appRechargeSubscriptionGift)
    {
        List<AppRechargeSubscriptionGift> list = appRechargeSubscriptionGiftService.selectList(appRechargeSubscriptionGift);
        ExcelUtil<AppRechargeSubscriptionGift> util = new ExcelUtil<>(AppRechargeSubscriptionGift.class);
        util.exportExcel(response, list, "订阅优惠规则数据");
    }

    /**
     * 获取订阅优惠规则详细信息
     */
    @PreAuthorize("@ss.hasPermi('app/recharge:subscriptiongift:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(appRechargeSubscriptionGiftService.getById(id));
    }

    /**
     * 新增订阅优惠规则
     */
    @PreAuthorize("@ss.hasPermi('app/recharge:subscriptiongift:add')")
    @Log(title = "订阅优惠规则", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AppRechargeSubscriptionGift appRechargeSubscriptionGift)
    {
        return toAjax(appRechargeSubscriptionGiftService.insert(appRechargeSubscriptionGift));
    }

    /**
     * 修改订阅优惠规则
     */
    @PreAuthorize("@ss.hasPermi('app/recharge:subscriptiongift:edit')")
    @Log(title = "订阅优惠规则", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AppRechargeSubscriptionGift appRechargeSubscriptionGift)
    {
        return toAjax(appRechargeSubscriptionGiftService.update(appRechargeSubscriptionGift));
    }

    /**
     * 删除订阅优惠规则
     */
    @PreAuthorize("@ss.hasPermi('app/recharge:subscriptiongift:remove')")
    @Log(title = "订阅优惠规则", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable List<Long> ids)
    {
        return toAjax(appRechargeSubscriptionGiftService.deleteByIds(ids));
    }


    /**
     * 查询自定义分析列
     */
    @PreAuthorize("@ss.hasPermi('app/recharge:subscriptiongift:list')")
    @GetMapping("/columns")
    public AjaxResult columns()
    {
        return AjaxResult.success(SummaryColumnUtils.get(AppRechargeSubscriptionGift.class));
    }

    /**
     * 查询自定义分析数据
     */
    @PreAuthorize("@ss.hasPermi('app/recharge:subscriptiongift:list')")
    @RequestMapping(value = "/summary")
    public TableDataInfo summary(@RequestBody SummaryRequest query)
    {
        startPage();
        List<AppRechargeSubscriptionGift> summary = appRechargeSubscriptionGiftService.summary(query);
        return getDataTable(summary);
    }

    /**
     * 导出自定义分析数据
     */
    @PreAuthorize("@ss.hasPermi('app/recharge:subscriptiongift:export')")
    @Log(title = "导出自定义分析数据", businessType = BusinessType.EXPORT)
    @PostMapping("/summaryExport")
    public void summaryExport(HttpServletResponse response, @RequestBody SummaryRequest query)
    {
        PageHelper.startPage(1, Integer.MAX_VALUE);
        if (null == query.getQuery()) {
            query.setQuery(new ArrayList<>());
        }
        List<AppRechargeSubscriptionGift> list = appRechargeSubscriptionGiftService.summary(query);
        query.getGroupBy().addAll(query.getColumns());
        ExcelUtil<AppRechargeSubscriptionGift> util = new ExcelUtil<>(AppRechargeSubscriptionGift.class);
        util.exportExcel(response, list, "导出数据");
    }



    /**
     * 下载导入模板
     */
    @PreAuthorize("@ss.hasPermi('app/recharge:subscriptiongift:export')")
    @Log(title = "下载导入模板", businessType = BusinessType.EXPORT)
    @PostMapping("/downloadImportModule")
    public void downloadImportModule()
    {
        appRechargeSubscriptionGiftService.downloadImportModule();
    }

    /**
     * 导入数据
     */
    @PreAuthorize("@ss.hasPermi('app/recharge:subscriptiongift:export')")
    @Log(title = "导入数据", businessType = BusinessType.EXPORT)
    @PostMapping("/importData")
    public void importData(MultipartFile file)
    {

        appRechargeSubscriptionGiftService.importData(file);
    }


    /**
     * 查询自定义分析数据-汇总
     */
    @PreAuthorize("@ss.hasPermi('app/recharge:subscriptiongift:list')")
    @RequestMapping(value = "/allSummary")
    public AjaxResult allSummary(@RequestBody SummaryRequest query)
    {
        PageHelper.startPage(1, 1);
        return AjaxResult.success(appRechargeSubscriptionGiftService.allSummary(query));
    }
}
