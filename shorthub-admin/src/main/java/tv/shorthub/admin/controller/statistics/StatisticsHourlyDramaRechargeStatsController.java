package tv.shorthub.admin.controller.statistics;

import com.github.pagehelper.PageHelper;
import tv.shorthub.common.core.domain.SummaryRequest;
import tv.shorthub.common.utils.SecurityUtils;
import tv.shorthub.common.utils.SummaryColumnUtils;
import org.springframework.web.multipart.MultipartFile;
import java.util.ArrayList;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import tv.shorthub.common.annotation.Log;
import tv.shorthub.common.core.controller.BaseController;
import tv.shorthub.common.core.domain.AjaxResult;
import tv.shorthub.common.enums.BusinessType;
import tv.shorthub.system.domain.StatisticsHourlyDramaRechargeStats;
import tv.shorthub.system.domain.dto.StatisticsHourlyDramaRechargeStatsDTO;
import tv.shorthub.system.domain.dto.StatisticsHourlyDramaRechargeStatsDetailDTO;
import tv.shorthub.system.dto.RangeSummaryRequest;
import tv.shorthub.system.service.IStatisticsHourlyDramaRechargeStatsService;
import tv.shorthub.statistics.service.IHourlyDramaRechargeStatsService;
import tv.shorthub.common.utils.poi.ExcelUtil;
import tv.shorthub.common.core.page.TableDataInfo;
import tv.shorthub.common.utils.DateUtils;
import tv.shorthub.statistics.dto.BatchUpdateRequest;
import tv.shorthub.admin.utils.StatisticsControllerPermissionUtils;
import org.springframework.web.bind.annotation.RequestParam;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;

/**
 * 小时级剧目充值统计Controller
 *
 * <AUTHOR>
 * @date 2025-07-15
 */
@Slf4j
@RestController
@RequestMapping("/statistics/dramarecharge")
public class StatisticsHourlyDramaRechargeStatsController extends BaseController
{
    @Autowired
    private IStatisticsHourlyDramaRechargeStatsService statisticsHourlyDramaRechargeStatsService;

    @Autowired
    private IHourlyDramaRechargeStatsService hourlyDramaRechargeStatsService;

    @Autowired
    private StatisticsControllerPermissionUtils controllerPermissionUtils;

    /**
     * 查询小时级剧目充值统计列表
     */
    @PreAuthorize("@ss.hasPermi('statistics:dramarecharge:list')")
    @GetMapping("/list")
    public TableDataInfo list(StatisticsHourlyDramaRechargeStats statisticsHourlyDramaRechargeStats)
    {
        startPage();
        List<StatisticsHourlyDramaRechargeStats> list = statisticsHourlyDramaRechargeStatsService.selectList(statisticsHourlyDramaRechargeStats);
        return getDataTable(list);
    }

    /**
     * 获取小时级剧目充值统计数据汇总
     */
    @PreAuthorize("@ss.hasPermi('statistics:dramarecharge:query')")
    @GetMapping(value = "/getSummary")
    public AjaxResult getSummary(StatisticsHourlyDramaRechargeStats statisticsHourlyDramaRechargeStats)
    {
        return AjaxResult.success(statisticsHourlyDramaRechargeStatsService.getSummary(statisticsHourlyDramaRechargeStats));
    }

    /**
     * 获取小时级剧目充值统计区间汇总数据
     */
    @PreAuthorize("@ss.hasPermi('statistics:dramarecharge:query')")
    @PostMapping("/getRangeSummary")
    public AjaxResult getRangeSummary(@RequestBody RangeSummaryRequest request) {
        if (request.getAppid() == null) {
            request.setAppid(SecurityUtils.getAppid());
        }
        if (request.getDelivererName() == null) {
            request.setDelivererName(SecurityUtils.getUsername());
        }
        log.info("[剧目充值统计] 区间汇总查询参数 - startTime: {}, endTime: {}, appid: {}, tfid: {}, contentId: {}, dramaId: {}, delivererName: {}",
                request.getStartTime(), request.getEndTime(), request.getAppid(), request.getTfid(), request.getContentId(), request.getDramaId(), request.getDelivererName());

        // 处理按投手筛选的逻辑
        AjaxResult delivererResult = controllerPermissionUtils.handleDelivererQuery(
            request.getDelivererName(), request.getTfid(), request.getStartTime(), request.getEndTime(),
            params -> {
                // DelivererQueryParams 没有 appid，使用 null
                String firstTfid = params.tfids.isEmpty() ? null : params.tfids.get(0);
                return statisticsHourlyDramaRechargeStatsService.getRangeSummaryWithDetails(params.startTime, params.endTime, null, firstTfid, request.getContentId(), request.getDramaId());
            },
            new StatisticsHourlyDramaRechargeStatsDetailDTO()
        );
        if (delivererResult != null) {
            return delivererResult;
        }

        // 处理默认查询
        AjaxResult defaultResult = controllerPermissionUtils.handleDefaultQuery(
            request.getAppid(), request.getTfid(), request.getStartTime(), request.getEndTime(),
            params -> {
                // DelivererQueryParams 没有 appid，使用 null
                String firstTfid = params.tfids.isEmpty() ? null : params.tfids.get(0);
                return statisticsHourlyDramaRechargeStatsService.getRangeSummaryWithDetails(params.startTime, params.endTime, null, firstTfid, request.getContentId(), request.getDramaId());
            },
            params -> {
                // BusinessAdminQueryParams 没有 appid，通过用户名获取当前用户的 appid
                return statisticsHourlyDramaRechargeStatsService.getRangeSummaryWithDetails(params.startTime, params.endTime, request.getAppid(), null, request.getContentId(), request.getDramaId());
            },
            params -> {
                // SystemAdminQueryParams 有 appid
                return statisticsHourlyDramaRechargeStatsService.getRangeSummaryWithDetails(params.startTime, params.endTime, params.appid, null, request.getContentId(), request.getDramaId());
            },
            new StatisticsHourlyDramaRechargeStatsDetailDTO()
        );
        if (defaultResult != null) {
            return defaultResult;
        }

        // 处理其他情况 - 包含剧目维度的筛选
        StatisticsHourlyDramaRechargeStatsDetailDTO result = statisticsHourlyDramaRechargeStatsService.getRangeSummaryWithDetails(request.getStartTime(), request.getEndTime(), request.getAppid(), request.getTfid(), request.getContentId(), request.getDramaId());
        return AjaxResult.success(result);
    }

    /**
     * 导出小时级剧目充值统计列表
     */
    @PreAuthorize("@ss.hasPermi('statistics:dramarecharge:export')")
    @Log(title = "小时级剧目充值统计", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, StatisticsHourlyDramaRechargeStats statisticsHourlyDramaRechargeStats)
    {
        List<StatisticsHourlyDramaRechargeStats> list = statisticsHourlyDramaRechargeStatsService.selectList(statisticsHourlyDramaRechargeStats);
        ExcelUtil<StatisticsHourlyDramaRechargeStats> util = new ExcelUtil<>(StatisticsHourlyDramaRechargeStats.class);
        util.exportExcel(response, list, "小时级剧目充值统计数据");
    }

    /**
     * 获取小时级剧目充值统计详细信息
     */
    @PreAuthorize("@ss.hasPermi('statistics:dramarecharge:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(statisticsHourlyDramaRechargeStatsService.getById(id));
    }

    /**
     * 新增小时级剧目充值统计
     */
    @PreAuthorize("@ss.hasPermi('statistics:dramarecharge:add')")
    @Log(title = "小时级剧目充值统计", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody StatisticsHourlyDramaRechargeStats statisticsHourlyDramaRechargeStats)
    {
        return toAjax(statisticsHourlyDramaRechargeStatsService.insert(statisticsHourlyDramaRechargeStats));
    }

    /**
     * 修改小时级剧目充值统计
     */
    @PreAuthorize("@ss.hasPermi('statistics:dramarecharge:edit')")
    @Log(title = "小时级剧目充值统计", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody StatisticsHourlyDramaRechargeStats statisticsHourlyDramaRechargeStats)
    {
        return toAjax(statisticsHourlyDramaRechargeStatsService.update(statisticsHourlyDramaRechargeStats));
    }

    /**
     * 删除小时级剧目充值统计
     */
    @PreAuthorize("@ss.hasPermi('statistics:dramarecharge:remove')")
    @Log(title = "小时级剧目充值统计", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable List<Long> ids)
    {
        return toAjax(statisticsHourlyDramaRechargeStatsService.deleteByIds(ids));
    }


    /**
     * 查询自定义分析列
     */
    @PreAuthorize("@ss.hasPermi('statistics:dramarecharge:list')")
    @GetMapping("/columns")
    public AjaxResult columns()
    {
        return AjaxResult.success(SummaryColumnUtils.get(StatisticsHourlyDramaRechargeStats.class));
    }

    /**
     * 查询自定义分析数据
     */
    @PreAuthorize("@ss.hasPermi('statistics:dramarecharge:list')")
    @RequestMapping(value = "/summary")
    public TableDataInfo summary(@RequestBody SummaryRequest query)
    {
        startPage();
        List<StatisticsHourlyDramaRechargeStats> summary = statisticsHourlyDramaRechargeStatsService.summary(query);
        return getDataTable(summary);
    }

    /**
     * 导出自定义分析数据
     */
    @PreAuthorize("@ss.hasPermi('statistics:dramarecharge:export')")
    @Log(title = "导出自定义分析数据", businessType = BusinessType.EXPORT)
    @PostMapping("/summaryExport")
    public void summaryExport(HttpServletResponse response, @RequestBody SummaryRequest query)
    {
        PageHelper.startPage(1, Integer.MAX_VALUE);
        if (null == query.getQuery()) {
            query.setQuery(new ArrayList<>());
        }
        List<StatisticsHourlyDramaRechargeStats> list = statisticsHourlyDramaRechargeStatsService.summary(query);
        query.getGroupBy().addAll(query.getColumns());
        ExcelUtil<StatisticsHourlyDramaRechargeStats> util = new ExcelUtil<>(StatisticsHourlyDramaRechargeStats.class);
        util.exportExcel(response, list, "导出数据");
    }



    /**
     * 下载导入模板
     */
    @PreAuthorize("@ss.hasPermi('statistics:dramarecharge:export')")
    @Log(title = "下载导入模板", businessType = BusinessType.EXPORT)
    @PostMapping("/downloadImportModule")
    public void downloadImportModule()
    {
        statisticsHourlyDramaRechargeStatsService.downloadImportModule();
    }

    /**
     * 导入数据
     */
    @PreAuthorize("@ss.hasPermi('statistics:dramarecharge:export')")
    @Log(title = "导入数据", businessType = BusinessType.EXPORT)
    @PostMapping("/importData")
    public void importData(MultipartFile file)
    {

        statisticsHourlyDramaRechargeStatsService.importData(file);
    }


    /**
     * 查询自定义分析数据-汇总
     */
    @PreAuthorize("@ss.hasPermi('statistics:dramarecharge:list')")
    @RequestMapping(value = "/allSummary")
    public AjaxResult allSummary(@RequestBody SummaryRequest query)
    {
        PageHelper.startPage(1, 1);
        return AjaxResult.success(statisticsHourlyDramaRechargeStatsService.allSummary(query));
    }

    /**
     * 全量统计最近N天的剧目充值数据
     */
    @PreAuthorize("@ss.hasPermi('statistics:dramarecharge:edit')")
    @Log(title = "全量统计剧目充值数据", businessType = BusinessType.UPDATE)
    @PostMapping("/batchUpdate")
    public AjaxResult batchUpdateRecentStats(@RequestBody BatchUpdateRequest request)
    {
        try {
            Integer days = request.getDays();

            // 默认统计最近7天
            if (days == null || days <= 0) {
                days = 7;
            }

            // 限制最多统计365天
            if (days > 365) {
                days = 365;
            }

            String result = hourlyDramaRechargeStatsService.batchUpdateRecentDramaRechargeStats(days);
            return AjaxResult.success(result);
        } catch (Exception e) {
            return AjaxResult.error("全量统计失败: " + e.getMessage());
        }
    }

    /**
     * 全量统计指定时间范围的剧目充值数据
     */
    @PreAuthorize("@ss.hasPermi('statistics:dramarecharge:edit')")
    @Log(title = "全量统计剧目充值数据", businessType = BusinessType.UPDATE)
    @PostMapping("/batchUpdateRange")
    public AjaxResult batchUpdateRangeStats(@RequestBody BatchUpdateRequest request)
    {
        try {
            if (!request.isValidDateRange()) {
                return AjaxResult.error("日期范围参数无效");
            }

            String result = hourlyDramaRechargeStatsService.batchUpdateRangeStats(
                DateUtils.parseDateToStr("yyyy-MM-dd", request.getStartDate()),
                DateUtils.parseDateToStr("yyyy-MM-dd", request.getEndDate())
            );
            return AjaxResult.success(result);
        } catch (Exception e) {
            return AjaxResult.error("全量统计失败: " + e.getMessage());
        }
    }


}
