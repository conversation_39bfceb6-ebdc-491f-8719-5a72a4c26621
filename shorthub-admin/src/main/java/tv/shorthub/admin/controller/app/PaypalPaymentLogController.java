package tv.shorthub.admin.controller.app;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.PageHelper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import tv.shorthub.admin.cache.RefreshAppDramaContents;
import tv.shorthub.common.config.AppConfig;
import tv.shorthub.common.core.domain.SummaryRequest;
import tv.shorthub.common.enums.OrderChannelEnums;
import tv.shorthub.common.utils.SummaryColumnUtils;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import tv.shorthub.common.annotation.Log;
import tv.shorthub.common.core.controller.BaseController;
import tv.shorthub.common.core.domain.AjaxResult;
import tv.shorthub.common.enums.BusinessType;
import tv.shorthub.paypal.config.PaypalConfigStorageHolder;
import tv.shorthub.paypal.constant.PayPalConstants;
import tv.shorthub.paypal.model.PaypalPaymentRequest;
import tv.shorthub.paypal.service.impl.PayPalPaymentServiceImpl;
import tv.shorthub.system.domain.*;
import tv.shorthub.system.service.*;
import tv.shorthub.common.utils.poi.ExcelUtil;
import tv.shorthub.common.core.page.TableDataInfo;
import org.springframework.util.StringUtils;
import com.alibaba.fastjson2.JSONObject;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import java.util.UUID;

/**
 * paypal原始订单Controller
 *
 * <AUTHOR>
 * @date 2025-05-15
 */
@RestController
@RequestMapping("/app/paypal/log")
public class PaypalPaymentLogController extends BaseController
{
    @Autowired
    private IPaypalPaymentLogService paypalPaymentLogService;

    @Autowired
    private IAppUserWatchContentService appUserWatchContentService;

    @Autowired
    private IAppDramaContentsService appDramaContentsService;

    @Autowired
    private IAppOrderInfoService appOrderInfoService;

    @Autowired
    private IAppUsersService appUsersService;

    @Autowired
    PayPalPaymentServiceImpl payPalPaymentService;

    /**
     * 查询paypal原始订单列表
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:log:list')")
    @GetMapping("/list")
    public TableDataInfo list(PaypalPaymentLog paypalPaymentLog)
    {
        startPage();
        List<PaypalPaymentLog> list = paypalPaymentLogService.selectList(paypalPaymentLog);
        return getDataTable(list);
    }

    /**
     * 获取paypal原始订单数据汇总
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:log:query')")
    @GetMapping(value = "/getSummary")
    public AjaxResult getSummary(PaypalPaymentLog paypalPaymentLog)
    {
        return AjaxResult.success(paypalPaymentLogService.getSummary(paypalPaymentLog));
    }

    /**
     * 导出paypal原始订单列表
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:log:export')")
    @Log(title = "paypal原始订单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PaypalPaymentLog paypalPaymentLog)
    {
        List<PaypalPaymentLog> list = paypalPaymentLogService.selectList(paypalPaymentLog);
        ExcelUtil<PaypalPaymentLog> util = new ExcelUtil<>(PaypalPaymentLog.class);
        util.exportExcel(response, list, "paypal原始订单数据");
    }

    /**
     * 获取paypal原始订单详细信息
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:log:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(paypalPaymentLogService.getById(id));
    }

    /**
     * 新增paypal原始订单
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:log:add')")
    @Log(title = "paypal原始订单", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PaypalPaymentLog paypalPaymentLog)
    {
        return toAjax(paypalPaymentLogService.insert(paypalPaymentLog));
    }

    /**
     * 修改paypal原始订单
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:log:edit')")
    @Log(title = "paypal原始订单", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PaypalPaymentLog paypalPaymentLog)
    {
        return toAjax(paypalPaymentLogService.update(paypalPaymentLog));
    }

    /**
     * 删除paypal原始订单
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:log:remove')")
    @Log(title = "paypal原始订单", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable List<Long> ids)
    {
        return toAjax(paypalPaymentLogService.deleteByIds(ids));
    }


    /**
     * 查询自定义分析列
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:log:list')")
    @GetMapping("/columns")
    public AjaxResult columns()
    {
        return AjaxResult.success(SummaryColumnUtils.get(PaypalPaymentLog.class));
    }

    /**
     * 查询自定义分析数据
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:log:list')")
    @RequestMapping(value = "/summary")
    public TableDataInfo summary(@RequestBody SummaryRequest query)
    {
        startPage();
        List<PaypalPaymentLog> summary = paypalPaymentLogService.summary(query);
        return getDataTable(summary);
    }

    /**
     * 导出自定义分析数据
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:log:export')")
    @Log(title = "导出自定义分析数据", businessType = BusinessType.EXPORT)
    @PostMapping("/summaryExport")
    public void summaryExport(HttpServletResponse response, @RequestBody SummaryRequest query)
    {
        PageHelper.startPage(1, Integer.MAX_VALUE);
        if (null == query.getQuery()) {
            query.setQuery(new ArrayList<>());
        }
        List<PaypalPaymentLog> list = paypalPaymentLogService.summary(query);
        query.getGroupBy().addAll(query.getColumns());
        ExcelUtil<PaypalPaymentLog> util = new ExcelUtil<>(PaypalPaymentLog.class);
        util.exportExcel(response, list, "导出数据");
    }



    /**
     * 下载导入模板
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:log:export')")
    @Log(title = "下载导入模板", businessType = BusinessType.EXPORT)
    @PostMapping("/downloadImportModule")
    public void downloadImportModule()
    {
        paypalPaymentLogService.downloadImportModule();
    }

    /**
     * 导入数据
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:log:export')")
    @Log(title = "导入数据", businessType = BusinessType.EXPORT)
    @PostMapping("/importData")
    public void importData(MultipartFile file)
    {

        paypalPaymentLogService.importData(file);
    }


    /**
     * 查询自定义分析数据-汇总
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:log:list')")
    @RequestMapping(value = "/allSummary")
    public AjaxResult allSummary(@RequestBody SummaryRequest query)
    {
        PageHelper.startPage(1, 1);
        return AjaxResult.success(paypalPaymentLogService.allSummary(query));
    }

    /**
     * 更新PayPal订单状态
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:log:edit')")
    @Log(title = "更新PayPal订单状态", businessType = BusinessType.UPDATE)
    @PostMapping("/updateOrderStatus")
    public AjaxResult updateOrderStatus(@RequestBody JSONObject params)
    {
        String orderNo = params.getString("orderNo");
        if (StringUtils.isEmpty(orderNo)) {
            return AjaxResult.error("订单号不能为空");
        }
        return AjaxResult.success(paypalPaymentLogService.updateOrderStatus(orderNo));
    }

    /**
     * 处理PayPal退款
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:log:edit')")
    @Log(title = "处理PayPal退款", businessType = BusinessType.UPDATE)
    @PostMapping("/refund")
    public AjaxResult refund(@RequestBody JSONObject params)
    {
        String orderNo = params.getString("orderNo");
        String reason = params.getString("reason");
        if (StringUtils.isEmpty(orderNo)) {
            return AjaxResult.error("订单号不能为空");
        }
        if (StringUtils.isEmpty(reason)) {
            return AjaxResult.error("退款原因不能为空");
        }
        return AjaxResult.success(paypalPaymentLogService.processRefund(orderNo, reason));
    }

    /**
     * 处理PayPal退订
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:log:edit')")
    @Log(title = "处理PayPal退订", businessType = BusinessType.UPDATE)
    @PostMapping("/unsubscribe")
    public AjaxResult unsubscribe(@RequestBody JSONObject params)
    {
        String orderNo = params.getString("orderNo");
        String reason = params.getString("reason");
        if (StringUtils.isEmpty(orderNo)) {
            return AjaxResult.error("订单号不能为空");
        }
        if (StringUtils.isEmpty(reason)) {
            return AjaxResult.error("退订原因不能为空");
        }
        return AjaxResult.success(paypalPaymentLogService.processUnsubscribe(orderNo, reason));
    }

    /**
     * 二次扣费测试
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:log:edit')")
    @Log(title = "二次扣费测试", businessType = BusinessType.UPDATE)
    @PostMapping("/secondSubscription")
    public AjaxResult secondSubscription(@RequestBody PaypalPaymentLog paypalPaymentLog) throws Exception {

        paypalPaymentLog = paypalPaymentLogService.getById(paypalPaymentLog.getId());

        // 普通支付流程
        PaypalPaymentRequest paypalPaymentRequest = new PaypalPaymentRequest();
        paypalPaymentRequest.setAmount(BigDecimal.valueOf(129));
        paypalPaymentRequest.setPaymentMethod(PayPalConstants.PaymentMethod.RECHARGE);
        paypalPaymentRequest.setItemName("Recharge");
        paypalPaymentRequest.setReturnUrl(tv.shorthub.common.config.AppConfig.getDomainApi() + "/api/order/execute-payment");
        paypalPaymentRequest.setCancelUrl(AppConfig.getDomainApi() + "/api/order/cancel-payment");
        paypalPaymentRequest.setOrderNo(paypalPaymentLog.getOrderNo());

        PaypalConfigStorageHolder.set(paypalPaymentLog.getClientId());

        JSONObject paymentSource = paypalPaymentLog.getRawData().getJSONObject("payment_source");
        if (null != paymentSource && paymentSource.containsKey(OrderChannelEnums.PAYPAL.getValue())) {
            JSONObject paypal = paymentSource.getJSONObject(OrderChannelEnums.PAYPAL.getValue());
            if (paypal.containsKey("attributes")) {
                JSONObject attributes = paypal.getJSONObject("attributes");
                if (attributes.containsKey("vault")) {
                    paypalPaymentRequest.setVaultId(attributes.getJSONObject("vault").getString("id"));
                }
            }
        }

        // 发起paypal支付
        JSONObject payment = payPalPaymentService.createPayment(paypalPaymentRequest);

        return AjaxResult.success(payment);
    }

    /**
     * 查询履行证明数据
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:log:list')")
    @GetMapping("/fulfillmentProof")
    public AjaxResult fulfillmentProof(@RequestParam String orderNo) {
        AppOrderInfo orderInfo = appOrderInfoService.getMapper().selectOne(new QueryWrapper<AppOrderInfo>().eq("order_no", orderNo));
        if (null == orderInfo) {
            return AjaxResult.error("订单不存在");
        }
        JSONObject data = new JSONObject();
        data.put("orderInfo", orderInfo);
        // 已解锁剧目记录
        List<AppUserWatchContent> appUserWatchContents = appUserWatchContentService.getMapper().selectList(new QueryWrapper<AppUserWatchContent>().eq("user_id", orderInfo.getUserId()));
        if (CollectionUtils.isNotEmpty(appUserWatchContents)) {
            List<AppDramaContents> unlockContents = appDramaContentsService.getMapper().selectList(new QueryWrapper<AppDramaContents>().in("content_id", appUserWatchContents.stream().map(AppUserWatchContent::getContentId).toList()));
            data.put("unlockContents", unlockContents);
        }

        AppUsers user = appUsersService.getMapper().selectOne(new QueryWrapper<AppUsers>().eq("user_id", orderInfo.getUserId()));
        if (null != user) {
            data.put("user", user);
        }

        return AjaxResult.success(data);
    }
}
