package tv.shorthub.admin.controller.airwallex;

import com.github.pagehelper.PageHelper;
import tv.shorthub.common.core.domain.SummaryRequest;
import tv.shorthub.common.utils.SummaryColumnUtils;
import org.springframework.web.multipart.MultipartFile;
import java.util.ArrayList;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import tv.shorthub.common.annotation.Log;
import tv.shorthub.common.core.controller.BaseController;
import tv.shorthub.common.core.domain.AjaxResult;
import tv.shorthub.common.enums.BusinessType;
import tv.shorthub.system.domain.AirwallexPaymentLog;
import tv.shorthub.system.service.IAirwallexPaymentLogService;
import tv.shorthub.common.utils.poi.ExcelUtil;
import tv.shorthub.common.core.page.TableDataInfo;

/**
 * airwallex支付日志Controller
 *
 * <AUTHOR>
 * @date 2025-07-17
 */
@RestController
@RequestMapping("/pay/airwallex/log")
public class AirwallexPaymentLogController extends BaseController
{
    @Autowired
    private IAirwallexPaymentLogService airwallexPaymentLogService;

    /**
     * 查询airwallex支付日志列表
     */
    @PreAuthorize("@ss.hasPermi('pay/airwallex:log:list')")
    @GetMapping("/list")
    public TableDataInfo list(AirwallexPaymentLog airwallexPaymentLog)
    {
        startPage();
        List<AirwallexPaymentLog> list = airwallexPaymentLogService.selectList(airwallexPaymentLog);
        return getDataTable(list);
    }

    /**
     * 获取airwallex支付日志数据汇总
     */
    @PreAuthorize("@ss.hasPermi('pay/airwallex:log:query')")
    @GetMapping(value = "/getSummary")
    public AjaxResult getSummary(AirwallexPaymentLog airwallexPaymentLog)
    {
        return AjaxResult.success(airwallexPaymentLogService.getSummary(airwallexPaymentLog));
    }

    /**
     * 导出airwallex支付日志列表
     */
    @PreAuthorize("@ss.hasPermi('pay/airwallex:log:export')")
    @Log(title = "airwallex支付日志", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AirwallexPaymentLog airwallexPaymentLog)
    {
        List<AirwallexPaymentLog> list = airwallexPaymentLogService.selectList(airwallexPaymentLog);
        ExcelUtil<AirwallexPaymentLog> util = new ExcelUtil<>(AirwallexPaymentLog.class);
        util.exportExcel(response, list, "airwallex支付日志数据");
    }

    /**
     * 获取airwallex支付日志详细信息
     */
    @PreAuthorize("@ss.hasPermi('pay/airwallex:log:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(airwallexPaymentLogService.getById(id));
    }

    /**
     * 新增airwallex支付日志
     */
    @PreAuthorize("@ss.hasPermi('pay/airwallex:log:add')")
    @Log(title = "airwallex支付日志", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AirwallexPaymentLog airwallexPaymentLog)
    {
        return toAjax(airwallexPaymentLogService.insert(airwallexPaymentLog));
    }

    /**
     * 修改airwallex支付日志
     */
    @PreAuthorize("@ss.hasPermi('pay/airwallex:log:edit')")
    @Log(title = "airwallex支付日志", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AirwallexPaymentLog airwallexPaymentLog)
    {
        return toAjax(airwallexPaymentLogService.update(airwallexPaymentLog));
    }

    /**
     * 删除airwallex支付日志
     */
    @PreAuthorize("@ss.hasPermi('pay/airwallex:log:remove')")
    @Log(title = "airwallex支付日志", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable List<Long> ids)
    {
        return toAjax(airwallexPaymentLogService.deleteByIds(ids));
    }


    /**
     * 查询自定义分析列
     */
    @PreAuthorize("@ss.hasPermi('pay/airwallex:log:list')")
    @GetMapping("/columns")
    public AjaxResult columns()
    {
        return AjaxResult.success(SummaryColumnUtils.get(AirwallexPaymentLog.class));
    }

    /**
     * 查询自定义分析数据
     */
    @PreAuthorize("@ss.hasPermi('pay/airwallex:log:list')")
    @RequestMapping(value = "/summary")
    public TableDataInfo summary(@RequestBody SummaryRequest query)
    {
        startPage();
        List<AirwallexPaymentLog> summary = airwallexPaymentLogService.summary(query);
        return getDataTable(summary);
    }

    /**
     * 导出自定义分析数据
     */
    @PreAuthorize("@ss.hasPermi('pay/airwallex:log:export')")
    @Log(title = "导出自定义分析数据", businessType = BusinessType.EXPORT)
    @PostMapping("/summaryExport")
    public void summaryExport(HttpServletResponse response, @RequestBody SummaryRequest query)
    {
        PageHelper.startPage(1, Integer.MAX_VALUE);
        if (null == query.getQuery()) {
            query.setQuery(new ArrayList<>());
        }
        List<AirwallexPaymentLog> list = airwallexPaymentLogService.summary(query);
        query.getGroupBy().addAll(query.getColumns());
        ExcelUtil<AirwallexPaymentLog> util = new ExcelUtil<>(AirwallexPaymentLog.class);
        util.exportExcel(response, list, "导出数据");
    }



    /**
     * 下载导入模板
     */
    @PreAuthorize("@ss.hasPermi('pay/airwallex:log:export')")
    @Log(title = "下载导入模板", businessType = BusinessType.EXPORT)
    @PostMapping("/downloadImportModule")
    public void downloadImportModule()
    {
        airwallexPaymentLogService.downloadImportModule();
    }

    /**
     * 导入数据
     */
    @PreAuthorize("@ss.hasPermi('pay/airwallex:log:export')")
    @Log(title = "导入数据", businessType = BusinessType.EXPORT)
    @PostMapping("/importData")
    public void importData(MultipartFile file)
    {

        airwallexPaymentLogService.importData(file);
    }


    /**
     * 查询自定义分析数据-汇总
     */
    @PreAuthorize("@ss.hasPermi('pay/airwallex:log:list')")
    @RequestMapping(value = "/allSummary")
    public AjaxResult allSummary(@RequestBody SummaryRequest query)
    {
        PageHelper.startPage(1, 1);
        return AjaxResult.success(airwallexPaymentLogService.allSummary(query));
    }
}
