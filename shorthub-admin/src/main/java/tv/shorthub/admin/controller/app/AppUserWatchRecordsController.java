package tv.shorthub.admin.controller.app;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import tv.shorthub.common.annotation.Log;
import tv.shorthub.common.annotation.RateLimiter;
import tv.shorthub.common.config.PathVariableList;
import tv.shorthub.common.core.cache.CacheKeyUtils;
import tv.shorthub.common.core.cache.CacheService;
import tv.shorthub.common.core.controller.BaseController;
import tv.shorthub.common.core.domain.AjaxResult;
import tv.shorthub.common.core.domain.SummaryRequest;
import tv.shorthub.common.core.page.TableDataInfo;
import tv.shorthub.common.enums.BusinessType;
import tv.shorthub.common.enums.LimitType;
import tv.shorthub.common.utils.StringUtils;
import tv.shorthub.common.utils.SummaryColumnUtils;
import tv.shorthub.common.utils.poi.ExcelUtil;
import tv.shorthub.system.domain.AppDramaContents;
import tv.shorthub.system.domain.AppDramaContentsSerial;
import tv.shorthub.system.domain.AppUserWatchRecords;
import tv.shorthub.system.service.IAppDramaContentsSerialService;
import tv.shorthub.system.service.IAppDramaContentsService;
import tv.shorthub.system.service.IAppUserWatchRecordsService;
import com.github.pagehelper.PageHelper;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 用户观看记录Controller
 *
 * <AUTHOR>
 * @date 2025-05-06
 */
@RestController
@RequestMapping("/app/watch_records")
public class AppUserWatchRecordsController extends BaseController
{
    @Autowired
    private IAppUserWatchRecordsService appUserWatchRecordsService;

    @Autowired
    private IAppDramaContentsService appDramaContentsService;

    @Autowired
    private IAppDramaContentsSerialService serialService;

    @Autowired
    CacheService cacheService;

    /**
     * 查询用户观看记录列表
     */
    @PreAuthorize("@ss.hasPermi('app:watch_records:list')")
    @GetMapping("/list")
    @RateLimiter(limitType = LimitType.USER_ID, time = 20, count = 10)
    public TableDataInfo list(AppUserWatchRecords appUserWatchRecords)
    {
        startPage();
        List<AppUserWatchRecords> list = appUserWatchRecordsService.selectList(appUserWatchRecords);
        TableDataInfo dataTable = getDataTable(list);
        List<AppUserWatchRecords> rows = (List<AppUserWatchRecords>) dataTable.getRows();
        if (StringUtils.isEmpty(rows)) {
            return dataTable;
        }

        // Get content IDs from watch records
        List<String> contentIds = rows.stream()
                .map(AppUserWatchRecords::getContentId)
                .distinct()
                .toList();

        // Fetch contents and serials
        List<AppDramaContents> contents = appDramaContentsService.getMapper()
                .selectList(new QueryWrapper<AppDramaContents>()
                        .in("content_id", contentIds));

        // Create maps for quick lookup
        Map<String, AppDramaContents> contentsMap = contents.stream()
                .collect(Collectors.toMap(AppDramaContents::getContentId, v -> v));

        // Enrich watch records with content and serial data
        for (AppUserWatchRecords row : rows) {
            String contentId = row.getContentId();
            Long serialNumber = row.getSerialNumber();

            row.getParams().put("content", contentsMap.get(contentId));

            // Filter serials by serial_number for this specific watch record
            row.getParams().put("serial", cacheService.getCacheMapValue(CacheKeyUtils.getContentSerialMap(contentId), serialNumber.toString()));
        }

        dataTable.setRows(rows);
        return dataTable;
    }

    /**
     * 获取用户观看记录数据汇总
     */
    @PreAuthorize("@ss.hasPermi('app:watch_records:query')")
    @GetMapping(value = "/getSummary")
    public AjaxResult getSummary(AppUserWatchRecords appUserWatchRecords)
    {
        return AjaxResult.success(appUserWatchRecordsService.getSummary(appUserWatchRecords));
    }

    /**
     * 导出用户观看记录列表
     */
    @PreAuthorize("@ss.hasPermi('app:watch_records:export')")
    @Log(title = "用户观看记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AppUserWatchRecords appUserWatchRecords)
    {
        List<AppUserWatchRecords> list = appUserWatchRecordsService.selectList(appUserWatchRecords);
        ExcelUtil<AppUserWatchRecords> util = new ExcelUtil<>(AppUserWatchRecords.class);
        util.exportExcel(response, list, "用户观看记录数据");
    }

    /**
     * 获取用户观看记录详细信息
     */
    @PreAuthorize("@ss.hasPermi('app:watch_records:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(appUserWatchRecordsService.getById(id));
    }

    /**
     * 新增用户观看记录
     */
    @PreAuthorize("@ss.hasPermi('app:watch_records:add')")
    @Log(title = "用户观看记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AppUserWatchRecords appUserWatchRecords)
    {
        return toAjax(appUserWatchRecordsService.insert(appUserWatchRecords));
    }

    /**
     * 修改用户观看记录
     */
    @PreAuthorize("@ss.hasPermi('app:watch_records:edit')")
    @Log(title = "用户观看记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AppUserWatchRecords appUserWatchRecords)
    {
        return toAjax(appUserWatchRecordsService.update(appUserWatchRecords));
    }

    /**
     * 删除用户观看记录
     */
    @PreAuthorize("@ss.hasPermi('app:watch_records:remove')")
    @Log(title = "用户观看记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariableList List<Long> ids)
    {
        return toAjax(appUserWatchRecordsService.deleteByIds(ids));
    }


    /**
     * 查询自定义分析列
     */
    @PreAuthorize("@ss.hasPermi('app:watch_records:list')")
    @GetMapping("/columns")
    public AjaxResult columns()
    {
        return AjaxResult.success(SummaryColumnUtils.get(AppUserWatchRecords.class));
    }

    /**
     * 查询自定义分析数据
     */
    @PreAuthorize("@ss.hasPermi('app:watch_records:list')")
    @RequestMapping(value = "/summary")
    public TableDataInfo summary(@RequestBody SummaryRequest query)
    {
        startPage();
        List<AppUserWatchRecords> summary = appUserWatchRecordsService.summary(query);
        return getDataTable(summary);
    }

    /**
     * 导出自定义分析数据
     */
    @PreAuthorize("@ss.hasPermi('app:watch_records:export')")
    @Log(title = "导出自定义分析数据", businessType = BusinessType.EXPORT)
    @PostMapping("/summaryExport")
    public void summaryExport(HttpServletResponse response, @RequestBody SummaryRequest query)
    {
        PageHelper.startPage(1, Integer.MAX_VALUE);
        if (null == query.getQuery()) {
            query.setQuery(new ArrayList<>());
        }
        List<AppUserWatchRecords> list = appUserWatchRecordsService.summary(query);
        query.getGroupBy().addAll(query.getColumns());
        ExcelUtil<AppUserWatchRecords> util = new ExcelUtil<>(AppUserWatchRecords.class, query.getGroupBy());
        util.exportExcel(response, list, "导出数据");
    }



    /**
     * 下载导入模板
     */
    @PreAuthorize("@ss.hasPermi('app:watch_records:export')")
    @Log(title = "下载导入模板", businessType = BusinessType.EXPORT)
    @PostMapping("/downloadImportModule")
    public void downloadImportModule()
    {
        appUserWatchRecordsService.downloadImportModule();
    }

    /**
     * 导入数据
     */
    @PreAuthorize("@ss.hasPermi('app:watch_records:export')")
    @Log(title = "导入数据", businessType = BusinessType.EXPORT)
    @PostMapping("/importData")
    public void importData(MultipartFile file)
    {

        appUserWatchRecordsService.importData(file);
    }


    /**
     * 查询自定义分析数据-汇总
     */
    @PreAuthorize("@ss.hasPermi('app:watch_records:list')")
    @RequestMapping(value = "/allSummary")
    public AjaxResult allSummary(@RequestBody SummaryRequest query)
    {
        PageHelper.startPage(1, 1);
        return AjaxResult.success(appUserWatchRecordsService.allSummary(query));
    }
}
