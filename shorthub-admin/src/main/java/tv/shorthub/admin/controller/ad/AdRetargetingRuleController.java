package tv.shorthub.admin.controller.ad;

import com.github.pagehelper.PageHelper;
import tv.shorthub.common.core.domain.SummaryRequest;
import tv.shorthub.common.utils.SummaryColumnUtils;
import org.springframework.web.multipart.MultipartFile;
import java.util.ArrayList;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import tv.shorthub.common.annotation.Log;
import tv.shorthub.common.core.controller.BaseController;
import tv.shorthub.common.core.domain.AjaxResult;
import tv.shorthub.common.enums.BusinessType;
import tv.shorthub.system.domain.AdRetargetingRule;
import tv.shorthub.system.service.IAdRetargetingRuleService;
import tv.shorthub.common.utils.poi.ExcelUtil;
import tv.shorthub.common.core.page.TableDataInfo;

/**
 * 回传规则Controller
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@RestController
@RequestMapping("/ad/retargeting/rule")
public class AdRetargetingRuleController extends BaseController
{
    @Autowired
    private IAdRetargetingRuleService adRetargetingRuleService;

    /**
     * 查询回传规则列表
     */
    @PreAuthorize("@ss.hasPermi('ad/retargeting:rule:list')")
    @GetMapping("/list")
    public TableDataInfo list(AdRetargetingRule adRetargetingRule)
    {
        startPage();
        List<AdRetargetingRule> list = adRetargetingRuleService.selectList(adRetargetingRule);
        return getDataTable(list);
    }

    /**
     * 获取回传规则数据汇总
     */
    @PreAuthorize("@ss.hasPermi('ad/retargeting:rule:query')")
    @GetMapping(value = "/getSummary")
    public AjaxResult getSummary(AdRetargetingRule adRetargetingRule)
    {
        return AjaxResult.success(adRetargetingRuleService.getSummary(adRetargetingRule));
    }

    /**
     * 导出回传规则列表
     */
    @PreAuthorize("@ss.hasPermi('ad/retargeting:rule:export')")
    @Log(title = "回传规则", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AdRetargetingRule adRetargetingRule)
    {
        List<AdRetargetingRule> list = adRetargetingRuleService.selectList(adRetargetingRule);
        ExcelUtil<AdRetargetingRule> util = new ExcelUtil<>(AdRetargetingRule.class);
        util.exportExcel(response, list, "回传规则数据");
    }

    /**
     * 获取回传规则详细信息
     */
    @PreAuthorize("@ss.hasPermi('ad/retargeting:rule:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(adRetargetingRuleService.getById(id));
    }

    /**
     * 新增回传规则
     */
    @PreAuthorize("@ss.hasPermi('ad/retargeting:rule:add')")
    @Log(title = "回传规则", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AdRetargetingRule adRetargetingRule)
    {
        return toAjax(adRetargetingRuleService.insert(adRetargetingRule));
    }

    /**
     * 修改回传规则
     */
    @PreAuthorize("@ss.hasPermi('ad/retargeting:rule:edit')")
    @Log(title = "回传规则", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AdRetargetingRule adRetargetingRule)
    {
        return toAjax(adRetargetingRuleService.update(adRetargetingRule));
    }

    /**
     * 删除回传规则
     */
    @PreAuthorize("@ss.hasPermi('ad/retargeting:rule:remove')")
    @Log(title = "回传规则", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable List<Long> ids)
    {
        return toAjax(adRetargetingRuleService.deleteByIds(ids));
    }


    /**
     * 查询自定义分析列
     */
    @PreAuthorize("@ss.hasPermi('ad/retargeting:rule:list')")
    @GetMapping("/columns")
    public AjaxResult columns()
    {
        return AjaxResult.success(SummaryColumnUtils.get(AdRetargetingRule.class));
    }

    /**
     * 查询自定义分析数据
     */
    @PreAuthorize("@ss.hasPermi('ad/retargeting:rule:list')")
    @RequestMapping(value = "/summary")
    public TableDataInfo summary(@RequestBody SummaryRequest query)
    {
        startPage();
        List<AdRetargetingRule> summary = adRetargetingRuleService.summary(query);
        return getDataTable(summary);
    }

    /**
     * 导出自定义分析数据
     */
    @PreAuthorize("@ss.hasPermi('ad/retargeting:rule:export')")
    @Log(title = "导出自定义分析数据", businessType = BusinessType.EXPORT)
    @PostMapping("/summaryExport")
    public void summaryExport(HttpServletResponse response, @RequestBody SummaryRequest query)
    {
        PageHelper.startPage(1, Integer.MAX_VALUE);
        if (null == query.getQuery()) {
            query.setQuery(new ArrayList<>());
        }
        List<AdRetargetingRule> list = adRetargetingRuleService.summary(query);
        query.getGroupBy().addAll(query.getColumns());
        ExcelUtil<AdRetargetingRule> util = new ExcelUtil<>(AdRetargetingRule.class);
        util.exportExcel(response, list, "导出数据");
    }



    /**
     * 下载导入模板
     */
    @PreAuthorize("@ss.hasPermi('ad/retargeting:rule:export')")
    @Log(title = "下载导入模板", businessType = BusinessType.EXPORT)
    @PostMapping("/downloadImportModule")
    public void downloadImportModule()
    {
        adRetargetingRuleService.downloadImportModule();
    }

    /**
     * 导入数据
     */
    @PreAuthorize("@ss.hasPermi('ad/retargeting:rule:export')")
    @Log(title = "导入数据", businessType = BusinessType.EXPORT)
    @PostMapping("/importData")
    public void importData(MultipartFile file)
    {

        adRetargetingRuleService.importData(file);
    }


    /**
     * 查询自定义分析数据-汇总
     */
    @PreAuthorize("@ss.hasPermi('ad/retargeting:rule:list')")
    @RequestMapping(value = "/allSummary")
    public AjaxResult allSummary(@RequestBody SummaryRequest query)
    {
        PageHelper.startPage(1, 1);
        return AjaxResult.success(adRetargetingRuleService.allSummary(query));
    }
}
