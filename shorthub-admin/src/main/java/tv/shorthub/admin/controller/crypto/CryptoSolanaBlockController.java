package tv.shorthub.admin.controller.crypto;

import com.github.pagehelper.PageHelper;
import tv.shorthub.common.core.domain.SummaryRequest;
import tv.shorthub.common.utils.SummaryColumnUtils;
import org.springframework.web.multipart.MultipartFile;
import java.util.ArrayList;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import tv.shorthub.common.annotation.Log;
import tv.shorthub.common.core.controller.BaseController;
import tv.shorthub.common.core.domain.AjaxResult;
import tv.shorthub.common.enums.BusinessType;
import tv.shorthub.system.domain.CryptoSolanaBlock;
import tv.shorthub.system.service.ICryptoSolanaBlockService;
import tv.shorthub.common.utils.poi.ExcelUtil;
import tv.shorthub.common.core.page.TableDataInfo;

/**
 * SOLANA区块链区块Controller
 *
 * <AUTHOR>
 * @date 2025-08-05
 */
@RestController
@RequestMapping("/crypto/blocksolana")
public class CryptoSolanaBlockController extends BaseController
{
    @Autowired
    private ICryptoSolanaBlockService cryptoSolanaBlockService;

    /**
     * 查询SOLANA区块链区块列表
     */
    @PreAuthorize("@ss.hasPermi('crypto:blocksolana:list')")
    @GetMapping("/list")
    public TableDataInfo list(CryptoSolanaBlock cryptoSolanaBlock)
    {
        startPage();
        List<CryptoSolanaBlock> list = cryptoSolanaBlockService.selectList(cryptoSolanaBlock);
        return getDataTable(list);
    }

    /**
     * 获取SOLANA区块链区块数据汇总
     */
    @PreAuthorize("@ss.hasPermi('crypto:blocksolana:query')")
    @GetMapping(value = "/getSummary")
    public AjaxResult getSummary(CryptoSolanaBlock cryptoSolanaBlock)
    {
        return AjaxResult.success(cryptoSolanaBlockService.getSummary(cryptoSolanaBlock));
    }

    /**
     * 导出SOLANA区块链区块列表
     */
    @PreAuthorize("@ss.hasPermi('crypto:blocksolana:export')")
    @Log(title = "SOLANA区块链区块", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CryptoSolanaBlock cryptoSolanaBlock)
    {
        List<CryptoSolanaBlock> list = cryptoSolanaBlockService.selectList(cryptoSolanaBlock);
        ExcelUtil<CryptoSolanaBlock> util = new ExcelUtil<>(CryptoSolanaBlock.class);
        util.exportExcel(response, list, "SOLANA区块链区块数据");
    }

    /**
     * 获取SOLANA区块链区块详细信息
     */
    @PreAuthorize("@ss.hasPermi('crypto:blocksolana:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(cryptoSolanaBlockService.getById(id));
    }

    /**
     * 新增SOLANA区块链区块
     */
    @PreAuthorize("@ss.hasPermi('crypto:blocksolana:add')")
    @Log(title = "SOLANA区块链区块", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CryptoSolanaBlock cryptoSolanaBlock)
    {
        return toAjax(cryptoSolanaBlockService.insert(cryptoSolanaBlock));
    }

    /**
     * 修改SOLANA区块链区块
     */
    @PreAuthorize("@ss.hasPermi('crypto:blocksolana:edit')")
    @Log(title = "SOLANA区块链区块", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CryptoSolanaBlock cryptoSolanaBlock)
    {
        return toAjax(cryptoSolanaBlockService.update(cryptoSolanaBlock));
    }

    /**
     * 删除SOLANA区块链区块
     */
    @PreAuthorize("@ss.hasPermi('crypto:blocksolana:remove')")
    @Log(title = "SOLANA区块链区块", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable List<Long> ids)
    {
        return toAjax(cryptoSolanaBlockService.deleteByIds(ids));
    }


    /**
     * 查询自定义分析列
     */
    @PreAuthorize("@ss.hasPermi('crypto:blocksolana:list')")
    @GetMapping("/columns")
    public AjaxResult columns()
    {
        return AjaxResult.success(SummaryColumnUtils.get(CryptoSolanaBlock.class));
    }

    /**
     * 查询自定义分析数据
     */
    @PreAuthorize("@ss.hasPermi('crypto:blocksolana:list')")
    @RequestMapping(value = "/summary")
    public TableDataInfo summary(@RequestBody SummaryRequest query)
    {
        startPage();
        List<CryptoSolanaBlock> summary = cryptoSolanaBlockService.summary(query);
        return getDataTable(summary);
    }

    /**
     * 导出自定义分析数据
     */
    @PreAuthorize("@ss.hasPermi('crypto:blocksolana:export')")
    @Log(title = "导出自定义分析数据", businessType = BusinessType.EXPORT)
    @PostMapping("/summaryExport")
    public void summaryExport(HttpServletResponse response, @RequestBody SummaryRequest query)
    {
        PageHelper.startPage(1, Integer.MAX_VALUE);
        if (null == query.getQuery()) {
            query.setQuery(new ArrayList<>());
        }
        List<CryptoSolanaBlock> list = cryptoSolanaBlockService.summary(query);
        query.getGroupBy().addAll(query.getColumns());
        ExcelUtil<CryptoSolanaBlock> util = new ExcelUtil<>(CryptoSolanaBlock.class);
        util.exportExcel(response, list, "导出数据");
    }



    /**
     * 下载导入模板
     */
    @PreAuthorize("@ss.hasPermi('crypto:blocksolana:export')")
    @Log(title = "下载导入模板", businessType = BusinessType.EXPORT)
    @PostMapping("/downloadImportModule")
    public void downloadImportModule()
    {
        cryptoSolanaBlockService.downloadImportModule();
    }

    /**
     * 导入数据
     */
    @PreAuthorize("@ss.hasPermi('crypto:blocksolana:export')")
    @Log(title = "导入数据", businessType = BusinessType.EXPORT)
    @PostMapping("/importData")
    public void importData(MultipartFile file)
    {

        cryptoSolanaBlockService.importData(file);
    }


    /**
     * 查询自定义分析数据-汇总
     */
    @PreAuthorize("@ss.hasPermi('crypto:blocksolana:list')")
    @RequestMapping(value = "/allSummary")
    public AjaxResult allSummary(@RequestBody SummaryRequest query)
    {
        PageHelper.startPage(1, 1);
        return AjaxResult.success(cryptoSolanaBlockService.allSummary(query));
    }
}
