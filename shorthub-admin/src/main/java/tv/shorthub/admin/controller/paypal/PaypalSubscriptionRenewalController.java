package tv.shorthub.admin.controller.paypal;

import com.github.pagehelper.PageHelper;
import tv.shorthub.common.core.domain.SummaryRequest;
import tv.shorthub.common.utils.SummaryColumnUtils;
import org.springframework.web.multipart.MultipartFile;
import java.util.ArrayList;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import tv.shorthub.common.annotation.Log;
import tv.shorthub.common.core.controller.BaseController;
import tv.shorthub.common.core.domain.AjaxResult;
import tv.shorthub.common.enums.BusinessType;
import tv.shorthub.system.domain.PaypalSubscriptionRenewal;
import tv.shorthub.system.service.IPaypalSubscriptionRenewalService;
import tv.shorthub.common.utils.poi.ExcelUtil;
import tv.shorthub.common.core.page.TableDataInfo;

/**
 * PayPal订阅续订记录Controller
 *
 * <AUTHOR>
 * @date 2025-06-12
 */
@RestController
@RequestMapping("/app/paypal/renewal")
public class PaypalSubscriptionRenewalController extends BaseController
{
    @Autowired
    private IPaypalSubscriptionRenewalService paypalSubscriptionRenewalService;

    /**
     * 查询PayPal订阅续订记录列表
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:renewal:list')")
    @GetMapping("/list")
    public TableDataInfo list(PaypalSubscriptionRenewal paypalSubscriptionRenewal)
    {
        startPage();
        List<PaypalSubscriptionRenewal> list = paypalSubscriptionRenewalService.selectList(paypalSubscriptionRenewal);
        return getDataTable(list);
    }

    /**
     * 获取PayPal订阅续订记录数据汇总
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:renewal:query')")
    @GetMapping(value = "/getSummary")
    public AjaxResult getSummary(PaypalSubscriptionRenewal paypalSubscriptionRenewal)
    {
        return AjaxResult.success(paypalSubscriptionRenewalService.getSummary(paypalSubscriptionRenewal));
    }

    /**
     * 导出PayPal订阅续订记录列表
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:renewal:export')")
    @Log(title = "PayPal订阅续订记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PaypalSubscriptionRenewal paypalSubscriptionRenewal)
    {
        List<PaypalSubscriptionRenewal> list = paypalSubscriptionRenewalService.selectList(paypalSubscriptionRenewal);
        ExcelUtil<PaypalSubscriptionRenewal> util = new ExcelUtil<>(PaypalSubscriptionRenewal.class);
        util.exportExcel(response, list, "PayPal订阅续订记录数据");
    }

    /**
     * 获取PayPal订阅续订记录详细信息
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:renewal:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(paypalSubscriptionRenewalService.getById(id));
    }

    /**
     * 新增PayPal订阅续订记录
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:renewal:add')")
    @Log(title = "PayPal订阅续订记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PaypalSubscriptionRenewal paypalSubscriptionRenewal)
    {
        return toAjax(paypalSubscriptionRenewalService.insert(paypalSubscriptionRenewal));
    }

    /**
     * 修改PayPal订阅续订记录
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:renewal:edit')")
    @Log(title = "PayPal订阅续订记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PaypalSubscriptionRenewal paypalSubscriptionRenewal)
    {
        return toAjax(paypalSubscriptionRenewalService.update(paypalSubscriptionRenewal));
    }

    /**
     * 删除PayPal订阅续订记录
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:renewal:remove')")
    @Log(title = "PayPal订阅续订记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable List<Long> ids)
    {
        return toAjax(paypalSubscriptionRenewalService.deleteByIds(ids));
    }


    /**
     * 查询自定义分析列
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:renewal:list')")
    @GetMapping("/columns")
    public AjaxResult columns()
    {
        return AjaxResult.success(SummaryColumnUtils.get(PaypalSubscriptionRenewal.class));
    }

    /**
     * 查询自定义分析数据
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:renewal:list')")
    @RequestMapping(value = "/summary")
    public TableDataInfo summary(@RequestBody SummaryRequest query)
    {
        startPage();
        List<PaypalSubscriptionRenewal> summary = paypalSubscriptionRenewalService.summary(query);
        return getDataTable(summary);
    }

    /**
     * 导出自定义分析数据
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:renewal:export')")
    @Log(title = "导出自定义分析数据", businessType = BusinessType.EXPORT)
    @PostMapping("/summaryExport")
    public void summaryExport(HttpServletResponse response, @RequestBody SummaryRequest query)
    {
        PageHelper.startPage(1, Integer.MAX_VALUE);
        if (null == query.getQuery()) {
            query.setQuery(new ArrayList<>());
        }
        List<PaypalSubscriptionRenewal> list = paypalSubscriptionRenewalService.summary(query);
        query.getGroupBy().addAll(query.getColumns());
        ExcelUtil<PaypalSubscriptionRenewal> util = new ExcelUtil<>(PaypalSubscriptionRenewal.class);
        util.exportExcel(response, list, "导出数据");
    }



    /**
     * 下载导入模板
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:renewal:export')")
    @Log(title = "下载导入模板", businessType = BusinessType.EXPORT)
    @PostMapping("/downloadImportModule")
    public void downloadImportModule()
    {
        paypalSubscriptionRenewalService.downloadImportModule();
    }

    /**
     * 导入数据
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:renewal:export')")
    @Log(title = "导入数据", businessType = BusinessType.EXPORT)
    @PostMapping("/importData")
    public void importData(MultipartFile file)
    {

        paypalSubscriptionRenewalService.importData(file);
    }


    /**
     * 查询自定义分析数据-汇总
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:renewal:list')")
    @RequestMapping(value = "/allSummary")
    public AjaxResult allSummary(@RequestBody SummaryRequest query)
    {
        PageHelper.startPage(1, 1);
        return AjaxResult.success(paypalSubscriptionRenewalService.allSummary(query));
    }
}
