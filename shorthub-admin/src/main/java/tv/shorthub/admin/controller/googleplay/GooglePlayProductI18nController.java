package tv.shorthub.admin.controller.googleplay;

import com.github.pagehelper.PageHelper;
import tv.shorthub.common.core.domain.SummaryRequest;
import tv.shorthub.common.utils.SummaryColumnUtils;
import org.springframework.web.multipart.MultipartFile;
import java.util.ArrayList;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import tv.shorthub.common.annotation.Log;
import tv.shorthub.common.core.controller.BaseController;
import tv.shorthub.common.core.domain.AjaxResult;
import tv.shorthub.common.enums.BusinessType;
import tv.shorthub.system.domain.GooglePlayProductI18n;
import tv.shorthub.system.service.IGooglePlayProductI18nService;
import tv.shorthub.common.utils.poi.ExcelUtil;
import tv.shorthub.common.core.page.TableDataInfo;

/**
 * Google Play产品多语言信息Controller
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
@RestController
@RequestMapping("/google/play/product/i18n")
public class GooglePlayProductI18nController extends BaseController
{
    @Autowired
    private IGooglePlayProductI18nService googlePlayProductI18nService;

    /**
     * 查询Google Play产品多语言信息列表
     */
    @PreAuthorize("@ss.hasPermi('google/play:product/i18n:list')")
    @GetMapping("/list")
    public TableDataInfo list(GooglePlayProductI18n googlePlayProductI18n)
    {
        startPage();
        List<GooglePlayProductI18n> list = googlePlayProductI18nService.selectList(googlePlayProductI18n);
        return getDataTable(list);
    }

    /**
     * 获取Google Play产品多语言信息数据汇总
     */
    @PreAuthorize("@ss.hasPermi('google/play:product/i18n:query')")
    @GetMapping(value = "/getSummary")
    public AjaxResult getSummary(GooglePlayProductI18n googlePlayProductI18n)
    {
        return AjaxResult.success(googlePlayProductI18nService.getSummary(googlePlayProductI18n));
    }

    /**
     * 导出Google Play产品多语言信息列表
     */
    @PreAuthorize("@ss.hasPermi('google/play:product/i18n:export')")
    @Log(title = "Google Play产品多语言信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, GooglePlayProductI18n googlePlayProductI18n)
    {
        List<GooglePlayProductI18n> list = googlePlayProductI18nService.selectList(googlePlayProductI18n);
        ExcelUtil<GooglePlayProductI18n> util = new ExcelUtil<>(GooglePlayProductI18n.class);
        util.exportExcel(response, list, "Google Play产品多语言信息数据");
    }

    /**
     * 获取Google Play产品多语言信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('google/play:product/i18n:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(googlePlayProductI18nService.getById(id));
    }

    /**
     * 新增Google Play产品多语言信息
     */
    @PreAuthorize("@ss.hasPermi('google/play:product/i18n:add')")
    @Log(title = "Google Play产品多语言信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody GooglePlayProductI18n googlePlayProductI18n)
    {
        return toAjax(googlePlayProductI18nService.insert(googlePlayProductI18n));
    }

    /**
     * 修改Google Play产品多语言信息
     */
    @PreAuthorize("@ss.hasPermi('google/play:product/i18n:edit')")
    @Log(title = "Google Play产品多语言信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody GooglePlayProductI18n googlePlayProductI18n)
    {
        return toAjax(googlePlayProductI18nService.update(googlePlayProductI18n));
    }

    /**
     * 删除Google Play产品多语言信息
     */
    @PreAuthorize("@ss.hasPermi('google/play:product/i18n:remove')")
    @Log(title = "Google Play产品多语言信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable List<Long> ids)
    {
        return toAjax(googlePlayProductI18nService.deleteByIds(ids));
    }


    /**
     * 查询自定义分析列
     */
    @PreAuthorize("@ss.hasPermi('google/play:product/i18n:list')")
    @GetMapping("/columns")
    public AjaxResult columns()
    {
        return AjaxResult.success(SummaryColumnUtils.get(GooglePlayProductI18n.class));
    }

    /**
     * 查询自定义分析数据
     */
    @PreAuthorize("@ss.hasPermi('google/play:product/i18n:list')")
    @RequestMapping(value = "/summary")
    public TableDataInfo summary(@RequestBody SummaryRequest query)
    {
        startPage();
        List<GooglePlayProductI18n> summary = googlePlayProductI18nService.summary(query);
        return getDataTable(summary);
    }

    /**
     * 导出自定义分析数据
     */
    @PreAuthorize("@ss.hasPermi('google/play:product/i18n:export')")
    @Log(title = "导出自定义分析数据", businessType = BusinessType.EXPORT)
    @PostMapping("/summaryExport")
    public void summaryExport(HttpServletResponse response, @RequestBody SummaryRequest query)
    {
        PageHelper.startPage(1, Integer.MAX_VALUE);
        if (null == query.getQuery()) {
            query.setQuery(new ArrayList<>());
        }
        List<GooglePlayProductI18n> list = googlePlayProductI18nService.summary(query);
        query.getGroupBy().addAll(query.getColumns());
        ExcelUtil<GooglePlayProductI18n> util = new ExcelUtil<>(GooglePlayProductI18n.class);
        util.exportExcel(response, list, "导出数据");
    }



    /**
     * 下载导入模板
     */
    @PreAuthorize("@ss.hasPermi('google/play:product/i18n:export')")
    @Log(title = "下载导入模板", businessType = BusinessType.EXPORT)
    @PostMapping("/downloadImportModule")
    public void downloadImportModule()
    {
        googlePlayProductI18nService.downloadImportModule();
    }

    /**
     * 导入数据
     */
    @PreAuthorize("@ss.hasPermi('google/play:product/i18n:export')")
    @Log(title = "导入数据", businessType = BusinessType.EXPORT)
    @PostMapping("/importData")
    public void importData(MultipartFile file)
    {

        googlePlayProductI18nService.importData(file);
    }


    /**
     * 查询自定义分析数据-汇总
     */
    @PreAuthorize("@ss.hasPermi('google/play:product/i18n:list')")
    @RequestMapping(value = "/allSummary")
    public AjaxResult allSummary(@RequestBody SummaryRequest query)
    {
        PageHelper.startPage(1, 1);
        return AjaxResult.success(googlePlayProductI18nService.allSummary(query));
    }
}
