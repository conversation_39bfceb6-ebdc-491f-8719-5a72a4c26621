package tv.shorthub.admin.controller.statistics;

import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import tv.shorthub.common.core.domain.SummaryRequest;
import tv.shorthub.common.utils.SummaryColumnUtils;
import org.springframework.web.multipart.MultipartFile;
import java.util.ArrayList;
import java.util.List;
import java.util.Date;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import tv.shorthub.common.annotation.Log;
import tv.shorthub.common.core.controller.BaseController;
import tv.shorthub.common.core.domain.AjaxResult;
import tv.shorthub.common.enums.BusinessType;
import tv.shorthub.system.domain.StatisticsDailyDramaStats;
import tv.shorthub.system.domain.StatisticsDailyOrderStats;
import tv.shorthub.system.service.IStatisticsDailyDramaStatsService;
import tv.shorthub.common.utils.poi.ExcelUtil;
import tv.shorthub.common.core.page.TableDataInfo;
import tv.shorthub.common.utils.DateUtils;
import tv.shorthub.admin.utils.StatisticsControllerPermissionUtils;
import tv.shorthub.statistics.service.IDailyDramaStatsService;
import tv.shorthub.statistics.dto.BatchUpdateRequest;

/**
 * 每日剧集分析统计Controller
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Slf4j
@RestController
@RequestMapping("/statistics/daydrama")
public class StatisticsDailyDramaStatsController extends BaseController
{
    @Autowired
    private IStatisticsDailyDramaStatsService statisticsDailyDramaStatsService;

    @Autowired
    private StatisticsControllerPermissionUtils controllerPermissionUtils;

    @Autowired
    private IDailyDramaStatsService dailyDramaStatsService;

    /**
     * 查询每日剧集分析统计列表
     */
    @PreAuthorize("@ss.hasPermi('statistics:daydrama:list')")
    @GetMapping("/list")
    public TableDataInfo list(StatisticsDailyDramaStats statisticsDailyDramaStats)
    {
        startPage();
        List<StatisticsDailyDramaStats> list = statisticsDailyDramaStatsService.selectList(statisticsDailyDramaStats);
        return getDataTable(list);
    }

    /**
     * 获取每日剧集分析统计数据汇总
     */
    @PreAuthorize("@ss.hasPermi('statistics:daydrama:query')")
    @GetMapping(value = "/getSummary")
    public AjaxResult getSummary(StatisticsDailyDramaStats statisticsDailyDramaStats)
    {
        return AjaxResult.success(statisticsDailyDramaStatsService.getSummary(statisticsDailyDramaStats));
    }

    /**
     * 获取每日剧集分析统计汇总数据
     */
    @PreAuthorize("@ss.hasPermi('statistics:daydrama:query')")
    @GetMapping("/getRangeSummary")
    public AjaxResult getRangeSummary(@RequestParam("startTime") Date startTime,
                                      @RequestParam("endTime") Date endTime,
                                      @RequestParam(value = "appid", required = false) String appid,
                                      @RequestParam(value = "tfid", required = false) String tfid,
                                      @RequestParam(value = "timezone", required = false, defaultValue = "UTC+8") String timezone,
                                      @RequestParam(value = "delivererName", required = false) String delivererName) {
        log.info("[每日剧集统计] 区间汇总查询参数 - startTime: {}, endTime: {}, appid: {}, tfid: {}, timezone: {}, delivererName: {}",
                startTime, endTime, appid, tfid, timezone, delivererName);

        // 处理按投手筛选的逻辑
        AjaxResult delivererResult = controllerPermissionUtils.handleDelivererQuery(
            delivererName, tfid, startTime, endTime,
            params -> statisticsDailyDramaStatsService.getSummaryByTfids(params.tfids, params.startTime, params.endTime, appid, timezone),
            new StatisticsDailyDramaStats()
        );
        if (delivererResult != null) {
            return delivererResult;
        }

        // 处理默认查询
        AjaxResult defaultResult = controllerPermissionUtils.handleDefaultQuery(
            appid, tfid, startTime, endTime,
            params -> statisticsDailyDramaStatsService.getSummaryByTfids(params.tfids, params.startTime, params.endTime, appid, timezone),
            params -> statisticsDailyDramaStatsService.getSummaryByCreator(params.creatorName, params.startTime, params.endTime, timezone),
            params -> statisticsDailyDramaStatsService.getRangeSummary(params.startTime, params.endTime, appid, null, timezone),
            new StatisticsDailyDramaStats()
        );
        if (defaultResult != null) {
            return defaultResult;
        }

        // 处理其他情况
        return controllerPermissionUtils.handleOtherQuery(
            appid, tfid, startTime, endTime,
            params -> statisticsDailyDramaStatsService.getRangeSummary(params.startTime, params.endTime, appid, params.tfid, timezone)
        );
    }

    /**
     * 批量统计最近N天数据
     */
    @PreAuthorize("@ss.hasPermi('statistics:daydrama:edit')")
    @Log(title = "批量统计最近N天数据", businessType = BusinessType.UPDATE)
    @PostMapping("/batchUpdate")
    public AjaxResult batchUpdate(@RequestBody BatchUpdateRequest request) {
        try {
            Integer days = request.getDays();

            // 默认统计最近7天
            if (days == null || days <= 0) {
                days = 7;
            }

            // 限制最多统计365天
            if (days > 365) {
                days = 365;
            }

            log.info("[每日剧集统计] 批量统计最近{}天数据", days);

            String result = dailyDramaStatsService.batchUpdateRecentDramaStats(days);
            return AjaxResult.success(result);
        } catch (Exception e) {
            log.error("批量统计失败", e);
            return AjaxResult.error("批量统计失败: " + e.getMessage());
        }
    }

    /**
     * 批量统计指定时间范围数据
     */
    @PreAuthorize("@ss.hasPermi('statistics:daydrama:edit')")
    @Log(title = "批量统计指定时间范围数据", businessType = BusinessType.UPDATE)
    @PostMapping("/batchUpdateRange")
    public AjaxResult batchUpdateRange(@RequestBody BatchUpdateRequest request) {
        try {
            if (!request.isValidDateRange()) {
                return AjaxResult.error("日期范围参数无效");
            }

            log.info("[每日剧集统计] 批量统计时间范围数据 - startDate: {}, endDate: {}", request.getStartDate(), request.getEndDate());

            String result = dailyDramaStatsService.batchUpdateDramaStats(request.getStartDate(), request.getEndDate());
            return AjaxResult.success(result);
        } catch (Exception e) {
            log.error("批量统计失败", e);
            return AjaxResult.error("批量统计失败: " + e.getMessage());
        }
    }

    /**
     * 导出每日剧集分析统计列表
     */
    @PreAuthorize("@ss.hasPermi('statistics:daydrama:export')")
    @Log(title = "每日剧集分析统计", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, StatisticsDailyDramaStats statisticsDailyDramaStats)
    {
        List<StatisticsDailyDramaStats> list = statisticsDailyDramaStatsService.selectList(statisticsDailyDramaStats);
        ExcelUtil<StatisticsDailyDramaStats> util = new ExcelUtil<>(StatisticsDailyDramaStats.class);
        util.exportExcel(response, list, "每日剧集分析统计数据");
    }

    /**
     * 获取每日剧集分析统计详细信息
     */
    @PreAuthorize("@ss.hasPermi('statistics:daydrama:query')")
    @GetMapping(value = "info/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(statisticsDailyDramaStatsService.getById(id));
    }

    /**
     * 新增每日剧集分析统计
     */
    @PreAuthorize("@ss.hasPermi('statistics:daydrama:add')")
    @Log(title = "每日剧集分析统计", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody StatisticsDailyDramaStats statisticsDailyDramaStats)
    {
        return toAjax(statisticsDailyDramaStatsService.insert(statisticsDailyDramaStats));
    }

    /**
     * 修改每日剧集分析统计
     */
    @PreAuthorize("@ss.hasPermi('statistics:daydrama:edit')")
    @Log(title = "每日剧集分析统计", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody StatisticsDailyDramaStats statisticsDailyDramaStats)
    {
        return toAjax(statisticsDailyDramaStatsService.update(statisticsDailyDramaStats));
    }

    /**
     * 删除每日剧集分析统计
     */
    @PreAuthorize("@ss.hasPermi('statistics:daydrama:remove')")
    @Log(title = "每日剧集分析统计", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable List<Long> ids)
    {
        return toAjax(statisticsDailyDramaStatsService.deleteByIds(ids));
    }

    /**
     * 查询自定义分析列
     */
    @PreAuthorize("@ss.hasPermi('statistics:daydrama:list')")
    @GetMapping("/columns")
    public AjaxResult columns()
    {
        return AjaxResult.success(SummaryColumnUtils.get(StatisticsDailyDramaStats.class));
    }

    /**
     * 查询自定义分析数据
     */
    @PreAuthorize("@ss.hasPermi('statistics:daydrama:list')")
    @RequestMapping(value = "/summary")
    public TableDataInfo summary(@RequestBody SummaryRequest query)
    {
        startPage();
        List<StatisticsDailyDramaStats> summary = statisticsDailyDramaStatsService.summary(query);
        return getDataTable(summary);
    }

    /**
     * 导出自定义分析数据
     */
    @PreAuthorize("@ss.hasPermi('statistics:daydrama:export')")
    @Log(title = "导出自定义分析数据", businessType = BusinessType.EXPORT)
    @PostMapping("/summaryExport")
    public void summaryExport(HttpServletResponse response, @RequestBody SummaryRequest query)
    {
        PageHelper.startPage(1, Integer.MAX_VALUE);
        if (null == query.getQuery()) {
            query.setQuery(new ArrayList<>());
        }
        List<StatisticsDailyDramaStats> list = statisticsDailyDramaStatsService.summary(query);
        query.getGroupBy().addAll(query.getColumns());
        ExcelUtil<StatisticsDailyDramaStats> util = new ExcelUtil<>(StatisticsDailyDramaStats.class);
        util.exportExcel(response, list, "导出数据");
    }

    /**
     * 下载导入模板
     */
    @PreAuthorize("@ss.hasPermi('statistics:daydrama:export')")
    @Log(title = "下载导入模板", businessType = BusinessType.EXPORT)
    @PostMapping("/downloadImportModule")
    public void downloadImportModule()
    {
        statisticsDailyDramaStatsService.downloadImportModule();
    }

    /**
     * 导入数据
     */
    @PreAuthorize("@ss.hasPermi('statistics:daydrama:export')")
    @Log(title = "导入数据", businessType = BusinessType.EXPORT)
    @PostMapping("/importData")
    public void importData(MultipartFile file)
    {
        statisticsDailyDramaStatsService.importData(file);
    }

    /**
     * 查询自定义分析数据-汇总
     */
    @PreAuthorize("@ss.hasPermi('statistics:daydrama:list')")
    @RequestMapping(value = "/allSummary")
    public AjaxResult allSummary(@RequestBody SummaryRequest query)
    {
        PageHelper.startPage(1, 1);
        return AjaxResult.success(statisticsDailyDramaStatsService.allSummary(query));
    }
}
