package tv.shorthub.admin.controller.app;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.PageHelper;
import org.springframework.web.bind.annotation.*;
import tv.shorthub.admin.service.googleplay.GooglePlaySyncServiceImpl;
import tv.shorthub.common.annotation.RateLimiter;
import tv.shorthub.common.core.cache.CacheKeyUtils;
import tv.shorthub.common.core.domain.SummaryRequest;
import tv.shorthub.common.core.domain.model.UserApp;
import tv.shorthub.common.core.redis.RedisCache;
import tv.shorthub.common.enums.LimitType;
import tv.shorthub.common.exception.ApiException;
import tv.shorthub.common.exception.ServiceException;
import tv.shorthub.common.utils.SecurityUtils;
import tv.shorthub.common.utils.SummaryColumnUtils;
import org.springframework.web.multipart.MultipartFile;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import tv.shorthub.common.annotation.Log;
import tv.shorthub.common.core.controller.BaseController;
import tv.shorthub.common.core.domain.AjaxResult;
import tv.shorthub.common.enums.BusinessType;
import tv.shorthub.system.domain.AppRecharge;
import tv.shorthub.system.service.IAppRechargeItemService;
import tv.shorthub.system.service.IAppRechargeService;
import tv.shorthub.common.utils.poi.ExcelUtil;
import tv.shorthub.common.core.page.TableDataInfo;

/**
 * 充值方案Controller
 *
 * <AUTHOR>
 * @date 2025-05-10
 */
@RestController
@RequestMapping("/app/recharge")
public class AppRechargeController extends BaseController
{
    @Autowired
    private IAppRechargeService appRechargeService;

    @Autowired
    private GooglePlaySyncServiceImpl googlePlaySyncService;

    @Autowired
    RedisCache redisCache;

    /**
     * 查询充值方案列表
     */
    @PreAuthorize("@ss.hasPermi('app:recharge:list')")
    @GetMapping("/list")
    public TableDataInfo list(AppRecharge appRecharge)
    {
        startPage();
        List<AppRecharge> list = appRechargeService.selectList(appRecharge);
        return getDataTable(list);
    }

    /**
     * 获取充值方案数据汇总
     */
    @PreAuthorize("@ss.hasPermi('app:recharge:query')")
    @GetMapping(value = "/getSummary")
    public AjaxResult getSummary(AppRecharge appRecharge)
    {
        return AjaxResult.success(appRechargeService.getSummary(appRecharge));
    }




    /**
     * 导出充值方案列表
     */
    @PreAuthorize("@ss.hasPermi('app:recharge:edit')")
    @Log(title = "同步充值方案到谷歌应用", businessType = BusinessType.INSERT)
    @GetMapping("/syncToGooglePlay")
    @RateLimiter(limitType = LimitType.USER_ID, count = 10)
    public AjaxResult syncToGooglePlay(@RequestParam String templateId)
    {
        Optional<UserApp> optional = SecurityUtils.getAppList().stream().filter(f -> f.getAppid().equals(SecurityUtils.getAppid())).findFirst();
        if (optional.isEmpty()) {
            throw new ServiceException("应用不存在");
        } else if (!optional.get().getChannel().equals("Android")) {
            throw new ServiceException("当前应用不支持同步操作");
        }

        AppRecharge appRecharge = appRechargeService.getMapper().selectOne(new QueryWrapper<AppRecharge>().eq("template_id", templateId));
        // 校验创建者权限
        if (!SecurityUtils.isBusinessAdmin() && !appRecharge.getCreateBy().equals(SecurityUtils.getUsername())) {
            throw new ServiceException("没有权限操作此数据");
        }

        if (Boolean.FALSE.equals(redisCache.redisTemplate.opsForValue().setIfAbsent(CacheKeyUtils.getRechargeToGooglePlayLock(templateId), true, 10, TimeUnit.MINUTES))) {
            throw new ServiceException("该方案已在同步中，暂时锁定，请稍后再试");
        }
        googlePlaySyncService.syncRechargeTemplateToGooglePlay(templateId, SecurityUtils.getAppid());
        return AjaxResult.success();
    }

    /**
     * 导出充值方案列表
     */
    @PreAuthorize("@ss.hasPermi('app:recharge:export')")
    @Log(title = "充值方案", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AppRecharge appRecharge)
    {
        List<AppRecharge> list = appRechargeService.selectList(appRecharge);
        ExcelUtil<AppRecharge> util = new ExcelUtil<>(AppRecharge.class);
        util.exportExcel(response, list, "充值方案数据");
    }

    /**
     * 获取充值方案详细信息
     */
    @PreAuthorize("@ss.hasPermi('app:recharge:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(appRechargeService.getById(id));
    }

    /**
     * 新增充值方案
     */
    @PreAuthorize("@ss.hasPermi('app:recharge:add')")
    @Log(title = "充值方案", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AppRecharge appRecharge)
    {
        return toAjax(appRechargeService.insert(appRecharge));
    }

    /**
     * 修改充值方案
     */
    @PreAuthorize("@ss.hasPermi('app:recharge:edit')")
    @Log(title = "充值方案", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AppRecharge appRecharge)
    {
        return toAjax(appRechargeService.update(appRecharge));
    }

    /**
     * 删除充值方案
     */
    @PreAuthorize("@ss.hasPermi('app:recharge:remove')")
    @Log(title = "充值方案", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable List<Long> ids)
    {
        throw new ApiException("暂不支持删除充值方案");
//        return toAjax(appRechargeService.deleteByIds(ids));
    }


    /**
     * 查询自定义分析列
     */
    @PreAuthorize("@ss.hasPermi('app:recharge:list')")
    @GetMapping("/columns")
    public AjaxResult columns()
    {
        return AjaxResult.success(SummaryColumnUtils.get(AppRecharge.class));
    }

    /**
     * 查询自定义分析数据
     */
    @PreAuthorize("@ss.hasPermi('app:recharge:list')")
    @RequestMapping(value = "/summary")
    public TableDataInfo summary(@RequestBody SummaryRequest query)
    {
        startPage();
        List<AppRecharge> summary = appRechargeService.summary(query);
        return getDataTable(summary);
    }

    /**
     * 导出自定义分析数据
     */
    @PreAuthorize("@ss.hasPermi('app:recharge:export')")
    @Log(title = "导出自定义分析数据", businessType = BusinessType.EXPORT)
    @PostMapping("/summaryExport")
    public void summaryExport(HttpServletResponse response, @RequestBody SummaryRequest query)
    {
        PageHelper.startPage(1, Integer.MAX_VALUE);
        if (null == query.getQuery()) {
            query.setQuery(new ArrayList<>());
        }
        List<AppRecharge> list = appRechargeService.summary(query);
        query.getGroupBy().addAll(query.getColumns());
        ExcelUtil<AppRecharge> util = new ExcelUtil<>(AppRecharge.class);
        util.exportExcel(response, list, "导出数据");
    }



    /**
     * 下载导入模板
     */
    @PreAuthorize("@ss.hasPermi('app:recharge:export')")
    @Log(title = "下载导入模板", businessType = BusinessType.EXPORT)
    @PostMapping("/downloadImportModule")
    public void downloadImportModule()
    {
        appRechargeService.downloadImportModule();
    }

    /**
     * 导入数据
     */
    @PreAuthorize("@ss.hasPermi('app:recharge:export')")
    @Log(title = "导入数据", businessType = BusinessType.EXPORT)
    @PostMapping("/importData")
    public void importData(MultipartFile file)
    {

        appRechargeService.importData(file);
    }


    /**
     * 查询自定义分析数据-汇总
     */
    @PreAuthorize("@ss.hasPermi('app:recharge:list')")
    @RequestMapping(value = "/allSummary")
    public AjaxResult allSummary(@RequestBody SummaryRequest query)
    {
        PageHelper.startPage(1, 1);
        return AjaxResult.success(appRechargeService.allSummary(query));
    }
}
