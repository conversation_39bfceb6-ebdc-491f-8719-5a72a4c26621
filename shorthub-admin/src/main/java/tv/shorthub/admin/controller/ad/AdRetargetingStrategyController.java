package tv.shorthub.admin.controller.ad;

import com.github.pagehelper.PageHelper;
import tv.shorthub.common.core.domain.SummaryRequest;
import tv.shorthub.common.exception.ApiException;
import tv.shorthub.common.utils.SummaryColumnUtils;
import org.springframework.web.multipart.MultipartFile;
import java.util.ArrayList;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import tv.shorthub.common.annotation.Log;
import tv.shorthub.common.core.controller.BaseController;
import tv.shorthub.common.core.domain.AjaxResult;
import tv.shorthub.common.enums.BusinessType;
import tv.shorthub.system.domain.AdRetargetingStrategy;
import tv.shorthub.system.service.IAdRetargetingStrategyService;
import tv.shorthub.common.utils.poi.ExcelUtil;
import tv.shorthub.common.core.page.TableDataInfo;

/**
 * 回传策略Controller
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@RestController
@RequestMapping("/ad/retargeting/strategy")
public class AdRetargetingStrategyController extends BaseController
{
    @Autowired
    private IAdRetargetingStrategyService adRetargetingStrategyService;

    /**
     * 查询回传策略列表
     */
    @PreAuthorize("@ss.hasPermi('ad/retargeting:strategy:list')")
    @GetMapping("/list")
    public TableDataInfo list(AdRetargetingStrategy adRetargetingStrategy)
    {
        startPage();
        List<AdRetargetingStrategy> list = adRetargetingStrategyService.selectList(adRetargetingStrategy);
        return getDataTable(list);
    }

    /**
     * 获取回传策略数据汇总
     */
    @PreAuthorize("@ss.hasPermi('ad/retargeting:strategy:query')")
    @GetMapping(value = "/getSummary")
    public AjaxResult getSummary(AdRetargetingStrategy adRetargetingStrategy)
    {
        return AjaxResult.success(adRetargetingStrategyService.getSummary(adRetargetingStrategy));
    }

    /**
     * 导出回传策略列表
     */
    @PreAuthorize("@ss.hasPermi('ad/retargeting:strategy:export')")
    @Log(title = "回传策略", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AdRetargetingStrategy adRetargetingStrategy)
    {
        List<AdRetargetingStrategy> list = adRetargetingStrategyService.selectList(adRetargetingStrategy);
        ExcelUtil<AdRetargetingStrategy> util = new ExcelUtil<>(AdRetargetingStrategy.class);
        util.exportExcel(response, list, "回传策略数据");
    }

    /**
     * 获取回传策略详细信息
     */
    @PreAuthorize("@ss.hasPermi('ad/retargeting:strategy:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(adRetargetingStrategyService.getById(id));
    }

    /**
     * 新增回传策略
     */
    @PreAuthorize("@ss.hasPermi('ad/retargeting:strategy:add')")
    @Log(title = "回传策略", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AdRetargetingStrategy adRetargetingStrategy)
    {
        return toAjax(adRetargetingStrategyService.insert(adRetargetingStrategy));
    }

    /**
     * 克隆回传策略
     */
    @PreAuthorize("@ss.hasPermi('ad/retargeting:strategy:add')")
    @Log(title = "克隆回传策略", businessType = BusinessType.INSERT)
    @PostMapping("/clone")
    public AjaxResult clone(@RequestBody AdRetargetingStrategy adRetargetingStrategy)
    {
        return toAjax(adRetargetingStrategyService.clone(adRetargetingStrategy));
    }

    /**
     * 修改回传策略
     */
    @PreAuthorize("@ss.hasPermi('ad/retargeting:strategy:edit')")
    @Log(title = "回传策略", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AdRetargetingStrategy adRetargetingStrategy)
    {
        return toAjax(adRetargetingStrategyService.update(adRetargetingStrategy));
    }

    /**
     * 删除回传策略
     */
    @PreAuthorize("@ss.hasPermi('ad/retargeting:strategy:remove')")
    @Log(title = "回传策略", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable List<Long> ids)
    {
        throw new ApiException("暂不支持删除回传策略");
//        return toAjax(adRetargetingStrategyService.deleteByIds(ids));
    }


    /**
     * 查询自定义分析列
     */
    @PreAuthorize("@ss.hasPermi('ad/retargeting:strategy:list')")
    @GetMapping("/columns")
    public AjaxResult columns()
    {
        return AjaxResult.success(SummaryColumnUtils.get(AdRetargetingStrategy.class));
    }

    /**
     * 查询自定义分析数据
     */
    @PreAuthorize("@ss.hasPermi('ad/retargeting:strategy:list')")
    @RequestMapping(value = "/summary")
    public TableDataInfo summary(@RequestBody SummaryRequest query)
    {
        startPage();
        List<AdRetargetingStrategy> summary = adRetargetingStrategyService.summary(query);
        return getDataTable(summary);
    }

    /**
     * 导出自定义分析数据
     */
    @PreAuthorize("@ss.hasPermi('ad/retargeting:strategy:export')")
    @Log(title = "导出自定义分析数据", businessType = BusinessType.EXPORT)
    @PostMapping("/summaryExport")
    public void summaryExport(HttpServletResponse response, @RequestBody SummaryRequest query)
    {
        PageHelper.startPage(1, Integer.MAX_VALUE);
        if (null == query.getQuery()) {
            query.setQuery(new ArrayList<>());
        }
        List<AdRetargetingStrategy> list = adRetargetingStrategyService.summary(query);
        query.getGroupBy().addAll(query.getColumns());
        ExcelUtil<AdRetargetingStrategy> util = new ExcelUtil<>(AdRetargetingStrategy.class);
        util.exportExcel(response, list, "导出数据");
    }



    /**
     * 下载导入模板
     */
    @PreAuthorize("@ss.hasPermi('ad/retargeting:strategy:export')")
    @Log(title = "下载导入模板", businessType = BusinessType.EXPORT)
    @PostMapping("/downloadImportModule")
    public void downloadImportModule()
    {
        adRetargetingStrategyService.downloadImportModule();
    }

    /**
     * 导入数据
     */
    @PreAuthorize("@ss.hasPermi('ad/retargeting:strategy:export')")
    @Log(title = "导入数据", businessType = BusinessType.EXPORT)
    @PostMapping("/importData")
    public void importData(MultipartFile file)
    {

        adRetargetingStrategyService.importData(file);
    }


    /**
     * 查询自定义分析数据-汇总
     */
    @PreAuthorize("@ss.hasPermi('ad/retargeting:strategy:list')")
    @RequestMapping(value = "/allSummary")
    public AjaxResult allSummary(@RequestBody SummaryRequest query)
    {
        PageHelper.startPage(1, 1);
        return AjaxResult.success(adRetargetingStrategyService.allSummary(query));
    }
}
