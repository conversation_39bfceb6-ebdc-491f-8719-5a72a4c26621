package tv.shorthub.admin.controller.crypto;

import com.github.pagehelper.PageHelper;
import tv.shorthub.common.core.domain.SummaryRequest;
import tv.shorthub.common.utils.SummaryColumnUtils;
import org.springframework.web.multipart.MultipartFile;
import java.util.ArrayList;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import tv.shorthub.common.annotation.Log;
import tv.shorthub.common.core.controller.BaseController;
import tv.shorthub.common.core.domain.AjaxResult;
import tv.shorthub.common.enums.BusinessType;
import tv.shorthub.system.domain.CryptoMonitorLog;
import tv.shorthub.system.service.ICryptoMonitorLogService;
import tv.shorthub.common.utils.poi.ExcelUtil;
import tv.shorthub.common.core.page.TableDataInfo;

/**
 * 虚拟货币交易监听记录Controller
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
@RestController
@RequestMapping("/crypto/log")
public class CryptoMonitorLogController extends BaseController
{
    @Autowired
    private ICryptoMonitorLogService cryptoMonitorLogService;

    /**
     * 查询虚拟货币交易监听记录列表
     */
    @PreAuthorize("@ss.hasPermi('crypto:log:list')")
    @GetMapping("/list")
    public TableDataInfo list(CryptoMonitorLog cryptoMonitorLog)
    {
        startPage();
        List<CryptoMonitorLog> list = cryptoMonitorLogService.selectList(cryptoMonitorLog);
        return getDataTable(list);
    }

    /**
     * 获取虚拟货币交易监听记录数据汇总
     */
    @PreAuthorize("@ss.hasPermi('crypto:log:query')")
    @GetMapping(value = "/getSummary")
    public AjaxResult getSummary(CryptoMonitorLog cryptoMonitorLog)
    {
        return AjaxResult.success(cryptoMonitorLogService.getSummary(cryptoMonitorLog));
    }

    /**
     * 导出虚拟货币交易监听记录列表
     */
    @PreAuthorize("@ss.hasPermi('crypto:log:export')")
    @Log(title = "虚拟货币交易监听记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CryptoMonitorLog cryptoMonitorLog)
    {
        List<CryptoMonitorLog> list = cryptoMonitorLogService.selectList(cryptoMonitorLog);
        ExcelUtil<CryptoMonitorLog> util = new ExcelUtil<>(CryptoMonitorLog.class);
        util.exportExcel(response, list, "虚拟货币交易监听记录数据");
    }

    /**
     * 获取虚拟货币交易监听记录详细信息
     */
    @PreAuthorize("@ss.hasPermi('crypto:log:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(cryptoMonitorLogService.getById(id));
    }

    /**
     * 新增虚拟货币交易监听记录
     */
    @PreAuthorize("@ss.hasPermi('crypto:log:add')")
    @Log(title = "虚拟货币交易监听记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CryptoMonitorLog cryptoMonitorLog)
    {
        return toAjax(cryptoMonitorLogService.insert(cryptoMonitorLog));
    }

    /**
     * 修改虚拟货币交易监听记录
     */
    @PreAuthorize("@ss.hasPermi('crypto:log:edit')")
    @Log(title = "虚拟货币交易监听记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CryptoMonitorLog cryptoMonitorLog)
    {
        return toAjax(cryptoMonitorLogService.update(cryptoMonitorLog));
    }

    /**
     * 删除虚拟货币交易监听记录
     */
    @PreAuthorize("@ss.hasPermi('crypto:log:remove')")
    @Log(title = "虚拟货币交易监听记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable List<Long> ids)
    {
        return toAjax(cryptoMonitorLogService.deleteByIds(ids));
    }


    /**
     * 查询自定义分析列
     */
    @PreAuthorize("@ss.hasPermi('crypto:log:list')")
    @GetMapping("/columns")
    public AjaxResult columns()
    {
        return AjaxResult.success(SummaryColumnUtils.get(CryptoMonitorLog.class));
    }

    /**
     * 查询自定义分析数据
     */
    @PreAuthorize("@ss.hasPermi('crypto:log:list')")
    @RequestMapping(value = "/summary")
    public TableDataInfo summary(@RequestBody SummaryRequest query)
    {
        startPage();
        List<CryptoMonitorLog> summary = cryptoMonitorLogService.summary(query);
        return getDataTable(summary);
    }

    /**
     * 导出自定义分析数据
     */
    @PreAuthorize("@ss.hasPermi('crypto:log:export')")
    @Log(title = "导出自定义分析数据", businessType = BusinessType.EXPORT)
    @PostMapping("/summaryExport")
    public void summaryExport(HttpServletResponse response, @RequestBody SummaryRequest query)
    {
        PageHelper.startPage(1, Integer.MAX_VALUE);
        if (null == query.getQuery()) {
            query.setQuery(new ArrayList<>());
        }
        List<CryptoMonitorLog> list = cryptoMonitorLogService.summary(query);
        query.getGroupBy().addAll(query.getColumns());
        ExcelUtil<CryptoMonitorLog> util = new ExcelUtil<>(CryptoMonitorLog.class);
        util.exportExcel(response, list, "导出数据");
    }



    /**
     * 下载导入模板
     */
    @PreAuthorize("@ss.hasPermi('crypto:log:export')")
    @Log(title = "下载导入模板", businessType = BusinessType.EXPORT)
    @PostMapping("/downloadImportModule")
    public void downloadImportModule()
    {
        cryptoMonitorLogService.downloadImportModule();
    }

    /**
     * 导入数据
     */
    @PreAuthorize("@ss.hasPermi('crypto:log:export')")
    @Log(title = "导入数据", businessType = BusinessType.EXPORT)
    @PostMapping("/importData")
    public void importData(MultipartFile file)
    {

        cryptoMonitorLogService.importData(file);
    }


    /**
     * 查询自定义分析数据-汇总
     */
    @PreAuthorize("@ss.hasPermi('crypto:log:list')")
    @RequestMapping(value = "/allSummary")
    public AjaxResult allSummary(@RequestBody SummaryRequest query)
    {
        PageHelper.startPage(1, 1);
        return AjaxResult.success(cryptoMonitorLogService.allSummary(query));
    }
}
