package tv.shorthub.admin.controller.airwallex;

import com.github.pagehelper.PageHelper;
import tv.shorthub.common.core.domain.SummaryRequest;
import tv.shorthub.common.utils.SummaryColumnUtils;
import org.springframework.web.multipart.MultipartFile;
import java.util.ArrayList;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import tv.shorthub.common.annotation.Log;
import tv.shorthub.common.core.controller.BaseController;
import tv.shorthub.common.core.domain.AjaxResult;
import tv.shorthub.common.enums.BusinessType;
import tv.shorthub.system.domain.AirwallexUserPlan;
import tv.shorthub.system.service.IAirwallexUserPlanService;
import tv.shorthub.common.utils.poi.ExcelUtil;
import tv.shorthub.common.core.page.TableDataInfo;

/**
 * airwallex用户扣费计划Controller
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
@RestController
@RequestMapping("/pay/airwallex/plan")
public class AirwallexUserPlanController extends BaseController
{
    @Autowired
    private IAirwallexUserPlanService airwallexUserPlanService;

    /**
     * 查询airwallex用户扣费计划列表
     */
    @PreAuthorize("@ss.hasPermi('pay/airwallex:plan:list')")
    @GetMapping("/list")
    public TableDataInfo list(AirwallexUserPlan airwallexUserPlan)
    {
        startPage();
        List<AirwallexUserPlan> list = airwallexUserPlanService.selectList(airwallexUserPlan);
        return getDataTable(list);
    }

    /**
     * 获取airwallex用户扣费计划数据汇总
     */
    @PreAuthorize("@ss.hasPermi('pay/airwallex:plan:query')")
    @GetMapping(value = "/getSummary")
    public AjaxResult getSummary(AirwallexUserPlan airwallexUserPlan)
    {
        return AjaxResult.success(airwallexUserPlanService.getSummary(airwallexUserPlan));
    }

    /**
     * 导出airwallex用户扣费计划列表
     */
    @PreAuthorize("@ss.hasPermi('pay/airwallex:plan:export')")
    @Log(title = "airwallex用户扣费计划", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AirwallexUserPlan airwallexUserPlan)
    {
        List<AirwallexUserPlan> list = airwallexUserPlanService.selectList(airwallexUserPlan);
        ExcelUtil<AirwallexUserPlan> util = new ExcelUtil<>(AirwallexUserPlan.class);
        util.exportExcel(response, list, "airwallex用户扣费计划数据");
    }

    /**
     * 获取airwallex用户扣费计划详细信息
     */
    @PreAuthorize("@ss.hasPermi('pay/airwallex:plan:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(airwallexUserPlanService.getById(id));
    }

    /**
     * 新增airwallex用户扣费计划
     */
    @PreAuthorize("@ss.hasPermi('pay/airwallex:plan:add')")
    @Log(title = "airwallex用户扣费计划", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AirwallexUserPlan airwallexUserPlan)
    {
        return toAjax(airwallexUserPlanService.insert(airwallexUserPlan));
    }

    /**
     * 修改airwallex用户扣费计划
     */
    @PreAuthorize("@ss.hasPermi('pay/airwallex:plan:edit')")
    @Log(title = "airwallex用户扣费计划", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AirwallexUserPlan airwallexUserPlan)
    {
        return toAjax(airwallexUserPlanService.update(airwallexUserPlan));
    }

    /**
     * 删除airwallex用户扣费计划
     */
    @PreAuthorize("@ss.hasPermi('pay/airwallex:plan:remove')")
    @Log(title = "airwallex用户扣费计划", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable List<Long> ids)
    {
        return toAjax(airwallexUserPlanService.deleteByIds(ids));
    }


    /**
     * 查询自定义分析列
     */
    @PreAuthorize("@ss.hasPermi('pay/airwallex:plan:list')")
    @GetMapping("/columns")
    public AjaxResult columns()
    {
        return AjaxResult.success(SummaryColumnUtils.get(AirwallexUserPlan.class));
    }

    /**
     * 查询自定义分析数据
     */
    @PreAuthorize("@ss.hasPermi('pay/airwallex:plan:list')")
    @RequestMapping(value = "/summary")
    public TableDataInfo summary(@RequestBody SummaryRequest query)
    {
        startPage();
        List<AirwallexUserPlan> summary = airwallexUserPlanService.summary(query);
        return getDataTable(summary);
    }

    /**
     * 导出自定义分析数据
     */
    @PreAuthorize("@ss.hasPermi('pay/airwallex:plan:export')")
    @Log(title = "导出自定义分析数据", businessType = BusinessType.EXPORT)
    @PostMapping("/summaryExport")
    public void summaryExport(HttpServletResponse response, @RequestBody SummaryRequest query)
    {
        PageHelper.startPage(1, Integer.MAX_VALUE);
        if (null == query.getQuery()) {
            query.setQuery(new ArrayList<>());
        }
        List<AirwallexUserPlan> list = airwallexUserPlanService.summary(query);
        query.getGroupBy().addAll(query.getColumns());
        ExcelUtil<AirwallexUserPlan> util = new ExcelUtil<>(AirwallexUserPlan.class);
        util.exportExcel(response, list, "导出数据");
    }



    /**
     * 下载导入模板
     */
    @PreAuthorize("@ss.hasPermi('pay/airwallex:plan:export')")
    @Log(title = "下载导入模板", businessType = BusinessType.EXPORT)
    @PostMapping("/downloadImportModule")
    public void downloadImportModule()
    {
        airwallexUserPlanService.downloadImportModule();
    }

    /**
     * 导入数据
     */
    @PreAuthorize("@ss.hasPermi('pay/airwallex:plan:export')")
    @Log(title = "导入数据", businessType = BusinessType.EXPORT)
    @PostMapping("/importData")
    public void importData(MultipartFile file)
    {

        airwallexUserPlanService.importData(file);
    }


    /**
     * 查询自定义分析数据-汇总
     */
    @PreAuthorize("@ss.hasPermi('pay/airwallex:plan:list')")
    @RequestMapping(value = "/allSummary")
    public AjaxResult allSummary(@RequestBody SummaryRequest query)
    {
        PageHelper.startPage(1, 1);
        return AjaxResult.success(airwallexUserPlanService.allSummary(query));
    }
}
