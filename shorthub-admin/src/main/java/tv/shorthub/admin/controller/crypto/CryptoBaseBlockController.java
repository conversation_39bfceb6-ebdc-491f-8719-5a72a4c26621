package tv.shorthub.admin.controller.crypto;

import com.github.pagehelper.PageHelper;
import tv.shorthub.common.core.domain.SummaryRequest;
import tv.shorthub.common.utils.SummaryColumnUtils;
import org.springframework.web.multipart.MultipartFile;
import java.util.ArrayList;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import tv.shorthub.common.annotation.Log;
import tv.shorthub.common.core.controller.BaseController;
import tv.shorthub.common.core.domain.AjaxResult;
import tv.shorthub.common.enums.BusinessType;
import tv.shorthub.system.domain.CryptoBaseBlock;
import tv.shorthub.system.service.ICryptoBaseBlockService;
import tv.shorthub.common.utils.poi.ExcelUtil;
import tv.shorthub.common.core.page.TableDataInfo;

/**
 * BASE区块链区块Controller
 *
 * <AUTHOR>
 * @date 2025-08-05
 */
@RestController
@RequestMapping("/crypto/blockbase")
public class CryptoBaseBlockController extends BaseController
{
    @Autowired
    private ICryptoBaseBlockService cryptoBaseBlockService;

    /**
     * 查询BASE区块链区块列表
     */
    @PreAuthorize("@ss.hasPermi('crypto:blockbase:list')")
    @GetMapping("/list")
    public TableDataInfo list(CryptoBaseBlock cryptoBaseBlock)
    {
        startPage();
        List<CryptoBaseBlock> list = cryptoBaseBlockService.selectList(cryptoBaseBlock);
        return getDataTable(list);
    }

    /**
     * 获取BASE区块链区块数据汇总
     */
    @PreAuthorize("@ss.hasPermi('crypto:blockbase:query')")
    @GetMapping(value = "/getSummary")
    public AjaxResult getSummary(CryptoBaseBlock cryptoBaseBlock)
    {
        return AjaxResult.success(cryptoBaseBlockService.getSummary(cryptoBaseBlock));
    }

    /**
     * 导出BASE区块链区块列表
     */
    @PreAuthorize("@ss.hasPermi('crypto:blockbase:export')")
    @Log(title = "BASE区块链区块", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CryptoBaseBlock cryptoBaseBlock)
    {
        List<CryptoBaseBlock> list = cryptoBaseBlockService.selectList(cryptoBaseBlock);
        ExcelUtil<CryptoBaseBlock> util = new ExcelUtil<>(CryptoBaseBlock.class);
        util.exportExcel(response, list, "BASE区块链区块数据");
    }

    /**
     * 获取BASE区块链区块详细信息
     */
    @PreAuthorize("@ss.hasPermi('crypto:blockbase:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(cryptoBaseBlockService.getById(id));
    }

    /**
     * 新增BASE区块链区块
     */
    @PreAuthorize("@ss.hasPermi('crypto:blockbase:add')")
    @Log(title = "BASE区块链区块", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CryptoBaseBlock cryptoBaseBlock)
    {
        return toAjax(cryptoBaseBlockService.insert(cryptoBaseBlock));
    }

    /**
     * 修改BASE区块链区块
     */
    @PreAuthorize("@ss.hasPermi('crypto:blockbase:edit')")
    @Log(title = "BASE区块链区块", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CryptoBaseBlock cryptoBaseBlock)
    {
        return toAjax(cryptoBaseBlockService.update(cryptoBaseBlock));
    }

    /**
     * 删除BASE区块链区块
     */
    @PreAuthorize("@ss.hasPermi('crypto:blockbase:remove')")
    @Log(title = "BASE区块链区块", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable List<Long> ids)
    {
        return toAjax(cryptoBaseBlockService.deleteByIds(ids));
    }


    /**
     * 查询自定义分析列
     */
    @PreAuthorize("@ss.hasPermi('crypto:blockbase:list')")
    @GetMapping("/columns")
    public AjaxResult columns()
    {
        return AjaxResult.success(SummaryColumnUtils.get(CryptoBaseBlock.class));
    }

    /**
     * 查询自定义分析数据
     */
    @PreAuthorize("@ss.hasPermi('crypto:blockbase:list')")
    @RequestMapping(value = "/summary")
    public TableDataInfo summary(@RequestBody SummaryRequest query)
    {
        startPage();
        List<CryptoBaseBlock> summary = cryptoBaseBlockService.summary(query);
        return getDataTable(summary);
    }

    /**
     * 导出自定义分析数据
     */
    @PreAuthorize("@ss.hasPermi('crypto:blockbase:export')")
    @Log(title = "导出自定义分析数据", businessType = BusinessType.EXPORT)
    @PostMapping("/summaryExport")
    public void summaryExport(HttpServletResponse response, @RequestBody SummaryRequest query)
    {
        PageHelper.startPage(1, Integer.MAX_VALUE);
        if (null == query.getQuery()) {
            query.setQuery(new ArrayList<>());
        }
        List<CryptoBaseBlock> list = cryptoBaseBlockService.summary(query);
        query.getGroupBy().addAll(query.getColumns());
        ExcelUtil<CryptoBaseBlock> util = new ExcelUtil<>(CryptoBaseBlock.class);
        util.exportExcel(response, list, "导出数据");
    }



    /**
     * 下载导入模板
     */
    @PreAuthorize("@ss.hasPermi('crypto:blockbase:export')")
    @Log(title = "下载导入模板", businessType = BusinessType.EXPORT)
    @PostMapping("/downloadImportModule")
    public void downloadImportModule()
    {
        cryptoBaseBlockService.downloadImportModule();
    }

    /**
     * 导入数据
     */
    @PreAuthorize("@ss.hasPermi('crypto:blockbase:export')")
    @Log(title = "导入数据", businessType = BusinessType.EXPORT)
    @PostMapping("/importData")
    public void importData(MultipartFile file)
    {

        cryptoBaseBlockService.importData(file);
    }


    /**
     * 查询自定义分析数据-汇总
     */
    @PreAuthorize("@ss.hasPermi('crypto:blockbase:list')")
    @RequestMapping(value = "/allSummary")
    public AjaxResult allSummary(@RequestBody SummaryRequest query)
    {
        PageHelper.startPage(1, 1);
        return AjaxResult.success(cryptoBaseBlockService.allSummary(query));
    }
}
