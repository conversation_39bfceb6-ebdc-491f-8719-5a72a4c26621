package tv.shorthub.admin.controller.paypal;

import com.github.pagehelper.PageHelper;
import tv.shorthub.common.core.domain.SummaryRequest;
import tv.shorthub.common.utils.SummaryColumnUtils;
import org.springframework.web.multipart.MultipartFile;
import java.util.ArrayList;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import tv.shorthub.common.annotation.Log;
import tv.shorthub.common.core.controller.BaseController;
import tv.shorthub.common.core.domain.AjaxResult;
import tv.shorthub.common.enums.BusinessType;
import tv.shorthub.system.domain.PaypalWebhookLog;
import tv.shorthub.system.service.IPaypalWebhookLogService;
import tv.shorthub.common.utils.poi.ExcelUtil;
import tv.shorthub.common.core.page.TableDataInfo;

/**
 * webhook事件日志Controller
 *
 * <AUTHOR>
 * @date 2025-05-27
 */
@RestController
@RequestMapping("/app/paypal/webhook")
public class PaypalWebhookLogController extends BaseController
{
    @Autowired
    private IPaypalWebhookLogService paypalWebhookLogService;

    /**
     * 查询webhook事件日志列表
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:webhook:list')")
    @GetMapping("/list")
    public TableDataInfo list(PaypalWebhookLog paypalWebhookLog)
    {
        startPage();
        List<PaypalWebhookLog> list = paypalWebhookLogService.selectList(paypalWebhookLog);
        return getDataTable(list);
    }

    /**
     * 获取webhook事件日志数据汇总
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:webhook:query')")
    @GetMapping(value = "/getSummary")
    public AjaxResult getSummary(PaypalWebhookLog paypalWebhookLog)
    {
        return AjaxResult.success(paypalWebhookLogService.getSummary(paypalWebhookLog));
    }

    /**
     * 导出webhook事件日志列表
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:webhook:export')")
    @Log(title = "webhook事件日志", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PaypalWebhookLog paypalWebhookLog)
    {
        List<PaypalWebhookLog> list = paypalWebhookLogService.selectList(paypalWebhookLog);
        ExcelUtil<PaypalWebhookLog> util = new ExcelUtil<>(PaypalWebhookLog.class);
        util.exportExcel(response, list, "webhook事件日志数据");
    }

    /**
     * 获取webhook事件日志详细信息
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:webhook:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(paypalWebhookLogService.getById(id));
    }

    /**
     * 新增webhook事件日志
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:webhook:add')")
    @Log(title = "webhook事件日志", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PaypalWebhookLog paypalWebhookLog)
    {
        return toAjax(paypalWebhookLogService.insert(paypalWebhookLog));
    }

    /**
     * 修改webhook事件日志
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:webhook:edit')")
    @Log(title = "webhook事件日志", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PaypalWebhookLog paypalWebhookLog)
    {
        return toAjax(paypalWebhookLogService.update(paypalWebhookLog));
    }

    /**
     * 删除webhook事件日志
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:webhook:remove')")
    @Log(title = "webhook事件日志", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable List<Long> ids)
    {
        return toAjax(paypalWebhookLogService.deleteByIds(ids));
    }


    /**
     * 查询自定义分析列
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:webhook:list')")
    @GetMapping("/columns")
    public AjaxResult columns()
    {
        return AjaxResult.success(SummaryColumnUtils.get(PaypalWebhookLog.class));
    }

    /**
     * 查询自定义分析数据
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:webhook:list')")
    @RequestMapping(value = "/summary")
    public TableDataInfo summary(@RequestBody SummaryRequest query)
    {
        startPage();
        List<PaypalWebhookLog> summary = paypalWebhookLogService.summary(query);
        return getDataTable(summary);
    }

    /**
     * 导出自定义分析数据
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:webhook:export')")
    @Log(title = "导出自定义分析数据", businessType = BusinessType.EXPORT)
    @PostMapping("/summaryExport")
    public void summaryExport(HttpServletResponse response, @RequestBody SummaryRequest query)
    {
        PageHelper.startPage(1, Integer.MAX_VALUE);
        if (null == query.getQuery()) {
            query.setQuery(new ArrayList<>());
        }
        List<PaypalWebhookLog> list = paypalWebhookLogService.summary(query);
        query.getGroupBy().addAll(query.getColumns());
        ExcelUtil<PaypalWebhookLog> util = new ExcelUtil<>(PaypalWebhookLog.class);
        util.exportExcel(response, list, "导出数据");
    }



    /**
     * 下载导入模板
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:webhook:export')")
    @Log(title = "下载导入模板", businessType = BusinessType.EXPORT)
    @PostMapping("/downloadImportModule")
    public void downloadImportModule()
    {
        paypalWebhookLogService.downloadImportModule();
    }

    /**
     * 导入数据
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:webhook:export')")
    @Log(title = "导入数据", businessType = BusinessType.EXPORT)
    @PostMapping("/importData")
    public void importData(MultipartFile file)
    {

        paypalWebhookLogService.importData(file);
    }


    /**
     * 查询自定义分析数据-汇总
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:webhook:list')")
    @RequestMapping(value = "/allSummary")
    public AjaxResult allSummary(@RequestBody SummaryRequest query)
    {
        PageHelper.startPage(1, 1);
        return AjaxResult.success(paypalWebhookLogService.allSummary(query));
    }
}
