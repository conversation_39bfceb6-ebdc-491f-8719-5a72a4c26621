package tv.shorthub.admin.controller.airwallex;

import com.github.pagehelper.PageHelper;
import tv.shorthub.common.core.domain.SummaryRequest;
import tv.shorthub.common.utils.SummaryColumnUtils;
import org.springframework.web.multipart.MultipartFile;
import java.util.ArrayList;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import tv.shorthub.common.annotation.Log;
import tv.shorthub.common.core.controller.BaseController;
import tv.shorthub.common.core.domain.AjaxResult;
import tv.shorthub.common.enums.BusinessType;
import tv.shorthub.system.domain.AirwallexWebhookLog;
import tv.shorthub.system.service.IAirwallexWebhookLogService;
import tv.shorthub.common.utils.poi.ExcelUtil;
import tv.shorthub.common.core.page.TableDataInfo;

/**
 * webhook事件日志Controller
 *
 * <AUTHOR>
 * @date 2025-07-19
 */
@RestController
@RequestMapping("/pay/airwallex/webhooklog")
public class AirwallexWebhookLogController extends BaseController
{
    @Autowired
    private IAirwallexWebhookLogService airwallexWebhookLogService;

    /**
     * 查询webhook事件日志列表
     */
    @PreAuthorize("@ss.hasPermi('pay/airwallex:webhooklog:list')")
    @GetMapping("/list")
    public TableDataInfo list(AirwallexWebhookLog airwallexWebhookLog)
    {
        startPage();
        List<AirwallexWebhookLog> list = airwallexWebhookLogService.selectList(airwallexWebhookLog);
        return getDataTable(list);
    }

    /**
     * 获取webhook事件日志数据汇总
     */
    @PreAuthorize("@ss.hasPermi('pay/airwallex:webhooklog:query')")
    @GetMapping(value = "/getSummary")
    public AjaxResult getSummary(AirwallexWebhookLog airwallexWebhookLog)
    {
        return AjaxResult.success(airwallexWebhookLogService.getSummary(airwallexWebhookLog));
    }

    /**
     * 导出webhook事件日志列表
     */
    @PreAuthorize("@ss.hasPermi('pay/airwallex:webhooklog:export')")
    @Log(title = "webhook事件日志", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AirwallexWebhookLog airwallexWebhookLog)
    {
        List<AirwallexWebhookLog> list = airwallexWebhookLogService.selectList(airwallexWebhookLog);
        ExcelUtil<AirwallexWebhookLog> util = new ExcelUtil<>(AirwallexWebhookLog.class);
        util.exportExcel(response, list, "webhook事件日志数据");
    }

    /**
     * 获取webhook事件日志详细信息
     */
    @PreAuthorize("@ss.hasPermi('pay/airwallex:webhooklog:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(airwallexWebhookLogService.getById(id));
    }

    /**
     * 新增webhook事件日志
     */
    @PreAuthorize("@ss.hasPermi('pay/airwallex:webhooklog:add')")
    @Log(title = "webhook事件日志", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AirwallexWebhookLog airwallexWebhookLog)
    {
        return toAjax(airwallexWebhookLogService.insert(airwallexWebhookLog));
    }

    /**
     * 修改webhook事件日志
     */
    @PreAuthorize("@ss.hasPermi('pay/airwallex:webhooklog:edit')")
    @Log(title = "webhook事件日志", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AirwallexWebhookLog airwallexWebhookLog)
    {
        return toAjax(airwallexWebhookLogService.update(airwallexWebhookLog));
    }

    /**
     * 删除webhook事件日志
     */
    @PreAuthorize("@ss.hasPermi('pay/airwallex:webhooklog:remove')")
    @Log(title = "webhook事件日志", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable List<Long> ids)
    {
        return toAjax(airwallexWebhookLogService.deleteByIds(ids));
    }


    /**
     * 查询自定义分析列
     */
    @PreAuthorize("@ss.hasPermi('pay/airwallex:webhooklog:list')")
    @GetMapping("/columns")
    public AjaxResult columns()
    {
        return AjaxResult.success(SummaryColumnUtils.get(AirwallexWebhookLog.class));
    }

    /**
     * 查询自定义分析数据
     */
    @PreAuthorize("@ss.hasPermi('pay/airwallex:webhooklog:list')")
    @RequestMapping(value = "/summary")
    public TableDataInfo summary(@RequestBody SummaryRequest query)
    {
        startPage();
        List<AirwallexWebhookLog> summary = airwallexWebhookLogService.summary(query);
        return getDataTable(summary);
    }

    /**
     * 导出自定义分析数据
     */
    @PreAuthorize("@ss.hasPermi('pay/airwallex:webhooklog:export')")
    @Log(title = "导出自定义分析数据", businessType = BusinessType.EXPORT)
    @PostMapping("/summaryExport")
    public void summaryExport(HttpServletResponse response, @RequestBody SummaryRequest query)
    {
        PageHelper.startPage(1, Integer.MAX_VALUE);
        if (null == query.getQuery()) {
            query.setQuery(new ArrayList<>());
        }
        List<AirwallexWebhookLog> list = airwallexWebhookLogService.summary(query);
        query.getGroupBy().addAll(query.getColumns());
        ExcelUtil<AirwallexWebhookLog> util = new ExcelUtil<>(AirwallexWebhookLog.class);
        util.exportExcel(response, list, "导出数据");
    }



    /**
     * 下载导入模板
     */
    @PreAuthorize("@ss.hasPermi('pay/airwallex:webhooklog:export')")
    @Log(title = "下载导入模板", businessType = BusinessType.EXPORT)
    @PostMapping("/downloadImportModule")
    public void downloadImportModule()
    {
        airwallexWebhookLogService.downloadImportModule();
    }

    /**
     * 导入数据
     */
    @PreAuthorize("@ss.hasPermi('pay/airwallex:webhooklog:export')")
    @Log(title = "导入数据", businessType = BusinessType.EXPORT)
    @PostMapping("/importData")
    public void importData(MultipartFile file)
    {

        airwallexWebhookLogService.importData(file);
    }


    /**
     * 查询自定义分析数据-汇总
     */
    @PreAuthorize("@ss.hasPermi('pay/airwallex:webhooklog:list')")
    @RequestMapping(value = "/allSummary")
    public AjaxResult allSummary(@RequestBody SummaryRequest query)
    {
        PageHelper.startPage(1, 1);
        return AjaxResult.success(airwallexWebhookLogService.allSummary(query));
    }
}
