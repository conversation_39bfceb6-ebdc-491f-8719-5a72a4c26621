package tv.shorthub.admin.controller.crypto;

import com.github.pagehelper.PageHelper;
import tv.shorthub.common.core.domain.SummaryRequest;
import tv.shorthub.common.utils.SummaryColumnUtils;
import org.springframework.web.multipart.MultipartFile;
import java.util.ArrayList;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import tv.shorthub.common.annotation.Log;
import tv.shorthub.common.core.controller.BaseController;
import tv.shorthub.common.core.domain.AjaxResult;
import tv.shorthub.common.enums.BusinessType;
import tv.shorthub.system.domain.CryptoDepositRecord;
import tv.shorthub.system.service.ICryptoDepositRecordService;
import tv.shorthub.common.utils.poi.ExcelUtil;
import tv.shorthub.common.core.page.TableDataInfo;

/**
 * 虚拟货币充值记录Controller
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
@RestController
@RequestMapping("/crypto/record")
public class CryptoDepositRecordController extends BaseController
{
    @Autowired
    private ICryptoDepositRecordService cryptoDepositRecordService;

    /**
     * 查询虚拟货币充值记录列表
     */
    @PreAuthorize("@ss.hasPermi('crypto:record:list')")
    @GetMapping("/list")
    public TableDataInfo list(CryptoDepositRecord cryptoDepositRecord)
    {
        startPage();
        List<CryptoDepositRecord> list = cryptoDepositRecordService.selectList(cryptoDepositRecord);
        return getDataTable(list);
    }

    /**
     * 获取虚拟货币充值记录数据汇总
     */
    @PreAuthorize("@ss.hasPermi('crypto:record:query')")
    @GetMapping(value = "/getSummary")
    public AjaxResult getSummary(CryptoDepositRecord cryptoDepositRecord)
    {
        return AjaxResult.success(cryptoDepositRecordService.getSummary(cryptoDepositRecord));
    }

    /**
     * 导出虚拟货币充值记录列表
     */
    @PreAuthorize("@ss.hasPermi('crypto:record:export')")
    @Log(title = "虚拟货币充值记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CryptoDepositRecord cryptoDepositRecord)
    {
        List<CryptoDepositRecord> list = cryptoDepositRecordService.selectList(cryptoDepositRecord);
        ExcelUtil<CryptoDepositRecord> util = new ExcelUtil<>(CryptoDepositRecord.class);
        util.exportExcel(response, list, "虚拟货币充值记录数据");
    }

    /**
     * 获取虚拟货币充值记录详细信息
     */
    @PreAuthorize("@ss.hasPermi('crypto:record:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(cryptoDepositRecordService.getById(id));
    }

    /**
     * 新增虚拟货币充值记录
     */
    @PreAuthorize("@ss.hasPermi('crypto:record:add')")
    @Log(title = "虚拟货币充值记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CryptoDepositRecord cryptoDepositRecord)
    {
        return toAjax(cryptoDepositRecordService.insert(cryptoDepositRecord));
    }

    /**
     * 修改虚拟货币充值记录
     */
    @PreAuthorize("@ss.hasPermi('crypto:record:edit')")
    @Log(title = "虚拟货币充值记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CryptoDepositRecord cryptoDepositRecord)
    {
        return toAjax(cryptoDepositRecordService.update(cryptoDepositRecord));
    }

    /**
     * 删除虚拟货币充值记录
     */
    @PreAuthorize("@ss.hasPermi('crypto:record:remove')")
    @Log(title = "虚拟货币充值记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable List<Long> ids)
    {
        return toAjax(cryptoDepositRecordService.deleteByIds(ids));
    }


    /**
     * 查询自定义分析列
     */
    @PreAuthorize("@ss.hasPermi('crypto:record:list')")
    @GetMapping("/columns")
    public AjaxResult columns()
    {
        return AjaxResult.success(SummaryColumnUtils.get(CryptoDepositRecord.class));
    }

    /**
     * 查询自定义分析数据
     */
    @PreAuthorize("@ss.hasPermi('crypto:record:list')")
    @RequestMapping(value = "/summary")
    public TableDataInfo summary(@RequestBody SummaryRequest query)
    {
        startPage();
        List<CryptoDepositRecord> summary = cryptoDepositRecordService.summary(query);
        return getDataTable(summary);
    }

    /**
     * 导出自定义分析数据
     */
    @PreAuthorize("@ss.hasPermi('crypto:record:export')")
    @Log(title = "导出自定义分析数据", businessType = BusinessType.EXPORT)
    @PostMapping("/summaryExport")
    public void summaryExport(HttpServletResponse response, @RequestBody SummaryRequest query)
    {
        PageHelper.startPage(1, Integer.MAX_VALUE);
        if (null == query.getQuery()) {
            query.setQuery(new ArrayList<>());
        }
        List<CryptoDepositRecord> list = cryptoDepositRecordService.summary(query);
        query.getGroupBy().addAll(query.getColumns());
        ExcelUtil<CryptoDepositRecord> util = new ExcelUtil<>(CryptoDepositRecord.class);
        util.exportExcel(response, list, "导出数据");
    }



    /**
     * 下载导入模板
     */
    @PreAuthorize("@ss.hasPermi('crypto:record:export')")
    @Log(title = "下载导入模板", businessType = BusinessType.EXPORT)
    @PostMapping("/downloadImportModule")
    public void downloadImportModule()
    {
        cryptoDepositRecordService.downloadImportModule();
    }

    /**
     * 导入数据
     */
    @PreAuthorize("@ss.hasPermi('crypto:record:export')")
    @Log(title = "导入数据", businessType = BusinessType.EXPORT)
    @PostMapping("/importData")
    public void importData(MultipartFile file)
    {

        cryptoDepositRecordService.importData(file);
    }


    /**
     * 查询自定义分析数据-汇总
     */
    @PreAuthorize("@ss.hasPermi('crypto:record:list')")
    @RequestMapping(value = "/allSummary")
    public AjaxResult allSummary(@RequestBody SummaryRequest query)
    {
        PageHelper.startPage(1, 1);
        return AjaxResult.success(cryptoDepositRecordService.allSummary(query));
    }
}
