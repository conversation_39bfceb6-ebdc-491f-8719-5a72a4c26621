package tv.shorthub.admin.controller.googleplay;

import com.github.pagehelper.PageHelper;
import tv.shorthub.common.core.domain.SummaryRequest;
import tv.shorthub.common.utils.SummaryColumnUtils;
import org.springframework.web.multipart.MultipartFile;
import java.util.ArrayList;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import tv.shorthub.common.annotation.Log;
import tv.shorthub.common.core.controller.BaseController;
import tv.shorthub.common.core.domain.AjaxResult;
import tv.shorthub.common.enums.BusinessType;
import tv.shorthub.system.domain.GooglePlayBillingLog;
import tv.shorthub.system.service.IGooglePlayBillingLogService;
import tv.shorthub.common.utils.poi.ExcelUtil;
import tv.shorthub.common.core.page.TableDataInfo;

/**
 * googleplay结算日志Controller
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
@RestController
@RequestMapping("/google/play/billing/log")
public class GooglePlayBillingLogController extends BaseController
{
    @Autowired
    private IGooglePlayBillingLogService googlePlayBillingLogService;

    /**
     * 查询googleplay结算日志列表
     */
    @PreAuthorize("@ss.hasPermi('google/play:billing/log:list')")
    @GetMapping("/list")
    public TableDataInfo list(GooglePlayBillingLog googlePlayBillingLog)
    {
        startPage();
        List<GooglePlayBillingLog> list = googlePlayBillingLogService.selectList(googlePlayBillingLog);
        return getDataTable(list);
    }

    /**
     * 获取googleplay结算日志数据汇总
     */
    @PreAuthorize("@ss.hasPermi('google/play:billing/log:query')")
    @GetMapping(value = "/getSummary")
    public AjaxResult getSummary(GooglePlayBillingLog googlePlayBillingLog)
    {
        return AjaxResult.success(googlePlayBillingLogService.getSummary(googlePlayBillingLog));
    }

    /**
     * 导出googleplay结算日志列表
     */
    @PreAuthorize("@ss.hasPermi('google/play:billing/log:export')")
    @Log(title = "googleplay结算日志", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, GooglePlayBillingLog googlePlayBillingLog)
    {
        List<GooglePlayBillingLog> list = googlePlayBillingLogService.selectList(googlePlayBillingLog);
        ExcelUtil<GooglePlayBillingLog> util = new ExcelUtil<>(GooglePlayBillingLog.class);
        util.exportExcel(response, list, "googleplay结算日志数据");
    }

    /**
     * 获取googleplay结算日志详细信息
     */
    @PreAuthorize("@ss.hasPermi('google/play:billing/log:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(googlePlayBillingLogService.getById(id));
    }

    /**
     * 新增googleplay结算日志
     */
    @PreAuthorize("@ss.hasPermi('google/play:billing/log:add')")
    @Log(title = "googleplay结算日志", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody GooglePlayBillingLog googlePlayBillingLog)
    {
        return toAjax(googlePlayBillingLogService.insert(googlePlayBillingLog));
    }

    /**
     * 修改googleplay结算日志
     */
    @PreAuthorize("@ss.hasPermi('google/play:billing/log:edit')")
    @Log(title = "googleplay结算日志", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody GooglePlayBillingLog googlePlayBillingLog)
    {
        return toAjax(googlePlayBillingLogService.update(googlePlayBillingLog));
    }

    /**
     * 删除googleplay结算日志
     */
    @PreAuthorize("@ss.hasPermi('google/play:billing/log:remove')")
    @Log(title = "googleplay结算日志", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable List<Long> ids)
    {
        return toAjax(googlePlayBillingLogService.deleteByIds(ids));
    }


    /**
     * 查询自定义分析列
     */
    @PreAuthorize("@ss.hasPermi('google/play:billing/log:list')")
    @GetMapping("/columns")
    public AjaxResult columns()
    {
        return AjaxResult.success(SummaryColumnUtils.get(GooglePlayBillingLog.class));
    }

    /**
     * 查询自定义分析数据
     */
    @PreAuthorize("@ss.hasPermi('google/play:billing/log:list')")
    @RequestMapping(value = "/summary")
    public TableDataInfo summary(@RequestBody SummaryRequest query)
    {
        startPage();
        List<GooglePlayBillingLog> summary = googlePlayBillingLogService.summary(query);
        return getDataTable(summary);
    }

    /**
     * 导出自定义分析数据
     */
    @PreAuthorize("@ss.hasPermi('google/play:billing/log:export')")
    @Log(title = "导出自定义分析数据", businessType = BusinessType.EXPORT)
    @PostMapping("/summaryExport")
    public void summaryExport(HttpServletResponse response, @RequestBody SummaryRequest query)
    {
        PageHelper.startPage(1, Integer.MAX_VALUE);
        if (null == query.getQuery()) {
            query.setQuery(new ArrayList<>());
        }
        List<GooglePlayBillingLog> list = googlePlayBillingLogService.summary(query);
        query.getGroupBy().addAll(query.getColumns());
        ExcelUtil<GooglePlayBillingLog> util = new ExcelUtil<>(GooglePlayBillingLog.class);
        util.exportExcel(response, list, "导出数据");
    }



    /**
     * 下载导入模板
     */
    @PreAuthorize("@ss.hasPermi('google/play:billing/log:export')")
    @Log(title = "下载导入模板", businessType = BusinessType.EXPORT)
    @PostMapping("/downloadImportModule")
    public void downloadImportModule()
    {
        googlePlayBillingLogService.downloadImportModule();
    }

    /**
     * 导入数据
     */
    @PreAuthorize("@ss.hasPermi('google/play:billing/log:export')")
    @Log(title = "导入数据", businessType = BusinessType.EXPORT)
    @PostMapping("/importData")
    public void importData(MultipartFile file)
    {

        googlePlayBillingLogService.importData(file);
    }


    /**
     * 查询自定义分析数据-汇总
     */
    @PreAuthorize("@ss.hasPermi('google/play:billing/log:list')")
    @RequestMapping(value = "/allSummary")
    public AjaxResult allSummary(@RequestBody SummaryRequest query)
    {
        PageHelper.startPage(1, 1);
        return AjaxResult.success(googlePlayBillingLogService.allSummary(query));
    }
}
