package tv.shorthub.admin.controller.statistics;

import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import tv.shorthub.admin.utils.StatisticsControllerPermissionUtils;
import tv.shorthub.common.core.domain.SummaryRequest;
import tv.shorthub.common.utils.SummaryColumnUtils;
import tv.shorthub.common.utils.SecurityUtils;
import org.springframework.web.multipart.MultipartFile;
import java.util.ArrayList;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import tv.shorthub.common.annotation.Log;
import tv.shorthub.common.core.controller.BaseController;
import tv.shorthub.common.core.domain.AjaxResult;
import tv.shorthub.common.enums.BusinessType;
import tv.shorthub.system.domain.StatisticsDailyDramaEpisodeStats;
import tv.shorthub.system.service.IStatisticsDailyDramaEpisodeStatsService;
import tv.shorthub.common.utils.poi.ExcelUtil;
import tv.shorthub.common.core.page.TableDataInfo;
import tv.shorthub.system.dto.RangeSummaryRequest;
/**
 * 剧集每日统计Controller
 *
 * <AUTHOR>
 * @date 2025-08-01
 */
@Slf4j
@RestController
@RequestMapping("/statistics/dayDramaEpisode")
public class StatisticsDailyDramaEpisodeStatsController extends BaseController
{
    @Autowired
    private IStatisticsDailyDramaEpisodeStatsService statisticsDailyDramaEpisodeStatsService;

    @Autowired
    private StatisticsControllerPermissionUtils controllerPermissionUtils;


    @PreAuthorize("@ss.hasPermi('statistics:dayDramaEpisode:list')")
    @PostMapping("/getRangeSummary")
    public AjaxResult getRangeSummary(@RequestBody RangeSummaryRequest request) {

        log.info("[剧集分集日统计] 区间汇总查询参数 - startTime: {}, endTime: {}, appid: {}, tfid: {}, contentId: {}, serialNumber: {}, timezone: {}, delivererName: {}",
                request.getStartTime(), request.getEndTime(), request.getAppid(), request.getTfid(),
                request.getContentId(), request.getSerialNumber(), request.getTimezone(), request.getDelivererName());
        // 权限验证和参数设置
        RangeSummaryRequest processedRequest = controllerPermissionUtils.SecurityUtilsExtracted(request);
        if (processedRequest == null) {
            return error("用户权限不足或权限处理失败");
        }

        // 调用统一的查询方法
        StatisticsDailyDramaEpisodeStats result = statisticsDailyDramaEpisodeStatsService.getRangeSummary(processedRequest);
        return success(result);
    }

    /**
     * 查询剧集每日统计列表
     */
    @PreAuthorize("@ss.hasPermi('statistics:dayDramaEpisode:list')")
    @GetMapping("/list")
    public TableDataInfo list(StatisticsDailyDramaEpisodeStats statisticsDailyDramaEpisodeStats)
    {
        startPage();
        List<StatisticsDailyDramaEpisodeStats> list = statisticsDailyDramaEpisodeStatsService.selectList(statisticsDailyDramaEpisodeStats);
        return getDataTable(list);
    }

    /**
     * 获取剧集每日统计数据汇总
     */
    @PreAuthorize("@ss.hasPermi('statistics:dayDramaEpisode:query')")
    @GetMapping(value = "/getSummary")
    public AjaxResult getSummary(StatisticsDailyDramaEpisodeStats statisticsDailyDramaEpisodeStats)
    {
        return AjaxResult.success(statisticsDailyDramaEpisodeStatsService.getSummary(statisticsDailyDramaEpisodeStats));
    }

    /**
     * 导出剧集每日统计列表
     */
    @PreAuthorize("@ss.hasPermi('statistics:dayDramaEpisode:export')")
    @Log(title = "剧集每日统计", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, StatisticsDailyDramaEpisodeStats statisticsDailyDramaEpisodeStats)
    {
        List<StatisticsDailyDramaEpisodeStats> list = statisticsDailyDramaEpisodeStatsService.selectList(statisticsDailyDramaEpisodeStats);
        ExcelUtil<StatisticsDailyDramaEpisodeStats> util = new ExcelUtil<>(StatisticsDailyDramaEpisodeStats.class);
        util.exportExcel(response, list, "剧集每日统计数据");
    }

    /**
     * 获取剧集每日统计详细信息
     */
    @PreAuthorize("@ss.hasPermi('statistics:dayDramaEpisode:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(statisticsDailyDramaEpisodeStatsService.getById(id));
    }

    /**
     * 新增剧集每日统计
     */
    @PreAuthorize("@ss.hasPermi('statistics:dayDramaEpisode:add')")
    @Log(title = "剧集每日统计", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody StatisticsDailyDramaEpisodeStats statisticsDailyDramaEpisodeStats)
    {
        return toAjax(statisticsDailyDramaEpisodeStatsService.insert(statisticsDailyDramaEpisodeStats));
    }

    /**
     * 修改剧集每日统计
     */
    @PreAuthorize("@ss.hasPermi('statistics:dayDramaEpisode:edit')")
    @Log(title = "剧集每日统计", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody StatisticsDailyDramaEpisodeStats statisticsDailyDramaEpisodeStats)
    {
        return toAjax(statisticsDailyDramaEpisodeStatsService.update(statisticsDailyDramaEpisodeStats));
    }

    /**
     * 删除剧集每日统计
     */
    @PreAuthorize("@ss.hasPermi('statistics:dayDramaEpisode:remove')")
    @Log(title = "剧集每日统计", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable List<Long> ids)
    {
        return toAjax(statisticsDailyDramaEpisodeStatsService.deleteByIds(ids));
    }


    /**
     * 查询自定义分析列
     */
    @PreAuthorize("@ss.hasPermi('statistics:dayDramaEpisode:list')")
    @GetMapping("/columns")
    public AjaxResult columns()
    {
        return AjaxResult.success(SummaryColumnUtils.get(StatisticsDailyDramaEpisodeStats.class));
    }

    /**
     * 查询自定义分析数据
     */
    @PreAuthorize("@ss.hasPermi('statistics:dayDramaEpisode:list')")
    @RequestMapping(value = "/summary")
    public TableDataInfo summary(@RequestBody SummaryRequest query)
    {
        startPage();
        List<StatisticsDailyDramaEpisodeStats> summary = statisticsDailyDramaEpisodeStatsService.summary(query);
        return getDataTable(summary);
    }
    /**
     * 导出自定义分析数据
     */
    @PreAuthorize("@ss.hasPermi('statistics:dayDramaEpisode:export')")
    @Log(title = "导出自定义分析数据", businessType = BusinessType.EXPORT)
    @PostMapping("/summaryExport")
    public void summaryExport(HttpServletResponse response, @RequestBody SummaryRequest query)
    {
        PageHelper.startPage(1, Integer.MAX_VALUE);
        if (null == query.getQuery()) {
            query.setQuery(new ArrayList<>());
        }
        List<StatisticsDailyDramaEpisodeStats> list = statisticsDailyDramaEpisodeStatsService.summary(query);
        query.getGroupBy().addAll(query.getColumns());
        ExcelUtil<StatisticsDailyDramaEpisodeStats> util = new ExcelUtil<>(StatisticsDailyDramaEpisodeStats.class);
        util.exportExcel(response, list, "导出数据");
    }


    /**
     * 下载导入模板
     */
    @PreAuthorize("@ss.hasPermi('statistics:dayDramaEpisode:export')")
    @Log(title = "下载导入模板", businessType = BusinessType.EXPORT)
    @PostMapping("/downloadImportModule")
    public void downloadImportModule()
    {
        statisticsDailyDramaEpisodeStatsService.downloadImportModule();
    }

    /**
     * 导入数据
     */
    @PreAuthorize("@ss.hasPermi('statistics:dayDramaEpisode:export')")
    @Log(title = "导入数据", businessType = BusinessType.EXPORT)
    @PostMapping("/importData")
    public void importData(MultipartFile file)
    {

        statisticsDailyDramaEpisodeStatsService.importData(file);
    }


    /**
     * 查询自定义分析数据-汇总
     */
    @PreAuthorize("@ss.hasPermi('statistics:dayDramaEpisode:list')")
    @RequestMapping(value = "/allSummary")
    public AjaxResult allSummary(@RequestBody SummaryRequest query)
    {
        PageHelper.startPage(1, 1);
        return AjaxResult.success(statisticsDailyDramaEpisodeStatsService.allSummary(query));
    }
}
