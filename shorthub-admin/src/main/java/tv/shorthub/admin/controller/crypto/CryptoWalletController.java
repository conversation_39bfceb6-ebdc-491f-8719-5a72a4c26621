package tv.shorthub.admin.controller.crypto;

import com.github.pagehelper.PageHelper;
import tv.shorthub.common.core.domain.SummaryRequest;
import tv.shorthub.common.utils.SummaryColumnUtils;
import org.springframework.web.multipart.MultipartFile;
import java.util.ArrayList;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import tv.shorthub.common.annotation.Log;
import tv.shorthub.common.core.controller.BaseController;
import tv.shorthub.common.core.domain.AjaxResult;
import tv.shorthub.common.enums.BusinessType;
import tv.shorthub.system.domain.CryptoWallet;
import tv.shorthub.system.service.ICryptoWalletService;
import tv.shorthub.common.utils.poi.ExcelUtil;
import tv.shorthub.common.core.page.TableDataInfo;

/**
 * 虚拟货币钱包地址Controller
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
@RestController
@RequestMapping("/crypto/wallet")
public class CryptoWalletController extends BaseController
{
    @Autowired
    private ICryptoWalletService cryptoWalletService;

    /**
     * 查询虚拟货币钱包地址列表
     */
    @PreAuthorize("@ss.hasPermi('crypto:wallet:list')")
    @GetMapping("/list")
    public TableDataInfo list(CryptoWallet cryptoWallet)
    {
        startPage();
        List<CryptoWallet> list = cryptoWalletService.selectList(cryptoWallet);
        return getDataTable(list);
    }

    /**
     * 获取虚拟货币钱包地址数据汇总
     */
    @PreAuthorize("@ss.hasPermi('crypto:wallet:query')")
    @GetMapping(value = "/getSummary")
    public AjaxResult getSummary(CryptoWallet cryptoWallet)
    {
        return AjaxResult.success(cryptoWalletService.getSummary(cryptoWallet));
    }

    /**
     * 导出虚拟货币钱包地址列表
     */
    @PreAuthorize("@ss.hasPermi('crypto:wallet:export')")
    @Log(title = "虚拟货币钱包地址", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CryptoWallet cryptoWallet)
    {
        List<CryptoWallet> list = cryptoWalletService.selectList(cryptoWallet);
        ExcelUtil<CryptoWallet> util = new ExcelUtil<>(CryptoWallet.class);
        util.exportExcel(response, list, "虚拟货币钱包地址数据");
    }

    /**
     * 获取虚拟货币钱包地址详细信息
     */
    @PreAuthorize("@ss.hasPermi('crypto:wallet:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(cryptoWalletService.getById(id));
    }

    /**
     * 新增虚拟货币钱包地址
     */
    @PreAuthorize("@ss.hasPermi('crypto:wallet:add')")
    @Log(title = "虚拟货币钱包地址", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CryptoWallet cryptoWallet)
    {
        return toAjax(cryptoWalletService.insert(cryptoWallet));
    }

    /**
     * 修改虚拟货币钱包地址
     */
    @PreAuthorize("@ss.hasPermi('crypto:wallet:edit')")
    @Log(title = "虚拟货币钱包地址", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CryptoWallet cryptoWallet)
    {
        return toAjax(cryptoWalletService.update(cryptoWallet));
    }

    /**
     * 删除虚拟货币钱包地址
     */
    @PreAuthorize("@ss.hasPermi('crypto:wallet:remove')")
    @Log(title = "虚拟货币钱包地址", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable List<Long> ids)
    {
        return toAjax(cryptoWalletService.deleteByIds(ids));
    }


    /**
     * 查询自定义分析列
     */
    @PreAuthorize("@ss.hasPermi('crypto:wallet:list')")
    @GetMapping("/columns")
    public AjaxResult columns()
    {
        return AjaxResult.success(SummaryColumnUtils.get(CryptoWallet.class));
    }

    /**
     * 查询自定义分析数据
     */
    @PreAuthorize("@ss.hasPermi('crypto:wallet:list')")
    @RequestMapping(value = "/summary")
    public TableDataInfo summary(@RequestBody SummaryRequest query)
    {
        startPage();
        List<CryptoWallet> summary = cryptoWalletService.summary(query);
        return getDataTable(summary);
    }

    /**
     * 导出自定义分析数据
     */
    @PreAuthorize("@ss.hasPermi('crypto:wallet:export')")
    @Log(title = "导出自定义分析数据", businessType = BusinessType.EXPORT)
    @PostMapping("/summaryExport")
    public void summaryExport(HttpServletResponse response, @RequestBody SummaryRequest query)
    {
        PageHelper.startPage(1, Integer.MAX_VALUE);
        if (null == query.getQuery()) {
            query.setQuery(new ArrayList<>());
        }
        List<CryptoWallet> list = cryptoWalletService.summary(query);
        query.getGroupBy().addAll(query.getColumns());
        ExcelUtil<CryptoWallet> util = new ExcelUtil<>(CryptoWallet.class);
        util.exportExcel(response, list, "导出数据");
    }



    /**
     * 下载导入模板
     */
    @PreAuthorize("@ss.hasPermi('crypto:wallet:export')")
    @Log(title = "下载导入模板", businessType = BusinessType.EXPORT)
    @PostMapping("/downloadImportModule")
    public void downloadImportModule()
    {
        cryptoWalletService.downloadImportModule();
    }

    /**
     * 导入数据
     */
    @PreAuthorize("@ss.hasPermi('crypto:wallet:export')")
    @Log(title = "导入数据", businessType = BusinessType.EXPORT)
    @PostMapping("/importData")
    public void importData(MultipartFile file)
    {

        cryptoWalletService.importData(file);
    }


    /**
     * 查询自定义分析数据-汇总
     */
    @PreAuthorize("@ss.hasPermi('crypto:wallet:list')")
    @RequestMapping(value = "/allSummary")
    public AjaxResult allSummary(@RequestBody SummaryRequest query)
    {
        PageHelper.startPage(1, 1);
        return AjaxResult.success(cryptoWalletService.allSummary(query));
    }
}
