package tv.shorthub.admin.controller.app;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.PageHelper;
import org.apache.commons.lang3.StringUtils;
import tv.shorthub.common.core.domain.SummaryRequest;
import tv.shorthub.common.exception.ApiException;
import tv.shorthub.common.utils.SummaryColumnUtils;
import org.springframework.web.multipart.MultipartFile;
import java.util.ArrayList;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import tv.shorthub.common.annotation.Log;
import tv.shorthub.common.core.controller.BaseController;
import tv.shorthub.common.core.domain.AjaxResult;
import tv.shorthub.common.enums.BusinessType;
import tv.shorthub.system.domain.AppDramaContents;
import tv.shorthub.system.domain.AppPromotion;
import tv.shorthub.system.domain.AppRecharge;
import tv.shorthub.system.service.IAppDramaContentsService;
import tv.shorthub.system.service.IAppPromotionService;
import tv.shorthub.common.utils.poi.ExcelUtil;
import tv.shorthub.common.core.page.TableDataInfo;
import tv.shorthub.system.service.IAppRechargeService;
import org.springframework.web.bind.annotation.RequestParam;
import tv.shorthub.system.dto.PromotionOptionDto;

/**
 * 推广链接Controller
 *
 * <AUTHOR>
 * @date 2025-05-15
 */
@RestController
@RequestMapping("/app/promotion/config")
public class AppPromotionController extends BaseController
{
    @Autowired
    private IAppPromotionService appPromotionService;

    @Autowired
    private IAppRechargeService appRechargeService;

    @Autowired
    private IAppDramaContentsService appDramaContentsService;

    /**
     * 查询推广链接列表
     */
    @PreAuthorize("@ss.hasPermi('app/promotion:config:list')")
    @GetMapping("/list")
    public TableDataInfo list(AppPromotion appPromotion)
    {
        startPage();
        List<AppPromotion> list = appPromotionService.selectList(appPromotion);
        TableDataInfo dataTable = getDataTable(list);
        List<AppPromotion> rows = (List<AppPromotion>) dataTable.getRows();
        for (AppPromotion row : rows) {
            AppDramaContents content = appDramaContentsService.getMapper().selectOne(new QueryWrapper<AppDramaContents>().eq("content_id", row.getContentId()));
            row.getParams().put("content", content);
            if (StringUtils.isNotEmpty(row.getFeeTemplateId())) {
                AppRecharge recharge = appRechargeService.getMapper().selectOne(new QueryWrapper<AppRecharge>().eq("template_id", row.getFeeTemplateId()));
                row.getParams().put("recharge", recharge);
            }
        }
        return getDataTable(list);
    }

    /**
     * 获取推广链接数据汇总
     */
    @PreAuthorize("@ss.hasPermi('app/promotion:config:query')")
    @GetMapping(value = "/getSummary")
    public AjaxResult getSummary(AppPromotion appPromotion)
    {
        return AjaxResult.success(appPromotionService.getSummary(appPromotion));
    }

    /**
     * 导出推广链接列表
     */
    @PreAuthorize("@ss.hasPermi('app/promotion:config:export')")
    @Log(title = "推广链接", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AppPromotion appPromotion)
    {
        List<AppPromotion> list = appPromotionService.selectList(appPromotion);
        ExcelUtil<AppPromotion> util = new ExcelUtil<>(AppPromotion.class);
        util.exportExcel(response, list, "推广链接数据");
    }

    /**
     * 获取推广链接详细信息
     */
    @PreAuthorize("@ss.hasPermi('app/promotion:config:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(appPromotionService.getById(id));
    }

    /**
     * 新增推广链接
     */
    @PreAuthorize("@ss.hasPermi('app/promotion:config:add')")
    @Log(title = "推广链接", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AppPromotion appPromotion)
    {
        return toAjax(appPromotionService.insert(appPromotion));
    }

    /**
     * 分配推广链接
     */
    @PreAuthorize("@ss.hasPermi('app/promotion:config:add')")
    @Log(title = "分配推广链接", businessType = BusinessType.INSERT)
    @PostMapping(value = "allocation")
    public AjaxResult allocation(@RequestBody AppPromotion appPromotion) {
        return toAjax(appPromotionService.allocation(appPromotion));
    }

    /**
     * 修改推广链接
     */
    @PreAuthorize("@ss.hasPermi('app/promotion:config:edit')")
    @Log(title = "推广链接", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AppPromotion appPromotion)
    {
        return toAjax(appPromotionService.update(appPromotion));
    }

    /**
     * 删除推广链接
     */
    @PreAuthorize("@ss.hasPermi('app/promotion:config:remove')")
    @Log(title = "推广链接", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable List<Long> ids)
    {

        throw new ApiException("暂不支持删除推广链接");
//        return toAjax(appPromotionService.deleteByIds(ids));
    }


    /**
     * 查询自定义分析列
     */
    @PreAuthorize("@ss.hasPermi('app/promotion:config:list')")
    @GetMapping("/columns")
    public AjaxResult columns()
    {
        return AjaxResult.success(SummaryColumnUtils.get(AppPromotion.class));
    }

    /**
     * 查询自定义分析数据
     */
    @PreAuthorize("@ss.hasPermi('app/promotion:config:list')")
    @RequestMapping(value = "/summary")
    public TableDataInfo summary(@RequestBody SummaryRequest query)
    {
        startPage();
        List<AppPromotion> summary = appPromotionService.summary(query);
        return getDataTable(summary);
    }

    /**
     * 导出自定义分析数据
     */
    @PreAuthorize("@ss.hasPermi('app/promotion:config:export')")
    @Log(title = "导出自定义分析数据", businessType = BusinessType.EXPORT)
    @PostMapping("/summaryExport")
    public void summaryExport(HttpServletResponse response, @RequestBody SummaryRequest query)
    {
        PageHelper.startPage(1, Integer.MAX_VALUE);
        if (null == query.getQuery()) {
            query.setQuery(new ArrayList<>());
        }
        List<AppPromotion> list = appPromotionService.summary(query);
        query.getGroupBy().addAll(query.getColumns());
        ExcelUtil<AppPromotion> util = new ExcelUtil<>(AppPromotion.class);
        util.exportExcel(response, list, "导出数据");
    }



    /**
     * 下载导入模板
     */
    @PreAuthorize("@ss.hasPermi('app/promotion:config:export')")
    @Log(title = "下载导入模板", businessType = BusinessType.EXPORT)
    @PostMapping("/downloadImportModule")
    public void downloadImportModule()
    {
        appPromotionService.downloadImportModule();
    }

    /**
     * 导入数据
     */
    @PreAuthorize("@ss.hasPermi('app/promotion:config:export')")
    @Log(title = "导入数据", businessType = BusinessType.EXPORT)
    @PostMapping("/importData")
    public void importData(MultipartFile file)
    {

        appPromotionService.importData(file);
    }


    /**
     * 查询自定义分析数据-汇总
     */
    @PreAuthorize("@ss.hasPermi('app/promotion:config:list')")
    @RequestMapping(value = "/allSummary")
    public AjaxResult allSummary(@RequestBody SummaryRequest query)
    {
        PageHelper.startPage(1, 1);
        return AjaxResult.success(appPromotionService.allSummary(query));
    }

    /**
     * 获取推广链接下拉框选项
     */
    @GetMapping("/options")
    public AjaxResult getPromotionOptions(@RequestParam(value = "appid", required = false) String appid,
                                          @RequestParam(value = "creatorName", required = false) String creatorName) {
        return AjaxResult.success(appPromotionService.getPromotionOptions(appid, creatorName));
    }
}
