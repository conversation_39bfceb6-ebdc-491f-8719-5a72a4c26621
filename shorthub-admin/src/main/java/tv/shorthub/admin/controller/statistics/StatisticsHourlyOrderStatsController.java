package tv.shorthub.admin.controller.statistics;

import com.github.pagehelper.PageHelper;
import tv.shorthub.common.core.domain.SummaryRequest;
import tv.shorthub.common.utils.SummaryColumnUtils;
import org.springframework.web.multipart.MultipartFile;
import java.util.ArrayList;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import tv.shorthub.common.annotation.Log;
import tv.shorthub.common.core.controller.BaseController;
import tv.shorthub.common.core.domain.AjaxResult;
import tv.shorthub.common.enums.BusinessType;
import tv.shorthub.system.domain.StatisticsHourlyOrderStats;
import tv.shorthub.system.service.IStatisticsHourlyOrderStatsService;
import tv.shorthub.statistics.service.IHourlyOrderStatsService;
import tv.shorthub.common.utils.poi.ExcelUtil;
import tv.shorthub.common.core.page.TableDataInfo;
import tv.shorthub.common.utils.DateUtils;
import tv.shorthub.statistics.dto.TimeRangeRequest;
import tv.shorthub.statistics.dto.BatchUpdateRequest;

import java.util.Date;
import java.util.Map;
import org.springframework.web.bind.annotation.RequestParam;
import lombok.extern.slf4j.Slf4j;
import tv.shorthub.common.utils.SecurityUtils;
import tv.shorthub.system.service.ISysUserService;
import tv.shorthub.system.service.IAppPromotionService;
import tv.shorthub.system.domain.AppPromotion;
import java.util.stream.Collectors;
import tv.shorthub.admin.utils.StatisticsControllerPermissionUtils;
import org.apache.commons.lang3.StringUtils;

/**
 * 每小时核心订单交易统计Controller
 *
 * <AUTHOR>
 * @date 2025-06-27
 */
@Slf4j
@RestController
@RequestMapping("/statistics/order")
public class StatisticsHourlyOrderStatsController extends BaseController
{

    @Autowired
    private IStatisticsHourlyOrderStatsService statisticsHourlyOrderStatsService;

    @Autowired
    private IHourlyOrderStatsService hourlyOrderStatsService;

    @Autowired
    private StatisticsControllerPermissionUtils controllerPermissionUtils;

    /**
     * 查询每小时核心订单交易统计列表
     */
    @PreAuthorize("@ss.hasPermi('statistics:order:list')")
    @GetMapping("/list")
    public TableDataInfo list(
            StatisticsHourlyOrderStats statisticsHourlyOrderStats,
            @RequestParam(required = false) String startTime,
            @RequestParam(required = false) String endTime,
            @RequestParam(required = false) String tfid)
    {
        log.info("[订单统计] 查询参数 - startTime: {}, endTime: {}, tfid: {}, 查询条件: {}", startTime, endTime, tfid, statisticsHourlyOrderStats);
        startPage();
        
        // 如果传递了 tfid 参数，设置到查询条件中
        if (tfid != null && !tfid.isEmpty()) {
            statisticsHourlyOrderStats.setTfid(tfid);
        }
        
        List<StatisticsHourlyOrderStats> list;
        if (startTime != null && endTime != null) {
            try {
                Date startDateTime = DateUtils.parseDate(startTime, "yyyy-MM-dd HH:mm:ss");
                Date endDateTime = DateUtils.parseDate(endTime, "yyyy-MM-dd HH:mm:ss");
                list = statisticsHourlyOrderStatsService.selectListByTimeRange(statisticsHourlyOrderStats, startDateTime, endDateTime);
            } catch (Exception e) {
                // 时间解析失败，使用默认查询
                list = statisticsHourlyOrderStatsService.selectList(statisticsHourlyOrderStats);
            }
        } else {
            list = statisticsHourlyOrderStatsService.selectList(statisticsHourlyOrderStats);
        }
        return getDataTable(list);
    }



    /**
     * 获取每小时核心订单交易统计汇总数据
     */
    @PreAuthorize("@ss.hasPermi('statistics:order:query')")
    @GetMapping("/getRangeSummary")
    public AjaxResult getRangeSummary(@RequestParam("startTime") Date startTime,
                                      @RequestParam("endTime") Date endTime,
                                      @RequestParam(value = "appid", required = false) String appid,
                                      @RequestParam(value = "tfid", required = false) String tfid,
                                      @RequestParam(value = "orderChannel", required = false) String orderChannel,
                                      @RequestParam(value = "delivererName", required = false) String delivererName) {
        log.info("[订单统计] 区间汇总查询参数 - startTime: {}, endTime: {}, appid: {}, tfid: {}, orderChannel: {}, delivererName: {}", startTime, endTime, appid, tfid, orderChannel, delivererName);
        
        // 处理按投手筛选的逻辑
        AjaxResult delivererResult = controllerPermissionUtils.handleDelivererQuery(
            delivererName, tfid, startTime, endTime,
            params -> statisticsHourlyOrderStatsService.getSummaryByTfids(params.tfids, params.startTime, params.endTime, null, orderChannel),
            new StatisticsHourlyOrderStats()
        );
        if (delivererResult != null) {
            return delivererResult;
        }
        
        // 处理默认查询
        AjaxResult defaultResult = controllerPermissionUtils.handleDefaultQuery(
            appid, tfid, startTime, endTime,
            params -> statisticsHourlyOrderStatsService.getSummaryByTfids(params.tfids, params.startTime, params.endTime, null, orderChannel),
            params -> statisticsHourlyOrderStatsService.getSummaryByCreator(params.creatorName, params.startTime, params.endTime, orderChannel),
            params -> statisticsHourlyOrderStatsService.getRangeSummary(params.startTime, params.endTime, params.appid, null, orderChannel),
            new StatisticsHourlyOrderStats()
        );
        if (defaultResult != null) {
            return defaultResult;
        }
        
        // 处理其他情况
        return controllerPermissionUtils.handleOtherQuery(
            appid, tfid, startTime, endTime,
            params -> statisticsHourlyOrderStatsService.getRangeSummary(params.startTime, params.endTime, params.appid, params.tfid, orderChannel)
        );
    }

    /**
     * 导出每小时核心订单交易统计列表
     */
    @PreAuthorize("@ss.hasPermi('statistics:order:export')")
    @Log(title = "每小时核心订单交易统计", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, StatisticsHourlyOrderStats statisticsHourlyOrderStats)
    {
        List<StatisticsHourlyOrderStats> list = statisticsHourlyOrderStatsService.selectList(statisticsHourlyOrderStats);
        ExcelUtil<StatisticsHourlyOrderStats> util = new ExcelUtil<>(StatisticsHourlyOrderStats.class);
        util.exportExcel(response, list, "每小时核心订单交易统计数据");
    }

    /**
     * 获取每小时核心订单交易统计详细信息
     */
    @PreAuthorize("@ss.hasPermi('statistics:order:query')")
    @GetMapping(value = "/info/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(statisticsHourlyOrderStatsService.getById(id));
    }

    /**
     * 新增每小时核心订单交易统计
     */
    @PreAuthorize("@ss.hasPermi('statistics:order:add')")
    @Log(title = "每小时核心订单交易统计", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody StatisticsHourlyOrderStats statisticsHourlyOrderStats)
    {
        return toAjax(statisticsHourlyOrderStatsService.insert(statisticsHourlyOrderStats));
    }

    /**
     * 修改每小时核心订单交易统计
     */
    @PreAuthorize("@ss.hasPermi('statistics:order:edit')")
    @Log(title = "每小时核心订单交易统计", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody StatisticsHourlyOrderStats statisticsHourlyOrderStats)
    {
        return toAjax(statisticsHourlyOrderStatsService.update(statisticsHourlyOrderStats));
    }

    /**
     * 删除每小时核心订单交易统计
     */
    @PreAuthorize("@ss.hasPermi('statistics:order:remove')")
    @Log(title = "每小时核心订单交易统计", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable List<Long> ids)
    {
        return toAjax(statisticsHourlyOrderStatsService.deleteByIds(ids));
    }


    /**
     * 查询自定义分析列
     */
    @PreAuthorize("@ss.hasPermi('statistics:order:list')")
    @GetMapping("/columns")
    public AjaxResult columns()
    {
        return AjaxResult.success(SummaryColumnUtils.get(StatisticsHourlyOrderStats.class));
    }

    /**
     * 查询自定义分析数据
     */
    @PreAuthorize("@ss.hasPermi('statistics:order:list')")
    @RequestMapping(value = "/summary")
    public TableDataInfo summary(@RequestBody SummaryRequest query)
    {
        startPage();
        List<StatisticsHourlyOrderStats> summary = statisticsHourlyOrderStatsService.summary(query);
        return getDataTable(summary);
    }

    /**
     * 导出自定义分析数据
     */
    @PreAuthorize("@ss.hasPermi('statistics:order:export')")
    @Log(title = "导出自定义分析数据", businessType = BusinessType.EXPORT)
    @PostMapping("/summaryExport")
    public void summaryExport(HttpServletResponse response, @RequestBody SummaryRequest query)
    {
        PageHelper.startPage(1, Integer.MAX_VALUE);
        if (null == query.getQuery()) {
            query.setQuery(new ArrayList<>());
        }
        List<StatisticsHourlyOrderStats> list = statisticsHourlyOrderStatsService.summary(query);
        query.getGroupBy().addAll(query.getColumns());
        ExcelUtil<StatisticsHourlyOrderStats> util = new ExcelUtil<>(StatisticsHourlyOrderStats.class);
        util.exportExcel(response, list, "导出数据");
    }



    /**
     * 下载导入模板
     */
    @PreAuthorize("@ss.hasPermi('statistics:order:export')")
    @Log(title = "下载导入模板", businessType = BusinessType.EXPORT)
    @PostMapping("/downloadImportModule")
    public void downloadImportModule()
    {
        statisticsHourlyOrderStatsService.downloadImportModule();
    }

    /**
     * 导入数据
     */
    @PreAuthorize("@ss.hasPermi('statistics:order:export')")
    @Log(title = "导入数据", businessType = BusinessType.EXPORT)
    @PostMapping("/importData")
    public void importData(MultipartFile file)
    {

        statisticsHourlyOrderStatsService.importData(file);
    }


    /**
     * 查询自定义分析数据-汇总
     */
    @PreAuthorize("@ss.hasPermi('statistics:order:list')")
    @RequestMapping(value = "/allSummary")
    public AjaxResult allSummary(@RequestBody SummaryRequest query)
    {
        PageHelper.startPage(1, 1);
        return AjaxResult.success(statisticsHourlyOrderStatsService.allSummary(query));
    }

    /**
     * 全量统计最近N天的订单数据
     */
    @PreAuthorize("@ss.hasPermi('statistics:order:edit')")
    @Log(title = "全量统计订单数据", businessType = BusinessType.UPDATE)
    @PostMapping("/batchUpdate")
    public AjaxResult batchUpdateRecentStats(@RequestBody BatchUpdateRequest request)
    {
        try {
            Integer days = request.getDays();
            
            // 默认统计最近7天
            if (days == null || days <= 0) {
                days = 7;
            }

            // 限制最多统计365天
            if (days > 365) {
                days = 365;
            }

            String result = hourlyOrderStatsService.batchUpdateRecentOrderStats(days);
            return AjaxResult.success(result);
        } catch (Exception e) {
            return AjaxResult.error("全量统计失败: " + e.getMessage());
        }
    }

    /**
     * 全量统计指定时间范围的订单数据
     */
    @PreAuthorize("@ss.hasPermi('statistics:order:edit')")
    @Log(title = "全量统计订单数据", businessType = BusinessType.UPDATE)
    @PostMapping("/batchUpdateRange")
    public AjaxResult batchUpdateRangeStats(@RequestBody BatchUpdateRequest request)
    {
        try {
            if (!request.isValidDateRange()) {
                return AjaxResult.error("日期范围参数无效");
            }

            String result = hourlyOrderStatsService.batchUpdateOrderStats(request.getStartDate(), request.getEndDate());
            return AjaxResult.success(result);
        } catch (Exception e) {
            return AjaxResult.error("全量统计失败: " + e.getMessage());
        }
    }


}
