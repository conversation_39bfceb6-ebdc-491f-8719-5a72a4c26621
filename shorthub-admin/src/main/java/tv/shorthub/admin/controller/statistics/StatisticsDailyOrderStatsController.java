package tv.shorthub.admin.controller.statistics;

import com.github.pagehelper.PageHelper;
import tv.shorthub.common.core.domain.SummaryRequest;
import tv.shorthub.common.utils.SummaryColumnUtils;
import org.springframework.web.multipart.MultipartFile;
import java.util.ArrayList;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import tv.shorthub.common.annotation.Log;
import tv.shorthub.common.core.controller.BaseController;
import tv.shorthub.common.core.domain.AjaxResult;
import tv.shorthub.common.enums.BusinessType;
import tv.shorthub.system.domain.StatisticsDailyOrderStats;
import tv.shorthub.system.service.IStatisticsDailyOrderStatsService;
import tv.shorthub.common.utils.poi.ExcelUtil;
import tv.shorthub.common.core.page.TableDataInfo;
import org.springframework.web.bind.annotation.RequestParam;
import lombok.extern.slf4j.Slf4j;
import tv.shorthub.common.utils.SecurityUtils;
import tv.shorthub.admin.utils.StatisticsControllerPermissionUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.Date;
import tv.shorthub.statistics.service.IDailyOrderStatsService;
import tv.shorthub.statistics.dto.BatchUpdateRequest;

/**
 * 每日订单统计Controller
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
@Slf4j
@RestController
@RequestMapping("/statistics/dayorder")
public class StatisticsDailyOrderStatsController extends BaseController
{
    @Autowired
    private IStatisticsDailyOrderStatsService statisticsDailyOrderStatsService;
    @Autowired
    private StatisticsControllerPermissionUtils controllerPermissionUtils;
    @Autowired
    private IDailyOrderStatsService dailyOrderStatsService;

    /**
     * 查询每日订单统计列表
     */
    @PreAuthorize("@ss.hasPermi('statistics:dayorder:list')")
    @GetMapping("/list")
    public TableDataInfo list(StatisticsDailyOrderStats statisticsDailyOrderStats)
    {
        startPage();
        List<StatisticsDailyOrderStats> list = statisticsDailyOrderStatsService.selectList(statisticsDailyOrderStats);
        return getDataTable(list);
    }

    /**
     * 获取每日订单统计数据汇总
     */
    @PreAuthorize("@ss.hasPermi('statistics:dayorder:query')")
    @GetMapping(value = "/getSummary")
    public AjaxResult getSummary(StatisticsDailyOrderStats statisticsDailyOrderStats)
    {
        return AjaxResult.success();
    }

    /**
     * 导出每日订单统计列表
     */
    @PreAuthorize("@ss.hasPermi('statistics:dayorder:export')")
    @Log(title = "每日订单统计", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, StatisticsDailyOrderStats statisticsDailyOrderStats)
    {
        List<StatisticsDailyOrderStats> list = statisticsDailyOrderStatsService.selectList(statisticsDailyOrderStats);
        ExcelUtil<StatisticsDailyOrderStats> util = new ExcelUtil<>(StatisticsDailyOrderStats.class);
        util.exportExcel(response, list, "每日订单统计数据");
    }

    /**
     * 获取每日订单统计详细信息
     */
    @PreAuthorize("@ss.hasPermi('statistics:dayorder:query')")
    @GetMapping(value = "/info/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(statisticsDailyOrderStatsService.getById(id));
    }

    /**
     * 新增每日订单统计
     */
    @PreAuthorize("@ss.hasPermi('statistics:dayorder:add')")
    @Log(title = "每日订单统计", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody StatisticsDailyOrderStats statisticsDailyOrderStats)
    {
        return toAjax(statisticsDailyOrderStatsService.insert(statisticsDailyOrderStats));
    }

    /**
     * 修改每日订单统计
     */
    @PreAuthorize("@ss.hasPermi('statistics:dayorder:edit')")
    @Log(title = "每日订单统计", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody StatisticsDailyOrderStats statisticsDailyOrderStats)
    {
        return toAjax(statisticsDailyOrderStatsService.update(statisticsDailyOrderStats));
    }

    /**
     * 删除每日订单统计
     */
    @PreAuthorize("@ss.hasPermi('statistics:dayorder:remove')")
    @Log(title = "每日订单统计", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable List<Long> ids)
    {
        return toAjax(statisticsDailyOrderStatsService.deleteByIds(ids));
    }


    /**
     * 查询自定义分析列
     */
    @PreAuthorize("@ss.hasPermi('statistics:dayorder:list')")
    @GetMapping("/columns")
    public AjaxResult columns()
    {
        return AjaxResult.success(SummaryColumnUtils.get(StatisticsDailyOrderStats.class));
    }

    /**
     * 查询自定义分析数据
     */
    @PreAuthorize("@ss.hasPermi('statistics:dayorder:list')")
    @RequestMapping(value = "/summary")
    public TableDataInfo summary(@RequestBody SummaryRequest query)
    {
        startPage();
        List<StatisticsDailyOrderStats> summary = statisticsDailyOrderStatsService.summary(query);
        return getDataTable(summary);
    }

    /**
     * 导出自定义分析数据
     */
    @PreAuthorize("@ss.hasPermi('statistics:dayorder:export')")
    @Log(title = "导出自定义分析数据", businessType = BusinessType.EXPORT)
    @PostMapping("/summaryExport")
    public void summaryExport(HttpServletResponse response, @RequestBody SummaryRequest query)
    {
        PageHelper.startPage(1, Integer.MAX_VALUE);
        if (null == query.getQuery()) {
            query.setQuery(new ArrayList<>());
        }
        List<StatisticsDailyOrderStats> list = statisticsDailyOrderStatsService.summary(query);
        query.getGroupBy().addAll(query.getColumns());
        ExcelUtil<StatisticsDailyOrderStats> util = new ExcelUtil<>(StatisticsDailyOrderStats.class);
        util.exportExcel(response, list, "导出数据");
    }



    /**
     * 下载导入模板
     */
    @PreAuthorize("@ss.hasPermi('statistics:dayorder:export')")
    @Log(title = "下载导入模板", businessType = BusinessType.EXPORT)
    @PostMapping("/downloadImportModule")
    public void downloadImportModule()
    {
        statisticsDailyOrderStatsService.downloadImportModule();
    }

    /**
     * 导入数据
     */
    @PreAuthorize("@ss.hasPermi('statistics:dayorder:export')")
    @Log(title = "导入数据", businessType = BusinessType.EXPORT)
    @PostMapping("/importData")
    public void importData(MultipartFile file)
    {

        statisticsDailyOrderStatsService.importData(file);
    }


    /**
     * 查询自定义分析数据-汇总
     */
    @PreAuthorize("@ss.hasPermi('statistics:dayorder:list')")
    @RequestMapping(value = "/allSummary")
    public AjaxResult allSummary(@RequestBody SummaryRequest query)
    {
        PageHelper.startPage(1, 1);
        return AjaxResult.success(statisticsDailyOrderStatsService.allSummary(query));
    }

    /**
     * 区间汇总接口，权限与小时统计一致
     */
    @PreAuthorize("@ss.hasPermi('statistics:dayorder:query')")
    @GetMapping("/getRangeSummary")
    public AjaxResult getRangeSummary(@RequestParam("startTime") Date startTime,
                                      @RequestParam("endTime") Date endTime,
                                      @RequestParam(value = "appid", required = false) String appid,
                                      @RequestParam(value = "tfid", required = false) String tfid,
                                      @RequestParam(value = "orderChannel", required = false) String orderChannel,
                                      @RequestParam(value = "delivererName", required = false) String delivererName,
                                      @RequestParam(value = "timezone", required = false, defaultValue = "UTC+8") String timezone) {
        log.info("[每日订单统计] 区间汇总查询参数 - startTime: {}, endTime: {}, appid: {}, tfid: {}, orderChannel: {}, delivererName: {}, timezone: {}", startTime, endTime, appid, tfid, orderChannel, delivererName, timezone);

                // 处理按投手筛选的逻辑
        AjaxResult delivererResult = controllerPermissionUtils.handleDelivererQuery(
            delivererName, tfid, startTime, endTime,
            params -> statisticsDailyOrderStatsService.getSummaryByTfids(params.tfids, params.startTime, params.endTime, null, orderChannel, timezone),
            new StatisticsDailyOrderStats()
        );
        if (delivererResult != null) {
            return delivererResult;
        }
        
        // 处理默认查询
        AjaxResult defaultResult = controllerPermissionUtils.handleDefaultQuery(
            appid, tfid, startTime, endTime,
            params -> statisticsDailyOrderStatsService.getSummaryByTfids(params.tfids, params.startTime, params.endTime, null, orderChannel, timezone),
            params -> statisticsDailyOrderStatsService.getSummaryByCreator(params.creatorName, params.startTime, params.endTime, orderChannel, timezone),
            params -> statisticsDailyOrderStatsService.getRangeSummary(params.startTime, params.endTime, params.appid, null, orderChannel, timezone),
            new StatisticsDailyOrderStats()
        );
        if (defaultResult != null) {
            return defaultResult;
        }

        // 处理其他情况
        return controllerPermissionUtils.handleOtherQuery(
            appid, tfid, startTime, endTime,
            params -> statisticsDailyOrderStatsService.getRangeSummary(params.startTime, params.endTime, params.appid, params.tfid, orderChannel, timezone)
        );
    }

    /**
     * 全量统计最近N天的订单数据
     */
    @PreAuthorize("@ss.hasPermi('statistics:dayorder:edit')")
    @Log(title = "全量统计每日订单数据", businessType = BusinessType.UPDATE)
    @PostMapping("/batchUpdate")
    public AjaxResult batchUpdateRecentStats(@RequestBody BatchUpdateRequest request)
    {
        try {
            Integer days = request.getDays();

            // 默认统计最近7天
            if (days == null || days <= 0) {
                days = 7;
            }

            // 限制最多统计365天
            if (days > 365) {
                days = 365;
            }

            String result = dailyOrderStatsService.batchUpdateRecentOrderStats(days);
            return AjaxResult.success(result);
        } catch (Exception e) {
            return AjaxResult.error("全量统计失败: " + e.getMessage());
        }
    }

    /**
     * 全量统计指定时间范围的订单数据
     */
    @PreAuthorize("@ss.hasPermi('statistics:dayorder:edit')")
    @Log(title = "全量统计每日订单数据", businessType = BusinessType.UPDATE)
    @PostMapping("/batchUpdateRange")
    public AjaxResult batchUpdateRangeStats(@RequestBody BatchUpdateRequest request)
    {
        try {
            if (!request.isValidDateRange()) {
                return AjaxResult.error("日期范围参数无效");
            }

            String result = dailyOrderStatsService.batchUpdateOrderStats(request.getStartDate(), request.getEndDate());
            return AjaxResult.success(result);
        } catch (Exception e) {
            return AjaxResult.error("全量统计失败: " + e.getMessage());
        }
    }

}
