package tv.shorthub.admin.controller.app;

import com.github.pagehelper.PageHelper;
import tv.shorthub.common.annotation.AutoCache;
import tv.shorthub.common.core.cache.CacheKeyUtils;
import tv.shorthub.common.core.domain.SummaryRequest;
import tv.shorthub.common.utils.SummaryColumnUtils;
import org.springframework.web.multipart.MultipartFile;
import java.util.ArrayList;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import tv.shorthub.common.annotation.Log;
import tv.shorthub.common.core.controller.BaseController;
import tv.shorthub.common.core.domain.AjaxResult;
import tv.shorthub.common.enums.BusinessType;
import tv.shorthub.system.domain.PaypalPaymentConfig;
import tv.shorthub.system.service.IPaypalPaymentConfigService;
import tv.shorthub.common.utils.poi.ExcelUtil;
import tv.shorthub.common.core.page.TableDataInfo;

/**
 * paypal配置Controller
 *
 * <AUTHOR>
 * @date 2025-05-22
 */
@RestController
@RequestMapping("/app/paypal/config")
public class PaypalPaymentConfigController extends BaseController
{
    @Autowired
    private IPaypalPaymentConfigService paypalPaymentConfigService;

    /**
     * 查询paypal配置列表
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:config:list')")
    @GetMapping("/list")
    public TableDataInfo list(PaypalPaymentConfig paypalPaymentConfig)
    {
        startPage();
        List<PaypalPaymentConfig> list = paypalPaymentConfigService.selectList(paypalPaymentConfig);
        return getDataTable(list);
    }

    /**
     * 获取paypal配置数据汇总
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:config:query')")
    @GetMapping(value = "/getSummary")
    public AjaxResult getSummary(PaypalPaymentConfig paypalPaymentConfig)
    {
        return AjaxResult.success(paypalPaymentConfigService.getSummary(paypalPaymentConfig));
    }

    /**
     * 导出paypal配置列表
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:config:export')")
    @Log(title = "paypal配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PaypalPaymentConfig paypalPaymentConfig)
    {
        List<PaypalPaymentConfig> list = paypalPaymentConfigService.selectList(paypalPaymentConfig);
        ExcelUtil<PaypalPaymentConfig> util = new ExcelUtil<>(PaypalPaymentConfig.class);
        util.exportExcel(response, list, "paypal配置数据");
    }

    /**
     * 获取paypal配置详细信息
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:config:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(paypalPaymentConfigService.getById(id));
    }

    /**
     * 新增paypal配置
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:config:add')")
    @Log(title = "paypal配置", businessType = BusinessType.INSERT)
    @PostMapping
    @AutoCache(key = CacheKeyUtils.TOPIC_PP_REFRESH)
    public AjaxResult add(@RequestBody PaypalPaymentConfig paypalPaymentConfig)
    {
        return toAjax(paypalPaymentConfigService.insert(paypalPaymentConfig));
    }

    /**
     * 修改paypal配置
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:config:edit')")
    @Log(title = "paypal配置", businessType = BusinessType.UPDATE)
    @PutMapping
    @AutoCache(key = CacheKeyUtils.TOPIC_PP_REFRESH)
    public AjaxResult edit(@RequestBody PaypalPaymentConfig paypalPaymentConfig)
    {
        return toAjax(paypalPaymentConfigService.update(paypalPaymentConfig));
    }

    /**
     * 删除paypal配置
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:config:remove')")
    @Log(title = "paypal配置", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable List<Long> ids)
    {
        return toAjax(paypalPaymentConfigService.deleteByIds(ids));
    }


    /**
     * 查询自定义分析列
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:config:list')")
    @GetMapping("/columns")
    public AjaxResult columns()
    {
        return AjaxResult.success(SummaryColumnUtils.get(PaypalPaymentConfig.class));
    }

    /**
     * 查询自定义分析数据
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:config:list')")
    @RequestMapping(value = "/summary")
    public TableDataInfo summary(@RequestBody SummaryRequest query)
    {
        startPage();
        List<PaypalPaymentConfig> summary = paypalPaymentConfigService.summary(query);
        return getDataTable(summary);
    }

    /**
     * 导出自定义分析数据
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:config:export')")
    @Log(title = "导出自定义分析数据", businessType = BusinessType.EXPORT)
    @PostMapping("/summaryExport")
    public void summaryExport(HttpServletResponse response, @RequestBody SummaryRequest query)
    {
        PageHelper.startPage(1, Integer.MAX_VALUE);
        if (null == query.getQuery()) {
            query.setQuery(new ArrayList<>());
        }
        List<PaypalPaymentConfig> list = paypalPaymentConfigService.summary(query);
        query.getGroupBy().addAll(query.getColumns());
        ExcelUtil<PaypalPaymentConfig> util = new ExcelUtil<>(PaypalPaymentConfig.class);
        util.exportExcel(response, list, "导出数据");
    }



    /**
     * 下载导入模板
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:config:export')")
    @Log(title = "下载导入模板", businessType = BusinessType.EXPORT)
    @PostMapping("/downloadImportModule")
    public void downloadImportModule()
    {
        paypalPaymentConfigService.downloadImportModule();
    }

    /**
     * 导入数据
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:config:export')")
    @Log(title = "导入数据", businessType = BusinessType.EXPORT)
    @PostMapping("/importData")
    public void importData(MultipartFile file)
    {

        paypalPaymentConfigService.importData(file);
    }


    /**
     * 查询自定义分析数据-汇总
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:config:list')")
    @RequestMapping(value = "/allSummary")
    public AjaxResult allSummary(@RequestBody SummaryRequest query)
    {
        PageHelper.startPage(1, 1);
        return AjaxResult.success(paypalPaymentConfigService.allSummary(query));
    }
}
