package tv.shorthub.admin.controller.airwallex;

import com.github.pagehelper.PageHelper;
import tv.shorthub.common.core.domain.SummaryRequest;
import tv.shorthub.common.utils.SummaryColumnUtils;
import org.springframework.web.multipart.MultipartFile;
import java.util.ArrayList;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import tv.shorthub.common.annotation.Log;
import tv.shorthub.common.core.controller.BaseController;
import tv.shorthub.common.core.domain.AjaxResult;
import tv.shorthub.common.enums.BusinessType;
import tv.shorthub.system.domain.AirwallexPaymentConfig;
import tv.shorthub.system.service.IAirwallexPaymentConfigService;
import tv.shorthub.common.utils.poi.ExcelUtil;
import tv.shorthub.common.core.page.TableDataInfo;

/**
 * airwallex支付配置Controller
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
@RestController
@RequestMapping("/pay/airwallex/config")
public class AirwallexPaymentConfigController extends BaseController
{
    @Autowired
    private IAirwallexPaymentConfigService airwallexPaymentConfigService;

    /**
     * 查询airwallex支付配置列表
     */
    @PreAuthorize("@ss.hasPermi('pay/airwallex:config:list')")
    @GetMapping("/list")
    public TableDataInfo list(AirwallexPaymentConfig airwallexPaymentConfig)
    {
        startPage();
        List<AirwallexPaymentConfig> list = airwallexPaymentConfigService.selectList(airwallexPaymentConfig);
        return getDataTable(list);
    }

    /**
     * 获取airwallex支付配置数据汇总
     */
    @PreAuthorize("@ss.hasPermi('pay/airwallex:config:query')")
    @GetMapping(value = "/getSummary")
    public AjaxResult getSummary(AirwallexPaymentConfig airwallexPaymentConfig)
    {
        return AjaxResult.success(airwallexPaymentConfigService.getSummary(airwallexPaymentConfig));
    }

    /**
     * 导出airwallex支付配置列表
     */
    @PreAuthorize("@ss.hasPermi('pay/airwallex:config:export')")
    @Log(title = "airwallex支付配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AirwallexPaymentConfig airwallexPaymentConfig)
    {
        List<AirwallexPaymentConfig> list = airwallexPaymentConfigService.selectList(airwallexPaymentConfig);
        ExcelUtil<AirwallexPaymentConfig> util = new ExcelUtil<>(AirwallexPaymentConfig.class);
        util.exportExcel(response, list, "airwallex支付配置数据");
    }

    /**
     * 获取airwallex支付配置详细信息
     */
    @PreAuthorize("@ss.hasPermi('pay/airwallex:config:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(airwallexPaymentConfigService.getById(id));
    }

    /**
     * 新增airwallex支付配置
     */
    @PreAuthorize("@ss.hasPermi('pay/airwallex:config:add')")
    @Log(title = "airwallex支付配置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AirwallexPaymentConfig airwallexPaymentConfig)
    {
        return toAjax(airwallexPaymentConfigService.insert(airwallexPaymentConfig));
    }

    /**
     * 修改airwallex支付配置
     */
    @PreAuthorize("@ss.hasPermi('pay/airwallex:config:edit')")
    @Log(title = "airwallex支付配置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AirwallexPaymentConfig airwallexPaymentConfig)
    {
        return toAjax(airwallexPaymentConfigService.update(airwallexPaymentConfig));
    }

    /**
     * 删除airwallex支付配置
     */
    @PreAuthorize("@ss.hasPermi('pay/airwallex:config:remove')")
    @Log(title = "airwallex支付配置", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable List<Long> ids)
    {
        return toAjax(airwallexPaymentConfigService.deleteByIds(ids));
    }


    /**
     * 查询自定义分析列
     */
    @PreAuthorize("@ss.hasPermi('pay/airwallex:config:list')")
    @GetMapping("/columns")
    public AjaxResult columns()
    {
        return AjaxResult.success(SummaryColumnUtils.get(AirwallexPaymentConfig.class));
    }

    /**
     * 查询自定义分析数据
     */
    @PreAuthorize("@ss.hasPermi('pay/airwallex:config:list')")
    @RequestMapping(value = "/summary")
    public TableDataInfo summary(@RequestBody SummaryRequest query)
    {
        startPage();
        List<AirwallexPaymentConfig> summary = airwallexPaymentConfigService.summary(query);
        return getDataTable(summary);
    }

    /**
     * 导出自定义分析数据
     */
    @PreAuthorize("@ss.hasPermi('pay/airwallex:config:export')")
    @Log(title = "导出自定义分析数据", businessType = BusinessType.EXPORT)
    @PostMapping("/summaryExport")
    public void summaryExport(HttpServletResponse response, @RequestBody SummaryRequest query)
    {
        PageHelper.startPage(1, Integer.MAX_VALUE);
        if (null == query.getQuery()) {
            query.setQuery(new ArrayList<>());
        }
        List<AirwallexPaymentConfig> list = airwallexPaymentConfigService.summary(query);
        query.getGroupBy().addAll(query.getColumns());
        ExcelUtil<AirwallexPaymentConfig> util = new ExcelUtil<>(AirwallexPaymentConfig.class);
        util.exportExcel(response, list, "导出数据");
    }



    /**
     * 下载导入模板
     */
    @PreAuthorize("@ss.hasPermi('pay/airwallex:config:export')")
    @Log(title = "下载导入模板", businessType = BusinessType.EXPORT)
    @PostMapping("/downloadImportModule")
    public void downloadImportModule()
    {
        airwallexPaymentConfigService.downloadImportModule();
    }

    /**
     * 导入数据
     */
    @PreAuthorize("@ss.hasPermi('pay/airwallex:config:export')")
    @Log(title = "导入数据", businessType = BusinessType.EXPORT)
    @PostMapping("/importData")
    public void importData(MultipartFile file)
    {

        airwallexPaymentConfigService.importData(file);
    }


    /**
     * 查询自定义分析数据-汇总
     */
    @PreAuthorize("@ss.hasPermi('pay/airwallex:config:list')")
    @RequestMapping(value = "/allSummary")
    public AjaxResult allSummary(@RequestBody SummaryRequest query)
    {
        PageHelper.startPage(1, 1);
        return AjaxResult.success(airwallexPaymentConfigService.allSummary(query));
    }
}
