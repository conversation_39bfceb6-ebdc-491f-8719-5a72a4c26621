package tv.shorthub.admin.controller.app;

import com.github.pagehelper.PageHelper;
import tv.shorthub.common.annotation.RateLimiter;
import tv.shorthub.common.core.domain.SummaryRequest;
import tv.shorthub.common.enums.LimitType;
import tv.shorthub.common.utils.SummaryColumnUtils;
import org.springframework.web.multipart.MultipartFile;
import java.util.ArrayList;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import tv.shorthub.common.annotation.Log;
import tv.shorthub.common.core.controller.BaseController;
import tv.shorthub.common.core.domain.AjaxResult;
import tv.shorthub.common.enums.BusinessType;
import tv.shorthub.system.domain.AppPromotionRequestLog;
import tv.shorthub.system.service.IAppPromotionRequestLogService;
import tv.shorthub.common.utils.poi.ExcelUtil;
import tv.shorthub.common.core.page.TableDataInfo;

/**
 * promotion访问日志Controller
 *
 * <AUTHOR>
 * @date 2025-05-28
 */
@RestController
@RequestMapping("/app/promotion/requestlog")
public class AppPromotionRequestLogController extends BaseController
{
    @Autowired
    private IAppPromotionRequestLogService appPromotionRequestLogService;

    /**
     * 查询promotion访问日志列表
     */
    @PreAuthorize("@ss.hasPermi('app/promotion:requestlog:list')")
    @GetMapping("/list")
    @RateLimiter(limitType = LimitType.USER_ID, time = 20, count = 10)
    public TableDataInfo list(AppPromotionRequestLog appPromotionRequestLog)
    {
        startPage();
        List<AppPromotionRequestLog> list = appPromotionRequestLogService.selectList(appPromotionRequestLog);
        return getDataTable(list);
    }

    /**
     * 获取promotion访问日志数据汇总
     */
    @PreAuthorize("@ss.hasPermi('app/promotion:requestlog:query')")
    @GetMapping(value = "/getSummary")
    public AjaxResult getSummary(AppPromotionRequestLog appPromotionRequestLog)
    {
        return AjaxResult.success(appPromotionRequestLogService.getSummary(appPromotionRequestLog));
    }

    /**
     * 导出promotion访问日志列表
     */
    @PreAuthorize("@ss.hasPermi('app/promotion:requestlog:export')")
    @Log(title = "promotion访问日志", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AppPromotionRequestLog appPromotionRequestLog)
    {
        List<AppPromotionRequestLog> list = appPromotionRequestLogService.selectList(appPromotionRequestLog);
        ExcelUtil<AppPromotionRequestLog> util = new ExcelUtil<>(AppPromotionRequestLog.class);
        util.exportExcel(response, list, "promotion访问日志数据");
    }

    /**
     * 获取promotion访问日志详细信息
     */
    @PreAuthorize("@ss.hasPermi('app/promotion:requestlog:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(appPromotionRequestLogService.getById(id));
    }

    /**
     * 新增promotion访问日志
     */
    @PreAuthorize("@ss.hasPermi('app/promotion:requestlog:add')")
    @Log(title = "promotion访问日志", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AppPromotionRequestLog appPromotionRequestLog)
    {
        return toAjax(appPromotionRequestLogService.insert(appPromotionRequestLog));
    }

    /**
     * 修改promotion访问日志
     */
    @PreAuthorize("@ss.hasPermi('app/promotion:requestlog:edit')")
    @Log(title = "promotion访问日志", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AppPromotionRequestLog appPromotionRequestLog)
    {
        return toAjax(appPromotionRequestLogService.update(appPromotionRequestLog));
    }

    /**
     * 删除promotion访问日志
     */
    @PreAuthorize("@ss.hasPermi('app/promotion:requestlog:remove')")
    @Log(title = "promotion访问日志", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable List<Long> ids)
    {
        return toAjax(appPromotionRequestLogService.deleteByIds(ids));
    }


    /**
     * 查询自定义分析列
     */
    @PreAuthorize("@ss.hasPermi('app/promotion:requestlog:list')")
    @GetMapping("/columns")
    public AjaxResult columns()
    {
        return AjaxResult.success(SummaryColumnUtils.get(AppPromotionRequestLog.class));
    }

    /**
     * 查询自定义分析数据
     */
    @PreAuthorize("@ss.hasPermi('app/promotion:requestlog:list')")
    @RequestMapping(value = "/summary")
    public TableDataInfo summary(@RequestBody SummaryRequest query)
    {
        startPage();
        List<AppPromotionRequestLog> summary = appPromotionRequestLogService.summary(query);
        return getDataTable(summary);
    }

    /**
     * 导出自定义分析数据
     */
    @PreAuthorize("@ss.hasPermi('app/promotion:requestlog:export')")
    @Log(title = "导出自定义分析数据", businessType = BusinessType.EXPORT)
    @PostMapping("/summaryExport")
    public void summaryExport(HttpServletResponse response, @RequestBody SummaryRequest query)
    {
        PageHelper.startPage(1, Integer.MAX_VALUE);
        if (null == query.getQuery()) {
            query.setQuery(new ArrayList<>());
        }
        List<AppPromotionRequestLog> list = appPromotionRequestLogService.summary(query);
        query.getGroupBy().addAll(query.getColumns());
        ExcelUtil<AppPromotionRequestLog> util = new ExcelUtil<>(AppPromotionRequestLog.class);
        util.exportExcel(response, list, "导出数据");
    }



    /**
     * 下载导入模板
     */
    @PreAuthorize("@ss.hasPermi('app/promotion:requestlog:export')")
    @Log(title = "下载导入模板", businessType = BusinessType.EXPORT)
    @PostMapping("/downloadImportModule")
    public void downloadImportModule()
    {
        appPromotionRequestLogService.downloadImportModule();
    }

    /**
     * 导入数据
     */
    @PreAuthorize("@ss.hasPermi('app/promotion:requestlog:export')")
    @Log(title = "导入数据", businessType = BusinessType.EXPORT)
    @PostMapping("/importData")
    public void importData(MultipartFile file)
    {

        appPromotionRequestLogService.importData(file);
    }


    /**
     * 查询自定义分析数据-汇总
     */
    @PreAuthorize("@ss.hasPermi('app/promotion:requestlog:list')")
    @RequestMapping(value = "/allSummary")
    public AjaxResult allSummary(@RequestBody SummaryRequest query)
    {
        PageHelper.startPage(1, 1);
        return AjaxResult.success(appPromotionRequestLogService.allSummary(query));
    }
}
