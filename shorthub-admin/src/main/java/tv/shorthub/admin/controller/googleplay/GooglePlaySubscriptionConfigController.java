package tv.shorthub.admin.controller.googleplay;

import com.github.pagehelper.PageHelper;
import tv.shorthub.common.core.domain.SummaryRequest;
import tv.shorthub.common.utils.SummaryColumnUtils;
import org.springframework.web.multipart.MultipartFile;
import java.util.ArrayList;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import tv.shorthub.common.annotation.Log;
import tv.shorthub.common.core.controller.BaseController;
import tv.shorthub.common.core.domain.AjaxResult;
import tv.shorthub.common.enums.BusinessType;
import tv.shorthub.system.domain.GooglePlaySubscriptionConfig;
import tv.shorthub.system.service.IGooglePlaySubscriptionConfigService;
import tv.shorthub.common.utils.poi.ExcelUtil;
import tv.shorthub.common.core.page.TableDataInfo;

/**
 * Google Play订阅产品配置Controller
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
@RestController
@RequestMapping("/google/play/product/subscription")
public class GooglePlaySubscriptionConfigController extends BaseController
{
    @Autowired
    private IGooglePlaySubscriptionConfigService googlePlaySubscriptionConfigService;

    /**
     * 查询Google Play订阅产品配置列表
     */
    @PreAuthorize("@ss.hasPermi('google/play:product/subscription:list')")
    @GetMapping("/list")
    public TableDataInfo list(GooglePlaySubscriptionConfig googlePlaySubscriptionConfig)
    {
        startPage();
        List<GooglePlaySubscriptionConfig> list = googlePlaySubscriptionConfigService.selectList(googlePlaySubscriptionConfig);
        return getDataTable(list);
    }

    /**
     * 获取Google Play订阅产品配置数据汇总
     */
    @PreAuthorize("@ss.hasPermi('google/play:product/subscription:query')")
    @GetMapping(value = "/getSummary")
    public AjaxResult getSummary(GooglePlaySubscriptionConfig googlePlaySubscriptionConfig)
    {
        return AjaxResult.success(googlePlaySubscriptionConfigService.getSummary(googlePlaySubscriptionConfig));
    }

    /**
     * 导出Google Play订阅产品配置列表
     */
    @PreAuthorize("@ss.hasPermi('google/play:product/subscription:export')")
    @Log(title = "Google Play订阅产品配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, GooglePlaySubscriptionConfig googlePlaySubscriptionConfig)
    {
        List<GooglePlaySubscriptionConfig> list = googlePlaySubscriptionConfigService.selectList(googlePlaySubscriptionConfig);
        ExcelUtil<GooglePlaySubscriptionConfig> util = new ExcelUtil<>(GooglePlaySubscriptionConfig.class);
        util.exportExcel(response, list, "Google Play订阅产品配置数据");
    }

    /**
     * 获取Google Play订阅产品配置详细信息
     */
    @PreAuthorize("@ss.hasPermi('google/play:product/subscription:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(googlePlaySubscriptionConfigService.getById(id));
    }

    /**
     * 新增Google Play订阅产品配置
     */
    @PreAuthorize("@ss.hasPermi('google/play:product/subscription:add')")
    @Log(title = "Google Play订阅产品配置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody GooglePlaySubscriptionConfig googlePlaySubscriptionConfig)
    {
        return toAjax(googlePlaySubscriptionConfigService.insert(googlePlaySubscriptionConfig));
    }

    /**
     * 修改Google Play订阅产品配置
     */
    @PreAuthorize("@ss.hasPermi('google/play:product/subscription:edit')")
    @Log(title = "Google Play订阅产品配置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody GooglePlaySubscriptionConfig googlePlaySubscriptionConfig)
    {
        return toAjax(googlePlaySubscriptionConfigService.update(googlePlaySubscriptionConfig));
    }

    /**
     * 删除Google Play订阅产品配置
     */
    @PreAuthorize("@ss.hasPermi('google/play:product/subscription:remove')")
    @Log(title = "Google Play订阅产品配置", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable List<Long> ids)
    {
        return toAjax(googlePlaySubscriptionConfigService.deleteByIds(ids));
    }


    /**
     * 查询自定义分析列
     */
    @PreAuthorize("@ss.hasPermi('google/play:product/subscription:list')")
    @GetMapping("/columns")
    public AjaxResult columns()
    {
        return AjaxResult.success(SummaryColumnUtils.get(GooglePlaySubscriptionConfig.class));
    }

    /**
     * 查询自定义分析数据
     */
    @PreAuthorize("@ss.hasPermi('google/play:product/subscription:list')")
    @RequestMapping(value = "/summary")
    public TableDataInfo summary(@RequestBody SummaryRequest query)
    {
        startPage();
        List<GooglePlaySubscriptionConfig> summary = googlePlaySubscriptionConfigService.summary(query);
        return getDataTable(summary);
    }

    /**
     * 导出自定义分析数据
     */
    @PreAuthorize("@ss.hasPermi('google/play:product/subscription:export')")
    @Log(title = "导出自定义分析数据", businessType = BusinessType.EXPORT)
    @PostMapping("/summaryExport")
    public void summaryExport(HttpServletResponse response, @RequestBody SummaryRequest query)
    {
        PageHelper.startPage(1, Integer.MAX_VALUE);
        if (null == query.getQuery()) {
            query.setQuery(new ArrayList<>());
        }
        List<GooglePlaySubscriptionConfig> list = googlePlaySubscriptionConfigService.summary(query);
        query.getGroupBy().addAll(query.getColumns());
        ExcelUtil<GooglePlaySubscriptionConfig> util = new ExcelUtil<>(GooglePlaySubscriptionConfig.class);
        util.exportExcel(response, list, "导出数据");
    }



    /**
     * 下载导入模板
     */
    @PreAuthorize("@ss.hasPermi('google/play:product/subscription:export')")
    @Log(title = "下载导入模板", businessType = BusinessType.EXPORT)
    @PostMapping("/downloadImportModule")
    public void downloadImportModule()
    {
        googlePlaySubscriptionConfigService.downloadImportModule();
    }

    /**
     * 导入数据
     */
    @PreAuthorize("@ss.hasPermi('google/play:product/subscription:export')")
    @Log(title = "导入数据", businessType = BusinessType.EXPORT)
    @PostMapping("/importData")
    public void importData(MultipartFile file)
    {

        googlePlaySubscriptionConfigService.importData(file);
    }


    /**
     * 查询自定义分析数据-汇总
     */
    @PreAuthorize("@ss.hasPermi('google/play:product/subscription:list')")
    @RequestMapping(value = "/allSummary")
    public AjaxResult allSummary(@RequestBody SummaryRequest query)
    {
        PageHelper.startPage(1, 1);
        return AjaxResult.success(googlePlaySubscriptionConfigService.allSummary(query));
    }
}
