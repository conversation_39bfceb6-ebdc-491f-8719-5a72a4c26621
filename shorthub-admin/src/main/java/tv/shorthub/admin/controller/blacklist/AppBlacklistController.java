package tv.shorthub.admin.controller.blacklist;

import com.github.pagehelper.PageHelper;
import tv.shorthub.common.annotation.AutoCache;
import tv.shorthub.common.core.domain.SummaryRequest;
import tv.shorthub.common.utils.SummaryColumnUtils;
import org.springframework.web.multipart.MultipartFile;
import java.util.ArrayList;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import tv.shorthub.common.annotation.Log;
import tv.shorthub.common.core.controller.BaseController;
import tv.shorthub.common.core.domain.AjaxResult;
import tv.shorthub.common.enums.BusinessType;
import tv.shorthub.system.domain.AppBlacklist;
import tv.shorthub.system.service.IAppBlacklistService;
import tv.shorthub.common.utils.poi.ExcelUtil;
import tv.shorthub.common.core.page.TableDataInfo;

/**
 * 黑名单Controller
 *
 * <AUTHOR>
 * @date 2025-08-06
 */
@RestController
@RequestMapping("/app/blacklist")
public class AppBlacklistController extends BaseController
{
    @Autowired
    private IAppBlacklistService appBlacklistService;

    /**
     * 查询黑名单列表
     */
    @PreAuthorize("@ss.hasPermi('app:blacklist:list')")
    @GetMapping("/list")
    public TableDataInfo list(AppBlacklist appBlacklist)
    {
        startPage();
        List<AppBlacklist> list = appBlacklistService.selectList(appBlacklist);
        return getDataTable(list);
    }

    /**
     * 获取黑名单数据汇总
     */
    @PreAuthorize("@ss.hasPermi('app:blacklist:query')")
    @GetMapping(value = "/getSummary")
    public AjaxResult getSummary(AppBlacklist appBlacklist)
    {
        return AjaxResult.success(appBlacklistService.getSummary(appBlacklist));
    }

    /**
     * 导出黑名单列表
     */
    @PreAuthorize("@ss.hasPermi('app:blacklist:export')")
    @Log(title = "黑名单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AppBlacklist appBlacklist)
    {
        List<AppBlacklist> list = appBlacklistService.selectList(appBlacklist);
        ExcelUtil<AppBlacklist> util = new ExcelUtil<>(AppBlacklist.class);
        util.exportExcel(response, list, "黑名单数据");
    }

    /**
     * 获取黑名单详细信息
     */
    @PreAuthorize("@ss.hasPermi('app:blacklist:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(appBlacklistService.getById(id));
    }

    /**
     * 新增黑名单
     */
    @PreAuthorize("@ss.hasPermi('app:blacklist:add')")
    @Log(title = "黑名单", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AppBlacklist appBlacklist)
    {
        return toAjax(appBlacklistService.insert(appBlacklist));
    }

    /**
     * 修改黑名单
     */
    @PreAuthorize("@ss.hasPermi('app:blacklist:edit')")
    @Log(title = "黑名单", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AppBlacklist appBlacklist)
    {
        return toAjax(appBlacklistService.update(appBlacklist));
    }

    /**
     * 删除黑名单
     */
    @PreAuthorize("@ss.hasPermi('app:blacklist:remove')")
    @Log(title = "黑名单", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable List<Long> ids)
    {
        return toAjax(appBlacklistService.deleteByIds(ids));
    }


    /**
     * 查询自定义分析列
     */
    @PreAuthorize("@ss.hasPermi('app:blacklist:list')")
    @GetMapping("/columns")
    public AjaxResult columns()
    {
        return AjaxResult.success(SummaryColumnUtils.get(AppBlacklist.class));
    }

    /**
     * 查询自定义分析数据
     */
    @PreAuthorize("@ss.hasPermi('app:blacklist:list')")
    @RequestMapping(value = "/summary")
    public TableDataInfo summary(@RequestBody SummaryRequest query)
    {
        startPage();
        List<AppBlacklist> summary = appBlacklistService.summary(query);
        return getDataTable(summary);
    }

    /**
     * 导出自定义分析数据
     */
    @PreAuthorize("@ss.hasPermi('app:blacklist:export')")
    @Log(title = "导出自定义分析数据", businessType = BusinessType.EXPORT)
    @PostMapping("/summaryExport")
    public void summaryExport(HttpServletResponse response, @RequestBody SummaryRequest query)
    {
        PageHelper.startPage(1, Integer.MAX_VALUE);
        if (null == query.getQuery()) {
            query.setQuery(new ArrayList<>());
        }
        List<AppBlacklist> list = appBlacklistService.summary(query);
        query.getGroupBy().addAll(query.getColumns());
        ExcelUtil<AppBlacklist> util = new ExcelUtil<>(AppBlacklist.class);
        util.exportExcel(response, list, "导出数据");
    }



    /**
     * 下载导入模板
     */
    @PreAuthorize("@ss.hasPermi('app:blacklist:export')")
    @Log(title = "下载导入模板", businessType = BusinessType.EXPORT)
    @PostMapping("/downloadImportModule")
    public void downloadImportModule()
    {
        appBlacklistService.downloadImportModule();
    }

    /**
     * 导入数据
     */
    @PreAuthorize("@ss.hasPermi('app:blacklist:export')")
    @Log(title = "导入数据", businessType = BusinessType.EXPORT)
    @PostMapping("/importData")
    public void importData(MultipartFile file)
    {

        appBlacklistService.importData(file);
    }


    /**
     * 查询自定义分析数据-汇总
     */
    @PreAuthorize("@ss.hasPermi('app:blacklist:list')")
    @RequestMapping(value = "/allSummary")
    public AjaxResult allSummary(@RequestBody SummaryRequest query)
    {
        PageHelper.startPage(1, 1);
        return AjaxResult.success(appBlacklistService.allSummary(query));
    }
}
