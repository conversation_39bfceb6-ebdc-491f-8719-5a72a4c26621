package tv.shorthub.admin.controller.app;

import com.github.pagehelper.PageHelper;
import tv.shorthub.common.core.domain.SummaryRequest;
import tv.shorthub.common.utils.SummaryColumnUtils;
import org.springframework.web.multipart.MultipartFile;
import java.util.ArrayList;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import tv.shorthub.common.annotation.Log;
import tv.shorthub.common.core.controller.BaseController;
import tv.shorthub.common.core.domain.AjaxResult;
import tv.shorthub.common.enums.BusinessType;
import tv.shorthub.system.domain.AppUserLevel;
import tv.shorthub.system.service.IAppUserLevelService;
import tv.shorthub.common.utils.poi.ExcelUtil;
import tv.shorthub.common.core.page.TableDataInfo;

/**
 * 会员级别Controller
 *
 * <AUTHOR>
 * @date 2025-05-14
 */
@RestController
@RequestMapping("/app/users/level")
public class AppUserLevelController extends BaseController
{
    @Autowired
    private IAppUserLevelService appUserLevelService;

    /**
     * 查询会员级别列表
     */
    @PreAuthorize("@ss.hasPermi('app/users:level:list')")
    @GetMapping("/list")
    public TableDataInfo list(AppUserLevel appUserLevel)
    {
        startPage();
        List<AppUserLevel> list = appUserLevelService.selectList(appUserLevel);
        return getDataTable(list);
    }

    /**
     * 获取会员级别数据汇总
     */
    @PreAuthorize("@ss.hasPermi('app/users:level:query')")
    @GetMapping(value = "/getSummary")
    public AjaxResult getSummary(AppUserLevel appUserLevel)
    {
        return AjaxResult.success(appUserLevelService.getSummary(appUserLevel));
    }

    /**
     * 导出会员级别列表
     */
    @PreAuthorize("@ss.hasPermi('app/users:level:export')")
    @Log(title = "会员级别", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AppUserLevel appUserLevel)
    {
        List<AppUserLevel> list = appUserLevelService.selectList(appUserLevel);
        ExcelUtil<AppUserLevel> util = new ExcelUtil<>(AppUserLevel.class);
        util.exportExcel(response, list, "会员级别数据");
    }

    /**
     * 获取会员级别详细信息
     */
    @PreAuthorize("@ss.hasPermi('app/users:level:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(appUserLevelService.getById(id));
    }

    /**
     * 新增会员级别
     */
    @PreAuthorize("@ss.hasPermi('app/users:level:add')")
    @Log(title = "会员级别", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AppUserLevel appUserLevel)
    {
        return toAjax(appUserLevelService.insert(appUserLevel));
    }

    /**
     * 修改会员级别
     */
    @PreAuthorize("@ss.hasPermi('app/users:level:edit')")
    @Log(title = "会员级别", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AppUserLevel appUserLevel)
    {
        return toAjax(appUserLevelService.update(appUserLevel));
    }

    /**
     * 删除会员级别
     */
    @PreAuthorize("@ss.hasPermi('app/users:level:remove')")
    @Log(title = "会员级别", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable List<Long> ids)
    {
        return toAjax(appUserLevelService.deleteByIds(ids));
    }


    /**
     * 查询自定义分析列
     */
    @PreAuthorize("@ss.hasPermi('app/users:level:list')")
    @GetMapping("/columns")
    public AjaxResult columns()
    {
        return AjaxResult.success(SummaryColumnUtils.get(AppUserLevel.class));
    }

    /**
     * 查询自定义分析数据
     */
    @PreAuthorize("@ss.hasPermi('app/users:level:list')")
    @RequestMapping(value = "/summary")
    public TableDataInfo summary(@RequestBody SummaryRequest query)
    {
        startPage();
        List<AppUserLevel> summary = appUserLevelService.summary(query);
        return getDataTable(summary);
    }

    /**
     * 导出自定义分析数据
     */
    @PreAuthorize("@ss.hasPermi('app/users:level:export')")
    @Log(title = "导出自定义分析数据", businessType = BusinessType.EXPORT)
    @PostMapping("/summaryExport")
    public void summaryExport(HttpServletResponse response, @RequestBody SummaryRequest query)
    {
        PageHelper.startPage(1, Integer.MAX_VALUE);
        if (null == query.getQuery()) {
            query.setQuery(new ArrayList<>());
        }
        List<AppUserLevel> list = appUserLevelService.summary(query);
        query.getGroupBy().addAll(query.getColumns());
        ExcelUtil<AppUserLevel> util = new ExcelUtil<>(AppUserLevel.class);
        util.exportExcel(response, list, "导出数据");
    }



    /**
     * 下载导入模板
     */
    @PreAuthorize("@ss.hasPermi('app/users:level:export')")
    @Log(title = "下载导入模板", businessType = BusinessType.EXPORT)
    @PostMapping("/downloadImportModule")
    public void downloadImportModule()
    {
        appUserLevelService.downloadImportModule();
    }

    /**
     * 导入数据
     */
    @PreAuthorize("@ss.hasPermi('app/users:level:export')")
    @Log(title = "导入数据", businessType = BusinessType.EXPORT)
    @PostMapping("/importData")
    public void importData(MultipartFile file)
    {

        appUserLevelService.importData(file);
    }


    /**
     * 查询自定义分析数据-汇总
     */
    @PreAuthorize("@ss.hasPermi('app/users:level:list')")
    @RequestMapping(value = "/allSummary")
    public AjaxResult allSummary(@RequestBody SummaryRequest query)
    {
        PageHelper.startPage(1, 1);
        return AjaxResult.success(appUserLevelService.allSummary(query));
    }
}
