package tv.shorthub.admin.controller.app;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.PageHelper;
import tv.shorthub.common.annotation.AutoCache;
import tv.shorthub.common.annotation.CacheRefresh;
import tv.shorthub.common.core.cache.refresh.CacheRefreshExecutor;
import tv.shorthub.common.core.domain.SummaryRequest;
import tv.shorthub.common.enums.CompressTypeEnum;
import tv.shorthub.common.utils.SummaryColumnUtils;
import org.springframework.web.multipart.MultipartFile;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import tv.shorthub.common.annotation.Log;
import tv.shorthub.common.core.controller.BaseController;
import tv.shorthub.common.core.domain.AjaxResult;
import tv.shorthub.common.enums.BusinessType;
import tv.shorthub.system.domain.AppDramaContentsSerial;
import tv.shorthub.system.domain.AppDramaContentsSerialUrl;
import tv.shorthub.system.service.IAppDramaContentsSerialService;
import tv.shorthub.common.utils.poi.ExcelUtil;
import tv.shorthub.common.core.page.TableDataInfo;
import tv.shorthub.system.service.IAppDramaContentsSerialUrlService;

/**
 * 剧集Controller
 *
 * <AUTHOR>
 * @date 2025-05-08
 */
@RestController
@RequestMapping("/app/drama_serial")
public class AppDramaContentsSerialController extends BaseController
{
    @Autowired
    private IAppDramaContentsSerialService appDramaContentsSerialService;

    @Autowired
    private IAppDramaContentsSerialUrlService appDramaContentsSerialUrlService;

    /**
     * 查询剧集列表
     */
    @PreAuthorize("@ss.hasPermi('app:drama_serial:list')")
    @GetMapping("/list")
    public TableDataInfo list(AppDramaContentsSerial appDramaContentsSerial)
    {
        startPage();
        List<AppDramaContentsSerial> list = appDramaContentsSerialService.selectList(appDramaContentsSerial);
        TableDataInfo dataTable = getDataTable(list);
        List<AppDramaContentsSerial> rows = (List<AppDramaContentsSerial>) dataTable.getRows();
        for (AppDramaContentsSerial row : rows) {
            QueryWrapper<AppDramaContentsSerialUrl> queryUrl = new QueryWrapper<AppDramaContentsSerialUrl>()
                    .eq("serial_id", row.getId())
                    .eq("compress_type", CompressTypeEnum.COMPRESSED.getValue())
                    .eq("video_format", "mp4")
                    ;
            AppDramaContentsSerialUrl compressUrl = appDramaContentsSerialUrlService.getMapper().selectOne(queryUrl);
            row.getParams().put("compressUrl", compressUrl);
        }
        return dataTable;
    }

    /**
     * 获取剧集数据汇总
     */
    @PreAuthorize("@ss.hasPermi('app:drama_serial:query')")
    @GetMapping(value = "/getSummary")
    public AjaxResult getSummary(AppDramaContentsSerial appDramaContentsSerial)
    {
        return AjaxResult.success(appDramaContentsSerialService.getSummary(appDramaContentsSerial));
    }

    /**
     * 导出剧集列表
     */
    @PreAuthorize("@ss.hasPermi('app:drama_serial:export')")
    @Log(title = "剧集", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AppDramaContentsSerial appDramaContentsSerial)
    {
        List<AppDramaContentsSerial> list = appDramaContentsSerialService.selectList(appDramaContentsSerial);
        ExcelUtil<AppDramaContentsSerial> util = new ExcelUtil<>(AppDramaContentsSerial.class);
        util.exportExcel(response, list, "剧集数据");
    }

    /**
     * 获取剧集详细信息
     */
    @PreAuthorize("@ss.hasPermi('app:drama_serial:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(appDramaContentsSerialService.getById(id));
    }

    /**
     * 新增剧集
     */
    @PreAuthorize("@ss.hasPermi('app:drama_serial:add')")
    @Log(title = "剧集", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AppDramaContentsSerial appDramaContentsSerial)
    {
        int insert = appDramaContentsSerialService.insert(appDramaContentsSerial);
        CacheRefreshExecutor.refreshByKey("app_drama_contents_serial");
        return toAjax(insert);
    }

    /**
     * 修改剧集
     */
    @PreAuthorize("@ss.hasPermi('app:drama_serial:edit')")
    @Log(title = "剧集", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AppDramaContentsSerial appDramaContentsSerial)
    {
        return toAjax(appDramaContentsSerialService.update(appDramaContentsSerial));
    }

    /**
     * 删除剧集
     */
    @PreAuthorize("@ss.hasPermi('app:drama_serial:remove')")
    @Log(title = "剧集", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable List<Long> ids)
    {
        return toAjax(appDramaContentsSerialService.deleteByIds(ids));
    }


    /**
     * 查询自定义分析列
     */
    @PreAuthorize("@ss.hasPermi('app:drama_serial:list')")
    @GetMapping("/columns")
    public AjaxResult columns()
    {
        return AjaxResult.success(SummaryColumnUtils.get(AppDramaContentsSerial.class));
    }

    /**
     * 查询自定义分析数据
     */
    @PreAuthorize("@ss.hasPermi('app:drama_serial:list')")
    @RequestMapping(value = "/summary")
    public TableDataInfo summary(@RequestBody SummaryRequest query)
    {
        startPage();
        List<AppDramaContentsSerial> summary = appDramaContentsSerialService.summary(query);
        return getDataTable(summary);
    }

    /**
     * 导出自定义分析数据
     */
    @PreAuthorize("@ss.hasPermi('app:drama_serial:export')")
    @Log(title = "导出自定义分析数据", businessType = BusinessType.EXPORT)
    @PostMapping("/summaryExport")
    public void summaryExport(HttpServletResponse response, @RequestBody SummaryRequest query)
    {
        PageHelper.startPage(1, Integer.MAX_VALUE);
        if (null == query.getQuery()) {
            query.setQuery(new ArrayList<>());
        }
        List<AppDramaContentsSerial> list = appDramaContentsSerialService.summary(query);
        query.getGroupBy().addAll(query.getColumns());
        ExcelUtil<AppDramaContentsSerial> util = new ExcelUtil<>(AppDramaContentsSerial.class);
        util.exportExcel(response, list, "导出数据");
    }



    /**
     * 下载导入模板
     */
    @PreAuthorize("@ss.hasPermi('app:drama_serial:export')")
    @Log(title = "下载导入模板", businessType = BusinessType.EXPORT)
    @PostMapping("/downloadImportModule")
    public void downloadImportModule()
    {
        appDramaContentsSerialService.downloadImportModule();
    }

    /**
     * 导入数据
     */
    @PreAuthorize("@ss.hasPermi('app:drama_serial:export')")
    @Log(title = "导入数据", businessType = BusinessType.EXPORT)
    @PostMapping("/importData")
    public void importData(MultipartFile file)
    {

        appDramaContentsSerialService.importData(file);
    }


    /**
     * 查询自定义分析数据-汇总
     */
    @PreAuthorize("@ss.hasPermi('app:drama_serial:list')")
    @RequestMapping(value = "/allSummary")
    public AjaxResult allSummary(@RequestBody SummaryRequest query)
    {
        PageHelper.startPage(1, 1);
        return AjaxResult.success(appDramaContentsSerialService.allSummary(query));
    }

    @PreAuthorize("@ss.hasPermi('app:drama_serial:edit')")
    @Log(title = "强制更新剧集链接", businessType = BusinessType.UPDATE)
    @PutMapping("/updateUrl")
    public AjaxResult updateUrl(@RequestBody AppDramaContentsSerial appDramaContentsSerial) {
        appDramaContentsSerialService.updateUrl(appDramaContentsSerial);
        return AjaxResult.success();
    }

    /**
     * 更新剧集排序
     */
    @PreAuthorize("@ss.hasPermi('app:drama_serial:edit')")
    @Log(title = "剧集排序", businessType = BusinessType.UPDATE)
    @PutMapping("/updateOrder")
    public AjaxResult updateOrder(@RequestBody List<AppDramaContentsSerial> orderList) {
        if (orderList == null || orderList.isEmpty()) {
            return error("无更新数据");
        }

        // 校验所有剧集是否属于同一个 content_id
        String contentId = orderList.getFirst().getContentId();
        boolean allSame = orderList.stream().allMatch(e -> contentId.equals(e.getContentId()));
        if (!allSame) {
            return error("只能对同一剧集的剧集进行排序");
        }
        // 校验 serialNumber 是否重复
        Set<Long> serialNumbers = orderList.stream()
                .map(AppDramaContentsSerial::getSerialNumber)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        if (serialNumbers.size() < orderList.size()) {
            return error("不能有重复的 serial_number");
        }
        int count = appDramaContentsSerialService.updateOrder(orderList);
        return toAjax(count > 0);
    }


}
