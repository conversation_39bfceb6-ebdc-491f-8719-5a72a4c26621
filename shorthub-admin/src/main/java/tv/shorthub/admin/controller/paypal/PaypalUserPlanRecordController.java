package tv.shorthub.admin.controller.paypal;

import com.github.pagehelper.PageHelper;
import tv.shorthub.common.core.domain.SummaryRequest;
import tv.shorthub.common.utils.SummaryColumnUtils;
import org.springframework.web.multipart.MultipartFile;
import java.util.ArrayList;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import tv.shorthub.common.annotation.Log;
import tv.shorthub.common.core.controller.BaseController;
import tv.shorthub.common.core.domain.AjaxResult;
import tv.shorthub.common.enums.BusinessType;
import tv.shorthub.system.domain.PaypalUserPlanRecord;
import tv.shorthub.system.service.IPaypalUserPlanRecordService;
import tv.shorthub.common.utils.poi.ExcelUtil;
import tv.shorthub.common.core.page.TableDataInfo;

/**
 * paypal签约续订记录Controller
 *
 * <AUTHOR>
 * @date 2025-06-27
 */
@RestController
@RequestMapping("/app/paypal/user/plan/record")
public class PaypalUserPlanRecordController extends BaseController
{
    @Autowired
    private IPaypalUserPlanRecordService paypalUserPlanRecordService;

    /**
     * 查询paypal签约续订记录列表
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:user/plan/record:list')")
    @GetMapping("/list")
    public TableDataInfo list(PaypalUserPlanRecord paypalUserPlanRecord)
    {
        startPage();
        List<PaypalUserPlanRecord> list = paypalUserPlanRecordService.selectList(paypalUserPlanRecord);
        return getDataTable(list);
    }

    /**
     * 获取paypal签约续订记录数据汇总
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:user/plan/record:query')")
    @GetMapping(value = "/getSummary")
    public AjaxResult getSummary(PaypalUserPlanRecord paypalUserPlanRecord)
    {
        return AjaxResult.success(paypalUserPlanRecordService.getSummary(paypalUserPlanRecord));
    }

    /**
     * 导出paypal签约续订记录列表
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:user/plan/record:export')")
    @Log(title = "paypal签约续订记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PaypalUserPlanRecord paypalUserPlanRecord)
    {
        List<PaypalUserPlanRecord> list = paypalUserPlanRecordService.selectList(paypalUserPlanRecord);
        ExcelUtil<PaypalUserPlanRecord> util = new ExcelUtil<>(PaypalUserPlanRecord.class);
        util.exportExcel(response, list, "paypal签约续订记录数据");
    }

    /**
     * 获取paypal签约续订记录详细信息
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:user/plan/record:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(paypalUserPlanRecordService.getById(id));
    }

    /**
     * 新增paypal签约续订记录
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:user/plan/record:add')")
    @Log(title = "paypal签约续订记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PaypalUserPlanRecord paypalUserPlanRecord)
    {
        return toAjax(paypalUserPlanRecordService.insert(paypalUserPlanRecord));
    }

    /**
     * 修改paypal签约续订记录
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:user/plan/record:edit')")
    @Log(title = "paypal签约续订记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PaypalUserPlanRecord paypalUserPlanRecord)
    {
        return toAjax(paypalUserPlanRecordService.update(paypalUserPlanRecord));
    }

    /**
     * 删除paypal签约续订记录
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:user/plan/record:remove')")
    @Log(title = "paypal签约续订记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable List<Long> ids)
    {
        return toAjax(paypalUserPlanRecordService.deleteByIds(ids));
    }


    /**
     * 查询自定义分析列
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:user/plan/record:list')")
    @GetMapping("/columns")
    public AjaxResult columns()
    {
        return AjaxResult.success(SummaryColumnUtils.get(PaypalUserPlanRecord.class));
    }

    /**
     * 查询自定义分析数据
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:user/plan/record:list')")
    @RequestMapping(value = "/summary")
    public TableDataInfo summary(@RequestBody SummaryRequest query)
    {
        startPage();
        List<PaypalUserPlanRecord> summary = paypalUserPlanRecordService.summary(query);
        return getDataTable(summary);
    }

    /**
     * 导出自定义分析数据
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:user/plan/record:export')")
    @Log(title = "导出自定义分析数据", businessType = BusinessType.EXPORT)
    @PostMapping("/summaryExport")
    public void summaryExport(HttpServletResponse response, @RequestBody SummaryRequest query)
    {
        PageHelper.startPage(1, Integer.MAX_VALUE);
        if (null == query.getQuery()) {
            query.setQuery(new ArrayList<>());
        }
        List<PaypalUserPlanRecord> list = paypalUserPlanRecordService.summary(query);
        query.getGroupBy().addAll(query.getColumns());
        ExcelUtil<PaypalUserPlanRecord> util = new ExcelUtil<>(PaypalUserPlanRecord.class);
        util.exportExcel(response, list, "导出数据");
    }



    /**
     * 下载导入模板
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:user/plan/record:export')")
    @Log(title = "下载导入模板", businessType = BusinessType.EXPORT)
    @PostMapping("/downloadImportModule")
    public void downloadImportModule()
    {
        paypalUserPlanRecordService.downloadImportModule();
    }

    /**
     * 导入数据
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:user/plan/record:export')")
    @Log(title = "导入数据", businessType = BusinessType.EXPORT)
    @PostMapping("/importData")
    public void importData(MultipartFile file)
    {

        paypalUserPlanRecordService.importData(file);
    }


    /**
     * 查询自定义分析数据-汇总
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:user/plan/record:list')")
    @RequestMapping(value = "/allSummary")
    public AjaxResult allSummary(@RequestBody SummaryRequest query)
    {
        PageHelper.startPage(1, 1);
        return AjaxResult.success(paypalUserPlanRecordService.allSummary(query));
    }
}
