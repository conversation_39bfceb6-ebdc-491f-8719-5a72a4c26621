package tv.shorthub.admin.controller.statistics;

import com.github.pagehelper.PageHelper;
import tv.shorthub.common.core.domain.SummaryRequest;
import tv.shorthub.common.utils.SummaryColumnUtils;
import org.springframework.web.multipart.MultipartFile;
import java.util.ArrayList;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import tv.shorthub.common.annotation.Log;
import tv.shorthub.common.core.controller.BaseController;
import tv.shorthub.common.core.domain.AjaxResult;
import tv.shorthub.common.enums.BusinessType;
import tv.shorthub.system.domain.StatisticsHourlyDramaStats;
import tv.shorthub.system.service.IStatisticsHourlyDramaStatsService;
import tv.shorthub.statistics.service.IHourlyDramaStatsService;
import tv.shorthub.common.utils.poi.ExcelUtil;
import tv.shorthub.common.core.page.TableDataInfo;
import tv.shorthub.common.utils.DateUtils;
import tv.shorthub.statistics.dto.TimeRangeRequest;
import tv.shorthub.statistics.dto.BatchUpdateRequest;
import java.util.Date;
import java.util.Map;
import org.springframework.web.bind.annotation.RequestParam;
import lombok.extern.slf4j.Slf4j;
import tv.shorthub.admin.utils.StatisticsControllerPermissionUtils;

/**
 * 每小时剧集分析统计Controller
 *
 * <AUTHOR>
 * @date 2025-07-05
 */
@Slf4j
@RestController
@RequestMapping("/statistics/drama")
public class StatisticsHourlyDramaStatsController extends BaseController
{
    @Autowired
    private IStatisticsHourlyDramaStatsService statisticsHourlyDramaStatsService;

    @Autowired
    private IHourlyDramaStatsService hourlyDramaStatsService;

    @Autowired
    private StatisticsControllerPermissionUtils controllerPermissionUtils;

    /**
     * 查询每小时剧集分析统计列表
     */
    @PreAuthorize("@ss.hasPermi('statistics:drama:list')")
    @GetMapping("/list")
    public TableDataInfo list(
            StatisticsHourlyDramaStats statisticsHourlyDramaStats)
    {
        startPage();
        List<StatisticsHourlyDramaStats> list = statisticsHourlyDramaStatsService.selectList(statisticsHourlyDramaStats);
        return getDataTable(list);
    }

    /**
     * 获取每小时剧集分析统计数据汇总
     */
    @PreAuthorize("@ss.hasPermi('statistics:drama:query')")
    @GetMapping(value = "/getSummary")
    public AjaxResult getSummary(StatisticsHourlyDramaStats statisticsHourlyDramaStats)
    {
        return AjaxResult.success(statisticsHourlyDramaStatsService.getSummary(statisticsHourlyDramaStats));
    }

    /**
     * 获取每小时剧集分析统计汇总数据
     */
    @PreAuthorize("@ss.hasPermi('statistics:drama:query')")
    @GetMapping("/getRangeSummary")
    public AjaxResult getRangeSummary(@RequestParam("startTime") Date startTime,
                                      @RequestParam("endTime") Date endTime,
                                      @RequestParam(value = "appid", required = false) String appid,
                                      @RequestParam(value = "tfid", required = false) String tfid,
                                      @RequestParam(value = "delivererName", required = false) String delivererName) {
        log.info("[剧集统计] 区间汇总查询参数 - startTime: {}, endTime: {}, appid: {}, tfid: {}, delivererName: {}", startTime, endTime, appid, tfid, delivererName);

        // 处理按投手筛选的逻辑
        AjaxResult delivererResult = controllerPermissionUtils.handleDelivererQuery(
            delivererName, tfid, startTime, endTime,
            params -> statisticsHourlyDramaStatsService.getSummaryByTfids(params.tfids, params.startTime, params.endTime),
            new StatisticsHourlyDramaStats()
        );
        if (delivererResult != null) {
            return delivererResult;
        }

        // 处理默认查询
        AjaxResult defaultResult = controllerPermissionUtils.handleDefaultQuery(
            appid, tfid, startTime, endTime,
            params -> statisticsHourlyDramaStatsService.getSummaryByTfids(params.tfids, params.startTime, params.endTime),
            params -> statisticsHourlyDramaStatsService.getSummaryByCreator(params.creatorName, params.startTime, params.endTime),
            params -> statisticsHourlyDramaStatsService.getRangeSummary(params.startTime, params.endTime, params.appid, null),
            new StatisticsHourlyDramaStats()
        );
        if (defaultResult != null) {
            return defaultResult;
        }

        // 处理其他情况
        return controllerPermissionUtils.handleOtherQuery(
            appid, tfid, startTime, endTime,
            params -> statisticsHourlyDramaStatsService.getRangeSummary(params.startTime, params.endTime, params.appid, params.tfid)
        );
    }

    /**
     * 导出每小时剧集分析统计列表
     */
    @PreAuthorize("@ss.hasPermi('statistics:drama:export')")
    @Log(title = "每小时剧集分析统计", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, StatisticsHourlyDramaStats statisticsHourlyDramaStats)
    {
        List<StatisticsHourlyDramaStats> list = statisticsHourlyDramaStatsService.selectList(statisticsHourlyDramaStats);
        ExcelUtil<StatisticsHourlyDramaStats> util = new ExcelUtil<>(StatisticsHourlyDramaStats.class);
        util.exportExcel(response, list, "每小时剧集分析统计数据");
    }

    /**
     * 获取每小时剧集分析统计详细信息
     */
    @PreAuthorize("@ss.hasPermi('statistics:drama:query')")
    @GetMapping(value = "/info/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(statisticsHourlyDramaStatsService.getById(id));
    }

    /**
     * 新增每小时剧集分析统计
     */
    @PreAuthorize("@ss.hasPermi('statistics:drama:add')")
    @Log(title = "每小时剧集分析统计", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody StatisticsHourlyDramaStats statisticsHourlyDramaStats)
    {
        return toAjax(statisticsHourlyDramaStatsService.insert(statisticsHourlyDramaStats));
    }

    /**
     * 修改每小时剧集分析统计
     */
    @PreAuthorize("@ss.hasPermi('statistics:drama:edit')")
    @Log(title = "每小时剧集分析统计", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody StatisticsHourlyDramaStats statisticsHourlyDramaStats)
    {
        return toAjax(statisticsHourlyDramaStatsService.update(statisticsHourlyDramaStats));
    }

    /**
     * 删除每小时剧集分析统计
     */
    @PreAuthorize("@ss.hasPermi('statistics:drama:remove')")
    @Log(title = "每小时剧集分析统计", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable List<Long> ids)
    {
        return toAjax(statisticsHourlyDramaStatsService.deleteByIds(ids));
    }


    /**
     * 查询自定义分析列
     */
    @PreAuthorize("@ss.hasPermi('statistics:drama:list')")
    @GetMapping("/columns")
    public AjaxResult columns()
    {
        return AjaxResult.success(SummaryColumnUtils.get(StatisticsHourlyDramaStats.class));
    }

    /**
     * 查询自定义分析数据
     */
    @PreAuthorize("@ss.hasPermi('statistics:drama:list')")
    @RequestMapping(value = "/summary")
    public TableDataInfo summary(@RequestBody SummaryRequest query)
    {
        startPage();
        List<StatisticsHourlyDramaStats> summary = statisticsHourlyDramaStatsService.summary(query);
        return getDataTable(summary);
    }

    /**
     * 导出自定义分析数据
     */
    @PreAuthorize("@ss.hasPermi('statistics:drama:export')")
    @Log(title = "导出自定义分析数据", businessType = BusinessType.EXPORT)
    @PostMapping("/summaryExport")
    public void summaryExport(HttpServletResponse response, @RequestBody SummaryRequest query)
    {
        PageHelper.startPage(1, Integer.MAX_VALUE);
        if (null == query.getQuery()) {
            query.setQuery(new ArrayList<>());
        }
        List<StatisticsHourlyDramaStats> list = statisticsHourlyDramaStatsService.summary(query);
        query.getGroupBy().addAll(query.getColumns());
        ExcelUtil<StatisticsHourlyDramaStats> util = new ExcelUtil<>(StatisticsHourlyDramaStats.class);
        util.exportExcel(response, list, "导出数据");
    }



    /**
     * 下载导入模板
     */
    @PreAuthorize("@ss.hasPermi('statistics:drama:export')")
    @Log(title = "下载导入模板", businessType = BusinessType.EXPORT)
    @PostMapping("/downloadImportModule")
    public void downloadImportModule()
    {
        statisticsHourlyDramaStatsService.downloadImportModule();
    }

    /**
     * 导入数据
     */
    @PreAuthorize("@ss.hasPermi('statistics:drama:export')")
    @Log(title = "导入数据", businessType = BusinessType.EXPORT)
    @PostMapping("/importData")
    public void importData(MultipartFile file)
    {
        statisticsHourlyDramaStatsService.importData(file);
    }

    /**
     * 查询自定义分析数据-汇总
     */
    @PreAuthorize("@ss.hasPermi('statistics:drama:list')")
    @RequestMapping(value = "/allSummary")
    public AjaxResult allSummary(@RequestBody SummaryRequest query)
    {
        PageHelper.startPage(1, 1);
        return AjaxResult.success(statisticsHourlyDramaStatsService.allSummary(query));
    }

    /**
     * 全量统计最近N天的剧集数据
     */
    @PreAuthorize("@ss.hasPermi('statistics:drama:edit')")
    @Log(title = "全量统计剧集数据", businessType = BusinessType.UPDATE)
    @PostMapping("/batchUpdate")
    public AjaxResult batchUpdateRecentStats(@RequestBody BatchUpdateRequest request)
    {
        try {
            Integer days = request.getDays();

            // 默认统计最近7天
            if (days == null || days <= 0) {
                days = 7;
            }

            // 限制最多统计365天
            if (days > 365) {
                days = 365;
            }

            String result = hourlyDramaStatsService.batchUpdateRecentDramaStats(days);
            return AjaxResult.success(result);
        } catch (Exception e) {
            return AjaxResult.error("全量统计失败: " + e.getMessage());
        }
    }

    /**
     * 全量统计指定时间范围的剧集数据
     */
    @PreAuthorize("@ss.hasPermi('statistics:drama:edit')")
    @Log(title = "全量统计剧集数据", businessType = BusinessType.UPDATE)
    @PostMapping("/batchUpdateRange")
    public AjaxResult batchUpdateRangeStats(@RequestBody BatchUpdateRequest request)
    {
        try {
            if (!request.isValidDateRange()) {
                return AjaxResult.error("日期范围参数无效");
            }

            String result = hourlyDramaStatsService.batchUpdateDramaStats(request.getStartDate(), request.getEndDate());
            return AjaxResult.success(result);
        } catch (Exception e) {
            return AjaxResult.error("全量统计失败: " + e.getMessage());
        }
    }

}
