package tv.shorthub.admin.controller.app;

import com.github.pagehelper.PageHelper;
import tv.shorthub.common.core.domain.SummaryRequest;
import tv.shorthub.common.utils.SummaryColumnUtils;
import org.springframework.web.multipart.MultipartFile;
import java.util.ArrayList;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import tv.shorthub.common.annotation.Log;
import tv.shorthub.common.core.controller.BaseController;
import tv.shorthub.common.core.domain.AjaxResult;
import tv.shorthub.common.enums.BusinessType;
import tv.shorthub.system.domain.AppConsumptionUnlockContent;
import tv.shorthub.system.service.IAppConsumptionUnlockContentService;
import tv.shorthub.common.utils.poi.ExcelUtil;
import tv.shorthub.common.core.page.TableDataInfo;

/**
 * 剧目解锁Controller
 *
 * <AUTHOR>
 * @date 2025-05-15
 */
@RestController
@RequestMapping("/app/user/unlockcontent")
public class AppConsumptionUnlockContentController extends BaseController
{
    @Autowired
    private IAppConsumptionUnlockContentService appConsumptionUnlockContentService;

    /**
     * 查询剧目解锁列表
     */
    @PreAuthorize("@ss.hasPermi('app/user:unlockcontent:list')")
    @GetMapping("/list")
    public TableDataInfo list(AppConsumptionUnlockContent appConsumptionUnlockContent)
    {
        startPage();
        List<AppConsumptionUnlockContent> list = appConsumptionUnlockContentService.selectList(appConsumptionUnlockContent);
        return getDataTable(list);
    }

    /**
     * 获取剧目解锁数据汇总
     */
    @PreAuthorize("@ss.hasPermi('app/user:unlockcontent:query')")
    @GetMapping(value = "/getSummary")
    public AjaxResult getSummary(AppConsumptionUnlockContent appConsumptionUnlockContent)
    {
        return AjaxResult.success(appConsumptionUnlockContentService.getSummary(appConsumptionUnlockContent));
    }

    /**
     * 导出剧目解锁列表
     */
    @PreAuthorize("@ss.hasPermi('app/user:unlockcontent:export')")
    @Log(title = "剧目解锁", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AppConsumptionUnlockContent appConsumptionUnlockContent)
    {
        List<AppConsumptionUnlockContent> list = appConsumptionUnlockContentService.selectList(appConsumptionUnlockContent);
        ExcelUtil<AppConsumptionUnlockContent> util = new ExcelUtil<>(AppConsumptionUnlockContent.class);
        util.exportExcel(response, list, "剧目解锁数据");
    }

    /**
     * 获取剧目解锁详细信息
     */
    @PreAuthorize("@ss.hasPermi('app/user:unlockcontent:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(appConsumptionUnlockContentService.getById(id));
    }

    /**
     * 新增剧目解锁
     */
    @PreAuthorize("@ss.hasPermi('app/user:unlockcontent:add')")
    @Log(title = "剧目解锁", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AppConsumptionUnlockContent appConsumptionUnlockContent)
    {
        return toAjax(appConsumptionUnlockContentService.insert(appConsumptionUnlockContent));
    }

    /**
     * 修改剧目解锁
     */
    @PreAuthorize("@ss.hasPermi('app/user:unlockcontent:edit')")
    @Log(title = "剧目解锁", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AppConsumptionUnlockContent appConsumptionUnlockContent)
    {
        return toAjax(appConsumptionUnlockContentService.update(appConsumptionUnlockContent));
    }

    /**
     * 删除剧目解锁
     */
    @PreAuthorize("@ss.hasPermi('app/user:unlockcontent:remove')")
    @Log(title = "剧目解锁", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable List<Long> ids)
    {
        return toAjax(appConsumptionUnlockContentService.deleteByIds(ids));
    }


    /**
     * 查询自定义分析列
     */
    @PreAuthorize("@ss.hasPermi('app/user:unlockcontent:list')")
    @GetMapping("/columns")
    public AjaxResult columns()
    {
        return AjaxResult.success(SummaryColumnUtils.get(AppConsumptionUnlockContent.class));
    }

    /**
     * 查询自定义分析数据
     */
    @PreAuthorize("@ss.hasPermi('app/user:unlockcontent:list')")
    @RequestMapping(value = "/summary")
    public TableDataInfo summary(@RequestBody SummaryRequest query)
    {
        startPage();
        List<AppConsumptionUnlockContent> summary = appConsumptionUnlockContentService.summary(query);
        return getDataTable(summary);
    }

    /**
     * 导出自定义分析数据
     */
    @PreAuthorize("@ss.hasPermi('app/user:unlockcontent:export')")
    @Log(title = "导出自定义分析数据", businessType = BusinessType.EXPORT)
    @PostMapping("/summaryExport")
    public void summaryExport(HttpServletResponse response, @RequestBody SummaryRequest query)
    {
        PageHelper.startPage(1, Integer.MAX_VALUE);
        if (null == query.getQuery()) {
            query.setQuery(new ArrayList<>());
        }
        List<AppConsumptionUnlockContent> list = appConsumptionUnlockContentService.summary(query);
        query.getGroupBy().addAll(query.getColumns());
        ExcelUtil<AppConsumptionUnlockContent> util = new ExcelUtil<>(AppConsumptionUnlockContent.class);
        util.exportExcel(response, list, "导出数据");
    }



    /**
     * 下载导入模板
     */
    @PreAuthorize("@ss.hasPermi('app/user:unlockcontent:export')")
    @Log(title = "下载导入模板", businessType = BusinessType.EXPORT)
    @PostMapping("/downloadImportModule")
    public void downloadImportModule()
    {
        appConsumptionUnlockContentService.downloadImportModule();
    }

    /**
     * 导入数据
     */
    @PreAuthorize("@ss.hasPermi('app/user:unlockcontent:export')")
    @Log(title = "导入数据", businessType = BusinessType.EXPORT)
    @PostMapping("/importData")
    public void importData(MultipartFile file)
    {

        appConsumptionUnlockContentService.importData(file);
    }


    /**
     * 查询自定义分析数据-汇总
     */
    @PreAuthorize("@ss.hasPermi('app/user:unlockcontent:list')")
    @RequestMapping(value = "/allSummary")
    public AjaxResult allSummary(@RequestBody SummaryRequest query)
    {
        PageHelper.startPage(1, 1);
        return AjaxResult.success(appConsumptionUnlockContentService.allSummary(query));
    }
}
