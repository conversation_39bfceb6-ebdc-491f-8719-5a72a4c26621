package tv.shorthub.admin.controller.app;

import com.github.pagehelper.PageHelper;
import tv.shorthub.common.core.domain.SummaryRequest;
import tv.shorthub.common.utils.SummaryColumnUtils;
import org.springframework.web.multipart.MultipartFile;
import java.util.ArrayList;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import tv.shorthub.common.annotation.Log;
import tv.shorthub.common.core.controller.BaseController;
import tv.shorthub.common.core.domain.AjaxResult;
import tv.shorthub.common.enums.BusinessType;
import tv.shorthub.system.domain.PaypalDispute;
import tv.shorthub.system.service.IPaypalDisputeService;
import tv.shorthub.common.utils.poi.ExcelUtil;
import tv.shorthub.common.core.page.TableDataInfo;

/**
 * PayPal争议Controller
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
@RestController
@RequestMapping("/app/paypal/dispute")
public class PaypalDisputeController extends BaseController
{
    @Autowired
    private IPaypalDisputeService paypalDisputeService;

    /**
     * 查询PayPal争议列表
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:dispute:list')")
    @GetMapping("/list")
    public TableDataInfo list(PaypalDispute paypalDispute)
    {
        startPage();
        List<PaypalDispute> list = paypalDisputeService.selectList(paypalDispute);
        return getDataTable(list);
    }

    /**
     * 获取PayPal争议数据汇总
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:dispute:query')")
    @GetMapping(value = "/getSummary")
    public AjaxResult getSummary(PaypalDispute paypalDispute)
    {
        return AjaxResult.success(paypalDisputeService.getSummary(paypalDispute));
    }

    /**
     * 导出PayPal争议列表
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:dispute:export')")
    @Log(title = "PayPal争议", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PaypalDispute paypalDispute)
    {
        List<PaypalDispute> list = paypalDisputeService.selectList(paypalDispute);
        ExcelUtil<PaypalDispute> util = new ExcelUtil<>(PaypalDispute.class);
        util.exportExcel(response, list, "PayPal争议数据");
    }

    /**
     * 获取PayPal争议详细信息
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:dispute:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(paypalDisputeService.getById(id));
    }

    /**
     * 新增PayPal争议
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:dispute:add')")
    @Log(title = "PayPal争议", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PaypalDispute paypalDispute)
    {
        return toAjax(paypalDisputeService.insert(paypalDispute));
    }

    /**
     * 修改PayPal争议
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:dispute:edit')")
    @Log(title = "PayPal争议", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PaypalDispute paypalDispute)
    {
        return toAjax(paypalDisputeService.update(paypalDispute));
    }

    /**
     * 删除PayPal争议
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:dispute:remove')")
    @Log(title = "PayPal争议", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable List<Long> ids)
    {
        return toAjax(paypalDisputeService.deleteByIds(ids));
    }


    /**
     * 查询自定义分析列
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:dispute:list')")
    @GetMapping("/columns")
    public AjaxResult columns()
    {
        return AjaxResult.success(SummaryColumnUtils.get(PaypalDispute.class));
    }

    /**
     * 查询自定义分析数据
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:dispute:list')")
    @RequestMapping(value = "/summary")
    public TableDataInfo summary(@RequestBody SummaryRequest query)
    {
        startPage();
        List<PaypalDispute> summary = paypalDisputeService.summary(query);
        return getDataTable(summary);
    }

    /**
     * 导出自定义分析数据
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:dispute:export')")
    @Log(title = "导出自定义分析数据", businessType = BusinessType.EXPORT)
    @PostMapping("/summaryExport")
    public void summaryExport(HttpServletResponse response, @RequestBody SummaryRequest query)
    {
        PageHelper.startPage(1, Integer.MAX_VALUE);
        if (null == query.getQuery()) {
            query.setQuery(new ArrayList<>());
        }
        List<PaypalDispute> list = paypalDisputeService.summary(query);
        query.getGroupBy().addAll(query.getColumns());
        ExcelUtil<PaypalDispute> util = new ExcelUtil<>(PaypalDispute.class);
        util.exportExcel(response, list, "导出数据");
    }



    /**
     * 下载导入模板
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:dispute:export')")
    @Log(title = "下载导入模板", businessType = BusinessType.EXPORT)
    @PostMapping("/downloadImportModule")
    public void downloadImportModule()
    {
        paypalDisputeService.downloadImportModule();
    }

    /**
     * 导入数据
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:dispute:export')")
    @Log(title = "导入数据", businessType = BusinessType.EXPORT)
    @PostMapping("/importData")
    public void importData(MultipartFile file)
    {

        paypalDisputeService.importData(file);
    }


    /**
     * 查询自定义分析数据-汇总
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:dispute:list')")
    @RequestMapping(value = "/allSummary")
    public AjaxResult allSummary(@RequestBody SummaryRequest query)
    {
        PageHelper.startPage(1, 1);
        return AjaxResult.success(paypalDisputeService.allSummary(query));
    }
}
