package tv.shorthub.admin.controller.app;

import com.github.pagehelper.PageHelper;
import tv.shorthub.common.core.domain.SummaryRequest;
import tv.shorthub.common.utils.SummaryColumnUtils;
import org.springframework.web.multipart.MultipartFile;
import java.util.ArrayList;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import tv.shorthub.common.annotation.Log;
import tv.shorthub.common.core.controller.BaseController;
import tv.shorthub.common.core.domain.AjaxResult;
import tv.shorthub.common.enums.BusinessType;
import tv.shorthub.system.domain.AppRechargeItemGift;
import tv.shorthub.system.service.IAppRechargeItemGiftService;
import tv.shorthub.common.utils.poi.ExcelUtil;
import tv.shorthub.common.core.page.TableDataInfo;

/**
 * 充值优惠Controller
 *
 * <AUTHOR>
 * @date 2025-05-10
 */
@RestController
@RequestMapping("/app/recharge_gift")
public class AppRechargeItemGiftController extends BaseController
{
    @Autowired
    private IAppRechargeItemGiftService appRechargeItemGiftService;

    /**
     * 查询充值优惠列表
     */
    @PreAuthorize("@ss.hasPermi('app:recharge_gift:list')")
    @GetMapping("/list")
    public TableDataInfo list(AppRechargeItemGift appRechargeItemGift)
    {
        startPage();
        List<AppRechargeItemGift> list = appRechargeItemGiftService.selectList(appRechargeItemGift);
        return getDataTable(list);
    }

    /**
     * 获取充值优惠数据汇总
     */
    @PreAuthorize("@ss.hasPermi('app:recharge_gift:query')")
    @GetMapping(value = "/getSummary")
    public AjaxResult getSummary(AppRechargeItemGift appRechargeItemGift)
    {
        return AjaxResult.success(appRechargeItemGiftService.getSummary(appRechargeItemGift));
    }

    /**
     * 导出充值优惠列表
     */
    @PreAuthorize("@ss.hasPermi('app:recharge_gift:export')")
    @Log(title = "充值优惠", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AppRechargeItemGift appRechargeItemGift)
    {
        List<AppRechargeItemGift> list = appRechargeItemGiftService.selectList(appRechargeItemGift);
        ExcelUtil<AppRechargeItemGift> util = new ExcelUtil<>(AppRechargeItemGift.class);
        util.exportExcel(response, list, "充值优惠数据");
    }

    /**
     * 获取充值优惠详细信息
     */
    @PreAuthorize("@ss.hasPermi('app:recharge_gift:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(appRechargeItemGiftService.getById(id));
    }

    /**
     * 新增充值优惠
     */
    @PreAuthorize("@ss.hasPermi('app:recharge_gift:add')")
    @Log(title = "充值优惠", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AppRechargeItemGift appRechargeItemGift)
    {
        return toAjax(appRechargeItemGiftService.insert(appRechargeItemGift));
    }

    /**
     * 修改充值优惠
     */
    @PreAuthorize("@ss.hasPermi('app:recharge_gift:edit')")
    @Log(title = "充值优惠", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AppRechargeItemGift appRechargeItemGift)
    {
        return toAjax(appRechargeItemGiftService.update(appRechargeItemGift));
    }

    /**
     * 删除充值优惠
     */
    @PreAuthorize("@ss.hasPermi('app:recharge_gift:remove')")
    @Log(title = "充值优惠", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable List<Long> ids)
    {
        return toAjax(appRechargeItemGiftService.deleteByIds(ids));
    }


    /**
     * 查询自定义分析列
     */
    @PreAuthorize("@ss.hasPermi('app:recharge_gift:list')")
    @GetMapping("/columns")
    public AjaxResult columns()
    {
        return AjaxResult.success(SummaryColumnUtils.get(AppRechargeItemGift.class));
    }

    /**
     * 查询自定义分析数据
     */
    @PreAuthorize("@ss.hasPermi('app:recharge_gift:list')")
    @RequestMapping(value = "/summary")
    public TableDataInfo summary(@RequestBody SummaryRequest query)
    {
        startPage();
        List<AppRechargeItemGift> summary = appRechargeItemGiftService.summary(query);
        return getDataTable(summary);
    }

    /**
     * 导出自定义分析数据
     */
    @PreAuthorize("@ss.hasPermi('app:recharge_gift:export')")
    @Log(title = "导出自定义分析数据", businessType = BusinessType.EXPORT)
    @PostMapping("/summaryExport")
    public void summaryExport(HttpServletResponse response, @RequestBody SummaryRequest query)
    {
        PageHelper.startPage(1, Integer.MAX_VALUE);
        if (null == query.getQuery()) {
            query.setQuery(new ArrayList<>());
        }
        List<AppRechargeItemGift> list = appRechargeItemGiftService.summary(query);
        query.getGroupBy().addAll(query.getColumns());
        ExcelUtil<AppRechargeItemGift> util = new ExcelUtil<>(AppRechargeItemGift.class);
        util.exportExcel(response, list, "导出数据");
    }



    /**
     * 下载导入模板
     */
    @PreAuthorize("@ss.hasPermi('app:recharge_gift:export')")
    @Log(title = "下载导入模板", businessType = BusinessType.EXPORT)
    @PostMapping("/downloadImportModule")
    public void downloadImportModule()
    {
        appRechargeItemGiftService.downloadImportModule();
    }

    /**
     * 导入数据
     */
    @PreAuthorize("@ss.hasPermi('app:recharge_gift:export')")
    @Log(title = "导入数据", businessType = BusinessType.EXPORT)
    @PostMapping("/importData")
    public void importData(MultipartFile file)
    {

        appRechargeItemGiftService.importData(file);
    }


    /**
     * 查询自定义分析数据-汇总
     */
    @PreAuthorize("@ss.hasPermi('app:recharge_gift:list')")
    @RequestMapping(value = "/allSummary")
    public AjaxResult allSummary(@RequestBody SummaryRequest query)
    {
        PageHelper.startPage(1, 1);
        return AjaxResult.success(appRechargeItemGiftService.allSummary(query));
    }
}
