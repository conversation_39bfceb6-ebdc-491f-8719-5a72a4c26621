package tv.shorthub.admin.controller.statistics;

import com.github.pagehelper.PageHelper;
import tv.shorthub.common.core.domain.SummaryRequest;
import tv.shorthub.common.utils.SummaryColumnUtils;
import org.springframework.web.multipart.MultipartFile;
import java.util.ArrayList;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import tv.shorthub.common.annotation.Log;
import tv.shorthub.common.core.controller.BaseController;
import tv.shorthub.common.core.domain.AjaxResult;
import tv.shorthub.common.enums.BusinessType;
import tv.shorthub.system.domain.StatisticsHourlyFunnelStats;
import tv.shorthub.system.service.IStatisticsHourlyFunnelStatsService;
import tv.shorthub.statistics.service.IHourlyFunnelStatsService;
import tv.shorthub.common.utils.poi.ExcelUtil;
import tv.shorthub.common.core.page.TableDataInfo;
import tv.shorthub.common.utils.DateUtils;
import tv.shorthub.statistics.dto.TimeRangeRequest;
import tv.shorthub.statistics.dto.BatchUpdateRequest;
import tv.shorthub.common.utils.SecurityUtils;
import tv.shorthub.admin.utils.StatisticsControllerPermissionUtils;

import java.util.Date;
import java.util.Map;
import org.springframework.web.bind.annotation.RequestParam;
import lombok.extern.slf4j.Slf4j;
import java.util.List;
import java.util.stream.Collectors;
import java.util.HashMap;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

/**
 * 每小时漏斗分析统计Controller
 *
 * <AUTHOR>
 * @date 2025-07-07
 */
@Slf4j
@RestController
@RequestMapping("/statistics/funnel")
public class StatisticsHourlyFunnelStatsController extends BaseController
{
    @Autowired
    private IStatisticsHourlyFunnelStatsService statisticsHourlyFunnelStatsService;

    @Autowired
    private IHourlyFunnelStatsService hourlyFunnelStatsService;

    @Autowired
    private StatisticsControllerPermissionUtils controllerPermissionUtils;

    /**
     * 查询每小时漏斗分析统计列表
     */
    @PreAuthorize("@ss.hasPermi('statistics:funnel:list')")
    @GetMapping("/list")
    public TableDataInfo list(
            StatisticsHourlyFunnelStats statisticsHourlyFunnelStats)
    {
        startPage();
        List<StatisticsHourlyFunnelStats> list = statisticsHourlyFunnelStatsService.selectList(statisticsHourlyFunnelStats);



        return getDataTable(list);
    }

    /**
     * 获取每小时漏斗分析统计数据汇总
     */
    @PreAuthorize("@ss.hasPermi('statistics:funnel:query')")
    @GetMapping(value = "/getSummary")
    public AjaxResult getSummary(StatisticsHourlyFunnelStats statisticsHourlyFunnelStats)
    {
        return AjaxResult.success(statisticsHourlyFunnelStatsService.getSummary(statisticsHourlyFunnelStats));
    }

    /**
     * 导出每小时漏斗分析统计列表
     */
    @PreAuthorize("@ss.hasPermi('statistics:funnel:export')")
    @Log(title = "每小时漏斗分析统计", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, StatisticsHourlyFunnelStats statisticsHourlyFunnelStats)
    {
        List<StatisticsHourlyFunnelStats> list = statisticsHourlyFunnelStatsService.selectList(statisticsHourlyFunnelStats);
        ExcelUtil<StatisticsHourlyFunnelStats> util = new ExcelUtil<>(StatisticsHourlyFunnelStats.class);
        util.exportExcel(response, list, "每小时漏斗分析统计数据");
    }

    /**
     * 获取每小时漏斗分析统计详细信息
     */
    @PreAuthorize("@ss.hasPermi('statistics:funnel:query')")
    @GetMapping(value = "/info/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(statisticsHourlyFunnelStatsService.getById(id));
    }

    /**
     * 新增每小时漏斗分析统计
     */
    @PreAuthorize("@ss.hasPermi('statistics:funnel:add')")
    @Log(title = "每小时漏斗分析统计", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody StatisticsHourlyFunnelStats statisticsHourlyFunnelStats)
    {
        return toAjax(statisticsHourlyFunnelStatsService.insert(statisticsHourlyFunnelStats));
    }

    /**
     * 修改每小时漏斗分析统计
     */
    @PreAuthorize("@ss.hasPermi('statistics:funnel:edit')")
    @Log(title = "每小时漏斗分析统计", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody StatisticsHourlyFunnelStats statisticsHourlyFunnelStats)
    {
        return toAjax(statisticsHourlyFunnelStatsService.update(statisticsHourlyFunnelStats));
    }

    /**
     * 删除每小时漏斗分析统计
     */
    @PreAuthorize("@ss.hasPermi('statistics:funnel:remove')")
    @Log(title = "每小时漏斗分析统计", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable List<Long> ids)
    {
        return toAjax(statisticsHourlyFunnelStatsService.deleteByIds(ids));
    }


    /**
     * 查询自定义分析列
     */
    @PreAuthorize("@ss.hasPermi('statistics:funnel:list')")
    @GetMapping("/columns")
    public AjaxResult columns()
    {
        return AjaxResult.success(SummaryColumnUtils.get(StatisticsHourlyFunnelStats.class));
    }

    /**
     * 查询自定义分析数据
     */
    @PreAuthorize("@ss.hasPermi('statistics:funnel:list')")
    @RequestMapping(value = "/summary")
    public TableDataInfo summary(@RequestBody SummaryRequest query)
    {
        startPage();
        List<StatisticsHourlyFunnelStats> summary = statisticsHourlyFunnelStatsService.summary(query);
        return getDataTable(summary);
    }

    /**
     * 导出自定义分析数据
     */
    @PreAuthorize("@ss.hasPermi('statistics:funnel:export')")
    @Log(title = "导出自定义分析数据", businessType = BusinessType.EXPORT)
    @PostMapping("/summaryExport")
    public void summaryExport(HttpServletResponse response, @RequestBody SummaryRequest query)
    {
        PageHelper.startPage(1, Integer.MAX_VALUE);
        if (null == query.getQuery()) {
            query.setQuery(new ArrayList<>());
        }
        List<StatisticsHourlyFunnelStats> list = statisticsHourlyFunnelStatsService.summary(query);
        query.getGroupBy().addAll(query.getColumns());
        ExcelUtil<StatisticsHourlyFunnelStats> util = new ExcelUtil<>(StatisticsHourlyFunnelStats.class);
        util.exportExcel(response, list, "导出数据");
    }



    /**
     * 下载导入模板
     */
    @PreAuthorize("@ss.hasPermi('statistics:funnel:export')")
    @Log(title = "下载导入模板", businessType = BusinessType.EXPORT)
    @PostMapping("/downloadImportModule")
    public void downloadImportModule()
    {
        statisticsHourlyFunnelStatsService.downloadImportModule();
    }

    /**
     * 导入数据
     */
    @PreAuthorize("@ss.hasPermi('statistics:funnel:export')")
    @Log(title = "导入数据", businessType = BusinessType.EXPORT)
    @PostMapping("/importData")
    public void importData(MultipartFile file)
    {

        statisticsHourlyFunnelStatsService.importData(file);
    }


    /**
     * 查询自定义分析数据-汇总
     */
    @PreAuthorize("@ss.hasPermi('statistics:funnel:list')")
    @RequestMapping(value = "/allSummary")
    public AjaxResult allSummary(@RequestBody SummaryRequest query)
    {
        PageHelper.startPage(1, 1);
        return AjaxResult.success(statisticsHourlyFunnelStatsService.allSummary(query));
    }

    /**
     * 获取每小时漏斗分析统计汇总数据
     */
    @PreAuthorize("@ss.hasPermi('statistics:funnel:query')")
    @GetMapping("/getRangeSummary")
    public AjaxResult getRangeSummary(@RequestParam("startTime") Date startTime,
                                      @RequestParam("endTime") Date endTime,
                                      @RequestParam(value = "appid", required = false) String appid,
                                      @RequestParam(value = "tfid", required = false) String tfid,
                                      @RequestParam(value = "delivererName", required = false) String delivererName) {

        // 处理按投手筛选的逻辑
        AjaxResult delivererResult = controllerPermissionUtils.handleDelivererQuery(
            delivererName, tfid, startTime, endTime,
            params -> statisticsHourlyFunnelStatsService.getSummaryByTfids(params.tfids, params.startTime, params.endTime),
            new StatisticsHourlyFunnelStats()
        );
        if (delivererResult != null) {
            return delivererResult;
        }

        // 处理默认查询
        AjaxResult defaultResult = controllerPermissionUtils.handleDefaultQuery(
            appid, tfid, startTime, endTime,
            params -> statisticsHourlyFunnelStatsService.getSummaryByTfids(params.tfids, params.startTime, params.endTime),
            params -> statisticsHourlyFunnelStatsService.getSummaryByCreator(params.creatorName, params.startTime, params.endTime),
            params -> statisticsHourlyFunnelStatsService.getRangeSummary(params.startTime, params.endTime, params.appid, null),
            new StatisticsHourlyFunnelStats()
        );
        if (defaultResult != null) {
            return defaultResult;
        }

        // 处理其他情况
        return controllerPermissionUtils.handleOtherQuery(
            appid, tfid, startTime, endTime,
            params -> statisticsHourlyFunnelStatsService.getRangeSummary(params.startTime, params.endTime, params.appid, params.tfid)
        );
    }

    /**
     * 全量统计最近N天的漏斗分析数据
     */
    @PreAuthorize("@ss.hasPermi('statistics:funnel:edit')")
    @Log(title = "全量统计漏斗分析数据", businessType = BusinessType.UPDATE)
    @PostMapping("/batchUpdate")
    public AjaxResult batchUpdateRecentStats(@RequestBody BatchUpdateRequest request)
    {
        try {
            Integer days = request.getDays();

            // 默认统计最近7天
            if (days == null || days <= 0) {
                days = 7;
            }

            // 限制最多统计365天
            if (days > 365) {
                days = 365;
            }

            String result = hourlyFunnelStatsService.batchUpdateRecentFunnelStats(days);
            return AjaxResult.success(result);
        } catch (Exception e) {
            return AjaxResult.error("全量统计失败: " + e.getMessage());
        }
    }

    /**
     * 全量统计指定时间范围的漏斗分析数据
     */
    @PreAuthorize("@ss.hasPermi('statistics:funnel:edit')")
    @Log(title = "全量统计漏斗分析数据", businessType = BusinessType.UPDATE)
    @PostMapping("/batchUpdateRange")
    public AjaxResult batchUpdateRangeStats(@RequestBody BatchUpdateRequest request)
    {
        try {
            if (!request.isValidDateRange()) {
                return AjaxResult.error("日期范围参数无效");
            }

            String result = hourlyFunnelStatsService.batchUpdateFunnelStats(request.getStartDate(), request.getEndDate());
            return AjaxResult.success(result);
        } catch (Exception e) {
            return AjaxResult.error("全量统计失败: " + e.getMessage());
        }
    }

}
