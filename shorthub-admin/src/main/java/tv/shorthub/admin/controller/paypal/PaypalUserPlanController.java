package tv.shorthub.admin.controller.paypal;

import com.github.pagehelper.PageHelper;
import tv.shorthub.common.core.domain.SummaryRequest;
import tv.shorthub.common.utils.SummaryColumnUtils;
import org.springframework.web.multipart.MultipartFile;
import java.util.ArrayList;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import tv.shorthub.common.annotation.Log;
import tv.shorthub.common.core.controller.BaseController;
import tv.shorthub.common.core.domain.AjaxResult;
import tv.shorthub.common.enums.BusinessType;
import tv.shorthub.system.domain.PaypalUserPlan;
import tv.shorthub.system.service.IPaypalUserPlanService;
import tv.shorthub.common.utils.poi.ExcelUtil;
import tv.shorthub.common.core.page.TableDataInfo;

/**
 * paypal用户扣费计划Controller
 *
 * <AUTHOR>
 * @date 2025-06-27
 */
@RestController
@RequestMapping("/app/paypal/user/plan")
public class PaypalUserPlanController extends BaseController
{
    @Autowired
    private IPaypalUserPlanService paypalUserPlanService;

    /**
     * 查询paypal用户扣费计划列表
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:user/plan:list')")
    @GetMapping("/list")
    public TableDataInfo list(PaypalUserPlan paypalUserPlan)
    {
        startPage();
        List<PaypalUserPlan> list = paypalUserPlanService.selectList(paypalUserPlan);
        return getDataTable(list);
    }

    /**
     * 获取paypal用户扣费计划数据汇总
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:user/plan:query')")
    @GetMapping(value = "/getSummary")
    public AjaxResult getSummary(PaypalUserPlan paypalUserPlan)
    {
        return AjaxResult.success(paypalUserPlanService.getSummary(paypalUserPlan));
    }

    /**
     * 导出paypal用户扣费计划列表
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:user/plan:export')")
    @Log(title = "paypal用户扣费计划", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PaypalUserPlan paypalUserPlan)
    {
        List<PaypalUserPlan> list = paypalUserPlanService.selectList(paypalUserPlan);
        ExcelUtil<PaypalUserPlan> util = new ExcelUtil<>(PaypalUserPlan.class);
        util.exportExcel(response, list, "paypal用户扣费计划数据");
    }

    /**
     * 获取paypal用户扣费计划详细信息
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:user/plan:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(paypalUserPlanService.getById(id));
    }

    /**
     * 新增paypal用户扣费计划
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:user/plan:add')")
    @Log(title = "paypal用户扣费计划", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PaypalUserPlan paypalUserPlan)
    {
        return toAjax(paypalUserPlanService.insert(paypalUserPlan));
    }

    /**
     * 修改paypal用户扣费计划
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:user/plan:edit')")
    @Log(title = "paypal用户扣费计划", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PaypalUserPlan paypalUserPlan)
    {
        return toAjax(paypalUserPlanService.update(paypalUserPlan));
    }

    /**
     * 删除paypal用户扣费计划
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:user/plan:remove')")
    @Log(title = "paypal用户扣费计划", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable List<Long> ids)
    {
        return toAjax(paypalUserPlanService.deleteByIds(ids));
    }


    /**
     * 查询自定义分析列
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:user/plan:list')")
    @GetMapping("/columns")
    public AjaxResult columns()
    {
        return AjaxResult.success(SummaryColumnUtils.get(PaypalUserPlan.class));
    }

    /**
     * 查询自定义分析数据
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:user/plan:list')")
    @RequestMapping(value = "/summary")
    public TableDataInfo summary(@RequestBody SummaryRequest query)
    {
        startPage();
        List<PaypalUserPlan> summary = paypalUserPlanService.summary(query);
        return getDataTable(summary);
    }

    /**
     * 导出自定义分析数据
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:user/plan:export')")
    @Log(title = "导出自定义分析数据", businessType = BusinessType.EXPORT)
    @PostMapping("/summaryExport")
    public void summaryExport(HttpServletResponse response, @RequestBody SummaryRequest query)
    {
        PageHelper.startPage(1, Integer.MAX_VALUE);
        if (null == query.getQuery()) {
            query.setQuery(new ArrayList<>());
        }
        List<PaypalUserPlan> list = paypalUserPlanService.summary(query);
        query.getGroupBy().addAll(query.getColumns());
        ExcelUtil<PaypalUserPlan> util = new ExcelUtil<>(PaypalUserPlan.class);
        util.exportExcel(response, list, "导出数据");
    }



    /**
     * 下载导入模板
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:user/plan:export')")
    @Log(title = "下载导入模板", businessType = BusinessType.EXPORT)
    @PostMapping("/downloadImportModule")
    public void downloadImportModule()
    {
        paypalUserPlanService.downloadImportModule();
    }

    /**
     * 导入数据
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:user/plan:export')")
    @Log(title = "导入数据", businessType = BusinessType.EXPORT)
    @PostMapping("/importData")
    public void importData(MultipartFile file)
    {

        paypalUserPlanService.importData(file);
    }


    /**
     * 查询自定义分析数据-汇总
     */
    @PreAuthorize("@ss.hasPermi('app/paypal:user/plan:list')")
    @RequestMapping(value = "/allSummary")
    public AjaxResult allSummary(@RequestBody SummaryRequest query)
    {
        PageHelper.startPage(1, 1);
        return AjaxResult.success(paypalUserPlanService.allSummary(query));
    }
}
