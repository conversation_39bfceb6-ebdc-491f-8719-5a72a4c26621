package tv.shorthub.admin.controller.system;

import com.github.pagehelper.PageHelper;
import tv.shorthub.common.core.domain.SummaryRequest;
import tv.shorthub.common.utils.SummaryColumnUtils;
import org.springframework.web.multipart.MultipartFile;
import java.util.ArrayList;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import tv.shorthub.common.annotation.Log;
import tv.shorthub.common.core.controller.BaseController;
import tv.shorthub.common.core.domain.AjaxResult;
import tv.shorthub.common.enums.BusinessType;
import tv.shorthub.system.domain.SysUserApp;
import tv.shorthub.system.service.ISysUserAppService;
import tv.shorthub.common.utils.poi.ExcelUtil;
import tv.shorthub.common.core.page.TableDataInfo;

/**
 * 系统用户app授权Controller
 *
 * <AUTHOR>
 * @date 2025-07-21
 */
@RestController
@RequestMapping("/system/userapp")
public class SysUserAppController extends BaseController
{
    @Autowired
    private ISysUserAppService sysUserAppService;

    /**
     * 查询系统用户app授权列表
     */
    @PreAuthorize("@ss.hasPermi('system:userapp:list')")
    @GetMapping("/list")
    public TableDataInfo list(SysUserApp sysUserApp)
    {
        startPage();
        List<SysUserApp> list = sysUserAppService.selectList(sysUserApp);
        return getDataTable(list);
    }

    /**
     * 获取系统用户app授权数据汇总
     */
    @PreAuthorize("@ss.hasPermi('system:userapp:query')")
    @GetMapping(value = "/getSummary")
    public AjaxResult getSummary(SysUserApp sysUserApp)
    {
        return AjaxResult.success(sysUserAppService.getSummary(sysUserApp));
    }

    /**
     * 导出系统用户app授权列表
     */
    @PreAuthorize("@ss.hasPermi('system:userapp:export')")
    @Log(title = "系统用户app授权", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysUserApp sysUserApp)
    {
        List<SysUserApp> list = sysUserAppService.selectList(sysUserApp);
        ExcelUtil<SysUserApp> util = new ExcelUtil<>(SysUserApp.class);
        util.exportExcel(response, list, "系统用户app授权数据");
    }

    /**
     * 获取系统用户app授权详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:userapp:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(sysUserAppService.getById(id));
    }

    /**
     * 新增系统用户app授权
     */
    @PreAuthorize("@ss.hasPermi('system:userapp:add')")
    @Log(title = "系统用户app授权", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SysUserApp sysUserApp)
    {
        return toAjax(sysUserAppService.insert(sysUserApp));
    }

    /**
     * 修改系统用户app授权
     */
    @PreAuthorize("@ss.hasPermi('system:userapp:edit')")
    @Log(title = "系统用户app授权", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SysUserApp sysUserApp)
    {
        return toAjax(sysUserAppService.update(sysUserApp));
    }

    /**
     * 删除系统用户app授权
     */
    @PreAuthorize("@ss.hasPermi('system:userapp:remove')")
    @Log(title = "系统用户app授权", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable List<Long> ids)
    {
        return toAjax(sysUserAppService.deleteByIds(ids));
    }


    /**
     * 查询自定义分析列
     */
    @PreAuthorize("@ss.hasPermi('system:userapp:list')")
    @GetMapping("/columns")
    public AjaxResult columns()
    {
        return AjaxResult.success(SummaryColumnUtils.get(SysUserApp.class));
    }

    /**
     * 查询自定义分析数据
     */
    @PreAuthorize("@ss.hasPermi('system:userapp:list')")
    @RequestMapping(value = "/summary")
    public TableDataInfo summary(@RequestBody SummaryRequest query)
    {
        startPage();
        List<SysUserApp> summary = sysUserAppService.summary(query);
        return getDataTable(summary);
    }

    /**
     * 导出自定义分析数据
     */
    @PreAuthorize("@ss.hasPermi('system:userapp:export')")
    @Log(title = "导出自定义分析数据", businessType = BusinessType.EXPORT)
    @PostMapping("/summaryExport")
    public void summaryExport(HttpServletResponse response, @RequestBody SummaryRequest query)
    {
        PageHelper.startPage(1, Integer.MAX_VALUE);
        if (null == query.getQuery()) {
            query.setQuery(new ArrayList<>());
        }
        List<SysUserApp> list = sysUserAppService.summary(query);
        query.getGroupBy().addAll(query.getColumns());
        ExcelUtil<SysUserApp> util = new ExcelUtil<>(SysUserApp.class);
        util.exportExcel(response, list, "导出数据");
    }



    /**
     * 下载导入模板
     */
    @PreAuthorize("@ss.hasPermi('system:userapp:export')")
    @Log(title = "下载导入模板", businessType = BusinessType.EXPORT)
    @PostMapping("/downloadImportModule")
    public void downloadImportModule()
    {
        sysUserAppService.downloadImportModule();
    }

    /**
     * 导入数据
     */
    @PreAuthorize("@ss.hasPermi('system:userapp:export')")
    @Log(title = "导入数据", businessType = BusinessType.EXPORT)
    @PostMapping("/importData")
    public void importData(MultipartFile file)
    {

        sysUserAppService.importData(file);
    }


    /**
     * 查询自定义分析数据-汇总
     */
    @PreAuthorize("@ss.hasPermi('system:userapp:list')")
    @RequestMapping(value = "/allSummary")
    public AjaxResult allSummary(@RequestBody SummaryRequest query)
    {
        PageHelper.startPage(1, 1);
        return AjaxResult.success(sysUserAppService.allSummary(query));
    }
}
