package tv.shorthub.admin.controller.statistics;

import com.github.pagehelper.PageHelper;
import tv.shorthub.common.core.domain.SummaryRequest;
import tv.shorthub.common.utils.SummaryColumnUtils;
import org.springframework.web.multipart.MultipartFile;
import java.util.ArrayList;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import tv.shorthub.common.annotation.Log;
import tv.shorthub.common.core.controller.BaseController;
import tv.shorthub.common.core.domain.AjaxResult;
import tv.shorthub.common.enums.BusinessType;
import tv.shorthub.system.domain.StatisticsHourlySubscriptionStats;
import tv.shorthub.system.dto.SubscriptionSummaryDto;
import tv.shorthub.system.service.IStatisticsHourlySubscriptionStatsService;
import tv.shorthub.statistics.service.IHourlySubscriptionStatsService;
import tv.shorthub.common.utils.poi.ExcelUtil;
import tv.shorthub.common.core.page.TableDataInfo;
import tv.shorthub.common.utils.DateUtils;
import tv.shorthub.statistics.dto.TimeRangeRequest;
import tv.shorthub.statistics.dto.BatchUpdateRequest;

import java.util.Date;
import java.util.Map;
import org.springframework.web.bind.annotation.RequestParam;
import lombok.extern.slf4j.Slf4j;
import tv.shorthub.common.utils.SecurityUtils;
import java.util.HashMap;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.springframework.util.CollectionUtils;
import java.util.stream.Collectors;
import tv.shorthub.admin.utils.StatisticsControllerPermissionUtils;
import org.apache.commons.lang3.StringUtils;
import java.math.BigDecimal;
import org.springframework.beans.BeanUtils;

/**
 * 每小时订单与订阅统计Controller
 *
 * <AUTHOR>
 * @date 2025-06-29
 */
@Slf4j
@RestController
@RequestMapping("/statistics/subscription")
public class StatisticsHourlySubscriptionStatsController extends BaseController
{
    @Autowired
    private IStatisticsHourlySubscriptionStatsService statisticsHourlySubscriptionStatsService;

    @Autowired
    private IHourlySubscriptionStatsService hourlySubscriptionStatsService;

    @Autowired
    private StatisticsControllerPermissionUtils controllerPermissionUtils;

    /**
     * 查询每小时订单与订阅统计列表
     */
    @PreAuthorize("@ss.hasPermi('statistics:subscription:list')")
    @GetMapping("/list")
    public TableDataInfo list(
            StatisticsHourlySubscriptionStats statisticsHourlySubscriptionStats,
            @RequestParam(required = false) String startTime,
            @RequestParam(required = false) String endTime)
    {
        log.info("[订阅统计] 查询参数 - startTime: {}, endTime: {}, 查询条件: {}", startTime, endTime, statisticsHourlySubscriptionStats);
        startPage();
        
        // 如果提供了时间范围参数，使用时间范围查询
        if (startTime != null && endTime != null) {
            try {
                Date startDateTime = DateUtils.parseDate(startTime, "yyyy-MM-dd HH:mm:ss");
                Date endDateTime = DateUtils.parseDate(endTime, "yyyy-MM-dd HH:mm:ss");
                List<StatisticsHourlySubscriptionStats> list = statisticsHourlySubscriptionStatsService.selectListByTimeRange(
                    statisticsHourlySubscriptionStats, startDateTime, endDateTime
                );
                return getDataTable(list);
            } catch (Exception e) {
                log.error("[订阅统计] 时间格式解析错误: {}", e.getMessage());
                return getDataTable(new ArrayList<>());
            }
        }
        
        // 否则使用原有的查询方法
        List<StatisticsHourlySubscriptionStats> list = statisticsHourlySubscriptionStatsService.selectList(statisticsHourlySubscriptionStats);
        return getDataTable(list);
    }



    /**
     * 导出每小时订单与订阅统计列表
     */
    @PreAuthorize("@ss.hasPermi('statistics:subscription:export')")
    @Log(title = "每小时订单与订阅统计", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, StatisticsHourlySubscriptionStats statisticsHourlySubscriptionStats)
    {
        List<StatisticsHourlySubscriptionStats> list = statisticsHourlySubscriptionStatsService.selectList(statisticsHourlySubscriptionStats);
        ExcelUtil<StatisticsHourlySubscriptionStats> util = new ExcelUtil<>(StatisticsHourlySubscriptionStats.class);
        util.exportExcel(response, list, "每小时订单与订阅统计数据");
    }

    /**
     * 获取每小时订单与订阅统计详细信息
     */
    @PreAuthorize("@ss.hasPermi('statistics:subscription:query')")
    @GetMapping(value = "/info/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(statisticsHourlySubscriptionStatsService.getById(id));
    }

    /**
     * 新增每小时订单与订阅统计
     */
    @PreAuthorize("@ss.hasPermi('statistics:subscription:add')")
    @Log(title = "每小时订单与订阅统计", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody StatisticsHourlySubscriptionStats statisticsHourlySubscriptionStats)
    {
        return toAjax(statisticsHourlySubscriptionStatsService.insert(statisticsHourlySubscriptionStats));
    }

    /**
     * 修改每小时订单与订阅统计
     */
    @PreAuthorize("@ss.hasPermi('statistics:subscription:edit')")
    @Log(title = "每小时订单与订阅统计", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody StatisticsHourlySubscriptionStats statisticsHourlySubscriptionStats)
    {
        return toAjax(statisticsHourlySubscriptionStatsService.update(statisticsHourlySubscriptionStats));
    }

    /**
     * 删除每小时订单与订阅统计
     */
    @PreAuthorize("@ss.hasPermi('statistics:subscription:remove')")
    @Log(title = "每小时订单与订阅统计", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable List<Long> ids)
    {
        return toAjax(statisticsHourlySubscriptionStatsService.deleteByIds(ids));
    }


    /**
     * 查询自定义分析列
     */
    @PreAuthorize("@ss.hasPermi('statistics:subscription:list')")
    @GetMapping("/columns")
    public AjaxResult columns()
    {
        return AjaxResult.success(SummaryColumnUtils.get(StatisticsHourlySubscriptionStats.class));
    }

    /**
     * 查询自定义分析数据
     */
    @PreAuthorize("@ss.hasPermi('statistics:subscription:list')")
    @RequestMapping(value = "/summary")
    public TableDataInfo summary(@RequestBody SummaryRequest query)
    {
        startPage();
        List<StatisticsHourlySubscriptionStats> summary = statisticsHourlySubscriptionStatsService.summary(query);
        return getDataTable(summary);
    }

    /**
     * 导出自定义分析数据
     */
    @PreAuthorize("@ss.hasPermi('statistics:subscription:export')")
    @Log(title = "导出自定义分析数据", businessType = BusinessType.EXPORT)
    @PostMapping("/summaryExport")
    public void summaryExport(HttpServletResponse response, @RequestBody SummaryRequest query)
    {
        PageHelper.startPage(1, Integer.MAX_VALUE);
        if (null == query.getQuery()) {
            query.setQuery(new ArrayList<>());
        }
        List<StatisticsHourlySubscriptionStats> list = statisticsHourlySubscriptionStatsService.summary(query);
        query.getGroupBy().addAll(query.getColumns());
        ExcelUtil<StatisticsHourlySubscriptionStats> util = new ExcelUtil<>(StatisticsHourlySubscriptionStats.class);
        util.exportExcel(response, list, "导出数据");
    }



    /**
     * 下载导入模板
     */
    @PreAuthorize("@ss.hasPermi('statistics:subscription:export')")
    @Log(title = "下载导入模板", businessType = BusinessType.EXPORT)
    @PostMapping("/downloadImportModule")
    public void downloadImportModule()
    {
        statisticsHourlySubscriptionStatsService.downloadImportModule();
    }

    /**
     * 导入数据
     */
    @PreAuthorize("@ss.hasPermi('statistics:subscription:export')")
    @Log(title = "导入数据", businessType = BusinessType.EXPORT)
    @PostMapping("/importData")
    public void importData(MultipartFile file)
    {

        statisticsHourlySubscriptionStatsService.importData(file);
    }


    /**
     * 查询自定义分析数据-汇总
     */
    @PreAuthorize("@ss.hasPermi('statistics:subscription:list')")
    @RequestMapping(value = "/allSummary")
    public AjaxResult allSummary(@RequestBody SummaryRequest query)
    {
        PageHelper.startPage(1, 1);
        return AjaxResult.success(statisticsHourlySubscriptionStatsService.allSummary(query));
    }

    /**
     * 获取每小时订阅统计汇总数据（支持时间范围查询）
     */
    @PreAuthorize("@ss.hasPermi('statistics:subscription:query')")
    @GetMapping("/getRangeSummary")
    public AjaxResult getRangeSummary(@RequestParam(value = "startTime") Date startTime,
                                      @RequestParam(value = "endTime") Date endTime,
                                      @RequestParam(value = "appid", required = false) String appid,
                                      @RequestParam(value = "tfid", required = false) String tfid,
                                      @RequestParam(value = "delivererName", required = false) String delivererName) {
        log.info("[订阅统计] 区间汇总查询参数 - startTime: {}, endTime: {}, appid: {}, tfid: {}, delivererName: {}", startTime, endTime, appid, tfid, delivererName);
        
        // 创建空结果供应商
        SubscriptionSummaryDto emptyDto = new SubscriptionSummaryDto();
        emptyDto.setUpcomingSevenDayAmount(BigDecimal.ZERO);
        emptyDto.setUpcomingThirtyDayAmount(BigDecimal.ZERO);
        
        // 处理按投手筛选的逻辑
        AjaxResult delivererResult = controllerPermissionUtils.handleDelivererQuery(
            delivererName, tfid, startTime, endTime,
            params -> {
                // 获取基本统计数据并转换为 SubscriptionSummaryDto
                StatisticsHourlySubscriptionStats basicSummary = statisticsHourlySubscriptionStatsService.getSummaryByTfids(params.tfids, params.startTime, params.endTime);
                SubscriptionSummaryDto summary = new SubscriptionSummaryDto();
                if (basicSummary != null) {
                    BeanUtils.copyProperties(basicSummary, summary);
                }
                // 设置待扣款金额（主账号/管理员可以看到）
                summary.setUpcomingSevenDayAmount(BigDecimal.ZERO);
                summary.setUpcomingThirtyDayAmount(BigDecimal.ZERO);
                return summary;
            },
            emptyDto
        );
        if (delivererResult != null) {
            return delivererResult;
        }
        
        // 处理默认查询
        AjaxResult defaultResult = controllerPermissionUtils.handleDefaultQuery(
            appid, tfid, startTime, endTime,
            params -> {
                // 投手查询：获取基本统计数据并转换为 SubscriptionSummaryDto
                StatisticsHourlySubscriptionStats basicSummary = statisticsHourlySubscriptionStatsService.getSummaryByTfids(params.tfids, params.startTime, params.endTime);
                SubscriptionSummaryDto summary = new SubscriptionSummaryDto();
                if (basicSummary != null) {
                    BeanUtils.copyProperties(basicSummary, summary);
                }
                // 投手不显示待扣款金额
                summary.setUpcomingSevenDayAmount(BigDecimal.ZERO);
                summary.setUpcomingThirtyDayAmount(BigDecimal.ZERO);
                return summary;
            },
            params -> statisticsHourlySubscriptionStatsService.getRangeSummaryByCreator(params.creatorName, params.startTime, params.endTime),
            params -> statisticsHourlySubscriptionStatsService.getRangeSummary(params.startTime, params.endTime, params.appid, null),
            emptyDto
        );
        if (defaultResult != null) {
            return defaultResult;
        }
        
        // 处理其他情况
        return controllerPermissionUtils.handleOtherQuery(
            appid, tfid, startTime, endTime,
            params -> statisticsHourlySubscriptionStatsService.getRangeSummary(params.startTime, params.endTime, params.appid, params.tfid)
        );
    }

    /**
     * 全量统计最近N天的订阅数据
     */
    @PreAuthorize("@ss.hasPermi('statistics:subscription:edit')")
    @Log(title = "全量统计订阅数据", businessType = BusinessType.UPDATE)
    @PostMapping("/batchUpdate")
    public AjaxResult batchUpdateRecentStats(@RequestBody BatchUpdateRequest request)
    {
        try {
            Integer days = request.getDays();
            
            // 默认统计最近7天
            if (days == null || days <= 0) {
                days = 7;
            }

            // 限制最多统计365天
            if (days > 365) {
                days = 365;
            }

            String result = hourlySubscriptionStatsService.batchUpdateRecentSubscriptionStats(days);
            return AjaxResult.success(result);
        } catch (Exception e) {
            return AjaxResult.error("全量统计失败: " + e.getMessage());
        }
    }

    /**
     * 全量统计指定时间范围的订阅数据
     */
    @PreAuthorize("@ss.hasPermi('statistics:subscription:edit')")
    @Log(title = "全量统计订阅数据", businessType = BusinessType.UPDATE)
    @PostMapping("/batchUpdateRange")
    public AjaxResult batchUpdateRangeStats(@RequestBody BatchUpdateRequest request)
    {
        try {
            if (!request.isValidDateRange()) {
                return AjaxResult.error("日期范围参数无效");
            }

            String result = hourlySubscriptionStatsService.batchUpdateSubscriptionStats(request.getStartDate(), request.getEndDate());
            return AjaxResult.success(result);
        } catch (Exception e) {
            return AjaxResult.error("全量统计失败: " + e.getMessage());
        }
    }

}
