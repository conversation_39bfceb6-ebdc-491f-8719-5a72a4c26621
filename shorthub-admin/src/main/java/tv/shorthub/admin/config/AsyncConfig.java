package tv.shorthub.admin.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.task.TaskExecutor;
import org.springframework.scheduling.annotation.AsyncConfigurer;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ConcurrentTaskExecutor;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Configuration
@EnableAsync
public class AsyncConfig implements AsyncConfigurer {

    @Bean
    public TaskExecutor taskExecutor() {
        ExecutorService executorService = Executors.newVirtualThreadPerTaskExecutor();
        System.out.println("Configured async task executor to use virtual threads.");
        return new ConcurrentTaskExecutor(executorService);
    }


    @Override
    public TaskExecutor getAsyncExecutor() {
        return taskExecutor();
    }
}
