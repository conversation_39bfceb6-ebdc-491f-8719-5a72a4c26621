//package tv.shorthub.admin.config;
//
//import com.xxl.job.core.executor.impl.XxlJobSpringExecutor;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//
//@Configuration
//@ConditionalOnProperty("xxl.job.admin.addresses")
//@Slf4j
//public class XxlConfiguration {
//
//    @Value("${xxl.job.admin.addresses}")
//    private String addresses;
//
//    @Value("${xxl.job.accessToken}")
//    private String accessToken;
//
//    @Value("${xxl.job.executor.appname}")
//    private String appname;
//
//    @Value("${xxl.job.executor.ip}")
//    private String ip;
//
//    @Value("${xxl.job.executor.port}")
//    private Integer port;
//
//    @Value("${xxl.job.executor.logpath}")
//    private String logpath;
//
//    @Value("${xxl.job.executor.logretentiondays}")
//    private Integer logretentiondays;
//
//    @Bean
//    public XxlJobSpringExecutor xxlJobExecutor() {
//        log.info(">>>>>>>>>>> xxl-job config init.");
//        XxlJobSpringExecutor xxlJobSpringExecutor = new XxlJobSpringExecutor();
//        xxlJobSpringExecutor.setAdminAddresses(addresses);
//        xxlJobSpringExecutor.setAppname(appname);
//        xxlJobSpringExecutor.setIp(ip);
//        xxlJobSpringExecutor.setPort(port);
//        xxlJobSpringExecutor.setAccessToken(accessToken);
//        xxlJobSpringExecutor.setLogPath(logpath);
//        xxlJobSpringExecutor.setLogRetentionDays(logretentiondays);
//        return xxlJobSpringExecutor;
//    }
//}
