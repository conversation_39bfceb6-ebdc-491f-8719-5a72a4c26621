package tv.shorthub.admin.config;

import org.springframework.context.annotation.Configuration;

@Configuration
public class MyBatisPlusConfig {

//    /**
//     * 配置分页插件
//     */
//    @Bean
//    public MybatisPlusInterceptor mybatisPlusInterceptor() {
//        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
//        // 添加分页插件
//        interceptor.addInnerInterceptor(new PaginationInnerInterceptor(DbType.MYSQL));
//        interceptor.addInnerInterceptor(new BatchInsertOrUpdateInterceptor());
//        return interceptor;
//    }


//    @Bean
//    public GlobalConfig globalConfig() {
//        GlobalConfig globalConfig = new GlobalConfig();
//        globalConfig.setDbConfig(new GlobalConfig.DbConfig().setEnableSqlRunner(true));
//        return globalConfig;
//    }
}
