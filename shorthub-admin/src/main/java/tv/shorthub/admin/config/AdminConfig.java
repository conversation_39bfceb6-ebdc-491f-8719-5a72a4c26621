package tv.shorthub.admin.config;


import tv.shorthub.common.utils.spring.SpringUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class AdminConfig {


    @Bean
    public RedissonClient redissonClient() {
        Config config = new Config();
        String address = "redis://" + SpringUtils.getRequiredProperty("spring.data.redis.host") + ":" + SpringUtils.getRequiredProperty("spring.data.redis.port");
        config.useSingleServer()
                .setAddress(address)
                .setDatabase(Integer.valueOf(SpringUtils.getRequiredProperty("spring.data.redis.database")))
                .setTimeout(30000)
        //.setPassword(SpringUtils.getRequiredProperty("spring.redis.password"))
        ;
        String password = SpringUtils.getRequiredProperty("spring.data.redis.password");
        if(StringUtils.isNotEmpty(password)) {
            config.useSingleServer()
                    .setPassword(password)
            ;
        }
        return Redisson.create(config);
    }

}
