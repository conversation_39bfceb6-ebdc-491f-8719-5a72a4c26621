package tv.shorthub.admin.cache;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tv.shorthub.common.annotation.CacheRefresh;
import tv.shorthub.common.core.cache.CacheKeyUtils;
import tv.shorthub.common.core.cache.refresh.RefreshService;
import tv.shorthub.common.core.redis.RedisCache;
import tv.shorthub.system.domain.AppPromotion;
import tv.shorthub.system.service.IAppPromotionService;

import java.util.List;

@CacheRefresh(key = "app_promotion", seconds = 15)
@Component
@Slf4j
public class RefreshAppPromotion implements RefreshService {

    @Autowired
    IAppPromotionService appPromotionService;

    @Autowired
    RedisCache redisCache;

    @Override
    public void start() {

        List<AppPromotion> appPromotions = appPromotionService.getMapper().selectList(null);
        for (AppPromotion appPromotion : appPromotions) {
            redisCache.setCacheObject(CacheKeyUtils.getAppPromotion(appPromotion.getTfid()), appPromotion);
        }

        log.info("刷新推广链接缓存成功，size：{}", appPromotions.size());
    }
}
