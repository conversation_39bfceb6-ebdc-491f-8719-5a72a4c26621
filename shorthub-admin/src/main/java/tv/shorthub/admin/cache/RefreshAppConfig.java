package tv.shorthub.admin.cache;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tv.shorthub.common.annotation.CacheRefresh;
import tv.shorthub.common.core.cache.CacheKeyUtils;
import tv.shorthub.common.core.cache.refresh.RefreshService;
import tv.shorthub.common.core.redis.RedisCache;
import tv.shorthub.system.domain.AppConfig;
import tv.shorthub.system.domain.AppPromotion;
import tv.shorthub.system.service.IAppConfigService;
import tv.shorthub.system.service.IAppPromotionService;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@CacheRefresh(key = "app_config", seconds = 15)
@Component
@Slf4j
public class RefreshAppConfig implements RefreshService {

    @Autowired
    IAppConfigService appConfigService;

    @Autowired
    RedisCache redisCache;

    @Override
    public void start() {

        List<AppConfig> list = appConfigService.getMapper().selectList(null);

        Map<String, AppConfig> dataMap = list.stream().collect(Collectors.toMap(AppConfig::getAppid, appConfig -> appConfig));

        redisCache.setCacheMap(CacheKeyUtils.APP_CONFIG_MAP, dataMap);

        log.info("刷新APP配置缓存成功，size：{}", list.size());
    }
}
