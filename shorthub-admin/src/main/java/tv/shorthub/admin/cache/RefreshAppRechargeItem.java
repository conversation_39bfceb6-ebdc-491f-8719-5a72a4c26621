package tv.shorthub.admin.cache;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tv.shorthub.common.annotation.CacheRefresh;
import tv.shorthub.common.core.cache.CacheKeyUtils;
import tv.shorthub.common.core.cache.refresh.RefreshService;
import tv.shorthub.common.core.redis.RedisCache;
import tv.shorthub.common.dto.RechargeItemDTO;
import tv.shorthub.common.utils.StringUtils;
import tv.shorthub.system.domain.*;
import tv.shorthub.system.service.*;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@CacheRefresh(key = "app_recharge_item", seconds = 10)
@Component
@Slf4j
public class RefreshAppRechargeItem implements RefreshService  {

    @Autowired
    IAppRechargeItemService appRechargeItemService;

    @Autowired
    IAppRechargeService appRechargeService;

    @Autowired
    IAppRechargeSubscriptionGiftService appRechargeSubscriptionGiftService;

    @Autowired
    IAppUserLevelRuleService levelRuleService;


    @Autowired
    RedisCache redisCache;

    @Autowired
    IGooglePlaySubscriptionConfigService googlePlaySubscriptionConfigService;

    @Override
    public void start() {
        long time = System.currentTimeMillis();
        QueryWrapper<AppRechargeItem> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("enable", true);
        //过滤二级方案
        queryWrapper.eq("parent_item_id", "");
        List<AppRechargeItem> datas = appRechargeItemService.getMapper().selectList(queryWrapper);
        Map<String, Map<String, List<AppRechargeItem>>> templateUserTypeGroup = datas.stream().collect(
                Collectors.groupingBy(AppRechargeItem::getTemplateId, Collectors.groupingBy(AppRechargeItem::getUserType))
        );
        Object testSubscription = redisCache.getCacheMapValue("test_google_play_subscription_config", "subscriptionId");
        Object productId = redisCache.getCacheMapValue("test_google_play_subscription_config", "productId");
        log.info("testSubscription: productId{}, {}", testSubscription, productId);

        List<AppRecharge> appRecharges = appRechargeService.getMapper().selectList(null);
        Map<String, AppRecharge> rechargeMap = appRecharges.stream().collect(Collectors.toMap(AppRecharge::getTemplateId, v -> v));

        List<GooglePlaySubscriptionConfig> googlePlaySubscriptionConfigs = googlePlaySubscriptionConfigService.selectList(null);
        Map<String, GooglePlaySubscriptionConfig> subscriptionConfigMap = googlePlaySubscriptionConfigs.stream().collect(Collectors.toMap(GooglePlaySubscriptionConfig::getBasePlanId, v -> v));


        for (Map.Entry<String, Map<String, List<AppRechargeItem>>> templateGroup : templateUserTypeGroup.entrySet()) {
            String templateId = templateGroup.getKey();
            AppRecharge appRecharge = rechargeMap.get(templateId);

            for (Map.Entry<String, List<AppRechargeItem>> userTypeGroup : templateGroup.getValue().entrySet()) {
                String userType = userTypeGroup.getKey();
                String key = CacheKeyUtils.getRechargeTemplateItem(templateId, userType);
                List<RechargeItemDTO> list = userTypeGroup.getValue().stream()
                    .map(item -> {
                        RechargeItemDTO value = new RechargeItemDTO();
                        AppRechargeSubscriptionGift subscriptionGift = getSubscriptionGift(item.getItemId());
                        if (null != subscriptionGift) {
                            value.setFirstPrice(subscriptionGift.getGiftPrice());
                        }
                        if (StringUtils.isNotEmpty(item.getMemberLevel())) {
                            AppUserLevelRule levelRule = getLevelRule(item.getMemberLevel());
                            if (null != levelRule && null != levelRule.getRuleParams() && levelRule.getRuleParams().containsKey("number")) {
                                value.setUnlockDramas(levelRule.getRuleParams().getLong("number"));
                            }
                        }
                        BeanUtils.copyProperties(item, value);
                        if (subscriptionConfigMap.containsKey(item.getItemId())) {
                            value.setSubscriptionId(subscriptionConfigMap.get(item.getItemId()).getSubscriptionId());
                            value.setProductId(item.getItemId());
                        } else {
                            value.setProductId(item.getItemId());
                        }
                        value.setLogin(null != appRecharge ? appRecharge.getMustLogin() : false);
                        value.setCompute(null != appRecharge ? appRecharge.getCompute() : false);
                        return value;
                    })
                    .sorted(Comparator.comparing(RechargeItemDTO::getSort))
                    .toList();
                redisCache.setCacheList(key, list);
            }

        }

        log.info("刷新充值模板缓存成功，耗时：{}ms", System.currentTimeMillis() - time);
    }

    public AppUserLevelRule getLevelRule(String level) {
        QueryWrapper<AppUserLevelRule> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("level", level);
        queryWrapper.last("LIMIT 1");
        return levelRuleService.getMapper().selectOne(queryWrapper);
    }


    public AppRechargeSubscriptionGift getSubscriptionGift(String itemId) {
        QueryWrapper<AppRechargeSubscriptionGift> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("item_id", itemId);
        queryWrapper.eq("enable", true);
        return appRechargeSubscriptionGiftService.getMapper().selectOne(queryWrapper);
    }

}
