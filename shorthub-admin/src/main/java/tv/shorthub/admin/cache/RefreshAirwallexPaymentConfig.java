package tv.shorthub.admin.cache;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tv.shorthub.common.annotation.CacheRefresh;
import tv.shorthub.common.core.cache.CacheKeyUtils;
import tv.shorthub.common.core.cache.refresh.RefreshService;
import tv.shorthub.common.core.redis.RedisCache;
import tv.shorthub.system.domain.AirwallexPaymentConfig;
import tv.shorthub.system.domain.AppRecharge;
import tv.shorthub.system.service.IAirwallexPaymentConfigService;
import tv.shorthub.system.service.IAppRechargeService;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@CacheRefresh(key = "airwallex_payment_config", seconds = 10)
@Component
@Slf4j
public class RefreshAirwallexPaymentConfig implements RefreshService  {

    @Autowired
    IAirwallexPaymentConfigService airwallexPaymentConfigService;


    @Autowired
    RedisCache redisCache;

    @Override
    public void start() {
        long time = System.currentTimeMillis();
        QueryWrapper<AirwallexPaymentConfig> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("enable", true);

        List<AirwallexPaymentConfig> airwallexPaymentConfigs = airwallexPaymentConfigService.getMapper().selectList(queryWrapper);
        if (CollectionUtils.isNotEmpty(airwallexPaymentConfigs)) {
            Map<String, AirwallexPaymentConfig> paymentConfigMap = airwallexPaymentConfigs.stream().collect(Collectors.toMap(e -> e.getId().toString(), v -> v));
            redisCache.setCacheMap(CacheKeyUtils.AIRWALLEX_PAYMENT_CONFIG_MAP, paymentConfigMap);
        }
        log.info("刷新空中云汇配置缓存成功:{} ，耗时：{}ms", airwallexPaymentConfigs.size(), System.currentTimeMillis() - time);
    }

}
