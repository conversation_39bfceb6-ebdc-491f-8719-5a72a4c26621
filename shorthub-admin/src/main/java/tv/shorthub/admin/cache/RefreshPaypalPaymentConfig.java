package tv.shorthub.admin.cache;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tv.shorthub.common.annotation.CacheRefresh;
import tv.shorthub.common.core.cache.CacheKeyUtils;
import tv.shorthub.common.core.cache.refresh.RefreshService;
import tv.shorthub.common.core.redis.RedisCache;
import tv.shorthub.system.domain.PaypalPaymentConfig;
import tv.shorthub.system.service.IPaypalPaymentConfigService;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@CacheRefresh(key = "paypal_payment_config", seconds = 15)
@Component
@Slf4j
public class RefreshPaypalPaymentConfig implements RefreshService {

    @Autowired
    IPaypalPaymentConfigService paypalPaymentConfigService;

    @Autowired
    RedisCache redisCache;

    @Override
    public void start() {

        List<PaypalPaymentConfig> paymentConfigs = paypalPaymentConfigService.getMapper().selectList(null);
        Map<String, PaypalPaymentConfig> paymentConfigMap = paymentConfigs.stream()
                .collect(Collectors.toMap(PaypalPaymentConfig::getClientId, paypalPaymentConfig -> paypalPaymentConfig));

        redisCache.setCacheMap(CacheKeyUtils.MAP_PAYPAL_PAYMENT_CONFIG, paymentConfigMap);

        Map<String, PaypalPaymentConfig> paymentConfigByIdMap = paymentConfigs.stream()
                .collect(Collectors.toMap(v -> v.getId() + "", paypalPaymentConfig -> paypalPaymentConfig));
        redisCache.setCacheMap(CacheKeyUtils.MAP_PAYPAL_PAYMENT_CONFIG_BY_ID, paymentConfigByIdMap);

        log.info("刷新Paypal配置缓存成功，size：{}", paymentConfigs.size());
    }
}
