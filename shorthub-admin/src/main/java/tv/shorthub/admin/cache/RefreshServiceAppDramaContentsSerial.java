package tv.shorthub.admin.cache;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tv.shorthub.common.annotation.CacheRefresh;
import tv.shorthub.common.core.cache.CacheKeyUtils;
import tv.shorthub.common.core.cache.refresh.RefreshService;
import tv.shorthub.common.core.redis.RedisCache;
import tv.shorthub.common.enums.CompressTypeEnum;
import tv.shorthub.system.domain.AppDramaContentsSerial;
import tv.shorthub.system.service.IAppDramaContentsSerialService;

import java.time.Duration;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@CacheRefresh(key = "app_drama_contents_serial", seconds = 10)
@Component
@Slf4j
public class RefreshServiceAppDramaContentsSerial implements RefreshService{

    @Autowired
    IAppDramaContentsSerialService appDramaContentsSerialService;

    @Autowired
    RedisCache redisCache;

    @Override
    public void start() {
        // 查询剧集地址已过期或在2小时内过期的剧集, 或过期时间为null的剧集
        QueryWrapper<AppDramaContentsSerial> queryWrapper = new QueryWrapper<>();
        queryWrapper.isNull("serial_url_expire_time")
                .or()
                .apply("serial_url_expire_time < NOW() + INTERVAL 2 HOUR");
        List<AppDramaContentsSerial> appDramaContentsSerials = appDramaContentsSerialService.getMapper().selectList(queryWrapper);
        log.info("准备更新授权的剧集数: {}", appDramaContentsSerials.size());
        for (AppDramaContentsSerial appDramaContentsSerial : appDramaContentsSerials) {
            appDramaContentsSerialService.updateUrl(appDramaContentsSerial);
        }

        serialRedisCache();
    }

    public void serialRedisCache() {
        List<AppDramaContentsSerial> appDramaContentsSerials = appDramaContentsSerialService.getMapper().selectList(null);
        log.info("总集数: {}", appDramaContentsSerials.size());
        Map<String, List<AppDramaContentsSerial>> contentsSerialGroup = appDramaContentsSerials.stream().collect(Collectors.groupingBy(AppDramaContentsSerial::getContentId));
        for (Map.Entry<String, List<AppDramaContentsSerial>> entry : contentsSerialGroup.entrySet()) {
            List<AppDramaContentsSerial> serials = entry.getValue();

            for (AppDramaContentsSerial serial : serials) {
                if (serial.getSerialUrlExpireTime() == null) {
                    continue;
                }
                String redisKey = CacheKeyUtils.getSerialUrl(serial.getContentId(), serial.getSerialNumber(), CompressTypeEnum.ORIGINAL.getValue());
                redisCache.setCacheObject(redisKey, serial.getSerialUrl());
                Duration between = Duration.between(new Date().toInstant(), serial.getSerialUrlExpireTime().toInstant());
                redisCache.expire(redisKey, between.toSeconds());
            }

            Map<String, AppDramaContentsSerial> serialMap = serials.stream().collect(Collectors.toMap(v -> v.getSerialNumber().toString(), serial -> serial, (v1, v2) -> v1));
            redisCache.setCacheMap(CacheKeyUtils.getContentSerialMap(entry.getKey()), serialMap);
        }
    }

}
