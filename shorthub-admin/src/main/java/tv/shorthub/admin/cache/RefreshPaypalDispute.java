//package tv.shorthub.admin.cache;
//
//import com.alibaba.fastjson2.JSONObject;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections4.CollectionUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//import tv.shorthub.common.annotation.CacheRefresh;
//import tv.shorthub.common.config.AppConfig;
//import tv.shorthub.common.core.cache.refresh.RefreshService;
//import tv.shorthub.paypal.config.PaypalConfigStorageHolder;
//import tv.shorthub.paypal.service.PayPalDisputeService;
//import tv.shorthub.system.domain.PaypalDispute;
//import tv.shorthub.system.domain.PaypalPaymentConfig;
//import tv.shorthub.system.service.IPaypalDisputeService;
//import tv.shorthub.common.utils.DateUtils;
////import tv.shorthub.admin.service.DingTalkService;
//import tv.shorthub.common.utils.StringUtils;
//import tv.shorthub.common.utils.spring.SpringUtils;
//import tv.shorthub.common.core.redis.RedisCache;
//import tv.shorthub.common.core.domain.model.LoginUser;
//import tv.shorthub.common.utils.SecurityUtils;
//import tv.shorthub.system.service.IPaypalPaymentConfigService;
//
//import java.math.BigDecimal;
//import java.util.ArrayList;
//import java.util.Date;
//import java.util.List;
//import java.util.concurrent.TimeUnit;
//
///**
// * PayPal争议数据同步服务
// */
//@CacheRefresh(key = "refresh_paypal_dispute", seconds = 300)
//@Slf4j
//@Component
//public class RefreshPaypalDispute implements RefreshService {
//
//    @Autowired
//    private PayPalDisputeService payPalDisputeService;
//
//    @Autowired
//    private IPaypalDisputeService paypalDisputeService;
//
//    @Autowired
//    private IPaypalPaymentConfigService paypalPaymentConfigService;
//
////    @Autowired
////    private DingTalkService dingTalkService;
//
//    @Autowired
//    private RedisCache redisCache;
//
//    private static final String DISPUTE_RISK_KEY = "paypal:dispute:risk:";
//    private static final String DISPUTE_NOTIFY_KEY = "paypal:dispute:notify:";
//    private static final int HIGH_RISK_THRESHOLD = 3; // 高风险阈值
//    private static final int NOTIFY_EXPIRE_DAYS = 7; // 通知过期天数
//
//    /**
//     * 同步PayPal争议数据
//     * 默认同步最近7天的数据
//     */
//    @Override
//    public void start() {
//        List<PaypalPaymentConfig> paymentConfigs = paypalPaymentConfigService.selectList(null);
//        for (PaypalPaymentConfig paymentConfig : paymentConfigs) {
//
//            try {
//                sync(paymentConfig);
//            } catch (Exception e) {
//                log.error("同步PayPal争议数据失败", e);
//            }
//        }
//
//    }
//
//    private void sync(PaypalPaymentConfig paymentConfig) throws Exception {
//        log.info("开始同步PayPal争议数据 {}", paymentConfig.getClientId());
//
//        PaypalConfigStorageHolder.set(paymentConfig.getClientId());
//
//        // 获取最近7天的争议数据
//        Date endTime = DateUtils.getNowDate();
//        String startTime = DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, DateUtils.addDays(endTime, -7));
//
//        List<JSONObject> disputes = payPalDisputeService.getDisputes(startTime, DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, DateUtils.getNowDate()));
//
//        List<PaypalDispute> paypalDisputes = new ArrayList<>();
//        for (JSONObject dispute : disputes) {
//            try {
//                // 获取争议详情
//                String disputeId = dispute.getString("dispute_id");
//                JSONObject disputeDetail = payPalDisputeService.getDisputeDetail(disputeId);
//
//                // 转换为PaypalDispute对象
//                PaypalDispute paypalDispute = new PaypalDispute();
//                paypalDispute.setDisputeId(disputeId);
//                paypalDispute.setPaymentId(disputeDetail.getString("payment_id"));
//                paypalDispute.setStatus(disputeDetail.getString("status"));
//                paypalDispute.setReason(disputeDetail.getString("reason"));
//                paypalDispute.setDisputeType(disputeDetail.getString("dispute_type"));
//
//                JSONObject amount = disputeDetail.getJSONObject("amount");
//                if (amount != null) {
//                    paypalDispute.setAmount(amount.getBigDecimal("value"));
//                    paypalDispute.setCurrency(amount.getString("currency_code"));
//                }
//
//                paypalDispute.setBuyerId(disputeDetail.getString("buyer_id"));
//                paypalDispute.setSellerId(disputeDetail.getString("seller_id"));
//                paypalDispute.setRawData(disputeDetail);
//
//                // 检查是否是新增争议或状态更新
//                PaypalDispute query = new PaypalDispute();
//                query.setDisputeId(disputeId);
//                PaypalDispute existingDispute = paypalDisputeService.selectList(
//                        query
//                ).stream().findFirst().orElse(null);
//
//                if (existingDispute == null) {
//                    // 新增争议，发送通知
//                    sendNewDisputeNotification(paypalDispute);
//                } else if (!StringUtils.equals(existingDispute.getStatus(), paypalDispute.getStatus())) {
//                    // 状态更新，发送通知
//                    sendStatusUpdateNotification(existingDispute, paypalDispute);
//                }
//
//                paypalDisputes.add(paypalDispute);
//
//            } catch (Exception e) {
//                log.error("处理争议数据失败: disputeId={}", dispute.getString("dispute_id"), e);
//            }
//        }
//
//        if (CollectionUtils.isNotEmpty(paypalDisputes)) {
//            paypalDisputeService.getMapper().batchInsertOrUpdate(paypalDisputes);
//        }
//
//        log.info("同步PayPal争议数据完成，共处理{}条数据", disputes.size());
//    }
//
//    /**
//     * 发送新增争议通知
//     */
//    private void sendNewDisputeNotification(PaypalDispute dispute) {
//        try {
//            String title = "⚠️ 新增PayPal争议告警";
//            String content = String.format(
//                "### ⚠️ 新增PayPal争议告警\n\n" +
//                "#### 基本信息\n" +
//                "- 争议ID：`%s`\n" +
//                "- 支付ID：`%s`\n" +
//                "- 创建时间：%s\n\n" +
//                "#### 争议详情\n" +
//                "- 争议类型：`%s`\n" +
//                "- 争议原因：`%s`\n" +
//                "- 争议状态：`%s`\n" +
//                "- 争议金额：`%s %s`\n\n" +
//                "#### 账户信息\n" +
//                "- 买家ID：`%s`\n" +
//                "- 卖家ID：`%s`\n\n" +
//                "#### 风控评估\n" +
//                "- 风险等级：%s\n" +
//                "- 买家历史争议数：%d\n" +
//                "- 是否高风险用户：%s\n\n" +
//                "#### 处理建议\n" +
//                "1. 请立即查看争议详情\n" +
//                "2. 准备相关证据材料\n" +
//                "3. 评估是否需要自动退款\n" +
//                "4. 记录处理过程\n\n" +
//                "#### 快速操作\n" +
//                "- [查看争议详情](%s)\n" +
//                "- [查看买家历史](%s)\n" +
//                "- [查看订单信息](%s)\n",
//                dispute.getDisputeId(),
//                dispute.getPaymentId(),
//                DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, new Date()),
//                dispute.getDisputeType(),
//                dispute.getReason(),
//                dispute.getStatus(),
//                dispute.getAmount(),
//                dispute.getCurrency(),
//                dispute.getBuyerId(),
//                dispute.getSellerId(),
//                getRiskLevel(dispute),
//                getBuyerDisputeCount(dispute.getBuyerId()),
//                isHighRiskBuyer(dispute.getBuyerId()) ? "是" : "否",
//                getDisputeDetailUrl(dispute.getDisputeId()),
//                getBuyerHistoryUrl(dispute.getBuyerId()),
//                getOrderDetailUrl(dispute.getPaymentId())
//            );
//
////            dingTalkService.sendMarkdown(title, content);
//        } catch (Exception e) {
//            log.error("发送新增争议通知失败: disputeId={}", dispute.getDisputeId(), e);
//        }
//    }
//
//    /**
//     * 发送状态更新通知
//     */
//    private void sendStatusUpdateNotification(PaypalDispute oldDispute, PaypalDispute newDispute) {
//        try {
//            String title = "🔄 PayPal争议状态更新";
//            String content = String.format(
//                "### 🔄 PayPal争议状态更新\n\n" +
//                "#### 基本信息\n" +
//                "- 争议ID：`%s`\n" +
//                "- 支付ID：`%s`\n" +
//                "- 更新时间：%s\n\n" +
//                "#### 状态变更\n" +
//                "- 原状态：`%s`\n" +
//                "- 新状态：`%s`\n" +
//                "- 变更时间：%s\n\n" +
//                "#### 争议详情\n" +
//                "- 争议类型：`%s`\n" +
//                "- 争议原因：`%s`\n" +
//                "- 争议金额：`%s %s`\n\n" +
//                "#### 账户信息\n" +
//                "- 买家ID：`%s`\n" +
//                "- 卖家ID：`%s`\n\n" +
//                "#### 风控评估\n" +
//                "- 风险等级：%s\n" +
//                "- 状态变更影响：%s\n" +
//                "- 是否需要人工介入：%s\n\n" +
//                "#### 处理建议\n" +
//                "1. 评估状态变更影响\n" +
//                "2. 更新处理策略\n" +
//                "3. 记录状态变更原因\n" +
//                "4. 必要时调整风控措施\n\n" +
//                "#### 快速操作\n" +
//                "- [查看争议详情](%s)\n" +
//                "- [查看处理记录](%s)\n" +
//                "- [更新处理策略](%s)\n",
//                newDispute.getDisputeId(),
//                newDispute.getPaymentId(),
//                DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, new Date()),
//                oldDispute.getStatus(),
//                newDispute.getStatus(),
//                DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, new Date()),
//                newDispute.getDisputeType(),
//                newDispute.getReason(),
//                newDispute.getAmount(),
//                newDispute.getCurrency(),
//                newDispute.getBuyerId(),
//                newDispute.getSellerId(),
//                getRiskLevel(newDispute),
//                getStatusChangeImpact(oldDispute.getStatus(), newDispute.getStatus()),
//                needsManualIntervention(newDispute) ? "是" : "否",
//                getDisputeDetailUrl(newDispute.getDisputeId()),
//                getProcessRecordUrl(newDispute.getDisputeId()),
//                getUpdateStrategyUrl(newDispute.getDisputeId())
//            );
//
////            dingTalkService.sendMarkdown(title, content);
//        } catch (Exception e) {
//            log.error("发送状态更新通知失败: disputeId={}", newDispute.getDisputeId(), e);
//        }
//    }
//
//    /**
//     * 获取风险等级
//     */
//    private String getRiskLevel(PaypalDispute dispute) {
//        if (isHighRiskDispute(dispute.getDisputeType(), dispute.getReason())) {
//            return "🔴 高风险";
//        } else if (dispute.getAmount() != null && dispute.getAmount().compareTo(new BigDecimal("1000")) > 0) {
//            return "🟡 中风险";
//        } else {
//            return "🟢 低风险";
//        }
//    }
//
//    /**
//     * 获取买家历史争议数
//     */
//    private int getBuyerDisputeCount(String buyerId) {
//        String buyerRiskKey = DISPUTE_RISK_KEY + "buyer:" + buyerId;
//        Integer count = redisCache.getCacheObject(buyerRiskKey);
//        return count != null ? count : 0;
//    }
//
//    /**
//     * 判断是否为高风险买家
//     */
//    private boolean isHighRiskBuyer(String buyerId) {
//        return getBuyerDisputeCount(buyerId) >= HIGH_RISK_THRESHOLD;
//    }
//
//    /**
//     * 获取状态变更影响
//     */
//    private String getStatusChangeImpact(String oldStatus, String newStatus) {
//        if ("OPEN".equals(oldStatus) && "RESOLVED".equals(newStatus)) {
//            return "争议已解决，需要确认处理结果";
//        } else if ("OPEN".equals(oldStatus) && "CLOSED".equals(newStatus)) {
//            return "争议已关闭，需要确认关闭原因";
//        } else if ("RESOLVED".equals(oldStatus) && "REOPENED".equals(newStatus)) {
//            return "争议重新开启，需要重新评估";
//        }
//        return "状态变更，需要关注";
//    }
//
//    /**
//     * 判断是否需要人工介入
//     */
//    private boolean needsManualIntervention(PaypalDispute dispute) {
//        return "REOPENED".equals(dispute.getStatus()) ||
//               "APPEALED".equals(dispute.getStatus()) ||
//               isHighRiskDispute(dispute.getDisputeType(), dispute.getReason());
//    }
//
//    /**
//     * 获取争议详情URL
//     */
//    private String getDisputeDetailUrl(String disputeId) {
//        return String.format("%s/paypal/dispute/detail/%s", getBaseUrl(), disputeId);
//    }
//
//    /**
//     * 获取买家历史URL
//     */
//    private String getBuyerHistoryUrl(String buyerId) {
//        return String.format("%s/paypal/buyer/history/%s", getBaseUrl(), buyerId);
//    }
//
//    /**
//     * 获取订单详情URL
//     */
//    private String getOrderDetailUrl(String paymentId) {
//        return String.format("%s/paypal/order/detail/%s", getBaseUrl(), paymentId);
//    }
//
//    /**
//     * 获取处理记录URL
//     */
//    private String getProcessRecordUrl(String disputeId) {
//        return String.format("%s/paypal/dispute/process/%s", getBaseUrl(), disputeId);
//    }
//
//    /**
//     * 获取更新策略URL
//     */
//    private String getUpdateStrategyUrl(String disputeId) {
//        return String.format("%s/paypal/dispute/strategy/%s", getBaseUrl(), disputeId);
//    }
//
//    /**
//     * 获取基础URL
//     */
//    private String getBaseUrl() {
//        return AppConfig.getDomainApi();
//    }
//
//    /**
//     * 处理风控逻辑
//     */
//    private void handleRiskControl(PaypalDispute dispute) {
//        try {
//            // 1. 检查买家风险
//            String buyerRiskKey = DISPUTE_RISK_KEY + "buyer:" + dispute.getBuyerId();
//            Integer buyerDisputeCount = redisCache.getCacheObject(buyerRiskKey);
//            if (buyerDisputeCount == null) {
//                buyerDisputeCount = 0;
//            }
//            buyerDisputeCount++;
//            redisCache.setCacheObject(buyerRiskKey, buyerDisputeCount, 30, TimeUnit.DAYS);
//
//            // 2. 检查争议类型风险
//            String disputeType = dispute.getDisputeType();
//            String reason = dispute.getReason();
//            boolean isHighRisk = isHighRiskDispute(disputeType, reason);
//
//            // 3. 检查金额风险
//            if (dispute.getAmount() != null && dispute.getAmount().compareTo(new BigDecimal("1000")) > 0) {
//                isHighRisk = true;
//            }
//
//            // 4. 处理高风险情况
//            if (isHighRisk || buyerDisputeCount >= HIGH_RISK_THRESHOLD) {
//                handleHighRiskDispute(dispute);
//            }
//
//            // 5. 发送通知
//            sendDisputeNotification(dispute);
//
//        } catch (Exception e) {
//            log.error("处理风控逻辑失败: disputeId={}", dispute.getDisputeId(), e);
//        }
//    }
//
//    /**
//     * 判断是否为高风险争议
//     */
//    private boolean isHighRiskDispute(String disputeType, String reason) {
//        // 高风险争议类型
//        if ("ITEM_NOT_RECEIVED".equals(disputeType) ||
//            "SIGNIFICANTLY_NOT_AS_DESCRIBED".equals(disputeType)) {
//            return true;
//        }
//
//        // 高风险原因
//        return StringUtils.contains(reason, "fraud") ||
//                StringUtils.contains(reason, "unauthorized") ||
//                StringUtils.contains(reason, "duplicate");
//    }
//
//    /**
//     * 处理高风险争议
//     */
//    private void handleHighRiskDispute(PaypalDispute dispute) {
//        try {
//            // 1. 记录高风险标记
//            String riskMarkKey = DISPUTE_RISK_KEY + "mark:" + dispute.getDisputeId();
//            redisCache.setCacheObject(riskMarkKey, true, 30, TimeUnit.DAYS);
//
//            // 2. 自动准备证据
//            prepareEvidence(dispute);
//
//            // 3. 通知管理员
//            notifyAdmin(dispute, "高风险争议");
//
//        } catch (Exception e) {
//            log.error("处理高风险争议失败: disputeId={}", dispute.getDisputeId(), e);
//        }
//    }
//
//    /**
//     * 准备争议证据
//     */
//    private void prepareEvidence(PaypalDispute dispute) {
//        try {
//            // 1. 获取订单信息
//            // TODO: 根据paymentId获取订单信息
//
//            // 2. 获取用户行为记录
//            // TODO: 获取用户登录、操作等记录
//
//            // 3. 获取IP地址信息
//            // TODO: 获取用户IP地址和地理位置信息
//
//            // 4. 获取设备信息
//            // TODO: 获取用户设备信息
//
//        } catch (Exception e) {
//            log.error("准备争议证据失败: disputeId={}", dispute.getDisputeId(), e);
//        }
//    }
//
//    /**
//     * 发送争议通知
//     */
//    private void sendDisputeNotification(PaypalDispute dispute) {
//        try {
//            String notifyKey = DISPUTE_NOTIFY_KEY + dispute.getDisputeId();
//            if (redisCache.getCacheObject(notifyKey) != null) {
//                return; // 已发送过通知
//            }
//
//            // 1. 发送系统通知
//            // TODO: 调用系统通知服务
//
//            // 2. 发送邮件通知
//            // TODO: 调用邮件服务
//
//            // 3. 记录通知状态
//            redisCache.setCacheObject(notifyKey, true, NOTIFY_EXPIRE_DAYS, TimeUnit.DAYS);
//
//        } catch (Exception e) {
//            log.error("发送争议通知失败: disputeId={}", dispute.getDisputeId(), e);
//        }
//    }
//
//    /**
//     * 通知管理员
//     */
//    private void notifyAdmin(PaypalDispute dispute, String reason) {
//        try {
//            // TODO: 实现管理员通知逻辑
//            log.info("管理员通知: disputeId={}, reason={}", dispute.getDisputeId(), reason);
//        } catch (Exception e) {
//            log.error("通知管理员失败: disputeId={}", dispute.getDisputeId(), e);
//        }
//    }
//}
