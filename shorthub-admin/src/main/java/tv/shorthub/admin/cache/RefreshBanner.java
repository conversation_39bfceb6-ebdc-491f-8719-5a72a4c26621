package tv.shorthub.admin.cache;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tv.shorthub.common.annotation.CacheRefresh;
import tv.shorthub.common.core.cache.CacheKeyUtils;
import tv.shorthub.common.core.cache.refresh.RefreshService;
import tv.shorthub.common.core.redis.RedisCache;
import tv.shorthub.common.dto.BannerConfig;
import tv.shorthub.system.domain.SysConfig;
import tv.shorthub.system.service.ISysConfigService;

import java.util.ArrayList;
import java.util.List;

@CacheRefresh(key = "refresh_banner", seconds = 10)
@Component
@Slf4j
public class RefreshBanner implements RefreshService {



    public static final List<String> languages = List.of("zh-CN", "zh-TW", "en-US");

    @Autowired
    private ISysConfigService configService;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    RedisCache redisCache;

    @Override
    public void start() {
        for (String language : languages) {
            List<SysConfig> configs = configService.selectConfigList(new SysConfig() {{
                setConfigKey("web.index.banner." + language);
            }});

            List<BannerConfig> banners = new ArrayList<>();
            if (!configs.isEmpty() && configs.getFirst().getConfigValue() != null) {
                try {
                    banners = objectMapper.readValue(configs.getFirst().getConfigValue(), new TypeReference<List<BannerConfig>>() {});
                } catch (Exception ignored) {
                    log.info("banner config error", ignored);
                }
            }
            redisCache.setCacheMapValue(CacheKeyUtils.MAP_BANNER, language, banners);
            log.info("设置Banner: language[{}], size[{}]", language, banners.size());
        }
    }
}
