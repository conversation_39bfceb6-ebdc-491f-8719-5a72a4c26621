package tv.shorthub.admin.cache;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tv.shorthub.common.annotation.CacheRefresh;
import tv.shorthub.common.core.cache.CacheKeyUtils;
import tv.shorthub.common.core.cache.refresh.RefreshService;
import tv.shorthub.common.core.redis.RedisCache;
import tv.shorthub.system.domain.AppRecharge;
import tv.shorthub.system.service.IAppRechargeService;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@CacheRefresh(key = "app_recharge", seconds = 10)
@Component
@Slf4j
public class RefreshAppRecharge implements RefreshService  {

    @Autowired
    IAppRechargeService AppRechargeService;


    @Autowired
    RedisCache redisCache;

    @Override
    public void start() {
        long time = System.currentTimeMillis();
        QueryWrapper<AppRecharge> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("enabled", true);
        List<AppRecharge> datas = AppRechargeService.getMapper().selectList(queryWrapper);
        Map<String, AppRecharge> templateGroup = datas.stream().collect(Collectors.toMap(AppRecharge::getTemplateId, v -> v));

        redisCache.setCacheMap(CacheKeyUtils.MAP_APP_RECHARGE, templateGroup);

        // 设置全局默认充值方案
        for (AppRecharge appRecharge : datas) {
            if (appRecharge.getIsDefault()) {
                redisCache.setCacheObject(CacheKeyUtils.RECHARGE_TEMPLATE_SYSTEM, appRecharge);
                log.info("设置全局默认充值方案：{}, {}", appRecharge.getAppid(), appRecharge.getTemplateName());
            }
        }

        log.info("刷新充值模板缓存成功，耗时：{}ms", System.currentTimeMillis() - time);
    }

}
