package tv.shorthub.admin.cache;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tv.shorthub.common.annotation.CacheRefresh;
import tv.shorthub.common.core.cache.CacheKeyUtils;
import tv.shorthub.common.core.cache.refresh.RefreshService;
import tv.shorthub.common.core.redis.RedisCache;
import tv.shorthub.system.domain.AdRetargetingStrategy;
import tv.shorthub.system.domain.AppPromotion;
import tv.shorthub.system.service.IAdRetargetingStrategyService;
import tv.shorthub.system.service.IAppPromotionService;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@CacheRefresh(key = "ad_retargeting_strategy", seconds = 10)
@Component
@Slf4j
public class RefreshAdRetargetingStrategy implements RefreshService {

    @Autowired
    IAdRetargetingStrategyService adRetargetingStrategyService;

    @Autowired
    RedisCache redisCache;

    @Override
    public void start() {

        List<AdRetargetingStrategy> list = adRetargetingStrategyService.getMapper().selectList(null);

        Map<String, AdRetargetingStrategy> collect = list.stream().collect(Collectors.toMap(v -> v.getId().toString(), v -> v));
        redisCache.setCacheMap(CacheKeyUtils.MAP_AD_RETARGETING_STRATEGY, collect);

        log.info("刷新策略配置缓存成功，size：{}", list.size());
    }
}
