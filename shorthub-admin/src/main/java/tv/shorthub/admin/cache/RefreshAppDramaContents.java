package tv.shorthub.admin.cache;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tv.shorthub.common.annotation.CacheRefresh;
import tv.shorthub.common.core.cache.CacheKeyUtils;
import tv.shorthub.common.core.cache.refresh.RefreshService;
import tv.shorthub.common.core.redis.RedisCache;
import tv.shorthub.common.dto.ContentInfo;
import tv.shorthub.system.domain.AppCountriesLanguages;
import tv.shorthub.system.domain.AppDrama;
import tv.shorthub.system.domain.AppDramaContents;
import tv.shorthub.system.domain.AppDramaDisplayRules;
import tv.shorthub.system.service.IAppCountriesLanguagesService;
import tv.shorthub.system.service.IAppDramaContentsService;
import tv.shorthub.system.service.IAppDramaDisplayRulesService;
import tv.shorthub.system.service.IAppDramaService;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@CacheRefresh(key = "app_drama_contents", seconds = 10)
@Component
@Slf4j
public class RefreshAppDramaContents implements RefreshService  {

    @Autowired
    IAppDramaContentsService appDramaContentsService;

    @Autowired
    IAppDramaService appDramaService;

    @Autowired
    RedisCache redisCache;

    @Autowired
    IAppDramaDisplayRulesService appDramaDisplayRulesService;

    @Autowired
    IAppCountriesLanguagesService appCountriesLanguagesService;

    @Override
    public void start() {
        long time = System.currentTimeMillis();
        List<AppDrama> dramas = appDramaService.getMapper().selectList(new QueryWrapper<>());

        List<String> enableDramas = dramas.stream().map(AppDrama::getDramaId).toList();

        List<AppDramaContents> appDramaContents = appDramaContentsService.getMapper().selectList(new QueryWrapper<AppDramaContents>().in("drama_id", enableDramas));

        // 按语种缓存
        Map<String, List<AppDramaContents>> languageGroup = appDramaContents.stream().collect(Collectors.groupingBy(AppDramaContents::getLanguageCode));

        for (String language : RefreshBanner.languages) {
            String key = CacheKeyUtils.getDramas(language);
            if (!languageGroup.containsKey(language)) {
                redisCache.setCacheList(key, new ArrayList<>());
            } else {
                List<ContentInfo> list = languageGroup.get(language).stream().map(content -> convertDTO(dramas.stream().filter(drama -> drama.getDramaId().equals(content.getDramaId())).findFirst().orElse(null), content)).toList();
                redisCache.setCacheList(key, list);
            }
        }

        // 按contentId缓存DTO
        Map<String, ContentInfo> contentIdGroupDTO = appDramaContents.stream()
                .map(content -> convertDTO(dramas.stream().filter(drama -> drama.getDramaId().equals(content.getDramaId())).findFirst().orElse(null), content))
                .collect(Collectors.toMap(ContentInfo::getContentId, v -> v));

        redisCache.setCacheMap(CacheKeyUtils.MAP_CONTENT_DETAIL_DTO, contentIdGroupDTO);

        // 按contentId缓存原始数据, MAP结构
        Map<String, AppDramaContents> contentIdGroup = appDramaContents.stream().collect(Collectors.toMap(AppDramaContents::getContentId, v -> v));
        redisCache.setCacheMap(CacheKeyUtils.MAP_APP_DRAMA_CONTENTS, contentIdGroup);

        // 按contentId缓存原始数据, 单个缓存
        for (AppDramaContents value : contentIdGroup.values()) {
            redisCache.setCacheObject(CacheKeyUtils.getContent(value.getContentId()), value);
        }

        // 新增：按地区和语种缓存
        refreshDramasByCountryAndLanguage(appDramaContents, dramas);

        log.info("刷新剧目缓存成功，耗时：{}ms", System.currentTimeMillis() - time);
    }

    /**
     * 按地区和语种缓存剧目
     * @param appDramaContents 剧目内容列表
     */
    private void refreshDramasByCountryAndLanguage(List<AppDramaContents> appDramaContents, List<AppDrama> dramas) {
        Map<String, AppDrama> dramaMap = dramas.stream().collect(
                Collectors.toMap(AppDrama::getDramaId, v -> v)
        );

        // 查询所有剧目的显示规则
        List<AppDramaDisplayRules> displayRules = appDramaDisplayRulesService.getMapper().selectList(
            new QueryWrapper<AppDramaDisplayRules>().in("drama_id",
                appDramaContents.stream().map(AppDramaContents::getDramaId).distinct().toList())
        );

        // 查询所有国家语言配置
        List<AppCountriesLanguages> countriesLanguages = appCountriesLanguagesService.getMapper().selectList(null);

        // 按剧目ID分组显示规则
        Map<String, List<AppDramaDisplayRules>> dramaDisplayRulesMap = displayRules.stream()
            .collect(Collectors.groupingBy(AppDramaDisplayRules::getDramaId));

        // 按语种分组
        Map<String, List<AppDramaContents>> languageGroup = appDramaContents.stream()
            .collect(Collectors.groupingBy(AppDramaContents::getLanguageCode));

        // 获取所有配置的国家语言组合
        Set<String> configuredKeys = countriesLanguages.stream()
            .map(AppCountriesLanguages::getCountryCode)
            .collect(Collectors.toSet());

        // 处理每个语种
        for (Map.Entry<String, List<AppDramaContents>> entry : languageGroup.entrySet()) {
            String language = entry.getKey();
            List<AppDramaContents> contents = entry.getValue();

            // 获取没有地区限制的剧目
            List<ContentInfo> noRuleContents = contents.stream()
                .filter(content -> !dramaDisplayRulesMap.containsKey(content.getDramaId()))
                .map(content -> convertDTO(dramaMap.get(content.getDramaId()), content))
                .toList();

            // 将剧目添加到对应地区的缓存中
            for (String country : configuredKeys) {
                String key = CacheKeyUtils.getDramasCountryLang(country, language);
                // 获取当前地区可展示的剧目
                List<ContentInfo> dramaContents = contents.stream()
                    .filter(content -> {
                        List<AppDramaDisplayRules> rules = dramaDisplayRulesMap.get(content.getDramaId());
                        return rules != null && rules.stream()
                            .anyMatch(rule -> rule.getCountryCode().equals(country));
                    })
                    .map(content -> convertDTO(dramaMap.get(content.getDramaId()), content))
                    .toList();

                // 合并有规则的剧目和没有规则的剧目，并去重
                List<ContentInfo> mergedContents = Stream.concat(dramaContents.stream(), noRuleContents.stream())
                    .collect(Collectors.groupingBy(ContentInfo::getContentId))
                    .values().stream()
                    .map(List::getFirst)
                    .toList();

                if (CollectionUtils.isEmpty(mergedContents)) {
                    redisCache.deleteObject(key);
                } else {
                    redisCache.setCacheList(key, mergedContents);
                }
            }
        }
    }

    public static ContentInfo convertDTO(AppDrama drama, AppDramaContents item) {
        ContentInfo contentInfo = new ContentInfo();
        contentInfo.setContentId(item.getContentId());
        contentInfo.setImage(item.getImage());
        contentInfo.setTotal(item.getTotal());
        contentInfo.setDescription(item.getDescription());
        contentInfo.setTitle(item.getTitle());
        contentInfo.setFeeCoin(item.getEveryMoney());
        contentInfo.setFeeBegin(item.getFeeBegin());
        contentInfo.setEnabled(item.getEnable() && null != drama && drama.getEnable());
        return contentInfo;
    }
}
