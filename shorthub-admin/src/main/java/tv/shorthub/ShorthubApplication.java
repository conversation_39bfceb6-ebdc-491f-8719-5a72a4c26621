package tv.shorthub;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * 启动程序
 *
 * <AUTHOR>
 */
@SpringBootApplication(
    exclude = { DataSourceAutoConfiguration.class },
    scanBasePackages = {"tv.shorthub"}
)
@EnableScheduling
@EnableAsync
public class ShorthubApplication
{
    public static void main(String[] args)
    {
        Runtime runtime = Runtime.getRuntime();

        // 获取堆内存信息（单位：字节）
        long maxMemory = runtime.maxMemory(); // 最大堆内存（-Xmx）
        long totalMemory = runtime.totalMemory(); // 当前分配的堆内存（-Xms 或动态调整）
        long freeMemory = runtime.freeMemory(); // 当前空闲堆内存
        long usedMemory = totalMemory - freeMemory; // 已用堆内存

        // 转换为 MB 输出
        System.out.println("最大堆内存: " + maxMemory / (1024 * 1024) + " MB");
        System.out.println("当前分配堆内存: " + totalMemory / (1024 * 1024) + " MB");
        System.out.println("空闲堆内存: " + freeMemory / (1024 * 1024) + " MB");
        System.out.println("已用堆内存: " + usedMemory / (1024 * 1024) + " MB");
        // System.setProperty("spring.devtools.restart.enabled", "false");
        SpringApplication.run(ShorthubApplication.class, args);
        System.out.println("(♥◠‿◠)ﾉﾞ  系统启动成功   ლ(´ڡ`ლ)ﾞ");
    }
}
