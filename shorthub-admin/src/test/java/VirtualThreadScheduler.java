import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

public class VirtualThreadScheduler {

    public static void main(String[] args) {
        // 使用 try-with-resources 创建 ScheduledExecutorService
        try (ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1)) {

            // 启动20个虚拟线程，每个线程都会执行一个定时任务
            for (int i = 0; i < 20; i++) {
                int threadId = i;
                Runnable task = () -> {
                    Thread.ofVirtual().start(() -> {
                        System.out.println("Task executed by thread " + threadId + " at: " + System.currentTimeMillis() + " by " + Thread.currentThread());
                    });
                };

                // 安排任务每秒执行一次
                scheduler.scheduleAtFixedRate(task, 0, 1, TimeUnit.SECONDS);
            }

            // 添加一个钩子来在程序结束时关闭调度器
            Runtime.getRuntime().addShutdownHook(new Thread(() -> {
                scheduler.shutdown();
                try {
                    if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                        scheduler.shutdownNow();
                    }
                } catch (InterruptedException e) {
                    scheduler.shutdownNow();
                }
            }));

            // 保持主线程运行
            synchronized (VirtualThreadScheduler.class) {
                try {
                    VirtualThreadScheduler.class.wait();
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            }
        }
    }
}
