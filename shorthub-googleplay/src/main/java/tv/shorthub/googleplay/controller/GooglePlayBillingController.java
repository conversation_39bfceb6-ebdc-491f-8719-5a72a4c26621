package tv.shorthub.googleplay.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import tv.shorthub.googleplay.util.GooglePlayBillingChecker;
import tv.shorthub.googleplay.util.GooglePlayBillingChecker.BillingCheckResult;

import java.util.HashMap;
import java.util.Map;

/**
 * Google Play Billing权限检查控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/googleplay/billing")
public class GooglePlayBillingController {

    @Autowired
    private GooglePlayBillingChecker billingChecker;

    /**
     * 检查指定应用的BILLING权限
     * @param packageName 应用包名
     * @return BILLING权限检查结果
     */
    @GetMapping("/check/{packageName}")
    public ResponseEntity<Map<String, Object>> checkBillingPermission(@PathVariable String packageName) {
        try {
            log.info("开始检查应用BILLING权限: {}", packageName);
            
            BillingCheckResult result = billingChecker.checkBillingPermission(packageName);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", result.isSuccess());
            response.put("packageName", result.getPackageName());
            response.put("appName", result.getAppName());
            response.put("errorMessage", result.getErrorMessage());
            response.put("checks", result.getChecks());
            response.put("summary", generateSummary(result));
            response.put("nextSteps", generateNextSteps(result));
            
            log.info("BILLING权限检查完成: {} - {}", packageName, result.isSuccess() ? "成功" : "失败");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("BILLING权限检查异常: {}", e.getMessage(), e);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("errorMessage", "BILLING权限检查过程中发生异常: " + e.getMessage());
            
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * 生成检查摘要
     */
    private Map<String, Object> generateSummary(BillingCheckResult result) {
        Map<String, Object> summary = new HashMap<>();
        
        long passedCount = result.getChecks().values().stream()
                .filter(check -> check.isPassed())
                .count();
        long totalCount = result.getChecks().size();
        
        summary.put("passedCount", passedCount);
        summary.put("totalCount", totalCount);
        summary.put("passRate", totalCount > 0 ? (double) passedCount / totalCount : 0.0);
        summary.put("status", result.isSuccess() ? "正常" : "需要配置");
        
        return summary;
    }

    /**
     * 生成下一步操作建议
     */
    private Map<String, Object> generateNextSteps(BillingCheckResult result) {
        Map<String, Object> nextSteps = new HashMap<>();
        
        if (result.isSuccess()) {
            nextSteps.put("message", "BILLING权限已正确配置，可以创建应用内商品");
            nextSteps.put("actions", new String[]{
                "可以开始创建应用内商品",
                "可以管理订阅产品",
                "可以处理购买验证"
            });
        } else {
            nextSteps.put("message", "需要配置BILLING权限才能创建应用内商品");
            nextSteps.put("actions", new String[]{
                "访问 Google Play Console: https://play.google.com/console",
                "选择应用: " + result.getPackageName(),
                "进入 '设置' > '应用内商品'",
                "点击 '启用应用内商品'",
                "阅读并同意条款",
                "保存设置"
            });
            nextSteps.put("priority", "high");
        }
        
        return nextSteps;
    }
} 