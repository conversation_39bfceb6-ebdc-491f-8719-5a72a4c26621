package tv.shorthub.googleplay.controller;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import tv.shorthub.googleplay.model.Product;
import tv.shorthub.googleplay.model.Purchase;
import tv.shorthub.googleplay.service.GooglePlayService;

import java.util.List;

/**
 * Google Play 控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/googleplay")
@RequiredArgsConstructor
public class GooglePlayController {

    private final GooglePlayService googlePlayService;

    // ==================== 产品管理 ====================

    /**
     * 获取应用内产品列表
     */
    @GetMapping("/{packageName}/products")
    public ResponseEntity<List<Product>> getProducts(@PathVariable String packageName) {
        try {
            List<Product> products = googlePlayService.getProducts(packageName);
            return ResponseEntity.ok(products);
        } catch (Exception e) {
            log.error("获取产品列表失败: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 获取单个产品详情
     */
    @GetMapping("/{packageName}/products/{productId}")
    public ResponseEntity<Product> getProduct(@PathVariable String packageName, @PathVariable String productId) {
        try {
            Product product = googlePlayService.getProduct(packageName, productId);
            return ResponseEntity.ok(product);
        } catch (Exception e) {
            log.error("获取产品详情失败: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 创建应用内产品
     */
    @PostMapping("/{packageName}/products")
    public ResponseEntity<Product> createProduct(@PathVariable String packageName, @RequestBody Product product) {
        try {
            Product createdProduct = googlePlayService.createProduct(packageName, product);
            return ResponseEntity.ok(createdProduct);
        } catch (Exception e) {
            log.error("创建产品失败: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 更新应用内产品
     */
    @PutMapping("/{packageName}/products/{productId}")
    public ResponseEntity<Product> updateProduct(@PathVariable String packageName, 
                                               @PathVariable String productId, 
                                               @RequestBody Product product) {
        try {
            Product updatedProduct = googlePlayService.updateProduct(packageName, productId, product);
            return ResponseEntity.ok(updatedProduct);
        } catch (Exception e) {
            log.error("更新产品失败: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 删除应用内产品
     */
    @DeleteMapping("/{packageName}/products/{productId}")
    public ResponseEntity<Void> deleteProduct(@PathVariable String packageName, @PathVariable String productId) {
        try {
            googlePlayService.deleteProduct(packageName, productId);
            return ResponseEntity.ok().build();
        } catch (Exception e) {
            log.error("删除产品失败: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    // ==================== 订单管理 ====================

    /**
     * 验证购买
     */
    @GetMapping("/{packageName}/purchases/verify")
    public ResponseEntity<Purchase> verifyPurchase(@PathVariable String packageName,
                                                 @RequestParam String productId,
                                                 @RequestParam String purchaseToken) {
        try {
            Purchase purchase = googlePlayService.verifyPurchase(packageName, productId, purchaseToken);
            return ResponseEntity.ok(purchase);
        } catch (Exception e) {
            log.error("验证购买失败: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 确认购买
     */
    @PostMapping("/{packageName}/purchases/acknowledge")
    public ResponseEntity<Void> acknowledgePurchase(@PathVariable String packageName,
                                                  @RequestParam String productId,
                                                  @RequestParam String purchaseToken) {
        try {
            googlePlayService.acknowledgePurchase(packageName, productId, purchaseToken);
            return ResponseEntity.ok().build();
        } catch (Exception e) {
            log.error("确认购买失败: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 消费购买
     */
    @PostMapping("/{packageName}/purchases/consume")
    public ResponseEntity<Void> consumePurchase(@PathVariable String packageName,
                                              @RequestParam String productId,
                                              @RequestParam String purchaseToken) {
        try {
            googlePlayService.consumePurchase(packageName, productId, purchaseToken);
            return ResponseEntity.ok().build();
        } catch (Exception e) {
            log.error("消费购买失败: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 查询用户购买历史
     */
    @GetMapping("/{packageName}/purchases/user/{userId}")
    public ResponseEntity<List<Purchase>> getUserPurchases(@PathVariable String packageName, 
                                                          @PathVariable String userId) {
        try {
            List<Purchase> purchases = googlePlayService.getUserPurchases(packageName, userId);
            return ResponseEntity.ok(purchases);
        } catch (Exception e) {
            log.error("获取用户购买历史失败: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    // ==================== 订阅管理 ====================

    /**
     * 取消订阅
     */
    @PostMapping("/{packageName}/subscriptions/cancel")
    public ResponseEntity<Void> cancelSubscription(@PathVariable String packageName,
                                                 @RequestParam String productId,
                                                 @RequestParam String purchaseToken) {
        try {
            googlePlayService.cancelSubscription(packageName, productId, purchaseToken);
            return ResponseEntity.ok().build();
        } catch (Exception e) {
            log.error("取消订阅失败: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 恢复订阅
     */
    @PostMapping("/{packageName}/subscriptions/restore")
    public ResponseEntity<Void> restoreSubscription(@PathVariable String packageName,
                                                  @RequestParam String productId,
                                                  @RequestParam String purchaseToken) {
        try {
            googlePlayService.restoreSubscription(packageName, productId, purchaseToken);
            return ResponseEntity.ok().build();
        } catch (Exception e) {
            log.error("恢复订阅失败: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 延期订阅
     */
    @PostMapping("/{packageName}/subscriptions/extend")
    public ResponseEntity<Void> extendSubscription(@PathVariable String packageName,
                                                 @RequestParam String productId,
                                                 @RequestParam String purchaseToken,
                                                 @RequestParam long extensionTimeInSeconds) {
        try {
            googlePlayService.extendSubscription(packageName, productId, purchaseToken, extensionTimeInSeconds);
            return ResponseEntity.ok().build();
        } catch (Exception e) {
            log.error("延期订阅失败: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 获取活跃订阅
     */
    @GetMapping("/{packageName}/subscriptions/active/{userId}")
    public ResponseEntity<List<Purchase>> getActiveSubscriptions(@PathVariable String packageName, 
                                                                @PathVariable String userId) {
        try {
            List<Purchase> subscriptions = googlePlayService.getActiveSubscriptions(packageName, userId);
            return ResponseEntity.ok(subscriptions);
        } catch (Exception e) {
            log.error("获取活跃订阅失败: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }
} 