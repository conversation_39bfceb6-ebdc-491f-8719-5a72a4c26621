package tv.shorthub.googleplay.config;

import com.google.api.client.googleapis.auth.oauth2.GoogleCredential;
import com.google.api.client.googleapis.javanet.GoogleNetHttpTransport;
import com.google.api.client.http.HttpRequestInitializer;
import com.google.api.client.http.HttpTransport;
import com.google.api.client.http.javanet.NetHttpTransport;
import com.google.api.client.json.jackson2.JacksonFactory;
import com.google.api.services.androidpublisher.AndroidPublisher;
import com.google.api.services.androidpublisher.AndroidPublisherScopes;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ClassPathResource;

import java.io.IOException;
import java.io.InputStream;
import java.net.InetSocketAddress;
import java.net.Proxy;
import java.security.GeneralSecurityException;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Google Play 配置类
 * 支持多个密钥管理和应用配置，HTTP代理支持
 */
@Slf4j
@Configuration
@ConfigurationProperties(prefix = "google.play")
@Data
public class GooglePlayConfig {

    /**
     * 应用配置列表
     */
    private List<AppConfig> apps;

    /**
     * 多语言配置
     */
    private LanguageConfig language;

    /**
     * 应用配置
     */
    @Data
    public static class AppConfig {
        /**
         * 应用包名
         */
        private String packageName;

        /**
         * 服务账号密钥文件路径
         */
        private String serviceAccountKeyPath;

        /**
         * 应用名称（用于标识）
         */
        private String appName;

        /**
         * 是否启用
         */
        private boolean enabled = true;

        /**
         * 默认语言
         */
        private String defaultLanguage;
        private String defaultCurrency;
        private String defaultRegionCode;

        /**
         * 代理配置
         */
        private ProxyConfig proxy;
    }

    /**
     * 多语言配置
     */
    @Data
    public static class LanguageConfig {
        /**
         * 映射
         */
        private Map<String, Map<String, String>> configs = new HashMap<>();
    }

    /**
     * 代理配置
     */
    @Data
    public static class ProxyConfig {
        /**
         * 是否启用代理
         */
        private boolean enabled = false;

        /**
         * 代理类型 (HTTP, SOCKS)
         */
        private String type = "HTTP";

        /**
         * 代理服务器地址
         */
        private String host;

        /**
         * 代理服务器端口
         */
        private int port;

        /**
         * 代理用户名（可选）
         */
        private String username;

        /**
         * 代理密码（可选）
         */
        private String password;

        /**
         * 连接超时时间（毫秒）
         */
        private int connectTimeout = 30000;

        /**
         * 读取超时时间（毫秒）
         */
        private int readTimeout = 60000;
    }

    /**
     * AndroidPublisher客户端缓存
     */
    private final Map<String, AndroidPublisher> publisherCache = new HashMap<>();

    /**
     * 获取AndroidPublisher客户端
     * @param packageName 应用包名
     * @return AndroidPublisher客户端
     */
    public AndroidPublisher getAndroidPublisher(String packageName) {
        return publisherCache.computeIfAbsent(packageName, this::createAndroidPublisher);
    }

    /**
     * 创建AndroidPublisher客户端
     * @param packageName 应用包名
     * @return AndroidPublisher客户端
     */
    private AndroidPublisher createAndroidPublisher(String packageName) {
        try {
            AppConfig appConfig = getAppConfig(packageName);
            if (appConfig == null) {
                throw new IllegalArgumentException("未找到包名为 " + packageName + " 的应用配置");
            }

            // 创建自定义的 HttpTransport
            HttpTransport httpTransport = createCustomHttpTransport(appConfig.getProxy());
            JacksonFactory jsonFactory = JacksonFactory.getDefaultInstance();

            // 加载服务账号密钥
            GoogleCredential credential = loadServiceAccountCredential(appConfig.getServiceAccountKeyPath());
            credential = credential.createScoped(Collections.singleton(AndroidPublisherScopes.ANDROIDPUBLISHER));

            // 创建自定义的请求初始化器
            HttpRequestInitializer requestInitializer = createCustomRequestInitializer(credential, appConfig.getProxy());

            return new AndroidPublisher.Builder(httpTransport, jsonFactory, requestInitializer)
                    .setApplicationName(appConfig.getAppName())
                    .build();

        } catch (GeneralSecurityException | IOException e) {
            log.error("创建AndroidPublisher客户端失败: {}", e.getMessage(), e);
            throw new RuntimeException("创建AndroidPublisher客户端失败", e);
        }
    }

    /**
     * 创建自定义的 HttpTransport
     * @return HttpTransport
     * @throws GeneralSecurityException
     * @throws IOException
     */
    private HttpTransport createCustomHttpTransport(ProxyConfig proxy) throws GeneralSecurityException, IOException {
        if (proxy != null && proxy.isEnabled()) {
            log.info("使用代理配置: {}:{}", proxy.getHost(), proxy.getPort());
            
            Proxy.Type proxyType = "SOCKS".equalsIgnoreCase(proxy.getType()) ? 
                Proxy.Type.SOCKS : Proxy.Type.HTTP;
            
            Proxy proxySettings = new Proxy(proxyType, 
                new InetSocketAddress(proxy.getHost(), proxy.getPort()));

            NetHttpTransport.Builder builder = new NetHttpTransport.Builder()
                .setProxy(proxySettings);

            return builder.build();
        } else {
            return GoogleNetHttpTransport.newTrustedTransport();
        }
    }

    /**
     * 创建自定义的请求初始化器
     * @param credential Google凭证
     * @return HttpRequestInitializer
     */
    private HttpRequestInitializer createCustomRequestInitializer(GoogleCredential credential, ProxyConfig proxy) {
        return request -> {
            // 首先应用原有的凭证
            credential.initialize(request);

            // 设置超时时间
            if (proxy != null && proxy.isEnabled()) {
                request.setConnectTimeout(proxy.getConnectTimeout());
                request.setReadTimeout(proxy.getReadTimeout());
            }
        };
    }

    /**
     * 获取应用配置
     * @param packageName 应用包名
     * @return 应用配置
     */
    public AppConfig getAppConfig(String packageName) {
        if (apps == null) {
            return null;
        }
        return apps.stream()
                .filter(app -> packageName.equals(app.getPackageName()) && app.isEnabled())
                .findFirst()
                .orElse(null);
    }

    /**
     * 加载服务账号密钥
     * @param keyPath 密钥文件路径
     * @return GoogleCredential
     * @throws IOException IO异常
     */
    private GoogleCredential loadServiceAccountCredential(String keyPath) throws IOException {
        try (InputStream inputStream = new ClassPathResource(keyPath).getInputStream()) {
            return GoogleCredential.fromStream(inputStream);
        }
    }

    /**
     * 获取应用的默认语言
     * @param packageName 应用包名
     * @return 默认语言
     */
    public String getDefaultLanguage(String packageName) {
        AppConfig appConfig = getAppConfig(packageName);
        if (appConfig != null && appConfig.getDefaultLanguage() != null) {
            return appConfig.getDefaultLanguage();
        }
        return "en-US";
    }

}