package tv.shorthub.googleplay.model;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 应用内产品模型
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Product {
    
    /**
     * 产品类型常量
     */
    public static final String TYPE_INAPP = "inapp";
    public static final String TYPE_SUBS = "subs";
    
    /**
     * 产品状态常量
     */
    public static final String STATUS_ACTIVE = "active";
    public static final String STATUS_INACTIVE = "inactive";
    public static final String STATUS_UNSPECIFIED = "statusUnspecified";
    
    /**
     * 货币代码常量
     */
    public static final String CURRENCY_HKD = "HKD";  // 港币
    public static final String CURRENCY_CNY = "CNY";  // 人民币
    public static final String CURRENCY_USD = "USD";  // 美元
    public static final String CURRENCY_EUR = "EUR";  // 欧元
    public static final String CURRENCY_GBP = "GBP";  // 英镑
    public static final String CURRENCY_JPY = "JPY";  // 日元
    public static final String CURRENCY_KRW = "KRW";  // 韩元
    public static final String CURRENCY_TWD = "TWD";  // 新台币
    public static final String CURRENCY_SGD = "SGD";  // 新加坡元
    public static final String CURRENCY_MYR = "MYR";  // 马来西亚林吉特
    public static final String CURRENCY_THB = "THB";  // 泰铢
    public static final String CURRENCY_IDR = "IDR";  // 印尼盾
    public static final String CURRENCY_PHP = "PHP";  // 菲律宾比索
    public static final String CURRENCY_VND = "VND";  // 越南盾
    
    /**
     * 产品ID
     */
    private String productId;
    
    /**
     * 产品名称
     */
    private String name;
    
    /**
     * 产品描述
     */
    private String description;
    
    /**
     * 产品类型 (inapp, subs)
     */
    private String type;
    
    /**
     * 默认价格
     */
    private BigDecimal defaultPrice;
    
    /**
     * 默认价格货币代码
     */
    private String defaultPriceCurrencyCode;
    
    /**
     * 产品状态
     */
    private String status;
    
    /**
     * 包名
     */
    private String packageName;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
} 