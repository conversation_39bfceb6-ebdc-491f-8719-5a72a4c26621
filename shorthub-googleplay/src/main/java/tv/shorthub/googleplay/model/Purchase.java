package tv.shorthub.googleplay.model;

import com.alibaba.fastjson2.JSONObject;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 购买订单模型
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Purchase {
    
    /**
     * 订单ID
     */
    private String orderId;
    
    /**
     * 产品ID
     */
    private String productId;
    
    /**
     * 包名
     */
    private String packageName;
    
    /**
     * 购买令牌
     */
    private String purchaseToken;
    
    /**
     * 购买状态
     */
    private String purchaseState;
    
    /**
     * 消费状态
     */
    private String consumptionState;
    
    /**
     * 开发者有效载荷
     */
    private String developerPayload;
    
    /**
     * 购买时间
     */
    private LocalDateTime purchaseTime;
    
    /**
     * 购买价格
     */
    private BigDecimal purchasePrice;
    
    /**
     * 购买价格货币代码
     */
    private String purchasePriceCurrencyCode;
    
    /**
     * 用户ID
     */
    private String userId;
    
    /**
     * 是否确认
     */
    private boolean acknowledged;
    
    /**
     * 是否自动续费（订阅）
     */
    private boolean autoRenewing;
    
    /**
     * 订阅过期时间
     */
    private LocalDateTime expiryTime;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;

    private JSONObject rawData;
} 