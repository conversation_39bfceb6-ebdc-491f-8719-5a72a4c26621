package tv.shorthub.googleplay.util;

import com.google.api.services.androidpublisher.AndroidPublisher;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tv.shorthub.googleplay.config.GooglePlayConfig;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * Google Play Billing权限检查工具类
 */
@Slf4j
@Component
public class GooglePlayBillingChecker {

    @Autowired
    private GooglePlayConfig googlePlayConfig;

    /**
     * 检查应用的BILLING权限
     * @param packageName 应用包名
     * @return 检查结果
     */
    public BillingCheckResult checkBillingPermission(String packageName) {
        BillingCheckResult result = new BillingCheckResult();
        result.setPackageName(packageName);
        
        try {
            // 检查应用内商品权限
            checkInAppProductsPermission(packageName, result);
            
        } catch (Exception e) {
            log.error("BILLING权限检查失败: {}", e.getMessage(), e);
            result.setSuccess(false);
            result.setErrorMessage("BILLING权限检查过程中发生错误: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 检查应用内商品权限
     */
    private void checkInAppProductsPermission(String packageName, BillingCheckResult result) {
        try {
            AndroidPublisher androidPublisher = googlePlayConfig.getAndroidPublisher(packageName);
            
            // 尝试获取应用内商品列表
            try {
                androidPublisher.inappproducts().list(packageName).execute();
                result.addCheck("应用内商品权限", true, "可以访问应用内商品，BILLING权限已启用");
                result.setSuccess(true);
            } catch (IOException e) {
                if (e.getMessage().contains("BILLING permission")) {
                    result.addCheck("应用内商品权限", false, "应用未启用BILLING权限，需要在Google Play Console中启用");
                    result.setSuccess(false);
                    result.setErrorMessage("应用必须在Google Play Console中启用BILLING权限才能创建应用内商品");
                } else if (e.getMessage().contains("403")) {
                    result.addCheck("应用内商品权限", false, "没有管理应用内商品的权限");
                    result.setSuccess(false);
                    result.setErrorMessage("服务账号没有管理应用内商品的权限");
                } else if (e.getMessage().contains("404")) {
                    result.addCheck("应用内商品权限", false, "应用不存在或包名错误");
                    result.setSuccess(false);
                    result.setErrorMessage("应用 " + packageName + " 在Google Play Console中不存在");
                } else {
                    result.addCheck("应用内商品权限", false, "权限检查失败: " + e.getMessage());
                    result.setSuccess(false);
                    result.setErrorMessage("权限检查失败: " + e.getMessage());
                }
            }
            
        } catch (Exception e) {
            result.addCheck("应用内商品权限", false, "权限检查异常: " + e.getMessage());
            result.setSuccess(false);
            result.setErrorMessage("权限检查异常: " + e.getMessage());
        }
    }
    
    /**
     * BILLING权限检查结果
     */
    public static class BillingCheckResult {
        private String packageName;
        private String appName;
        private boolean success = true;
        private String errorMessage;
        private Map<String, CheckItem> checks = new HashMap<>();
        
        public void addCheck(String name, boolean passed, String message) {
            checks.put(name, new CheckItem(passed, message));
        }
        
        // Getters and Setters
        public String getPackageName() { return packageName; }
        public void setPackageName(String packageName) { this.packageName = packageName; }
        
        public String getAppName() { return appName; }
        public void setAppName(String appName) { this.appName = appName; }
        
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        
        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
        
        public Map<String, CheckItem> getChecks() { return checks; }
        public void setChecks(Map<String, CheckItem> checks) { this.checks = checks; }
        
        @Override
        public String toString() {
            StringBuilder sb = new StringBuilder();
            sb.append("BILLING权限检查结果 - ").append(packageName).append("\n");
            if (appName != null) {
                sb.append("应用名称: ").append(appName).append("\n");
            }
            sb.append("总体状态: ").append(success ? "成功" : "失败").append("\n");
            
            if (errorMessage != null) {
                sb.append("错误信息: ").append(errorMessage).append("\n");
            }
            
            sb.append("详细检查:\n");
            checks.forEach((name, check) -> {
                sb.append("  ").append(name).append(": ")
                  .append(check.isPassed() ? "✓" : "✗")
                  .append(" ").append(check.getMessage()).append("\n");
            });
            
            return sb.toString();
        }
    }
    
    /**
     * 检查项目
     */
    public static class CheckItem {
        private boolean passed;
        private String message;
        
        public CheckItem(boolean passed, String message) {
            this.passed = passed;
            this.message = message;
        }
        
        public boolean isPassed() { return passed; }
        public void setPassed(boolean passed) { this.passed = passed; }
        
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
    }
} 