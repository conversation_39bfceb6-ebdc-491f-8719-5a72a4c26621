package tv.shorthub.googleplay.service.impl;

import com.alibaba.fastjson2.JSONObject;
import com.google.api.services.androidpublisher.AndroidPublisher;
import com.google.api.services.androidpublisher.model.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tv.shorthub.googleplay.config.GooglePlayConfig;
import tv.shorthub.googleplay.model.Product;
import tv.shorthub.googleplay.model.Purchase;
import tv.shorthub.googleplay.service.GooglePlayService;

import java.io.IOException;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;

/**
 * Google Play 服务实现类（适配 v3-rev20250602-2.0.0）
 */
@Slf4j
@Service
public class GooglePlayServiceImpl implements GooglePlayService {

    @Autowired
    private GooglePlayConfig googlePlayConfig;

    // ==================== 产品管理 ====================

    @Override
    public List<Product> getProducts(String packageName) {
        try {
            AndroidPublisher androidPublisher = googlePlayConfig.getAndroidPublisher(packageName);
            AndroidPublisher.Inappproducts.List request = androidPublisher.inappproducts().list(packageName);
            InappproductsListResponse response = request.execute();
            List<Product> products = new ArrayList<>();
            if (response.getInappproduct() != null) {
                for (InAppProduct inAppProduct : response.getInappproduct()) {
                    products.add(convertToProduct(inAppProduct, packageName));
                }
            }
            return products;
        } catch (IOException e) {
            log.error("获取产品列表失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取产品列表失败", e);
        }
    }

    @Override
    public Product getProduct(String packageName, String productId) {
        try {
            AndroidPublisher androidPublisher = googlePlayConfig.getAndroidPublisher(packageName);
            AndroidPublisher.Inappproducts.Get request = androidPublisher.inappproducts().get(packageName, productId);
            InAppProduct inAppProduct = request.execute();
            return convertToProduct(inAppProduct, packageName);
        } catch (IOException e) {
            log.error("获取产品详情失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取产品详情失败", e);
        }
    }

    @Override
    public Product createProduct(String packageName, Product product) {
        try {
            AndroidPublisher androidPublisher = googlePlayConfig.getAndroidPublisher(packageName);
            InAppProduct inAppProduct = new InAppProduct();
            inAppProduct.setPackageName(packageName);
            inAppProduct.setSku(product.getProductId());


            GooglePlayConfig.AppConfig appConfig = googlePlayConfig.getAppConfig(packageName);
            
            // 从配置获取默认语言
            String defaultLanguage = googlePlayConfig.getDefaultLanguage(packageName);
            inAppProduct.setDefaultLanguage(defaultLanguage);
            
            // 从配置获取支持的语言列表
            Map<String, Map<String, String>> configs = googlePlayConfig.getLanguage().getConfigs();

            // 创建多语言列表
            Map<String, InAppProductListing> listings = new HashMap<>();

            // 为每种支持的语言创建列表
            for (String languageCode : configs.keySet()) {
                InAppProductListing listing = new InAppProductListing();
                
                if (defaultLanguage.equals(languageCode)) {
                    // 默认语言使用原始名称和描述
                    listing.setTitle(product.getName());
                    listing.setDescription(product.getDescription());
                } else {
                    // 其他语言使用转换后的名称和描述
                    String translatedTitle = translateTitle(product.getType(), languageCode);
                    String translatedDescription = translateDescription(product.getType(), languageCode);
                    listing.setTitle(translatedTitle);
                    listing.setDescription(translatedDescription);
                }
                
                listings.put(languageCode, listing);
            }
            
            inAppProduct.setListings(listings);
            
            // 设置价格 - 支持多地区价格配置
            // 获取币种映射
            Map<String, Price> prices = new HashMap<>();
            for (String languageCode : configs.keySet()) {
                Price price = new Price();
                price.setPriceMicros(product.getDefaultPrice().multiply(new BigDecimal("1000000")).toBigInteger().toString());
                price.setCurrency(configs.get(languageCode).get("currency"));
                prices.put(configs.get(languageCode).get("region_code"), price);
            }

            inAppProduct.setPrices(prices);
            inAppProduct.setDefaultPrice(prices.get(appConfig.getDefaultRegionCode()));

            // 设置产品类型
            inAppProduct.setPurchaseType(convertToPurchaseType(product.getType()));
            // 设置产品状态 - Google Play API要求必填
            String status = product.getStatus();
            if (status == null || status.trim().isEmpty()) {
                status = Product.STATUS_INACTIVE; // 新创建的产品默认为非活跃状态
            }
            inAppProduct.setStatus(status);
            AndroidPublisher.Inappproducts.Insert request = androidPublisher.inappproducts().insert(packageName, inAppProduct);
            InAppProduct createdProduct = request.execute();
            log.info("创建产品成功: {} - {}, {}", packageName, createdProduct.getSku(), product.getProductId());
            return convertToProduct(createdProduct, packageName);
        } catch (IOException e) {
            log.error("创建产品失败: {}", e.getMessage(), e);
            throw new RuntimeException("创建产品失败", e);
        }
    }

    @Override
    public Product updateProduct(String packageName, String productId, Product product) {
        try {
            AndroidPublisher androidPublisher = googlePlayConfig.getAndroidPublisher(packageName);
            InAppProduct inAppProduct = new InAppProduct();
            inAppProduct.setPackageName(packageName);
            inAppProduct.setSku(productId);

            GooglePlayConfig.AppConfig appConfig = googlePlayConfig.getAppConfig(packageName);
            // 从配置获取支持的语言列表
            Map<String, Map<String, String>> configs = googlePlayConfig.getLanguage().getConfigs();


            // 从配置获取默认语言
            String defaultLanguage = googlePlayConfig.getDefaultLanguage(packageName);
            
            // 更新产品信息 - 支持多语言
            Map<String, InAppProductListing> listings = new HashMap<>();

            // 为每种支持的语言创建列表
            for (String languageCode : configs.keySet()) {
                InAppProductListing listing = new InAppProductListing();

                if (defaultLanguage.equals(languageCode)) {
                    // 默认语言使用原始名称和描述
                    listing.setTitle(product.getName());
                    listing.setDescription(product.getDescription());
                } else {
                    // 其他语言使用转换后的名称和描述
                    String translatedTitle = translateTitle(product.getType(), languageCode);
                    String translatedDescription = translateDescription(product.getType(), languageCode);
                    listing.setTitle(translatedTitle);
                    listing.setDescription(translatedDescription);
                }

                listings.put(languageCode, listing);
            }

            inAppProduct.setListings(listings);

            // 设置价格 - 支持多地区价格配置
            // 获取币种映射
            Map<String, Price> prices = new HashMap<>();
            for (String languageCode : configs.keySet()) {
                Price price = new Price();
                price.setPriceMicros(product.getDefaultPrice().multiply(new BigDecimal("1000000")).toBigInteger().toString());
                price.setCurrency(configs.get(languageCode).get("currency"));
                prices.put(configs.get(languageCode).get("region_code"), price);
            }

            inAppProduct.setPrices(prices);
            inAppProduct.setDefaultPrice(prices.get(appConfig.getDefaultRegionCode()));

            // 设置产品类型
            inAppProduct.setPurchaseType(convertToPurchaseType(product.getType()));

            // 更新产品类型
            if (product.getType() != null) {
                inAppProduct.setPurchaseType(convertToPurchaseType(product.getType()));
            }
            // 更新产品状态
            if (product.getStatus() != null) {
                inAppProduct.setStatus(product.getStatus());
            }
            AndroidPublisher.Inappproducts.Update request = androidPublisher.inappproducts().update(packageName, productId, inAppProduct);
            InAppProduct updatedProduct = request.execute();
            log.info("产品更新成功: {} - {}", packageName, productId);
            return convertToProduct(updatedProduct, packageName);
        } catch (IOException e) {
            log.error("更新产品失败: {}", e.getMessage(), e);
            throw new RuntimeException("更新产品失败", e);
        }
    }

    @Override
    public void deleteProduct(String packageName, String productId) {
        try {
            AndroidPublisher androidPublisher = googlePlayConfig.getAndroidPublisher(packageName);
            AndroidPublisher.Inappproducts.Delete request = androidPublisher.inappproducts().delete(packageName, productId);
            request.execute();
            log.info("产品删除成功: {} - {}", packageName, productId);
        } catch (IOException e) {
            log.error("删除产品失败: {}", e.getMessage(), e);
            throw new RuntimeException("删除产品失败", e);
        }
    }

    // ==================== 订单管理 ====================

    @Override
    public Purchase verifyPurchase(String packageName, String productId, String purchaseToken) {
        try {
            AndroidPublisher androidPublisher = googlePlayConfig.getAndroidPublisher(packageName);
            AndroidPublisher.Purchases.Products.Get request = androidPublisher.purchases().products().get(packageName, productId, purchaseToken);
            ProductPurchase productPurchase = request.execute();
            log.info("订单验证结果-原始: {}", JSONObject.toJSONString(productPurchase));
            return convertToPurchase(productPurchase, packageName, productId);
        } catch (IOException e) {
            log.error("验证购买失败: {}", e.getMessage(), e);
            throw new RuntimeException("验证购买失败", e);
        }
    }

    @Override
    public void acknowledgePurchase(String packageName, String productId, String purchaseToken) {
        try {
            AndroidPublisher androidPublisher = googlePlayConfig.getAndroidPublisher(packageName);
            // 新版acknowledge直接传null body即可
            AndroidPublisher.Purchases.Products.Acknowledge request = androidPublisher.purchases().products().acknowledge(packageName, productId, purchaseToken, null);
            request.execute();
            log.info("购买确认成功: {} - {} - {}", packageName, productId, purchaseToken);
        } catch (IOException e) {
            String errorMessage = e.getMessage();
            log.error("确认购买失败: {} - {} - {} - 错误: {}", packageName, productId, purchaseToken, errorMessage, e);
            
            // 根据错误类型提供更具体的错误信息
            if (errorMessage != null) {
                if (errorMessage.contains("productNotOwnedByUser")) {
                    throw new RuntimeException("购买确认失败: 该购买不属于当前用户，请检查服务账号权限或购买令牌是否有效", e);
                } else if (errorMessage.contains("400")) {
                    throw new RuntimeException("购买确认失败: 请求参数错误，请检查产品ID和购买令牌", e);
                } else if (errorMessage.contains("401")) {
                    throw new RuntimeException("购买确认失败: 认证失败，请检查服务账号密钥和权限", e);
                } else if (errorMessage.contains("403")) {
                    throw new RuntimeException("购买确认失败: 权限不足，请检查服务账号是否有足够的权限", e);
                } else if (errorMessage.contains("404")) {
                    throw new RuntimeException("购买确认失败: 购买记录不存在，请检查产品ID和购买令牌", e);
                }
            }
            
            throw new RuntimeException("确认购买失败: " + errorMessage, e);
        }
    }

    @Override
    public void consumePurchase(String packageName, String productId, String purchaseToken) {
        try {
            AndroidPublisher androidPublisher = googlePlayConfig.getAndroidPublisher(packageName);
            // 新版consume直接传null body即可
            AndroidPublisher.Purchases.Products.Consume request = androidPublisher.purchases().products().consume(packageName, productId, purchaseToken);
            request.execute();
            log.info("购买消费成功: {} - {} - {}", packageName, productId, purchaseToken);
        } catch (IOException e) {
            log.error("消费购买失败: {}", e.getMessage(), e);
            throw new RuntimeException("消费购买失败", e);
        }
    }

    @Override
    public List<Purchase> getUserPurchases(String packageName, String userId) {
        // 新版API不再支持通过userId直接查历史，需自行实现或跳过
        log.warn("新版API不支持通过userId直接查历史购买");
        return Collections.emptyList();
    }

    @Override
    public Purchase getOrder(String packageName, String orderId) {
        log.warn("Google Play API 不直接支持通过订单ID查询订单");
        throw new UnsupportedOperationException("Google Play API 不直接支持通过订单ID查询订单");
    }

    // ==================== 订阅管理 ====================

    @Override
    public void cancelSubscription(String packageName, String productId, String purchaseToken) {
        try {
            AndroidPublisher androidPublisher = googlePlayConfig.getAndroidPublisher(packageName);
            // 新版cancel直接传null body即可
            AndroidPublisher.Purchases.Subscriptions.Cancel request = androidPublisher.purchases().subscriptions().cancel(packageName, productId, purchaseToken);
            request.execute();
            log.info("订阅取消成功: {} - {} - {}", packageName, productId, purchaseToken);
        } catch (IOException e) {
            log.error("取消订阅失败: {}", e.getMessage(), e);
            throw new RuntimeException("取消订阅失败", e);
        }
    }

    @Override
    public void restoreSubscription(String packageName, String productId, String purchaseToken) {
        log.warn("新版API不支持直接恢复订阅，请在客户端重新发起订阅");
        throw new UnsupportedOperationException("新版API不支持直接恢复订阅");
    }

    @Override
    public void extendSubscription(String packageName, String productId, String purchaseToken, long extensionTimeInSeconds) {
        try {
            AndroidPublisher androidPublisher = googlePlayConfig.getAndroidPublisher(packageName);
            SubscriptionPurchasesDeferRequest deferRequest = new SubscriptionPurchasesDeferRequest();
            SubscriptionDeferralInfo deferralInfo = new SubscriptionDeferralInfo();
            long now = System.currentTimeMillis();
            deferralInfo.setDesiredExpiryTimeMillis(now + extensionTimeInSeconds * 1000);
            deferRequest.setDeferralInfo(deferralInfo);
            AndroidPublisher.Purchases.Subscriptions.Defer request = androidPublisher.purchases().subscriptions().defer(packageName, productId, purchaseToken, deferRequest);
            request.execute();
            log.info("订阅延期成功: {} - {} - {}", packageName, productId, purchaseToken);
        } catch (IOException e) {
            log.error("延期订阅失败: {}", e.getMessage(), e);
            throw new RuntimeException("延期订阅失败", e);
        }
    }

    @Override
    public List<Purchase> getActiveSubscriptions(String packageName, String userId) {
        log.warn("新版API不支持通过userId直接查活跃订阅");
        return Collections.emptyList();
    }

    // ==================== 退款管理 ====================

    @Override
    public void refundOrder(String packageName, String orderId, String reason) {
        log.warn("Google Play 退款需要通过 Google Play Console 手动处理");
        throw new UnsupportedOperationException("Google Play 退款需要通过 Google Play Console 手动处理");
    }

    @Override
    public String getRefundStatus(String packageName, String orderId) {
        log.warn("Google Play 不提供退款状态查询 API");
        throw new UnsupportedOperationException("Google Play 不提供退款状态查询 API");
    }

    // ==================== 工具方法 ====================

    /**
     * 将字符串产品类型转换为Google Play API的PurchaseType枚举
     * 根据Google Play API v3文档：
     * - purchaseTypeUnspecified: 未指定的购买类型
     * - managedUser: 默认商品类型 - 一次性购买
     * - subscription: 具有周期性周期的应用内商品
     */
    private String convertToPurchaseType(String productType) {
        if (productType == null) {
            return "managedUser"; // 默认为一次性购买
        }
        
        switch (productType.toLowerCase()) {
            case Product.TYPE_INAPP:
            case "manageduser":
                return "managedUser";
            case Product.TYPE_SUBS:
            case "subscription":
                return "subscription";
            default:
                log.warn("未知的产品类型: {}, 使用默认类型 managedUser", productType);
                return "managedUser";
        }
    }

    /**
     * 将Google Play API的PurchaseType枚举转换为标准字符串格式
     */
    private String convertFromPurchaseType(String purchaseType) {
        if (purchaseType == null) {
            return Product.TYPE_INAPP; // 默认为一次性购买
        }
        
        switch (purchaseType) {
            case "managedUser":
                return Product.TYPE_INAPP;
            case "subscription":
                return Product.TYPE_SUBS;
            case "purchaseTypeUnspecified":
                return Product.TYPE_INAPP; // 默认为一次性购买
            default:
                log.warn("未知的PurchaseType: {}, 使用默认类型 inapp", purchaseType);
                return Product.TYPE_INAPP;
        }
    }

    private Product convertToProduct(InAppProduct inAppProduct, String packageName) {
        String name = null;
        String desc = null;
        
        // 获取默认语言
        String defaultLanguage = getDefaultLanguageForRegion(packageName);
        
        if (inAppProduct.getListings() != null && !inAppProduct.getListings().isEmpty()) {
            // 优先使用默认语言
            if (inAppProduct.getListings().get(defaultLanguage) != null) {
                name = inAppProduct.getListings().get(defaultLanguage).getTitle();
                desc = inAppProduct.getListings().get(defaultLanguage).getDescription();
            } else {
                // 如果没有默认语言，尝试其他语言
                for (Map.Entry<String, InAppProductListing> entry : inAppProduct.getListings().entrySet()) {
                    if (entry.getValue() != null) {
                        name = entry.getValue().getTitle();
                        desc = entry.getValue().getDescription();
                        break;
                    }
                }
            }
        }
        
        BigDecimal price = null;
        String currency = null;
        if (inAppProduct.getDefaultPrice() != null) {
            try {
                price = new BigDecimal(inAppProduct.getDefaultPrice().getPriceMicros()).divide(new BigDecimal("1000000"));
            } catch (Exception ignore) {}
            currency = inAppProduct.getDefaultPrice().getCurrency();
        }
        return Product.builder()
                .productId(inAppProduct.getSku())
                .name(name)
                .description(desc)
                .type(convertFromPurchaseType(inAppProduct.getPurchaseType()))
                .defaultPrice(price)
                .defaultPriceCurrencyCode(currency)
                .status(inAppProduct.getStatus())
                .packageName(packageName)
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .build();
    }

    private Purchase convertToPurchase(ProductPurchase productPurchase, String packageName, String productId) {
        // 添加调试日志
        log.debug("转换ProductPurchase: orderId={}, purchaseToken={}, purchaseState={}, consumptionState={}, acknowledgementState={}", 
                productPurchase.getOrderId(), 
                productPurchase.getPurchaseToken(), 
                productPurchase.getPurchaseState(), 
                productPurchase.getConsumptionState(), 
                productPurchase.getAcknowledgementState());
        
        return Purchase.builder()
                .orderId(productPurchase.getOrderId())
                .productId(productId)
                .packageName(packageName)
                .purchaseToken(productPurchase.getPurchaseToken()) // 确保正确保存
                .purchaseState(String.valueOf(productPurchase.getPurchaseState()))
                .consumptionState(String.valueOf(productPurchase.getConsumptionState()))
                .developerPayload(productPurchase.getDeveloperPayload())
                .purchaseTime(productPurchase.getPurchaseTimeMillis() != null
                        ? LocalDateTime.ofInstant(Instant.ofEpochMilli(productPurchase.getPurchaseTimeMillis()), ZoneId.systemDefault()) : null)
                .acknowledged(
                    productPurchase.getAcknowledgementState() != null && productPurchase.getAcknowledgementState() == 1
                )
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .rawData(JSONObject.parseObject(JSONObject.toJSONString(productPurchase)))
                .build();
    }

    /**
     * 根据应用包名获取默认语言
     * 香港地区应用使用 zh-HK，其他地区使用 en-US
     */
    private String getDefaultLanguageForRegion(String packageName) {
        return "zh-HK";
        // // 根据包名判断应用发布地区
        // if (packageName.contains("shorthub.tv") || packageName.contains("hk")) {
        //     // 香港地区应用使用繁体中文
        //     return "zh-HK";
        // } else if (packageName.contains("cn") || packageName.contains("china")) {
        //     // 中国大陆应用使用简体中文
        //     return "zh-CN";
        // } else {
        //     // 其他地区默认使用英文
        //     return "en-US";
        // }
    }

    /**
     * 翻译标题
     */
    private String translateTitle(String type, String languageCode) {
        if (type.equals("subs")) {
            return googlePlayConfig.getLanguage().getConfigs().get(languageCode).get("subscription");
        }
        return googlePlayConfig.getLanguage().getConfigs().get(languageCode).get("coin");
    }

    /**
     * 翻译描述
     */
    private String translateDescription(String type, String languageCode) {
        if (type.equals("subs")) {
            return googlePlayConfig.getLanguage().getConfigs().get(languageCode).get("subscription_desc");
        }
        return googlePlayConfig.getLanguage().getConfigs().get(languageCode).get("coin_desc");
    }
}