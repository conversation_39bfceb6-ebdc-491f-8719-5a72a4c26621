package tv.shorthub.googleplay.service;

import tv.shorthub.googleplay.model.Product;
import tv.shorthub.googleplay.model.Purchase;

import java.util.List;

/**
 * Google Play 服务接口
 */
public interface GooglePlayService {

    // ==================== 产品管理 ====================

    /**
     * 获取应用内产品列表
     * @param packageName 包名
     * @return 产品列表
     */
    List<Product> getProducts(String packageName);

    /**
     * 获取单个产品详情
     * @param packageName 包名
     * @param productId 产品ID
     * @return 产品详情
     */
    Product getProduct(String packageName, String productId);

    /**
     * 创建应用内产品
     * @param packageName 包名
     * @param product 产品信息
     * @return 创建的产品
     */
    Product createProduct(String packageName, Product product);

    /**
     * 更新应用内产品
     * @param packageName 包名
     * @param productId 产品ID
     * @param product 产品信息
     * @return 更新的产品
     */
    Product updateProduct(String packageName, String productId, Product product);

    /**
     * 删除应用内产品
     * @param packageName 包名
     * @param productId 产品ID
     */
    void deleteProduct(String packageName, String productId);

    // ==================== 订单管理 ====================

    /**
     * 验证购买
     * @param packageName 包名
     * @param productId 产品ID
     * @param purchaseToken 购买令牌
     * @return 购买信息
     */
    Purchase verifyPurchase(String packageName, String productId, String purchaseToken);

    /**
     * 确认购买
     * @param packageName 包名
     * @param productId 产品ID
     * @param purchaseToken 购买令牌
     */
    void acknowledgePurchase(String packageName, String productId, String purchaseToken);

    /**
     * 消费购买（一次性产品）
     * @param packageName 包名
     * @param productId 产品ID
     * @param purchaseToken 购买令牌
     */
    void consumePurchase(String packageName, String productId, String purchaseToken);

    /**
     * 查询用户购买历史
     * @param packageName 包名
     * @param userId 用户ID
     * @return 购买历史
     */
    List<Purchase> getUserPurchases(String packageName, String userId);

    /**
     * 查询订单详情
     * @param packageName 包名
     * @param orderId 订单ID
     * @return 订单详情
     */
    Purchase getOrder(String packageName, String orderId);

    // ==================== 订阅管理 ====================

    /**
     * 取消订阅
     * @param packageName 包名
     * @param productId 产品ID
     * @param purchaseToken 购买令牌
     */
    void cancelSubscription(String packageName, String productId, String purchaseToken);

    /**
     * 恢复订阅
     * @param packageName 包名
     * @param productId 产品ID
     * @param purchaseToken 购买令牌
     */
    void restoreSubscription(String packageName, String productId, String purchaseToken);

    /**
     * 延期订阅
     * @param packageName 包名
     * @param productId 产品ID
     * @param purchaseToken 购买令牌
     * @param extensionTimeInSeconds 延期时间（秒）
     */
    void extendSubscription(String packageName, String productId, String purchaseToken, long extensionTimeInSeconds);

    /**
     * 获取活跃订阅
     * @param packageName 包名
     * @param userId 用户ID
     * @return 活跃订阅列表
     */
    List<Purchase> getActiveSubscriptions(String packageName, String userId);

    // ==================== 退款管理 ====================

    /**
     * 申请退款
     * @param packageName 包名
     * @param orderId 订单ID
     * @param reason 退款原因
     */
    void refundOrder(String packageName, String orderId, String reason);

    /**
     * 查询退款状态
     * @param packageName 包名
     * @param orderId 订单ID
     * @return 退款状态
     */
    String getRefundStatus(String packageName, String orderId);
} 