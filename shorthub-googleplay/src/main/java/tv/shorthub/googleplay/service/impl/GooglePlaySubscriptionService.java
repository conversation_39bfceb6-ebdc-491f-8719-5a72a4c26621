package tv.shorthub.googleplay.service.impl;

import com.alibaba.fastjson2.JSONObject;
import com.google.api.services.androidpublisher.model.*;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.google.api.services.androidpublisher.AndroidPublisher;

import tv.shorthub.googleplay.config.GooglePlayConfig;
import tv.shorthub.googleplay.model.Purchase;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
@Slf4j
public class GooglePlaySubscriptionService {

    @Autowired
    private GooglePlayConfig googlePlayConfig;

    /**
     * 检查订阅产品是否存在
     */
    public boolean subscriptionExists(String packageName, String subscriptionId) {
        try {
            AndroidPublisher publisher = googlePlayConfig.getAndroidPublisher(packageName);
            publisher.monetization().subscriptions().get(packageName, subscriptionId).execute();
            return true;
        } catch (Exception e) {
            log.info("Subscription " + subscriptionId + " does not exist: " + e.getMessage());
            return false;
        }
    }

    /**
     * 获取所有订阅产品
     */
    public List<Subscription> getSubscriptions(String packageName) throws Exception {
        AndroidPublisher publisher = googlePlayConfig.getAndroidPublisher(packageName);
        return publisher.monetization().subscriptions().list(packageName).execute().getSubscriptions();
    }

    /**
     * 获取订阅的地区版本信息
     * 根据Google Play API要求，必须使用最新的regionsVersion
     */
    public String getSubscriptionRegionsVersion(String packageName, String subscriptionId) throws Exception {
        AndroidPublisher publisher = googlePlayConfig.getAndroidPublisher(packageName);

        try {
            // 获取现有订阅以确认其存在
            Subscription subscription = publisher.monetization().subscriptions().get(packageName, subscriptionId).execute();

            // 根据API错误信息，最新的regionsVersion是2025/01
            // Google Play API只接受最新版本，不允许使用旧版本
            String latestRegionsVersion = "2025/01";

            System.out.println("使用最新的regionsVersion: " + latestRegionsVersion);
            return latestRegionsVersion;

        } catch (Exception e) {
            System.err.println("获取订阅信息失败，使用最新regionsVersion: " + e.getMessage());
            // 如果获取失败，仍使用最新版本
            return "2025/01";
        }
    }

    /**
     * 价格转换结果类
     */
    public static class PriceConversionResult {
        private Map<String, Money> convertedPrices;
        private String regionVersion;
        
        public PriceConversionResult(Map<String, Money> convertedPrices, String regionVersion) {
            this.convertedPrices = convertedPrices;
            this.regionVersion = regionVersion;
        }
        
        public Map<String, Money> getConvertedPrices() { return convertedPrices; }
        public String getRegionVersion() { return regionVersion; }
    }

    /**
     * 使用Google Play API自动转换价格
     */
    private PriceConversionResult convertPricesForAllRegions(String packageName, Long usdPriceUnits, Integer usdPriceNanos) throws Exception {
        try {
            AndroidPublisher publisher = googlePlayConfig.getAndroidPublisher(packageName);
            
            // 创建基准USD价格
            Money basePrice = new Money();
            basePrice.setCurrencyCode("USD");
            basePrice.setUnits(usdPriceUnits);
            basePrice.setNanos(usdPriceNanos);
            
            log.info("尝试使用Google Play价格转换API，基准价格: USD {}.{}", 
                usdPriceUnits, usdPriceNanos);
            
            // 创建转换请求
            ConvertRegionPricesRequest convertRequest = new ConvertRegionPricesRequest();
            convertRequest.setPrice(basePrice);
            
            // 调用Google Play的价格转换API
            ConvertRegionPricesResponse convertResponse = publisher.monetization()
                .convertRegionPrices(packageName, convertRequest)
                .execute();
            
            log.info("转换结果: {}", JSONObject.toJSONString(convertResponse));
            
            // 提取地区版本
            String regionVersion = null;
            if (convertResponse.getRegionVersion() != null) {
                regionVersion = convertResponse.getRegionVersion().getVersion();
                log.info("从API响应中获取地区版本: {}", regionVersion);
            } else {
                regionVersion = "2025/01"; // 默认版本
                log.warn("API响应中没有地区版本信息，使用默认版本: {}", regionVersion);
            }
            
            // 提取转换后的价格
            Map<String, Money> convertedPrices = new HashMap<>();
            if (convertResponse != null && convertResponse.getConvertedRegionPrices() != null) {
                log.info("Google Play价格转换API调用成功，获取到 {} 个地区的价格", 
                    convertResponse.getConvertedRegionPrices().size());
                
                // 解析转换后的地区价格
                Map<String, ConvertedRegionPrice> regionPrices = convertResponse.getConvertedRegionPrices();
                for (Map.Entry<String, ConvertedRegionPrice> entry : regionPrices.entrySet()) {
                    String regionCode = entry.getKey();
                    ConvertedRegionPrice regionData = entry.getValue();
                    
                    if (regionData != null && regionData.getPrice() != null) {
                        Money regionPrice = regionData.getPrice();
                        convertedPrices.put(regionCode, regionPrice);
                        
                        log.info("解析地区 {} 价格: {}.{} {}", 
                            regionCode, regionPrice.getUnits(), regionPrice.getNanos(), 
                            regionPrice.getCurrencyCode());
                    }
                }
                
                log.info("成功解析 {} 个地区的价格", convertedPrices.size());
                return new PriceConversionResult(convertedPrices, regionVersion);
            }
            
            log.warn("API响应为空或无转换价格数据");
            return new PriceConversionResult(new HashMap<>(), regionVersion);
            
        } catch (Exception e) {
            log.warn("Google Play价格转换API调用失败: {}, 将使用回退方案", e.getMessage());
            return new PriceConversionResult(new HashMap<>(), "2025/01");
        }
    }

    /**
     * 基础计划创建结果类
     */
    public static class BasePlanCreationResult {
        private BasePlan basePlan;
        private String regionVersion;
        
        public BasePlanCreationResult(BasePlan basePlan, String regionVersion) {
            this.basePlan = basePlan;
            this.regionVersion = regionVersion;
        }
        
        public BasePlan getBasePlan() { return basePlan; }
        public String getRegionVersion() { return regionVersion; }
    }

    /**
     * 创建基础计划
     *
     * @param packageName 应用包名
     * @param basePlanId 基础计划ID
     * @param billingPeriodDuration 计费周期
     * @param gracePeriodDuration 宽限期
     * @param currencyCode 当前计价的货币单位
     * @param priceUnits USD价格单位（基准价格）
     * @param priceNanos USD价格纳秒（基准价格）
     */
    public BasePlanCreationResult builderBasePlanWithRegionVersion(
        String packageName,
        String basePlanId,
        String billingPeriodDuration,
        String gracePeriodDuration,
        String currencyCode,
        Long priceUnits,
        Integer priceNanos
        ) throws Exception {

            // 创建新的基础计划
            BasePlan newBasePlan = new BasePlan();
            newBasePlan.setBasePlanId(basePlanId);
            newBasePlan.setState("ACTIVE");

            AutoRenewingBasePlanType autoRenewingType = new AutoRenewingBasePlanType();
            autoRenewingType.setBillingPeriodDuration(billingPeriodDuration);
            autoRenewingType.setGracePeriodDuration(gracePeriodDuration);
            newBasePlan.setAutoRenewingBasePlanType(autoRenewingType);

            // 创建区域配置列表
            List<RegionalBasePlanConfig> regionalBasePlanConfigs = new ArrayList<>();

            // 尝试使用Google Play API自动转换价格
            PriceConversionResult priceConversionResult = convertPricesForAllRegions(packageName, priceUnits, priceNanos);
            String regionVersion = priceConversionResult.getRegionVersion(); // 使用API返回的版本
            
            if (priceConversionResult.getConvertedPrices() != null && !priceConversionResult.getConvertedPrices().isEmpty()) {
                // 使用API转换的价格，为所有支持的地区设置价格
                log.info("使用Google Play API转换的价格，为 {} 个地区设置价格", priceConversionResult.getConvertedPrices().size());
                
                for (Map.Entry<String, Money> entry : priceConversionResult.getConvertedPrices().entrySet()) {
                    String regionCode = entry.getKey();
                    Money convertedPrice = entry.getValue();
                    
                    RegionalBasePlanConfig regionalConfig = new RegionalBasePlanConfig();
                    regionalConfig.setRegionCode(regionCode);
                    regionalConfig.setPrice(convertedPrice);
                    regionalConfig.setNewSubscriberAvailability(true); // 关键：让该地区可用
                    regionalBasePlanConfigs.add(regionalConfig);
                    
                    log.info("设置地区 {} 价格: {}.{} {}", 
                        regionCode, convertedPrice.getUnits(), convertedPrice.getNanos(), 
                        convertedPrice.getCurrencyCode());
                }
            } else {
                log.info("没有获取到googleplay价格配置");
                throw new RuntimeException("同步价格异常");
            }

            newBasePlan.setRegionalConfigs(regionalBasePlanConfigs);
            log.info("创建基础计划完成，包含 {} 个地区配置，地区版本: {}", 
                regionalBasePlanConfigs.size(), regionVersion);

            return new BasePlanCreationResult(newBasePlan, regionVersion);
    }

    /**
     * 创建基础计划（兼容性方法）
     */
    public BasePlan builderBasePlan(
        String packageName,
        String basePlanId,
        String billingPeriodDuration,
        String gracePeriodDuration,
        String currencyCode,
        Long priceUnits,
        Integer priceNanos
        ) throws Exception {
        BasePlanCreationResult result = builderBasePlanWithRegionVersion(
            packageName, basePlanId, billingPeriodDuration, gracePeriodDuration, 
            currencyCode, priceUnits, priceNanos);
        return result.getBasePlan();
    }

    /**
     * 创建订阅产品
     * 注意：根据Google Play API文档，订阅产品可能需要在Google Play Console中先手动创建
     */
    public Subscription createSubscriptionBasePlan(String packageName, String subscriptionId, List<BasePlan> basePlans, List<SubscriptionListing> listings) throws Exception {

        // 获取现有订阅
        Subscription existingSubscription = getSubscription(packageName, subscriptionId);
        log.info("✅ 获取现有订阅成功: {}", existingSubscription.getProductId());

        // 尝试更新现有订阅（添加新的基础计划）
        log.info("\n🔧 尝试添加新的基础计划到现有订阅...");
        try {
            existingSubscription.setListings(listings);
            // 添加到现有订阅
            if (existingSubscription.getBasePlans() == null) {
                existingSubscription.setBasePlans(basePlans);
            } else {
                // 创建新列表并添加现有计划
                List<BasePlan> updatedBasePlans = new ArrayList<>(existingSubscription.getBasePlans());

                // 使用Map进行去重,以basePlanId为key
                Map<String, BasePlan> basePlanMap = new HashMap<>();

                // 先添加现有计划
                for (BasePlan plan : updatedBasePlans) {
                    basePlanMap.put(plan.getBasePlanId(), plan);
                }

                // 添加或更新新计划
                for (BasePlan newPlan : basePlans) {
                    basePlanMap.put(newPlan.getBasePlanId(), newPlan);
                }

                // 转换回List
                updatedBasePlans = new ArrayList<>(basePlanMap.values());
                existingSubscription.setBasePlans(updatedBasePlans);
            }


            // 尝试更新订阅
            AndroidPublisher publisher = googlePlayConfig.getAndroidPublisher(packageName);

            // 先尝试不指定regionsVersion，让API自动处理
            log.info("尝试不指定regionsVersion进行更新...");

            // 添加updateMask参数，指定要更新的字段
            String updateMask = "basePlans"; // 我们要更新基础计划

            String regionsVersion = getSubscriptionRegionsVersion(packageName, subscriptionId);
            log.info("使用地区版本: {}", regionsVersion);

            Subscription updatedSubscription = publisher.monetization().subscriptions()
                    .patch(packageName, subscriptionId, existingSubscription)
                    .setUpdateMask(updateMask)
                    .setRegionsVersionVersion(regionsVersion)
                    .execute();

            log.info("✅ 指定regionsVersion更新成功: {}", updatedSubscription.getProductId());
            log.info("基础计划数量: {}", (updatedSubscription.getBasePlans() != null ? updatedSubscription.getBasePlans().size() : 0));
            return existingSubscription;
        } catch (Exception e) {
            log.error("❌ 更新订阅失败: {}", e.getMessage(), e);
            if (e instanceof com.google.api.client.googleapis.json.GoogleJsonResponseException) {
                com.google.api.client.googleapis.json.GoogleJsonResponseException gjre =
                        (com.google.api.client.googleapis.json.GoogleJsonResponseException) e;
                log.error("Response code: {}", gjre.getStatusCode());
                log.error("Response body: {}", gjre.getDetails());
            }
        }

        return null;
    }

    /**
     * 获取单个订阅产品
     */
    public Subscription getSubscription(String packageName, String subscriptionId) throws Exception {
        AndroidPublisher publisher = googlePlayConfig.getAndroidPublisher(packageName);
        return publisher.monetization().subscriptions().get(packageName, subscriptionId).execute();
    }


    /**
     * 创建订阅产品
     *
     * @param packageName 应用包名
     * @param subscriptionId 订阅ID
     * @param basePlans 基础计划列表
     * @param listings 订阅描述列表
     * @return 创建的订阅产品
     */
    public Subscription createSubscription(String packageName, String subscriptionId, List<BasePlan> basePlans, List<SubscriptionListing> listings) throws Exception {
        AndroidPublisher publisher = googlePlayConfig.getAndroidPublisher(packageName);

        try {
            // 检查订阅是否已经存在
            if (subscriptionExists(packageName, subscriptionId)) {
                log.warn("⚠️ 订阅产品已存在，更新基础计划: {}", subscriptionId);
                return createSubscriptionBasePlan(packageName, subscriptionId, basePlans, listings);
            }

            log.info("🔧 开始创建新的订阅产品: {}", subscriptionId);

            // 创建新的订阅产品
            Subscription newSubscription = new Subscription();
            // 确保设置产品ID
            newSubscription.setProductId(subscriptionId);
            newSubscription.setListings(listings);

            // 设置基础计划
            if (basePlans != null && !basePlans.isEmpty()) {
                newSubscription.setBasePlans(basePlans);
                log.info("设置基础计划数量: {}", basePlans.size());
                
                // 记录基础计划的地区配置信息
                for (BasePlan basePlan : basePlans) {
                    if (basePlan.getRegionalConfigs() != null) {
                        log.info("基础计划 {} 包含 {} 个地区配置", 
                            basePlan.getBasePlanId(), basePlan.getRegionalConfigs().size());
                    }
                }
            } else {
                log.warn("⚠️ 没有提供基础计划，可能导致创建失败");
            }

            // 获取地区版本 - 使用最新的版本
            String regionsVersion = "2025/01";
            log.info("使用地区版本: {}", regionsVersion);

            // 打印调试信息
            log.info("准备创建订阅 - Package: {}, ProductId: {}", 
                packageName, newSubscription.getProductId());

            // 创建订阅产品 - 修复：添加productId查询参数
            Subscription createdSubscription = publisher.monetization().subscriptions()
                    .create(packageName, newSubscription)
                    .setProductId(subscriptionId)  // 添加productId查询参数
                    .setRegionsVersionVersion(regionsVersion)
                    .execute();

            log.info("✅ 订阅产品创建成功: {}", createdSubscription.getProductId());
            log.info("基础计划数量: {}", (createdSubscription.getBasePlans() != null ? createdSubscription.getBasePlans().size() : 0));

            return createdSubscription;

        } catch (Exception e) {
            log.error("❌ 创建订阅产品失败: {}", e.getMessage(), e);
            if (e instanceof com.google.api.client.googleapis.json.GoogleJsonResponseException) {
                com.google.api.client.googleapis.json.GoogleJsonResponseException gjre =
                        (com.google.api.client.googleapis.json.GoogleJsonResponseException) e;
                log.error("Response code: {}", gjre.getStatusCode());
                log.error("Response body: {}", gjre.getDetails());
            }
            throw e;
        }
    }

    /**
     * 激活基础计划
     *
     * @param packageName 应用包名
     * @param subscriptionId 订阅ID
     * @param basePlanId 基础计划ID
     * @return 是否激活成功
     */
    public boolean activateBasePlan(String packageName, String subscriptionId, String basePlanId) throws Exception {
        AndroidPublisher publisher = googlePlayConfig.getAndroidPublisher(packageName);

        try {
            log.info("🔧 激活基础计划: {} - {}", subscriptionId, basePlanId);

            // 获取地区版本
            String regionsVersion = getSubscriptionRegionsVersion(packageName, subscriptionId);

            // 创建激活请求 (通常为空请求体)
            com.google.api.services.androidpublisher.model.ActivateBasePlanRequest activateRequest =
                new com.google.api.services.androidpublisher.model.ActivateBasePlanRequest();

            publisher.monetization().subscriptions().basePlans()
                    .activate(packageName, subscriptionId, basePlanId, activateRequest)
                    .execute();

            log.info("✅ 基础计划激活成功: {} - {}", subscriptionId, basePlanId);
            return true;

        } catch (Exception e) {
            log.error("❌ 激活基础计划失败: {} - {} - {}", subscriptionId, basePlanId, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 批量激活基础计划
     *
     * @param packageName 应用包名
     * @param subscriptionId 订阅ID
     * @param basePlanIds 基础计划ID列表
     * @return 激活成功的数量
     */
    public int activateBasePlans(String packageName, String subscriptionId, List<String> basePlanIds) throws Exception {
        int successCount = 0;

        for (String basePlanId : basePlanIds) {
            try {
                activateBasePlan(packageName, subscriptionId, basePlanId);
                successCount++;
            } catch (Exception e) {
                log.error("❌ 激活基础计划失败: {} - {}", subscriptionId, basePlanId);
                // 继续激活其他计划
            }
        }

        log.info("批量激活基础计划完成: 成功 {} / 总计 {}", successCount, basePlanIds.size());
        return successCount;
    }

    /**
     * 创建订阅产品并自动激活基础计划
     *
     * @param packageName 应用包名
     * @param subscriptionId 订阅ID
     * @param basePlans 基础计划列表
     * @param listings 订阅描述列表
     * @param autoActivate 是否自动激活基础计划
     * @return 创建的订阅产品
     */
    public Subscription createSubscriptionAndActivate(
            String packageName,
            String subscriptionId,
            List<BasePlan> basePlans,
            List<SubscriptionListing> listings,
            boolean autoActivate) throws Exception {

        // 先创建订阅产品
        Subscription subscription = createSubscription(packageName, subscriptionId, basePlans, listings);

        if (autoActivate && basePlans != null && !basePlans.isEmpty()) {
            log.info("🔧 开始自动激活基础计划...");

            // 提取基础计划ID列表
            List<String> basePlanIds = basePlans.stream()
                    .map(BasePlan::getBasePlanId)
                    .collect(java.util.stream.Collectors.toList());

            // 激活所有基础计划
            activateBasePlans(packageName, subscriptionId, basePlanIds);
        }

        return subscription;
    }

    /**
     * 创建订阅产品(带默认基础计划)并自动激活
     *
     * @param packageName 应用包名
     * @param subscriptionId 订阅ID
     * @param basePlanId 基础计划ID
     * @param billingPeriodDuration 计费周期(如: P1M表示1个月, P1Y表示1年)
     * @param gracePeriodDuration 宽限期(如: P3D表示3天)
     * @param priceUnits 价格单位
     * @param priceNanos 价格纳秒
     * @param title 订阅标题
     * @param description 订阅描述
     * @param languageCode 语言代码 (如: zh-CN, en-US)
     * @param autoActivate 是否自动激活基础计划
     * @return 创建的订阅产品
     */
    public Subscription createSubscriptionWithDefaultPlanAndActivate(
            String packageName,
            String subscriptionId,
            String basePlanId,
            String billingPeriodDuration,
            String gracePeriodDuration,
            Long priceUnits,
            Integer priceNanos,
            String title,
            String description,
            String languageCode,
            boolean autoActivate) throws Exception {

        log.info("🔧 创建订阅产品及默认基础计划: {} - {}", subscriptionId, basePlanId);

        // 创建基础计划
        BasePlan basePlan = builderBasePlan(
            packageName,
            basePlanId,
            billingPeriodDuration,
            gracePeriodDuration,
            "USD",
            priceUnits,
            priceNanos
        );

        List<BasePlan> basePlans = Arrays.asList(basePlan);

        // 创建订阅描述
        SubscriptionListing listing = new SubscriptionListing();
        listing.setLanguageCode(languageCode);
        listing.setTitle(title);
        listing.setDescription(description);
        List<SubscriptionListing> listings = Arrays.asList(listing);

        // 创建订阅产品并自动激活
        return createSubscriptionAndActivate(packageName, subscriptionId, basePlans, listings, autoActivate);
    }

    /**
     * 创建订阅优惠（使用固定价格）
     *
     * @param packageName 应用包名
     * @param subscriptionId 订阅ID
     * @param basePlanId 基础计划ID
     * @param offerId 优惠ID
     * @param freeTrialDays 免费试用天数（0表示无免费试用）
     * @param discountDuration 折扣持续时间（ISO 8601格式：P1D=1天, P1W=1周, P1M=1个月等）
     * @param discountedPriceUnits 优惠后价格（整数部分）
     * @param discountedPriceNanos 优惠后价格（纳秒部分）
     * @param originalPriceUnits 原价格（整数部分，用于日志）
     * @param originalPriceNanos 原价格（纳秒部分，用于日志）
     * @return 创建的优惠
     */
    public boolean createSubscriptionOfferWithFixedPrice(
            String packageName,
            String subscriptionId,
            String basePlanId,
            String offerId,
            int freeTrialDays,
            String discountDuration,
            Long discountedPriceUnits,
            Integer discountedPriceNanos,
            Long originalPriceUnits,
            Integer originalPriceNanos) throws Exception {

        AndroidPublisher publisher = googlePlayConfig.getAndroidPublisher(packageName);

        try {
            BigDecimal originalPrice = new BigDecimal(originalPriceUnits).add(new BigDecimal(originalPriceNanos).divide(new BigDecimal("1000000000")));
            BigDecimal discountedPrice = new BigDecimal(discountedPriceUnits).add(new BigDecimal(discountedPriceNanos).divide(new BigDecimal("1000000000")));

            log.info("🔧 创建订阅优惠: {} - {} - {}, 免费试用={}天, 原价={}元, 优惠后={}元, 优惠期={}",
                subscriptionId, basePlanId, offerId, freeTrialDays, originalPrice, discountedPrice, discountDuration);

            // 创建优惠对象
            com.google.api.services.androidpublisher.model.SubscriptionOffer offer =
                new com.google.api.services.androidpublisher.model.SubscriptionOffer();
            offer.setOfferId(offerId);

            // 设置优惠标签（可选）
            List<com.google.api.services.androidpublisher.model.OfferTag> offerTags = new ArrayList<>();
            com.google.api.services.androidpublisher.model.OfferTag tag =
                new com.google.api.services.androidpublisher.model.OfferTag();
            tag.setTag("introductory_offer");
            offerTags.add(tag);
            offer.setOfferTags(offerTags);

            // 设置地区配置
            List<com.google.api.services.androidpublisher.model.RegionalSubscriptionOfferConfig> regionalConfigs = new ArrayList<>();
            Map<String, Map<String, String>> configs = googlePlayConfig.getLanguage().getConfigs();
            for (String language : configs.keySet()) {
                String regionCode = configs.get(language).get("region_code");
                com.google.api.services.androidpublisher.model.RegionalSubscriptionOfferConfig regionalConfig =
                    new com.google.api.services.androidpublisher.model.RegionalSubscriptionOfferConfig();
                regionalConfig.setRegionCode(regionCode);
                regionalConfig.setNewSubscriberAvailability(true); // 对新订阅者可用
                regionalConfigs.add(regionalConfig);
            }
            offer.setRegionalConfigs(regionalConfigs);

            // 设置优惠阶段
            List<com.google.api.services.androidpublisher.model.SubscriptionOfferPhase> phases = createOfferPhasesWithFixedPrice(
                freeTrialDays, discountDuration, discountedPriceUnits, discountedPriceNanos);
            if (!phases.isEmpty()) {
                offer.setPhases(phases);
            }

            // 获取地区版本
            String regionsVersion = getSubscriptionRegionsVersion(packageName, subscriptionId);

            // 创建优惠
            com.google.api.services.androidpublisher.model.SubscriptionOffer createdOffer =
                publisher.monetization().subscriptions().basePlans().offers()
                    .create(packageName, subscriptionId, basePlanId, offer)
                    .setRegionsVersionVersion(regionsVersion)
                    .execute();

            log.info("✅ 订阅优惠创建成功: {} - {} - {}", subscriptionId, basePlanId, offerId);
            return true;

        } catch (Exception e) {
            log.error("❌ 创建订阅优惠失败: {} - {} - {} - {}", subscriptionId, basePlanId, offerId, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 创建优惠阶段（使用固定价格）
     */
    private List<com.google.api.services.androidpublisher.model.SubscriptionOfferPhase> createOfferPhasesWithFixedPrice(
            int freeTrialDays, String discountDuration, Long discountedPriceUnits, Integer discountedPriceNanos) {

        List<com.google.api.services.androidpublisher.model.SubscriptionOfferPhase> phases = new ArrayList<>();

        // 第一阶段：免费试用（如果有）
        if (freeTrialDays > 0) {
            com.google.api.services.androidpublisher.model.SubscriptionOfferPhase freeTrialPhase =
                new com.google.api.services.androidpublisher.model.SubscriptionOfferPhase();
            freeTrialPhase.setDuration("P" + freeTrialDays + "D"); // 如P7D表示7天
            freeTrialPhase.setRecurrenceCount(1);

            // 设置免费试用的地区配置
            List<com.google.api.services.androidpublisher.model.RegionalSubscriptionOfferPhaseConfig> regionalConfigs = new ArrayList<>();
            Map<String, Map<String, String>> configs = googlePlayConfig.getLanguage().getConfigs();
            for (String language : configs.keySet()) {
                String regionCode = configs.get(language).get("region_code");
                com.google.api.services.androidpublisher.model.RegionalSubscriptionOfferPhaseConfig regionalConfig =
                    new com.google.api.services.androidpublisher.model.RegionalSubscriptionOfferPhaseConfig();
                regionalConfig.setRegionCode(regionCode);
                // 设置为免费
                com.google.api.services.androidpublisher.model.RegionalSubscriptionOfferPhaseFreePriceOverride freePrice =
                    new com.google.api.services.androidpublisher.model.RegionalSubscriptionOfferPhaseFreePriceOverride();
                regionalConfig.setFree(freePrice);
                regionalConfigs.add(regionalConfig);
            }
            freeTrialPhase.setRegionalConfigs(regionalConfigs);

            phases.add(freeTrialPhase);
            log.info("添加免费试用阶段: {}天", freeTrialDays);
        }

        // 第二阶段：固定优惠价格
        com.google.api.services.androidpublisher.model.SubscriptionOfferPhase discountPhase =
            new com.google.api.services.androidpublisher.model.SubscriptionOfferPhase();
        discountPhase.setDuration(discountDuration);
        discountPhase.setRecurrenceCount(1); // 只在第一个计费周期有效

        // 设置固定价格的地区配置
        List<com.google.api.services.androidpublisher.model.RegionalSubscriptionOfferPhaseConfig> regionalConfigs = new ArrayList<>();
        Map<String, Map<String, String>> configs = googlePlayConfig.getLanguage().getConfigs();
        for (String language : configs.keySet()) {
            String regionCode = configs.get(language).get("region_code");
            String currency = configs.get(language).get("currency");

            com.google.api.services.androidpublisher.model.RegionalSubscriptionOfferPhaseConfig regionalConfig =
                new com.google.api.services.androidpublisher.model.RegionalSubscriptionOfferPhaseConfig();
            regionalConfig.setRegionCode(regionCode);

            // 设置固定价格
            com.google.api.services.androidpublisher.model.Money discountPrice =
                new com.google.api.services.androidpublisher.model.Money();
            discountPrice.setCurrencyCode(currency);
            discountPrice.setUnits(discountedPriceUnits);
            discountPrice.setNanos(discountedPriceNanos);
            regionalConfig.setPrice(discountPrice);

            regionalConfigs.add(regionalConfig);
        }
        discountPhase.setRegionalConfigs(regionalConfigs);

        phases.add(discountPhase);
        BigDecimal discountedPrice = new BigDecimal(discountedPriceUnits).add(new BigDecimal(discountedPriceNanos).divide(new BigDecimal("1000000000")));
        log.info("添加固定价格阶段: 价格={}元, 持续时间: {}", discountedPrice, discountDuration);

        return phases;
    }

    /**
     * 激活订阅优惠
     *
     * @param packageName 应用包名
     * @param subscriptionId 订阅ID
     * @param basePlanId 基础计划ID
     * @param offerId 优惠ID
     * @return 是否激活成功
     */
    public boolean activateSubscriptionOffer(String packageName, String subscriptionId, String basePlanId, String offerId) throws Exception {
        AndroidPublisher publisher = googlePlayConfig.getAndroidPublisher(packageName);

        try {
            log.info("🔧 激活订阅优惠: {} - {} - {}", subscriptionId, basePlanId, offerId);

            // 创建激活请求
            com.google.api.services.androidpublisher.model.ActivateSubscriptionOfferRequest activateRequest =
                new com.google.api.services.androidpublisher.model.ActivateSubscriptionOfferRequest();

            publisher.monetization().subscriptions().basePlans().offers()
                    .activate(packageName, subscriptionId, basePlanId, offerId, activateRequest)
                    .execute();

            log.info("✅ 订阅优惠激活成功: {} - {} - {}", subscriptionId, basePlanId, offerId);
            return true;

        } catch (Exception e) {
            log.error("❌ 激活订阅优惠失败: {} - {} - {} - {}", subscriptionId, basePlanId, offerId, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 创建带首期优惠的订阅产品
     *
     * @param packageName 应用包名
     * @param subscriptionId 订阅ID
     * @param basePlan 基础计划
     * @param listings 订阅描述
     * @param offerConfig 优惠配置
     * @param discountedPriceUnits 优惠后价格（整数部分）
     * @param discountedPriceNanos 优惠后价格（纳秒部分）
     * @param originalPriceUnits 原价格（整数部分）
     * @param originalPriceNanos 原价格（纳秒部分）
     * @return 创建的订阅产品
     */
    public com.google.api.services.androidpublisher.model.Subscription createSubscriptionWithIntroductoryOffer(
            String packageName,
            String subscriptionId,
            BasePlan basePlan,
            List<SubscriptionListing> listings,
            IntroductoryOfferConfig offerConfig,
            Long discountedPriceUnits,
            Integer discountedPriceNanos,
            Long originalPriceUnits,
            Integer originalPriceNanos) throws Exception {

        log.info("🔧 创建带首期优惠的订阅产品: {} - {}", subscriptionId, offerConfig.getOfferId());

        // 1. 创建订阅产品
        com.google.api.services.androidpublisher.model.Subscription subscription =
            createSubscription(packageName, subscriptionId, Arrays.asList(basePlan), listings);

        // 2. 激活基础计划
        activateBasePlan(packageName, subscriptionId, basePlan.getBasePlanId());

        // 3. 创建首期优惠 - 使用优惠后价格作为固定价格
        createSubscriptionOfferWithFixedPrice(
            packageName,
            subscriptionId,
            basePlan.getBasePlanId(),
            offerConfig.getOfferId(),
            offerConfig.getFreeTrialDays(),
            offerConfig.getDiscountDuration(),
            discountedPriceUnits,
            discountedPriceNanos,
            originalPriceUnits,
            originalPriceNanos
        );

        // 4. 激活优惠
        activateSubscriptionOffer(packageName, subscriptionId, basePlan.getBasePlanId(), offerConfig.getOfferId());

        log.info("✅ 带首期优惠的订阅产品创建完成: {}", subscriptionId);
        return subscription;
    }

    /**
     * 首期优惠配置类
     */
    public static class IntroductoryOfferConfig {
        private String offerId;
        private int freeTrialDays;           // 免费试用天数
        private int discountPercentage;      // 折扣百分比
        private String discountDuration;     // 折扣持续时间（ISO 8601格式：P1D=1天, P1W=1周, P1M=1个月, P3M=3个月, P1Y=1年）

        public IntroductoryOfferConfig(String offerId, int freeTrialDays, int discountPercentage, String discountDuration) {
            this.offerId = offerId;
            this.freeTrialDays = freeTrialDays;
            this.discountPercentage = discountPercentage;
            this.discountDuration = discountDuration;
        }

        // Getters
        public String getOfferId() { return offerId; }
        public int getFreeTrialDays() { return freeTrialDays; }
        public int getDiscountPercentage() { return discountPercentage; }
        public String getDiscountDuration() { return discountDuration; }
    }

    /**
     * 创建订阅优惠（首期优惠 - 百分比折扣）
     *
     * @param packageName 应用包名
     * @param subscriptionId 订阅ID
     * @param basePlanId 基础计划ID
     * @param offerId 优惠ID
     * @param freeTrialDays 免费试用天数（0表示无免费试用）
     * @param discountPercentage 折扣百分比（如50表示50%折扣，0表示无折扣）
     * @param discountDuration 折扣持续时间（ISO 8601格式：P1D=1天, P1W=1周, P1M=1个月等）
     * @param basePriceUnits 基础计划价格（整数部分）
     * @param basePriceNanos 基础计划价格（纳秒部分）
     * @return 创建的优惠
     */
    public boolean createSubscriptionOffer(
            String packageName,
            String subscriptionId,
            String basePlanId,
            String offerId,
            int freeTrialDays,
            int discountPercentage,
            String discountDuration,
            Long basePriceUnits,
            Integer basePriceNanos) throws Exception {

        AndroidPublisher publisher = googlePlayConfig.getAndroidPublisher(packageName);

        try {
            log.info("🔧 创建订阅优惠: {} - {} - {}, 免费试用={}天, 折扣={}%, 优惠期={}, 基础价格={}元",
                subscriptionId, basePlanId, offerId, freeTrialDays, discountPercentage, discountDuration,
                basePriceUnits + "." + (basePriceNanos / 1000000));

            // 创建优惠对象
            com.google.api.services.androidpublisher.model.SubscriptionOffer offer =
                new com.google.api.services.androidpublisher.model.SubscriptionOffer();
            offer.setOfferId(offerId);

            // 设置优惠标签（可选）
            List<com.google.api.services.androidpublisher.model.OfferTag> offerTags = new ArrayList<>();
            com.google.api.services.androidpublisher.model.OfferTag tag =
                new com.google.api.services.androidpublisher.model.OfferTag();
            tag.setTag("introductory_offer");
            offerTags.add(tag);
            offer.setOfferTags(offerTags);

            // 设置地区配置
            List<com.google.api.services.androidpublisher.model.RegionalSubscriptionOfferConfig> regionalConfigs = new ArrayList<>();
            Map<String, Map<String, String>> configs = googlePlayConfig.getLanguage().getConfigs();
            for (String language : configs.keySet()) {
                String regionCode = configs.get(language).get("region_code");
                com.google.api.services.androidpublisher.model.RegionalSubscriptionOfferConfig regionalConfig =
                    new com.google.api.services.androidpublisher.model.RegionalSubscriptionOfferConfig();
                regionalConfig.setRegionCode(regionCode);
                regionalConfig.setNewSubscriberAvailability(true); // 对新订阅者可用
                regionalConfigs.add(regionalConfig);
            }
            offer.setRegionalConfigs(regionalConfigs);

            // 设置优惠阶段
            List<com.google.api.services.androidpublisher.model.SubscriptionOfferPhase> phases = createOfferPhases(
                freeTrialDays, discountPercentage, discountDuration, basePriceUnits, basePriceNanos);
            if (!phases.isEmpty()) {
                offer.setPhases(phases);
            }

            // 获取地区版本
            String regionsVersion = getSubscriptionRegionsVersion(packageName, subscriptionId);

            // 创建优惠
            com.google.api.services.androidpublisher.model.SubscriptionOffer createdOffer =
                publisher.monetization().subscriptions().basePlans().offers()
                    .create(packageName, subscriptionId, basePlanId, offer)
                    .setRegionsVersionVersion(regionsVersion)
                    .execute();

            log.info("✅ 订阅优惠创建成功: {} - {} - {}", subscriptionId, basePlanId, offerId);
            return true;

        } catch (Exception e) {
            log.error("❌ 创建订阅优惠失败: {} - {} - {} - {}", subscriptionId, basePlanId, offerId, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 创建优惠阶段（百分比计算）
     */
    private List<com.google.api.services.androidpublisher.model.SubscriptionOfferPhase> createOfferPhases(
            int freeTrialDays, int discountPercentage, String discountDuration, Long basePriceUnits, Integer basePriceNanos) {

        List<com.google.api.services.androidpublisher.model.SubscriptionOfferPhase> phases = new ArrayList<>();

        // 第一阶段：免费试用（如果有）
        if (freeTrialDays > 0) {
            com.google.api.services.androidpublisher.model.SubscriptionOfferPhase freeTrialPhase =
                new com.google.api.services.androidpublisher.model.SubscriptionOfferPhase();
            freeTrialPhase.setDuration("P" + freeTrialDays + "D"); // 如P7D表示7天
            freeTrialPhase.setRecurrenceCount(1);

            // 设置免费试用的地区配置
            List<com.google.api.services.androidpublisher.model.RegionalSubscriptionOfferPhaseConfig> regionalConfigs = new ArrayList<>();
            Map<String, Map<String, String>> configs = googlePlayConfig.getLanguage().getConfigs();
            for (String language : configs.keySet()) {
                String regionCode = configs.get(language).get("region_code");
                com.google.api.services.androidpublisher.model.RegionalSubscriptionOfferPhaseConfig regionalConfig =
                    new com.google.api.services.androidpublisher.model.RegionalSubscriptionOfferPhaseConfig();
                regionalConfig.setRegionCode(regionCode);
                // 设置为免费
                com.google.api.services.androidpublisher.model.RegionalSubscriptionOfferPhaseFreePriceOverride freePrice =
                    new com.google.api.services.androidpublisher.model.RegionalSubscriptionOfferPhaseFreePriceOverride();
                regionalConfig.setFree(freePrice);
                regionalConfigs.add(regionalConfig);
            }
            freeTrialPhase.setRegionalConfigs(regionalConfigs);

            phases.add(freeTrialPhase);
            log.info("添加免费试用阶段: {}天", freeTrialDays);
        }

        // 第二阶段：折扣价格（如果有）
        if (discountPercentage > 0 && discountPercentage < 100) {
            com.google.api.services.androidpublisher.model.SubscriptionOfferPhase discountPhase =
                new com.google.api.services.androidpublisher.model.SubscriptionOfferPhase();
            discountPhase.setDuration(discountDuration);
            discountPhase.setRecurrenceCount(1); // 只在第一个计费周期有效

            // 计算折扣后的价格
            BigDecimal basePrice = new BigDecimal(basePriceUnits).add(new BigDecimal(basePriceNanos).divide(new BigDecimal("1000000000")));
            BigDecimal discountAmount = basePrice.multiply(new BigDecimal(discountPercentage)).divide(new BigDecimal("100"));
            BigDecimal discountedPrice = basePrice.subtract(discountAmount);

            // 分离整数和纳秒部分
            long discountedPriceUnits = discountedPrice.longValue();
            int discountedPriceNanos = discountedPrice.subtract(new BigDecimal(discountedPriceUnits))
                .multiply(new BigDecimal("1000000000")).intValue();

            // 设置折扣的地区配置
            List<com.google.api.services.androidpublisher.model.RegionalSubscriptionOfferPhaseConfig> regionalConfigs = new ArrayList<>();
            Map<String, Map<String, String>> configs = googlePlayConfig.getLanguage().getConfigs();
            for (String language : configs.keySet()) {
                String regionCode = configs.get(language).get("region_code");
                String currency = configs.get(language).get("currency");

                com.google.api.services.androidpublisher.model.RegionalSubscriptionOfferPhaseConfig regionalConfig =
                    new com.google.api.services.androidpublisher.model.RegionalSubscriptionOfferPhaseConfig();
                regionalConfig.setRegionCode(regionCode);

                // 设置固定价格
                com.google.api.services.androidpublisher.model.Money discountPrice =
                    new com.google.api.services.androidpublisher.model.Money();
                discountPrice.setCurrencyCode(currency);
                discountPrice.setUnits(discountedPriceUnits);
                discountPrice.setNanos(discountedPriceNanos);
                regionalConfig.setPrice(discountPrice);

                regionalConfigs.add(regionalConfig);
            }
            discountPhase.setRegionalConfigs(regionalConfigs);

            phases.add(discountPhase);
            log.info("添加折扣阶段: {}%折扣, 原价={}元, 折扣后价格={}元, 持续时间: {}",
                discountPercentage, basePrice, discountedPrice, discountDuration);
        }

        return phases;
    }

    /**
     * 检查订阅优惠是否存在
     */
    public boolean subscriptionOfferExists(String packageName, String subscriptionId, String basePlanId, String offerId) {
        try {
            AndroidPublisher publisher = googlePlayConfig.getAndroidPublisher(packageName);
            publisher.monetization().subscriptions().basePlans().offers()
                .get(packageName, subscriptionId, basePlanId, offerId)
                .execute();
            return true;
        } catch (Exception e) {
            log.info("Subscription offer " + offerId + " does not exist: " + e.getMessage());
            return false;
        }
    }

    /**
     * 更新订阅优惠
     */
    public boolean updateSubscriptionOffer(
            String packageName,
            String subscriptionId,
            String basePlanId,
            String offerId,
            int freeTrialDays,
            String discountDuration,
            BigDecimal usdDiscountedPrice,
            BasePlan basePlan) throws Exception {
        AndroidPublisher publisher = googlePlayConfig.getAndroidPublisher(packageName);
        try {
            log.info("🔧 更新订阅优惠: {} - {} - {}, 免费试用={}天, USD优惠后价格={}, 优惠期={}",
                subscriptionId, basePlanId, offerId, freeTrialDays, usdDiscountedPrice, discountDuration);

            // 创建优惠对象
            com.google.api.services.androidpublisher.model.SubscriptionOffer offer =
                new com.google.api.services.androidpublisher.model.SubscriptionOffer();
            offer.setOfferId(offerId);

            // 设置地区配置（所有基础计划地区）
            List<RegionalSubscriptionOfferConfig> regionalConfigs = getSubscriptionOfferConfigs(basePlan);
            offer.setRegionalConfigs(regionalConfigs);

            // 设置优惠阶段（固定金额）
            List<com.google.api.services.androidpublisher.model.SubscriptionOfferPhase> phases = new ArrayList<>();

            // 免费试用阶段（如有）
            if (freeTrialDays > 0) {
                SubscriptionOfferPhase freeTrialPhase = getFreeTrialPhase(freeTrialDays, basePlan);
                phases.add(freeTrialPhase);
            }

            // 固定金额优惠阶段
            com.google.api.services.androidpublisher.model.SubscriptionOfferPhase discountPhase =
                new com.google.api.services.androidpublisher.model.SubscriptionOfferPhase();
            discountPhase.setDuration(discountDuration);
            discountPhase.setRecurrenceCount(1); // 只在第一个计费周期有效
            List<com.google.api.services.androidpublisher.model.RegionalSubscriptionOfferPhaseConfig> discountRegionalConfigs = new ArrayList<>();

            // 先找到USD基准价
            BigDecimal usdBasePrice = getBasePrice(basePlan);

            // 统计哪些币种/地区只允许整数（所有该币种的nanos都为0）
            Map<String, Boolean> integerOnlyCurrencyMap = new HashMap<>();
            if (basePlan.getRegionalConfigs() != null) {
                Map<String, List<Integer>> currencyNanosMap = new HashMap<>();
                for (RegionalBasePlanConfig config : basePlan.getRegionalConfigs()) {
                    Money price = config.getPrice();
                    if (price == null) continue;
                    String currency = price.getCurrencyCode();
                    int nanos = price.getNanos() != null ? price.getNanos() : 0;
                    currencyNanosMap.computeIfAbsent(currency, k -> new ArrayList<>()).add(nanos);
                }
                for (Map.Entry<String, List<Integer>> entry : currencyNanosMap.entrySet()) {
                    boolean allZero = entry.getValue().stream().allMatch(n -> n == 0);
                    integerOnlyCurrencyMap.put(entry.getKey(), allZero);
                }
            }

            for (RegionalBasePlanConfig config : basePlan.getRegionalConfigs()) {
                String regionCode = config.getRegionCode();
                Money regionBasePrice = config.getPrice();
                if (regionBasePrice == null) continue;
                String currency = regionBasePrice.getCurrencyCode();
                BigDecimal localDiscountedPrice = getLocalDiscountedPrice(usdDiscountedPrice, regionBasePrice, usdBasePrice);

                long localUnits;
                int localNanos;
                // 动态判断：如果该币种所有基础价 nanos==0，视为整数币种
                if (integerOnlyCurrencyMap.getOrDefault(currency, false)) {
                    localUnits = localDiscountedPrice.setScale(0, RoundingMode.HALF_UP).longValue();
                    localNanos = 0;
                } else {
                    localUnits = localDiscountedPrice.longValue();
                    localNanos = localDiscountedPrice.subtract(new BigDecimal(localUnits))
                        .multiply(new BigDecimal("1000000000")).intValue();
                }

                RegionalSubscriptionOfferPhaseConfig regionalConfig =
                    new RegionalSubscriptionOfferPhaseConfig();
                regionalConfig.setRegionCode(regionCode);
                Money discountPrice =
                    new Money();
                discountPrice.setCurrencyCode(currency);
                discountPrice.setUnits(localUnits);
                discountPrice.setNanos(localNanos);
                regionalConfig.setPrice(discountPrice);
                discountRegionalConfigs.add(regionalConfig);
            }
            discountPhase.setRegionalConfigs(discountRegionalConfigs);
            phases.add(discountPhase);

            offer.setPhases(phases);

            // 获取地区版本
            String regionsVersion = getSubscriptionRegionsVersion(packageName, subscriptionId);

            // 更新优惠
            com.google.api.services.androidpublisher.model.SubscriptionOffer updatedOffer =
                publisher.monetization().subscriptions().basePlans().offers()
                    .patch(packageName, subscriptionId, basePlanId, offerId, offer)
                    .setRegionsVersionVersion(regionsVersion)
                    .setUpdateMask("phases,regionalConfigs") // 指定要更新的字段
                    .execute();

            log.info("✅ 订阅优惠更新成功: {} - {} - {}", subscriptionId, basePlanId, offerId);
            return true;

        } catch (Exception e) {
            log.error("❌ 更新订阅优惠失败: {} - {} - {} - {}", subscriptionId, basePlanId, offerId, e.getMessage(), e);
            throw e;
        }
    }

    private static List<RegionalSubscriptionOfferConfig> getSubscriptionOfferConfigs(BasePlan basePlan) {
        List<RegionalSubscriptionOfferConfig> regionalConfigs = new ArrayList<>();
        if (basePlan.getRegionalConfigs() != null) {
            for (RegionalBasePlanConfig config : basePlan.getRegionalConfigs()) {
                RegionalSubscriptionOfferConfig regionalConfig =
                    new RegionalSubscriptionOfferConfig();
                regionalConfig.setRegionCode(config.getRegionCode());
                regionalConfig.setNewSubscriberAvailability(true);
                regionalConfigs.add(regionalConfig);
            }
        }
        return regionalConfigs;
    }

    /**
     * 获取订阅优惠信息
     */
    public com.google.api.services.androidpublisher.model.SubscriptionOffer getSubscriptionOffer(
            String packageName, String subscriptionId, String basePlanId, String offerId) {
        try {
            AndroidPublisher publisher = googlePlayConfig.getAndroidPublisher(packageName);
            return publisher.monetization().subscriptions().basePlans().offers()
                .get(packageName, subscriptionId, basePlanId, offerId)
                .execute();
        } catch (Exception e) {
            log.warn("获取订阅优惠失败: {} - {} - {} - {}", subscriptionId, basePlanId, offerId, e.getMessage());
            return null;
        }
    }

    /**
     * 比较优惠价格是否相同
     */
    private boolean isOfferPriceSame(com.google.api.services.androidpublisher.model.SubscriptionOffer existingOffer, 
                                   BigDecimal newUsdDiscountedPrice, BasePlan basePlan) {
        try {
            if (existingOffer == null || existingOffer.getPhases() == null) {
                return false;
            }
            
            // 查找折扣阶段的价格，获取现有优惠的USD价格（从US地区获取）
            BigDecimal existingUsdPrice = null;
            for (com.google.api.services.androidpublisher.model.SubscriptionOfferPhase phase : existingOffer.getPhases()) {
                if (phase.getRegionalConfigs() != null) {
                    for (com.google.api.services.androidpublisher.model.RegionalSubscriptionOfferPhaseConfig config : phase.getRegionalConfigs()) {
                        if (config.getPrice() != null && "USD".equals(config.getPrice().getCurrencyCode()) && "US".equals(config.getRegionCode())) {
                            existingUsdPrice = new BigDecimal(config.getPrice().getUnits())
                                .add(new BigDecimal(config.getPrice().getNanos()).divide(new BigDecimal("1000000000")));
                            break;
                        }
                    }
                    if (existingUsdPrice != null) break;
                }
            }
            
            if (existingUsdPrice == null) {
                log.info("无法从现有优惠中获取US地区的USD价格");
                return false;
            }
            
            // 直接比较USD优惠价格（newUsdDiscountedPrice已经是USD优惠价格）
            BigDecimal difference = existingUsdPrice.subtract(newUsdDiscountedPrice).abs();
            boolean isSame = difference.compareTo(new BigDecimal("0.01")) <= 0;
            
            log.info("比较优惠价格: 现有US地区USD优惠价={}, 新USD优惠价={}, 差异={}, 是否相同={}", 
                existingUsdPrice, newUsdDiscountedPrice, difference, isSame);
            
            return isSame;
        } catch (Exception e) {
            log.error("比较优惠价格失败:", e);
            return false;
        }
    }

    /**
     * 创建订阅基础计划优惠（首次订阅固定金额优惠）
     *
     * @return 是否创建成功
     */
    public boolean createFixedPriceIntroOffer(
            String packageName,
            String subscriptionId,
            String basePlanId,
            String offerId,
            int freeTrialDays,
            String discountDuration,
            BigDecimal usdDiscountedPrice, // USD优惠金额
            BasePlan basePlan // 用于获取所有地区和本地币种价格
    ) throws Exception {
        AndroidPublisher publisher = googlePlayConfig.getAndroidPublisher(packageName);
        try {
            log.info("🔧 创建首次订阅固定金额优惠: {} - {} - {}, 免费试用={}天, USD优惠后价格={}, 优惠期={}",
                subscriptionId, basePlanId, offerId, freeTrialDays, usdDiscountedPrice, discountDuration);

            // 检查优惠是否已存在
            if (subscriptionOfferExists(packageName, subscriptionId, basePlanId, offerId)) {
                log.warn("⚠️ 订阅优惠已存在，检查价格是否有变化: {} - {} - {}", subscriptionId, basePlanId, offerId);
                
                // 获取现有优惠
                com.google.api.services.androidpublisher.model.SubscriptionOffer existingOffer = 
                    getSubscriptionOffer(packageName, subscriptionId, basePlanId, offerId);
                
                // 比较价格是否相同
                if (isOfferPriceSame(existingOffer, usdDiscountedPrice, basePlan)) {
                    log.info("✅ 优惠价格未变化，跳过更新: {} - {} - {}", subscriptionId, basePlanId, offerId);
                } else {
                    log.info("🔄 优惠价格有变化，开始更新: {} - {} - {}", subscriptionId, basePlanId, offerId);
                    updateSubscriptionOffer(packageName, subscriptionId, basePlanId, offerId,
                        freeTrialDays, discountDuration, usdDiscountedPrice, basePlan);
                }
                return true;
            }

            // 创建优惠对象
            com.google.api.services.androidpublisher.model.SubscriptionOffer offer =
                new com.google.api.services.androidpublisher.model.SubscriptionOffer();
            offer.setOfferId(offerId);

            // 设置优惠标签（可选）
//            List<com.google.api.services.androidpublisher.model.OfferTag> offerTags = new ArrayList<>();
//            com.google.api.services.androidpublisher.model.OfferTag tag =
//                new com.google.api.services.androidpublisher.model.OfferTag();
//            tag.setTag("introductory_offer");
//            offerTags.add(tag);
//            offer.setOfferTags(offerTags);

            // 设置地区配置（所有基础计划地区）
            List<RegionalSubscriptionOfferConfig> regionalConfigs = getSubscriptionOfferConfigs(basePlan);
            offer.setRegionalConfigs(regionalConfigs);

            // 设置优惠阶段（固定金额）
            List<com.google.api.services.androidpublisher.model.SubscriptionOfferPhase> phases = new ArrayList<>();

            // 免费试用阶段（如有）
            if (freeTrialDays > 0) {
                SubscriptionOfferPhase freeTrialPhase = getFreeTrialPhase(freeTrialDays, basePlan);
                phases.add(freeTrialPhase);
            }

            // 固定金额优惠阶段
            com.google.api.services.androidpublisher.model.SubscriptionOfferPhase discountPhase =
                new com.google.api.services.androidpublisher.model.SubscriptionOfferPhase();
            discountPhase.setDuration(discountDuration);
            discountPhase.setRecurrenceCount(1); // 只在第一个计费周期有效
            List<com.google.api.services.androidpublisher.model.RegionalSubscriptionOfferPhaseConfig> discountRegionalConfigs = new ArrayList<>();

            // 先找到USD基准价
            BigDecimal usdBasePrice = getBasePrice(basePlan);

            // 统计哪些币种/地区只允许整数（所有该币种的nanos都为0）
            Map<String, Boolean> integerOnlyCurrencyMap = new HashMap<>();
            Map<String, List<Integer>> currencyNanosMap = new HashMap<>();
            for (RegionalBasePlanConfig config : basePlan.getRegionalConfigs()) {
                Money price = config.getPrice();
                if (price == null) continue;
                String currency = price.getCurrencyCode();
                int nanos = price.getNanos() != null ? price.getNanos() : 0;
                currencyNanosMap.computeIfAbsent(currency, k -> new ArrayList<>()).add(nanos);
            }
            for (Map.Entry<String, List<Integer>> entry : currencyNanosMap.entrySet()) {
                boolean allZero = entry.getValue().stream().allMatch(n -> n == 0);
                integerOnlyCurrencyMap.put(entry.getKey(), allZero);
            }

            for (RegionalBasePlanConfig config : basePlan.getRegionalConfigs()) {
                String regionCode = config.getRegionCode();
                Money regionBasePrice = config.getPrice();
                if (regionBasePrice == null) continue;
                String currency = regionBasePrice.getCurrencyCode();
                BigDecimal localDiscountedPrice = getLocalDiscountedPrice(usdDiscountedPrice, regionBasePrice, usdBasePrice);

                long localUnits;
                int localNanos;
                // 动态判断：如果该币种所有基础价 nanos==0，视为整数币种
                if (integerOnlyCurrencyMap.getOrDefault(currency, false)) {
                    localUnits = localDiscountedPrice.setScale(0, RoundingMode.HALF_UP).longValue();
                    localNanos = 0;
                } else {
                    localUnits = localDiscountedPrice.longValue();
                    localNanos = localDiscountedPrice.subtract(new BigDecimal(localUnits))
                        .multiply(new BigDecimal("1000000000")).intValue();
                }

                RegionalSubscriptionOfferPhaseConfig regionalConfig =
                    new RegionalSubscriptionOfferPhaseConfig();
                regionalConfig.setRegionCode(regionCode);
                Money discountPrice =
                    new Money();
                discountPrice.setCurrencyCode(currency);
                discountPrice.setUnits(localUnits);
                discountPrice.setNanos(localNanos);
                regionalConfig.setPrice(discountPrice);
                discountRegionalConfigs.add(regionalConfig);
            }
            discountPhase.setRegionalConfigs(discountRegionalConfigs);
            phases.add(discountPhase);

            offer.setPhases(phases);

            // 获取地区版本
            String regionsVersion = getSubscriptionRegionsVersion(packageName, subscriptionId);

            // 创建优惠
            com.google.api.services.androidpublisher.model.SubscriptionOffer createdOffer =
                publisher.monetization().subscriptions().basePlans().offers()
                    .create(packageName, subscriptionId, basePlanId, offer)
                    .setRegionsVersionVersion(regionsVersion)
                    .setOfferId(offerId)
                    .execute();

            log.info("✅ 首次订阅固定金额优惠创建成功: {} - {} - {}", subscriptionId, basePlanId, offerId);
            return true;

        } catch (Exception e) {
            log.error("❌ 创建首次订阅固定金额优惠失败: {} - {} - {} - {}", subscriptionId, basePlanId, offerId, e.getMessage(), e);
            throw e;
        }
    }

    private static BigDecimal getLocalDiscountedPrice(BigDecimal usdDiscountedPrice, Money regionBasePrice, BigDecimal usdBasePrice) {
        long units = regionBasePrice.getUnits() != null ? regionBasePrice.getUnits() : 0L;
        int nanos = regionBasePrice.getNanos() != null ? regionBasePrice.getNanos() : 0;
        BigDecimal localBasePrice = new BigDecimal(units)
            .add(new BigDecimal(nanos).divide(new BigDecimal("1000000000")));
        // 按比例换算优惠金额
        BigDecimal localDiscountedPrice = localBasePrice
            .multiply(usdDiscountedPrice)
            .divide(usdBasePrice, 2, RoundingMode.HALF_UP);
        return localDiscountedPrice;
    }

    private static BigDecimal getBasePrice(BasePlan basePlan) {
        BigDecimal usdBasePrice = null;
        if (basePlan.getRegionalConfigs() != null) {
            for (RegionalBasePlanConfig config : basePlan.getRegionalConfigs()) {
                if (config.getPrice() != null && "USD".equals(config.getPrice().getCurrencyCode())) {
                    usdBasePrice = new BigDecimal(config.getPrice().getUnits())
                        .add(new BigDecimal(config.getPrice().getNanos()).divide(new BigDecimal("1000000000")));
                    break;
                }
            }
        }
        if (usdBasePrice == null) throw new RuntimeException("USD base price not found in basePlan regionalConfigs");
        return usdBasePrice;
    }

    private static SubscriptionOfferPhase getFreeTrialPhase(int freeTrialDays, BasePlan basePlan) {
        SubscriptionOfferPhase freeTrialPhase =
            new SubscriptionOfferPhase();
        freeTrialPhase.setDuration("P" + freeTrialDays + "D");
        freeTrialPhase.setRecurrenceCount(1);
        List<RegionalSubscriptionOfferPhaseConfig> freeTrialRegionalConfigs = new ArrayList<>();
        if (basePlan.getRegionalConfigs() != null) {
            for (RegionalBasePlanConfig config : basePlan.getRegionalConfigs()) {
                RegionalSubscriptionOfferPhaseConfig regionalConfig =
                    new RegionalSubscriptionOfferPhaseConfig();
                regionalConfig.setRegionCode(config.getRegionCode());
                // 设置为免费
                RegionalSubscriptionOfferPhaseFreePriceOverride freePrice =
                    new RegionalSubscriptionOfferPhaseFreePriceOverride();
                regionalConfig.setFree(freePrice);
                freeTrialRegionalConfigs.add(regionalConfig);
            }
        }
        freeTrialPhase.setRegionalConfigs(freeTrialRegionalConfigs);
        return freeTrialPhase;
    }

    // ==================== 订阅购买验证 ====================

    /**
     * 验证订阅购买
     * @param packageName 包名
     * @param subscriptionId 订阅产品ID
     * @param purchaseToken 购买令牌
     * @return 购买信息
     */
    public Purchase verifySubscriptionPurchase(String packageName, String subscriptionId, String purchaseToken) {
        try {
            AndroidPublisher androidPublisher = googlePlayConfig.getAndroidPublisher(packageName);
            AndroidPublisher.Purchases.Subscriptions.Get request = androidPublisher.purchases().subscriptions().get(packageName, subscriptionId, purchaseToken);
            SubscriptionPurchase subscriptionPurchase = request.execute();
            log.info("订阅验证结果-原始: {}", JSONObject.toJSONString(subscriptionPurchase));
            return convertSubscriptionToPurchase(subscriptionPurchase, packageName, subscriptionId, purchaseToken);
        } catch (IOException e) {
            log.error("验证订阅购买失败: {}", e.getMessage(), e);
            throw new RuntimeException("验证订阅购买失败", e);
        }
    }

    /**
     * 确认订阅购买
     * @param packageName 包名
     * @param subscriptionId 订阅产品ID
     * @param purchaseToken 购买令牌
     */
    public void acknowledgeSubscriptionPurchase(String packageName, String subscriptionId, String purchaseToken) {
        try {
            AndroidPublisher androidPublisher = googlePlayConfig.getAndroidPublisher(packageName);
            AndroidPublisher.Purchases.Subscriptions.Acknowledge request = androidPublisher.purchases().subscriptions().acknowledge(packageName, subscriptionId, purchaseToken, null);
            request.execute();
            log.info("订阅购买确认成功: {} - {} - {}", packageName, subscriptionId, purchaseToken);
        } catch (IOException e) {
            String errorMessage = e.getMessage();
            log.error("确认订阅购买失败: {} - {} - {} - 错误: {}", packageName, subscriptionId, purchaseToken, errorMessage, e);

            // 根据错误类型提供更具体的错误信息
            if (errorMessage != null) {
                if (errorMessage.contains("productNotOwnedByUser")) {
                    throw new RuntimeException("订阅购买确认失败: 该购买不属于当前用户，请检查服务账号权限或购买令牌是否有效", e);
                } else if (errorMessage.contains("400")) {
                    throw new RuntimeException("订阅购买确认失败: 请求参数错误，请检查产品ID和购买令牌", e);
                } else if (errorMessage.contains("401")) {
                    throw new RuntimeException("订阅购买确认失败: 认证失败，请检查服务账号密钥和权限", e);
                } else if (errorMessage.contains("403")) {
                    throw new RuntimeException("订阅购买确认失败: 权限不足，请检查服务账号是否有足够的权限", e);
                } else if (errorMessage.contains("404")) {
                    throw new RuntimeException("订阅购买确认失败: 购买记录不存在，请检查产品ID和购买令牌", e);
                } else if (errorMessage.contains("409")) {
                    throw new RuntimeException("订阅购买确认失败: 购买已经被确认过了或正在被其他进程处理", e);
                }
            }

            throw new RuntimeException("确认订阅购买失败: " + errorMessage, e);
        }
    }

    /**
     * 转换订阅购买结果为Purchase对象
     */
    private Purchase convertSubscriptionToPurchase(SubscriptionPurchase subscriptionPurchase, String packageName, String subscriptionId, String purchaseToken) {
        // 添加调试日志
        log.debug("转换SubscriptionPurchase: orderId={}, paymentState={}, acknowledgementState={}",
                subscriptionPurchase.getOrderId(),
                subscriptionPurchase.getPaymentState(),
                subscriptionPurchase.getAcknowledgementState());

        return Purchase.builder()
                .orderId(subscriptionPurchase.getOrderId())
                .productId(subscriptionId)
                .packageName(packageName)
                .purchaseToken(purchaseToken)
                .purchaseState(subscriptionPurchase.getPaymentState() != null ? String.valueOf(subscriptionPurchase.getPaymentState()) : "0")
                .consumptionState("1") // 订阅产品总是被认为是已消费状态
                .developerPayload(subscriptionPurchase.getDeveloperPayload())
                .purchaseTime(subscriptionPurchase.getStartTimeMillis() != null
                        ? LocalDateTime.ofInstant(Instant.ofEpochMilli(subscriptionPurchase.getStartTimeMillis()), ZoneId.systemDefault()) : null)
                .acknowledged(
                    subscriptionPurchase.getAcknowledgementState() != null && subscriptionPurchase.getAcknowledgementState() == 1
                )
                .autoRenewing(subscriptionPurchase.getAutoRenewing() != null && subscriptionPurchase.getAutoRenewing())
                .expiryTime(subscriptionPurchase.getExpiryTimeMillis() != null
                        ? LocalDateTime.ofInstant(Instant.ofEpochMilli(subscriptionPurchase.getExpiryTimeMillis()), ZoneId.systemDefault()) : null)
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .rawData(JSONObject.parseObject(JSONObject.toJSONString(subscriptionPurchase)))
                .build();
    }

    /**
     * 取消订阅购买
     * @param packageName 包名
     * @param subscriptionId 订阅产品ID
     * @param purchaseToken 购买令牌
     */
    public void cancelSubscriptionPurchase(String packageName, String subscriptionId, String purchaseToken) {
        try {
            AndroidPublisher androidPublisher = googlePlayConfig.getAndroidPublisher(packageName);
            AndroidPublisher.Purchases.Subscriptions.Cancel request = androidPublisher.purchases().subscriptions().cancel(packageName, subscriptionId, purchaseToken);
            request.execute();
            log.info("订阅取消成功: {} - {} - {}", packageName, subscriptionId, purchaseToken);
        } catch (IOException e) {
            String errorMessage = e.getMessage();
            log.error("取消订阅失败: {} - {} - {} - 错误: {}", packageName, subscriptionId, purchaseToken, errorMessage, e);

            // 根据错误类型提供更具体的错误信息
            if (errorMessage != null) {
                if (errorMessage.contains("productNotOwnedByUser")) {
                    throw new RuntimeException("取消订阅失败: 该订阅不属于当前用户，请检查服务账号权限或购买令牌是否有效", e);
                } else if (errorMessage.contains("400")) {
                    throw new RuntimeException("取消订阅失败: 请求参数错误，请检查产品ID和购买令牌", e);
                } else if (errorMessage.contains("401")) {
                    throw new RuntimeException("取消订阅失败: 认证失败，请检查服务账号密钥和权限", e);
                } else if (errorMessage.contains("403")) {
                    throw new RuntimeException("取消订阅失败: 权限不足，请检查服务账号是否有足够的权限", e);
                } else if (errorMessage.contains("404")) {
                    throw new RuntimeException("取消订阅失败: 订阅记录不存在，请检查产品ID和购买令牌", e);
                } else if (errorMessage.contains("410")) {
                    throw new RuntimeException("取消订阅失败: 订阅已经过期或已被取消", e);
                }
            }

            throw new RuntimeException("取消订阅失败: " + errorMessage, e);
        }
    }

    // ==================== 现有的订阅产品管理方法 ====================
}
