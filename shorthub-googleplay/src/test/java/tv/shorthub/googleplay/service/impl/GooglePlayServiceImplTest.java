package tv.shorthub.googleplay.service.impl;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import tv.shorthub.googleplay.model.Product;

import java.lang.reflect.Method;

import static org.junit.jupiter.api.Assertions.*;

/**
 * GooglePlayServiceImpl 测试类
 */
@DisplayName("GooglePlayServiceImpl 测试")
class GooglePlayServiceImplTest {

    private GooglePlayServiceImpl googlePlayService;

    @BeforeEach
    void setUp() {
        googlePlayService = new GooglePlayServiceImpl();
    }

    @Test
    @DisplayName("测试产品类型转换 - inapp 转 managedUser")
    void testConvertToPurchaseType_Inapp() throws Exception {
        Method method = GooglePlayServiceImpl.class.getDeclaredMethod("convertToPurchaseType", String.class);
        method.setAccessible(true);
        
        String result = (String) method.invoke(googlePlayService, Product.TYPE_INAPP);
        assertEquals("managedUser", result);
    }

    @Test
    @DisplayName("测试产品类型转换 - subs 转 subscription")
    void testConvertToPurchaseType_Subs() throws Exception {
        Method method = GooglePlayServiceImpl.class.getDeclaredMethod("convertToPurchaseType", String.class);
        method.setAccessible(true);
        
        String result = (String) method.invoke(googlePlayService, Product.TYPE_SUBS);
        assertEquals("subscription", result);
    }

    @Test
    @DisplayName("测试产品类型转换 - null 转 managedUser")
    void testConvertToPurchaseType_Null() throws Exception {
        Method method = GooglePlayServiceImpl.class.getDeclaredMethod("convertToPurchaseType", String.class);
        method.setAccessible(true);
        
        String result = (String) method.invoke(googlePlayService, (String) null);
        assertEquals("managedUser", result);
    }

    @Test
    @DisplayName("测试产品类型转换 - 未知类型转 managedUser")
    void testConvertToPurchaseType_Unknown() throws Exception {
        Method method = GooglePlayServiceImpl.class.getDeclaredMethod("convertToPurchaseType", String.class);
        method.setAccessible(true);
        
        String result = (String) method.invoke(googlePlayService, "unknown_type");
        assertEquals("managedUser", result);
    }

    @Test
    @DisplayName("测试产品类型反向转换 - managedUser 转 inapp")
    void testConvertFromPurchaseType_ManagedUser() throws Exception {
        Method method = GooglePlayServiceImpl.class.getDeclaredMethod("convertFromPurchaseType", String.class);
        method.setAccessible(true);
        
        String result = (String) method.invoke(googlePlayService, "managedUser");
        assertEquals(Product.TYPE_INAPP, result);
    }

    @Test
    @DisplayName("测试产品类型反向转换 - subscription 转 subs")
    void testConvertFromPurchaseType_Subscription() throws Exception {
        Method method = GooglePlayServiceImpl.class.getDeclaredMethod("convertFromPurchaseType", String.class);
        method.setAccessible(true);
        
        String result = (String) method.invoke(googlePlayService, "subscription");
        assertEquals(Product.TYPE_SUBS, result);
    }

    @Test
    @DisplayName("测试产品类型反向转换 - null 转 inapp")
    void testConvertFromPurchaseType_Null() throws Exception {
        Method method = GooglePlayServiceImpl.class.getDeclaredMethod("convertFromPurchaseType", String.class);
        method.setAccessible(true);
        
        String result = (String) method.invoke(googlePlayService, (String) null);
        assertEquals(Product.TYPE_INAPP, result);
    }

    @Test
    @DisplayName("测试产品类型反向转换 - 未知类型转 inapp")
    void testConvertFromPurchaseType_Unknown() throws Exception {
        Method method = GooglePlayServiceImpl.class.getDeclaredMethod("convertFromPurchaseType", String.class);
        method.setAccessible(true);
        
        String result = (String) method.invoke(googlePlayService, "UNKNOWN_TYPE");
        assertEquals(Product.TYPE_INAPP, result);
    }

    @Test
    @DisplayName("测试产品状态常量")
    void testProductStatusConstants() {
        assertEquals("active", Product.STATUS_ACTIVE);
        assertEquals("inactive", Product.STATUS_INACTIVE);
        assertEquals("statusUnspecified", Product.STATUS_UNSPECIFIED);
    }
} 