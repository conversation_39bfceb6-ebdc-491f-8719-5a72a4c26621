# ShortHub Google Play 模块

这是一个完整的Google Play模块，支持billing结算功能、应用内产品管理、订阅管理等功能。**新增支持HTTP代理功能**。

## 功能特性

### 🛒 Billing结算功能
- ✅ 验证支付
- ✅ 退款处理
- ✅ 订单查询
- ✅ 订阅管理
- ✅ 退订处理

### 📱 应用内产品管理
- ✅ 动态创建产品
- ✅ 产品信息更新
- ✅ 产品删除
- ✅ 产品列表查询

### 🔑 多密钥管理
- ✅ 支持多个Play密钥
- ✅ 动态密钥配置
- ✅ 应用隔离

### 🌐 HTTP代理支持
- ✅ HTTP/SOCKS代理支持
- ✅ 代理认证（用户名/密码）
- ✅ 连接超时配置
- ✅ 代理连接测试
- ✅ 动态代理切换

## 技术栈

- **Spring Boot 3.x**
- **Google Play Developer API v3**
- **Google Auth Library**
- **Lombok**
- **Jackson**

## 快速开始

### 1. 配置服务账号密钥

将Google Play服务账号密钥文件放置在 `src/main/resources/` 目录下，并在 `application.yml` 中配置：

```yaml
google:
  play:
    apps:
      - packageName: "com.b.shorthub.tv"
        serviceAccountKeyPath: "ace-world-464010-i4-64900a8cf96b.json"
        appName: "ShortHub TV"
        enabled: true
    
    # HTTP代理配置
    proxy:
      enabled: true
      type: "HTTP"                     # HTTP 或 SOCKS
      host: "proxy.example.com"
      port: 8080
      username: "proxy_user"           # 可选
      password: "proxy_pass"           # 可选
      connectTimeout: 30000
      readTimeout: 60000
```

### 2. 启动应用

```bash
mvn spring-boot:run
```

应用将在 `http://localhost:8080` 启动。

### 3. 运行测试

```bash
mvn test
```

## API 文档

### 产品管理 API

#### 获取产品列表
```http
GET /api/googleplay/{packageName}/products
```

#### 获取单个产品
```http
GET /api/googleplay/{packageName}/products/{productId}
```

#### 创建产品
```http
POST /api/googleplay/{packageName}/products
Content-Type: application/json

{
  "productId": "premium_feature",
  "name": "高级功能",
  "description": "解锁所有高级功能",
  "type": "inapp",
  "defaultPrice": 9.99,
  "defaultPriceCurrencyCode": "CNY"
}
```

#### 更新产品
```http
PUT /api/googleplay/{packageName}/products/{productId}
Content-Type: application/json

{
  "name": "高级功能升级版",
  "description": "解锁所有高级功能和新特性",
  "defaultPrice": 12.99
}
```

### 代理管理 API

#### 获取代理配置
```http
GET /api/googleplay/proxy/config
```

#### 更新代理配置
```http
POST /api/googleplay/proxy/config
Content-Type: application/json

{
  "enabled": true,
  "type": "HTTP",
  "host": "proxy.example.com",
  "port": 8080,
  "username": "proxy_user",
  "password": "proxy_pass",
  "connectTimeout": 30000,
  "readTimeout": 60000
}
```

#### 测试代理连接
```http
POST /api/googleplay/proxy/test
Content-Type: application/json

{
  "host": "proxy.example.com",
  "port": 8080,
  "type": "HTTP"
}
```

#### 获取代理状态
```http
GET /api/googleplay/proxy/status
```

#### 启用/禁用代理
```http
POST /api/googleplay/proxy/enable
POST /api/googleplay/proxy/disable
```

## 代理配置详解

### 支持的代理类型

1. **HTTP代理**
   ```yaml
   proxy:
     type: "HTTP"
     host: "proxy.example.com"
     port: 8080
   ```

2. **SOCKS代理**
   ```yaml
   proxy:
     type: "SOCKS"
     host: "socks.example.com"
     port: 1080
   ```

### 代理认证

支持用户名/密码认证的代理服务器：

```yaml
proxy:
  username: "your_username"
  password: "your_password"
```

### 超时配置

```yaml
proxy:
  connectTimeout: 30000    # 连接超时（毫秒）
  readTimeout: 60000       # 读取超时（毫秒）
```

## 环境配置

### 开发环境
```yaml
spring:
  profiles:
    active: development

google:
  play:
    proxy:
      enabled: false    # 开发环境不使用代理
```

### 生产环境
```yaml
spring:
  profiles:
    active: production-with-proxy

google:
  play:
    proxy:
      enabled: true
      host: "${PROXY_HOST}"
      port: "${PROXY_PORT}"
```

## 安全说明

1. **代理服务器安全**: 确保使用可信的代理服务器
2. **密钥保护**: 不要将代理用户名/密码提交到版本控制
3. **合规使用**: 请遵守Google Play服务条款
4. **监控日志**: 定期检查请求日志确保正常运行

## 故障排除

### 代理连接问题
- 检查代理服务器地址和端口
- 验证代理认证信息
- 测试代理服务器可用性

### 请求被拒绝
- 检查User-Agent配置
- 调整请求头设置
- 验证代理IP是否被封禁

### 超时问题
- 调整connectTimeout和readTimeout
- 检查网络连接质量
- 考虑使用更快的代理服务器

## 日志配置

启用详细日志来调试问题：

```yaml
logging:
  level:
    tv.shorthub.googleplay: DEBUG
    com.google.api.client.http: DEBUG
```

## 示例配置文件

参考 `application-proxy-example.yml` 获取完整的配置示例。 