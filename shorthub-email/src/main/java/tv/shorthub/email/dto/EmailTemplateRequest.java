package tv.shorthub.email.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import tv.shorthub.email.enums.EmailTemplateType;

import java.math.BigDecimal;

/**
 * 邮件模板生成请求 DTO
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EmailTemplateRequest {

    /**
     * 模板类型
     */
    private EmailTemplateType templateType;

    /**
     * 语言 (例如: "zh_TW", "en_US")
     */
    private String language;

    /**
     * 用户名
     */
    private String userName;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 方案名称
     */
    private String planName;

    /**
     * 金额
     */
    private BigDecimal amount;

    /**
     * 支付/交易时间
     */
    private String paymentTime;

    /**
     * 取消时间
     */
    private String cancellationTime;

    /**
     * 网站URL，用于邮件中的链接
     */
    private String websiteUrl;

    /**
     * 收件人邮箱
     */
    private String toEmail;

    /**
     * 邮件主题（如果为空会根据模板类型自动生成）
     */
    private String subject;

    /**
     * 关联ID（订单ID或其他业务ID），用于日志追踪和幂等性控制
     */
    private String referenceId;

    /**
     * 取消订阅链接，用于邮件中的"取消订阅"按钮
     * 如果为空则不显示取消订阅按钮
     */
    private String cancelSubscriptionUrl;

    /**
     * 下次扣费日期，用于提前扣费通知邮件
     * 注意：当邮件类型为 UPCOMING_PAYMENT_NOTIFICATION 时，此字段为必填项
     */
    private String nextBillingDate;
}
