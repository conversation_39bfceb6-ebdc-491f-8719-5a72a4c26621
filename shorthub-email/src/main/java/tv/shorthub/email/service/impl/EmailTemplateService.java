package tv.shorthub.email.service.impl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import tv.shorthub.email.service.IEmailTemplateService;
import org.apache.commons.lang3.StringUtils;
import java.math.BigDecimal;
import java.time.Year;

/**
 * 邮件模板服务
 * 集中管理所有邮件模板
 */
@Service
@Slf4j
public class EmailTemplateService implements IEmailTemplateService {

    private static final String COMPANY_NAME = "Shorthub";
    private static final String COMPANY_WEBSITE_URL = "https://www.shorthub.tv";
    private static final String SUPPORT_EMAIL = "<EMAIL>";
    private static final String LOGO_URL = "https://shorthub.tv/logo.png";



    private String getCommonEmailStyles() {
        return "<style>" +
                "body { margin: 0; padding: 0; width: 100% !important; -webkit-font-smoothing: antialiased; font-family: 'Segoe UI', 'Helvetica Neue', Helvetica, Arial, sans-serif; background-color: #0d0d0d; color: #e0e0e0; }" +
                ".email-container { max-width: 600px; margin: 30px auto; background-color: #1a1a1a; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.4); overflow: hidden; border: 1px solid #333; }" +
                ".email-header { padding: 30px 40px; text-align: center; background-color: #1a1a1a; border-bottom: 1px solid #333; }" +
                ".logo-container { display: flex; align-items: center; justify-content: center; margin-bottom: 15px; }" +
                ".logo { height: 32px; width: auto; }" +
                ".brand-name { font-size: 24px; color: #ffffff; font-weight: 700; margin-left: 12px; text-decoration: none; }" +
                ".brand-name sup { font-size: 0.5em; vertical-align: super; color: #aaa; }" +
                ".email-header h1 { font-size: 26px; color: #ffffff; margin: 10px 0 0; font-weight: 600; }" +
                ".email-content { padding: 35px 40px; line-height: 1.7; color: #cccccc; font-size: 16px; }" +
                ".email-content h2 { font-size: 22px; color: #ffffff; margin-top: 0; margin-bottom: 20px; font-weight: 600; }" +
                ".email-content p { margin-bottom: 18px; color: #b3b3b3; }" +
                ".email-content strong { color: #ffffff; font-weight: 600; }" +
                ".email-content a { color: #5DADE2; text-decoration: none; font-weight: 500; }" +
                ".email-content a:hover { text-decoration: underline; }" +
                ".details-box { background-color: #252525; padding: 25px; margin: 25px 0; border-radius: 8px; border: 1px solid #444; }" +
                ".details-box p { margin-bottom: 12px !important; font-size: 16px; display: flex; justify-content: space-between; flex-wrap: wrap; }" +
                ".details-box p:last-child { margin-bottom: 0 !important; }" +
                ".details-box .label { font-weight: 500; color: #a0a0a0; margin-right: 10px; }" +
                ".details-box .value { font-weight: 600; color: #ffffff; text-align: right; word-break: break-all; }" +
                ".amount { color: #E74C3C; font-weight: 700; }" +
                ".plan-name { color: #5DADE2; font-weight: 700; }" +
                ".status-success { color: #2ECC71; font-weight: 600; }" +
                ".button-cta { display: inline-block; background-color: #ffffff; color: #111111 !important; padding: 12px 28px; margin-top: 15px; border-radius: 6px; text-decoration: none; font-weight: 700; font-size: 16px; text-align: center; transition: background-color 0.3s ease; }" +
                ".button-cta:hover { background-color: #f0f0f0; }" +
                ".button-secondary { display: inline-block; background-color: transparent; color: #E74C3C !important; padding: 10px 24px; margin-top: 10px; border: 2px solid #E74C3C; border-radius: 6px; text-decoration: none; font-weight: 600; font-size: 14px; text-align: center; transition: all 0.3s ease; }" +
                ".button-secondary:hover { background-color: #E74C3C; color: #ffffff !important; }" +
                ".divider { height: 1px; background-color: #333; margin: 30px 0; }" +
                ".email-footer { text-align: center; padding: 25px 40px; background-color: #0d0d0d; border-top: 1px solid #333; font-size: 13px; color: #777; }" +
                ".email-footer p { margin: 8px 0; }" +
                ".email-footer a { color: #5DADE2 !important; text-decoration: none; font-weight: 500; }" +
                ".email-footer a:hover { text-decoration: underline; }" +
                "@media screen and (max-width: 600px) {" +
                "    .email-container { margin: 0; border-radius: 0; width: 100%; border: none; }" +
                "    .email-content { padding: 30px 20px; }" +
                "    .email-header { padding: 25px 20px; }" +
                "    .brand-name { font-size: 22px; }" +
                "    .email-header h1 { font-size: 24px; }" +
                "    .email-content h2 { font-size: 20px; }" +
                "    .details-box { padding: 20px; }" +
                "    .details-box p { flex-direction: column; align-items: flex-start; }" +
                "    .details-box .label { margin-bottom: 5px; }" +
                "    .details-box .value { text-align: left; }" +
                "    .email-footer { padding: 20px; }" +
                "}" +
                "</style>";
    }

    /**
     * 生成支付成功邮件模板（繁体）
     */
    @Override
    public String generatePaymentSuccessTemplateTW(String userName, String orderNo, BigDecimal amount, String paymentTime, String websiteUrl) {
        BigDecimal finalAmount = (amount == null) ? BigDecimal.ZERO : amount;
        int currentYear = Year.now().getValue();
        String styles = getCommonEmailStyles();
        String url = StringUtils.isNotBlank(websiteUrl) ? websiteUrl : COMPANY_WEBSITE_URL;
        return String.format(
                "<!DOCTYPE html>" +
                        "<html lang=\"zh-TW\">" +
                        "<head>" +
                        "    <meta charset=\"UTF-8\">" +
                        "    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">" +
                        "    <title>支付成功通知</title>" +
                        "    %s" +
                        "</head>" +
                        "<body>" +
                        "    <div class=\"email-container\">" +
                        "        <div class=\"email-header\">" +
                        "            <div class=\"logo-container\">" +
                        "                <img src=\"%s\" alt=\"%s Logo\" class=\"logo\">" +
                        "                <span class=\"brand-name\">%s<sup>TV</sup></span>" +
                        "            </div>" +
                        "            <h1>✅ 支付成功</h1>" +
                        "        </div>" +
                        "        <div class=\"email-content\">" +
                        "            <h2>親愛的 %s 您好，</h2>" +
                        "            <p>我們很高興通知您，您的付款已<span class=\"status-success\">成功處理</span>。感謝您的信任！</p>" +
                        "            <div class=\"details-box\">" +
                        "                <p><span class=\"label\">訂單編號：</span><strong class=\"value\">%s</strong></p>" +
                        "                <p><span class=\"label\">支付金額：</span><span class=\"amount value\">%s 元</span></p>" +
                        "                <p><span class=\"label\">支付時間：</span><span class=\"value\">%s</span></p>" +
                        "                <p><span class=\"label\">支付狀態：</span><span class=\"status-success value\">已完成</span></p>" +
                        "            </div>" +
                        "            <p>訂單已經生效</p>" +
                        "            <p style=\"text-align:center;\"><a href=\"%s\" class=\"button-cta\">前往官網</a></p>" +
                        "            <div class=\"divider\"></div>" +
                        "            <p>如果您有任何疑問，歡迎隨時<a href=\"mailto:%s\">聯繫我們的支援團隊</a>。</p>" +
                        "        </div>" +
                        "        <div class=\"email-footer\">" +
                        "            <p>&copy; %d %s. 版權所有。</p>" +
                        "            <p>此為系統自動發送的郵件，請勿直接回覆。</p>" +
                        "            <p><a href=\"%s\">瀏覽我們的網站</a></p>" +
                        "        </div>" +
                        "    </div>" +
                        "</body>" +
                        "</html>",
                styles,
                LOGO_URL, COMPANY_NAME, COMPANY_NAME,
                userName,
                orderNo,
                finalAmount.toPlainString(),
                paymentTime,
                url,
                SUPPORT_EMAIL,
                currentYear, COMPANY_NAME,
                url
        );
    }

    /**
     * 生成支付成功邮件模板（英文）
     */
    @Override
    public String generatePaymentSuccessTemplateEN(String userName, String orderNo, BigDecimal amount, String paymentTime, String websiteUrl) {
        BigDecimal finalAmount = (amount == null) ? BigDecimal.ZERO : amount;
        int currentYear = Year.now().getValue();
        String styles = getCommonEmailStyles();
        String url = StringUtils.isNotBlank(websiteUrl) ? websiteUrl : COMPANY_WEBSITE_URL;
        return String.format(
                "<!DOCTYPE html>" +
                        "<html lang=\"en-US\">" +
                        "<head>" +
                        "    <meta charset=\"UTF-8\">" +
                        "    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">" +
                        "    <title>Payment Success Notification</title>" +
                        "    %s" +
                        "</head>" +
                        "<body>" +
                        "    <div class=\"email-container\">" +
                        "        <div class=\"email-header\">" +
                        "            <div class=\"logo-container\">" +
                        "                <img src=\"%s\" alt=\"%s Logo\" class=\"logo\">" +
                        "                <span class=\"brand-name\">%s<sup>TV</sup></span>" +
                        "            </div>" +
                        "            <h1>✅ Payment Successful</h1>" +
                        "        </div>" +
                        "        <div class=\"email-content\">" +
                        "            <h2>Dear %s,</h2>" +
                        "            <p>We are pleased to inform you that your payment has been <span class=\"status-success\">successfully processed</span>. Thank you for your trust!</p>" +
                        "            <div class=\"details-box\">" +
                        "                <p><span class=\"label\">Order Number:</span> <strong class=\"value\">%s</strong></p>" +
                        "                <p><span class=\"label\">Amount Paid:</span> <span class=\"amount value\">$%s USD</span></p>" +
                        "                <p><span class=\"label\">Payment Time:</span> <span class=\"value\">%s</span></p>" +
                        "                <p><span class=\"label\">Payment Status:</span> <span class=\"status-success value\">Completed</span></p>" +
                        "            </div>" +
                        "            <p>Your order is now effective.</p>" +
                        "            <p style=\"text-align:center;\"><a href=\"%s\" class=\"button-cta\">Visit Website</a></p>" +
                        "            <div class=\"divider\"></div>" +
                        "            <p>If you have any questions, feel free to <a href=\"mailto:%s\">contact our support team</a>.</p>" +
                        "        </div>" +
                        "        <div class=\"email-footer\">" +
                        "            <p>&copy; %d %s. All rights reserved.</p>" +
                        "            <p>This is an automated message, please do not reply directly.</p>" +
                        "            <p><a href=\"%s\">Visit our website</a></p>" +
                        "        </div>" +
                        "    </div>" +
                        "</body>" +
                        "</html>",
                styles,
                LOGO_URL, COMPANY_NAME, COMPANY_NAME,
                userName,
                orderNo,
                finalAmount.toPlainString(),
                paymentTime,
                url,
                SUPPORT_EMAIL,
                currentYear, COMPANY_NAME,
                url
        );
    }

    /**
     * 生成订阅成功邮件模板（繁体）
     */
    @Override
    public String generateSubscriptionSuccessTemplateTW(String userName, String orderNo, String planName, BigDecimal amount, String paymentTime, String websiteUrl) {
        int currentYear = Year.now().getValue();
        String styles = getCommonEmailStyles();
        String url = StringUtils.isNotBlank(websiteUrl) ? websiteUrl : COMPANY_WEBSITE_URL;
        return String.format(
                "<!DOCTYPE html>" +
                        "<html lang=\"zh-TW\">" +
                        "<head>" +
                        "    <meta charset=\"UTF-8\">" +
                        "    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">" +
                        "    <title>訂閱成功通知</title>" +
                        "    %s" +
                        "</head>" +
                        "<body>" +
                        "    <div class=\"email-container\">" +
                        "        <div class=\"email-header\">" +
                        "            <div class=\"logo-container\">" +
                        "                 <img src=\"%s\" alt=\"%s Logo\" class=\"logo\">" +
                        "                 <span class=\"brand-name\">%s<sup>TV</sup></span>" +
                        "            </div>" +
                        "            <h1>🎉 訂閱成功</h1>" +
                        "        </div>" +
                        "        <div class=\"email-content\">" +
                        "            <h2>親愛的 %s 您好，</h2>" +
                        "            <p>我們很高興通知您，您的訂閱已<span class=\"status-success\">成功啟用</span>。歡迎加入我們的會員行列！</p>" +
                        "            <div class=\"details-box\">" +
                        "                <p><span class=\"label\">訂單編號：</span><strong class=\"value\">%s</strong></p>" +
                        "                <p><span class=\"label\">訂閱方案：</span><span class=\"plan-name value\">%s</span></p>" +
                        "                <p><span class=\"label\">支付金額：</span><span class=\"amount value\">%s 元</span></p>" +
                        "                <p><span class=\"label\">支付時間：</span><span class=\"value\">%s</span></p>" +
                        "                <p><span class=\"label\">訂閱狀態：</span><span class=\"status-success value\">已啟用</span></p>" +
                        "            </div>" +
                        "            <p>感謝您的支持！您現在可以開始使用您的訂閱權益。我們希望您能享受我們提供的所有服務。</p>" +
                        "            <p style=\"text-align:center;\"><a href=\"%s\" class=\"button-cta\">前往官網</a></p>" +
                        "            <div class=\"divider\"></div>" +
                        "            <p>如果您有任何疑問，歡迎隨時<a href=\"mailto:%s\">聯繫我們的支援團隊</a>。</p>" +
                        "        </div>" +
                        "        <div class=\"email-footer\">" +
                        "            <p>&copy; %d %s. 版權所有。</p>" +
                        "            <p>此為系統自動發送的郵件，请勿直接回复。</p>" +
                        "            <p><a href=\"%s\">瀏覽我們的網站</a></p>" +
                        "        </div>" +
                        "    </div>" +
                        "</body>" +
                        "</html>",
                styles,
                LOGO_URL, COMPANY_NAME, COMPANY_NAME,
                userName,
                orderNo,
                planName,
                amount.toPlainString(),
                paymentTime,
                url,
                SUPPORT_EMAIL,
                currentYear, COMPANY_NAME,
                url
        );
    }

    /**
     * 生成订阅成功邮件模板（英文）
     */
    @Override
    public String generateSubscriptionSuccessTemplateEN(String userName, String orderNo, String planName, BigDecimal amount, String paymentTime, String websiteUrl) {
        int currentYear = Year.now().getValue();
        String styles = getCommonEmailStyles();
        String url = StringUtils.isNotBlank(websiteUrl) ? websiteUrl : COMPANY_WEBSITE_URL;
        return String.format(
                "<!DOCTYPE html>" +
                        "<html lang=\"en-US\">" +
                        "<head>" +
                        "    <meta charset=\"UTF-8\">" +
                        "    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">" +
                        "    <title>Subscription Success Notification</title>" +
                        "    %s" +
                        "</head>" +
                        "<body>" +
                        "    <div class=\"email-container\">" +
                        "        <div class=\"email-header\">" +
                        "            <div class=\"logo-container\">" +
                        "                <img src=\"%s\" alt=\"%s Logo\" class=\"logo\">" +
                        "                <span class=\"brand-name\">%s<sup>TV</sup></span>" +
                        "            </div>" +
                        "            <h1>🎉 Subscription Successful</h1>" +
                        "        </div>" +
                        "        <div class=\"email-content\">" +
                        "            <h2>Dear %s,</h2>" +
                        "            <p>We are pleased to inform you that your subscription has been <span class=\"status-success\">successfully activated</span>. Welcome to our membership!</p>" +
                        "            <div class=\"details-box\">" +
                        "                <p><span class=\"label\">Order Number:</span> <strong class=\"value\">%s</strong></p>" +
                        "                <p><span class=\"label\">Plan Name:</span> <span class=\"plan-name value\">%s</span></p>" +
                        "                <p><span class=\"label\">Amount Paid:</span> <span class=\"amount value\">$%s USD</span></p>" +
                        "                <p><span class=\"label\">Payment Time:</span> <span class=\"value\">%s</span></p>" +
                        "                <p><span class=\"label\">Subscription Status:</span> <span class=\"status-success value\">Active</span></p>" +
                        "            </div>" +
                        "            <p>Thank you for your support! You can now start enjoying your subscription benefits. We hope you enjoy all the services we provide.</p>" +
                        "            <p style=\"text-align:center;\"><a href=\"%s\" class=\"button-cta\">Visit Website</a></p>" +
                        "            <div class=\"divider\"></div>" +
                        "            <p>If you have any questions, feel free to <a href=\"mailto:%s\">contact our support team</a>.</p>" +
                        "        </div>" +
                        "        <div class=\"email-footer\">" +
                        "            <p>&copy; %d %s. All rights reserved.</p>" +
                        "            <p>This is an automated message, please do not reply directly.</p>" +
                        "            <p><a href=\"%s\">Visit our website</a></p>" +
                        "        </div>" +
                        "    </div>" +
                        "</body>" +
                        "</html>",
                styles,
                LOGO_URL, COMPANY_NAME, COMPANY_NAME,
                userName,
                orderNo,
                planName,
                amount.toPlainString(),
                paymentTime,
                url,
                SUPPORT_EMAIL,
                currentYear, COMPANY_NAME,
                url
        );
    }

    /**
     * 生成订阅取消邮件模板（繁体）
     * @param userName 用户名
     * @param planName 方案名称
     * @param cancellationTime 取消时间
     * @param websiteUrl 网站URL
     * @return 邮件HTML内容
     */
    @Override
    public String generateSubscriptionCancelledTemplateTW(String userName, String planName, String cancellationTime, String websiteUrl) {
        int currentYear = Year.now().getValue();
        String styles = getCommonEmailStyles();
        String url = StringUtils.isNotBlank(websiteUrl) ? websiteUrl : COMPANY_WEBSITE_URL;
        return String.format(
                "<!DOCTYPE html>" +
                        "<html lang=\"zh-TW\">" +
                        "<head>" +
                        "    <meta charset=\"UTF-8\">" +
                        "    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">" +
                        "    <title>訂閱取消確認</title>" +
                        "    %s" +
                        "</head>" +
                        "<body>" +
                        "    <div class=\"email-container\">" +
                        "        <div class=\"email-header\">" +
                        "            <div class=\"logo-container\">" +
                        "                 <img src=\"%s\" alt=\"%s Logo\" class=\"logo\">" +
                        "                 <span class=\"brand-name\">%s<sup>TV</sup></span>" +
                        "            </div>" +
                        "            <h1>🚫 訂閱已取消</h1>" +
                        "        </div>" +
                        "        <div class=\"email-content\">" +
                        "            <h2>親愛的 %s 您好，</h2>" +
                        "            <p>我們已收到您的請求，並已成功為您取消訂閱。我們很遺憾看到您離開。</p>" +
                        "            <p><strong style=\"color:#E74C3C;\">【解約說明】您的訂閱已解約，相關權益將於本計費週期結束後終止。</strong></p>" +
                        "            <div class=\"details-box\">" +
                        "                <p><span class=\"label\">已取消方案：</span><span class=\"plan-name value\">%s</span></p>" +
                        "                <p><span class=\"label\">取消時間：</span><span class=\"value\">%s</span></p>" +
                        "                <p><span class=\"label\">訂閱狀態：</span><span class=\"value\">已取消</span></p>" +
                        "            </div>" +
                        "            <p>您的訂閱權益將持續到當前計費週期結束。我們隨時歡迎您再次回來！</p>" +
                        "            <p style=\"text-align:center;\"><a href=\"%s\" class=\"button-cta\">查看方案</a></p>" +
                        "            <div class=\"divider\"></div>" +
                        "            <p>如果您有任何疑問，或願意分享您取消訂閱的原因，歡迎随时<a href=\"mailto:%s\">聯繫我們</a>。</p>" +
                        "        </div>" +
                        "        <div class=\"email-footer\">" +
                        "            <p>&copy; %d %s. 版權所有。</p>" +
                        "            <p>此為系統自動發送的郵件，请勿直接回复。</p>" +
                        "            <p><a href=\"%s\">瀏覽我們的網站</a></p>" +
                        "        </div>" +
                        "    </div>" +
                        "</body>" +
                        "</html>",
                styles,
                LOGO_URL, COMPANY_NAME, COMPANY_NAME,
                userName,
                planName,
                cancellationTime,
                url,
                SUPPORT_EMAIL,
                currentYear, COMPANY_NAME,
                url
        );
    }

    /**
     * 生成订阅取消邮件模板（英文）
     * @param userName 用户名
     * @param planName 方案名称
     * @param cancellationTime 取消时间
     * @return 邮件HTML内容
     */
    @Override
    public String generateSubscriptionCancelledTemplateEN(String userName, String planName, String cancellationTime, String websiteUrl) {
        int currentYear = Year.now().getValue();
        String styles = getCommonEmailStyles();
        String url = StringUtils.isNotBlank(websiteUrl) ? websiteUrl : COMPANY_WEBSITE_URL;
        return String.format(
                "<!DOCTYPE html>" +
                        "<html lang=\"en-US\">" +
                        "<head>" +
                        "    <meta charset=\"UTF-8\">" +
                        "    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">" +
                        "    <title>Subscription Cancellation Confirmation</title>" +
                        "    %s" +
                        "</head>" +
                        "<body>" +
                        "    <div class=\"email-container\">" +
                        "        <div class=\"email-header\">" +
                        "            <div class=\"logo-container\">" +
                        "                <img src=\"%s\" alt=\"%s Logo\" class=\"logo\">" +
                        "                <span class=\"brand-name\">%s<sup>TV</sup></span>" +
                        "            </div>" +
                        "            <h1>🚫 Subscription Canceled</h1>" +
                        "        </div>" +
                        "        <div class=\"email-content\">" +
                        "            <h2>Dear %s,</h2>" +
                        "            <p>We've received your request and have successfully canceled your subscription. We're sorry to see you go.</p>" +
                        "            <p><strong style=\"color:#E74C3C;\">[Cancellation Notice] Your subscription has been canceled. Your benefits will end after the current billing cycle.</strong></p>" +
                        "            <div class=\"details-box\">" +
                        "                <p><span class=\"label\">Canceled Plan:</span> <span class=\"plan-name value\">%s</span></p>" +
                        "                <p><span class=\"label\">Cancellation Time:</span> <span class=\"value\">%s</span></p>" +
                        "                <p><span class=\"label\">Subscription Status:</span> <span class=\"value\">Canceled</span></p>" +
                        "            </div>" +
                        "            <p>Your subscription benefits will remain active until the end of the current billing cycle. We hope to see you again soon!</p>" +
                        "            <p style=\"text-align:center;\"><a href=\"%s\" class=\"button-cta\">View Plans</a></p>" +
                        "            <div class=\"divider\"></div>" +
                        "            <p>If you have any questions or would like to share your feedback, please feel free to <a href=\"mailto:%s\">contact us</a>.</p>" +
                        "        </div>" +
                        "        <div class=\"email-footer\">" +
                        "            <p>&copy; %d %s. All rights reserved.</p>" +
                        "            <p>This is an automated message, please do not reply directly.</p>" +
                        "            <p><a href=\"%s\">Visit our website</a></p>" +
                        "        </div>" +
                        "    </div>" +
                        "</body>" +
                        "</html>",
                styles,
                LOGO_URL, COMPANY_NAME, COMPANY_NAME,
                userName,
                planName,
                cancellationTime,
                url,
                SUPPORT_EMAIL,
                currentYear, COMPANY_NAME,
                url
        );
    }

    /**
     * 生成订阅续期通知邮件模板（繁体）
     */
    @Override
    public String generateSubscriptionRenewalTemplateTW(String userName, String orderNo, BigDecimal amount, String paymentTime, String websiteUrl, String cancelSubscriptionUrl) {
        BigDecimal finalAmount = (amount == null) ? BigDecimal.ZERO : amount;
        int currentYear = Year.now().getValue();
        String styles = getCommonEmailStyles();
        String url = StringUtils.isNotBlank(websiteUrl) ? websiteUrl : COMPANY_WEBSITE_URL;

        // 构建取消订阅按钮（如果有取消订阅链接）
        String cancelSubscriptionButton = "";
        if (StringUtils.isNotBlank(cancelSubscriptionUrl)) {
            cancelSubscriptionButton = "<p style=\"text-align:center; margin-top: 15px;\"><a href=\"" + cancelSubscriptionUrl + "\" class=\"button-secondary\">取消訂閱</a></p>";
        }

        return String.format(
                "<!DOCTYPE html>" +
                        "<html lang=\"zh-TW\">" +
                        "<head>" +
                        "    <meta charset=\"UTF-8\">" +
                        "    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">" +
                        "    <title>訂閱續期成功通知</title>" +
                        "    %s" +
                        "</head>" +
                        "<body>" +
                        "    <div class=\"email-container\">" +
                        "        <div class=\"email-header\">" +
                        "            <div class=\"logo-container\">" +
                        "                <img src=\"%s\" alt=\"%s\" class=\"logo\">" +
                        "                <span class=\"brand-name\">%s</span>" +
                        "            </div>" +
                        "            <h1>訂閱續期成功</h1>" +
                        "        </div>" +
                        "        <div class=\"email-content\">" +
                        "            <h2>親愛的 %s，</h2>" +
                        "            <p>感謝您繼續選擇我們的服務！您的訂閱已成功續期，以下是您的續期詳情：</p>" +
                        "            <div class=\"details-box\">" +
                        "                <p><span class=\"label\">訂單編號：</span><span class=\"value\">%s</span></p>" +
                        "                <p><span class=\"label\">續期金額：</span><span class=\"value\">$%s</span></p>" +
                        "                <p><span class=\"label\">支付時間：</span><span class=\"value\">%s</span></p>" +
                        "                <p><span class=\"label\">續期狀態：</span><span class=\"status-success value\">已完成</span></p>" +
                        "            </div>" +
                        "            <p>您的訂閱服務已延長，可以繼續享受我們提供的所有功能和服務。感謝您的持續支持！</p>" +
                        "            <p style=\"text-align:center;\"><a href=\"%s\" class=\"button-cta\">前往官網</a></p>" +
                        "            %s" +
                        "            <div class=\"divider\"></div>" +
                        "            <p>如果您有任何疑問，歡迎隨時<a href=\"mailto:%s\">聯繫我們的支援團隊</a>。</p>" +
                        "        </div>" +
                        "        <div class=\"email-footer\">" +
                        "            <p>&copy; %d %s. 版權所有。</p>" +
                        "            <p>此為系統自動發送的郵件，請勿直接回覆。</p>" +
                        "            <p><a href=\"%s\">瀏覽我們的網站</a></p>" +
                        "        </div>" +
                        "    </div>" +
                        "</body>" +
                        "</html>",
                styles,
                LOGO_URL, COMPANY_NAME, COMPANY_NAME,
                userName,
                orderNo,
                finalAmount.toPlainString(),
                paymentTime,
                url,
                cancelSubscriptionButton,
                SUPPORT_EMAIL,
                currentYear, COMPANY_NAME,
                url
        );
    }

    /**
     * 生成订阅续期通知邮件模板（英文）
     */
    @Override
    public String generateSubscriptionRenewalTemplateEN(String userName, String orderNo, BigDecimal amount, String paymentTime, String websiteUrl, String cancelSubscriptionUrl) {
        BigDecimal finalAmount = (amount == null) ? BigDecimal.ZERO : amount;
        int currentYear = Year.now().getValue();
        String styles = getCommonEmailStyles();
        String url = StringUtils.isNotBlank(websiteUrl) ? websiteUrl : COMPANY_WEBSITE_URL;

        // 构建取消订阅按钮（如果有取消订阅链接）
        String cancelSubscriptionButton = "";
        if (StringUtils.isNotBlank(cancelSubscriptionUrl)) {
            cancelSubscriptionButton = "<p style=\"text-align:center; margin-top: 15px;\"><a href=\"" + cancelSubscriptionUrl + "\" class=\"button-secondary\">Cancel Subscription</a></p>";
        }

        return String.format(
                "<!DOCTYPE html>" +
                        "<html lang=\"en-US\">" +
                        "<head>" +
                        "    <meta charset=\"UTF-8\">" +
                        "    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">" +
                        "    <title>Subscription Renewal Success Notification</title>" +
                        "    %s" +
                        "</head>" +
                        "<body>" +
                        "    <div class=\"email-container\">" +
                        "        <div class=\"email-header\">" +
                        "            <div class=\"logo-container\">" +
                        "                <img src=\"%s\" alt=\"%s\" class=\"logo\">" +
                        "                <span class=\"brand-name\">%s</span>" +
                        "            </div>" +
                        "            <h1>Subscription Renewal Success</h1>" +
                        "        </div>" +
                        "        <div class=\"email-content\">" +
                        "            <h2>Dear %s,</h2>" +
                        "            <p>Thank you for continuing to choose our service! Your subscription has been successfully renewed. Here are your renewal details:</p>" +
                        "            <div class=\"details-box\">" +
                        "                <p><span class=\"label\">Order Number:</span><span class=\"value\">%s</span></p>" +
                        "                <p><span class=\"label\">Renewal Amount:</span><span class=\"value\">$%s</span></p>" +
                        "                <p><span class=\"label\">Payment Time:</span><span class=\"value\">%s</span></p>" +
                        "                <p><span class=\"label\">Renewal Status:</span><span class=\"status-success value\">Completed</span></p>" +
                        "            </div>" +
                        "            <p>Your subscription service has been extended and you can continue to enjoy all the features and services we provide. Thank you for your continued support!</p>" +
                        "            <p style=\"text-align:center;\"><a href=\"%s\" class=\"button-cta\">Visit Website</a></p>" +
                        "            %s" +
                        "            <div class=\"divider\"></div>" +
                        "            <p>If you have any questions, feel free to <a href=\"mailto:%s\">contact our support team</a>.</p>" +
                        "        </div>" +
                        "        <div class=\"email-footer\">" +
                        "            <p>&copy; %d %s. All rights reserved.</p>" +
                        "            <p>This is an automated message, please do not reply directly.</p>" +
                        "            <p><a href=\"%s\">Visit our website</a></p>" +
                        "        </div>" +
                        "    </div>" +
                        "</body>" +
                        "</html>",
                styles,
                LOGO_URL, COMPANY_NAME, COMPANY_NAME,
                userName,
                orderNo,
                finalAmount.toPlainString(),
                paymentTime,
                url,
                cancelSubscriptionButton,
                SUPPORT_EMAIL,
                currentYear, COMPANY_NAME,
                url
        );
    }

    /**
     * 生成提前扣费通知邮件模板（繁体）
     */
    @Override
    public String generateUpcomingPaymentNotificationTemplateTW(String userName, String planName, BigDecimal amount, String nextBillingDate, String websiteUrl, String cancelSubscriptionUrl) {
        BigDecimal finalAmount = (amount == null) ? BigDecimal.ZERO : amount;
        int currentYear = Year.now().getValue();
        String styles = getCommonEmailStyles();
        String url = StringUtils.isNotBlank(websiteUrl) ? websiteUrl : COMPANY_WEBSITE_URL;

        // 构建取消订阅按钮（如果有取消订阅链接）
        String cancelSubscriptionButton = "";
        if (StringUtils.isNotBlank(cancelSubscriptionUrl)) {
            cancelSubscriptionButton = "<p style=\"text-align:center; margin-top: 15px;\"><a href=\"" + cancelSubscriptionUrl + "\" class=\"button-secondary\">取消訂閱</a></p>";
        }

        return String.format(
                "<!DOCTYPE html>" +
                        "<html lang=\"zh-TW\">" +
                        "<head>" +
                        "    <meta charset=\"UTF-8\">" +
                        "    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">" +
                        "    <title>即將扣費提醒</title>" +
                        "    %s" +
                        "</head>" +
                        "<body>" +
                        "    <div class=\"email-container\">" +
                        "        <div class=\"email-header\">" +
                        "            <div class=\"logo-container\">" +
                        "                <img src=\"%s\" alt=\"%s\" class=\"logo\">" +
                        "                <span class=\"brand-name\">%s</span>" +
                        "            </div>" +
                        "            <h1>💳 即將扣費提醒</h1>" +
                        "        </div>" +
                        "        <div class=\"email-content\">" +
                        "            <h2>親愛的 %s，</h2>" +
                        "            <p>您的訂閱即將續期，我們將在 <strong>%s</strong> 自動為您的訂閱進行續費。</p>" +
                        "            <div class=\"details-box\">" +
                        "                <p><span class=\"label\">訂閱方案：</span><span class=\"value\">%s</span></p>" +
                        "                <p><span class=\"label\">扣費金額：</span><span class=\"value\">$%s</span></p>" +
                        "                <p><span class=\"label\">下次扣費日期：</span><span class=\"value\">%s</span></p>" +
                        "                <p><span class=\"label\">訂閱狀態：</span><span class=\"status-success value\">進行中</span></p>" +
                        "            </div>" +
                        "            <p><strong style=\"color:#F39C12;\">⚠️ 重要提醒：</strong>如果您不希望續費，請在扣費日期前取消訂閱。取消後您仍可使用服務至當前計費週期結束。</p>" +
                        "            <p style=\"text-align:center;\"><a href=\"%s\" class=\"button-cta\">管理訂閱</a></p>" +
                        "            %s" +
                        "            <div class=\"divider\"></div>" +
                        "            <p>如果您有任何疑問或需要協助，歡迎隨時<a href=\"mailto:%s\">聯繫我們的支援團隊</a>。</p>" +
                        "        </div>" +
                        "        <div class=\"email-footer\">" +
                        "            <p>&copy; %d %s. 版權所有。</p>" +
                        "            <p>此為系統自動發送的郵件，請勿直接回覆。</p>" +
                        "            <p><a href=\"%s\">瀏覽我們的網站</a></p>" +
                        "        </div>" +
                        "    </div>" +
                        "</body>" +
                        "</html>",
                styles,
                LOGO_URL, COMPANY_NAME, COMPANY_NAME,
                userName,
                nextBillingDate,
                planName,
                finalAmount.toPlainString(),
                nextBillingDate,
                url,
                cancelSubscriptionButton,
                SUPPORT_EMAIL,
                currentYear, COMPANY_NAME,
                url
        );
    }

    /**
     * 生成提前扣费通知邮件模板（英文）
     */
    @Override
    public String generateUpcomingPaymentNotificationTemplateEN(String userName, String planName, BigDecimal amount, String nextBillingDate, String websiteUrl, String cancelSubscriptionUrl) {
        BigDecimal finalAmount = (amount == null) ? BigDecimal.ZERO : amount;
        int currentYear = Year.now().getValue();
        String styles = getCommonEmailStyles();
        String url = StringUtils.isNotBlank(websiteUrl) ? websiteUrl : COMPANY_WEBSITE_URL;

        // 构建取消订阅按钮（如果有取消订阅链接）
        String cancelSubscriptionButton = "";
        if (StringUtils.isNotBlank(cancelSubscriptionUrl)) {
            cancelSubscriptionButton = "<p style=\"text-align:center; margin-top: 15px;\"><a href=\"" + cancelSubscriptionUrl + "\" class=\"button-secondary\">Cancel Subscription</a></p>";
        }

        return String.format(
                "<!DOCTYPE html>" +
                        "<html lang=\"en-US\">" +
                        "<head>" +
                        "    <meta charset=\"UTF-8\">" +
                        "    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">" +
                        "    <title>Upcoming Payment Reminder</title>" +
                        "    %s" +
                        "</head>" +
                        "<body>" +
                        "    <div class=\"email-container\">" +
                        "        <div class=\"email-header\">" +
                        "            <div class=\"logo-container\">" +
                        "                <img src=\"%s\" alt=\"%s\" class=\"logo\">" +
                        "                <span class=\"brand-name\">%s</span>" +
                        "            </div>" +
                        "            <h1>💳 Upcoming Payment Reminder</h1>" +
                        "        </div>" +
                        "        <div class=\"email-content\">" +
                        "            <h2>Dear %s,</h2>" +
                        "            <p>Your subscription is about to renew. We will automatically charge your subscription on <strong>%s</strong>.</p>" +
                        "            <div class=\"details-box\">" +
                        "                <p><span class=\"label\">Subscription Plan:</span><span class=\"value\">%s</span></p>" +
                        "                <p><span class=\"label\">Charge Amount:</span><span class=\"value\">$%s</span></p>" +
                        "                <p><span class=\"label\">Next Billing Date:</span><span class=\"value\">%s</span></p>" +
                        "                <p><span class=\"label\">Subscription Status:</span><span class=\"status-success value\">Active</span></p>" +
                        "            </div>" +
                        "            <p><strong style=\"color:#F39C12;\">⚠️ Important Notice:</strong> If you don't want to renew, please cancel your subscription before the billing date. After cancellation, you can still use the service until the end of the current billing cycle.</p>" +
                        "            <p style=\"text-align:center;\"><a href=\"%s\" class=\"button-cta\">Manage Subscription</a></p>" +
                        "            %s" +
                        "            <div class=\"divider\"></div>" +
                        "            <p>If you have any questions or need assistance, feel free to <a href=\"mailto:%s\">contact our support team</a>.</p>" +
                        "        </div>" +
                        "        <div class=\"email-footer\">" +
                        "            <p>&copy; %d %s. All rights reserved.</p>" +
                        "            <p>This is an automated message, please do not reply directly.</p>" +
                        "            <p><a href=\"%s\">Visit our website</a></p>" +
                        "        </div>" +
                        "    </div>" +
                        "</body>" +
                        "</html>",
                styles,
                LOGO_URL, COMPANY_NAME, COMPANY_NAME,
                userName,
                nextBillingDate,
                planName,
                finalAmount.toPlainString(),
                nextBillingDate,
                url,
                cancelSubscriptionButton,
                SUPPORT_EMAIL,
                currentYear, COMPANY_NAME,
                url
        );
    }
}
