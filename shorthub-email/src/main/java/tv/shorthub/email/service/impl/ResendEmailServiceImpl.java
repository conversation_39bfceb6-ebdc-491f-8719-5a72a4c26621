package tv.shorthub.email.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.resend.*;
import com.resend.services.emails.model.CreateEmailOptions;
import com.resend.services.emails.model.CreateEmailResponse;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import tv.shorthub.email.dto.EmailTemplateRequest;
import tv.shorthub.email.service.EmailService;
import tv.shorthub.system.domain.AppOrderInfo;
import tv.shorthub.system.domain.AppUsers;
import tv.shorthub.system.domain.SysEmailLog;
import tv.shorthub.system.mapper.AppOrderInfoMapper;
import tv.shorthub.system.mapper.AppUsersMapper;
import tv.shorthub.system.service.ISysEmailLogService;
import tv.shorthub.email.service.IEmailTemplateService;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;

/**
 * 邮件服务实现类 - 使用 Resend 服务
 * <AUTHOR>
 */
@Service
@Slf4j
public class ResendEmailServiceImpl implements EmailService {

    private final static String FROM_EMAIL = "ShortHubTV <<EMAIL>>";
    @Autowired
    private ISysEmailLogService emailLogService;

    @Autowired
    private AppOrderInfoMapper appOrderInfoMapper;

    @Autowired
    private AppUsersMapper appUsersMapper;
    @Autowired
    private IEmailTemplateService emailTemplateService;

    @Value("${resend.api-key}")
    private String resendApiKey;

    private Resend resend;

    @PostConstruct
    public void init() {
        this.resend = new Resend(resendApiKey);
    }

    @Override
    public void sendEmail(EmailTemplateRequest request) {

        String OrderNo = request.getOrderNo();
        AppOrderInfo appOrderInfo = appOrderInfoMapper.selectOne(new QueryWrapper<AppOrderInfo>().eq("order_no", OrderNo));
        request.setWebsiteUrl(appOrderInfo.getExtendJson().getString("verifyWithLogin"));
        request.setPaymentTime(appOrderInfo.getPayTime().toString());
        request.setAmount(appOrderInfo.getOrderAmount());
        AppUsers appUsers = appUsersMapper.selectOne(new QueryWrapper<AppUsers>().eq("user_id", appOrderInfo.getUserId()));
        request.setUserName(appUsers.getUsername());
        request.setLanguage(appUsers.getLanguageCode());
        request.setReferenceId(appOrderInfo.getOrderNo());
        if (request.getToEmail() == null ){
            request.setToEmail(appUsers.getEmail());

        }
        switch (request.getTemplateType()) {
            case PAYMENT_SUCCESS:
                sendPaymentSuccessEmail(request.getToEmail(), request.getUserName(), request.getOrderNo(), request.getAmount(), request.getPaymentTime(), request.getLanguage(), request.getWebsiteUrl());
                break;
            case SUBSCRIPTION_SUCCESS:
                sendSubscriptionSuccessEmail(request.getToEmail(), request.getUserName(), request.getOrderNo(), request.getPlanName(), request.getAmount(), request.getPaymentTime(), request.getLanguage(), request.getWebsiteUrl());
                break;
            case SUBSCRIPTION_CANCELLED:
                sendSubscriptionCancelledEmail(request.getToEmail(), request.getUserName(), request.getPlanName(), request.getCancellationTime(), request.getLanguage(), request.getReferenceId(), request.getWebsiteUrl());
                break;
            case SUBSCRIPTION_RENEWAL:
                sendSubscriptionRenewalEmailWithCancelUrl(request.getToEmail(), request.getUserName(), request.getOrderNo(), request.getAmount(), request.getPaymentTime(), request.getLanguage(), request.getWebsiteUrl(), request.getCancelSubscriptionUrl());
                break;
            case UPCOMING_PAYMENT_NOTIFICATION:
                if (StringUtils.isBlank(request.getNextBillingDate())) {
                    log.error("[邮件服务] 提前扣费通知邮件缺少必要参数 nextBillingDate。referenceId: {}", request.getReferenceId());
                    throw new IllegalArgumentException("提前扣费通知邮件必须设置 nextBillingDate 字段");
                }
                sendUpcomingPaymentNotificationEmail(request.getToEmail(), request.getUserName(), request.getPlanName(), request.getAmount(), request.getNextBillingDate(), request.getLanguage(), request.getWebsiteUrl(), request.getCancelSubscriptionUrl(), request.getReferenceId());
                break;
            default:
                log.warn("[邮件服务] 未知的邮件模板类型: {}", request.getTemplateType());
                break;
        }
    }

    @Override
    public void sendEmail(String to, String subject, String content, String referenceId) {
        SysEmailLog emailLog = new SysEmailLog();
        emailLog.setFromEmail(FROM_EMAIL);
        emailLog.setToEmail(to);
        emailLog.setSubject(subject);
        emailLog.setOrderId(referenceId);
        if (to == null || to.trim().isEmpty() || !to.contains("@") || !to.contains(".")) {
            log.info("[邮件服务] 邮件发送失败 - 邮箱格式无效: {}", to);
            emailLogService.insert(emailLog);
            return;
        }
        try {
            int insertResult = emailLogService.insert(emailLog);
            if (insertResult == 0) {
                log.info("[邮件服务] 邮件日志唯一索引冲突，跳过重复发送。referenceId={}, to={}, subject={}", referenceId, to, subject);
                return;
            }
        } catch (Exception e) {
            String msg = e.getMessage();
            if (msg != null && msg.contains("Duplicate")) {
                log.info("[邮件服务] 邮件日志唯一索引冲突，跳过重复发送。referenceId={}, to={}, subject={}", referenceId, to, subject);
                return;
            }
            throw e;
        }

        if (resendApiKey == null || resendApiKey.isEmpty()) {
            String errorMsg = "[邮件服务] 邮件发送失败：Resend API Key 未配置。";
            log.error(errorMsg);
            emailLog.setStatus("1");
            emailLog.setErrorMessage(errorMsg);
            emailLogService.update(emailLog);
            return;
        }

        log.info("[邮件服务] 准备通过 Resend 发送邮件到: {}，主题: {}", to, subject);

        try {
            if (resend == null) {
                resend = new Resend(resendApiKey);
            }
            CreateEmailOptions params = CreateEmailOptions.builder()
                    .from(FROM_EMAIL)
                    .to(to)
                    .subject(subject)
                    .html(content)
                    .build();

            CreateEmailResponse response = resend.emails().send(params);

            emailLog.setResponseBody(response.getId());
            emailLog.setEmailId(response.getId());
            emailLog.setStatus("0");
            log.info("[邮件服务] 邮件发送成功，收件人: {}, 邮件ID: {}", to, response.getId());

        } catch (Exception e) {
            String errorMsg = String.format("[邮件服务] 调用 Resend 发送邮件时发生异常，收件人: %s", to);
            log.error(errorMsg, e);
            emailLog.setStatus("1");
            emailLog.setErrorMessage(e.getMessage());
            emailLog.setResponseBody(e.toString());
        } finally {
            emailLogService.update(emailLog);
        }
    }


    /**
     * 发送续期通知邮件（带取消订阅链接）
     */
    @Override
    public void sendSubscriptionRenewalEmailWithCancelUrl(String userEmail, String userName, String orderNo, java.math.BigDecimal amount, String paymentTime, String languageCode, String websiteUrl, String cancelSubscriptionUrl) {
        log.info("[邮件服务] 开始处理续期通知邮件发送流程（带取消订阅链接）。订单号: {}, 邮箱: {}", orderNo, userEmail);
        try {
            String subject;
            String content;
            if ("zh_TW".equalsIgnoreCase(languageCode)) {
                subject = "來自shorthub.TV - 訂閱續期成功通知";
                content = emailTemplateService.generateSubscriptionRenewalTemplateTW(userName, orderNo, amount, paymentTime, websiteUrl, cancelSubscriptionUrl);
            } else {
                subject = "By shorthub.TV - Subscription Renewal Success";
                content = emailTemplateService.generateSubscriptionRenewalTemplateEN(userName, orderNo, amount, paymentTime, websiteUrl, cancelSubscriptionUrl);
            }

            sendEmail(userEmail, subject, content, orderNo);
            log.info("[邮件服务] 续期通知邮件发送完成（带取消订阅链接）。订单号: {}, 邮箱: {}", orderNo, userEmail);
        }
        catch (Exception e) {
            log.error("[邮件服务] 发送续期通知邮件时出现未预期的异常（带取消订阅链接）。订单号: {}", orderNo, e);
        }
    }

    @Override
    public void sendPaymentSuccessEmail(String userEmail, String userName, String orderNo, java.math.BigDecimal amount, String paymentTime, String languageCode, String websiteUrl) {
        log.info("[邮件服务] 开始处理支付成功邮件发送流程。订单号: {}, 邮箱: {}", orderNo, userEmail);
        try {
            String subject;
            String content;
            if ("zh_TW".equalsIgnoreCase(languageCode)) {
                subject = "來自shorthub.TV - 支付成功通知";
                content = emailTemplateService.generatePaymentSuccessTemplateTW(userName, orderNo, amount, paymentTime, websiteUrl);
            } else {
                subject = "By shorthub.TV - Payment Success";
                content = emailTemplateService.generatePaymentSuccessTemplateEN(userName, orderNo, amount, paymentTime, websiteUrl);
            }
            log.info("[邮件服务] 支付成功邮件内容已生成. 主题: {}, 订单号: {}", subject, orderNo);
            this.sendEmail(userEmail, subject, content, orderNo);
        } catch (Exception e) {
            log.error("[邮件服务] 发送支付成功邮件时出现未预期的异常。订单号: {}", orderNo, e);
        }
    }

    @Override
    public void sendSubscriptionSuccessEmail(String userEmail, String userName, String orderNo, String planName, java.math.BigDecimal amount, String paymentTime, String languageCode, String websiteUrl) {
        log.info("[邮件服务] 开始处理订阅成功邮件发送流程。订单号: {}, 邮箱: {}", orderNo, userEmail);
        planName = "SUBSCRIPTION";
        try {
            String subject;
            String content;
            if ("zh_TW".equalsIgnoreCase(languageCode)) {
                subject = "來自shorthub.TV - 支付成功通知";
                content = emailTemplateService.generateSubscriptionSuccessTemplateTW(userName, orderNo, planName, amount, paymentTime, websiteUrl);
            } else {
                subject = "By shorthub.TV - Payment Success";
                content = emailTemplateService.generateSubscriptionSuccessTemplateEN(userName, orderNo, planName, amount, paymentTime, websiteUrl);
            }
            log.info("[邮件服务] 订阅成功邮件内容已生成. 主题: {}, 订单号: {}", subject, orderNo);
            this.sendEmail(userEmail, subject, content, orderNo);
        } catch (Exception e) {
            log.error("[邮件服务] 发送订阅成功邮件时出现未预期的异常。订单号: {}", orderNo, e);
        }
    }

    @Override
    public void sendSubscriptionCancelledEmail(String userEmail, String userName, String planName, String cancellationTime, String languageCode, String subscriptionId, String websiteUrl) {
        log.info("[邮件服务] 开始处理订阅取消邮件发送流程。订阅ID: {}, 邮箱: {}", subscriptionId, userEmail);
        planName = "SUBSCRIPTION";
        try {
            String subject;
            String content;
            if ("zh_TW".equalsIgnoreCase(languageCode)) {
                subject = "來自shorthub.TV - 訂閱取消確認";
                content = emailTemplateService.generateSubscriptionCancelledTemplateTW(userName, planName, cancellationTime, websiteUrl);
            } else {
                subject = "By shorthub.TV - Subscription Cancelled";
                content = emailTemplateService.generateSubscriptionCancelledTemplateEN(userName, planName, cancellationTime, websiteUrl);
            }

            log.info("[邮件服务] 邮件内容已生成. 主题: {}, 订阅ID: {}", subject, subscriptionId);
            this.sendEmail(userEmail, subject, content, subscriptionId);

        } catch (Exception e) {
            log.error("[邮件服务] 发送订阅取消邮件时出现未预期的异常。订阅ID: {}", subscriptionId, e);
        }
    }

    @Override
    public void sendUpcomingPaymentNotificationEmail(String userEmail, String userName, String planName, BigDecimal amount, String nextBillingDate, String languageCode, String websiteUrl, String cancelSubscriptionUrl, String subscriptionId) {
        log.info("[邮件服务] 开始处理提前扣费通知邮件发送流程。订阅ID: {}, 邮箱: {}", subscriptionId, userEmail);
        planName = "SUBSCRIPTION";
        try {
            String subject;
            String content;
            if ("zh_TW".equalsIgnoreCase(languageCode)) {
                subject = "來自shorthub.TV - 即將扣費提醒";
                content = emailTemplateService.generateUpcomingPaymentNotificationTemplateTW(userName, planName, amount, nextBillingDate, websiteUrl, cancelSubscriptionUrl);
            } else {
                subject = "By shorthub.TV - Upcoming Payment Reminder";
                content = emailTemplateService.generateUpcomingPaymentNotificationTemplateEN(userName, planName, amount, nextBillingDate, websiteUrl, cancelSubscriptionUrl);
            }

            log.info("[邮件服务] 提前扣费通知邮件内容已生成. 主题: {}, 订阅ID: {}", subject, subscriptionId);
            this.sendEmail(userEmail, subject, content, subscriptionId);

        } catch (Exception e) {
            log.error("[邮件服务] 发送提前扣费通知邮件时出现未预期的异常。订阅ID: {}", subscriptionId, e);
        }
    }
}
