package tv.shorthub.email.service;

import java.math.BigDecimal;
import tv.shorthub.email.dto.EmailTemplateRequest;

/**
 * 邮件模板服务接口
 */
public interface IEmailTemplateService {

    /**
     * 生成支付成功邮件模板（繁体）
     */
    String generatePaymentSuccessTemplateTW(String userName, String orderNo, BigDecimal amount, String paymentTime, String websiteUrl);

    /**
     * 生成支付成功邮件模板（英文）
     */
    String generatePaymentSuccessTemplateEN(String userName, String orderNo, BigDecimal amount, String paymentTime, String websiteUrl);

    /**
     * 生成订阅成功邮件模板（繁体）
     */
    String generateSubscriptionSuccessTemplateTW(String userName, String orderNo, String planName, BigDecimal amount, String paymentTime, String websiteUrl);

    /**
     * 生成订阅成功邮件模板（英文）
     */
    String generateSubscriptionSuccessTemplateEN(String userName, String orderNo, String planName, BigDecimal amount, String paymentTime, String websiteUrl);

    /**
     * 生成订阅取消邮件模板（繁体）
     * @param userName 用户名
     * @param planName 方案名称
     * @param cancellationTime 取消时间
     * @param websiteUrl 网站URL
     * @return 邮件HTML内容
     */
    String generateSubscriptionCancelledTemplateTW(String userName, String planName, String cancellationTime, String websiteUrl);

    /**
     * 生成订阅取消邮件模板（英文）
     * @param userName 用户名
     * @param planName 方案名称
     * @param cancellationTime 取消时间
     * @param websiteUrl 网站URL
     * @return 邮件HTML内容
     */
    String generateSubscriptionCancelledTemplateEN(String userName, String planName, String cancellationTime, String websiteUrl);

    /**
     * 生成订阅续期通知邮件模板（繁体）
     * @param userName 用户名
     * @param orderNo 订单号
     * @param amount 续期金额
     * @param paymentTime 支付时间
     * @param websiteUrl 网站URL
     * @param cancelSubscriptionUrl 取消订阅链接，为空则不显示取消订阅按钮
     * @return 邮件HTML内容
     */
    String generateSubscriptionRenewalTemplateTW(String userName, String orderNo, BigDecimal amount, String paymentTime, String websiteUrl, String cancelSubscriptionUrl);

    /**
     * 生成订阅续期通知邮件模板（英文）
     * @param userName 用户名
     * @param orderNo 订单号
     * @param amount 续期金额
     * @param paymentTime 支付时间
     * @param websiteUrl 网站URL
     * @param cancelSubscriptionUrl 取消订阅链接，为空则不显示取消订阅按钮
     * @return 邮件HTML内容
     */
    String generateSubscriptionRenewalTemplateEN(String userName, String orderNo, BigDecimal amount, String paymentTime, String websiteUrl, String cancelSubscriptionUrl);

    /**
     * 生成提前扣费通知邮件模板（繁体）
     * @param userName 用户名
     * @param planName 订阅计划名称
     * @param amount 即将扣费金额
     * @param nextBillingDate 下次扣费日期
     * @param websiteUrl 网站URL
     * @param cancelSubscriptionUrl 取消订阅链接，为空则不显示取消订阅按钮
     * @return 邮件HTML内容
     */
    String generateUpcomingPaymentNotificationTemplateTW(String userName, String planName, BigDecimal amount, String nextBillingDate, String websiteUrl, String cancelSubscriptionUrl);

    /**
     * 生成提前扣费通知邮件模板（英文）
     * @param userName 用户名
     * @param planName 订阅计划名称
     * @param amount 即将扣费金额
     * @param nextBillingDate 下次扣费日期
     * @param websiteUrl 网站URL
     * @param cancelSubscriptionUrl 取消订阅链接，为空则不显示取消订阅按钮
     * @return 邮件HTML内容
     */
    String generateUpcomingPaymentNotificationTemplateEN(String userName, String planName, BigDecimal amount, String nextBillingDate, String websiteUrl, String cancelSubscriptionUrl);
}
