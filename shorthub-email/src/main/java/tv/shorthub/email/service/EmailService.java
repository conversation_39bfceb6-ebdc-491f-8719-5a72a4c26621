package tv.shorthub.email.service;

import java.math.BigDecimal;
import tv.shorthub.email.dto.EmailTemplateRequest;

/**
 * 邮件服务接口
 * <AUTHOR>
 */
public interface EmailService {

    /**
     * 发送邮件 (底层通用方法)
     *
     * @param to 收件人
     * @param subject 主题
     * @param content 内容
     * @param referenceId 关联的ID（订单ID或订阅ID），用于幂等性控制和日志追溯
     */
    void sendEmail(String to, String subject, String content, String referenceId);

    /**
     * 发送邮件 (基于模板请求)
     *
     * @param request 邮件模板请求
     */
    void sendEmail(EmailTemplateRequest request);


    /**
     * 续期通知（带取消订阅链接）
     */
    void sendSubscriptionRenewalEmailWithCancelUrl(String userEmail, String userName, String orderNo, BigDecimal amount, String paymentTime, String languageCode, String websiteUrl, String cancelSubscriptionUrl);

    /**
     * 发送支付成功邮件
     */
    void sendPaymentSuccessEmail(String userEmail, String userName, String orderNo, BigDecimal amount, String paymentTime, String languageCode, String websiteUrl);

    /**
     * 发送订阅成功邮件
     */
    void sendSubscriptionSuccessEmail(String userEmail, String userName, String orderNo, String planName, BigDecimal amount, String paymentTime, String languageCode, String websiteUrl);


    /**
     * 发送订阅取消邮件
     *
     * @param userEmail 收件人邮箱
     * @param userName 用户名
     * @param planName 订阅方案名称
     * @param cancellationTime 取消时间
     * @param languageCode 语言代码 (e.g., "en-US", "zh_TW")
     * @param subscriptionId 关联的订阅ID，用于幂等性控制和日志追溯
     */
    void sendSubscriptionCancelledEmail(String userEmail, String userName, String planName, String cancellationTime, String languageCode, String subscriptionId, String websiteUrl);

    /**
     * 发送提前扣费通知邮件
     *
     * @param userEmail 收件人邮箱
     * @param userName 用户名
     * @param planName 订阅方案名称
     * @param amount 即将扣费金额
     * @param nextBillingDate 下次扣费日期
     * @param languageCode 语言代码 (e.g., "en-US", "zh_TW")
     * @param websiteUrl 网站URL
     * @param cancelSubscriptionUrl 取消订阅链接
     * @param subscriptionId 关联的订阅ID，用于幂等性控制和日志追溯
     */
    void sendUpcomingPaymentNotificationEmail(String userEmail, String userName, String planName, BigDecimal amount, String nextBillingDate, String languageCode, String websiteUrl, String cancelSubscriptionUrl, String subscriptionId);

}
