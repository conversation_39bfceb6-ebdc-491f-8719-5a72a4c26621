# Shorthub-TV短剧平台产品架构设计图

## 1. 系统概述

Shorthub-TV是一个现代化的短剧平台，支持多语种、多支付方式、多渠道分发的综合性视频内容平台。系统采用微服务架构，集成了视频处理、支付处理、广告投放、用户管理等多个核心业务模块。

## 2. 整体系统架构

```
┌─────────────────────────────────────────────────────────────────────────────────────┐
│                                   前端层 (Frontend Layer)                                │
├─────────────────────────────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐                      │
│  │   Web 客户端     │  │   管理后台      │  │   移动端应用     │                      │
│  │   (Vue.js)      │  │   (Vue.js)     │  │   (Mobile App)  │                      │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘                      │
└─────────────────────────────────────────────────────────────────────────────────────┘
                                        │
                                        │ HTTPS
                                        │
┌─────────────────────────────────────────────────────────────────────────────────────┐
│                               网关/负载均衡层 (Gateway Layer)                           │
├─────────────────────────────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐                      │
│  │   API Gateway   │  │  Load Balancer  │  │   CDN Service   │                      │
│  │   (Nginx/Kong)  │  │    (HAProxy)    │  │  (Cloudflare)   │                      │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘                      │
└─────────────────────────────────────────────────────────────────────────────────────┘
                                        │
                                        │
┌─────────────────────────────────────────────────────────────────────────────────────┐
│                                应用服务层 (Application Layer)                          │
├─────────────────────────────────────────────────────────────────────────────────────┤
│                                                                                     │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐                      │
│  │  管理端服务      │  │   API服务       │  │   事件服务      │                      │
│  │ shorthub-admin  │  │ shorthub-api    │  │ shorthub-event  │                      │
│  │   (Port 8080)   │  │   (Port 8081)   │  │                │                      │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘                      │
│                                                                                     │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐                      │
│  │   支付服务群     │  │   第三方集成     │  │   通知服务      │                      │
│  │                │  │                │  │                │                      │
│  │ • PayPal       │  │ • WeChat       │  │ • DingTalk     │                      │
│  │ • PayerMax     │  │ • Google Play  │  │ • Email        │                      │
│  │ • Airwallex    │  │ • Facebook     │  │                │                      │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘                      │
│                                                                                     │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐                      │
│  │   广告服务      │  │   统计服务      │  │   视频处理      │                      │
│  │  shorthub-ad    │  │shorthub-statistics│ │   (FFmpeg)     │                      │
│  │                │  │                │  │                │                      │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘                      │
│                                                                                     │
└─────────────────────────────────────────────────────────────────────────────────────┘
                                        │
                                        │
┌─────────────────────────────────────────────────────────────────────────────────────┐
│                               核心框架层 (Framework Layer)                             │
├─────────────────────────────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐                      │
│  │   核心框架      │  │   系统管理      │  │   通用工具      │                      │
│  │shorthub-framework│ │shorthub-system  │  │shorthub-common  │                      │
│  │                │  │                │  │                │                      │
│  │ • 权限控制      │  │ • 用户管理      │  │ • 工具类库      │                      │
│  │ • 会话管理      │  │ • 角色管理      │  │ • 常量定义      │                      │
│  │ • 安全认证      │  │ • 菜单管理      │  │ • 枚举类型      │                      │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘                      │
└─────────────────────────────────────────────────────────────────────────────────────┘
                                        │
                                        │
┌─────────────────────────────────────────────────────────────────────────────────────┐
│                               中间件层 (Middleware Layer)                             │
├─────────────────────────────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐                      │
│  │   缓存服务      │  │   消息队列      │  │   对象存储      │                      │
│  │     Redis       │  │ Cloudflare Queue│  │  Cloudflare R2  │                      │
│  │                │  │                │  │                │                      │
│  │ • 会话缓存      │  │ • 异步处理      │  │ • 视频存储      │                      │
│  │ • 数据缓存      │  │ • 事件传递      │  │ • 图片存储      │                      │
│  │ • 分布式锁      │  │ • 支付回调      │  │ • 文件存储      │                      │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘                      │
└─────────────────────────────────────────────────────────────────────────────────────┘
                                        │
                                        │
┌─────────────────────────────────────────────────────────────────────────────────────┐
│                                数据层 (Data Layer)                                  │
├─────────────────────────────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐                      │
│  │   关系数据库     │  │   数据访问层     │  │   数据备份      │                      │
│  │     MySQL       │  │   MyBatis Plus  │  │                │                      │
│  │                │  │                │  │ • 定时备份      │                      │
│  │ • 用户数据      │  │ • ORM映射       │  │ • 增量备份      │                      │
│  │ • 业务数据      │  │ • 分页查询      │  │ • 异地备份      │                      │
│  │ • 系统配置      │  │ • 事务管理      │  │                │                      │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘                      │
└─────────────────────────────────────────────────────────────────────────────────────┘
```

## 3. 核心业务模块详解

### 3.1 内容管理模块

```mermaid
graph TB
    A[剧目管理] --> B[剧集管理]
    B --> C[视频上传]
    C --> D[视频处理]
    D --> E[转码压缩]
    E --> F[格式转换]
    F --> G[存储分发]

    D --> D1[FFmpeg处理]
    D --> D2[GPU加速]
    D --> D3[多码率生成]

    F --> F1[MP4格式]
    F --> F2[HLS格式]
    F --> F3[自适应流]

    G --> G1[Cloudflare R2]
    G --> G2[CDN分发]
    G --> G3[地域优化]
```

**技术实现：**

- **视频处理**：使用FFmpeg进行视频压缩和转码
- **存储方案**：Cloudflare R2对象存储
- **分发优化**：CDN加速，支持多地域分发
- **格式支持**：MP4原始格式，HLS流媒体格式

### 3.2 用户管理模块

```mermaid
graph TB
    A[用户注册/登录] --> B[社交登录]
    A --> C[邮箱注册]

    B --> B1[Google登录]
    B --> B2[Facebook登录]
    B --> B3[微信登录]

    C --> D[用户认证]
    D --> E[权限管理]
    E --> F[会话管理]

    F --> F1[JWT Token]
    F --> F2[Redis缓存]
    F --> F3[多端同步]

    E --> E1[角色分配]
    E --> E2[权限控制]
    E --> E3[数据隔离]
```

**核心特性：**

- **多渠道登录**：支持Google、Facebook、微信等社交登录
- **权限体系**：基于RBAC的权限管理系统
- **会话管理**：JWT+Redis的分布式会话管理
- **多语言支持**：国际化用户体验

### 3.3 支付处理模块

```mermaid
graph TB
    A[支付请求] --> B[支付渠道选择]

    B --> C[PayPal支付]
    B --> D[PayerMax支付]
    B --> E[Airwallex支付]
    B --> F[Google Play支付]

    C --> C1[订单创建]
    C --> C2[支付执行]
    C --> C3[Webhook回调]

    D --> D1[加密签名]
    D --> D2[订单处理]
    D --> D3[状态同步]

    E --> E1[支付意图]
    E --> E2[3DS验证]
    E --> E3[结果处理]

    F --> F1[应用内购买]
    F --> F2[订阅管理]
    F --> F3[收据验证]

    C3 --> G[订单履行]
    D3 --> G
    E3 --> G
    F3 --> G

    G --> H[用户充值]
    G --> I[通知发送]
    G --> J[数据统计]
```

**支付特性：**

- **多支付渠道**：集成4大主流支付平台
- **安全保障**：RSA加密签名，webhook验证
- **订阅支持**：支持周期性订阅付费
- **实时通知**：钉钉、邮件实时通知支付状态

### 3.4 视频处理工作流

```mermaid
graph TB
    A[视频上传] --> B[格式检测]
    B --> C[压缩处理]

    C --> C1[GPU编码]
    C --> C2[CPU编码]
    C --> C3[自适应参数]

    C1 --> D[HLS转换]
    C2 --> D
    C3 --> D

    D --> D1[分片生成]
    D --> D2[M3U8索引]
    D --> D3[多码率支持]

    D1 --> E[存储上传]
    D2 --> E
    D3 --> E

    E --> F[URL签名]
    F --> G[CDN分发]
    G --> H[客户端播放]

    subgraph "后台任务"
        I[定时压缩]
        J[质量检测]
        K[存储优化]
    end
```

**处理特性：**

- **智能压缩**：根据文件大小自动选择压缩参数
- **GPU加速**：支持CUDA硬件加速编码
- **多格式输出**：同时支持MP4和HLS格式
- **自动化处理**：后台定时任务自动处理待压缩视频

### 3.5 广告投放模块

```mermaid
graph TB
    A[广告投放] --> B[平台选择]

    B --> C[Facebook Ads]
    B --> D[Google Ads]
    B --> E[TikTok Ads]

    C --> F[受众定位]
    D --> F
    E --> F

    F --> G[广告创建]
    G --> H[投放执行]
    H --> I[数据回传]

    I --> J[转化跟踪]
    J --> K[效果分析]
    K --> L[优化建议]

    subgraph "数据流"
        M[用户行为]
        N[付费数据]
        O[归因分析]
    end
```

## 4. 数据流架构

### 4.1 用户行为数据流

```
用户操作 → 前端埋点 → API网关 → 业务服务 → 消息队列 → 数据处理 → 存储分析
    ↓           ↓          ↓         ↓          ↓          ↓          ↓
  点击播放    事件收集    请求路由   业务逻辑   异步处理   数据清洗   统计报表
  用户充值    数据上报    负载均衡   权限校验   事件分发   归因计算   广告优化
  视频观看    实时传输    熔断限流   状态更新   批量处理   趋势分析   决策支持
```

### 4.2 支付处理数据流

```
支付请求 → 订单创建 → 第三方支付 → Webhook回调 → 订单履行 → 用户通知
    ↓          ↓          ↓           ↓           ↓          ↓
  参数校验   数据持久化   外部接口    异步处理    业务逻辑   消息发送
  渠道选择   状态管理     安全验证    队列处理    充值到账   钉钉通知
  金额计算   日志记录     签名校验    重试机制    统计更新   邮件通知
```

## 5. 技术栈详情

### 5.1 后端技术栈

| 技术分类     | 技术选型             | 版本    | 用途说明  |
| -------- | ---------------- | ----- | ----- |
| **框架**   | Spring Boot      | 3.3.0 | 主应用框架 |
| **安全**   | Spring Security  | 3.x   | 认证授权  |
| **数据库**  | MySQL            | 8.0+  | 主数据存储 |
| **ORM**  | MyBatis Plus     | 3.5.7 | 数据访问层 |
| **缓存**   | Redis            | 7.0+  | 分布式缓存 |
| **队列**   | Cloudflare Queue | -     | 消息队列  |
| **存储**   | Cloudflare R2    | -     | 对象存储  |
| **视频处理** | FFmpeg           | 6.0+  | 视频转码  |

### 5.2 前端技术栈

| 技术分类    | 技术选型         | 用途说明   |
| ------- | ------------ | ------ |
| **框架**  | Vue.js       | 前端应用框架 |
| **UI库** | Element UI   | 管理后台UI |
| **构建**  | Webpack/Vite | 前端构建工具 |
| **语言**  | TypeScript   | 类型安全   |

### 5.3 部署运维

| 技术分类   | 技术选型           | 用途说明   |
| ------ | -------------- | ------ |
| **容器** | Docker         | 应用容器化  |
| **编排** | Docker Compose | 本地开发环境 |
| **监控** | Alibaba ARMS   | 应用性能监控 |
| **日志** | 自定义日志          | 业务日志记录 |

## 6. 部署架构

### 6.1 生产环境部署

```
┌─────────────────────────────────────────────────────────────┐
│                        负载均衡层                            │
├─────────────────────────────────────────────────────────────┤
│  ┌──────────────┐    ┌──────────────┐    ┌──────────────┐   │
│  │   Nginx 1    │    │   Nginx 2    │    │   Nginx 3    │   │
│  │   (主节点)    │    │   (备节点)    │    │   (备节点)    │   │
│  └──────────────┘    └──────────────┘    └──────────────┘   │
└─────────────────────────────────────────────────────────────┘
                                │
                                │
┌─────────────────────────────────────────────────────────────┐
│                        应用服务层                            │
├─────────────────────────────────────────────────────────────┤
│  ┌──────────────┐    ┌──────────────┐    ┌──────────────┐   │
│  │shorthub-admin│    │shorthub-api  │    │shorthub-api  │   │
│  │   :8080      │    │   :8081      │    │   :8082      │   │
│  └──────────────┘    └──────────────┘    └──────────────┘   │
└─────────────────────────────────────────────────────────────┘
                                │
                                │
┌─────────────────────────────────────────────────────────────┐
│                        数据存储层                            │
├─────────────────────────────────────────────────────────────┤
│  ┌──────────────┐    ┌──────────────┐    ┌──────────────┐   │
│  │  MySQL主库   │    │  MySQL从库   │    │   Redis集群   │   │
│  │   (写入)     │    │   (只读)     │    │   (缓存)     │   │
│  └──────────────┘    └──────────────┘    └──────────────┘   │
└─────────────────────────────────────────────────────────────┘
```

### 6.2 容器化部署

```dockerfile
# 多阶段构建
FROM openjdk:21-slim as builder
WORKDIR /app
COPY . .
RUN ./mvnw clean package -DskipTests

FROM openjdk:21-slim
WORKDIR /app
COPY --from=builder /app/shorthub-admin/target/*.jar app.jar
EXPOSE 8080
CMD ["java", "-jar", "app.jar"]
```

## 7. 安全架构

### 7.1 多层安全防护

```
┌─────────────────────────────────────────────────────────────┐
│                      网络安全层                              │
│  • WAF防护 • DDoS防护 • IP白名单 • SSL证书                  │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                      应用安全层                              │
│  • JWT认证 • RBAC权限 • 接口限流 • 参数校验                  │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                      数据安全层                              │
│  • 数据加密 • SQL注入防护 • 敏感信息脱敏 • 审计日志          │
└─────────────────────────────────────────────────────────────┘
```

### 7.2 支付安全

- **签名验证**：所有支付请求使用RSA签名验证
- **Webhook验证**：支付回调使用官方SDK验证
- **敏感数据加密**：支付相关敏感信息加密存储
- **PCI DSS合规**：符合支付卡行业数据安全标准

## 8. 监控与运维

### 8.1 监控体系

```
┌─────────────────────────────────────────────────────────────┐
│                      业务监控                                │
│  • 支付成功率 • 用户活跃度 • 视频播放量 • 转换率            │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                      应用监控                                │
│  • 接口响应时间 • 错误率 • QPS • 内存使用率                  │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                      基础设施监控                            │
│  • CPU使用率 • 内存使用率 • 磁盘空间 • 网络流量              │
└─────────────────────────────────────────────────────────────┘
```

### 8.2 告警机制

- **实时告警**：通过钉钉机器人实时通知异常
- **分级告警**：根据严重程度分级处理
- **自动恢复**：部分异常支持自动恢复机制

## 9. 扩展性设计

### 9.1 水平扩展

- **无状态设计**：应用服务无状态，支持水平扩展
- **数据库分库分表**：支持按用户ID或时间分片
- **缓存集群**：Redis集群支持数据分片
- **CDN加速**：静态资源全球CDN分发

### 9.2 功能扩展

- **插件化架构**：支付渠道、通知方式支持插件化扩展
- **多租户支持**：系统支持多应用、多租户部署
- **国际化**：完整的多语言、多时区支持

## 10. 性能优化

### 10.1 数据库优化

- **读写分离**：主从复制，读写分离提升性能
- **索引优化**：核心查询字段建立合适索引
- **连接池**：Druid连接池优化数据库连接

### 10.2 缓存策略

- **多级缓存**：应用缓存+Redis缓存+CDN缓存
- **缓存预热**：热点数据提前加载到缓存
- **缓存穿透防护**：布隆过滤器防止缓存穿透

### 10.3 视频优化

- **智能转码**：根据网络条件自适应码率
- **边缘缓存**：视频文件CDN边缘缓存
- **预加载机制**：智能预加载下一集内容

---

## 总结

Shorthub-TV短剧平台是一个技术先进、功能完整的现代化视频平台。系统集成了完整的支付体系、智能视频处理、精准广告投放等核心功能。通过模块化设计和云原生技术，系统具备了良好的可扩展性和高可用性，能够支撑大规模用户和业务增长。 