# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

# Shorthub-TV 短剧平台架构

## 系统概述
Shorthub-TV 短剧平台，支持多语种、多支付、多渠道分发。基于 Spring Boot 微服务架构。

## 核心模块
- **管理端**: shorthub-admin (8080)
- **API服务**: shorthub-api (8081)  
- **广告服务**: shorthub-ad
- **统计服务**: shorthub-statistics
- **基础框架**: shorthub-framework/system/common

## 技术栈
| 分类 | 技术 | 版本 |
|---|---|---|
| 框架 | Spring Boot | 3.3.0 |
| 数据库 | MySQL | 8.0+ |
| ORM | MyBatis Plus | 3.5.7 |
| 缓存 | Redis | 7.0+ |
| 存储 | Cloudflare R2 | - |
| 队列 | Cloudflare Queue | - |
| 视频 | FFmpeg | 6.0+ |

## 项目架构

### 多模块 Maven 项目结构
- **父项目**: shorthub (pom packaging)
- **主应用模块**: shorthub-admin (8080), shorthub-api (8081)
- **业务模块**: shorthub-ad, shorthub-statistics, shorthub-crypto
- **支付模块**: shorthub-paypal, shorthub-payermax, shorthub-airwallex, shorthub-googleplay
- **通信模块**: shorthub-dingtalk, shorthub-email, shorthub-wechat
- **基础模块**: shorthub-framework, shorthub-system, shorthub-common
- **工具模块**: shorthub-generator, shorthub-volcengine

### 关键配置文件
- **application.yml**: 主配置文件，包含服务端口、数据库、Redis等配置
- **application-{env}.yml**: 环境特定配置 (local/dev/prod)
- **startup.sh**: 服务启动脚本，包含JVM参数和健康检查

### MyBatis-Plus 数据访问层
```java
// 通用 Mapper 接口 (所有业务实体都扩展此接口)
public interface CommonMapper<T> extends BaseMapper<T> {
    T getSummary(T entity);
    List<T> summary(SummaryRequest request);
    T allSummary(SummaryRequest request);
    int batchInsertOrUpdate(List<T> list);
}

// Mapper XML 文件位置
shorthub-system/src/main/resources/mapper/
├── app/                    # 应用业务相关
├── statistics/            # 统计数据相关  
├── crypto/               # 加密货币支付
├── pay/                  # 支付相关
└── system/              # 系统管理相关
```

### Spring Boot 3.3.0 + Java 21
- **虚拟线程**: 部分模块启用虚拟线程支持
- **GraalVM**: 支持原生镜像编译
- **安全配置**: Spring Security + JWT 认证
- **多环境配置**: local/dev/prod 环境隔离

## 核心功能

### 内容管理
- **视频处理**: FFmpeg转码、GPU加速、多码率
- **存储分发**: Cloudflare R2 + CDN
- **格式支持**: MP4、HLS自适应流

### 用户管理
- **多渠道登录**: Google、Facebook、微信
- **权限体系**: RBAC角色权限
- **会话管理**: JWT + Redis

### 支付处理
- **支付渠道**: PayPal、PayerMax、Airwallex、Google Play
- **安全保障**: RSA签名、Webhook验证
- **通知服务**: 钉钉、邮件实时通知

### 广告投放
- **投放平台**: Facebook、Google、TikTok Ads
- **转化跟踪**: 用户行为分析、效果优化

## 部署架构
- **负载均衡**: Nginx 主备集群
- **应用服务**: shorthub-admin(8080) + shorthub-api(8081)
- **数据存储**: MySQL 主从 + Redis 集群

## 基础框架公共组件

### shorthub-framework (核心框架)
- **安全认证**: `SecurityConfig` Spring Security配置、JWT Token验证
- **AOP切面**: `LogAspect` 日志记录、`DataSourceAspect` 数据源切换、`RateLimiterAspect` 限流
- **拦截器**: `RepeatSubmitInterceptor` 防重复提交、`DataSourceInterceptor` 数据源拦截
- **服务层**: `TokenService` JWT令牌管理、`SysLoginService` 登录服务、`PermissionService` 权限服务
- **配置类**: `RedisConfig` Redis配置、`ThreadPoolConfig` 线程池配置、`DruidConfig` 数据源配置

### shorthub-system (系统管理)
- **用户管理**: `SysUser` 用户实体、`SysUserMapper` 用户数据访问、用户CRUD操作
- **角色权限**: `SysRole` 角色管理、`SysMenu` 菜单权限、`SysRoleMenu` 角色菜单关联
- **部门管理**: `SysDept` 部门实体、部门树形结构管理
- **配置管理**: `SysConfig` 系统配置、`SysDictData` 字典数据管理
- **日志管理**: `SysOperLog` 操作日志、`SysLogininfor` 登录日志
- **业务实体**: App相关业务实体(用户、剧集、充值、统计等)

### shorthub-common (公共工具)
- **核心域**: `AjaxResult` 统一响应结果、`BaseEntity` 基础实体、`R` 通用返回结果
- **Redis缓存**: `RedisCache` Redis操作工具、缓存管理、分布式锁
- **工具类**: `StringUtils` 字符串工具、`ServletUtils` Servlet工具、`SecurityUtils` 安全工具
- **注解**: `@Log` 日志注解、`@DataScope` 数据权限、`@RateLimiter` 限流注解、`@RepeatSubmit` 防重复提交
- **枚举类**: `BusinessType` 业务类型、`HttpMethod` HTTP方法、`PayStatus` 支付状态
- **异常处理**: `ServiceException` 服务异常、`GlobalException` 全局异常、文件上传异常
- **分页**: `PageDomain` 分页域、`TableDataInfo` 表格数据、`TableSupport` 分页支持
- **缓存服务**: `LocalObjectCache` 本地对象缓存、`VideoUrlCacheService` 视频URL缓存

### 常用公共方法
```java
// Redis 缓存操作
redisCache.setCacheObject(key, value, timeout, TimeUnit.MINUTES);
Object obj = redisCache.getCacheObject(key);

// 统一响应结果
return AjaxResult.success(data);
return AjaxResult.error("操作失败");
return R.ok(data);

// JWT Token 管理
String token = tokenService.createToken(loginUser);
LoginUser loginUser = tokenService.getLoginUser(request);

// 字符串工具
StringUtils.isNotEmpty(str);
StringUtils.format("用户{}登录成功", username);

// 权限验证
SecurityUtils.getLoginUser();
SecurityUtils.hasPermi("system:user:list");
```
### 数据权限和流量区分
-- 权限控制条件
-- ADMIN: 所有 appid 数据
-- 主账号: 指定 appid 下所有数据 
-- 子账号: 指定 appid 下自己投放的 tfid 数据 (create_by)
