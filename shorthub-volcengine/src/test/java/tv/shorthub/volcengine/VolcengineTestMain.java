package tv.shorthub.volcengine;

import com.volcengine.ApiClient;
import com.volcengine.sign.Credentials;
import com.volcengine.vod20250101.Vod20250101Api;
import com.volcengine.vod20250101.model.*;
import lombok.extern.slf4j.Slf4j;

/**
 * 火山引擎VOD简单测试
 * 直接运行main方法测试连接和授权
 */
@Slf4j
public class VolcengineTestMain {
    
    public static void main(String[] args) {
        System.out.println("开始测试火山引擎VOD服务连接...");
        
        try {
            // 配置信息
            String ak = "AKLTODA2ZTA2MmEwOTA4NDBmNDg2OTZhYjU5ZDk1YzRmYTQ";
            String sk = "T0dSa1l6Wm1OREV5TlRKak5EUXlOV0k1TURKbFlqbGhNbVZqWVRnNE9Eaw==";
            String region = "cn-north-1";
            
            System.out.println("配置信息:");
            System.out.println("- AK: " + ak.substring(0, 8) + "****");
            System.out.println("- Region: " + region);
            
            // 创建API客户端
            ApiClient apiClient = new ApiClient()
                    .setCredentials(Credentials.getCredentials(ak, sk))
                    .setRegion(region);
            
            System.out.println("✅ API客户端创建成功");
            
            // 创建VOD API
            Vod20250101Api vodApi = new Vod20250101Api(apiClient);
            System.out.println("✅ VOD API创建成功");
            
            // 构建测试请求
            StartExecutionRequest request = new StartExecutionRequest();
            
            // 输入参数
            InputForStartExecutionInput input = new InputForStartExecutionInput();
            input.setType("Vid");
            input.setVid("test_video_id_123"); // 测试视频ID
            request.setInput(input);
            
            // 操作参数
            OperationForStartExecutionInput operation = new OperationForStartExecutionInput();
            operation.setType("Task");
            
            TaskForStartExecutionInput task = new TaskForStartExecutionInput();
            task.setType("Highlight");
            
            HighlightForStartExecutionInput highlight = new HighlightForStartExecutionInput();
            task.setHighlight(highlight);
            
            operation.setTask(task);
            request.setOperation(operation);
            
            // 控制参数
            ControlForStartExecutionInput control = new ControlForStartExecutionInput();
            control.setClientToken("test_token_" + System.currentTimeMillis());
            request.setControl(control);
            
            System.out.println("🚀 开始调用StartExecution API...");
            
            // 调用API
            StartExecutionResponse response = vodApi.startExecution(request);
            
            System.out.println("✅ API调用成功!");
            System.out.println("响应信息:");
            if (response.getResponseMetadata() != null) {
                System.out.println("- RequestId: " + response.getResponseMetadata().getRequestId());
            }
            System.out.println("- Response对象: " + response.toString());
            
            System.out.println("🎉 火山引擎VOD服务连接测试成功!");
            
        } catch (Exception e) {
            System.err.println("❌ 测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}