package tv.shorthub.volcengine;

import com.volcengine.vod20250101.model.*;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

/**
 * AI 翻译参数构建测试
 * 验证请求参数的正确构建，不依赖实际的 API 调用
 */
@Slf4j
public class AITranslationParameterTest {

    /**
     * 测试：AI 翻译请求参数构建
     */
    @Test
    public void testAITranslationRequestBuilder() {
        try {
            log.info("开始测试 AI 翻译请求参数构建...");

            // 测试参数
            String vid = "test_video_id_123";
            String sourceLanguage = "zh";
            String targetLanguage = "en";
            String clientToken = "test_token_" + System.currentTimeMillis();

            // 构建请求对象
            StartExecutionRequest request = new StartExecutionRequest();

            // 输入参数 - 指定视频ID
            InputForStartExecutionInput input = new InputForStartExecutionInput();
            input.setType("Vid");
            input.setVid(vid);
            request.setInput(input);

            // 操作参数 - AI翻译任务
            OperationForStartExecutionInput operation = new OperationForStartExecutionInput();
            operation.setType("Task");

            TaskForStartExecutionInput task = new TaskForStartExecutionInput();
            task.setType("AITranslation");

            operation.setTask(task);
            request.setOperation(operation);

            // 控制参数
            ControlForStartExecutionInput control = new ControlForStartExecutionInput();
            control.setClientToken(clientToken);
            request.setControl(control);

            // 验证参数构建
            log.info("✅ 请求参数构建成功");
            log.info("- Input Type: {}", request.getInput().getType());
            log.info("- Input Vid: {}", request.getInput().getVid());
            log.info("- Operation Type: {}", request.getOperation().getType());
            log.info("- Task Type: {}", request.getOperation().getTask().getType());
            log.info("- Client Token: {}", request.getControl().getClientToken());

            // 检查可用的方法
            log.info("🔍 检查 TaskForStartExecutionInput 可用方法:");
            log.info("- task.getType(): {}", task.getType());
            
            // 尝试查看是否有其他设置 AI 翻译参数的方法
            // 这里可以通过反射或直接调用来探索可用的方法
            
            log.info("🎉 AI 翻译请求参数构建测试完成");

        } catch (Exception e) {
            log.error("AI 翻译请求参数构建测试失败", e);
        }
    }

    /**
     * 测试：探索 TaskForStartExecutionInput 的可用方法
     */
    @Test
    public void testTaskInputMethods() {
        try {
            log.info("开始探索 TaskForStartExecutionInput 的可用方法...");

            TaskForStartExecutionInput task = new TaskForStartExecutionInput();
            task.setType("AITranslation");

            // 通过反射查看可用的方法
            Class<?> taskClass = task.getClass();
            log.info("TaskForStartExecutionInput 类名: {}", taskClass.getName());
            
            log.info("可用的方法:");
            for (var method : taskClass.getMethods()) {
                if (method.getName().startsWith("set") || method.getName().startsWith("get")) {
                    log.info("- {}", method.getName());
                }
            }

            // 检查是否有字段可以设置翻译参数
            log.info("可用的字段:");
            for (var field : taskClass.getDeclaredFields()) {
                log.info("- {} ({})", field.getName(), field.getType().getSimpleName());
            }

        } catch (Exception e) {
            log.error("探索 TaskForStartExecutionInput 方法失败", e);
        }
    }

    /**
     * 测试：不同任务类型的参数设置
     */
    @Test
    public void testDifferentTaskTypes() {
        try {
            log.info("开始测试不同任务类型的参数设置...");

            // 测试 Highlight 任务（已知可用）
            TaskForStartExecutionInput highlightTask = new TaskForStartExecutionInput();
            highlightTask.setType("Highlight");
            
            HighlightForStartExecutionInput highlight = new HighlightForStartExecutionInput();
            highlightTask.setHighlight(highlight);
            
            log.info("✅ Highlight 任务设置成功");

            // 测试 AITranslation 任务
            TaskForStartExecutionInput aiTask = new TaskForStartExecutionInput();
            aiTask.setType("AITranslation");
            
            // 检查是否有类似 setAiTranslation 的方法
            try {
                var method = aiTask.getClass().getMethod("setAiTranslation", Object.class);
                log.info("✅ 找到 setAiTranslation 方法: {}", method);
            } catch (NoSuchMethodException e) {
                log.warn("⚠️ 未找到 setAiTranslation 方法");
            }

            // 检查是否有其他相关方法
            for (var method : aiTask.getClass().getMethods()) {
                if (method.getName().toLowerCase().contains("translation") || 
                    method.getName().toLowerCase().contains("ai")) {
                    log.info("🔍 发现相关方法: {}", method.getName());
                }
            }

        } catch (Exception e) {
            log.error("测试不同任务类型失败", e);
        }
    }
}
