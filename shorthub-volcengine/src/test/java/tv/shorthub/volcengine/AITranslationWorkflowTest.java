package tv.shorthub.volcengine;

import com.volcengine.vod20250101.model.StartExecutionResponse;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.context.ActiveProfiles;
import tv.shorthub.volcengine.service.vod.VolcengineVodService;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;

/**
 * AI 翻译工作流测试
 * 演示完整的媒资上传和 AI 翻译流程
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("local")
public class AITranslationWorkflowTest {

    @Autowired
    private VolcengineVodService volcengineVodService;

    /**
     * 测试：提交 AI 翻译工作流（需要已有的 Vid）
     */
    @Test
    public void testSubmitAITranslationWorkflow() {
        try {
            // 使用已有的视频ID进行测试
            String vid = "test_video_id_123";
            String sourceLanguage = "zh"; // 中文
            String targetLanguage = "en"; // 英文
            String clientToken = "test_ai_translation_" + System.currentTimeMillis();

            log.info("开始测试 AI 翻译工作流...");
            log.info("测试参数 - vid: {}, {} -> {}", vid, sourceLanguage, targetLanguage);

            StartExecutionResponse response = volcengineVodService.submitAITranslationWorkflow(
                    vid, sourceLanguage, targetLanguage, clientToken);

            log.info("AI 翻译工作流提交成功!");
            if (response.getResponseMetadata() != null) {
                log.info("RequestId: {}", response.getResponseMetadata().getRequestId());
            }
            log.info("Response: {}", response.toString());

        } catch (Exception e) {
            log.error("AI 翻译工作流测试失败", e);
            // 在测试环境中，这可能是预期的，因为需要真实的 Vid
        }
    }

    /**
     * 测试：完整的上传和翻译流程（需要真实视频文件）
     */
    @Test
    public void testUploadAndTranslate() {
        try {
            // 创建模拟的视频文件
            String testVideoPath = "src/test/resources/test_video.mp4";
            File testFile = new File(testVideoPath);
            
            if (!testFile.exists()) {
                log.warn("测试视频文件不存在: {}", testVideoPath);
                log.info("请将测试视频文件放置在: {}", testFile.getAbsolutePath());
                return;
            }

            // 创建 MockMultipartFile
            FileInputStream fis = new FileInputStream(testFile);
            MockMultipartFile multipartFile = new MockMultipartFile(
                    "file", 
                    testFile.getName(), 
                    "video/mp4", 
                    fis
            );

            log.info("开始测试完整的上传和翻译流程...");
            
            StartExecutionResponse response = volcengineVodService.uploadAndTranslate(
                    multipartFile, "zh", "en");

            log.info("上传和翻译流程完成!");
            log.info("RequestId: {}", response.getResponseMetadata().getRequestId());
            
        } catch (IOException e) {
            log.error("文件读取失败", e);
        } catch (Exception e) {
            log.error("上传和翻译流程测试失败", e);
        }
    }

    /**
     * 测试：从本地文件上传和翻译
     */
    @Test
    public void testUploadLocalFileAndTranslate() {
        try {
            String localFilePath = "src/test/resources/test_video.mp4";
            File testFile = new File(localFilePath);
            
            if (!testFile.exists()) {
                log.warn("测试视频文件不存在: {}", localFilePath);
                log.info("请将测试视频文件放置在: {}", testFile.getAbsolutePath());
                return;
            }

            log.info("开始测试本地文件上传和翻译流程...");
            
            StartExecutionResponse response = volcengineVodService.uploadLocalFileAndTranslate(
                    localFilePath, "zh", "en");

            log.info("本地文件上传和翻译流程完成!");
            log.info("RequestId: {}", response.getResponseMetadata().getRequestId());
            
        } catch (Exception e) {
            log.error("本地文件上传和翻译流程测试失败", e);
        }
    }

    /**
     * 测试：仅上传视频获取 Vid
     */
    @Test
    public void testUploadVideoOnly() {
        try {
            String testVideoPath = "src/test/resources/test_video.mp4";
            File testFile = new File(testVideoPath);
            
            if (!testFile.exists()) {
                log.warn("测试视频文件不存在: {}", testVideoPath);
                return;
            }

            log.info("开始测试仅上传视频...");
            
            String vid = volcengineVodService.uploadLocalFileAndGetVid(testVideoPath);

            log.info("视频上传成功，获得 Vid: {}", vid);
            
            // 可以保存这个 Vid 用于后续的翻译测试
            
        } catch (Exception e) {
            log.error("视频上传测试失败", e);
        }
    }

    /**
     * 测试：分步骤的上传流程
     */
    @Test
    public void testStepByStepUpload() {
        try {
            String fileName = "test_video.mp4";
            Long fileSize = 1024L * 1024L; // 1MB

            log.info("开始测试分步骤上传流程...");
            
            // 1. 申请上传
            log.info("步骤1: 申请上传...");
            var applyResponse = volcengineVodService.applyUpload(fileName, fileSize);
            String sessionKey = applyResponse.getResult().getSessionKey();
            log.info("申请上传成功，SessionKey: {}", sessionKey);

            // 2. 这里应该上传文件到指定地址（需要实现具体的上传逻辑）
            log.info("步骤2: 上传文件到火山引擎...");
            // uploadFileToVolcengine(file, uploadAddress, sessionKey);

            // 3. 确认上传
            log.info("步骤3: 确认上传...");
            var commitResponse = volcengineVodService.commitUpload(sessionKey);
            String vid = commitResponse.getResult().getVid();
            log.info("确认上传成功，Vid: {}", vid);
            
        } catch (Exception e) {
            log.error("分步骤上传流程测试失败", e);
        }
    }
}
