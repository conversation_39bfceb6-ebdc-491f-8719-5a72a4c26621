package tv.shorthub.volcengine.service.vod;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import tv.shorthub.volcengine.exception.VolcengineException;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.util.Map;

/**
 * 火山引擎文件上传服务
 * 负责实际的文件上传到火山引擎指定地址
 */
@Slf4j
@Service
public class VolcengineUploadService {

    private final RestTemplate restTemplate;

    public VolcengineUploadService() {
        this.restTemplate = new RestTemplate();
    }

    /**
     * 上传文件到火山引擎指定地址
     * 
     * @param file 要上传的文件
     * @param uploadAddress 上传地址信息（JSON格式）
     * @param sessionKey 会话密钥
     */
    public void uploadFile(File file, String uploadAddress, String sessionKey) {
        try {
            log.info("开始上传文件到火山引擎 - fileName: {}, fileSize: {}", 
                    file.getName(), file.length());

            // 解析上传地址信息
            UploadAddressInfo addressInfo = parseUploadAddress(uploadAddress);
            
            // 读取文件内容
            byte[] fileContent = Files.readAllBytes(file.toPath());
            
            // 构建请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.setContentLength(fileContent.length);
            
            // 添加火山引擎要求的认证头
            if (addressInfo.getHeaders() != null) {
                addressInfo.getHeaders().forEach(headers::add);
            }
            
            // 创建请求实体
            HttpEntity<byte[]> requestEntity = new HttpEntity<>(fileContent, headers);
            
            // 发送 PUT 请求上传文件
            ResponseEntity<String> response = restTemplate.exchange(
                    addressInfo.getUploadUrl(),
                    HttpMethod.PUT,
                    requestEntity,
                    String.class
            );
            
            // 检查响应状态
            if (response.getStatusCode().is2xxSuccessful()) {
                log.info("文件上传到火山引擎成功 - statusCode: {}", response.getStatusCode());
            } else {
                throw new VolcengineException("文件上传失败，状态码: " + response.getStatusCode());
            }
            
        } catch (IOException e) {
            log.error("读取文件失败", e);
            throw new VolcengineException("读取文件失败: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("上传文件到火山引擎失败", e);
            throw new VolcengineException("上传文件到火山引擎失败: " + e.getMessage(), e);
        }
    }

    /**
     * 解析上传地址信息
     * 火山引擎返回的 uploadAddress 通常是 JSON 格式，包含上传 URL 和必要的 headers
     */
    private UploadAddressInfo parseUploadAddress(String uploadAddress) {
        try {
            // TODO: 根据火山引擎实际返回的格式解析
            // 这里需要根据火山引擎 API 文档中的具体格式来实现
            
            // 示例格式（需要根据实际情况调整）:
            // {
            //   "uploadUrl": "https://tos-s3-cn-beijing.volces.com/...",
            //   "headers": {
            //     "Authorization": "...",
            //     "x-tos-acl": "private"
            //   }
            // }
            
            log.info("解析上传地址: {}", uploadAddress);
            
            // 简化实现：假设 uploadAddress 就是直接的 URL
            // 实际使用时需要根据火山引擎的具体格式进行 JSON 解析
            UploadAddressInfo info = new UploadAddressInfo();
            info.setUploadUrl(uploadAddress);
            
            return info;
            
        } catch (Exception e) {
            log.error("解析上传地址失败", e);
            throw new VolcengineException("解析上传地址失败: " + e.getMessage(), e);
        }
    }

    /**
     * 上传地址信息
     */
    public static class UploadAddressInfo {
        private String uploadUrl;
        private Map<String, String> headers;

        public String getUploadUrl() {
            return uploadUrl;
        }

        public void setUploadUrl(String uploadUrl) {
            this.uploadUrl = uploadUrl;
        }

        public Map<String, String> getHeaders() {
            return headers;
        }

        public void setHeaders(Map<String, String> headers) {
            this.headers = headers;
        }
    }

    /**
     * 上传进度回调接口
     */
    public interface UploadProgressCallback {
        void onProgress(long bytesUploaded, long totalBytes);
    }

    /**
     * 带进度回调的文件上传
     * 
     * @param file 要上传的文件
     * @param uploadAddress 上传地址信息
     * @param sessionKey 会话密钥
     * @param progressCallback 进度回调
     */
    public void uploadFileWithProgress(File file, String uploadAddress, String sessionKey, 
                                     UploadProgressCallback progressCallback) {
        try {
            log.info("开始带进度的文件上传 - fileName: {}, fileSize: {}", 
                    file.getName(), file.length());

            // 解析上传地址信息
            UploadAddressInfo addressInfo = parseUploadAddress(uploadAddress);
            
            // 读取文件内容
            byte[] fileContent = Files.readAllBytes(file.toPath());
            long totalBytes = fileContent.length;
            
            // 构建请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.setContentLength(totalBytes);
            
            // 添加火山引擎要求的认证头
            if (addressInfo.getHeaders() != null) {
                addressInfo.getHeaders().forEach(headers::add);
            }
            
            // 创建请求实体
            HttpEntity<byte[]> requestEntity = new HttpEntity<>(fileContent, headers);
            
            // 模拟进度回调
            if (progressCallback != null) {
                progressCallback.onProgress(0, totalBytes);
            }
            
            // 发送 PUT 请求上传文件
            ResponseEntity<String> response = restTemplate.exchange(
                    addressInfo.getUploadUrl(),
                    HttpMethod.PUT,
                    requestEntity,
                    String.class
            );
            
            // 上传完成回调
            if (progressCallback != null) {
                progressCallback.onProgress(totalBytes, totalBytes);
            }
            
            // 检查响应状态
            if (response.getStatusCode().is2xxSuccessful()) {
                log.info("带进度的文件上传成功 - statusCode: {}", response.getStatusCode());
            } else {
                throw new VolcengineException("文件上传失败，状态码: " + response.getStatusCode());
            }
            
        } catch (IOException e) {
            log.error("读取文件失败", e);
            throw new VolcengineException("读取文件失败: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("带进度的文件上传失败", e);
            throw new VolcengineException("带进度的文件上传失败: " + e.getMessage(), e);
        }
    }

    /**
     * 验证文件是否适合上传
     * 
     * @param file 要验证的文件
     * @return 是否通过验证
     */
    public boolean validateFile(File file) {
        if (!file.exists()) {
            log.error("文件不存在: {}", file.getAbsolutePath());
            return false;
        }
        
        if (!file.isFile()) {
            log.error("不是文件: {}", file.getAbsolutePath());
            return false;
        }
        
        if (file.length() == 0) {
            log.error("文件为空: {}", file.getAbsolutePath());
            return false;
        }
        
        // 检查文件大小限制（例如：最大 5GB）
        long maxFileSize = 5L * 1024 * 1024 * 1024; // 5GB
        if (file.length() > maxFileSize) {
            log.error("文件过大: {} bytes, 最大允许: {} bytes", file.length(), maxFileSize);
            return false;
        }
        
        return true;
    }
}
