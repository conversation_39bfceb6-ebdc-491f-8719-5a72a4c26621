package tv.shorthub.volcengine.service.translation;

import com.volcengine.ApiException;
import com.volcengine.vod20250101.Vod20250101Api;
import com.volcengine.vod20250101.model.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tv.shorthub.volcengine.config.VolcengineProperties;
import tv.shorthub.volcengine.exception.VolcengineException;

import java.util.HashMap;
import java.util.Map;

/**
 * AI 翻译服务
 * 专门处理火山引擎 VOD 的 AI 翻译工作流
 * 参考：https://www.volcengine.com/docs/4/1584290
 */
@Slf4j
@Service
public class AITranslationService {

    @Autowired
    private Vod20250101Api vod20250101Api;

    @Autowired
    private VolcengineProperties properties;

    /**
     * 支持的语言映射
     */
    private static final Map<String, String> SUPPORTED_LANGUAGES = new HashMap<>();
    
    static {
        SUPPORTED_LANGUAGES.put("zh", "中文");
        SUPPORTED_LANGUAGES.put("en", "英文");
        SUPPORTED_LANGUAGES.put("ja", "日文");
        SUPPORTED_LANGUAGES.put("ko", "韩文");
        SUPPORTED_LANGUAGES.put("fr", "法文");
        SUPPORTED_LANGUAGES.put("de", "德文");
        SUPPORTED_LANGUAGES.put("es", "西班牙文");
        SUPPORTED_LANGUAGES.put("ru", "俄文");
        SUPPORTED_LANGUAGES.put("it", "意大利文");
        SUPPORTED_LANGUAGES.put("pt", "葡萄牙文");
        SUPPORTED_LANGUAGES.put("ar", "阿拉伯文");
        SUPPORTED_LANGUAGES.put("th", "泰文");
        SUPPORTED_LANGUAGES.put("vi", "越南文");
        SUPPORTED_LANGUAGES.put("hi", "印地文");
    }

    /**
     * 提交 AI 翻译工作流
     * 
     * @param vid 视频ID
     * @param sourceLanguage 源语言代码
     * @param targetLanguage 目标语言代码
     * @param clientToken 客户端令牌（可选）
     * @return 工作流执行响应
     */
    public StartExecutionResponse submitTranslationWorkflow(String vid, String sourceLanguage, 
                                                          String targetLanguage, String clientToken) {
        try {
            // 验证参数
            validateParameters(vid, sourceLanguage, targetLanguage);

            // 构建请求对象
            StartExecutionRequest request = buildTranslationRequest(vid, sourceLanguage, targetLanguage, clientToken);

            // 调用API
            log.info("提交AI翻译工作流 - vid: {}, {} -> {}", vid, sourceLanguage, targetLanguage);

            StartExecutionResponse response = vod20250101Api.startExecution(request);

            log.info("AI翻译工作流提交成功 - requestId: {}", 
                    response.getResponseMetadata() != null ? response.getResponseMetadata().getRequestId() : "unknown");

            return response;

        } catch (ApiException e) {
            log.error("AI翻译工作流提交失败: {}", e.getResponseBody(), e);
            throw new VolcengineException("AI翻译工作流提交失败: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("AI翻译工作流提交异常", e);
            throw new VolcengineException("AI翻译工作流提交异常: " + e.getMessage(), e);
        }
    }

    /**
     * 构建 AI 翻译请求
     */
    private StartExecutionRequest buildTranslationRequest(String vid, String sourceLanguage, 
                                                        String targetLanguage, String clientToken) {
        // 构建请求对象
        StartExecutionRequest request = new StartExecutionRequest();

        // 输入参数 - 指定视频ID
        InputForStartExecutionInput input = new InputForStartExecutionInput();
        input.setType("Vid");
        input.setVid(vid);
        request.setInput(input);

        // 操作参数 - AI翻译任务
        OperationForStartExecutionInput operation = new OperationForStartExecutionInput();
        operation.setType("Task");

        TaskForStartExecutionInput task = new TaskForStartExecutionInput();
        task.setType("AITranslation");

        // 注意：当前 SDK 版本可能不包含 AITranslationForStartExecutionInput 类
        // 根据火山引擎官方文档，AI翻译参数可能需要通过其他方式设置
        // 这里先构建基本的任务结构，具体的翻译参数设置待确认
        
        log.info("构建AI翻译任务 - 源语言: {} ({}), 目标语言: {} ({})", 
                sourceLanguage, SUPPORTED_LANGUAGES.get(sourceLanguage),
                targetLanguage, SUPPORTED_LANGUAGES.get(targetLanguage));

        operation.setTask(task);
        request.setOperation(operation);

        // 控制参数
        ControlForStartExecutionInput control = new ControlForStartExecutionInput();
        control.setClientToken(clientToken != null ? clientToken : generateClientToken());
        request.setControl(control);

        return request;
    }

    /**
     * 验证输入参数
     */
    private void validateParameters(String vid, String sourceLanguage, String targetLanguage) {
        if (vid == null || vid.trim().isEmpty()) {
            throw new IllegalArgumentException("视频ID不能为空");
        }

        if (sourceLanguage == null || sourceLanguage.trim().isEmpty()) {
            throw new IllegalArgumentException("源语言不能为空");
        }

        if (targetLanguage == null || targetLanguage.trim().isEmpty()) {
            throw new IllegalArgumentException("目标语言不能为空");
        }

        if (sourceLanguage.equals(targetLanguage)) {
            throw new IllegalArgumentException("源语言和目标语言不能相同");
        }

        if (!SUPPORTED_LANGUAGES.containsKey(sourceLanguage)) {
            throw new IllegalArgumentException("不支持的源语言: " + sourceLanguage);
        }

        if (!SUPPORTED_LANGUAGES.containsKey(targetLanguage)) {
            throw new IllegalArgumentException("不支持的目标语言: " + targetLanguage);
        }
    }

    /**
     * 生成客户端令牌
     */
    private String generateClientToken() {
        return "ai_translation_" + System.currentTimeMillis();
    }

    /**
     * 获取支持的语言列表
     */
    public Map<String, String> getSupportedLanguages() {
        return new HashMap<>(SUPPORTED_LANGUAGES);
    }

    /**
     * 检查语言是否支持
     */
    public boolean isLanguageSupported(String languageCode) {
        return SUPPORTED_LANGUAGES.containsKey(languageCode);
    }

    /**
     * 获取语言名称
     */
    public String getLanguageName(String languageCode) {
        return SUPPORTED_LANGUAGES.getOrDefault(languageCode, "未知语言");
    }

    /**
     * 批量提交翻译任务
     * 
     * @param vids 视频ID列表
     * @param sourceLanguage 源语言
     * @param targetLanguage 目标语言
     * @return 批量提交结果
     */
    public Map<String, Object> batchSubmitTranslation(String[] vids, String sourceLanguage, String targetLanguage) {
        Map<String, Object> results = new HashMap<>();
        int successCount = 0;
        int failCount = 0;

        for (String vid : vids) {
            try {
                String clientToken = generateClientToken() + "_" + vid;
                StartExecutionResponse response = submitTranslationWorkflow(vid, sourceLanguage, targetLanguage, clientToken);
                
                results.put(vid, Map.of(
                        "status", "success",
                        "requestId", response.getResponseMetadata() != null ? 
                                response.getResponseMetadata().getRequestId() : "unknown",
                        "clientToken", clientToken
                ));
                successCount++;
                
            } catch (Exception e) {
                log.error("批量翻译失败 - vid: {}", vid, e);
                results.put(vid, Map.of(
                        "status", "failed",
                        "error", e.getMessage()
                ));
                failCount++;
            }
        }

        Map<String, Object> summary = new HashMap<>();
        summary.put("total", vids.length);
        summary.put("success", successCount);
        summary.put("failed", failCount);
        summary.put("results", results);

        log.info("批量翻译任务提交完成 - 总数: {}, 成功: {}, 失败: {}", vids.length, successCount, failCount);
        return summary;
    }

    /**
     * 常用语言对的快捷方法
     */
    public StartExecutionResponse translateChineseToEnglish(String vid, String clientToken) {
        return submitTranslationWorkflow(vid, "zh", "en", clientToken);
    }

    public StartExecutionResponse translateEnglishToChinese(String vid, String clientToken) {
        return submitTranslationWorkflow(vid, "en", "zh", clientToken);
    }

    public StartExecutionResponse translateChineseToJapanese(String vid, String clientToken) {
        return submitTranslationWorkflow(vid, "zh", "ja", clientToken);
    }

    public StartExecutionResponse translateChineseToKorean(String vid, String clientToken) {
        return submitTranslationWorkflow(vid, "zh", "ko", clientToken);
    }
}
