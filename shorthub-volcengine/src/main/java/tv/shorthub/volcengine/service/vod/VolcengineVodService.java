package tv.shorthub.volcengine.service.vod;

import com.volcengine.ApiException;
import com.volcengine.vod20250101.Vod20250101Api;
import com.volcengine.vod20250101.model.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import tv.shorthub.volcengine.config.VolcengineProperties;
import tv.shorthub.volcengine.exception.VolcengineException;

import java.io.File;
import java.io.IOException;
import java.lang.reflect.Method;
import java.nio.file.Files;
import java.nio.file.StandardCopyOption;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 火山引擎VOD服务 - 2025年1月版
 * 基于官方SDK vod20250101 实现，直接返回官方模型
 */
@Slf4j
@Service
public class VolcengineVodService {

    @Autowired
    private Vod20250101Api vod20250101Api;

    @Autowired
    private VolcengineProperties properties;

    @Autowired
    private VolcengineUploadService uploadService;

    /**
     * 提交媒体处理任务
     * 参考官方示例: https://www.volcengine.com/docs/4/1617647
     *
     * @param vid 视频ID
     * @param taskType 任务类型 (Highlight, Transcode, Snapshot)
     * @param clientToken 客户端令牌
     * @return 官方StartExecutionResponse
     */
    public StartExecutionResponse startExecution(String vid, String taskType, String clientToken) {
        try {
            // 构建官方SDK请求对象
            StartExecutionRequest request = new StartExecutionRequest();

            // 构建输入参数
            InputForStartExecutionInput input = new InputForStartExecutionInput();
            input.setType("Vid");
            input.setVid(vid);
            request.setInput(input);

            // 构建操作参数
            OperationForStartExecutionInput operation = new OperationForStartExecutionInput();
            operation.setType("Task");

            TaskForStartExecutionInput task = new TaskForStartExecutionInput();
            task.setType(taskType);

            // 根据任务类型设置具体配置
            configureTaskByType(task, taskType);

            operation.setTask(task);
            request.setOperation(operation);

            // 构建控制参数
            ControlForStartExecutionInput control = new ControlForStartExecutionInput();
            control.setClientToken(clientToken);
            request.setControl(control);

            // 调用火山引擎API
            log.info("提交媒体处理任务 - vid: {}, taskType: {}, clientToken: {}",
                    vid, taskType, clientToken);

            StartExecutionResponse response = vod20250101Api.startExecution(request);

            log.info("媒体处理任务提交成功 - requestId: {}",
                    response.getResponseMetadata() != null ? response.getResponseMetadata().getRequestId() : "unknown");

            return response;

        } catch (ApiException e) {
            log.error("火山引擎API调用失败: {}", e.getResponseBody(), e);
            throw new VolcengineException("媒体处理任务提交失败: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("媒体处理任务提交异常", e);
            throw new VolcengineException("媒体处理任务提交异常: " + e.getMessage(), e);
        }
    }

    /**
     * 查询任务执行状态 (可扩展功能)
     */
    public void queryExecutionStatus(String executionId) {
        // TODO: 实现任务状态查询功能
        log.info("查询任务执行状态 - executionId: {}", executionId);
    }

    /**
     * 取消任务执行 (可扩展功能)
     */
    public void cancelExecution(String executionId) {
        // TODO: 实现任务取消功能
        log.info("取消任务执行 - executionId: {}", executionId);
    }

    // ==================== 媒资上传相关方法 ====================

    // 注意：当前 SDK 版本可能不包含 ApplyUpload/CommitUpload API
    // 这些方法已被注释，等待确认正确的 API 接口名称

    /*
    public Object applyUpload(String fileName, Long fileSize) {
        try {
            // TODO: 确认正确的申请上传 API 接口名称和参数
            log.info("申请上传 - fileName: {}, fileSize: {}, spaceName: {}",
                    fileName, fileSize, properties.getVod().getSpaceName());

            // 临时返回，等待实现
            throw new VolcengineException("申请上传 API 待确认实现");

        } catch (Exception e) {
            log.error("申请上传异常", e);
            throw new VolcengineException("申请上传异常: " + e.getMessage(), e);
        }
    }
    */

    /*
    public Object commitUpload(String sessionKey) {
        try {
            // TODO: 确认正确的确认上传 API 接口名称和参数
            log.info("确认上传 - sessionKey: {}, spaceName: {}",
                    sessionKey, properties.getVod().getSpaceName());

            // 临时返回，等待实现
            throw new VolcengineException("确认上传 API 待确认实现");

        } catch (Exception e) {
            log.error("确认上传异常", e);
            throw new VolcengineException("确认上传异常: " + e.getMessage(), e);
        }
    }
    */

    // 完整上传流程方法已被注释，等待确认正确的 API 接口
    /*
    public String uploadVideoAndGetVid(MultipartFile file) {
        try {
            String originalFileName = file.getOriginalFilename();
            Long fileSize = file.getSize();

            log.info("开始完整上传流程 - fileName: {}, fileSize: {}", originalFileName, fileSize);

            // TODO: 实现正确的上传流程
            throw new VolcengineException("完整上传流程待实现");

        } catch (Exception e) {
            log.error("完整上传流程失败", e);
            throw new VolcengineException("完整上传流程失败: " + e.getMessage(), e);
        }
    }
    */

    /*
    public String uploadLocalFileAndGetVid(String localFilePath) {
        try {
            File file = new File(localFilePath);
            if (!file.exists()) {
                throw new IllegalArgumentException("文件不存在: " + localFilePath);
            }

            String fileName = file.getName();
            Long fileSize = file.length();

            log.info("开始本地文件上传流程 - fileName: {}, fileSize: {}", fileName, fileSize);

            // TODO: 实现正确的上传流程
            throw new VolcengineException("本地文件上传流程待实现");

        } catch (Exception e) {
            log.error("本地文件上传流程失败", e);
            throw new VolcengineException("本地文件上传流程失败: " + e.getMessage(), e);
        }
    }
    */

    // ==================== AI 翻译工作流相关方法 ====================

    /**
     * 提交 AI 翻译工作流
     * 参考: https://www.volcengine.com/docs/4/1584290
     *
     * 根据火山引擎官方文档，AI翻译工作流的正确调用方式：
     * 1. 使用 StartExecution 接口
     * 2. 任务类型设置为 "AITranslation"
     * 3. 翻译参数通过 Task 的属性设置
     *
     * @param vid 视频ID
     * @param sourceLanguage 源语言 (如: zh, en, ja, ko)
     * @param targetLanguage 目标语言 (如: en, zh, ja, ko)
     * @param clientToken 客户端令牌（可选，用于幂等性）
     * @return 工作流执行响应
     */
    public StartExecutionResponse submitAITranslationWorkflow(String vid, String sourceLanguage,
                                                            String targetLanguage, String clientToken) {
        try {
            // 参数验证
            validateTranslationParameters(vid, sourceLanguage, targetLanguage);

            // 构建请求对象
            StartExecutionRequest request = new StartExecutionRequest();

            // 输入参数 - 指定视频ID
            InputForStartExecutionInput input = new InputForStartExecutionInput();
            input.setType("Vid");
            input.setVid(vid);
            request.setInput(input);

            // 操作参数 - AI翻译任务
            OperationForStartExecutionInput operation = new OperationForStartExecutionInput();
            operation.setType("Task");

            // 任务配置
            TaskForStartExecutionInput task = new TaskForStartExecutionInput();
            task.setType("AITranslation");

            // 根据火山引擎文档，AI翻译的语言参数可能需要通过以下方式设置：
            // 1. 检查 task 对象是否有设置翻译参数的方法
            // 2. 或者通过 operation 的其他属性设置
            // 3. 或者通过请求的扩展字段设置

            // 尝试通过反射或直接调用来设置翻译参数
            setTranslationParameters(task, sourceLanguage, targetLanguage);

            operation.setTask(task);
            request.setOperation(operation);

            // 控制参数
            ControlForStartExecutionInput control = new ControlForStartExecutionInput();
            control.setClientToken(clientToken != null ? clientToken : generateClientToken());
            request.setControl(control);

            // 调用API
            log.info("提交AI翻译工作流 - vid: {}, {} -> {}, clientToken: {}",
                    vid, sourceLanguage, targetLanguage, control.getClientToken());

            StartExecutionResponse response = vod20250101Api.startExecution(request);

            String requestId = response.getResponseMetadata() != null ?
                    response.getResponseMetadata().getRequestId() : "unknown";

            log.info("AI翻译工作流提交成功 - requestId: {}", requestId);

            return response;

        } catch (ApiException e) {
            log.error("AI翻译工作流提交失败 - vid: {}, API错误: {}", vid, e.getResponseBody(), e);
            throw new VolcengineException("AI翻译工作流提交失败: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("AI翻译工作流提交异常 - vid: {}", vid, e);
            throw new VolcengineException("AI翻译工作流提交异常: " + e.getMessage(), e);
        }
    }

    /**
     * 批量提交 AI 翻译工作流
     *
     * @param vids 视频ID数组
     * @param sourceLanguage 源语言
     * @param targetLanguage 目标语言
     * @return 批量处理结果
     */
    public Map<String, Object> batchSubmitAITranslationWorkflow(String[] vids, String sourceLanguage, String targetLanguage) {
        Map<String, Object> results = new HashMap<>();
        int successCount = 0;
        int failCount = 0;

        log.info("开始批量提交AI翻译工作流 - 数量: {}, {} -> {}", vids.length, sourceLanguage, targetLanguage);

        for (String vid : vids) {
            try {
                String clientToken = generateClientToken() + "_batch_" + vid;
                StartExecutionResponse response = submitAITranslationWorkflow(vid, sourceLanguage, targetLanguage, clientToken);

                results.put(vid, Map.of(
                        "status", "success",
                        "requestId", response.getResponseMetadata() != null ?
                                response.getResponseMetadata().getRequestId() : "unknown",
                        "clientToken", clientToken
                ));
                successCount++;

            } catch (Exception e) {
                log.error("批量翻译失败 - vid: {}", vid, e);
                results.put(vid, Map.of(
                        "status", "failed",
                        "error", e.getMessage()
                ));
                failCount++;
            }
        }

        Map<String, Object> summary = new HashMap<>();
        summary.put("total", vids.length);
        summary.put("success", successCount);
        summary.put("failed", failCount);
        summary.put("sourceLanguage", sourceLanguage);
        summary.put("targetLanguage", targetLanguage);
        summary.put("results", results);

        log.info("批量AI翻译工作流提交完成 - 总数: {}, 成功: {}, 失败: {}", vids.length, successCount, failCount);
        return summary;
    }

    /**
     * 常用语言对的快捷方法
     */
    public StartExecutionResponse translateChineseToEnglish(String vid, String clientToken) {
        return submitAITranslationWorkflow(vid, "zh", "en", clientToken);
    }

    public StartExecutionResponse translateEnglishToChinese(String vid, String clientToken) {
        return submitAITranslationWorkflow(vid, "en", "zh", clientToken);
    }

    public StartExecutionResponse translateChineseToJapanese(String vid, String clientToken) {
        return submitAITranslationWorkflow(vid, "zh", "ja", clientToken);
    }

    public StartExecutionResponse translateChineseToKorean(String vid, String clientToken) {
        return submitAITranslationWorkflow(vid, "zh", "ko", clientToken);
    }

    /**
     * 获取支持的语言列表
     */
    public Map<String, String> getSupportedLanguages() {
        Map<String, String> languages = new HashMap<>();
        languages.put("zh", "中文");
        languages.put("en", "英文");
        languages.put("ja", "日文");
        languages.put("ko", "韩文");
        languages.put("fr", "法文");
        languages.put("de", "德文");
        languages.put("es", "西班牙文");
        languages.put("ru", "俄文");
        languages.put("it", "意大利文");
        languages.put("pt", "葡萄牙文");
        languages.put("ar", "阿拉伯文");
        languages.put("th", "泰文");
        languages.put("vi", "越南文");
        languages.put("hi", "印地文");
        return languages;
    }

    // 一站式服务方法已被注释，等待上传 API 确认后实现
    /*
    public StartExecutionResponse uploadAndTranslate(MultipartFile file, String sourceLanguage, String targetLanguage) {
        try {
            log.info("开始一站式上传和翻译服务 - fileName: {}, {} -> {}",
                    file.getOriginalFilename(), sourceLanguage, targetLanguage);

            // TODO: 实现正确的上传和翻译流程
            throw new VolcengineException("一站式上传和翻译服务待实现");

        } catch (Exception e) {
            log.error("一站式上传和翻译服务失败", e);
            throw new VolcengineException("一站式上传和翻译服务失败: " + e.getMessage(), e);
        }
    }
    */

    /*
    public StartExecutionResponse uploadLocalFileAndTranslate(String localFilePath, String sourceLanguage, String targetLanguage) {
        try {
            log.info("开始本地文件一站式上传和翻译服务 - filePath: {}, {} -> {}",
                    localFilePath, sourceLanguage, targetLanguage);

            // TODO: 实现正确的本地文件上传和翻译流程
            throw new VolcengineException("本地文件一站式上传和翻译服务待实现");

        } catch (Exception e) {
            log.error("本地文件一站式上传和翻译服务失败", e);
            throw new VolcengineException("本地文件一站式上传和翻译服务失败: " + e.getMessage(), e);
        }
    }
    */

    // ==================== AI 翻译辅助方法 ====================

    /**
     * 验证翻译参数
     */
    private void validateTranslationParameters(String vid, String sourceLanguage, String targetLanguage) {
        if (vid == null || vid.trim().isEmpty()) {
            throw new IllegalArgumentException("视频ID不能为空");
        }

        if (sourceLanguage == null || sourceLanguage.trim().isEmpty()) {
            throw new IllegalArgumentException("源语言不能为空");
        }

        if (targetLanguage == null || targetLanguage.trim().isEmpty()) {
            throw new IllegalArgumentException("目标语言不能为空");
        }

        if (sourceLanguage.equals(targetLanguage)) {
            throw new IllegalArgumentException("源语言和目标语言不能相同");
        }

        // 验证支持的语言
        String[] supportedLanguages = {"zh", "en", "ja", "ko", "fr", "de", "es", "ru", "it", "pt", "ar", "th", "vi", "hi"};
        boolean sourceSupported = false;
        boolean targetSupported = false;

        for (String lang : supportedLanguages) {
            if (lang.equals(sourceLanguage)) sourceSupported = true;
            if (lang.equals(targetLanguage)) targetSupported = true;
        }

        if (!sourceSupported) {
            throw new IllegalArgumentException("不支持的源语言: " + sourceLanguage);
        }

        if (!targetSupported) {
            throw new IllegalArgumentException("不支持的目标语言: " + targetLanguage);
        }
    }

    /**
     * 设置翻译参数
     * 根据火山引擎文档，尝试不同的方式设置AI翻译的语言参数
     */
    private void setTranslationParameters(TaskForStartExecutionInput task, String sourceLanguage, String targetLanguage) {
        try {
            // 方法1: 尝试通过反射查找设置翻译参数的方法
            Class<?> taskClass = task.getClass();

            // 查找可能的设置方法
            try {
                var setSourceMethod = taskClass.getMethod("setSourceLanguage", String.class);
                var setTargetMethod = taskClass.getMethod("setTargetLanguage", String.class);

                setSourceMethod.invoke(task, sourceLanguage);
                setTargetMethod.invoke(task, targetLanguage);

                log.info("✅ 通过反射设置翻译参数成功");
                return;

            } catch (NoSuchMethodException e) {
                log.debug("未找到 setSourceLanguage/setTargetLanguage 方法");
            }

            // 方法2: 检查是否有 setAiTranslation 方法
            try {
                var setAiTranslationMethod = taskClass.getMethod("setAiTranslation", Object.class);

                // 创建翻译参数对象（如果存在的话）
                Object translationParams = createTranslationParams(sourceLanguage, targetLanguage);
                if (translationParams != null) {
                    setAiTranslationMethod.invoke(task, translationParams);
                    log.info("✅ 通过 setAiTranslation 设置翻译参数成功");
                    return;
                }

            } catch (NoSuchMethodException e) {
                log.debug("未找到 setAiTranslation 方法");
            }

            // 方法3: 检查是否有其他相关的设置方法
            for (var method : taskClass.getMethods()) {
                String methodName = method.getName().toLowerCase();
                if (methodName.contains("translation") || methodName.contains("language")) {
                    log.debug("发现可能相关的方法: {}", method.getName());
                }
            }

            // 如果所有方法都失败，记录警告但继续执行
            log.warn("⚠️ 无法设置AI翻译的语言参数，将使用默认配置提交任务");
            log.warn("源语言: {}, 目标语言: {} - 可能需要在火山引擎控制台中配置", sourceLanguage, targetLanguage);

        } catch (Exception e) {
            log.error("设置翻译参数时发生异常", e);
            throw new VolcengineException("设置翻译参数失败: " + e.getMessage(), e);
        }
    }

    /**
     * 尝试创建翻译参数对象
     */
    private Object createTranslationParams(String sourceLanguage, String targetLanguage) {
        try {
            // 尝试查找可能的翻译参数类
            String[] possibleClassNames = {
                "com.volcengine.vod20250101.model.AITranslationForStartExecutionInput",
                "com.volcengine.vod20250101.model.TranslationForStartExecutionInput",
                "com.volcengine.vod20250101.model.AITranslationInput"
            };

            for (String className : possibleClassNames) {
                try {
                    Class<?> clazz = Class.forName(className);
                    Object instance = clazz.getDeclaredConstructor().newInstance();

                    // 尝试设置语言参数
                    try {
                        var setSourceMethod = clazz.getMethod("setSourceLanguage", String.class);
                        var setTargetMethod = clazz.getMethod("setTargetLanguage", String.class);

                        setSourceMethod.invoke(instance, sourceLanguage);
                        setTargetMethod.invoke(instance, targetLanguage);

                        log.info("✅ 创建翻译参数对象成功: {}", className);
                        return instance;

                    } catch (NoSuchMethodException e) {
                        log.debug("类 {} 没有语言设置方法", className);
                    }

                } catch (ClassNotFoundException e) {
                    log.debug("类不存在: {}", className);
                }
            }

        } catch (Exception e) {
            log.debug("创建翻译参数对象失败", e);
        }

        return null;
    }

    /**
     * 生成客户端令牌
     */
    private String generateClientToken() {
        return "ai_translation_" + System.currentTimeMillis() + "_" + (int)(Math.random() * 1000);
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 上传 MultipartFile 到火山引擎指定地址
     */
    private void uploadFileToVolcengine(MultipartFile file, String uploadAddress, String sessionKey) {
        try {
            // 创建临时文件
            File tempFile = File.createTempFile("volcengine_upload_", "_" + file.getOriginalFilename());
            file.transferTo(tempFile);

            try {
                uploadLocalFileToVolcengine(tempFile, uploadAddress, sessionKey);
            } finally {
                // 清理临时文件
                if (tempFile.exists()) {
                    tempFile.delete();
                }
            }

        } catch (IOException e) {
            log.error("创建临时文件失败", e);
            throw new VolcengineException("创建临时文件失败: " + e.getMessage(), e);
        }
    }

    /**
     * 上传本地文件到火山引擎指定地址
     */
    private void uploadLocalFileToVolcengine(File file, String uploadAddress, String sessionKey) {
        try {
            log.info("开始上传文件到火山引擎 - fileName: {}, fileSize: {}",
                    file.getName(), file.length());

            // 验证文件
            if (!uploadService.validateFile(file)) {
                throw new VolcengineException("文件验证失败");
            }

            // 使用专门的上传服务
            uploadService.uploadFile(file, uploadAddress, sessionKey);

            log.info("文件上传到火山引擎完成");

        } catch (Exception e) {
            log.error("上传文件到火山引擎失败", e);
            throw new VolcengineException("上传文件到火山引擎失败: " + e.getMessage(), e);
        }
    }

    /**
     * 更新任务类型配置 - 添加AI翻译支持
     */
    private void configureTaskByType(TaskForStartExecutionInput task, String taskType) {
        switch (taskType) {
            case "AITranslation":
                // AI翻译任务配置在调用方法中设置
                break;
            case "Highlight":
                HighlightForStartExecutionInput highlight = new HighlightForStartExecutionInput();
                task.setHighlight(highlight);
                break;
            case "Transcode":
                // TODO: 添加转码任务配置
                break;
            case "Snapshot":
                // TODO: 添加截图任务配置
                break;
            default:
                log.warn("不支持的任务类型: {}", taskType);
                break;
        }
    }
}
