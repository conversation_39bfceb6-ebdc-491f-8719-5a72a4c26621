package tv.shorthub.volcengine.service.vod;

import com.volcengine.ApiException;
import com.volcengine.vod20250101.Vod20250101Api;
import com.volcengine.vod20250101.model.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tv.shorthub.volcengine.config.VolcengineProperties;
import tv.shorthub.volcengine.exception.VolcengineException;

/**
 * 火山引擎VOD服务 - 2025年1月版
 * 基于官方SDK vod20250101 实现，直接返回官方模型
 */
@Slf4j
@Service
public class VolcengineVodService {

    @Autowired
    private Vod20250101Api vod20250101Api;

    @Autowired
    private VolcengineProperties properties;

    /**
     * 提交媒体处理任务
     * 参考官方示例: https://www.volcengine.com/docs/4/1617647
     *
     * @param vid 视频ID
     * @param taskType 任务类型 (Highlight, Transcode, Snapshot)
     * @param clientToken 客户端令牌
     * @return 官方StartExecutionResponse
     */
    public StartExecutionResponse startExecution(String vid, String taskType, String clientToken) {
        try {
            // 构建官方SDK请求对象
            StartExecutionRequest request = new StartExecutionRequest();

            // 构建输入参数
            InputForStartExecutionInput input = new InputForStartExecutionInput();
            input.setType("Vid");
            input.setVid(vid);
            request.setInput(input);

            // 构建操作参数
            OperationForStartExecutionInput operation = new OperationForStartExecutionInput();
            operation.setType("Task");

            TaskForStartExecutionInput task = new TaskForStartExecutionInput();
            task.setType(taskType);

            // 根据任务类型设置具体配置
            configureTaskByType(task, taskType);

            operation.setTask(task);
            request.setOperation(operation);

            // 构建控制参数
            ControlForStartExecutionInput control = new ControlForStartExecutionInput();
            control.setClientToken(clientToken);
            request.setControl(control);

            // 调用火山引擎API
            log.info("提交媒体处理任务 - vid: {}, taskType: {}, clientToken: {}",
                    vid, taskType, clientToken);

            StartExecutionResponse response = vod20250101Api.startExecution(request);

            log.info("媒体处理任务提交成功 - requestId: {}",
                    response.getResponseMetadata() != null ? response.getResponseMetadata().getRequestId() : "unknown");

            return response;

        } catch (ApiException e) {
            log.error("火山引擎API调用失败: {}", e.getResponseBody(), e);
            throw new VolcengineException("媒体处理任务提交失败: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("媒体处理任务提交异常", e);
            throw new VolcengineException("媒体处理任务提交异常: " + e.getMessage(), e);
        }
    }
    /**
     * 根据任务类型配置具体参数
     */
    private void configureTaskByType(TaskForStartExecutionInput task, String taskType) {
        switch (taskType) {
            default:
                log.warn("不支持的任务类型: {}", taskType);
                break;
        }
    }

    /**
     * 查询任务执行状态 (可扩展功能)
     */
    public void queryExecutionStatus(String executionId) {
        // TODO: 实现任务状态查询功能
        log.info("查询任务执行状态 - executionId: {}", executionId);
    }

    /**
     * 取消任务执行 (可扩展功能)
     */
    public void cancelExecution(String executionId) {
        // TODO: 实现任务取消功能
        log.info("取消任务执行 - executionId: {}", executionId);
    }
}
