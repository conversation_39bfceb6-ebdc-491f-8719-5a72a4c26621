package tv.shorthub.volcengine.service.vod;

import com.volcengine.ApiException;
import com.volcengine.vod20250101.Vod20250101Api;
import com.volcengine.vod20250101.model.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import tv.shorthub.volcengine.config.VolcengineProperties;
import tv.shorthub.volcengine.exception.VolcengineException;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.StandardCopyOption;
import java.util.UUID;

/**
 * 火山引擎VOD服务 - 2025年1月版
 * 基于官方SDK vod20250101 实现，直接返回官方模型
 */
@Slf4j
@Service
public class VolcengineVodService {

    @Autowired
    private Vod20250101Api vod20250101Api;

    @Autowired
    private VolcengineProperties properties;

    @Autowired
    private VolcengineUploadService uploadService;

    /**
     * 提交媒体处理任务
     * 参考官方示例: https://www.volcengine.com/docs/4/1617647
     *
     * @param vid 视频ID
     * @param taskType 任务类型 (Highlight, Transcode, Snapshot)
     * @param clientToken 客户端令牌
     * @return 官方StartExecutionResponse
     */
    public StartExecutionResponse startExecution(String vid, String taskType, String clientToken) {
        try {
            // 构建官方SDK请求对象
            StartExecutionRequest request = new StartExecutionRequest();

            // 构建输入参数
            InputForStartExecutionInput input = new InputForStartExecutionInput();
            input.setType("Vid");
            input.setVid(vid);
            request.setInput(input);

            // 构建操作参数
            OperationForStartExecutionInput operation = new OperationForStartExecutionInput();
            operation.setType("Task");

            TaskForStartExecutionInput task = new TaskForStartExecutionInput();
            task.setType(taskType);

            // 根据任务类型设置具体配置
            configureTaskByType(task, taskType);

            operation.setTask(task);
            request.setOperation(operation);

            // 构建控制参数
            ControlForStartExecutionInput control = new ControlForStartExecutionInput();
            control.setClientToken(clientToken);
            request.setControl(control);

            // 调用火山引擎API
            log.info("提交媒体处理任务 - vid: {}, taskType: {}, clientToken: {}",
                    vid, taskType, clientToken);

            StartExecutionResponse response = vod20250101Api.startExecution(request);

            log.info("媒体处理任务提交成功 - requestId: {}",
                    response.getResponseMetadata() != null ? response.getResponseMetadata().getRequestId() : "unknown");

            return response;

        } catch (ApiException e) {
            log.error("火山引擎API调用失败: {}", e.getResponseBody(), e);
            throw new VolcengineException("媒体处理任务提交失败: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("媒体处理任务提交异常", e);
            throw new VolcengineException("媒体处理任务提交异常: " + e.getMessage(), e);
        }
    }

    /**
     * 查询任务执行状态 (可扩展功能)
     */
    public void queryExecutionStatus(String executionId) {
        // TODO: 实现任务状态查询功能
        log.info("查询任务执行状态 - executionId: {}", executionId);
    }

    /**
     * 取消任务执行 (可扩展功能)
     */
    public void cancelExecution(String executionId) {
        // TODO: 实现任务取消功能
        log.info("取消任务执行 - executionId: {}", executionId);
    }

    // ==================== 媒资上传相关方法 ====================

    /**
     * 申请上传 - 获取上传地址和授权信息
     *
     * @param fileName 文件名
     * @param fileSize 文件大小（字节）
     * @return 申请上传响应
     */
    public ApplyUploadResponse applyUpload(String fileName, Long fileSize) {
        try {
            ApplyUploadRequest request = new ApplyUploadRequest();
            request.setSpaceName(properties.getVod().getSpaceName());
            request.setFileName(fileName);
            request.setFileSize(fileSize);

            log.info("申请上传 - fileName: {}, fileSize: {}, spaceName: {}",
                    fileName, fileSize, properties.getVod().getSpaceName());

            ApplyUploadResponse response = vod20250101Api.applyUpload(request);

            log.info("申请上传成功 - requestId: {}",
                    response.getResponseMetadata() != null ? response.getResponseMetadata().getRequestId() : "unknown");

            return response;

        } catch (ApiException e) {
            log.error("申请上传失败: {}", e.getResponseBody(), e);
            throw new VolcengineException("申请上传失败: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("申请上传异常", e);
            throw new VolcengineException("申请上传异常: " + e.getMessage(), e);
        }
    }

    /**
     * 确认上传 - 获取 Vid
     *
     * @param sessionKey 上传会话密钥
     * @return 确认上传响应，包含 Vid
     */
    public CommitUploadResponse commitUpload(String sessionKey) {
        try {
            CommitUploadRequest request = new CommitUploadRequest();
            request.setSpaceName(properties.getVod().getSpaceName());
            request.setSessionKey(sessionKey);

            log.info("确认上传 - sessionKey: {}, spaceName: {}",
                    sessionKey, properties.getVod().getSpaceName());

            CommitUploadResponse response = vod20250101Api.commitUpload(request);

            log.info("确认上传成功 - requestId: {}, vid: {}",
                    response.getResponseMetadata() != null ? response.getResponseMetadata().getRequestId() : "unknown",
                    response.getResult() != null ? response.getResult().getVid() : "unknown");

            return response;

        } catch (ApiException e) {
            log.error("确认上传失败: {}", e.getResponseBody(), e);
            throw new VolcengineException("确认上传失败: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("确认上传异常", e);
            throw new VolcengineException("确认上传异常: " + e.getMessage(), e);
        }
    }

    /**
     * 完整的媒资上传流程 - 从文件到 Vid
     *
     * @param file 要上传的文件
     * @return Vid 视频ID
     */
    public String uploadVideoAndGetVid(MultipartFile file) {
        try {
            String originalFileName = file.getOriginalFilename();
            Long fileSize = file.getSize();

            log.info("开始完整上传流程 - fileName: {}, fileSize: {}", originalFileName, fileSize);

            // 1. 申请上传
            ApplyUploadResponse applyResponse = applyUpload(originalFileName, fileSize);
            String sessionKey = applyResponse.getResult().getSessionKey();
            String uploadAddress = applyResponse.getResult().getUploadAddress();

            // 2. 上传文件到火山引擎
            uploadFileToVolcengine(file, uploadAddress, sessionKey);

            // 3. 确认上传，获取 Vid
            CommitUploadResponse commitResponse = commitUpload(sessionKey);
            String vid = commitResponse.getResult().getVid();

            log.info("完整上传流程成功 - vid: {}", vid);
            return vid;

        } catch (Exception e) {
            log.error("完整上传流程失败", e);
            throw new VolcengineException("完整上传流程失败: " + e.getMessage(), e);
        }
    }

    /**
     * 从本地文件上传到火山引擎获取 Vid
     *
     * @param localFilePath 本地文件路径
     * @return Vid 视频ID
     */
    public String uploadLocalFileAndGetVid(String localFilePath) {
        try {
            File file = new File(localFilePath);
            if (!file.exists()) {
                throw new IllegalArgumentException("文件不存在: " + localFilePath);
            }

            String fileName = file.getName();
            Long fileSize = file.length();

            log.info("开始本地文件上传流程 - fileName: {}, fileSize: {}", fileName, fileSize);

            // 1. 申请上传
            ApplyUploadResponse applyResponse = applyUpload(fileName, fileSize);
            String sessionKey = applyResponse.getResult().getSessionKey();
            String uploadAddress = applyResponse.getResult().getUploadAddress();

            // 2. 上传文件到火山引擎
            uploadLocalFileToVolcengine(file, uploadAddress, sessionKey);

            // 3. 确认上传，获取 Vid
            CommitUploadResponse commitResponse = commitUpload(sessionKey);
            String vid = commitResponse.getResult().getVid();

            log.info("本地文件上传流程成功 - vid: {}", vid);
            return vid;

        } catch (Exception e) {
            log.error("本地文件上传流程失败", e);
            throw new VolcengineException("本地文件上传流程失败: " + e.getMessage(), e);
        }
    }

    // ==================== AI 翻译工作流相关方法 ====================

    /**
     * 提交 AI 翻译工作流
     * 参考: https://www.volcengine.com/docs/4/1584290
     *
     * @param vid 视频ID
     * @param sourceLanguage 源语言 (如: zh, en)
     * @param targetLanguage 目标语言 (如: en, zh)
     * @param clientToken 客户端令牌
     * @return 工作流执行响应
     */
    public StartExecutionResponse submitAITranslationWorkflow(String vid, String sourceLanguage,
                                                            String targetLanguage, String clientToken) {
        try {
            // 构建请求对象
            StartExecutionRequest request = new StartExecutionRequest();

            // 输入参数 - 指定视频ID
            InputForStartExecutionInput input = new InputForStartExecutionInput();
            input.setType("Vid");
            input.setVid(vid);
            request.setInput(input);

            // 操作参数 - AI翻译任务
            OperationForStartExecutionInput operation = new OperationForStartExecutionInput();
            operation.setType("Task");

            TaskForStartExecutionInput task = new TaskForStartExecutionInput();
            task.setType("AITranslation");

            // AI翻译具体配置
            AITranslationForStartExecutionInput aiTranslation = new AITranslationForStartExecutionInput();
            aiTranslation.setSourceLanguage(sourceLanguage);
            aiTranslation.setTargetLanguage(targetLanguage);
            task.setAiTranslation(aiTranslation);

            operation.setTask(task);
            request.setOperation(operation);

            // 控制参数
            ControlForStartExecutionInput control = new ControlForStartExecutionInput();
            control.setClientToken(clientToken != null ? clientToken : "ai_translation_" + System.currentTimeMillis());
            request.setControl(control);

            // 调用API
            log.info("提交AI翻译工作流 - vid: {}, sourceLanguage: {}, targetLanguage: {}",
                    vid, sourceLanguage, targetLanguage);

            StartExecutionResponse response = vod20250101Api.startExecution(request);

            log.info("AI翻译工作流提交成功 - requestId: {}",
                    response.getResponseMetadata() != null ? response.getResponseMetadata().getRequestId() : "unknown");

            return response;

        } catch (ApiException e) {
            log.error("AI翻译工作流提交失败: {}", e.getResponseBody(), e);
            throw new VolcengineException("AI翻译工作流提交失败: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("AI翻译工作流提交异常", e);
            throw new VolcengineException("AI翻译工作流提交异常: " + e.getMessage(), e);
        }
    }

    /**
     * 一站式服务：上传视频并提交AI翻译
     *
     * @param file 视频文件
     * @param sourceLanguage 源语言
     * @param targetLanguage 目标语言
     * @return AI翻译工作流响应
     */
    public StartExecutionResponse uploadAndTranslate(MultipartFile file, String sourceLanguage, String targetLanguage) {
        try {
            log.info("开始一站式上传和翻译服务 - fileName: {}, {} -> {}",
                    file.getOriginalFilename(), sourceLanguage, targetLanguage);

            // 1. 上传视频获取 Vid
            String vid = uploadVideoAndGetVid(file);

            // 2. 提交AI翻译工作流
            String clientToken = "upload_translate_" + System.currentTimeMillis();
            StartExecutionResponse response = submitAITranslationWorkflow(vid, sourceLanguage, targetLanguage, clientToken);

            log.info("一站式上传和翻译服务完成 - vid: {}", vid);
            return response;

        } catch (Exception e) {
            log.error("一站式上传和翻译服务失败", e);
            throw new VolcengineException("一站式上传和翻译服务失败: " + e.getMessage(), e);
        }
    }

    /**
     * 从本地文件一站式上传和翻译
     *
     * @param localFilePath 本地文件路径
     * @param sourceLanguage 源语言
     * @param targetLanguage 目标语言
     * @return AI翻译工作流响应
     */
    public StartExecutionResponse uploadLocalFileAndTranslate(String localFilePath, String sourceLanguage, String targetLanguage) {
        try {
            log.info("开始本地文件一站式上传和翻译服务 - filePath: {}, {} -> {}",
                    localFilePath, sourceLanguage, targetLanguage);

            // 1. 上传本地文件获取 Vid
            String vid = uploadLocalFileAndGetVid(localFilePath);

            // 2. 提交AI翻译工作流
            String clientToken = "local_upload_translate_" + System.currentTimeMillis();
            StartExecutionResponse response = submitAITranslationWorkflow(vid, sourceLanguage, targetLanguage, clientToken);

            log.info("本地文件一站式上传和翻译服务完成 - vid: {}", vid);
            return response;

        } catch (Exception e) {
            log.error("本地文件一站式上传和翻译服务失败", e);
            throw new VolcengineException("本地文件一站式上传和翻译服务失败: " + e.getMessage(), e);
        }
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 上传 MultipartFile 到火山引擎指定地址
     */
    private void uploadFileToVolcengine(MultipartFile file, String uploadAddress, String sessionKey) {
        try {
            // 创建临时文件
            File tempFile = File.createTempFile("volcengine_upload_", "_" + file.getOriginalFilename());
            file.transferTo(tempFile);

            try {
                uploadLocalFileToVolcengine(tempFile, uploadAddress, sessionKey);
            } finally {
                // 清理临时文件
                if (tempFile.exists()) {
                    tempFile.delete();
                }
            }

        } catch (IOException e) {
            log.error("创建临时文件失败", e);
            throw new VolcengineException("创建临时文件失败: " + e.getMessage(), e);
        }
    }

    /**
     * 上传本地文件到火山引擎指定地址
     */
    private void uploadLocalFileToVolcengine(File file, String uploadAddress, String sessionKey) {
        try {
            log.info("开始上传文件到火山引擎 - fileName: {}, fileSize: {}",
                    file.getName(), file.length());

            // 验证文件
            if (!uploadService.validateFile(file)) {
                throw new VolcengineException("文件验证失败");
            }

            // 使用专门的上传服务
            uploadService.uploadFile(file, uploadAddress, sessionKey);

            log.info("文件上传到火山引擎完成");

        } catch (Exception e) {
            log.error("上传文件到火山引擎失败", e);
            throw new VolcengineException("上传文件到火山引擎失败: " + e.getMessage(), e);
        }
    }

    /**
     * 更新任务类型配置 - 添加AI翻译支持
     */
    private void configureTaskByType(TaskForStartExecutionInput task, String taskType) {
        switch (taskType) {
            case "AITranslation":
                // AI翻译任务配置在调用方法中设置
                break;
            case "Highlight":
                HighlightForStartExecutionInput highlight = new HighlightForStartExecutionInput();
                task.setHighlight(highlight);
                break;
            case "Transcode":
                // TODO: 添加转码任务配置
                break;
            case "Snapshot":
                // TODO: 添加截图任务配置
                break;
            default:
                log.warn("不支持的任务类型: {}", taskType);
                break;
        }
    }
}
