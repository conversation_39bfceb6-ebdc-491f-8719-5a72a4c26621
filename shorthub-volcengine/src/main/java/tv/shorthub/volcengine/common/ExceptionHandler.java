package tv.shorthub.volcengine.common;

import com.volcengine.ApiException;
import com.volcengine.model.Error;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import tv.shorthub.volcengine.exception.VolcengineException;

import java.net.SocketException;
import java.net.SocketTimeoutException;
import java.net.UnknownHostException;
import java.net.UnknownServiceException;

/**
 * 火山引擎异常处理工具类
 * 根据官方文档实现统一的异常处理逻辑
 */
@Slf4j
public class ExceptionHandler {

    /**
     * 处理火山引擎API异常
     * 根据异常类型进行分类处理
     */
    public static VolcengineException handleApiException(ApiException e, String operation) {
        Throwable cause = e.getCause();

        // 1. 客户端错误 - 请求未到达服务端
        if (cause == null && e.getCode() == 0 && !StringUtils.isEmpty(e.getMessage())) {
            log.error("客户端错误 - {}: {}", operation, e.getMessage());
            return new VolcengineException("CLIENT_ERROR", "客户端错误: " + e.getMessage(), null, e);
        }

        // 2. 网络/超时错误
        if (isNetworkError(cause)) {
            log.error("网络/超时错误 - {}: {}", operation, cause.getMessage());
            return new VolcengineException("NETWORK_ERROR", "网络错误: " + cause.getMessage(), null, e);
        }

        // 3. 服务端错误 - 请求成功到达服务器，返回业务逻辑错误
        if (e.getResponseMetadata() != null && e.getResponseMetadata().getError() != null) {
            Error error = e.getResponseMetadata().getError();
            String requestId = e.getResponseMetadata().getRequestId();

            log.error("服务端错误 - {}: code={}, message={}, requestId={}",
                    operation, error.getCode(), error.getMessage(), requestId);

            return new VolcengineException(error.getCode(), error.getMessage(), requestId, e);
        }

        // 4. 其他错误
        if (cause != null) {
            log.error("其他错误 - {}: {}", operation, cause.getMessage());
            return new VolcengineException("OTHER_ERROR", "其他错误: " + cause.getMessage(), null, e);
        }

        // 默认处理
        log.error("未知错误 - {}: {}", operation, e.getMessage());
        return new VolcengineException("UNKNOWN_ERROR", "未知错误: " + e.getMessage(), null, e);
    }

    /**
     * 判断是否为网络错误
     */
    private static boolean isNetworkError(Throwable cause) {
        return cause instanceof SocketTimeoutException ||
               cause instanceof UnknownHostException ||
               cause instanceof UnknownServiceException ||
               cause instanceof SocketException;
    }

    /**
     * 处理通用异常
     */
    public static VolcengineException handleException(Exception e, String operation) {
        log.error("系统异常 - {}: {}", operation, e.getMessage(), e);
        return new VolcengineException("SYSTEM_ERROR", "系统异常: " + e.getMessage(), null, e);
    }

    /**
     * 检查是否为可重试的错误
     */
    public static boolean isRetryableError(VolcengineException e) {
        String errorCode = e.getErrorCode();

        // 网络错误可重试
        if ("NETWORK_ERROR".equals(errorCode)) {
            return true;
        }

        // 限流错误可重试
        if ("Throttling".equals(errorCode) || "RequestLimitExceeded".equals(errorCode)) {
            return true;
        }

        // 服务器内部错误可重试
        if ("InternalError".equals(errorCode) || "ServiceUnavailable".equals(errorCode)) {
            return true;
        }

        return false;
    }
}
