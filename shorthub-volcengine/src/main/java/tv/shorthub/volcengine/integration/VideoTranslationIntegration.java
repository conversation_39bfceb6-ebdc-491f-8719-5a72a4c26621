package tv.shorthub.volcengine.integration;

import com.volcengine.vod20250101.model.StartExecutionResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import tv.shorthub.common.core.domain.AjaxResult;
import tv.shorthub.volcengine.service.vod.VolcengineVodService;

import java.util.HashMap;
import java.util.Map;

/**
 * 视频翻译集成服务
 * 提供与现有系统集成的高级接口
 */
@Slf4j
@Service
public class VideoTranslationIntegration {

    @Autowired
    private VolcengineVodService volcengineVodService;

    /**
     * 剧集视频上传并翻译
     * 集成到现有的剧集管理系统
     * 
     * @param file 视频文件
     * @param contentId 剧目内容ID
     * @param serialNumber 集数
     * @param sourceLanguage 源语言
     * @param targetLanguage 目标语言
     * @return 处理结果
     */
    public AjaxResult uploadDramaAndTranslate(MultipartFile file, String contentId, 
                                            Integer serialNumber, String sourceLanguage, String targetLanguage) {
        try {
            log.info("开始剧集视频上传和翻译 - contentId: {}, serialNumber: {}, {} -> {}", 
                    contentId, serialNumber, sourceLanguage, targetLanguage);

            // 1. 上传视频到火山引擎获取 Vid
            String vid = volcengineVodService.uploadVideoAndGetVid(file);
            log.info("视频上传成功，获得 Vid: {}", vid);

            // 2. 提交 AI 翻译工作流
            String clientToken = String.format("drama_%s_serial_%d_%d", 
                    contentId, serialNumber, System.currentTimeMillis());
            
            StartExecutionResponse translationResponse = volcengineVodService.submitAITranslationWorkflow(
                    vid, sourceLanguage, targetLanguage, clientToken);

            // 3. 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("vid", vid);
            result.put("contentId", contentId);
            result.put("serialNumber", serialNumber);
            result.put("sourceLanguage", sourceLanguage);
            result.put("targetLanguage", targetLanguage);
            result.put("translationTaskId", translationResponse.getResponseMetadata().getRequestId());
            result.put("clientToken", clientToken);

            log.info("剧集视频上传和翻译完成 - vid: {}, taskId: {}", 
                    vid, translationResponse.getResponseMetadata().getRequestId());

            return AjaxResult.success("剧集视频上传和翻译成功", result);

        } catch (Exception e) {
            log.error("剧集视频上传和翻译失败", e);
            return AjaxResult.error("剧集视频上传和翻译失败: " + e.getMessage());
        }
    }

    /**
     * 批量翻译已有剧集
     * 为已经上传的剧集批量提交翻译任务
     * 
     * @param vids 视频ID列表
     * @param sourceLanguage 源语言
     * @param targetLanguage 目标语言
     * @return 处理结果
     */
    public AjaxResult batchTranslateDramas(String[] vids, String sourceLanguage, String targetLanguage) {
        try {
            log.info("开始批量翻译剧集 - 数量: {}, {} -> {}", vids.length, sourceLanguage, targetLanguage);

            Map<String, Object> results = new HashMap<>();
            int successCount = 0;
            int failCount = 0;

            for (String vid : vids) {
                try {
                    String clientToken = String.format("batch_translate_%s_%d", vid, System.currentTimeMillis());
                    StartExecutionResponse response = volcengineVodService.submitAITranslationWorkflow(
                            vid, sourceLanguage, targetLanguage, clientToken);
                    
                    results.put(vid, Map.of(
                            "status", "success",
                            "taskId", response.getResponseMetadata().getRequestId(),
                            "clientToken", clientToken
                    ));
                    successCount++;
                    
                } catch (Exception e) {
                    log.error("翻译任务提交失败 - vid: {}", vid, e);
                    results.put(vid, Map.of(
                            "status", "failed",
                            "error", e.getMessage()
                    ));
                    failCount++;
                }
            }

            Map<String, Object> summary = new HashMap<>();
            summary.put("total", vids.length);
            summary.put("success", successCount);
            summary.put("failed", failCount);
            summary.put("results", results);

            log.info("批量翻译剧集完成 - 成功: {}, 失败: {}", successCount, failCount);
            return AjaxResult.success("批量翻译任务提交完成", summary);

        } catch (Exception e) {
            log.error("批量翻译剧集失败", e);
            return AjaxResult.error("批量翻译剧集失败: " + e.getMessage());
        }
    }

    /**
     * 从本地存储的剧集文件上传并翻译
     * 适用于已经存储在本地/R2的视频文件
     * 
     * @param storagePath 存储路径
     * @param contentId 剧目内容ID
     * @param serialNumber 集数
     * @param sourceLanguage 源语言
     * @param targetLanguage 目标语言
     * @return 处理结果
     */
    public AjaxResult uploadStoredVideoAndTranslate(String storagePath, String contentId, 
                                                  Integer serialNumber, String sourceLanguage, String targetLanguage) {
        try {
            log.info("开始从存储路径上传和翻译 - storagePath: {}, contentId: {}, serialNumber: {}", 
                    storagePath, contentId, serialNumber);

            // 1. 从存储路径上传到火山引擎
            String vid = volcengineVodService.uploadLocalFileAndGetVid(storagePath);
            log.info("存储文件上传成功，获得 Vid: {}", vid);

            // 2. 提交翻译任务
            String clientToken = String.format("stored_%s_serial_%d_%d", 
                    contentId, serialNumber, System.currentTimeMillis());
            
            StartExecutionResponse translationResponse = volcengineVodService.submitAITranslationWorkflow(
                    vid, sourceLanguage, targetLanguage, clientToken);

            // 3. 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("vid", vid);
            result.put("storagePath", storagePath);
            result.put("contentId", contentId);
            result.put("serialNumber", serialNumber);
            result.put("sourceLanguage", sourceLanguage);
            result.put("targetLanguage", targetLanguage);
            result.put("translationTaskId", translationResponse.getResponseMetadata().getRequestId());
            result.put("clientToken", clientToken);

            log.info("存储文件上传和翻译完成 - vid: {}", vid);
            return AjaxResult.success("存储文件上传和翻译成功", result);

        } catch (Exception e) {
            log.error("存储文件上传和翻译失败", e);
            return AjaxResult.error("存储文件上传和翻译失败: " + e.getMessage());
        }
    }

    /**
     * 获取支持的语言列表
     * 
     * @return 支持的语言代码和名称
     */
    public AjaxResult getSupportedLanguages() {
        Map<String, String> languages = new HashMap<>();
        languages.put("zh", "中文");
        languages.put("en", "英文");
        languages.put("ja", "日文");
        languages.put("ko", "韩文");
        languages.put("fr", "法文");
        languages.put("de", "德文");
        languages.put("es", "西班牙文");
        languages.put("ru", "俄文");
        languages.put("it", "意大利文");
        languages.put("pt", "葡萄牙文");
        languages.put("ar", "阿拉伯文");
        languages.put("th", "泰文");
        languages.put("vi", "越南文");
        languages.put("hi", "印地文");

        return AjaxResult.success("支持的语言列表", languages);
    }

    /**
     * 检查翻译任务状态（占位方法）
     * 实际使用时需要根据火山引擎的回调或查询接口实现
     * 
     * @param taskId 任务ID
     * @return 任务状态
     */
    public AjaxResult checkTranslationStatus(String taskId) {
        try {
            // TODO: 实现任务状态查询
            // 可以通过火山引擎的查询接口或回调机制获取任务状态
            
            Map<String, Object> status = new HashMap<>();
            status.put("taskId", taskId);
            status.put("status", "processing"); // processing, completed, failed
            status.put("progress", 50); // 进度百分比
            status.put("message", "翻译任务进行中");

            return AjaxResult.success("任务状态查询成功", status);

        } catch (Exception e) {
            log.error("查询翻译任务状态失败", e);
            return AjaxResult.error("查询翻译任务状态失败: " + e.getMessage());
        }
    }

    /**
     * 取消翻译任务（占位方法）
     * 
     * @param taskId 任务ID
     * @return 取消结果
     */
    public AjaxResult cancelTranslationTask(String taskId) {
        try {
            // TODO: 实现任务取消
            volcengineVodService.cancelExecution(taskId);
            
            return AjaxResult.success("翻译任务取消成功");

        } catch (Exception e) {
            log.error("取消翻译任务失败", e);
            return AjaxResult.error("取消翻译任务失败: " + e.getMessage());
        }
    }
}
