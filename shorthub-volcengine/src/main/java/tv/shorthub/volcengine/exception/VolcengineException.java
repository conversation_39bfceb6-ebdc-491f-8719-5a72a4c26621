package tv.shorthub.volcengine.exception;

/**
 * 火山引擎业务异常
 */
public class VolcengineException extends RuntimeException {
    
    private String errorCode;
    private String requestId;
    
    public VolcengineException(String message) {
        super(message);
    }
    
    public VolcengineException(String message, Throwable cause) {
        super(message, cause);
    }
    
    public VolcengineException(String errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }
    
    public VolcengineException(String errorCode, String message, String requestId) {
        super(message);
        this.errorCode = errorCode;
        this.requestId = requestId;
    }
    
    public VolcengineException(String errorCode, String message, String requestId, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
        this.requestId = requestId;
    }
    
    public String getErrorCode() {
        return errorCode;
    }
    
    public String getRequestId() {
        return requestId;
    }
}