package tv.shorthub.volcengine.controller;

import com.volcengine.vod20250101.model.ApplyUploadResponse;
import com.volcengine.vod20250101.model.CommitUploadResponse;
import com.volcengine.vod20250101.model.StartExecutionResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import tv.shorthub.common.core.domain.AjaxResult;
import tv.shorthub.volcengine.service.vod.VolcengineVodService;

/**
 * 火山引擎 VOD 控制器
 * 提供媒资上传和 AI 翻译相关接口
 */
@Slf4j
@RestController
@RequestMapping("/volcengine/vod")
@Tag(name = "火山引擎VOD", description = "火山引擎视频点播服务接口")
public class VolcengineVodController {

    @Autowired
    private VolcengineVodService volcengineVodService;

    /**
     * 申请上传
     */
    @PostMapping("/apply-upload")
    @Operation(summary = "申请上传", description = "申请视频上传，获取上传地址和会话密钥")
    public AjaxResult applyUpload(
            @Parameter(description = "文件名") @RequestParam String fileName,
            @Parameter(description = "文件大小（字节）") @RequestParam Long fileSize) {
        try {
            ApplyUploadResponse response = volcengineVodService.applyUpload(fileName, fileSize);
            return AjaxResult.success("申请上传成功", response);
        } catch (Exception e) {
            log.error("申请上传失败", e);
            return AjaxResult.error("申请上传失败: " + e.getMessage());
        }
    }

    /**
     * 确认上传
     */
    @PostMapping("/commit-upload")
    @Operation(summary = "确认上传", description = "确认上传完成，获取视频ID")
    public AjaxResult commitUpload(
            @Parameter(description = "会话密钥") @RequestParam String sessionKey) {
        try {
            CommitUploadResponse response = volcengineVodService.commitUpload(sessionKey);
            return AjaxResult.success("确认上传成功", response);
        } catch (Exception e) {
            log.error("确认上传失败", e);
            return AjaxResult.error("确认上传失败: " + e.getMessage());
        }
    }

    /**
     * 完整上传流程 - 直接上传文件获取 Vid
     */
    @PostMapping("/upload-video")
    @Operation(summary = "上传视频", description = "完整的视频上传流程，直接返回视频ID")
    public AjaxResult uploadVideo(
            @Parameter(description = "视频文件") @RequestParam("file") MultipartFile file) {
        try {
            String vid = volcengineVodService.uploadVideoAndGetVid(file);
            return AjaxResult.success("视频上传成功", vid);
        } catch (Exception e) {
            log.error("视频上传失败", e);
            return AjaxResult.error("视频上传失败: " + e.getMessage());
        }
    }

    /**
     * 从本地文件上传
     */
    @PostMapping("/upload-local-file")
    @Operation(summary = "上传本地文件", description = "从本地文件路径上传视频获取Vid")
    public AjaxResult uploadLocalFile(
            @Parameter(description = "本地文件路径") @RequestParam String localFilePath) {
        try {
            String vid = volcengineVodService.uploadLocalFileAndGetVid(localFilePath);
            return AjaxResult.success("本地文件上传成功", vid);
        } catch (Exception e) {
            log.error("本地文件上传失败", e);
            return AjaxResult.error("本地文件上传失败: " + e.getMessage());
        }
    }

    /**
     * 提交 AI 翻译工作流
     */
    @PostMapping("/submit-ai-translation")
    @Operation(summary = "提交AI翻译", description = "为指定视频提交AI翻译工作流")
    public AjaxResult submitAITranslation(
            @Parameter(description = "视频ID") @RequestParam String vid,
            @Parameter(description = "源语言") @RequestParam String sourceLanguage,
            @Parameter(description = "目标语言") @RequestParam String targetLanguage,
            @Parameter(description = "客户端令牌") @RequestParam(required = false) String clientToken) {
        try {
            StartExecutionResponse response = volcengineVodService.submitAITranslationWorkflow(
                    vid, sourceLanguage, targetLanguage, clientToken);
            return AjaxResult.success("AI翻译工作流提交成功", response);
        } catch (Exception e) {
            log.error("AI翻译工作流提交失败", e);
            return AjaxResult.error("AI翻译工作流提交失败: " + e.getMessage());
        }
    }

    /**
     * 一站式服务：上传视频并提交AI翻译
     */
    @PostMapping("/upload-and-translate")
    @Operation(summary = "上传并翻译", description = "一站式服务：上传视频文件并提交AI翻译工作流")
    public AjaxResult uploadAndTranslate(
            @Parameter(description = "视频文件") @RequestParam("file") MultipartFile file,
            @Parameter(description = "源语言") @RequestParam String sourceLanguage,
            @Parameter(description = "目标语言") @RequestParam String targetLanguage) {
        try {
            StartExecutionResponse response = volcengineVodService.uploadAndTranslate(
                    file, sourceLanguage, targetLanguage);
            return AjaxResult.success("上传并翻译服务完成", response);
        } catch (Exception e) {
            log.error("上传并翻译服务失败", e);
            return AjaxResult.error("上传并翻译服务失败: " + e.getMessage());
        }
    }

    /**
     * 从本地文件一站式上传和翻译
     */
    @PostMapping("/upload-local-and-translate")
    @Operation(summary = "本地文件上传并翻译", description = "从本地文件路径上传视频并提交AI翻译工作流")
    public AjaxResult uploadLocalAndTranslate(
            @Parameter(description = "本地文件路径") @RequestParam String localFilePath,
            @Parameter(description = "源语言") @RequestParam String sourceLanguage,
            @Parameter(description = "目标语言") @RequestParam String targetLanguage) {
        try {
            StartExecutionResponse response = volcengineVodService.uploadLocalFileAndTranslate(
                    localFilePath, sourceLanguage, targetLanguage);
            return AjaxResult.success("本地文件上传并翻译服务完成", response);
        } catch (Exception e) {
            log.error("本地文件上传并翻译服务失败", e);
            return AjaxResult.error("本地文件上传并翻译服务失败: " + e.getMessage());
        }
    }

    /**
     * 提交媒体处理任务（原有功能）
     */
    @PostMapping("/start-execution")
    @Operation(summary = "提交媒体处理任务", description = "提交各种媒体处理任务")
    public AjaxResult startExecution(
            @Parameter(description = "视频ID") @RequestParam String vid,
            @Parameter(description = "任务类型") @RequestParam String taskType,
            @Parameter(description = "客户端令牌") @RequestParam(required = false) String clientToken) {
        try {
            StartExecutionResponse response = volcengineVodService.startExecution(vid, taskType, clientToken);
            return AjaxResult.success("媒体处理任务提交成功", response);
        } catch (Exception e) {
            log.error("媒体处理任务提交失败", e);
            return AjaxResult.error("媒体处理任务提交失败: " + e.getMessage());
        }
    }
}
