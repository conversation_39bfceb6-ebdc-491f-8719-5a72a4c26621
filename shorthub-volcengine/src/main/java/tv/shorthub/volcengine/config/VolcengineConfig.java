package tv.shorthub.volcengine.config;

import com.volcengine.ApiClient;
import com.volcengine.sign.Credentials;
import com.volcengine.vod20250101.Vod20250101Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import tv.shorthub.common.utils.StringUtils;

/**
 * 火山引擎客户端配置 - 2025年1月版本
 * 使用官方 vod20250101 API
 */
@Slf4j
@Configuration
public class VolcengineConfig {
    
    @Autowired
    private VolcengineProperties properties;
    
    /**
     * 创建 ApiClient
     * 支持AK/SK认证和环境变量自动读取
     */
    @Bean
    public ApiClient apiClient() {
        try {
            String accessKey = getAccessKey();
            String secretKey = getSecretKey();
            
            ApiClient client = new ApiClient()
                    .setCredentials(Credentials.getCredentials(accessKey, secretKey))
                    .setRegion(properties.getRegion());
            
            // 设置端点（如果配置了自定义端点）
            if (StringUtils.isNotEmpty(properties.getEndpoint())) {
                client.setEndpoint(properties.getEndpoint());
                log.info("使用自定义端点: {}", properties.getEndpoint());
            }
            
            // 设置调试模式
            client.setDebugging(properties.isDebugging());
            
            log.info("火山引擎ApiClient初始化成功，区域: {}", properties.getRegion());
            return client;
            
        } catch (Exception e) {
            log.error("火山引擎ApiClient初始化失败", e);
            throw new IllegalStateException("火山引擎客户端初始化失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 创建VOD API客户端 - 2025年1月版本
     */
    @Bean
    public Vod20250101Api vod20250101Api(ApiClient apiClient) {
        Vod20250101Api vodApi = new Vod20250101Api(apiClient);
        log.info("VOD 2025年1月版本 API客户端创建成功");
        return vodApi;
    }
    
    /**
     * 获取AccessKey，优先使用配置文件，其次使用环境变量
     */
    private String getAccessKey() {
        if (StringUtils.isNotEmpty(properties.getAccessKey())) {
            return properties.getAccessKey();
        }
        
        String envAK = System.getenv("VOLCENGINE_ACCESS_KEY");
        if (StringUtils.isNotEmpty(envAK)) {
            log.info("使用环境变量中的AccessKey");
            return envAK;
        }
        
        throw new IllegalStateException("未找到AccessKey，请在配置文件或环境变量中设置");
    }
    
    /**
     * 获取SecretKey，优先使用配置文件，其次使用环境变量
     */
    private String getSecretKey() {
        if (StringUtils.isNotEmpty(properties.getSecretKey())) {
            return properties.getSecretKey();
        }
        
        String envSK = System.getenv("VOLCENGINE_SECRET_KEY");
        if (StringUtils.isNotEmpty(envSK)) {
            log.info("使用环境变量中的SecretKey");
            return envSK;
        }
        
        throw new IllegalStateException("未找到SecretKey，请在配置文件或环境变量中设置");
    }
}