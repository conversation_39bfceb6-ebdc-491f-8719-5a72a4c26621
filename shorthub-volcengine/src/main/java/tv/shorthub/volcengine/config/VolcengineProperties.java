package tv.shorthub.volcengine.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 火山引擎配置类 - 精简版
 */
@Data
@Component
@ConfigurationProperties(prefix = "volcengine")
public class VolcengineProperties {
    
    /**
     * Access Key ID
     */
    private String accessKey;
    
    /**
     * Secret Access Key
     */
    private String secretKey;
    
    /**
     * 区域设置 (默认: cn-north-1)
     */
    private String region = "cn-north-1";
    
    /**
     * 自定义 Endpoint（可选）
     */
    private String endpoint;
    
    /**
     * 是否开启调试模式
     */
    private boolean debugging = false;
    
    /**
     * VOD服务配置
     */
    private Vod vod = new Vod();
    
    @Data
    public static class Vod {
        /**
         * 空间名称
         */
        private String spaceName;
    }
}