# ShortHub 火山引擎 VOD 模块

## 📋 功能概述

本模块提供火山引擎视频点播（VOD）服务的集成，当前支持：

- ✅ **AI 翻译工作流**：提交 AI 翻译任务（已实现）
- ✅ **媒体处理**：支持各种媒体处理任务（已实现）
- ⚠️ **媒资上传**：等待确认正确的 API 接口名称（待实现）
- ⚠️ **一站式服务**：依赖媒资上传 API（待实现）

## ⚠️ 重要说明

当前使用的 `volcengine-java-sdk-vod20250101` 版本 `0.2.23` 中，`ApplyUploadResponse` 和 `CommitUploadResponse` 类不存在，导致编译错误。

**需要确认的问题**：
1. 正确的媒资上传 API 接口名称
2. 对应的请求和响应类名
3. 是否需要升级 SDK 版本或使用不同的依赖

## 🚀 快速开始

### 1. 配置火山引擎凭证

在 `application.yml` 中配置：

```yaml
volcengine:
  access-key: YOUR_ACCESS_KEY
  secret-key: YOUR_SECRET_KEY
  region: cn-north-1
  debugging: false
  vod:
    space-name: YOUR_SPACE_NAME
```

### 2. 核心功能使用

#### 2.1 提交 AI 翻译工作流

```java
@Autowired
private VolcengineVodService volcengineVodService;

// 基本用法：提交 AI 翻译任务
StartExecutionResponse response = volcengineVodService.submitAITranslationWorkflow(
    "your_video_id",    // Vid（需要已存在的视频ID）
    "zh",               // 源语言（中文）
    "en",               // 目标语言（英文）
    "client_token"      // 客户端令牌（可选）
);

// 快捷方法：常用语言对
StartExecutionResponse response1 = volcengineVodService.translateChineseToEnglish(vid, clientToken);
StartExecutionResponse response2 = volcengineVodService.translateEnglishToChinese(vid, clientToken);
StartExecutionResponse response3 = volcengineVodService.translateChineseToJapanese(vid, clientToken);
StartExecutionResponse response4 = volcengineVodService.translateChineseToKorean(vid, clientToken);
```

#### 2.2 批量提交翻译任务

```java
// 批量翻译多个视频
String[] vids = {"video_1", "video_2", "video_3"};
Map<String, Object> result = volcengineVodService.batchSubmitAITranslationWorkflow(
    vids, "zh", "en");

// 查看批量处理结果
int total = (Integer) result.get("total");
int success = (Integer) result.get("success");
int failed = (Integer) result.get("failed");
Map<String, Object> details = (Map<String, Object>) result.get("results");
```

#### 2.3 获取支持的语言

```java
// 获取所有支持的语言列表
Map<String, String> languages = volcengineVodService.getSupportedLanguages();
// 返回: {"zh": "中文", "en": "英文", "ja": "日文", ...}
```

#### 2.4 其他媒体处理任务

```java
// 提交高光提取任务
StartExecutionResponse response = volcengineVodService.startExecution(
    "your_video_id",    // Vid
    "Highlight",        // 任务类型
    "client_token"      // 客户端令牌（可选）
);
```

### 3. 智能参数处理

本实现包含智能的参数处理机制：

- ✅ **自动参数验证**：检查语言代码有效性、Vid 格式等
- ✅ **智能参数设置**：通过反射自动查找和设置翻译参数
- ✅ **兼容性处理**：适配不同 SDK 版本的 API 差异
- ✅ **错误恢复**：即使某些参数设置失败，仍能提交基本任务

### 4. 待实现功能

以下功能因 SDK 类不存在而暂时注释：
- ❌ 媒资上传（ApplyUpload/CommitUpload）
- ❌ 一站式上传并翻译服务

## 🌐 REST API 接口

### 媒资上传相关

| 接口 | 方法 | 描述 |
|------|------|------|
| `/volcengine/vod/upload-video` | POST | 上传视频文件获取 Vid |
| `/volcengine/vod/upload-local-file` | POST | 从本地路径上传视频 |
| `/volcengine/vod/apply-upload` | POST | 申请上传获取上传地址 |
| `/volcengine/vod/commit-upload` | POST | 确认上传获取 Vid |

### AI 翻译相关

| 接口 | 方法 | 描述 |
|------|------|------|
| `/volcengine/vod/submit-ai-translation` | POST | 提交 AI 翻译工作流 |
| `/volcengine/vod/upload-and-translate` | POST | 上传视频并提交翻译 |
| `/volcengine/vod/upload-local-and-translate` | POST | 本地文件上传并翻译 |

### 使用示例

#### 上传视频并翻译

```bash
curl -X POST "http://localhost:8080/volcengine/vod/upload-and-translate" \
  -F "file=@/path/to/video.mp4" \
  -F "sourceLanguage=zh" \
  -F "targetLanguage=en"
```

#### 仅提交翻译任务

```bash
curl -X POST "http://localhost:8080/volcengine/vod/submit-ai-translation" \
  -d "vid=your_video_id" \
  -d "sourceLanguage=zh" \
  -d "targetLanguage=en"
```

## 📊 支持的语言代码

| 语言 | 代码 | 语言 | 代码 |
|------|------|------|------|
| 中文 | zh | 英文 | en |
| 日文 | ja | 韩文 | ko |
| 法文 | fr | 德文 | de |
| 西班牙文 | es | 俄文 | ru |

## 🔄 完整的媒资上传流程

```mermaid
sequenceDiagram
    participant Client
    participant Service
    participant Volcengine

    Client->>Service: uploadAndTranslate(file, zh, en)
    Service->>Volcengine: applyUpload(fileName, fileSize)
    Volcengine-->>Service: uploadAddress + sessionKey
    Service->>Volcengine: uploadFile(file, uploadAddress)
    Service->>Volcengine: commitUpload(sessionKey)
    Volcengine-->>Service: Vid
    Service->>Volcengine: submitAITranslationWorkflow(vid, zh, en)
    Volcengine-->>Service: translationTaskId
    Service-->>Client: StartExecutionResponse
```

## 🧪 测试

运行测试类验证功能：

```bash
# 运行所有测试
mvn test -Dtest=AITranslationWorkflowTest

# 运行特定测试
mvn test -Dtest=AITranslationWorkflowTest#testSubmitAITranslationWorkflow
```

### 测试文件准备

在 `src/test/resources/` 目录下放置测试视频文件：

```
src/test/resources/
└── test_video.mp4
```

## 🔧 集成到现有系统

### 在 Admin 模块中使用

```java
@RestController
@RequestMapping("/admin/video")
public class VideoController {
    
    @Autowired
    private VolcengineVodService volcengineVodService;
    
    @PostMapping("/upload-and-translate")
    public AjaxResult uploadAndTranslate(
            @RequestParam("file") MultipartFile file,
            @RequestParam String sourceLanguage,
            @RequestParam String targetLanguage) {
        
        try {
            StartExecutionResponse response = volcengineVodService.uploadAndTranslate(
                file, sourceLanguage, targetLanguage);
            return AjaxResult.success("上传并翻译成功", response);
        } catch (Exception e) {
            return AjaxResult.error("操作失败: " + e.getMessage());
        }
    }
}
```

### 与现有文件上传系统集成

```java
// 在现有的文件上传完成后，上传到火山引擎
@PostMapping("/upload")
public AjaxResult uploadFile(MultipartFile file) {
    // 1. 现有的上传到 R2/本地逻辑
    String localPath = fileUploadService.upload(file);
    
    // 2. 同时上传到火山引擎获取 Vid
    String vid = volcengineVodService.uploadVideoAndGetVid(file);
    
    // 3. 保存 Vid 到数据库
    // dramaService.updateVid(contentId, vid);
    
    return AjaxResult.success("上传成功", Map.of("localPath", localPath, "vid", vid));
}
```

## ⚠️ 注意事项

1. **文件上传实现**：当前 `uploadFileToVolcengine` 方法需要根据火山引擎的具体上传协议实现
2. **错误处理**：建议在生产环境中添加重试机制和详细的错误处理
3. **异步处理**：AI 翻译是异步任务，需要通过回调或轮询获取结果
4. **费用控制**：AI 翻译会产生费用，建议添加使用量控制

## 📚 相关文档

- [火山引擎 VOD 官方文档](https://www.volcengine.com/docs/4/)
- [SubmitAITranslationWorkflow API](https://www.volcengine.com/docs/4/1584290)
- [媒资上传概述](https://www.volcengine.com/docs/4/65640)
