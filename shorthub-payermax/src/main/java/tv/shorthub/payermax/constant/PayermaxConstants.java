package tv.shorthub.payermax.constant;

/**
 * Payermax支付常量
 */
public class PayermaxConstants {
    
    /**
     * 计费周期定义
     */
    public static class PeriodUnit {
        /**
         * 按天周期
         */
        public static final String DAY = "D";

        /**
         * 按周周期
         */
        public static final String WEEK = "W";

        /**
         * 按月周期
         */
        public static final String MONTH = "M";

        /**
         * 按季度周期（需要转换为按月，3个月一次）
         */
        public static final String QUARTER = "QUARTER";

        /**
         * 按年周期
         */
        public static final String YEAR = "Y";
    }

    /**
     * 订阅状态
     */
    public static class SubscriptionStatus {
        /**
         * 激活
         */
        public static final String ACTIVE = "ACTIVE";
        
        /**
         * 取消
         */
        public static final String CANCEL = "CANCEL";
        
        /**
         * 暂停
         */
        public static final String SUSPENDED = "SUSPENDED";
        
        /**
         * 过期
         */
        public static final String EXPIRED = "EXPIRED";
    }
} 