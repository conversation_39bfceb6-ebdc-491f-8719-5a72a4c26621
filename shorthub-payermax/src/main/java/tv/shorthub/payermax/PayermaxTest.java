package tv.shorthub.payermax;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpEntity;
import org.springframework.web.client.RestTemplate;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.core.util.StrUtil;
import tv.shorthub.common.utils.DateUtils;

import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.Signature;
import java.security.spec.PKCS8EncodedKeySpec;
import java.util.Base64;

public class PayermaxTest {

    // Payermax配置信息
    private static final String MERCHANT_NO = "SDP01010115688123";  // 替换为你的商户号
    private static final String APP_ID = "f8c68bc435cb4022a4faf5270d89b4b8";           // 替换为你的应用ID
    private static final String PRIVATE_KEY = "MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQDP+Wdi7bPp71nt7xfnmZVApAUU60uwFGypdDHF0NFbSCXEQZ3HSm7P1uNPYhcIZ+rwH+ty8wrtH/NvZSMoBKmpG+fOU4IXvLGDBDS50uhjQjYjiaARNICXxrHqOQHOE7S2AlfBZ0XtKrzivGW1rcZRED/Rlg2zUeUARhKLcJX/TU4WM5feUOupDokGx9s2dBU/GDx8DPuTPl2TO3jST2CjmQXw9RhpL1ulRX+E4bGhdFxszdOBwzzdTnhHAIGNOFW2EblOaNDLE/TcxIv6mwPQIIjypD5V/MnU4QzFg+lNgR9YdSJy1lpyFkhiu5Zu79RAELCIxEo+jkYN7Jy5QmgpAgMBAAECggEABnbmYYhpc/n3teIIFcUWmFEw2OmyaylLXQi1UQSGCWa988fQBMU5xLXq0Alzaqm46gNX+8qgWjwIwqY7uESi1b8+DpIBGcXzT5L7z5MwcYC58uireXVuvNOqgpEIjxRaOjZq7gz3ewvTHgoZ8D0pTFTrYuidiZIgAojrDntZUjuRb937En1NZrb0+eri0WQUb4+WqqJj08TXJaBFGiuVsuEzedc/JpTwix5Tm4b9LIiOS0oU7ZFSquIdYRAqzrZ6cVyQYHBfofgtbiJ0x5UiVjb+J8kn4cMxXOzbd+nq4NZxaXMGA2t4a7wrSdOZ7HFpC5gu1g0EYivLzRqSV2n2oQKBgQDU8cA3Feoap/+LAUZ0Vc1Xj+49DHfoJ8lMpVvwZHZ/tOM8fx0o2aMWwgMhX0By92mDgTZSD9HirBl/NFGgPn3GLeskOOmGwqKAkhEO/LuvpOGF/LIvOmmbVbZoTxwXjPALHPA5qQ85ZDkbJxGjyFDY9u2UDwj5LGkWKjO9rd8bGQKBgQD6BmT/GWQDV99UNHXiSt2eHOmlrfhm2uUZ1cP2Ktd12xDaVOFJ7a8rK6P2wo73R+rtjLH79FXr5sZfFYab08GcK9sWcJY/EStv9XgmxE4zYKqyDqHJiey0pRy02W1Cf5Ygd6kLSqzfqLhHd5nvdi+I6j+oURMzGI2Eelp9VoVnkQKBgQCGVtFdryyWjn/PsQlCoVK/N+UjjHck9dyvxu5elPKRFIv7AXJi8BJMbC9PYWkmXmGpfd3IHYTazJn1Pwtz3Zi5awSaQHFK/AVKuVubqeO6JnnEbqv20ZIIViWSXr4pOVfCmwoORlJ9cXX5ZEdjnR8sEXPGYUnW8KaHPi6QLl2/GQKBgAHggmmX2zRbOIX9TtVAEqHZPbfMsoyzH/0tqabzc2KjHmFFOdvAOQxt63EyzLf2hkD6SWvGzc5TkTGti7EnydBOU/q7JaWbj2Prx9ciqiOgmid0DKriqjBA/RIuf698HbhorZA0I1AaiCehMjql/H1epNOyqB4+FPHlqUA/ogjBAoGBAKTcXiah6QLYgX0f8zzd8MMVoGQ5V2Ir+PQZ+VSex6iB+IqiBd6KVfUTAwTAP8yU7pMtEOq1IzUFw2+G7wTehiDNBYW0Fo8ijSTfp+0rYHhOmhB0nkVnOio0R3cDwAoGlHXJ00FIkQ47DOHeqUkpbWhcJiVLxaSoJmSCF6iKlPss"; // 替换为你的私钥
    private static final String GATEWAY_URL = "https://pay-gate-uat.payermax.com"; // Payermax API网关地址
    private static final String NOTIFY_URL = "https://ssmapi.shorthub.tv/payermax/payment/notify"; // 支付回调地址
    private static final String RETURN_URL = "https://ssmapi.shorthub.tv/payment/result"; // 支付成功跳转地址

    public static void main(String[] args) {
        try {
            // 构建请求参数
            JSONObject params = new JSONObject();
            params.put("version", "1.4");
            params.put("keyVersion", "1");
            params.put("requestTime", DateUtils.parseDateToStr("yyyy-MM-dd'T'HH:mm:ss.SSSXXX", DateUtils.getNowDate()));
            params.put("appId", APP_ID);
            params.put("merchantNo", MERCHANT_NO);

            JSONObject data = new JSONObject();
            data.put("outTradeNo", IdUtil.getSnowflakeNextIdStr());
            data.put("integrate", "Hosted_Checkout");
            data.put("subject", "Buy");
            data.put("totalAmount", 199);
            data.put("currency", "USD");
            data.put("userId", "userId1");
            data.put("language", "zh-TW");
            data.put("frontCallbackUrl", NOTIFY_URL);
            data.put("notifyUrl", NOTIFY_URL);
            params.put("data", data);


            // 生成签名
            String sign = generateSign(params);
            // 创建请求头
            HttpHeaders headers = new HttpHeaders();
            headers.set("Content-Type", "application/json");
            headers.set("sign", sign);

            // 打印请求参数
            System.out.println("Request parameters:");
            System.out.println(params.toJSONString());
            System.out.println("Request sign:");
            System.out.println(sign);

            // 发送请求
            RestTemplate restTemplate = new RestTemplate();
            HttpEntity<String> requestEntity = new HttpEntity<>(params.toJSONString(), headers);
            String response = restTemplate.postForObject(
                GATEWAY_URL + "/aggregate-pay/api/gateway/orderAndPay",
                requestEntity,
                String.class
            );

            // 打印响应结果
            System.out.println("\nResponse:");
            System.out.println(response);

        } catch (Exception e) {
            System.err.println("Error occurred while testing payment creation:");
            e.printStackTrace();
        }
    }

    /**
     * 生成签名
     */
    private static String generateSign(JSONObject params) {
        try {
            String content = params.toString();
            System.out.println("待签名字符串：");
            System.out.println(content);
            
            String privateKeyPEM = PRIVATE_KEY
                .replace("-----BEGIN PRIVATE KEY-----", "")
                .replace("-----END PRIVATE KEY-----", "")
                .replaceAll("\\s", "");
            
            byte[] privateKeyBytes = Base64.getDecoder().decode(privateKeyPEM);
            PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(privateKeyBytes);
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            PrivateKey privateKey = keyFactory.generatePrivate(keySpec);
            
            // 4. 使用SHA256WithRSA进行签名
            Signature signature = Signature.getInstance("SHA256withRSA");
            signature.initSign(privateKey);
            signature.update(content.getBytes("UTF-8"));
            
            // 5. 对签名结果进行Base64编码
            byte[] signed = signature.sign();
            return Base64.getEncoder().encodeToString(signed);
            
        } catch (Exception e) {
            throw new RuntimeException("Failed to generate signature", e);
        }
    }
} 