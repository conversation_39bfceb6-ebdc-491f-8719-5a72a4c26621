package tv.shorthub.payermax.service;

import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.web.client.RestTemplate;
import tv.shorthub.common.utils.DateUtils;
import tv.shorthub.payermax.config.PayermaxConfig;

import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.Signature;
import java.security.spec.PKCS8EncodedKeySpec;
import java.util.Base64;

@Slf4j
public class BasePayermax {

    @Autowired
    public PayermaxConfig payermaxConfig;

    public JSONObject buildParams(String version) {
        JSONObject params = new JSONObject();
        params.put("version", version);
        params.put("keyVersion", "1");
        params.put("requestTime", DateUtils.parseDateToStr("yyyy-MM-dd'T'HH:mm:ss.SSSXXX", DateUtils.getNowDate()));
        params.put("appId", payermaxConfig.getAppId());
        params.put("merchantNo", payermaxConfig.getMerchantNo());
        return params;
    }
    public JSONObject post(JSONObject data, String path) {
        return post(data, path, "1.4");
    }

    public JSONObject post(JSONObject data, String path, String version) {

        JSONObject params = buildParams(version);
        params.put("data", data);

        // 生成签名
        String sign = generateSign(params);
        // 创建请求头
        HttpHeaders headers = new HttpHeaders();
        headers.set("Content-Type", "application/json");
        headers.set("sign", sign);

        log.info("Request parameters: {}", params.toJSONString());

        // 发送请求
        RestTemplate restTemplate = new RestTemplate();
        HttpEntity<String> requestEntity = new HttpEntity<>(params.toJSONString(), headers);
        JSONObject response = restTemplate.postForObject(
                payermaxConfig.getGatewayUrl() + path,
                requestEntity,
                JSONObject.class
        );

        log.info("PayerMax response: {}", response);
        return response;
    }


    /**
     * 生成签名
     */
    public String generateSign(JSONObject params) {
        try {
            String content = params.toString();

            byte[] privateKeyBytes = Base64.getDecoder().decode(payermaxConfig.getPrivateKey());
            PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(privateKeyBytes);
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            PrivateKey privateKey = keyFactory.generatePrivate(keySpec);

            // 4. 使用SHA256WithRSA进行签名
            Signature signature = Signature.getInstance("SHA256withRSA");
            signature.initSign(privateKey);
            signature.update(content.getBytes("UTF-8"));

            // 5. 对签名结果进行Base64编码
            byte[] signed = signature.sign();
            return Base64.getEncoder().encodeToString(signed);

        } catch (Exception e) {
            throw new RuntimeException("Failed to generate signature", e);
        }
    }
}
