package tv.shorthub.payermax.service;

import com.alibaba.fastjson2.JSONObject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class PayermaxSession extends BasePayermax {

    private static final String APPLY_DROPIN_SESSION = "/aggregate-pay/api/gateway/applyDropinSession";

    public JSONObject applyDropinSession(
            String totalAmount,
            String mitType,
            String currency,
            String country,
            String userId,
            String referralCode,
            List<String> componentList
    ) {
        JSONObject data = new JSONObject();
        data.put("totalAmount", totalAmount);
        data.put("currency", currency);
        data.put("country", country);
        data.put("userId", userId);
        if (StringUtils.isNotEmpty(mitType)) {
            data.put("mitType", mitType);
        }
        if (StringUtils.isNotEmpty(referralCode)) {
            data.put("referralCode", referralCode);
        }
        if (CollectionUtils.isNotEmpty(componentList)) {
            data.put("componentList", componentList);
        }
        return post(data, APPLY_DROPIN_SESSION);
    }
}
