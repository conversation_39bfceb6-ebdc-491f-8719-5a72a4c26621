package tv.shorthub.payermax.service;

import com.alibaba.fastjson2.JSONObject;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;

import org.springframework.stereotype.Component;
import tv.shorthub.common.utils.DateUtils;
import tv.shorthub.payermax.constant.PayermaxConstants;

@Component
public class PayermaxSubscribeService extends BasePayermax {

    private static final String SUBSCRIPTION_CREATE = "/aggregate-pay/api/gateway/subscriptionCreate";
    private static final String SUBSCRIPTION_QUERY = "/aggregate-pay/api/gateway/subscriptionQuery";
    private static final String SUBSCRIPTION_CANCEL = "/aggregate-pay/api/gateway/subscriptionCancel";

    /**
     * 处理计费周期，支持季度扣费
     * @param periodUnit 原始计费周期单位
     * @return 包含periodUnit和periodCount的JSONObject
     */
    private JSONObject processPeriodRule(String periodUnit) {
        String actualPeriodUnit = periodUnit;
        int periodCount = 1;
        
        // 如果是季度扣费，转换为按月扣费，每3个月一次
        if (PayermaxConstants.PeriodUnit.QUARTER.equals(periodUnit)) {
            actualPeriodUnit = PayermaxConstants.PeriodUnit.MONTH;
            periodCount = 3;
        }
        
        JSONObject periodRule = new JSONObject();
        periodRule.put("periodUnit", actualPeriodUnit);
        periodRule.put("periodCount", periodCount);
        return periodRule;
    }

    public JSONObject subscriptionCreate(
            String orderNo,
            String userId,
            String language,
            String callbackUrl,
            Long totalPeriods,
            String periodUnit,
            BigDecimal amount,
            String currency,
            BigDecimal discountAmount

    ) {
        JSONObject data = new JSONObject();
        data.put("subscriptionRequestId", orderNo);
        data.put("userId", userId);
//        data.put("language", language);
        data.put("callbackUrl", callbackUrl);

        JSONObject subscriptionPlan = new JSONObject();
        subscriptionPlan.put("subject", "Plan");
        subscriptionPlan.put("description", "Plan");
        subscriptionPlan.put("totalPeriods", totalPeriods);

        JSONObject periodRule = processPeriodRule(periodUnit);
        subscriptionPlan.put("periodRule", periodRule);

        JSONObject periodAmount = new JSONObject();
        periodAmount.put("amount", amount);
        periodAmount.put("currency", currency);
        subscriptionPlan.put("periodAmount", periodAmount);
        subscriptionPlan.put("firstPeriodStartDate", DateUtils.parseDateToStr("yyyy-MM-dd'T'HH:mm:ss.SSSXXX", DateUtils.addMinutes(DateUtils.getNowDate(), 1)));

        if (null != discountAmount) {
            JSONObject trialPeriodConfig = new JSONObject();
            trialPeriodConfig.put("trialPeriodCount", 1);
            JSONObject trialPeriodAmount = new JSONObject();
            trialPeriodAmount.put("amount", discountAmount);
            trialPeriodAmount.put("currency", currency);
            trialPeriodConfig.put("trialPeriodAmount", trialPeriodAmount);
            subscriptionPlan.put("trialPeriodConfig", trialPeriodConfig);
        }

        data.put("subscriptionPlan", subscriptionPlan);

        return post(data, SUBSCRIPTION_CREATE);
    }


    public JSONObject subscriptionQuery(String orderNo) {
        JSONObject data = new JSONObject();
        data.put("subscriptionRequestId", orderNo);
        return post(data, SUBSCRIPTION_QUERY, "1.5");
    }



    public JSONObject subscriptionCancel(String subscriptionNo) {
        JSONObject data = new JSONObject();
        data.put("subscriptionNo", subscriptionNo);
        return post(data, SUBSCRIPTION_CANCEL, "1.5");
    }

}
