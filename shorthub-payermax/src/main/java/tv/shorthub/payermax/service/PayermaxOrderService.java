package tv.shorthub.payermax.service;


import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tv.shorthub.payermax.config.PayermaxConfig;

import java.math.BigDecimal;


@Component
@Slf4j
public class PayermaxOrderService extends BasePayermax {
    private static final String ORDER_AND_PAY = "/aggregate-pay/api/gateway/orderAndPay";
    private static final String ORDER_QUERY = "/aggregate-pay/api/gateway/orderQuery";
    private static final String REFUND = "/aggregate-pay/api/gateway/refund";
    private static final String REFUND_QUERY = "/aggregate-pay/api/gateway/refundQuery";

    @Autowired
    PayermaxConfig payermaxConfig;

    /**
     * 创建订单
     * @param outTradeNo 订单号
     * @param totalAmount 金额, 单位/分
     * @param subject 订单标题
     * @param userId 用户ID
     * @param language 语言
     * @param currency 币种
     * @return
     */
    public JSONObject orderAndPay(
            String integrate,
            String outTradeNo,
            BigDecimal totalAmount,
            String subject,
            String userId,
            String language,
            String currency,
            String frontCallbackUrl,
            String notifyUrl,
            String cancelUrl,
            JSONObject subscriptionPlan,
            JSONObject paymentDetail,
            String country
    ) {
        try {
            // 构建请求参数
            JSONObject data = new JSONObject();
            data.put("outTradeNo", outTradeNo);
            data.put("integrate", integrate);
            data.put("subject", subject);
            data.put("totalAmount", totalAmount);
            data.put("currency", currency);
            data.put("userId", userId);
            data.put("country", country);
            data.put("language", language);
            data.put("frontCallbackUrl", frontCallbackUrl);
            data.put("terminalType", "WEB");
            data.put("notifyUrl", notifyUrl);

            if (null != subscriptionPlan) {
                data.put("subscriptionPlan", subscriptionPlan);
            }

            if (null != paymentDetail) {
                data.put("paymentDetail", paymentDetail);
            }

            return post(data, ORDER_AND_PAY, "1.5");
        } catch (Exception e) {
            log.error("Error occurred while testing payment creation:", e);
        }
        return null;
    }

    /**
     * 订单查询
     * @param outTradeNo 订单号
     * @return
     */
    public JSONObject orderQuery(String outTradeNo) {
        JSONObject data = new JSONObject();
        data.put("outTradeNo", outTradeNo);
        return post(data, ORDER_QUERY);
    }

    /**
     * 退款
     * @param outRefundNo 退款单号
     * @param refundAmount 金额, 单位/分
     * @param refundCurrency 币种
     * @param outTradeNo 原订单号
     * @return
     */
    public JSONObject refund(String outRefundNo, BigDecimal refundAmount, String refundCurrency, String outTradeNo, String comments) {
        JSONObject data = new JSONObject();
        data.put("outRefundNo", outRefundNo);
        data.put("refundAmount", refundAmount);
        data.put("refundCurrency", refundCurrency);
        data.put("outTradeNo", outTradeNo);
        data.put("comments", comments);
        return post(data, REFUND);
    }

    /**
     * 退款查询
     * @param outRefundNo 退款单号
     * @return
     */
    public JSONObject refundQuery(String outRefundNo) {
        JSONObject data = new JSONObject();
        data.put("outRefundNo", outRefundNo);
        return post(data, REFUND_QUERY);
    }


}
