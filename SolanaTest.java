import java.security.MessageDigest;
import java.util.Arrays;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import org.bitcoinj.crypto.HDKeyDerivation;
import org.bitcoinj.crypto.DeterministicKey;
import org.web3j.crypto.MnemonicUtils;
import net.i2p.crypto.eddsa.spec.EdDSANamedCurveTable;
import net.i2p.crypto.eddsa.spec.EdDSAPrivateKeySpec;
import net.i2p.crypto.eddsa.spec.EdDSAParameterSpec;
import net.i2p.crypto.eddsa.EdDSAPrivateKey;
import net.i2p.crypto.eddsa.EdDSAPublicKey;

public class SolanaTest {
    
    public static void main(String[] args) throws Exception {
        String mnemonic = "abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon about";
        testSolanaKeyGeneration(mnemonic);
    }
    
    public static void testSolanaKeyGeneration(String mnemonic) throws Exception {
        System.out.println("=== Solana Key Generation Test ===");
        System.out.println("Mnemonic: " + mnemonic);
        
        // 1. Generate seed from mnemonic
        byte[] seed = MnemonicUtils.generateSeed(mnemonic, "");
        System.out.println("Seed length: " + seed.length);
        
        // 2. BIP44 derivation: m/44'/501'/0'/0'
        DeterministicKey masterKey = HDKeyDerivation.createMasterPrivateKey(seed);
        DeterministicKey purposeKey = HDKeyDerivation.deriveChildKey(masterKey, 44 | 0x80000000);
        DeterministicKey coinKey = HDKeyDerivation.deriveChildKey(purposeKey, 501 | 0x80000000);
        DeterministicKey accountKey = HDKeyDerivation.deriveChildKey(coinKey, 0 | 0x80000000);
        DeterministicKey addressKey = HDKeyDerivation.deriveChildKey(accountKey, 0 | 0x80000000);
        
        byte[] bip44PrivateKey = addressKey.getPrivKeyBytes();
        System.out.println("BIP44 Private Key length: " + bip44PrivateKey.length);
        System.out.println("BIP44 Private Key (hex): " + bytesToHex(bip44PrivateKey));
        
        // 3. Generate Ed25519 keypair
        EdDSAParameterSpec spec = EdDSANamedCurveTable.getByName("Ed25519");
        EdDSAPrivateKeySpec privKeySpec = new EdDSAPrivateKeySpec(bip44PrivateKey, spec);
        EdDSAPrivateKey privKey = new EdDSAPrivateKey(privKeySpec);
        EdDSAPublicKey pubKey = new EdDSAPublicKey(
            new net.i2p.crypto.eddsa.spec.EdDSAPublicKeySpec(privKey.getA(), spec));
        
        byte[] publicKeyBytes = pubKey.getAbyte();
        System.out.println("Ed25519 Public Key length: " + publicKeyBytes.length);
        System.out.println("Ed25519 Public Key (hex): " + bytesToHex(publicKeyBytes));
        
        // 4. Generate Solana address (Base58 encode public key)
        String address = encodeBase58(publicKeyBytes);
        System.out.println("Solana Address: " + address);
        
        // Expected for this mnemonic: should match Phantom wallet result
    }
    
    private static String bytesToHex(byte[] bytes) {
        StringBuilder result = new StringBuilder();
        for (byte b : bytes) {
            result.append(String.format("%02x", b));
        }
        return result.toString();
    }
    
    private static final String ALPHABET = "**********************************************************";
    
    private static String encodeBase58(byte[] input) {
        if (input.length == 0) return "";
        
        int[] digits = new int[input.length * 2];
        int digitslen = 1;
        
        for (int i = 0; i < input.length; i++) {
            int carry = input[i] & 0xFF;
            for (int j = 0; j < digitslen; j++) {
                carry += digits[j] << 8;
                digits[j] = carry % 58;
                carry /= 58;
            }
            while (carry > 0) {
                digits[digitslen++] = carry % 58;
                carry /= 58;
            }
        }
        
        // Handle leading zeros
        int zeroslen = 0;
        while (zeroslen < input.length && input[zeroslen] == 0) {
            zeroslen++;
        }
        
        StringBuilder result = new StringBuilder();
        for (int i = 0; i < zeroslen; i++) {
            result.append(ALPHABET.charAt(0));
        }
        for (int i = digitslen - 1; i >= 0; i--) {
            result.append(ALPHABET.charAt(digits[i]));
        }
        
        return result.toString();
    }
}