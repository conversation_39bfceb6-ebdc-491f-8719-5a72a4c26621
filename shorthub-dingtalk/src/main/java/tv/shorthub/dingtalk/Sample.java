package tv.shorthub.dingtalk;

import com.aliyun.dingtalkoauth2_1_0.Client;
import com.aliyun.dingtalkoauth2_1_0.models.GetAccessTokenRequest;
import com.aliyun.tea.TeaException;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teautil.Common;

/**
 * 钉钉应用获取接口调用凭证示例
 * accessToken: 699b5486ab66307982f1b4924e132c2b
 */
public class Sample {
    // 应用凭证，应用 Client ID 和 Client Secret
    private static final String APP_KEY = "dingai0gzk3fohe7d6kq";
    private static final String APP_SECRET = "uJijCdhBhxffca63mTReCtzO2EubFqUePmgonu72JqKVMhAWiIyoB8b1mDfSLNOb";

    /**
     * 创建钉钉客户端实例
     */
    public static Client createClient() throws Exception {
        Config config = new Config();
        config.protocol = "https";
        config.regionId = "central";
        return new Client(config);
    }

    public static void main(String[] args) {
        try {
            // 创建客户端实例
            Client client = createClient();

            // 构建获取access_token的请求
            GetAccessTokenRequest request = new GetAccessTokenRequest()
                    .setAppKey(APP_KEY)
                    .setAppSecret(APP_SECRET);

            // 发送请求获取access_token
            String accessToken = client.getAccessToken(request).getBody().getAccessToken();
            System.out.println("accessToken: " + accessToken);

        } catch (TeaException err) {
            // 处理业务异常
            if (!Common.empty(err.code) && !Common.empty(err.message)) {
                System.err.println("钉钉API错误: " + err.code + " - " + err.message);
            }
        } catch (Exception e) {
            // 处理其他异常
            System.err.println("程序异常: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
