package tv.shorthub.dingtalk.service.impl;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.apache.commons.lang3.StringUtils;
import tv.shorthub.dingtalk.config.DingTalkProperties;
import tv.shorthub.dingtalk.service.DingTalkNotificationService;
import tv.shorthub.dingtalk.service.DingTalkService;
import tv.shorthub.dingtalk.model.NotificationExtInfo;
import tv.shorthub.system.domain.SysDingtalkLog;
import tv.shorthub.system.mapper.SysDingtalkLogMapper;
import tv.shorthub.system.service.IAppOrderInfoService;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;

@Slf4j
@Service
@RequiredArgsConstructor
public class DingTalkNotificationServiceImpl implements DingTalkNotificationService {

    private final DingTalkService dingTalkService;
    private final DingTalkProperties dingTalkProperties;
    private final SysDingtalkLogMapper dingtalkLogMapper;
    IAppOrderInfoService appOrderInfoService;
    @Override
    public void sendPaymentSuccessNotification(String orderNo, BigDecimal amount, String currency, String userEmail, String paymentMethod, String sourceEvent, String paymentTime) {
        sendPaymentSuccessNotification(orderNo, amount, currency, userEmail, paymentMethod, sourceEvent, paymentTime, null);
    }

    @Override
    public void sendPaymentSuccessNotification(String orderNo, BigDecimal amount, String currency, String userEmail, String paymentMethod, String sourceEvent, String paymentTime, NotificationExtInfo extInfo) {
        String businessType = "PAYMENT";
        if (!tryLockNotification(businessType, orderNo, sourceEvent)) {
            return; // 获取锁失败，说明事件已在处理中
        }

        JSONObject response = null;
        String markdownText = "";
        try {
            String webhookUrl = getPaymentWebhookUrl();
            if (StringUtils.isBlank(webhookUrl)) {
                log.warn("[钉钉通知] Webhook URL 未配置，跳过发送. 业务ID: {}, 事件: {}", orderNo, sourceEvent);
                return;
            }

            log.info("[钉钉通知] 准备发送支付成功通知. 订单号: {}", orderNo);
            StringBuilder markdown = new StringBuilder();
            markdown.append("#### PayPal 支付\n\n")
                    .append("**订单号:** ").append(orderNo).append("\n\n")
                    .append("**金额:** ").append((amount != null) ? amount.toPlainString() : "N/A").append(" ").append(StringUtils.defaultIfBlank(currency, "")).append("\n\n")
                    .append("**用户:** ").append(StringUtils.defaultIfBlank(userEmail, "N/A")).append("\n\n");
            
            // 添加扩展信息
            if (extInfo != null) {
                if (StringUtils.isNotBlank(extInfo.getCountry())) {
                    markdown.append("**国家/地区:** ").append(extInfo.getCountry()).append("\n\n");
                }
                if (StringUtils.isNotBlank(extInfo.getClientIp())) {
                    markdown.append("**IP地址:** ").append(extInfo.getClientIp()).append("\n\n");
                }
                if (StringUtils.isNotBlank(extInfo.getLanguage())) {
                    markdown.append("**语言:** ").append(extInfo.getLanguage()).append("\n\n");
                }
                if (StringUtils.isNotBlank(extInfo.getClientUserAgent())) {
                    // 简化 User-Agent 显示
                    String ua = extInfo.getClientUserAgent();
                    String simpleUA = simplifyUserAgent(ua);
                    markdown.append("**设备:** ").append(simpleUA).append("\n\n");
                }
            }
            
            markdown.append("**支付时间:** ").append(StringUtils.defaultIfBlank(paymentTime, LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))).append("\n");
            markdownText = markdown.toString();
            
            // 添加调试日志
            log.info("[钉钉通知] 发送的消息内容: {}", markdownText);
            log.info("[钉钉通知] 扩展信息状态: {}", extInfo != null ? "存在" : "不存在");
            if (extInfo != null) {
                log.info("[钉钉通知] 扩展信息详情: country={}, clientIp={}, language={}, userAgent={}", 
                    extInfo.getCountry(), extInfo.getClientIp(), extInfo.getLanguage(), 
                    extInfo.getClientUserAgent() != null ? extInfo.getClientUserAgent().substring(0, Math.min(50, extInfo.getClientUserAgent().length())) : null);
            }
            
            response = dingTalkService.sendMarkdownMessage(webhookUrl, "付款成功", markdownText);
        } catch (Exception e) {
            log.error("[钉钉通知] 发送钉钉消息时发生未知异常. 业务ID: {}, 事件: {}", orderNo, sourceEvent, e);
        } finally {
            updateNotificationLog(orderNo, sourceEvent, markdownText, response);
        }
    }

    @Override
    public void sendSubscriptionSuccessNotification(String subscriptionId, String planId, BigDecimal amount, String currency, String userEmail, String sourceEvent, String paymentTime) {
        sendSubscriptionSuccessNotification(subscriptionId, planId, amount, currency, userEmail, sourceEvent, paymentTime, null);
    }

    @Override
    public void sendSubscriptionSuccessNotification(String subscriptionId, String planId, BigDecimal amount, String currency, String userEmail, String sourceEvent, String paymentTime, NotificationExtInfo extInfo) {
        // 统一使用 SUBSCRIPTION_PAYMENT 作为业务类型，使用 subscriptionId 作为唯一业务ID
        String businessType = "SUBSCRIPTION_PAYMENT";
        if (!tryLockNotification(businessType, subscriptionId, sourceEvent)) {
            return;
        }

        JSONObject response = null;
        String markdownText = "";
        try {
            String webhookUrl = getPaymentWebhookUrl();
            if (StringUtils.isBlank(webhookUrl)) {
                log.warn("[钉钉通知] Webhook URL 未配置，跳过发送. 业务ID: {}, 事件: {}", subscriptionId, sourceEvent);
                return;
            }

            log.info("[钉钉通知] 准备发送订阅成功通知 (首次). 订阅号: {}", subscriptionId);
            StringBuilder markdown = new StringBuilder();
            markdown.append("#### PayPal 订阅成功\n\n")
                    .append("**订阅号:** ").append(subscriptionId).append("\n\n")
                    .append("**支付金额:** ").append((amount != null) ? amount.toPlainString() : "N/A").append(" ").append(StringUtils.defaultIfBlank(currency, "").toUpperCase()).append("\n\n")
                    .append("**用户:** ").append(StringUtils.defaultIfBlank(userEmail, "N/A")).append("\n\n");
            
            // 添加扩展信息
            if (extInfo != null) {
                if (StringUtils.isNotBlank(extInfo.getCountry())) {
                    markdown.append("**国家/地区:** ").append(extInfo.getCountry()).append("\n\n");
                }
                if (StringUtils.isNotBlank(extInfo.getClientIp())) {
                    markdown.append("**IP地址:** ").append(extInfo.getClientIp()).append("\n\n");
                }
                if (StringUtils.isNotBlank(extInfo.getLanguage())) {
                    markdown.append("**语言:** ").append(extInfo.getLanguage()).append("\n\n");
                }
                if (StringUtils.isNotBlank(extInfo.getClientUserAgent())) {
                    String simpleUA = simplifyUserAgent(extInfo.getClientUserAgent());
                    markdown.append("**设备:** ").append(simpleUA).append("\n\n");
                }
            }
            
            markdown.append("**支付时间:** ").append(StringUtils.defaultIfBlank(paymentTime, LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))).append("\n");
            markdownText = markdown.toString();
            response = dingTalkService.sendMarkdownMessage(webhookUrl, "订阅成功", markdownText);
        } catch (Exception e) {
             log.error("[钉钉通知] 发送钉钉消息时发生未知异常. 业务ID: {}, 事件: {}", subscriptionId, sourceEvent, e);
        } finally {
            updateNotificationLog(subscriptionId, sourceEvent, markdownText, response);
        }
    }

    @Override
    public void sendSubscriptionRenewalNotification(String subscriptionId, String saleId, String planId, BigDecimal amount, String currency, String userEmail, String sourceEvent, String paymentTime) {
        sendSubscriptionRenewalNotification(subscriptionId, saleId, planId, amount, currency, userEmail, sourceEvent, paymentTime, null);
    }

    @Override
    public void sendSubscriptionRenewalNotification(String subscriptionId, String saleId, String planId, BigDecimal amount, String currency, String userEmail, String sourceEvent, String paymentTime, NotificationExtInfo extInfo) {
        // 统一使用 SUBSCRIPTION_PAYMENT 作为业务类型，使用 saleId 作为唯一业务ID
        String businessType = "SUBSCRIPTION_PAYMENT";
        if (!tryLockNotification(businessType, saleId, sourceEvent)) {
            return;
        }

        JSONObject response = null;
        String markdownText = "";
        try {
            String webhookUrl = getPaymentWebhookUrl();
            if (StringUtils.isBlank(webhookUrl)) {
                log.warn("[钉钉通知] Webhook URL 未配置，跳过发送. 业务ID: {}, 事件: {}", saleId, sourceEvent);
                return;
            }

            log.info("[钉钉通知] 准备发送订阅续期通知. 订阅号: {}, 续订交易ID: {}", subscriptionId, saleId);
            StringBuilder markdown = new StringBuilder();
            markdown.append("#### PayPal 订阅续期\n\n")
                    .append("**订阅号:** ").append(subscriptionId).append("\n\n")
                    .append("**续订金额:** ").append((amount != null) ? amount.toPlainString() : "N/A").append(" ").append(StringUtils.defaultIfBlank(currency, "").toUpperCase()).append("\n\n")
                    .append("**用户:** ").append(StringUtils.defaultIfBlank(userEmail, "N/A")).append("\n\n");
            
            // 添加扩展信息
            if (extInfo != null) {
                if (StringUtils.isNotBlank(extInfo.getCountry())) {
                    markdown.append("**国家/地区:** ").append(extInfo.getCountry()).append("\n\n");
                }
                if (StringUtils.isNotBlank(extInfo.getClientIp())) {
                    markdown.append("**IP地址:** ").append(extInfo.getClientIp()).append("\n\n");
                }
                if (StringUtils.isNotBlank(extInfo.getLanguage())) {
                    markdown.append("**语言:** ").append(extInfo.getLanguage()).append("\n\n");
                }
                if (StringUtils.isNotBlank(extInfo.getClientUserAgent())) {
                    String simpleUA = simplifyUserAgent(extInfo.getClientUserAgent());
                    markdown.append("**设备:** ").append(simpleUA).append("\n\n");
                }
            }
            
            markdown.append("**支付时间:** ").append(StringUtils.defaultIfBlank(paymentTime, LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))).append("\n");
            markdownText = markdown.toString();
            response = dingTalkService.sendMarkdownMessage(webhookUrl, "订阅续订", markdownText);
        } catch (Exception e) {
             log.error("[钉钉通知] 发送钉钉消息时发生未知异常. 业务ID: {}, 事件: {}", saleId, sourceEvent, e);
        } finally {
            updateNotificationLog(saleId, sourceEvent, markdownText, response);
        }
    }

    @Override
    public void sendRefundNotification(String orderNo, String refundId, BigDecimal amount, String currency, String reason, String sourceEvent, String refundTime) {
        String businessType = "REFUND";
        if (!tryLockNotification(businessType, refundId, sourceEvent)) {
            return;
        }

        JSONObject response = null;
        String markdownText = "";
        try {
            String webhookUrl = getPaymentWebhookUrl();
            if (StringUtils.isBlank(webhookUrl)) {
                log.warn("[钉钉通知] Webhook URL 未配置，跳过发送. 业务ID: {}, 事件: {}", refundId, sourceEvent);
                return;
            }

            log.info("[钉钉通知] 准备发送退款通知. 退款ID: {}", refundId);
            StringBuilder markdown = new StringBuilder();
            markdown.append("#### PayPal 退款\n\n")
                    .append("**关联订单:** ").append(orderNo).append("\n\n")
                    .append("**退款ID:** ").append(refundId).append("\n\n")
                    .append("**金额:** ").append((amount != null) ? amount.toPlainString() : "N/A").append(" ").append(StringUtils.defaultIfBlank(currency, "").toUpperCase()).append("\n\n")
                    .append("**原因:** ").append(StringUtils.defaultIfBlank(reason, "无")).append("\n\n")
                    .append("**退款时间:** ").append(StringUtils.defaultIfBlank(refundTime, LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))).append("\n");
            markdownText = markdown.toString();
            response = dingTalkService.sendMarkdownMessage(webhookUrl, "退款通知", markdownText);
        } catch (Exception e) {
             log.error("[钉钉通知] 发送钉钉消息时发生未知异常. 业务ID: {}, 事件: {}", refundId, sourceEvent, e);
        } finally {
            updateNotificationLog(refundId, sourceEvent, markdownText, response);
        }
    }

    @Override
    public void sendDisputeNotification(String title, String disputeId, String reason, String status, BigDecimal amount, String currency, String sourceEvent) {
        String businessType = "DISPUTE";
        if (!tryLockNotification(businessType, disputeId, sourceEvent)) {
            return;
        }

        JSONObject response = null;
        String markdownText = "";
        try {
            String webhookUrl = getPaymentWebhookUrl();
            if (StringUtils.isBlank(webhookUrl)) {
                log.warn("[钉钉通知] Webhook URL 未配置，跳过发送. 业务ID: {}, 事件: {}", disputeId, sourceEvent);
                return;
            }

            log.info("[钉钉通知] 准备发送争议通知. 争议ID: {}", disputeId);
            StringBuilder markdown = new StringBuilder();
            markdown.append("#### ").append(title).append("\n\n")
                    .append("**争议ID:** ").append(disputeId).append("\n\n")
                    .append("**状态:** ").append(status).append("\n\n")
                    .append("**原因:** ").append(StringUtils.defaultIfBlank(reason, "无")).append("\n\n")
                    .append("**金额:** ").append((amount != null) ? amount.toPlainString() : "N/A").append(" ").append(StringUtils.defaultIfBlank(currency, "").toUpperCase()).append("\n\n")
                    .append("**通知时间:** ").append(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).append("\n");
            markdownText = markdown.toString();
            response = dingTalkService.sendMarkdownMessage(webhookUrl, title, markdownText);
        } catch (Exception e) {
             log.error("[钉钉通知] 发送钉钉消息时发生未知异常. 业务ID: {}, 事件: {}", disputeId, sourceEvent, e);
        } finally {
            updateNotificationLog(disputeId, sourceEvent, markdownText, response);
        }
    }

    @Override
    public void sendSubscriptionCancelledNotification(String subscriptionId, String planId, BigDecimal amount, String currency, String userEmail, String reason, String sourceEvent, String cancelTime) {
        sendSubscriptionCancelledNotification(subscriptionId, planId, amount, currency, userEmail, reason, sourceEvent, cancelTime, null);
    }

    @Override
    public void sendSubscriptionCancelledNotification(String subscriptionId, String planId, BigDecimal amount, String currency, String userEmail, String reason, String sourceEvent, String cancelTime, NotificationExtInfo extInfo) {
        String businessType = "SUBSCRIPTION_CANCELLED";
        if (!tryLockNotification(businessType, subscriptionId, sourceEvent)) {
            return;
        }

        JSONObject response = null;
        String markdownText = "";
        try {
            String webhookUrl = getPaymentWebhookUrl();
            if (StringUtils.isBlank(webhookUrl)) {
                log.warn("[钉钉通知] Webhook URL 未配置，跳过发送. 业务ID: {}, 事件: {}", subscriptionId, sourceEvent);
                return;
            }

            log.info("[钉钉通知] 准备发送订阅取消通知. 订阅号: {}", subscriptionId);
            StringBuilder markdown = new StringBuilder();
            markdown.append("#### PayPal 订阅取消\n\n")
                    .append("**订阅号:** ").append(subscriptionId).append("\n\n")
                    .append("**原金额:** ").append((amount != null) ? amount.toPlainString() : "N/A").append(" ").append(StringUtils.defaultIfBlank(currency, "").toUpperCase()).append("\n\n")
                    .append("**用户:** ").append(StringUtils.defaultIfBlank(userEmail, "N/A")).append("\n\n")
                    .append("**原因:** ").append(StringUtils.defaultIfBlank(reason, "用户主动取消或未知")).append("\n\n");
            
            // 添加扩展信息（如果有）
            if (extInfo != null) {
                if (StringUtils.isNotBlank(extInfo.getCountry())) {
                    markdown.append("**国家/地区:** ").append(extInfo.getCountry()).append("\n\n");
                }
                if (StringUtils.isNotBlank(extInfo.getClientIp())) {
                    markdown.append("**IP地址:** ").append(extInfo.getClientIp()).append("\n\n");
                }
                if (StringUtils.isNotBlank(extInfo.getLanguage())) {
                    markdown.append("**语言:** ").append(extInfo.getLanguage()).append("\n\n");
                }
                if (StringUtils.isNotBlank(extInfo.getClientUserAgent())) {
                    String simpleUA = simplifyUserAgent(extInfo.getClientUserAgent());
                    markdown.append("**设备:** ").append(simpleUA).append("\n\n");
                }
            }
            
            markdown.append("**取消时间:** ").append(StringUtils.defaultIfBlank(cancelTime, LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))).append("\n");
            markdownText = markdown.toString();
            response = dingTalkService.sendMarkdownMessage(webhookUrl, "订阅取消", markdownText);

        } catch (Exception e) {
             log.error("[钉钉通知] 发送钉钉消息时发生未知异常. 业务ID: {}, 事件: {}", subscriptionId, sourceEvent, e);
        } finally {
            updateNotificationLog(subscriptionId, sourceEvent, markdownText, response);
        }
    }

    /**
     * 尝试通过插入一条 PENDING 状态的记录来为通知加锁
     *
     * @return 如果加锁成功（插入成功），返回 true
     */
    private boolean tryLockNotification(String businessType, String businessId, String eventType) {
        if (StringUtils.isAnyBlank(businessId, eventType)) {
            log.warn("[钉钉通知] 加锁失败：业务ID或事件类型为空。businessId={}, eventType={}", businessId, eventType);
            return false;
        }
//        if (dingtalkLogMapper.selectOne(new SysDingtalkLog())
        SysDingtalkLog logEntry = new SysDingtalkLog();
        logEntry.setBusinessType(businessType);
        logEntry.setBusinessId(businessId);
        logEntry.setEventType(eventType);
        logEntry.setStatus("2"); // 2 = PENDING
        logEntry.setCreateTime(new Date());
        logEntry.setWebhookUrl(""); // 初始化为空
        logEntry.setMessageContent(""); // 初始化为空

        try {
            int affectedRows = dingtalkLogMapper.insert(logEntry);
            if (affectedRows > 0) {
                log.info("[钉钉通知] 事件加锁成功. 业务ID: {}, 事件类型: {}", businessId, eventType);
                return true; // 插入成功，获得锁
            } else {
                // 插入返回0，可能意味着数据库配置问题或插入条件不满足，但没有抛出异常。
                // 这种情况下，我们假定锁获取失败，以防止发送重复通知。
                log.warn("[钉钉通知] 事件加锁失败，数据库插入返回0行. 业务ID: {}, 事件类型: {}", businessId, eventType);
                return false;
            }
        } catch (Exception e) {
            // 大概率是 DuplicateKeyException，说明锁已被其他线程获取
            log.warn("[钉钉通知] 事件加锁失败，可能已被处理. 业务ID: {}, 事件类型: {}. Error: {}", businessId, eventType, e.getMessage());
            return false;
        }
    }

    /**
     * 更新通知日志的最终状态
     */
    private void updateNotificationLog(String businessId, String eventType, String content, JSONObject response) {

        QueryWrapper<SysDingtalkLog> updateWrapper = new QueryWrapper<>();
        updateWrapper.eq("business_id", businessId).eq("event_type", eventType);

        SysDingtalkLog logToUpdate = new SysDingtalkLog();
        logToUpdate.setMessageContent(content);

        // Flow Webhook 的成功响应是 {"success": true}
        if (response != null && response.getBooleanValue("success", false)) {
            logToUpdate.setStatus("0"); // 成功
            logToUpdate.setResponseCode("0");
            logToUpdate.setResponseMessage(response.toJSONString());
        } else {
            logToUpdate.setStatus("1"); // 失败
            logToUpdate.setResponseCode("-1");
            logToUpdate.setResponseMessage(response != null ? response.toJSONString() : "未能获取到有效的钉钉响应");
        }

        try {
            dingtalkLogMapper.update(logToUpdate, updateWrapper);
        } catch (Exception e) {
            log.error("[钉钉通知] 更新最终日志状态时发生异常. 业务ID: {}, 事件类型: {}", businessId, eventType, e);
        }
    }

    private String getPaymentWebhookUrl() {
        if (dingTalkProperties.getWebhooks() == null || StringUtils.isBlank(dingTalkProperties.getWebhooks().getPayment())) {
            log.error("[钉钉通知] 钉钉支付相关 webhook URL 未配置");
            return null;
        }
        return dingTalkProperties.getWebhooks().getPayment();
    }

    /**
     * 简化 User-Agent 字符串，提取关键信息
     */
    private String simplifyUserAgent(String userAgent) {
        if (StringUtils.isBlank(userAgent)) {
            return "未知设备";
        }
        
        // 提取关键信息
        if (userAgent.contains("iPhone")) {
            if (userAgent.contains("Instagram")) {
                return "iPhone (Instagram)";
            } else if (userAgent.contains("Mobile")) {
                return "iPhone (Safari)";
            }
            return "iPhone";
        } else if (userAgent.contains("Android")) {
            if (userAgent.contains("Instagram")) {
                return "Android (Instagram)";
            }
            return "Android";
        } else if (userAgent.contains("Windows")) {
            return "Windows PC";
        } else if (userAgent.contains("Mac")) {
            return "Mac";
        } else if (userAgent.contains("iPad")) {
            return "iPad";
        }
        
        // 如果无法识别，返回前50个字符
        return userAgent.length() > 50 ? userAgent.substring(0, 50) + "..." : userAgent;
    }
}
