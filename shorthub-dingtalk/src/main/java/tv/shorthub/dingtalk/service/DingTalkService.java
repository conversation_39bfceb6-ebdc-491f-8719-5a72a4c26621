package tv.shorthub.dingtalk.service;

import com.alibaba.fastjson2.JSONObject;
import org.springframework.stereotype.Service;

/**
 * 钉钉机器人基础服务接口
 */
@Service
public interface DingTalkService {

    /**
     * 发送 Markdown 格式消息
     *
     * @param webhookUrl Webhook 地址
     * @param title      标题
     * @param markdownText Markdown 格式的文本
     * @return 调用结果, 失败返回 null
     */
    JSONObject sendMarkdownMessage(String webhookUrl, String title, String markdownText);
}
