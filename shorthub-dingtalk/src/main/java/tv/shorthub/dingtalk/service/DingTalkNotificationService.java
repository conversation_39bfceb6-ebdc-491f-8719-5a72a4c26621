package tv.shorthub.dingtalk.service;

import java.math.BigDecimal;
import tv.shorthub.dingtalk.model.NotificationExtInfo;

/**
 * 钉钉业务通知服务
 */
public interface DingTalkNotificationService {

    /**
     * 发送支付成功通知
     * @param orderNo 系统订单号
     * @param amount 金额
     * @param currency 币种
     * @param userEmail 用户邮箱
     * @param paymentMethod 支付方式 (e.g., "一次性支付", "订阅")
     * @param sourceEvent 触发事件 (e.g., "PAYMENT.CAPTURE.COMPLETED")
     * @param paymentTime 支付时间 (格式: yyyy-MM-dd HH:mm:ss)
     */
    void sendPaymentSuccessNotification(String orderNo, BigDecimal amount, String currency, String userEmail, String paymentMethod, String sourceEvent, String paymentTime);

    /**
     * 发送支付成功通知（带扩展信息）
     * @param orderNo 系统订单号
     * @param amount 金额
     * @param currency 币种
     * @param userEmail 用户邮箱
     * @param paymentMethod 支付方式 (e.g., "一次性支付", "订阅")
     * @param sourceEvent 触发事件 (e.g., "PAYMENT.CAPTURE.COMPLETED")
     * @param paymentTime 支付时间 (格式: yyyy-MM-dd HH:mm:ss)
     * @param extInfo 扩展信息（国家、IP、设备等）
     */
    void sendPaymentSuccessNotification(String orderNo, BigDecimal amount, String currency, String userEmail, String paymentMethod, String sourceEvent, String paymentTime, NotificationExtInfo extInfo);

    /**
     * 发送订阅成功通知
     * @param subscriptionId 订阅ID
     * @param planId 计划ID
     * @param amount 金额
     * @param currency 币种
     * @param userEmail 用户邮箱
     * @param sourceEvent 触发事件
     * @param paymentTime 支付时间 (格式: yyyy-MM-dd HH:mm:ss)
     */
    void sendSubscriptionSuccessNotification(String subscriptionId, String planId, BigDecimal amount, String currency, String userEmail, String sourceEvent, String paymentTime);

    /**
     * 发送订阅成功通知（带扩展信息）
     * @param subscriptionId 订阅ID
     * @param planId 计划ID
     * @param amount 金额
     * @param currency 币种
     * @param userEmail 用户邮箱
     * @param sourceEvent 触发事件
     * @param paymentTime 支付时间 (格式: yyyy-MM-dd HH:mm:ss)
     * @param extInfo 扩展信息（国家、IP、设备等）
     */
    void sendSubscriptionSuccessNotification(String subscriptionId, String planId, BigDecimal amount, String currency, String userEmail, String sourceEvent, String paymentTime, NotificationExtInfo extInfo);

    /**
     * 发送订阅续期成功通知
     * @param subscriptionId 订阅ID
     * @param saleId 销售ID
     * @param planId 计划ID
     * @param amount 金额
     * @param currency 币种
     * @param userEmail 用户邮箱
     * @param sourceEvent 触发事件
     * @param paymentTime 支付时间 (格式: yyyy-MM-dd HH:mm:ss)
     */
    void sendSubscriptionRenewalNotification(String subscriptionId, String saleId, String planId, BigDecimal amount, String currency, String userEmail, String sourceEvent, String paymentTime);

    /**
     * 发送订阅续期成功通知（带扩展信息）
     * @param subscriptionId 订阅ID
     * @param saleId 销售ID
     * @param planId 计划ID
     * @param amount 金额
     * @param currency 币种
     * @param userEmail 用户邮箱
     * @param sourceEvent 触发事件
     * @param paymentTime 支付时间 (格式: yyyy-MM-dd HH:mm:ss)
     * @param extInfo 扩展信息（国家、IP、设备等）
     */
    void sendSubscriptionRenewalNotification(String subscriptionId, String saleId, String planId, BigDecimal amount, String currency, String userEmail, String sourceEvent, String paymentTime, NotificationExtInfo extInfo);

    /**
     * 发送退款成功通知
     * @param orderNo 系统订单号
     * @param refundId PayPal退款ID
     * @param amount 退款金额
     * @param currency 币种
     * @param reason 退款原因 (可选)
     * @param sourceEvent 触发事件
     * @param refundTime 退款时间 (格式: yyyy-MM-dd HH:mm:ss)
     */
    void sendRefundNotification(String orderNo, String refundId, BigDecimal amount, String currency, String reason, String sourceEvent, String refundTime);

    /**
     * 发送争议通知
     * @param title 标题 (例如 "【新争议】", "【争议更新】")
     * @param disputeId 争议ID
     * @param reason 争议原因
     * @param status 争议状态
     * @param amount 金额
     * @param currency 币种
     * @param sourceEvent 触发事件
     */
    void sendDisputeNotification(String title, String disputeId, String reason, String status, BigDecimal amount, String currency, String sourceEvent);

    /**
     * 发送订阅取消通知
     * @param subscriptionId 订阅ID
     * @param planId 计划ID
     * @param amount 金额
     * @param currency 币种
     * @param userEmail 用户邮箱
     * @param reason 取消原因 (可选)
     * @param sourceEvent 触发事件
     * @param cancelTime 取消时间 (格式: yyyy-MM-dd HH:mm:ss)
     */
    void sendSubscriptionCancelledNotification(String subscriptionId, String planId, BigDecimal amount, String currency, String userEmail, String reason, String sourceEvent, String cancelTime);

    /**
     * 发送订阅取消通知（带扩展信息）
     * @param subscriptionId 订阅ID
     * @param planId 计划ID
     * @param amount 金额
     * @param currency 币种
     * @param userEmail 用户邮箱
     * @param reason 取消原因 (可选)
     * @param sourceEvent 触发事件
     * @param cancelTime 取消时间 (格式: yyyy-MM-dd HH:mm:ss)
     * @param extInfo 扩展信息（国家、IP、设备等）
     */
    void sendSubscriptionCancelledNotification(String subscriptionId, String planId, BigDecimal amount, String currency, String userEmail, String reason, String sourceEvent, String cancelTime, NotificationExtInfo extInfo);
}