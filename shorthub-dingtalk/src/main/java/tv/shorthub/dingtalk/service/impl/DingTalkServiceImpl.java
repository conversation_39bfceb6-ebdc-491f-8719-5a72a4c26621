package tv.shorthub.dingtalk.service.impl;

import com.alibaba.fastjson2.JSONObject;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import tv.shorthub.dingtalk.service.DingTalkService;

@Slf4j
@Service
@RequiredArgsConstructor
public class DingTalkServiceImpl implements DingTalkService {

    private final RestTemplate restTemplate;

    @Override
    public JSONObject sendMarkdownMessage(String webhookUrl, String title, String markdownText) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.valueOf("text/plain;charset=UTF-8"));

        HttpEntity<String> request = new HttpEntity<>(markdownText, headers);

        try {
            String response = restTemplate.postForObject(webhookUrl, request, String.class);
            log.info("[钉钉通知] Flow Webhook 消息发送成功。响应: {}", response);
            return JSONObject.parseObject(response);
        } catch (Exception e) {
            log.error("[钉钉通知] Flow Webhook 消息发送失败。Webhook: {}", webhookUrl, e);
            return null;
        }
    }
} 