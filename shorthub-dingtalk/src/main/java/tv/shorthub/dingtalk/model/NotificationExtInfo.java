package tv.shorthub.dingtalk.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 钉钉通知扩展信息
 * 用于传递订单的地理位置、设备等信息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NotificationExtInfo {
    /**
     * 国家/地区
     */
    private String country;
    
    /**
     * 客户端IP
     */
    private String clientIp;
    
    /**
     * 客户端User-Agent
     */
    private String clientUserAgent;
    
    /**
     * 语言
     */
    private String language;
} 