package tv.shorthub.dingtalk.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

@Data
@Component
@ConfigurationProperties(prefix = "dingtalk")
@Validated
public class DingTalkProperties {

    /**
     * Webhook 设置
     */
    private Webhook webhooks;

    @Data
    public static class Webhook {
        /**
         * 支付与订阅通知的 Webhook 地址 (Flow a.k.a. 工作流机器人)
         */
        private String payment;
    }
} 