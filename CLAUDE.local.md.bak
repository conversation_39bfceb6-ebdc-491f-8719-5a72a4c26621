### 📊 统计核心基础表 (3张必需)

#### 1. **app_user_watch_records** (观看记录表) ⭐ 数据源
- **实体类**: AppUserWatchRecords
- **用途**: 观看数据分析核心表
- **关键字段**:
  - user_id: 用户ID (去重统计)
  - content_id: 剧目内容ID
  - serial_number: 观看集数
  - tfid: 流量标识符 (关联推广表)
  - serial_second: 观看到第几秒 ⭐ 主要分析数据
  - appid: 应用标识 (权限控制)

#### 2. **app_promotion** (推广表) ⭐ 流量来源
- **实体类**: AppPromotion
- **用途**: 区分付费流量 vs 自然流量，权限控制
- **关键字段**:
  - tfid: 流量标识符 ⭐ **空值为" "=自然流量，有值=付费流量**
  - ad_channel: 广告渠道 (facebook, google)
  - create_by: 投放人员 (子账号权限控制)
  - video_fee_begin: 推广专属收费起始集数
  - video_every_money: 推广专属每集价格

#### 3. **app_drama_contents_serial** (剧集详情表) ⭐ 基础信息
- **实体类**: AppDramaContentsSerial
- **用途**: 提供剧集基础信息和视频时长
- **关键字段**:
  - content_id: 剧目内容ID
  - serial_number: 集数序号
  - video_seconds: 视频总时长 (计算完成率必需)
  - title: 单集标题

### 📋 辅助表 (可选)

#### 4. **app_drama_contents** (剧目内容表)
- **实体类**: AppDramaContents
- **用途**: 剧目标题和默认收费策略
- **关键字段**: content_id, title, fee_begin, every_money

## 📊 投放平台分析指标
## 🎯 投放平台剧集分析 - 核心表组合


### 基础观看分析
  1. 每集平均观看时长: AVG(serial_second) 按剧集分组
  2. 每集观看完成率: (serial_second / video_seconds) * 100%
  3. 用户观看深度: 观看进度分布统计
  4. 剧集热度排行: 各集观看人数和观看时长

### 流量来源分析
  1. **自然流量 vs 付费流量**: 基于 tfid 是否为空区分
  2. **广告渠道效果**: Facebook vs Google 渠道观看效果对比
  3. **收费策略分析**: 默认收费 vs 推广个性化收费效果
  4. **推广转化分析**: 不同推广链接的用户观看转化率
