import java.security.MessageDigest;
import java.util.Arrays;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import org.bitcoinj.crypto.HDKeyDerivation;
import org.bitcoinj.crypto.DeterministicKey;
import org.bitcoinj.crypto.MnemonicCode;
import org.web3j.crypto.MnemonicUtils;
import net.i2p.crypto.eddsa.spec.EdDSANamedCurveTable;
import net.i2p.crypto.eddsa.spec.EdDSAPrivateKeySpec;
import net.i2p.crypto.eddsa.spec.EdDSAParameterSpec;
import net.i2p.crypto.eddsa.EdDSAPrivateKey;
import net.i2p.crypto.eddsa.EdDSAPublicKey;

public class SolanaAdvancedTest {
    
    public static void main(String[] args) throws Exception {
        String testMnemonic = "autumn wink trash organ catalog enrich thing student hundred mobile face flame";
        String expectedAddress = "482BcArEm6vAFXHwUv6MGEyudhZsiEFakH4Hsfd9hJu1";
        
        System.out.println("=== Advanced Solana Address Generation Tests ===");
        System.out.println("Mnemonic: " + testMnemonic);
        System.out.println("Expected: " + expectedAddress);
        System.out.println();
        
        // Test 1: Different derivation paths (non-hardened)
        testNonHardenedPaths(testMnemonic, expectedAddress);
        
        // Test 2: Legacy Solana derivation (pre BIP44)
        testLegacyDerivation(testMnemonic, expectedAddress);
        
        // Test 3: Direct BIP39 word processing
        testDirectWordProcessing(testMnemonic, expectedAddress);
        
        // Test 4: Alternative Ed25519 implementations
        testAlternativeEd25519(testMnemonic, expectedAddress);
        
        // Test 5: SHA-512 based derivation
        testSHA512Derivation(testMnemonic, expectedAddress);
        
        // Test 6: Try different account indices
        testDifferentAccountIndices(testMnemonic, expectedAddress);
    }
    
    private static void testNonHardenedPaths(String mnemonic, String expectedAddress) throws Exception {
        System.out.println("--- Test 1: Non-hardened derivation paths ---");
        
        byte[] seed = MnemonicUtils.generateSeed(mnemonic, "");
        DeterministicKey masterKey = HDKeyDerivation.createMasterPrivateKey(seed);
        
        // Try m/44/501/0/0 (non-hardened)
        try {
            DeterministicKey purposeKey = HDKeyDerivation.deriveChildKey(masterKey, 44);
            DeterministicKey coinKey = HDKeyDerivation.deriveChildKey(purposeKey, 501);
            DeterministicKey accountKey = HDKeyDerivation.deriveChildKey(coinKey, 0);
            DeterministicKey addressKey = HDKeyDerivation.deriveChildKey(accountKey, 0);
            
            String address = generateSolanaAddress(addressKey.getPrivKeyBytes());
            System.out.println("m/44/501/0/0: " + address);
            System.out.println("Match: " + address.equals(expectedAddress));
        } catch (Exception e) {
            System.out.println("m/44/501/0/0 failed: " + e.getMessage());
        }
        
        // Try m/501/0/0 (Solana-specific)
        try {
            DeterministicKey coinKey = HDKeyDerivation.deriveChildKey(masterKey, 501 | 0x80000000);
            DeterministicKey accountKey = HDKeyDerivation.deriveChildKey(coinKey, 0 | 0x80000000);
            DeterministicKey addressKey = HDKeyDerivation.deriveChildKey(accountKey, 0 | 0x80000000);
            
            String address = generateSolanaAddress(addressKey.getPrivKeyBytes());
            System.out.println("m/501'/0'/0': " + address);
            System.out.println("Match: " + address.equals(expectedAddress));
        } catch (Exception e) {
            System.out.println("m/501'/0'/0' failed: " + e.getMessage());
        }
        
        System.out.println();
    }
    
    private static void testLegacyDerivation(String mnemonic, String expectedAddress) throws Exception {
        System.out.println("--- Test 2: Legacy Solana derivation ---");
        
        try {
            // Use entropy directly from mnemonic
            byte[] entropy = mnemonicToEntropy(mnemonic);
            String address1 = generateSolanaAddress(entropy);
            System.out.println("Direct entropy: " + address1);
            System.out.println("Match: " + address1.equals(expectedAddress));
            
            // Hash entropy with SHA-256
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hashedEntropy = digest.digest(entropy);
            String address2 = generateSolanaAddress(hashedEntropy);
            System.out.println("SHA-256(entropy): " + address2);
            System.out.println("Match: " + address2.equals(expectedAddress));
            
        } catch (Exception e) {
            System.out.println("Legacy derivation failed: " + e.getMessage());
        }
        
        System.out.println();
    }
    
    private static void testDirectWordProcessing(String mnemonic, String expectedAddress) throws Exception {
        System.out.println("--- Test 3: Direct word processing ---");
        
        try {
            String[] words = mnemonic.split(" ");
            
            // Method 1: Concatenate words and hash
            String concat = String.join("", words);
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] wordHash = digest.digest(concat.getBytes("UTF-8"));
            String address1 = generateSolanaAddress(wordHash);
            System.out.println("Word concat hash: " + address1);
            System.out.println("Match: " + address1.equals(expectedAddress));
            
            // Method 2: Use mnemonic as UTF-8 bytes directly
            byte[] mnemonicBytes = mnemonic.getBytes("UTF-8");
            byte[] mnemonicHash = digest.digest(mnemonicBytes);
            String address2 = generateSolanaAddress(Arrays.copyOf(mnemonicHash, 32));
            System.out.println("Mnemonic UTF-8 hash: " + address2);
            System.out.println("Match: " + address2.equals(expectedAddress));
            
        } catch (Exception e) {
            System.out.println("Direct word processing failed: " + e.getMessage());
        }
        
        System.out.println();
    }
    
    private static void testAlternativeEd25519(String mnemonic, String expectedAddress) throws Exception {
        System.out.println("--- Test 4: Alternative Ed25519 implementations ---");
        
        try {
            byte[] seed = MnemonicUtils.generateSeed(mnemonic, "");
            
            // Standard BIP44 private key
            DeterministicKey masterKey = HDKeyDerivation.createMasterPrivateKey(seed);
            DeterministicKey purposeKey = HDKeyDerivation.deriveChildKey(masterKey, 44 | 0x80000000);
            DeterministicKey coinKey = HDKeyDerivation.deriveChildKey(purposeKey, 501 | 0x80000000);
            DeterministicKey accountKey = HDKeyDerivation.deriveChildKey(coinKey, 0 | 0x80000000);
            DeterministicKey addressKey = HDKeyDerivation.deriveChildKey(accountKey, 0 | 0x80000000);
            byte[] privateKeyBytes = addressKey.getPrivKeyBytes();
            
            // Method 1: Use SHA-512 of private key as Ed25519 seed
            MessageDigest sha512 = MessageDigest.getInstance("SHA-512");
            byte[] sha512Hash = sha512.digest(privateKeyBytes);
            String address1 = generateSolanaAddress(Arrays.copyOf(sha512Hash, 32));
            System.out.println("SHA-512 of private key: " + address1);
            System.out.println("Match: " + address1.equals(expectedAddress));
            
            // Method 2: Use HMAC-SHA512 with specific salt
            Mac hmac = Mac.getInstance("HmacSHA512");
            SecretKeySpec keySpec = new SecretKeySpec("ed25519 seed".getBytes(), "HmacSHA512");
            hmac.init(keySpec);
            byte[] hmacResult = hmac.doFinal(privateKeyBytes);
            String address2 = generateSolanaAddress(Arrays.copyOf(hmacResult, 32));
            System.out.println("HMAC-SHA512 with 'ed25519 seed': " + address2);
            System.out.println("Match: " + address2.equals(expectedAddress));
            
        } catch (Exception e) {
            System.out.println("Alternative Ed25519 failed: " + e.getMessage());
        }
        
        System.out.println();
    }
    
    private static void testSHA512Derivation(String mnemonic, String expectedAddress) throws Exception {
        System.out.println("--- Test 5: SHA-512 based derivation ---");
        
        try {
            // Direct SHA-512 of mnemonic
            MessageDigest sha512 = MessageDigest.getInstance("SHA-512");
            byte[] mnemonicHash = sha512.digest(mnemonic.getBytes("UTF-8"));
            String address1 = generateSolanaAddress(Arrays.copyOf(mnemonicHash, 32));
            System.out.println("SHA-512 of mnemonic: " + address1);
            System.out.println("Match: " + address1.equals(expectedAddress));
            
            // SHA-512 of seed (PBKDF2 result)
            byte[] seed = MnemonicUtils.generateSeed(mnemonic, "");
            byte[] seedHash = sha512.digest(seed);
            String address2 = generateSolanaAddress(Arrays.copyOf(seedHash, 32));
            System.out.println("SHA-512 of seed: " + address2);
            System.out.println("Match: " + address2.equals(expectedAddress));
            
            // Double SHA-512
            byte[] doubleHash = sha512.digest(seedHash);
            String address3 = generateSolanaAddress(Arrays.copyOf(doubleHash, 32));
            System.out.println("Double SHA-512: " + address3);
            System.out.println("Match: " + address3.equals(expectedAddress));
            
        } catch (Exception e) {
            System.out.println("SHA-512 derivation failed: " + e.getMessage());
        }
        
        System.out.println();
    }
    
    private static void testDifferentAccountIndices(String mnemonic, String expectedAddress) throws Exception {
        System.out.println("--- Test 6: Different account indices ---");
        
        byte[] seed = MnemonicUtils.generateSeed(mnemonic, "");
        DeterministicKey masterKey = HDKeyDerivation.createMasterPrivateKey(seed);
        DeterministicKey purposeKey = HDKeyDerivation.deriveChildKey(masterKey, 44 | 0x80000000);
        DeterministicKey coinKey = HDKeyDerivation.deriveChildKey(purposeKey, 501 | 0x80000000);
        
        // Try different account indices
        for (int i = 0; i < 5; i++) {
            try {
                DeterministicKey accountKey = HDKeyDerivation.deriveChildKey(coinKey, i | 0x80000000);
                DeterministicKey addressKey = HDKeyDerivation.deriveChildKey(accountKey, 0 | 0x80000000);
                
                String address = generateSolanaAddress(addressKey.getPrivKeyBytes());
                System.out.println("Account " + i + ": " + address);
                System.out.println("Match: " + address.equals(expectedAddress));
                
                if (address.equals(expectedAddress)) {
                    System.out.println("*** FOUND MATCH AT ACCOUNT INDEX " + i + " ***");
                    break;
                }
            } catch (Exception e) {
                System.out.println("Account " + i + " failed: " + e.getMessage());
            }
        }
        
        System.out.println();
    }
    
    private static String generateSolanaAddress(byte[] privateKeyBytes) throws Exception {
        // Ensure we have 32 bytes
        if (privateKeyBytes.length > 32) {
            privateKeyBytes = Arrays.copyOf(privateKeyBytes, 32);
        } else if (privateKeyBytes.length < 32) {
            byte[] padded = new byte[32];
            System.arraycopy(privateKeyBytes, 0, padded, 0, privateKeyBytes.length);
            privateKeyBytes = padded;
        }
        
        EdDSAParameterSpec spec = EdDSANamedCurveTable.getByName("Ed25519");
        EdDSAPrivateKeySpec privKeySpec = new EdDSAPrivateKeySpec(privateKeyBytes, spec);
        EdDSAPrivateKey privKey = new EdDSAPrivateKey(privKeySpec);
        EdDSAPublicKey pubKey = new EdDSAPublicKey(
            new net.i2p.crypto.eddsa.spec.EdDSAPublicKeySpec(privKey.getA(), spec));
        
        return encodeBase58(pubKey.getAbyte());
    }
    
    private static byte[] mnemonicToEntropy(String mnemonic) throws Exception {
        // Simple entropy calculation from mnemonic words
        String[] words = mnemonic.split(" ");
        byte[] entropy = new byte[16]; // 128-bit entropy for 12 words
        
        // This is a simplified entropy extraction - real implementation would use BIP39 wordlist
        for (int i = 0; i < Math.min(words.length, entropy.length); i++) {
            entropy[i] = (byte) (words[i].hashCode() & 0xFF);
        }
        
        return entropy;
    }
    
    private static final String ALPHABET = "**********************************************************";
    
    private static String encodeBase58(byte[] input) {
        if (input.length == 0) return "";
        
        int[] digits = new int[input.length * 2];
        int digitslen = 1;
        
        for (int i = 0; i < input.length; i++) {
            int carry = input[i] & 0xFF;
            for (int j = 0; j < digitslen; j++) {
                carry += digits[j] << 8;
                digits[j] = carry % 58;
                carry /= 58;
            }
            while (carry > 0) {
                digits[digitslen++] = carry % 58;
                carry /= 58;
            }
        }
        
        // Handle leading zeros
        int zeroslen = 0;
        while (zeroslen < input.length && input[zeroslen] == 0) {
            zeroslen++;
        }
        
        StringBuilder result = new StringBuilder();
        for (int i = 0; i < zeroslen; i++) {
            result.append(ALPHABET.charAt(0));
        }
        for (int i = digitslen - 1; i >= 0; i--) {
            result.append(ALPHABET.charAt(digits[i]));
        }
        
        return result.toString();
    }
}