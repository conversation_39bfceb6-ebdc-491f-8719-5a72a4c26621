//package tv.shorthub.paypal.test;
//
//import com.alibaba.fastjson2.JSONObject;
//import lombok.extern.slf4j.Slf4j;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.junit4.SpringRunner;
//import tv.shorthub.paypal.service.PayPalOrderService;
//import tv.shorthub.paypal.service.PayPalDisputeService;
//
//@Slf4j
//@RunWith(SpringRunner.class)
//@SpringBootTest
//public class CreateTestDispute {
//
//    @Autowired
//    private PayPalOrderService orderService;
//
//    @Autowired
//    private PayPalDisputeService disputeService;
//
//    @Test
//    public void createTestDispute() throws Exception {
//        // 1. 创建测试订单
//        JSONObject order = createTestOrder();
//        String orderId = order.getString("id");
//        log.info("创建测试订单成功: orderId={}", orderId);
//
//        // 2. 使用买家账户支付订单
//        JSONObject payment = captureOrder(orderId);
//        String paymentId = payment.getString("id");
//        log.info("订单支付成功: paymentId={}", paymentId);
//
//        // 3. 等待一段时间，确保订单状态已更新
//        Thread.sleep(5000);
//
//        // 4. 使用买家账户发起争议
//        createDispute(paymentId);
//        log.info("创建测试争议成功: paymentId={}", paymentId);
//    }
//
//    private JSONObject createTestOrder() throws Exception {
//        JSONObject order = new JSONObject();
//        order.put("intent", "CAPTURE");
//
//        JSONObject purchaseUnit = new JSONObject();
//        purchaseUnit.put("amount", new JSONObject()
//            .fluentPut("currency_code", "USD")
//            .fluentPut("value", "100.00"));
//
//        order.put("purchase_units", new JSONObject[]{purchaseUnit});
//
//        return orderService.createOrder(order);
//    }
//
//    private JSONObject captureOrder(String orderId) throws Exception {
//        return orderService.captureOrder(orderId);
//    }
//
//    private void createDispute(String paymentId) throws Exception {
//        // 注意：在 Sandbox 环境中，我们需要使用买家账户登录 PayPal 网站
//        // 然后手动发起争议，因为 API 不直接支持创建争议
//        log.info("请在 PayPal Sandbox 网站使用买家账户登录，然后手动发起争议");
//        log.info("争议步骤：");
//        log.info("1. 登录 https://www.sandbox.paypal.com");
//        log.info("2. 使用买家账户登录");
//        log.info("3. 进入 Resolution Center");
//        log.info("4. 找到订单 ID: {}", paymentId);
//        log.info("5. 点击 'Report a Problem'");
//        log.info("6. 选择争议原因（例如：Item Not Received）");
//        log.info("7. 提交争议");
//    }
//}
