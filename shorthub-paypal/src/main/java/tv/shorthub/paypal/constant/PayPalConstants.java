package tv.shorthub.paypal.constant;

public class PayPalConstants {
    public static final String TOKEN_CACHE_KEY = "paypal:token";
    public static final String API_VERSION = "v1";

    public static class Endpoints {
        public static final String TOKEN = "/v1/oauth2/token";
        public static final String DISPUTES = "/v1/customer/disputes";
        public static final String PAYMENTS = "/v2/payments";
        public static final String ORDERS = "/v2/checkout/orders";
        public static final String ORDERS_CAPTURE = "/v2/checkout/orders/%s/capture";
        public static final String REFUNDS = "/v2/payments/refunds";
        public static final String SUBSCRIPTIONS = "/v1/billing/subscriptions";
        public static final String PLANS = "/v1/billing/plans";
        public static final String PRODUCTS = "/v1/catalogs/products";
        public static final String CAPTURES = "/v2/payments/captures";
        public static final String BILLING_AGREEMENTS = "/v1/payments/billing-agreements";
    }

    /**
     * 争议状态定义
     * OPEN: 争议已打开
     * WAITING_FOR_BUYER_RESPONSE: 等待买家响应
     * WAITING_FOR_SELLER_RESPONSE: 等待卖家响应
     * UNDER_REVIEW: 争议正在审核中
     * RESOLVED: 争议已解决
     * CLOSED: 争议已关闭
     */
    public static class DisputeStatus {
        public static final String OPEN = "OPEN";
        public static final String WAITING_FOR_BUYER_RESPONSE = "WAITING_FOR_BUYER_RESPONSE";
        public static final String WAITING_FOR_SELLER_RESPONSE = "WAITING_FOR_SELLER_RESPONSE";
        public static final String UNDER_REVIEW = "UNDER_REVIEW";
        public static final String RESOLVED = "RESOLVED";
        public static final String CLOSED = "CLOSED";
    }

    public static class PaymentStatus {
        /**
         * 支付状态定义
         * CREATED: 已创建
         * SAVED: 已保存
         * APPROVED: 已批准
         * VOIDED: 已作废
         * COMPLETED: 已完成
         * PAYER_ACTION_REQUIRED: 需要付款方操作
         * REFUNDED: 已退款
         *
         *
         */
        public static final String CREATED = "CREATED";
        public static final String SAVED = "SAVED";
        public static final String APPROVED = "APPROVED";
        public static final String VOIDED = "VOIDED";
        public static final String COMPLETED = "COMPLETED";
        public static final String PAYER_ACTION_REQUIRED = "PAYER_ACTION_REQUIRED";
        public static final String REFUNDED = "REFUNDED";
    }

    /**
     * 退款状态定义
     * PENDING: 待处理
     * COMPLETED: 已完成
     * CANCELLED: 已取消
     * FAILED: 已失败
     */
    public static class RefundStatus {
        public static final String PENDING = "PENDING";
        public static final String COMPLETED = "COMPLETED";
        public static final String CANCELLED = "CANCELLED";
        public static final String FAILED = "FAILED";
    }

    public static class SubscriptionStatus {
        /**
         * 订阅状态定义
         * APPROVAL_PENDING: 等待批准
         * APPROVED: 已批准
         * ACTIVE: 活跃中
         * SUSPENDED: 已暂停
         * CANCELLED: 已取消
         * EXPIRED: 已过期
         */
        public static final String APPROVAL_PENDING = "APPROVAL_PENDING";
        public static final String APPROVED = "APPROVED";
        public static final String ACTIVE = "ACTIVE";
        public static final String SUSPENDED = "SUSPENDED";
        public static final String CANCELLED = "CANCELLED";
        public static final String EXPIRED = "EXPIRED";
    }

    public static class BillingCycle {
        /**
         * 按天周期
         */
        public static final String DAY = "DAY";

        /**
         * 按周周期
         */
        public static final String WEEK = "WEEK";

        /**
         * 按月周期
         */
        public static final String MONTH = "MONTH";

        /**
         * 按季度周期
         */
        public static final String QUARTER = "QUARTER";

        /**
         * 按年周期
         */
        public static final String YEAR = "YEAR";
    }

    /**
     * 争议类型定义
     * UNAUTHORIZED_TRANSACTION: 未授权交易
     * ITEM_NOT_RECEIVED: 未收到物品
     * ITEM_NOT_AS_DESCRIBED: 未收到物品
     * DUPLICATE_TRANSACTION: 重复交易
     * OTHER: 其他
     */
    public static class DisputeType {
        public static final String UNAUTHORIZED_TRANSACTION = "UNAUTHORIZED_TRANSACTION";
        public static final String ITEM_NOT_RECEIVED = "ITEM_NOT_RECEIVED";
        public static final String ITEM_NOT_AS_DESCRIBED = "ITEM_NOT_AS_DESCRIBED";
        public static final String DUPLICATE_TRANSACTION = "DUPLICATE_TRANSACTION";
        public static final String OTHER = "OTHER";
    }

    public static class PaymentMethod {
        // 充值
        public static final String RECHARGE = "RECHARGE";
        // 订阅
        public static final String SUBSCRIPTION = "SUBSCRIPTION";
    }

    /**
     * 争议解决结果
     * RESOLVED_BUYER_FAVOUR: 解决，买家胜诉
     * RESOLVED_SELLER_FAVOUR: 解决，卖家胜诉
     * RESOLVED_WITH_PAYOUT: 通过赔付解决
     * CANCELED_BY_BUYER: 买家取消
     * ACCEPTED: 卖家接受
     */
    public static class DisputeOutcome {
        public static final String RESOLVED_BUYER_FAVOUR = "RESOLVED_BUYER_FAVOUR";
        public static final String RESOLVED_SELLER_FAVOUR = "RESOLVED_SELLER_FAVOUR";
        public static final String RESOLVED_WITH_PAYOUT = "RESOLVED_WITH_PAYOUT";
        public static final String CANCELED_BY_BUYER = "CANCELED_BY_BUYER";
        public static final String ACCEPTED = "ACCEPTED";
    }
}
