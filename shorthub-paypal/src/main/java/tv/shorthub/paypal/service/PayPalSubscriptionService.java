package tv.shorthub.paypal.service;

import com.alibaba.fastjson2.JSONObject;
import tv.shorthub.paypal.model.PaypalSubscriptionRequest;

public interface PayPalSubscriptionService {
    /**
     * 创建订阅计划
     *
     * @param request 订阅请求参数
     * @return 订阅计划信息
     */
    JSONObject createPlan(PaypalSubscriptionRequest request) throws Exception;

    /**
     * 创建订阅
     *
     * @param planId 订阅计划ID
     * @param request 订阅请求参数
     * @return 订阅信息
     */
    JSONObject createSubscription(String planId, PaypalSubscriptionRequest request) throws Exception;

    /**
     * 查询订阅状态
     *
     * @param subscriptionId 订阅ID
     * @return 订阅详情
     */
    JSONObject querySubscription(String subscriptionId) throws Exception;

    /**
     * 取消订阅
     *
     * @param subscriptionId 订阅ID
     * @return 取消结果
     */
    JSONObject cancelSubscription(String subscriptionId) throws Exception;

    /**
     * 暂停订阅
     *
     * @param subscriptionId 订阅ID
     * @return 暂停结果
     */
    JSONObject suspendSubscription(String subscriptionId) throws Exception;

    /**
     * 恢复订阅
     *
     * @param subscriptionId 订阅ID
     * @return 恢复结果
     */
    JSONObject reactivateSubscription(String subscriptionId) throws Exception;
} 