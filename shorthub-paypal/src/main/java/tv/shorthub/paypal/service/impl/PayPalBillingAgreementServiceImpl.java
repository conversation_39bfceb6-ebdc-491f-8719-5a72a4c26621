package tv.shorthub.paypal.service.impl;

import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tv.shorthub.paypal.constant.PayPalConstants;
import tv.shorthub.paypal.service.PayPalBillingAgreementService;
import tv.shorthub.paypal.service.PayPalBaseService;
import tv.shorthub.paypal.service.PayPalAccountService;
import tv.shorthub.paypal.model.Account;

@Slf4j
@Service
public class PayPalBillingAgreementServiceImpl extends PayPalBaseService implements PayPalBillingAgreementService {

    @Autowired
    private PayPalAccountService accountService;

    @Override
    public JSONObject getBillingAgreement(String agreementId) throws Exception {
        // 获取默认账户
        String accountId = accountService.getDefaultAccount().getId();

        log.info("开始查询账单协议: agreementId={}, 账户: {}", agreementId, accountId);

        // 构建请求路径
        String endpoint = PayPalConstants.Endpoints.BILLING_AGREEMENTS + "/" + agreementId;

        // 发送GET请求
        JSONObject agreement = doGet(endpoint);
        log.info("查询账单协议结果: agreementId={}, status={}, 账户: {}", agreementId, 
                agreement.getString("state"), accountId);

        return agreement;
    }
} 