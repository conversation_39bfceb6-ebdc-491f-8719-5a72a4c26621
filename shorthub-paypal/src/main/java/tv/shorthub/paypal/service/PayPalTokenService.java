package tv.shorthub.paypal.service;

import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import tv.shorthub.paypal.utils.ProxyTemplateFactory;
import tv.shorthub.common.core.redis.RedisCache;
import tv.shorthub.common.exception.ApiException;
import tv.shorthub.common.utils.MessageUtils;
import tv.shorthub.paypal.model.Account;
import tv.shorthub.paypal.constant.PayPalConstants;
import org.springframework.web.client.RestTemplate;

import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class PayPalTokenService {

    @Autowired
    private RedisCache redisCache;

    private Integer tokenExpireSeconds = 3500;

    /**
     * 获取PayPal访问令牌
     */
    public String getAccessToken(Account account) {
        if (account == null || account.getClientId() == null || account.getClientSecret() == null) {
            log.error("获取PayPal访问令牌失败: 账户配置无效");
            throw new ApiException(MessageUtils.message("paypal.account.invalid"));
        }

        // 使用账户ID作为缓存key的一部分
        String cacheKey = String.format("paypal:token:%s", account.getId());
        
        // 从缓存获取token
        String cachedToken = redisCache.getCacheObject(cacheKey);
        if (cachedToken != null) {
            log.debug("从缓存获取PayPal访问令牌: accountId={}", account.getId());
            return cachedToken;
        }

        // 如果缓存中没有，则重新获取
        HttpHeaders headers = new HttpHeaders();
        headers.setBasicAuth(account.getClientId(), account.getClientSecret());
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

        HttpEntity<String> requestEntity = new HttpEntity<>("grant_type=client_credentials", headers);

        try {
            log.debug("请求PayPal访问令牌: accountId={}, baseUrl={}", account.getId(), account.getBaseUrl());
            RestTemplate restTemplate = ProxyTemplateFactory.getRestTemplate(account);
            ResponseEntity<JSONObject> response = restTemplate.postForEntity(
                account.getBaseUrl() + PayPalConstants.Endpoints.TOKEN,
                requestEntity,
                JSONObject.class
            );

            if (response.getStatusCode() != HttpStatus.OK) {
                log.error("获取PayPal访问令牌失败: accountId={}, status={}", account.getId(), response.getStatusCode());
                throw new ApiException(MessageUtils.message("paypal.auth.failed"));
            }

            JSONObject responseBody = response.getBody();
            if (responseBody == null || !responseBody.containsKey("access_token")) {
                log.error("获取PayPal访问令牌失败: accountId={}, response={}", account.getId(), responseBody);
                throw new ApiException(MessageUtils.message("paypal.auth.failed"));
            }

            String accessToken = responseBody.getString("access_token");
            // 缓存token
            redisCache.setCacheObject(
                cacheKey,
                accessToken,
                tokenExpireSeconds,
                TimeUnit.SECONDS
            );
            log.debug("缓存PayPal访问令牌: accountId={}", account.getId());
            return accessToken;
        } catch (Exception e) {
            log.error("获取PayPal访问令牌失败: accountId={}, error={}", account.getId(), e.getMessage(), e);
            throw new ApiException(MessageUtils.message("paypal.auth.failed"));
        }
    }

    /**
     * 清除PayPal访问令牌
     */
    public void clearAccessToken(Account account) {
        if (account != null) {
            String cacheKey = String.format("paypal:token:%s", account.getId());
            redisCache.deleteObject(cacheKey);
            log.debug("清除PayPal访问令牌: accountId={}", account.getId());
        }
    }
}
