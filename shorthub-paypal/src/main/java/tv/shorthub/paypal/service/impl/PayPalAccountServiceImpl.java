package tv.shorthub.paypal.service.impl;

import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import tv.shorthub.paypal.utils.ProxyTemplateFactory;
import tv.shorthub.common.core.redis.RedisCache;
import tv.shorthub.paypal.config.PaypalConfigStorageHolder;
import tv.shorthub.paypal.service.PayPalAccountService;
import tv.shorthub.paypal.model.Account;
import tv.shorthub.common.exception.ApiException;
import tv.shorthub.common.utils.MessageUtils;
import tv.shorthub.common.core.cache.CacheKeyUtils;
import org.springframework.web.client.RestTemplate;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class PayPalAccountServiceImpl implements PayPalAccountService {

    @Autowired
    private RedisCache redisCache;

    private static final Long TOKEN_EXPIRE_TIME = 3600L; // 1小时过期
    private final Map<String, Account> accountMap = new ConcurrentHashMap<>();
    private String defaultAccountId;

    @Override
    public boolean registerAccount(Account account) {
        if (account == null || account.getId() == null || account.getId().isEmpty()) {
            throw new ApiException(MessageUtils.message("paypal.account.invalid"));
        }

        // 验证账户配置
        validateAccount(account);

        // 注册账户
        accountMap.put(account.getId(), account);
        log.info("注册PayPal账户成功: {}", account.getId());

        // 如果是第一个账户，设置为默认账户
        if (accountMap.size() == 1) {
            setDefaultAccount(account.getId());
        }

        return true;
    }

    @Override
    public boolean updateAccount(Account account) {
        if (account == null || account.getId() == null || account.getId().isEmpty()) {
            throw new ApiException(MessageUtils.message("paypal.account.invalid"));
        }

        // 验证账户是否存在
        if (!accountMap.containsKey(account.getId())) {
            throw new ApiException(MessageUtils.message("paypal.account.not.found"));
        }

        // 验证账户配置
        validateAccount(account);

        // 更新账户
        accountMap.put(account.getId(), account);
        log.info("更新PayPal账户成功: {}", account.getId());

        return true;
    }

    @Override
    public boolean unregisterAccount(String accountId) {
        if (accountId == null || accountId.isEmpty()) {
            throw new ApiException(MessageUtils.message("paypal.account.invalid"));
        }

        // 验证账户是否存在
        if (!accountMap.containsKey(accountId)) {
            throw new ApiException(MessageUtils.message("paypal.account.not.found"));
        }

        // 如果是默认账户，不允许注销
        if (accountId.equals(defaultAccountId)) {
            throw new ApiException(MessageUtils.message("paypal.account.default.cannot.unregister"));
        }

        // 注销账户
        accountMap.remove(accountId);
        log.info("注销PayPal账户成功: {}", accountId);

        return true;
    }

    @Override
    public List<Account> getAllAccounts() {
        return List.copyOf(accountMap.values());
    }

    @Override
    public boolean setDefaultAccount(String accountId) {
        if (accountId == null || accountId.isEmpty()) {
            throw new ApiException(MessageUtils.message("paypal.account.invalid"));
        }

        // 验证账户是否存在
        if (!accountMap.containsKey(accountId)) {
            throw new ApiException(MessageUtils.message("paypal.account.not.found"));
        }

        // 设置默认账户
        defaultAccountId = accountId;
        log.info("设置默认PayPal账户成功: {}", accountId);

        return true;
    }

    @Override
    public Account getDefaultAccount() {
        if (defaultAccountId == null || defaultAccountId.isEmpty()) {
            throw new ApiException(MessageUtils.message("paypal.account.default.not.set"));
        }

        Account account = accountMap.get(defaultAccountId);
        if (account == null) {
            throw new ApiException(MessageUtils.message("paypal.account.default.not.found"));
        }

        return account;
    }

    @Override
    public Account getCurrentAccount() {
        return getAccount(PaypalConfigStorageHolder.get());
    }

    @Override
    public Account getAccount(String accountId) {
        if (accountId == null || accountId.isEmpty()) {
            throw new ApiException(MessageUtils.message("paypal.account.invalid"));
        }

        Account account = accountMap.get(accountId);
        if (account == null) {
            throw new ApiException(MessageUtils.message("paypal.account.not.found"));
        }

        return account;
    }

    @Override
    public String getAccessToken(String accountId) {
        // 从缓存中获取token
        String cacheKey = CacheKeyUtils.getPayPalTokenCacheKey(accountId);
        String accessToken = redisCache.getCacheObject(cacheKey);

        if (accessToken != null) {
            return accessToken;
        }

        // 缓存未命中，刷新token
        return refreshAccessToken(accountId);
    }

    @Override
    public String refreshAccessToken(String accountId) {
        Account account = getAccount(accountId);

        try {
            log.info("开始刷新PayPal访问令牌: accountId={}", accountId);

            HttpHeaders headers = new HttpHeaders();
            headers.setBasicAuth(account.getClientId(), account.getClientSecret());
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

            HttpEntity<String> requestEntity = new HttpEntity<>("grant_type=client_credentials", headers);
            RestTemplate restTemplate = ProxyTemplateFactory.getRestTemplate(account);
            ResponseEntity<JSONObject> response = restTemplate.postForEntity(
                account.getBaseUrl() + "/v1/oauth2/token",
                requestEntity,
                JSONObject.class
            );

            JSONObject tokenResponse = response.getBody();
            if (tokenResponse == null || !tokenResponse.containsKey("access_token")) {
                throw new ApiException(MessageUtils.message("paypal.token.refresh.failed"));
            }

            String accessToken = tokenResponse.getString("access_token");

            // 缓存token
            String cacheKey = CacheKeyUtils.getPayPalTokenCacheKey(accountId);
            redisCache.setCacheObject(cacheKey, accessToken, TOKEN_EXPIRE_TIME.intValue(), TimeUnit.SECONDS);

            log.info("刷新PayPal访问令牌成功: accountId={}", accountId);

            return accessToken;
        } catch (Exception e) {
            log.error("刷新PayPal访问令牌失败: accountId={}", accountId, e);
            throw new ApiException(MessageUtils.message("paypal.token.refresh.failed"));
        }
    }

    @Override
    public boolean validateAccount(String accountId) {
        try {
            getAccessToken(accountId);
            return true;
        } catch (Exception e) {
            log.error("验证PayPal账户失败: accountId={}", accountId, e);
            return false;
        }
    }

    /**
     * 验证账户配置
     */
    private void validateAccount(Account account) {
        if (account.getClientId() == null || account.getClientId().isEmpty()) {
            throw new ApiException(MessageUtils.message("paypal.account.client.id.required"));
        }
        if (account.getClientSecret() == null || account.getClientSecret().isEmpty()) {
            throw new ApiException(MessageUtils.message("paypal.account.client.secret.required"));
        }
        if (account.getBaseUrl() == null || account.getBaseUrl().isEmpty()) {
            throw new ApiException(MessageUtils.message("paypal.account.base.url.required"));
        }
        if (account.getBrandName() == null || account.getBrandName().isEmpty()) {
            throw new ApiException(MessageUtils.message("paypal.account.brand.name.required"));
        }
//        if (account.getMerchantEmail() == null || account.getMerchantEmail().isEmpty()) {
//            throw new ApiException(MessageUtils.message("paypal.account.merchant.email.required"));
//        }
    }
}
