package tv.shorthub.paypal.service.impl;

import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSONArray;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import tv.shorthub.common.enums.OrderChannelEnums;
import tv.shorthub.common.utils.StringUtils;
import tv.shorthub.paypal.config.PaypalConfigStorageHolder;
import tv.shorthub.paypal.model.PaypalPaymentRequest;
import tv.shorthub.common.exception.ApiException;
import tv.shorthub.paypal.service.PayPalPaymentService;
import tv.shorthub.paypal.service.PayPalCallback;
import tv.shorthub.paypal.service.PayPalAccountService;
import tv.shorthub.paypal.service.PayPalBaseService;
import tv.shorthub.paypal.model.Account;
import tv.shorthub.paypal.constant.PayPalConstants;

@Slf4j
@Service
public class PayPalPaymentServiceImpl extends PayPalBaseService implements PayPalPaymentService {

    @Autowired
    private PayPalAccountService accountService;

    @Override
    public JSONObject createPayment(PaypalPaymentRequest request) throws Exception {
        // 获取账户ID，如果未指定则使用默认账户
        String accountId = request.getAccountId();
        if (accountId == null || accountId.isEmpty()) {
            accountId = accountService.getDefaultAccount().getId();
        }

        Account account = accountService.getAccount(accountId);

        // 构建请求体
        JSONObject orderRequest = new JSONObject();
        orderRequest.put("intent", "CAPTURE");

        JSONArray purchaseUnits = new JSONArray();
        JSONObject purchaseUnit = new JSONObject();

        JSONObject amount = new JSONObject();
        amount.put("currency_code", "USD");
        amount.put("value", request.getAmount().toString());
        JSONObject breakdown = new JSONObject();
        JSONObject item_total = new JSONObject();
        item_total.put("currency_code", "USD");
        item_total.put("value", request.getAmount().toString());
        breakdown.put("item_total", item_total);
        amount.put("breakdown", breakdown);


        JSONArray items = new JSONArray();
        JSONObject item = new JSONObject();
        item.put("name", request.getItemName());
        item.put("quantity", "1");
        JSONObject unit_amount = new JSONObject();
        unit_amount.put("currency_code", "USD");
        unit_amount.put("value", request.getAmount().toString());
        item.put("unit_amount", unit_amount);
        item.put("category", "DIGITAL_GOODS");
        items.add(item);
        purchaseUnit.put("items", items);
        purchaseUnit.put("amount", amount);
        purchaseUnit.put("custom_id", request.getOrderNo());

        // 在沙盒环境下使用测试商家账号
        if (account.isSandbox()) {
            JSONObject payee = new JSONObject();
            payee.put("email_address", account.getMerchantEmail());
            purchaseUnit.put("payee", payee);
        }

        purchaseUnits.add(purchaseUnit);

        orderRequest.put("purchase_units", purchaseUnits);

        JSONObject paymentSource = new JSONObject();
        JSONObject paypal = new JSONObject();
        
        JSONObject experience_context = new JSONObject();
        experience_context.put("payment_method_preference", "IMMEDIATE_PAYMENT_REQUIRED");
        experience_context.put("shipping_preference", "NO_SHIPPING");
        experience_context.put("return_url", request.getReturnUrl());
        experience_context.put("cancel_url", request.getCancelUrl());
        paypal.put("experience_context", experience_context);

        JSONObject application_context = new JSONObject();
        application_context.put("brand_name", "Shorthub.TV");
        application_context.put("locale", request.getLocale());
        orderRequest.put("application_context", application_context);

        if (StringUtils.isNotEmpty(request.getVaultId())) {
            // 使用保存的支付方式时，直接设置vault_id
            paypal.put("vault_id", request.getVaultId());
        } else {
            // 没有vault_id时，才设置attributes.vault配置
            JSONObject attributes = new JSONObject();
            JSONObject vault = new JSONObject();
            vault.put("store_in_vault", "ON_SUCCESS");
            vault.put("usage_type", "MERCHANT");
            attributes.put("vault", vault);
            paypal.put("attributes", attributes);
        }

        paymentSource.put(OrderChannelEnums.PAYPAL.getValue(), paypal);
        orderRequest.put("payment_source", paymentSource);

        // 发送请求 - 使用带PayPal-Request-Id的方法，因为包含payment_source时需要此头部
        JSONObject order = doPostWithRequestId(PayPalConstants.Endpoints.ORDERS, orderRequest);
        log.info("订单请求参数: {}, 返回报文: {}",orderRequest, order);
        log.info("创建订单: {}, 环境: {}, 账户: {}", order.getString("id"), account.isSandbox() ? "沙盒" : "生产", accountId);

        return order;
    }

    @Override
    public JSONObject queryOrder(String orderId) throws Exception {
        // 从请求中获取账户ID，如果未指定则使用默认账户
        String accountId = accountService.getDefaultAccount().getId();
        Account account = accountService.getAccount(accountId);

        log.info("开始查询订单: orderId={}, 账户: {}", orderId, accountId);

        // 发送请求
        JSONObject order = doGet(PayPalConstants.Endpoints.ORDERS + "/" + orderId);
        log.info("查询订单结果: orderId={}, status={}, 账户: {}", orderId, order.getString("status"), accountId);

        return order;
    }

    @Override
    public JSONObject executePayment(String orderId, String payerId, PayPalCallback paymentCallback) throws Exception {
        // 从请求中获取账户ID，如果未指定则使用默认账户
        log.info("开始捕获订单: orderId={}, payerId={}, 账户: {}", orderId, payerId, PaypalConfigStorageHolder.get());

        JSONObject order;
        // 发送请求
        try {
            order = doPost(PayPalConstants.Endpoints.ORDERS + "/" + orderId + "/capture", null);
            log.info("捕获订单结果: orderId={}, status={}, 账户: {}", orderId, order, PaypalConfigStorageHolder.get());
        } catch (Exception exception) {
            order = new JSONObject();
            order.put("exception", exception.getMessage());
            log.error("捕获订单失败: {}, {}", orderId, exception.getMessage(), exception);
            if (exception.getMessage().contains("ORDER_ALREADY_CAPTURED")) {
                order = queryOrder(orderId);
            }
        }

        // 检查订单状态
        if ("COMPLETED".equals(order.getString("status"))) {
            JSONObject data = new JSONObject();
            data.put("orderId", orderId);
            data.put("payerId", payerId);
            data.put("paymentData", order);
            // 调用成功回调
            paymentCallback.onSuccess(data);
        } else {
            // 调用失败回调
            JSONObject data = new JSONObject();
            data.put("orderId", orderId);
            data.put("payerId", payerId);
            data.put("error", "Order status is not COMPLETED");
            paymentCallback.onFailed(data);
        }

        return order;
    }

    @Override
    public JSONObject refundPayment(String paymentId, String amount, String currency) throws Exception {

        // 先查询支付详情获取captureId
        JSONObject payment = queryOrder(paymentId);
        log.info("查询支付详情: paymentId={}, response={}", paymentId, payment);

        // 从支付详情中获取captureId
        String captureId = null;
        if (payment != null && payment.containsKey("purchase_units")) {
            JSONArray purchaseUnits = payment.getJSONArray("purchase_units");
            if (purchaseUnits != null && !purchaseUnits.isEmpty()) {
                JSONObject purchaseUnit = purchaseUnits.getJSONObject(0);
                if (purchaseUnit.containsKey("payments")) {
                    JSONObject payments = purchaseUnit.getJSONObject("payments");
                    if (payments.containsKey("captures")) {
                        JSONArray captures = payments.getJSONArray("captures");
                        if (captures != null && !captures.isEmpty()) {
                            captureId = captures.getJSONObject(0).getString("id");
                        }
                    }
                }
            }
        }

        if (captureId == null) {
            throw new ApiException("无法获取captureId");
        }

        // 构建退款请求
        JSONObject refundRequest = new JSONObject();
        JSONObject amountObj = new JSONObject();
        amountObj.put("value", amount);
        amountObj.put("currency_code", currency);
        refundRequest.put("amount", amountObj);
        refundRequest.put("note_to_payer", "Refund for payment " + paymentId);

        // 使用captureId进行退款
        String endpoint = "/v2/payments/captures/" + captureId + "/refund";
        return doPost(endpoint, refundRequest);
    }

    @Override
    public JSONObject queryRefund(String refundId) throws Exception {
        // 使用正确的退款查询端点
        String endpoint = PayPalConstants.Endpoints.REFUNDS + "/" + refundId;
        return doGet(endpoint);
    }

    @Override
    public JSONObject getSubscriptionDetails(String subscriptionId) throws Exception {
        String endpoint = PayPalConstants.Endpoints.SUBSCRIPTIONS + "/" + subscriptionId;
        log.info("开始查询v1订阅详情: subscriptionId={}, 账户: {}", subscriptionId, PaypalConfigStorageHolder.get());
        JSONObject subscriptionDetails = doGet(endpoint);
        log.info("查询v1订阅详情成功: subscriptionId={}, status={}", subscriptionId, subscriptionDetails.getString("status"));
        return subscriptionDetails;
    }
}
