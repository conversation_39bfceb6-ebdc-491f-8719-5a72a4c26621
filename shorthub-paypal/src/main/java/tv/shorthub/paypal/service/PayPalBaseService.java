package tv.shorthub.paypal.service;

import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.web.client.RestTemplate;
import tv.shorthub.common.exception.ApiException;
import tv.shorthub.common.utils.MessageUtils;
import tv.shorthub.paypal.config.PaypalConfigStorageHolder;
import tv.shorthub.paypal.model.Account;
import tv.shorthub.paypal.utils.ProxyTemplateFactory;

import java.util.UUID;

@Slf4j
public abstract class PayPalBaseService {


    @Autowired
    protected PayPalTokenService payPalTokenService;

    @Autowired
    private PayPalAccountService payPalAccountService;
    /**
     * 创建带有认证头的HTTP请求头
     */
    protected HttpHeaders createAuthHeaders(Account account) {
        if (account == null) {
            log.error("创建认证头失败: account为空");
            throw new ApiException(MessageUtils.message("paypal.account.invalid"));
        }

        try {
            String accessToken = payPalTokenService.getAccessToken(account);
            HttpHeaders headers = new HttpHeaders();
            headers.setBearerAuth(accessToken);
            headers.setContentType(MediaType.APPLICATION_JSON);
            return headers;
        } catch (Exception e) {
            log.error("创建认证头失败: sandbox={} accountId={}, error={}", account.isSandbox(), account.getId(), e.getMessage(), e);
            throw new ApiException(MessageUtils.message("paypal.auth.failed"));
        }
    }

    /**
     * 创建带有认证头和PayPal-Request-Id的HTTP请求头
     */
    protected HttpHeaders createAuthHeadersWithRequestId(Account account) {
        HttpHeaders headers = createAuthHeaders(account);
        // 添加PayPal-Request-Id头部，用于防止重复请求
        headers.set("PayPal-Request-Id", UUID.randomUUID().toString());
        return headers;
    }

    /**
     * 发送GET请求
     */
    protected JSONObject doGet(String endpoint) {
        Account account = payPalAccountService.getCurrentAccount();
        try {
            log.debug("发送PayPal GET请求: sandbox={}, endpoint={}, accountId={}", account.isSandbox(), endpoint, account.getId());
            HttpEntity<String> requestEntity = new HttpEntity<>(createAuthHeaders(account));
            RestTemplate restTemplate = ProxyTemplateFactory.getRestTemplate(account);
            ResponseEntity<JSONObject> response = restTemplate.exchange(
                account.getBaseUrl() + endpoint,
                HttpMethod.GET,
                requestEntity,
                JSONObject.class
            );
            return response.getBody();
        } catch (Exception e) {
            log.error("PayPal API请求失败: sandbox={}, endpoint={}, accountId={}, error={}", account.isSandbox(), endpoint, account.getId(), e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 发送POST请求
     */
    protected JSONObject doPost(String endpoint, Object body) {
        Account account = payPalAccountService.getCurrentAccount();
        try {
            log.debug("发送PayPal POST请求: sandbox={}, endpoint={}, accountId={}, body={}", account.isSandbox(), endpoint, account.getId(), body);
            HttpEntity<Object> requestEntity = new HttpEntity<>(body, createAuthHeaders(account));
            RestTemplate restTemplate = ProxyTemplateFactory.getRestTemplate(account);
            ResponseEntity<JSONObject> response = restTemplate.postForEntity(
                account.getBaseUrl() + endpoint,
                requestEntity,
                JSONObject.class
            );
            return response.getBody();
        } catch (Exception e) {
            log.error("PayPal API请求失败: sandbox={}, endpoint={}, accountId={}, body={}, error={}", account.isSandbox(), endpoint, account.getId(), body, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 发送PUT请求
     */
    protected JSONObject doPut(String endpoint, Object body) {
        Account account = payPalAccountService.getCurrentAccount();
        try {
            log.debug("发送PayPal PUT请求: sandbox={}, endpoint={}, accountId={}, body={}", account.isSandbox(), endpoint, account.getId(), body);
            HttpEntity<Object> requestEntity = new HttpEntity<>(body, createAuthHeaders(account));
            RestTemplate restTemplate = ProxyTemplateFactory.getRestTemplate(account);
            ResponseEntity<JSONObject> response = restTemplate.exchange(
                account.getBaseUrl() + endpoint,
                HttpMethod.PUT,
                requestEntity,
                JSONObject.class
            );
            return response.getBody();
        } catch (Exception e) {
            log.error("PayPal API请求失败: sandbox={}, endpoint={}, accountId={}, body={}, error={}", account.isSandbox(), endpoint, account.getId(), body, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 发送POST请求（带PayPal-Request-Id头部）
     */
    protected JSONObject doPostWithRequestId(String endpoint, Object body) {
        Account account = payPalAccountService.getCurrentAccount();
        try {
            log.debug("发送PayPal POST请求(带Request-Id): sandbox={}, endpoint={}, accountId={}, body={}", account.isSandbox(), endpoint, account.getId(), body);
            HttpEntity<Object> requestEntity = new HttpEntity<>(body, createAuthHeadersWithRequestId(account));
            RestTemplate restTemplate = ProxyTemplateFactory.getRestTemplate(account);
            ResponseEntity<JSONObject> response = restTemplate.postForEntity(
                account.getBaseUrl() + endpoint,
                requestEntity,
                JSONObject.class
            );
            return response.getBody();
        } catch (Exception e) {
            log.error("PayPal API请求失败: sandbox={}, endpoint={}, accountId={}, body={}, error={}", account.isSandbox(), endpoint, account.getId(), body, e.getMessage(), e);
            throw e;
        }
    }
}
