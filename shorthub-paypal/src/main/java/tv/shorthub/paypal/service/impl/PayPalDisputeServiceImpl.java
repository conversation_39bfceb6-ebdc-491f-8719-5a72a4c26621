package tv.shorthub.paypal.service.impl;

import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSONArray;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tv.shorthub.paypal.service.PayPalDisputeService;
import tv.shorthub.paypal.service.PayPalAccountService;
import tv.shorthub.paypal.service.PayPalBaseService;
import tv.shorthub.paypal.model.Account;
import tv.shorthub.paypal.constant.PayPalConstants;
import tv.shorthub.common.exception.ApiException;
import tv.shorthub.common.utils.MessageUtils;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class PayPalDisputeServiceImpl extends PayPalBaseService implements PayPalDisputeService {

    @Autowired
    private PayPalAccountService accountService;

    @Override
    public List<JSONObject> getDisputes(String startTime, String endTime) throws Exception {
        try {
            log.info("开始获取PayPal争议列表: startTime={}, endTime={}", startTime, endTime);

            // 构建查询参数
            StringBuilder endpointBuilder = new StringBuilder(PayPalConstants.Endpoints.DISPUTES);
            List<String> params = new ArrayList<>();

            if (startTime != null) {
                // 将时间转换为 Internet date and time format (yyyy-MM-ddTHH:mm:ss.SSSZ)
                DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
                
                LocalDateTime startDateTime = LocalDateTime.parse(startTime, inputFormatter);
                
                // 检查时间是否在180天内
                LocalDateTime now = LocalDateTime.now();
                LocalDateTime minDate = now.minusDays(180);
                
                if (startDateTime.isBefore(minDate)) {
                    log.warn("开始时间超过180天，将使用180天前的时间: original={}, adjusted={}", 
                            startDateTime, minDate);
                    startDateTime = minDate;
                }
                
                String formattedStartTime = startDateTime.atOffset(ZoneOffset.UTC).format(outputFormatter);
                params.add("start_time=" + formattedStartTime);
            }

            // 添加分页参数
            params.add("page_size=50");  // 使用最大页面大小以减少请求次数

            // 组合所有参数
            if (!params.isEmpty()) {
                endpointBuilder.append("?").append(String.join("&", params));
            }

            String endpoint = endpointBuilder.toString();
            log.debug("PayPal API请求URL: {}", endpoint);

            // 发送请求
            JSONObject response = doGet(endpoint);
            
            // 从响应中获取 disputes 数组
            List<JSONObject> result = new ArrayList<>();
            JSONArray disputes = response.getJSONArray("items");
            
            if (disputes != null) {
                for (int i = 0; i < disputes.size(); i++) {
                    result.add(disputes.getJSONObject(i));
                }
            }

            log.info("获取PayPal争议列表成功: count={}", result.size());
            return result;
        } catch (Exception e) {
            log.error("获取PayPal争议列表失败: ", e);
            throw new ApiException(MessageUtils.message("paypal.dispute.list.failed"));
        }
    }

    @Override
    public JSONObject getDisputeDetail(String disputeId) throws Exception {
        Account account = accountService.getCurrentAccount();
        try {
            log.info("开始获取PayPal争议详情: disputeId={}, accountId={}", disputeId, account.getId());

            // 发送请求
            String endpoint = PayPalConstants.Endpoints.DISPUTES + "/" + disputeId;
            JSONObject dispute = doGet(endpoint);

            log.info("获取PayPal争议详情成功: disputeId={}, accountId={}", disputeId, account.getId());
            return dispute;
        } catch (Exception e) {
            log.error("获取PayPal争议详情失败: disputeId={}, accountId={}", disputeId, account.getId(), e);
            throw new ApiException(MessageUtils.message("paypal.dispute.detail.failed"));
        }
    }

    @Override
    public JSONObject acceptDispute(String disputeId, String note) throws Exception {
        Account account = accountService.getCurrentAccount();
        try {
            log.info("开始接受PayPal争议: disputeId={}, accountId={}", disputeId, account.getId());

            // 构建请求体
            JSONObject requestBody = new JSONObject();
            requestBody.put("note", note);

            // 发送请求
            String endpoint = PayPalConstants.Endpoints.DISPUTES + "/" + disputeId + "/accept";
            JSONObject result = doPost(endpoint, requestBody);

            log.info("接受PayPal争议成功: disputeId={}, accountId={}", disputeId, account.getId());
            return result;
        } catch (Exception e) {
            log.error("接受PayPal争议失败: disputeId={}, accountId={}", disputeId, account.getId(), e);
            throw new ApiException(MessageUtils.message("paypal.dispute.accept.failed"));
        }
    }

    @Override
    public JSONObject rejectDispute(String disputeId, String note) throws Exception {
        Account account = accountService.getCurrentAccount();
        try {
            log.info("开始拒绝PayPal争议: disputeId={}, accountId={}", disputeId, account.getId());

            // 构建请求体
            JSONObject requestBody = new JSONObject();
            requestBody.put("note", note);

            // 发送请求
            String endpoint = PayPalConstants.Endpoints.DISPUTES + "/" + disputeId + "/reject";
            JSONObject result = doPost(endpoint, requestBody);

            log.info("拒绝PayPal争议成功: disputeId={}, accountId={}", disputeId, account.getId());
            return result;
        } catch (Exception e) {
            log.error("拒绝PayPal争议失败: disputeId={}, accountId={}", disputeId, account.getId(), e);
            throw new ApiException(MessageUtils.message("paypal.dispute.reject.failed"));
        }
    }

    @Override
    public JSONObject provideEvidence(String disputeId, JSONObject evidence) throws Exception {
        Account account = accountService.getCurrentAccount();
        try {
            log.info("开始提供PayPal争议证据: disputeId={}, accountId={}", disputeId, account.getId());

            // 发送请求
            String endpoint = PayPalConstants.Endpoints.DISPUTES + "/" + disputeId + "/provide-evidence";
            JSONObject result = doPost(endpoint, evidence);

            log.info("提供PayPal争议证据成功: disputeId={}, accountId={}", disputeId, account.getId());
            return result;
        } catch (Exception e) {
            log.error("提供PayPal争议证据失败: disputeId={}, accountId={}", disputeId, account.getId(), e);
            throw new ApiException(MessageUtils.message("paypal.dispute.evidence.failed"));
        }
    }
}
