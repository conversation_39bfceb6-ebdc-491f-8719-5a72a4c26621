package tv.shorthub.paypal.service;

import com.alibaba.fastjson2.JSONObject;
import tv.shorthub.paypal.model.PaypalPaymentRequest;
import tv.shorthub.paypal.service.PayPalCallback;

public interface PayPalPaymentService {

    /**
     * 创建PayPal支付订单
     *
     * @param request 支付请求参数
     * @return 支付订单信息
     */
    JSONObject createPayment(PaypalPaymentRequest request) throws Exception;

    /**
     * 查询PayPal订单状态
     *
     * @param orderId 订单ID
     * @return 订单详情
     */
    JSONObject queryOrder(String orderId) throws Exception;

    /**
     * 执行PayPal支付
     *
     * @param orderId 订单ID
     * @param payerId 支付者ID
     * @return 支付结果
     */
    JSONObject executePayment(String orderId, String payerId, PayPalCallback paymentCallback) throws Exception;

    JSONObject refundPayment(String paymentId, String amount, String currency) throws Exception;

    JSONObject queryRefund(String refundId) throws Exception;

    /**
     * 查询v1订阅的详细信息
     *
     * @param subscriptionId 订阅ID (I-xxxx)
     * @return 订阅详情
     */
    JSONObject getSubscriptionDetails(String subscriptionId) throws Exception;
}
