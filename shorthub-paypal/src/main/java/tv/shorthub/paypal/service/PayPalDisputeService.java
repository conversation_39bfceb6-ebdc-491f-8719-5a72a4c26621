package tv.shorthub.paypal.service;

import com.alibaba.fastjson2.JSONObject;
import java.util.List;

/**
 * PayPal争议服务接口
 */
public interface PayPalDisputeService {
    
    /**
     * 获取争议列表
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 争议列表
     */
    List<JSONObject> getDisputes(String startTime, String endTime) throws Exception;

    /**
     * 获取争议详情
     *
     * @param disputeId 争议ID
     * @return 争议详情
     */
    JSONObject getDisputeDetail(String disputeId) throws Exception;

    /**
     * 接受争议
     *
     * @param disputeId 争议ID
     * @param note 备注
     * @return 处理结果
     */
    JSONObject acceptDispute(String disputeId, String note) throws Exception;

    /**
     * 拒绝争议
     *
     * @param disputeId 争议ID
     * @param note 备注
     * @return 处理结果
     */
    JSONObject rejectDispute(String disputeId, String note) throws Exception;

    /**
     * 提供证据
     *
     * @param disputeId 争议ID
     * @param evidence 证据信息
     * @return 处理结果
     */
    JSONObject provideEvidence(String disputeId, JSONObject evidence) throws Exception;
} 