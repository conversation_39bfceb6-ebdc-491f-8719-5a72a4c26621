package tv.shorthub.paypal.service;

import tv.shorthub.paypal.model.Account;
import java.util.List;

/**
 * PayPal账户服务
 * 负责管理PayPal账户的注册、更新、注销以及访问令牌管理
 */
public interface PayPalAccountService {

    /**
     * 注册PayPal账户
     *
     * @param account 账户配置
     * @return 是否注册成功
     */
    boolean registerAccount(Account account);

    /**
     * 更新PayPal账户
     *
     * @param account 账户配置
     * @return 是否更新成功
     */
    boolean updateAccount(Account account);

    /**
     * 注销PayPal账户
     *
     * @param accountId 账户ID
     * @return 是否注销成功
     */
    boolean unregisterAccount(String accountId);

    /**
     * 获取所有已注册的账户
     *
     * @return 账户列表
     */
    List<Account> getAllAccounts();

    /**
     * 设置默认账户
     *
     * @param accountId 账户ID
     * @return 是否设置成功
     */
    boolean setDefaultAccount(String accountId);

    /**
     * 获取默认账户
     *
     * @return 默认账户
     */
    Account getDefaultAccount();

    Account getCurrentAccount();

    /**
     * 获取账户配置
     *
     * @param accountId 账户ID
     * @return 账户配置
     */
    Account getAccount(String accountId);

    /**
     * 获取访问令牌
     * 如果缓存中有有效的令牌则返回缓存的令牌，否则刷新令牌
     *
     * @param accountId 账户ID
     * @return 访问令牌
     */
    String getAccessToken(String accountId);

    /**
     * 刷新访问令牌
     * 强制从PayPal获取新的访问令牌
     *
     * @param accountId 账户ID
     * @return 新的访问令牌
     */
    String refreshAccessToken(String accountId);

    /**
     * 验证账户配置
     * 通过尝试获取访问令牌来验证账户配置是否正确
     *
     * @param accountId 账户ID
     * @return 是否验证成功
     */
    boolean validateAccount(String accountId);
}
