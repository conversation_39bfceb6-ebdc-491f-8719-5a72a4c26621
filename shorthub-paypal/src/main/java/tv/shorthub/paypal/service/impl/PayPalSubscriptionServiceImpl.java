package tv.shorthub.paypal.service.impl;

import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSONArray;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tv.shorthub.paypal.model.PaypalSubscriptionRequest;
import tv.shorthub.paypal.service.PayPalSubscriptionService;
import tv.shorthub.paypal.service.PayPalAccountService;
import tv.shorthub.paypal.service.PayPalBaseService;
import tv.shorthub.paypal.model.Account;
import tv.shorthub.paypal.constant.PayPalConstants;
import tv.shorthub.common.core.redis.RedisCache;

import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class PayPalSubscriptionServiceImpl extends PayPalBaseService implements PayPalSubscriptionService {

    @Autowired
    private PayPalAccountService accountService;

    @Autowired
    private RedisCache redisCache;

    private static final String PRODUCT_CACHE_KEY_PREFIX = "paypal:product:";
    private static final int PRODUCT_CACHE_EXPIRE_DAYS = 30;

    /**
     * 获取或创建产品
     */
    private String getOrCreateProduct(PaypalSubscriptionRequest request) throws Exception {
        // 使用产品名称和描述作为缓存key
        String cacheKey = PRODUCT_CACHE_KEY_PREFIX + request.getPlanName() + ":" + request.getDescription();

        // 尝试从缓存获取产品ID
        String productId = redisCache.getCacheObject(cacheKey);
        if (productId != null) {
            log.debug("从缓存获取产品ID: {}", productId);
            return productId;
        }

        // 创建新产品
        JSONObject productRequest = new JSONObject();
        productRequest.put("name", request.getPlanName());
        productRequest.put("description", request.getDescription());
        productRequest.put("type", "SERVICE");
        productRequest.put("category", "SOFTWARE");

        JSONObject product = doPost(PayPalConstants.Endpoints.PRODUCTS, productRequest);
        productId = product.getString("id");

        // 缓存产品ID
        redisCache.setCacheObject(cacheKey, productId, PRODUCT_CACHE_EXPIRE_DAYS, TimeUnit.MINUTES);
        log.debug("创建并缓存新产品ID: {}", productId);

        return productId;
    }

    /**
     * 处理计费周期，支持季度扣费
     * @param billingCycle 原始计费周期
     * @return 包含intervalUnit和intervalCount的JSONObject
     */
    private JSONObject processBillingCycle(String billingCycle) {
        String intervalUnit = billingCycle;
        int intervalCount = 1;
        
        if (PayPalConstants.BillingCycle.QUARTER.equals(billingCycle)) {
            intervalUnit = PayPalConstants.BillingCycle.MONTH;
            intervalCount = 3;
        }
        
        return new JSONObject()
                .fluentPut("interval_unit", intervalUnit)
                .fluentPut("interval_count", intervalCount);
    }

    @Override
    public JSONObject createPlan(PaypalSubscriptionRequest request) throws Exception {

        // 获取或创建产品
        String productId = getOrCreateProduct(request);

        // 构建订阅计划请求
        JSONObject planRequest = new JSONObject();
        planRequest.put("product_id", productId);
        planRequest.put("name", request.getPlanName());
        planRequest.put("description", request.getDescription());
        planRequest.put("status", "ACTIVE");
        planRequest.put("billing_cycles", new JSONArray());

        // 添加第一个周期的计费（优惠价格）
        JSONObject firstBillingCycle = new JSONObject();

        int discountCycles = 0;
        if (null != request.getDiscountAmount()) {
            // 处理计费周期，支持季度扣费
            JSONObject interval = processBillingCycle(request.getBillingCycle());
            firstBillingCycle.put("frequency", interval);
            firstBillingCycle.put("tenure_type", "TRIAL");  // 设置为试用期类型
            firstBillingCycle.put("sequence", 1);
            firstBillingCycle.put("total_cycles", 1);  // 只执行一次
            firstBillingCycle.put("pricing_scheme", new JSONObject()
                    .fluentPut("fixed_price", new JSONObject()
                            .fluentPut("value", request.getDiscountAmount().toString())  // 使用优惠价格
                            .fluentPut("currency_code", "USD")));
            discountCycles = 1;
            planRequest.getJSONArray("billing_cycles").add(firstBillingCycle);
        }

        // 添加后续周期的计费（正常价格）
        JSONObject regularBillingCycle = new JSONObject();
        
        // 处理计费周期，支持季度扣费
        JSONObject interval = processBillingCycle(request.getBillingCycle());
        
        regularBillingCycle.put("frequency", interval);
        regularBillingCycle.put("tenure_type", "REGULAR");
        regularBillingCycle.put("sequence", 1 + discountCycles);
        regularBillingCycle.put("total_cycles", request.getBillingCycles() - discountCycles);  // 总周期数减去优化周期
        regularBillingCycle.put("pricing_scheme", new JSONObject()
                .fluentPut("fixed_price", new JSONObject()
                        .fluentPut("value", request.getAmount().toString())  // 使用正常价格
                        .fluentPut("currency_code", "USD")));

        planRequest.getJSONArray("billing_cycles").add(regularBillingCycle);

        // 添加支付偏好设置
        JSONObject paymentPreferences = new JSONObject();
        paymentPreferences.put("auto_bill_outstanding", true);
        paymentPreferences.put("setup_fee", new JSONObject()
                .fluentPut("value", "0")
                .fluentPut("currency_code", "USD"));
        paymentPreferences.put("setup_fee_failure_action", "CONTINUE");
        paymentPreferences.put("payment_failure_threshold", 3);
        planRequest.put("payment_preferences", paymentPreferences);

        // 发送请求
        return doPost(PayPalConstants.Endpoints.PLANS, planRequest);
    }

    @Override
    public JSONObject createSubscription(String planId, PaypalSubscriptionRequest request) throws Exception {
        Account account = accountService.getCurrentAccount();

        // 构建订阅请求
        JSONObject subscriptionRequest = new JSONObject();
        subscriptionRequest.put("plan_id", planId);
        subscriptionRequest.put("application_context", new JSONObject()
                .fluentPut("brand_name", account.getBrandName())
                .fluentPut("shipping_preference", "NO_SHIPPING")
                .fluentPut("locale", request.getLocale())
                .fluentPut("return_url", request.getReturnUrl())
                .fluentPut("cancel_url", request.getCancelUrl()));

        // 发送请求
        return doPost(PayPalConstants.Endpoints.SUBSCRIPTIONS, subscriptionRequest);
    }

    @Override
    public JSONObject querySubscription(String subscriptionId) throws Exception {
        return doGet(PayPalConstants.Endpoints.SUBSCRIPTIONS + "/" + subscriptionId);
    }

    @Override
    public JSONObject cancelSubscription(String subscriptionId) throws Exception {

        JSONObject cancelRequest = new JSONObject();
        cancelRequest.put("reason", "Customer requested cancellation");

        return doPost(PayPalConstants.Endpoints.SUBSCRIPTIONS + "/" + subscriptionId + "/cancel", cancelRequest);
    }

    @Override
    public JSONObject suspendSubscription(String subscriptionId) throws Exception {
        JSONObject suspendRequest = new JSONObject();
        suspendRequest.put("reason", "Customer requested suspension");

        return doPost(PayPalConstants.Endpoints.SUBSCRIPTIONS + "/" + subscriptionId + "/suspend", suspendRequest);
    }

    @Override
    public JSONObject reactivateSubscription(String subscriptionId) throws Exception {
        return doPost(PayPalConstants.Endpoints.SUBSCRIPTIONS + "/" + subscriptionId + "/activate", null);
    }
}
