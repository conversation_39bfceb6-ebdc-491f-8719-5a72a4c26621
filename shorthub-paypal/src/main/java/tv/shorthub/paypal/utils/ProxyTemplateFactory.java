package tv.shorthub.paypal.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;
import org.apache.hc.core5.http.HttpHost;
import org.apache.hc.client5.http.auth.AuthScope;
import org.apache.hc.client5.http.auth.UsernamePasswordCredentials;
import org.apache.hc.client5.http.impl.auth.BasicCredentialsProvider;
import org.apache.hc.client5.http.impl.classic.CloseableHttpClient;
import org.apache.hc.client5.http.impl.classic.HttpClients;
import tv.shorthub.common.utils.spring.SpringUtils;
import tv.shorthub.paypal.model.Account;
import tv.shorthub.paypal.service.PayPalAccountService;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;


@Slf4j
public class ProxyTemplateFactory {

    private static final Map<String, RestTemplate> templateMap = new ConcurrentHashMap<>();
    public static RestTemplate getRestTemplate() {
        return getRestTemplate(SpringUtils.getBean(PayPalAccountService.class).getCurrentAccount());
    }

    public static RestTemplate getRestTemplate(Account account) {
        String key = account.getClientId();
        if (StringUtils.isNotEmpty(account.getProxyHost()) && null != account.getProxyPort()) {
            key = key + ":" + account.getProxyHost() + ":" + account.getProxyPort();
        }
        return templateMap.computeIfAbsent(key, k -> createRestTemplate(account));
    }

    private static RestTemplate createRestTemplate(Account account) {
        CloseableHttpClient httpClient;
        if (StringUtils.isNotEmpty(account.getProxyHost())) {
            HttpHost proxy = new HttpHost(account.getProxyHost(), account.getProxyPort());
            BasicCredentialsProvider credsProvider = new BasicCredentialsProvider();
            credsProvider.setCredentials(
                    new AuthScope(account.getProxyHost(), account.getProxyPort()),
                    new UsernamePasswordCredentials(account.getProxyUser(), account.getProxyPassword().toCharArray())
            );
            httpClient = HttpClients.custom()
                    .setProxy(proxy)
                    .setDefaultCredentialsProvider(credsProvider)
                    .build();
            log.info("Paypal ClientId: {}, Using proxy: {}, {}", account.getClientId(), account.getProxyHost(), account.getProxyPort());
        } else {
            httpClient = HttpClients.createDefault();
        }

        ClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory(httpClient);
        return new RestTemplate(requestFactory);
    }

    public static void removeRestTemplate(Account account) {
        templateMap.remove(account.getClientId());
    }
}
