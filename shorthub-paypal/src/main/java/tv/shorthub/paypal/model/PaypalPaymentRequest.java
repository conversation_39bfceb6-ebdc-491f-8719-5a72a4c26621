package tv.shorthub.paypal.model;

import lombok.Data;
import java.math.BigDecimal;

/**
 * PayPal支付请求
 */
@Data
public class PaypalPaymentRequest {
    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 商品名称
     */
    private String itemName;

    /**
     * 支付金额
     */
    private BigDecimal amount;

    /**
     * 支付方式
     */
    private String paymentMethod;

    /**
     * 返回URL
     */
    private String returnUrl;

    /**
     * 取消URL
     */
    private String cancelUrl;

    /**
     * PayPal账户ID
     * 如果为空，则使用默认账户
     */
    private String accountId;

    private String locale = "en-US";

    private String userId;
    private String vaultId;
}
