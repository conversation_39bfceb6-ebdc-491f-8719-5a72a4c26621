package tv.shorthub.paypal.model;

import lombok.Data;
import java.math.BigDecimal;

/**
 * PayPal订阅请求
 */
@Data
public class PaypalSubscriptionRequest {
    /**
     * 订阅计划名称
     */
    private String planName;

    /**
     * 订阅计划描述
     */
    private String description;

    /**
     * 语言
     */
    private String locale;

    /**
     * 订阅金额
     */
    private BigDecimal amount;

    /**
     * 优惠金额（第一个周期的价格）
     */
    private BigDecimal discountAmount;

    /**
     * 订阅周期 (DAY, WEEK, MONTH, YEAR)
     */
    private String billingCycle;

    /**
     * 订阅周期数
     */
    private Integer billingCycles;

    /**
     * 返回URL
     */
    private String returnUrl;

    /**
     * 取消URL
     */
    private String cancelUrl;

    /**
     * PayPal账户ID
     * 如果为空，则使用默认账户
     */
    private String accountId;

    /**
     * 用户ID
     */
    private String userId;
} 