package tv.shorthub.paypal.model;

import lombok.Data;

/**
 * PayPal账户配置
 */
@Data
public class Account {
        /**
         * 账户ID
         */
        private String id;

        /**
         * 客户端ID
         */
        private String clientId;

        /**
         * 客户端密钥
         */
        private String clientSecret;

        /**
         * 是否沙盒环境
         */
        private boolean sandbox;

        /**
         * 商家邮箱
         */
        private String merchantEmail;

        /**
         * API基础URL
         */
        private String baseUrl;

        /**
         * 品牌名称
         */
        private String brandName;

        /**
         * 是否启用
         */
        private boolean enabled = true;

        /**
         * 代理服务器地址
         */
        private String proxyHost;

        /**
         * 代理服务器端口
         */
        private Integer proxyPort;

        /**
         * 代理用户名
         */
        private String proxyUser;

        /**
         * 代理密码
         */
        private String proxyPassword;
}
