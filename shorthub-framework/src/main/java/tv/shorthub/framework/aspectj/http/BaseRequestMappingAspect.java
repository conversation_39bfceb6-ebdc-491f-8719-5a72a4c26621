package tv.shorthub.framework.aspectj.http;

import com.alibaba.fastjson2.JSON;
import tv.shorthub.common.enums.SysEnv;
import tv.shorthub.common.utils.SecurityUtils;
import tv.shorthub.common.utils.ServletUtils;
import tv.shorthub.common.utils.ip.IpUtils;
import tv.shorthub.common.utils.spring.SpringUtils;
import tv.shorthub.common.utils.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.validation.BindingResult;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.util.Collection;
import java.util.Map;

/**
 * 接口日志输出
 *
 * <AUTHOR>
 */
public class BaseRequestMappingAspect
{
    private static final Logger log = LoggerFactory.getLogger(BaseRequestMappingAspect.class);


    public Object doAround(ProceedingJoinPoint pjp) throws Throwable {
        Object[] args = pjp.getArgs();
        long time = System.currentTimeMillis();

        //执行接口
        Object proceed;
        String requestStr = argsArrayToString(args);
        try {
            proceed = pjp.proceed();
            log.info("正常请求: URI {}, cost: {}ms,  DETAIL: [ip={}, userId={}, tfid={}], REQ: [{}] RES: [{}]",
                    StringUtils.substring(ServletUtils.getRequest().getRequestURI(), 0, 255),
                    System.currentTimeMillis() - time,
                    IpUtils.getIpAddr(ServletUtils.getRequest()),
                    getUserId(),
                    ServletUtils.getRequest().getHeader("access-tfid"),
                    requestStr,
                    StringUtils.substring(JSON.toJSONString(proceed), 0, 2000)
            );
        } catch (Throwable e) {
            log.error("异常请求: URI {}, cost: {}ms, DETAIL: [ip={}, userId={}, exception={}], REQ: [{}], RES: [{}]",
                    StringUtils.substring(ServletUtils.getRequest().getRequestURI(), 0, 255),
                    System.currentTimeMillis() - time,
                    IpUtils.getIpAddr(ServletUtils.getRequest()),
                    getUserId(),
                    e.getMessage(),
                    requestStr,
                    "",
                    e
            );
            throw e;
        }
        return proceed;
    }

    private String getUserId() {
        return String.valueOf(SecurityUtils.safeGetUserId());

    }

    /**
     * 参数拼装
     */
    private String argsArrayToString(Object[] paramsArray)
    {
        String params = "";
        if (paramsArray != null && paramsArray.length > 0)
        {
            for (Object o : paramsArray)
            {
                if (StringUtils.isNotNull(o) && !isFilterObject(o))
                {
                    try
                    {
                        String jsonObj = JSON.toJSONString(o);
                        params += jsonObj + " ";
                    }
                    catch (Exception e)
                    {
                    }
                }
            }
        }
        return params.trim();
    }

    /**
     * 判断是否需要过滤的对象。
     *
     * @param o 对象信息。
     * @return 如果是需要过滤的对象，则返回true；否则返回false。
     */
    @SuppressWarnings("rawtypes")
    public boolean isFilterObject(final Object o)
    {
        Class<?> clazz = o.getClass();
        if (clazz.isArray())
        {
            return clazz.getComponentType().isAssignableFrom(MultipartFile.class);
        }
        else if (Collection.class.isAssignableFrom(clazz))
        {
            Collection collection = (Collection) o;
            for (Object value : collection)
            {
                return value instanceof MultipartFile;
            }
        }
        else if (Map.class.isAssignableFrom(clazz))
        {
            Map map = (Map) o;
            for (Object value : map.entrySet())
            {
                Map.Entry entry = (Map.Entry) value;
                return entry.getValue() instanceof MultipartFile;
            }
        }
        return o instanceof MultipartFile || o instanceof HttpServletRequest || o instanceof HttpServletResponse
                || o instanceof BindingResult;
    }
}
