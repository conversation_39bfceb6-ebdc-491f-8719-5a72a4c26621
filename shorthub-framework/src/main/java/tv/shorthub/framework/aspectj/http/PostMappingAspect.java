package tv.shorthub.framework.aspectj.http;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

/**
 * 接口日志输出
 *
 * <AUTHOR>
 */
@Aspect
@Component
public class PostMappingAspect extends BaseRequestMappingAspect
{

    @Around("@annotation(org.springframework.web.bind.annotation.PostMapping)")
    public Object around(ProceedingJoinPoint pjp) throws Throwable {
        return doAround(pjp);
    }
}
