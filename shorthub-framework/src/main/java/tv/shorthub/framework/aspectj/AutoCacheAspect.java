package tv.shorthub.framework.aspectj;

import tv.shorthub.common.annotation.AutoCache;
import tv.shorthub.common.core.redis.RedisCacheNotify;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;

/**
 * 操作日志记录处理
 *
 * <AUTHOR>
 */
@Aspect
@Component
@Slf4j
public class AutoCacheAspect
{
    @Autowired
    RedisCacheNotify redisCacheNotify;

    @Around("@annotation(tv.shorthub.common.annotation.AutoCache)")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        Object proceed = joinPoint.proceed();

        // 获取方法签名
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        // 获取方法对象
        Method method = signature.getMethod();
        // 获取注解对象
        AutoCache autoCache = method.getAnnotation(AutoCache.class);

        if (StringUtils.isNotEmpty(autoCache.key())) {
            redisCacheNotify.notify(autoCache.key());
        } else {
            for (String key : autoCache.keys()) {
                redisCacheNotify.notify(key);
            }
        }
        return proceed;
    }
}
