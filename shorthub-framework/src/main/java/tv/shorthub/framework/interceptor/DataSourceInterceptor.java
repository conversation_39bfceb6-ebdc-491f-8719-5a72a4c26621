package tv.shorthub.framework.interceptor;

import tv.shorthub.common.enums.DataSourceType;
import tv.shorthub.framework.datasource.DynamicDataSourceContextHolder;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

@Component
public class DataSourceInterceptor implements HandlerInterceptor {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        String dataName = request.getHeader("dataName");
        if (ObjectUtils.isEmpty(dataName)) {
            DynamicDataSourceContextHolder.setDataSourceType(DataSourceType.MASTER.name());
        } else {
            DynamicDataSourceContextHolder.setDataSourceType(dataName);
        }

        // You can add more conditions based on your requirements
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {
        DynamicDataSourceContextHolder.clearDataSourceType();
    }
}
