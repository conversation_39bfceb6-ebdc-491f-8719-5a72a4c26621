<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

    <groupId>tv.shorthub</groupId>
    <artifactId>shorthub</artifactId>
    <version>1.0.0</version>

    <name>shorthub</name>
    <description>Shorthub-TV广告平台</description>

    <properties>
        <spring-boot.version>3.3.0</spring-boot.version>
        <shorthub.version>1.0.0</shorthub.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>21</java.version>
        <maven-jar-plugin.version>3.1.1</maven-jar-plugin.version>
        <druid.version>1.2.20</druid.version>
        <bitwalker.version>1.21</bitwalker.version>
        <swagger.version>3.0.0</swagger.version>
        <kaptcha.version>2.3.3</kaptcha.version>
        <pagehelper.boot.version>2.1.0</pagehelper.boot.version>
        <fastjson.version>2.0.43</fastjson.version>
        <oshi.version>6.4.8</oshi.version>
        <commons.io.version>2.16.1</commons.io.version>
        <commons.text.version>1.12.0</commons.text.version>
        <commons.collections4.version>4.4</commons.collections4.version>
        <poi.version>5.3.0</poi.version>
        <velocity.version>2.3</velocity.version>
        <jwt.version>0.9.1</jwt.version>
        <mybatis-plus.version>3.5.7</mybatis-plus.version>
        <jsqlparse.version>4.9</jsqlparse.version>
        <easyexcel.version>3.1.3</easyexcel.version>
        <zxing.version>3.5.3</zxing.version>
        <caffeine.version>3.1.5</caffeine.version>
        <hutool.version>5.8.25</hutool.version>
        <aliyun-oss.version>3.15.2</aliyun-oss.version>
        <wechat-mp.version>4.4.4.B</wechat-mp.version>
        <wechat-cp.version>4.6.2.B</wechat-cp.version>
        <redisson.version>3.20.0</redisson.version>
    </properties>

    <!-- 依赖声明 -->
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>${easyexcel.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.jsqlparser</groupId>
                <artifactId>jsqlparser</artifactId>
                <version>${jsqlparse.version}</version>
            </dependency>
            <!-- 上面排除掉，再引入，版本不兼容问题 -->
       <!--     <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis-spring</artifactId>
                <version>3.0.3</version>
            </dependency>-->


            <!-- SpringBoot的依赖配置-->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- 阿里数据库连接池 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-3-starter</artifactId>
                <version>${druid.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>



            <!-- https://mvnrepository.com/artifact/com.github.binarywang/weixin-java-mp -->
            <dependency>
                <groupId>com.github.binarywang</groupId>
                <artifactId>weixin-java-mp</artifactId>
                <version>${wechat-mp.version}</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/com.github.binarywang/weixin-java-miniapp -->
            <dependency>
                <groupId>com.github.binarywang</groupId>
                <artifactId>weixin-java-miniapp</artifactId>
                <version>${wechat-mp.version}</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/com.github.binarywang/weixin-java-pay -->
            <dependency>
                <groupId>com.github.binarywang</groupId>
                <artifactId>weixin-java-pay</artifactId>
                <version>${wechat-mp.version}</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/com.github.binarywang/weixin-java-cp -->
            <dependency>
                <groupId>com.github.binarywang</groupId>
                <artifactId>weixin-java-cp</artifactId>
                <version>${wechat-cp.version}</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/com.github.binarywang/weixin-java-open -->
            <dependency>
                <groupId>com.github.binarywang</groupId>
                <artifactId>weixin-java-open</artifactId>
                <version>${wechat-mp.version}</version>
            </dependency>

            <dependency>
                <groupId>com.aliyun.oss</groupId>
                <artifactId>aliyun-sdk-oss</artifactId>
                <version>${aliyun-oss.version}</version>
            </dependency>

            <!-- 解析客户端操作系统、浏览器等 -->
            <dependency>
                <groupId>eu.bitwalker</groupId>
                <artifactId>UserAgentUtils</artifactId>
                <version>${bitwalker.version}</version>
            </dependency>

            <!-- pagehelper 分页插件 -->
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper-spring-boot-starter</artifactId>
                <version>${pagehelper.boot.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-starter</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- 获取系统信息 -->
            <dependency>
                <groupId>com.github.oshi</groupId>
                <artifactId>oshi-core</artifactId>
                <version>${oshi.version}</version>
            </dependency>

            <!-- Swagger3依赖 -->
            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-boot-starter</artifactId>
                <version>${swagger.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>io.swagger</groupId>
                        <artifactId>swagger-models</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- io常用工具类 -->
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons.io.version}</version>
            </dependency>

            <!-- excel工具 -->
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>${poi.version}</version>
            </dependency>

            <!-- velocity代码生成使用模板 -->
            <dependency>
                <groupId>org.apache.velocity</groupId>
                <artifactId>velocity-engine-core</artifactId>
                <version>${velocity.version}</version>
            </dependency>

            <!-- collections工具类 -->
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-collections4</artifactId>
                <version>${commons.collections4.version}</version>
            </dependency>


            <!-- 阿里JSON解析器 -->
            <dependency>
                <groupId>com.alibaba.fastjson2</groupId>
                <artifactId>fastjson2</artifactId>
                <version>${fastjson.version}</version>
            </dependency>

            <!-- Token生成与解析-->
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt</artifactId>
                <version>${jwt.version}</version>
            </dependency>

            <!-- 验证码 -->
            <dependency>
                <groupId>pro.fessional</groupId>
                <artifactId>kaptcha</artifactId>
                <version>${kaptcha.version}</version>
            </dependency>


            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-text</artifactId>
                <version>${commons.text.version}</version>
            </dependency>

            <dependency>
                <groupId>com.google.zxing</groupId>
                <artifactId>core</artifactId>
                <version>${zxing.version}</version>
            </dependency>

            <dependency>
                <groupId>com.google.zxing</groupId>
                <artifactId>javase</artifactId>
                <version>${zxing.version}</version>
            </dependency>

            <dependency>
                <groupId>com.github.ben-manes.caffeine</groupId>
                <artifactId>caffeine</artifactId>
                <version>${caffeine.version}</version>
            </dependency>

            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson</artifactId>
                <version>${redisson.version}</version>
            </dependency>

            <!-- 代码生成-->
            <dependency>
                <groupId>tv.shorthub</groupId>
                <artifactId>shorthub-generator</artifactId>
                <version>${shorthub.version}</version>
            </dependency>

            <!-- 核心模块-->
            <dependency>
                <groupId>tv.shorthub</groupId>
                <artifactId>shorthub-framework</artifactId>
                <version>${shorthub.version}</version>
            </dependency>

            <!-- 系统模块-->
            <dependency>
                <groupId>tv.shorthub</groupId>
                <artifactId>shorthub-system</artifactId>
                <version>${shorthub.version}</version>
            </dependency>

            <!-- 通用工具-->
            <dependency>
                <groupId>tv.shorthub</groupId>
                <artifactId>shorthub-common</artifactId>
                <version>${shorthub.version}</version>
            </dependency>

            <!-- 接入层服务-->
            <dependency>
                <groupId>tv.shorthub</groupId>
                <artifactId>shorthub-api</artifactId>
                <version>${shorthub.version}</version>
            </dependency>

            <!-- 微信服务-->
            <dependency>
                <groupId>tv.shorthub</groupId>
                <artifactId>shorthub-wechat</artifactId>
                <version>${shorthub.version}</version>
            </dependency>

            <!-- PayPal服务-->
            <dependency>
                <groupId>tv.shorthub</groupId>
                <artifactId>shorthub-paypal</artifactId>
                <version>${shorthub.version}</version>
            </dependency>

            <!-- PayPal服务-->
            <dependency>
                <groupId>tv.shorthub</groupId>
                <artifactId>shorthub-ad</artifactId>
                <version>${shorthub.version}</version>
            </dependency>

            <!-- Payermax服务-->
            <dependency>
                <groupId>tv.shorthub</groupId>
                <artifactId>shorthub-payermax</artifactId>
                <version>${shorthub.version}</version>
            </dependency>

            <!-- 钉钉服务-->
            <dependency>
                <groupId>tv.shorthub</groupId>
                <artifactId>shorthub-dingtalk</artifactId>
                <version>${shorthub.version}</version>
            </dependency>

            <!-- 邮件服务-->
            <dependency>
                <groupId>tv.shorthub</groupId>
                <artifactId>shorthub-email</artifactId>
                <version>${shorthub.version}</version>
            </dependency>

            <dependency>
                <groupId>tv.shorthub</groupId>
                <artifactId>shorthub-statistics</artifactId>
                <version>${shorthub.version}</version>
            </dependency>

            <!-- Google Play服务-->
            <dependency>
                <groupId>tv.shorthub</groupId>
                <artifactId>shorthub-googleplay</artifactId>
                <version>${shorthub.version}</version>
            </dependency>

            <!-- Airwallex服务-->
            <dependency>
                <groupId>tv.shorthub</groupId>
                <artifactId>shorthub-airwallex</artifactId>
                <version>${shorthub.version}</version>
            </dependency>

            <!-- 虚拟货币支付服务-->
            <dependency>
                <groupId>tv.shorthub</groupId>
                <artifactId>shorthub-crypto</artifactId>
                <version>${shorthub.version}</version>
            </dependency>

        </dependencies>
    </dependencyManagement>

    <modules>
        <module>shorthub-admin</module>
        <module>shorthub-wechat</module>
        <module>shorthub-api</module>
        <module>shorthub-event</module>
        <module>shorthub-framework</module>
        <module>shorthub-system</module>
        <module>shorthub-generator</module>
        <module>shorthub-common</module>
        <module>shorthub-paypal</module>
        <module>shorthub-ad</module>
        <module>shorthub-statistics</module>
        <module>shorthub-payermax</module>
        <module>shorthub-googleplay</module>
        <module>shorthub-dingtalk</module>
        <module>shorthub-email</module>
        <module>shorthub-airwallex</module>
        <module>shorthub-crypto</module>
        <module>shorthub-volcengine</module>
    </modules>
    <packaging>pom</packaging>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.1</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                    <compilerArgument>-parameters</compilerArgument>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <repositories>
        <repository>
            <id>public</id>
            <name>aliyun nexus</name>
            <url>https://maven.aliyun.com/repository/public</url>
            <releases>
                <enabled>true</enabled>
            </releases>
        </repository>
    </repositories>

    <pluginRepositories>
        <pluginRepository>
            <id>public</id>
            <name>aliyun nexus</name>
            <url>https://maven.aliyun.com/repository/public</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </pluginRepository>
    </pluginRepositories>

</project>
