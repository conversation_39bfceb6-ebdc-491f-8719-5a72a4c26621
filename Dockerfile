FROM openjdk:21-slim

# 设置工作目录
WORKDIR /app

# 安装必要的依赖并更新安全补丁
RUN apt-get update && \
    apt-get upgrade -y && \
    apt-get install -y --no-install-recommends \
    libfreetype6 \
    fontconfig \
    ffmpeg \
    git \
    curl \
    wget \
    && rm -rf /var/lib/apt/lists/*

# 安装 Maven 3.9.9
RUN wget https://dlcdn.apache.org/maven/maven-3/3.9.9/binaries/apache-maven-3.9.9-bin.tar.gz && \
    tar -xzf apache-maven-3.9.9-bin.tar.gz -C /opt && \
    rm apache-maven-3.9.9-bin.tar.gz && \
    ln -s /opt/apache-maven-3.9.9/bin/mvn /usr/local/bin/mvn

# 设置环境变量
ENV TZ=Asia/Shanghai
ENV MAVEN_HOME=/opt/apache-maven-3.9.9
ENV PATH=$MAVEN_HOME/bin:$PATH

# 复制所有源代码
COPY . /app/

# 构建应用
RUN mvn clean package -DskipTests

# 创建必要的目录
RUN mkdir -p /app/logs \
    /app/uploadPath/download \
    /app/pid

# 创建日志文件并设置权限
RUN touch /app/logs/sys-info.log && \
    chmod +x /app/shorthub-admin/src/main/resources/build/start.sh

EXPOSE 8080

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8080/actuator/health || exit 1

# 保持容器运行
CMD ["tail", "-f", "/dev/null"] 