import java.security.MessageDigest;
import java.util.Arrays;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import org.bitcoinj.crypto.HDKeyDerivation;
import org.bitcoinj.crypto.DeterministicKey;
import org.bitcoinj.crypto.MnemonicCode;
import org.web3j.crypto.MnemonicUtils;
import net.i2p.crypto.eddsa.spec.EdDSANamedCurveTable;
import net.i2p.crypto.eddsa.spec.EdDSAPrivateKeySpec;
import net.i2p.crypto.eddsa.spec.EdDSAParameterSpec;
import net.i2p.crypto.eddsa.EdDSAPrivateKey;
import net.i2p.crypto.eddsa.EdDSAPublicKey;

public class SolanaPhantomTest {
    
    public static void main(String[] args) throws Exception {
        // Test the specific case user provided
        String testMnemonic = "autumn wink trash organ catalog enrich thing student hundred mobile face flame";
        String expectedAddress = "482BcArEm6vAFXHwUv6MGEyudhZsiEFakH4Hsfd9hJu1";
        String actualAddress = "2enVRf5CHnxG92mq6s3q98UDoBYwdtJFrnizw4QTrSto";
        
        System.out.println("=== Testing User's Specific Case ===");
        System.out.println("Mnemonic: " + testMnemonic);
        System.out.println("Expected (Phantom): " + expectedAddress);
        System.out.println("Actual (Our code): " + actualAddress);
        System.out.println();
        
        // Test all different approaches
        testAllApproaches(testMnemonic, expectedAddress, 0);
    }
    
    public static void testAllApproaches(String mnemonic, String expectedAddress, int accountIndex) throws Exception {
        System.out.println("=== Testing All Approaches for Account Index: " + accountIndex + " ===");
        
        // Approach 1: Web3j seed generation + standard BIP44
        testApproach1(mnemonic, accountIndex, expectedAddress);
        
        // Approach 2: Web3j seed generation + different BIP44 path
        testApproach2(mnemonic, accountIndex, expectedAddress);
        
        // Approach 3: BitcoinJ seed generation
        testApproach3(mnemonic, accountIndex, expectedAddress);
        
        // Approach 4: Manual PBKDF2
        testApproach4(mnemonic, accountIndex, expectedAddress);
        
        // Approach 5: With passphrase
        testApproach5(mnemonic, accountIndex, expectedAddress);
        
        // Approach 6: Direct seed as private key
        testApproach6(mnemonic, accountIndex, expectedAddress);
    }
    
    private static void testApproach1(String mnemonic, int accountIndex, String expectedAddress) throws Exception {
        System.out.println("--- Approach 1: Web3j + Standard BIP44 m/44'/501'/" + accountIndex + "'/0' ---");
        try {
            byte[] seed = MnemonicUtils.generateSeed(mnemonic, "");
            
            DeterministicKey masterKey = HDKeyDerivation.createMasterPrivateKey(seed);
            DeterministicKey purposeKey = HDKeyDerivation.deriveChildKey(masterKey, 44 | 0x80000000);
            DeterministicKey coinKey = HDKeyDerivation.deriveChildKey(purposeKey, 501 | 0x80000000);
            DeterministicKey accountKey = HDKeyDerivation.deriveChildKey(coinKey, accountIndex | 0x80000000);
            DeterministicKey addressKey = HDKeyDerivation.deriveChildKey(accountKey, 0 | 0x80000000);
            
            byte[] privateKeyBytes = addressKey.getPrivKeyBytes();
            
            EdDSAParameterSpec spec = EdDSANamedCurveTable.getByName("Ed25519");
            EdDSAPrivateKeySpec privKeySpec = new EdDSAPrivateKeySpec(privateKeyBytes, spec);
            EdDSAPrivateKey privKey = new EdDSAPrivateKey(privKeySpec);
            EdDSAPublicKey pubKey = new EdDSAPublicKey(
                new net.i2p.crypto.eddsa.spec.EdDSAPublicKeySpec(privKey.getA(), spec));
            
            String address = encodeBase58(pubKey.getAbyte());
            System.out.println("Result: " + address);
            System.out.println("Match: " + address.equals(expectedAddress));
            
        } catch (Exception e) {
            System.out.println("Error: " + e.getMessage());
        }
        System.out.println();
    }
    
    private static void testApproach2(String mnemonic, int accountIndex, String expectedAddress) throws Exception {
        System.out.println("--- Approach 2: Web3j + Alternative BIP44 m/44'/501'/0'/0/" + accountIndex + " ---");
        try {
            byte[] seed = MnemonicUtils.generateSeed(mnemonic, "");
            
            DeterministicKey masterKey = HDKeyDerivation.createMasterPrivateKey(seed);
            DeterministicKey purposeKey = HDKeyDerivation.deriveChildKey(masterKey, 44 | 0x80000000);
            DeterministicKey coinKey = HDKeyDerivation.deriveChildKey(purposeKey, 501 | 0x80000000);
            DeterministicKey accountKey = HDKeyDerivation.deriveChildKey(coinKey, 0 | 0x80000000);
            DeterministicKey changeKey = HDKeyDerivation.deriveChildKey(accountKey, 0);
            DeterministicKey addressKey = HDKeyDerivation.deriveChildKey(changeKey, accountIndex);
            
            byte[] privateKeyBytes = addressKey.getPrivKeyBytes();
            
            EdDSAParameterSpec spec = EdDSANamedCurveTable.getByName("Ed25519");
            EdDSAPrivateKeySpec privKeySpec = new EdDSAPrivateKeySpec(privateKeyBytes, spec);
            EdDSAPrivateKey privKey = new EdDSAPrivateKey(privKeySpec);
            EdDSAPublicKey pubKey = new EdDSAPublicKey(
                new net.i2p.crypto.eddsa.spec.EdDSAPublicKeySpec(privKey.getA(), spec));
            
            String address = encodeBase58(pubKey.getAbyte());
            System.out.println("Result: " + address);
            System.out.println("Match: " + address.equals(expectedAddress));
            
        } catch (Exception e) {
            System.out.println("Error: " + e.getMessage());
        }
        System.out.println();
    }
    
    private static void testApproach3(String mnemonic, int accountIndex, String expectedAddress) throws Exception {
        System.out.println("--- Approach 3: BitcoinJ MnemonicCode + BIP44 ---");
        try {
            byte[] seed = MnemonicCode.toSeed(Arrays.asList(mnemonic.split(" ")), "");
            
            DeterministicKey masterKey = HDKeyDerivation.createMasterPrivateKey(seed);
            DeterministicKey purposeKey = HDKeyDerivation.deriveChildKey(masterKey, 44 | 0x80000000);
            DeterministicKey coinKey = HDKeyDerivation.deriveChildKey(purposeKey, 501 | 0x80000000);
            DeterministicKey accountKey = HDKeyDerivation.deriveChildKey(coinKey, accountIndex | 0x80000000);
            DeterministicKey addressKey = HDKeyDerivation.deriveChildKey(accountKey, 0 | 0x80000000);
            
            byte[] privateKeyBytes = addressKey.getPrivKeyBytes();
            
            EdDSAParameterSpec spec = EdDSANamedCurveTable.getByName("Ed25519");
            EdDSAPrivateKeySpec privKeySpec = new EdDSAPrivateKeySpec(privateKeyBytes, spec);
            EdDSAPrivateKey privKey = new EdDSAPrivateKey(privKeySpec);
            EdDSAPublicKey pubKey = new EdDSAPublicKey(
                new net.i2p.crypto.eddsa.spec.EdDSAPublicKeySpec(privKey.getA(), spec));
            
            String address = encodeBase58(pubKey.getAbyte());
            System.out.println("Result: " + address);
            System.out.println("Match: " + address.equals(expectedAddress));
            
        } catch (Exception e) {
            System.out.println("Error: " + e.getMessage());
        }
        System.out.println();
    }
    
    private static void testApproach4(String mnemonic, int accountIndex, String expectedAddress) throws Exception {
        System.out.println("--- Approach 4: Manual PBKDF2 + BIP44 ---");
        try {
            byte[] seed = generateSeedPBKDF2(mnemonic, "");
            
            DeterministicKey masterKey = HDKeyDerivation.createMasterPrivateKey(seed);
            DeterministicKey purposeKey = HDKeyDerivation.deriveChildKey(masterKey, 44 | 0x80000000);
            DeterministicKey coinKey = HDKeyDerivation.deriveChildKey(purposeKey, 501 | 0x80000000);
            DeterministicKey accountKey = HDKeyDerivation.deriveChildKey(coinKey, accountIndex | 0x80000000);
            DeterministicKey addressKey = HDKeyDerivation.deriveChildKey(accountKey, 0 | 0x80000000);
            
            byte[] privateKeyBytes = addressKey.getPrivKeyBytes();
            
            EdDSAParameterSpec spec = EdDSANamedCurveTable.getByName("Ed25519");
            EdDSAPrivateKeySpec privKeySpec = new EdDSAPrivateKeySpec(privateKeyBytes, spec);
            EdDSAPrivateKey privKey = new EdDSAPrivateKey(privKeySpec);
            EdDSAPublicKey pubKey = new EdDSAPublicKey(
                new net.i2p.crypto.eddsa.spec.EdDSAPublicKeySpec(privKey.getA(), spec));
            
            String address = encodeBase58(pubKey.getAbyte());
            System.out.println("Result: " + address);
            System.out.println("Match: " + address.equals(expectedAddress));
            
        } catch (Exception e) {
            System.out.println("Error: " + e.getMessage());
        }
        System.out.println();
    }
    
    private static void testApproach5(String mnemonic, int accountIndex, String expectedAddress) throws Exception {
        System.out.println("--- Approach 5: With Empty Passphrase (Explicit) ---");
        try {
            byte[] seed = MnemonicUtils.generateSeed(mnemonic, "");
            
            DeterministicKey masterKey = HDKeyDerivation.createMasterPrivateKey(seed);
            DeterministicKey purposeKey = HDKeyDerivation.deriveChildKey(masterKey, 44 | 0x80000000);
            DeterministicKey coinKey = HDKeyDerivation.deriveChildKey(purposeKey, 501 | 0x80000000);
            DeterministicKey accountKey = HDKeyDerivation.deriveChildKey(coinKey, accountIndex | 0x80000000);
            DeterministicKey addressKey = HDKeyDerivation.deriveChildKey(accountKey, 0 | 0x80000000);
            
            byte[] privateKeyBytes = addressKey.getPrivKeyBytes();
            
            // Try using the seed directly as Ed25519 seed 
            EdDSAParameterSpec spec = EdDSANamedCurveTable.getByName("Ed25519");
            EdDSAPrivateKeySpec privKeySpec = new EdDSAPrivateKeySpec(Arrays.copyOf(seed, 32), spec);
            EdDSAPrivateKey privKey = new EdDSAPrivateKey(privKeySpec);
            EdDSAPublicKey pubKey = new EdDSAPublicKey(
                new net.i2p.crypto.eddsa.spec.EdDSAPublicKeySpec(privKey.getA(), spec));
            
            String address = encodeBase58(pubKey.getAbyte());
            System.out.println("Result (seed as Ed25519): " + address);
            System.out.println("Match: " + address.equals(expectedAddress));
            
            // Also try with private key
            EdDSAPrivateKeySpec privKeySpec2 = new EdDSAPrivateKeySpec(privateKeyBytes, spec);
            EdDSAPrivateKey privKey2 = new EdDSAPrivateKey(privKeySpec2);
            EdDSAPublicKey pubKey2 = new EdDSAPublicKey(
                new net.i2p.crypto.eddsa.spec.EdDSAPublicKeySpec(privKey2.getA(), spec));
            
            String address2 = encodeBase58(pubKey2.getAbyte());
            System.out.println("Result (BIP44 key): " + address2);
            System.out.println("Match: " + address2.equals(expectedAddress));
            
        } catch (Exception e) {
            System.out.println("Error: " + e.getMessage());
        }
        System.out.println();
    }
    
    private static void testApproach6(String mnemonic, int accountIndex, String expectedAddress) throws Exception {
        System.out.println("--- Approach 6: Direct Seed Slice + Ed25519 ---");
        try {
            // Try using different slices of the seed as the Ed25519 private key
            byte[] seed = MnemonicUtils.generateSeed(mnemonic, "");
            
            // First 32 bytes
            byte[] seed32 = Arrays.copyOf(seed, 32);
            EdDSAParameterSpec spec = EdDSANamedCurveTable.getByName("Ed25519");
            EdDSAPrivateKeySpec privKeySpec = new EdDSAPrivateKeySpec(seed32, spec);
            EdDSAPrivateKey privKey = new EdDSAPrivateKey(privKeySpec);
            EdDSAPublicKey pubKey = new EdDSAPublicKey(
                new net.i2p.crypto.eddsa.spec.EdDSAPublicKeySpec(privKey.getA(), spec));
            
            String address = encodeBase58(pubKey.getAbyte());
            System.out.println("Result (first 32 bytes): " + address);
            System.out.println("Match: " + address.equals(expectedAddress));
            
            // Last 32 bytes
            byte[] seedLast32 = Arrays.copyOfRange(seed, 32, 64);
            EdDSAPrivateKeySpec privKeySpec2 = new EdDSAPrivateKeySpec(seedLast32, spec);
            EdDSAPrivateKey privKey2 = new EdDSAPrivateKey(privKeySpec2);
            EdDSAPublicKey pubKey2 = new EdDSAPublicKey(
                new net.i2p.crypto.eddsa.spec.EdDSAPublicKeySpec(privKey2.getA(), spec));
            
            String address2 = encodeBase58(pubKey2.getAbyte());
            System.out.println("Result (last 32 bytes): " + address2);
            System.out.println("Match: " + address2.equals(expectedAddress));
            
        } catch (Exception e) {
            System.out.println("Error: " + e.getMessage());
        }
        System.out.println();
    }
    
    private static byte[] generateSeedPBKDF2(String mnemonic, String passphrase) throws Exception {
        String salt = "mnemonic" + passphrase;
        
        javax.crypto.spec.PBEKeySpec spec = new javax.crypto.spec.PBEKeySpec(
            mnemonic.toCharArray(), 
            salt.getBytes("UTF-8"), 
            2048, 
            512
        );
        
        javax.crypto.SecretKeyFactory factory = javax.crypto.SecretKeyFactory.getInstance("PBKDF2WithHmacSHA512");
        byte[] key = factory.generateSecret(spec).getEncoded();
        return key;
    }
    
    private static final String ALPHABET = "**********************************************************";
    
    private static String encodeBase58(byte[] input) {
        if (input.length == 0) return "";
        
        int[] digits = new int[input.length * 2];
        int digitslen = 1;
        
        for (int i = 0; i < input.length; i++) {
            int carry = input[i] & 0xFF;
            for (int j = 0; j < digitslen; j++) {
                carry += digits[j] << 8;
                digits[j] = carry % 58;
                carry /= 58;
            }
            while (carry > 0) {
                digits[digitslen++] = carry % 58;
                carry /= 58;
            }
        }
        
        // Handle leading zeros
        int zeroslen = 0;
        while (zeroslen < input.length && input[zeroslen] == 0) {
            zeroslen++;
        }
        
        StringBuilder result = new StringBuilder();
        for (int i = 0; i < zeroslen; i++) {
            result.append(ALPHABET.charAt(0));
        }
        for (int i = digitslen - 1; i >= 0; i--) {
            result.append(ALPHABET.charAt(digits[i]));
        }
        
        return result.toString();
    }
}