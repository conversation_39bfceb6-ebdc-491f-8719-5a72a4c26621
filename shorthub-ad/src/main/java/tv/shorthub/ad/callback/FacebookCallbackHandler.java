package tv.shorthub.ad.callback;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.hc.client5.http.classic.methods.HttpGet;
import org.apache.hc.client5.http.classic.methods.HttpPost;
import org.apache.hc.client5.http.impl.classic.CloseableHttpClient;
import org.apache.hc.client5.http.impl.classic.HttpClients;
import org.apache.hc.core5.http.ContentType;
import org.apache.hc.core5.http.io.entity.StringEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import tv.shorthub.ad.config.FacebookEventType;
import tv.shorthub.ad.domain.*;
import tv.shorthub.ad.service.FacebookAttributionService;
import tv.shorthub.common.enums.AdPlatformEnum;
import tv.shorthub.common.enums.SysEnv;
import tv.shorthub.common.utils.StringUtils;
import tv.shorthub.common.utils.spring.SpringUtils;
import tv.shorthub.system.domain.AdRetargetingStrategy;
import tv.shorthub.system.domain.AdRetargetingEvent;
import tv.shorthub.system.domain.AppPromotionRequestLog;
import tv.shorthub.system.service.IAppPromotionRequestLogService;
import org.apache.hc.core5.http.HttpHost;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.Cookie;
import java.io.IOException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Map;
import java.util.List;
import java.nio.charset.StandardCharsets;
import java.lang.StringBuilder;

/**
 * Facebook回传处理器
 * https://developers.facebook.com/docs/marketing-api/conversions-api/using-the-api/
 *
 * https://business.facebook.com/events_manager2/list/dataset/2760888280746751/overview?business_id=1030377568729480&nav_source=
 *
 * https://business.facebook.com/events_manager2/list/dataset/2760888280746751/test_events?business_id=1030377568729480&nav_source=
 */
@Slf4j
@Component
public class FacebookCallbackHandler implements CallbackHandler {

    private static final String FACEBOOK_API_VERSION = "v22.0";
    private static final String FACEBOOK_API_BASE_URL = "https://graph.facebook.com/" + FACEBOOK_API_VERSION;

    @Autowired
    FacebookAttributionService facebookAttributionService;
    @Override
    public String getPlatform() {
        return AdPlatformEnum.Facebook.getValue();
    }

    @Override
    public String buildCallbackUrl(AdRetargetingStrategy strategy, AdRetargetingEvent event, RetargetingEventData eventData) {
        // Facebook回传URL格式: https://graph.facebook.com/v22.0/{pixel-id}/events
        JSONObject callbackParams = (JSONObject)strategy.getParams().get("callbackParams");
        String pixelId = callbackParams.getString("pixelId");
        String accessToken = callbackParams.getString("accessToken");
        return FACEBOOK_API_BASE_URL + "/" + pixelId + "/events?access_token=" + accessToken;
    }

    @Override
    public String buildCallbackParams(AdRetargetingStrategy strategy, AdRetargetingEvent event, RetargetingEventData eventData) {
        JSONObject callbackParams = (JSONObject)strategy.getParams().get("callbackParams");
        JSONObject params = new JSONObject();

        // 1. 构建事件数据
        JSONObject eventDataObj = new JSONObject();
        String eventType = event.getEventType();
        eventDataObj.put("event_name", eventType);
        eventDataObj.put("event_time", null == eventData.getEventTime() ? System.currentTimeMillis() / 1000 : eventData.getEventTime());
        eventDataObj.put("action_source", StringUtils.isEmpty(eventData.getActionSource()) ? "website" :  eventData.getActionSource());

        // 2. 添加用户数据
        eventDataObj.put("user_data", getUserData(eventData));

        // 3. 添加自定义数据
        eventDataObj.put("custom_data", getCustomData(eventData));

        // 4. 构建最终请求参数
        params.put("data", new Object[]{eventDataObj});
        if (callbackParams.containsKey("testEventCode")) {
            params.put("test_event_code", callbackParams.get("testEventCode"));
        }

        // 记录构建的参数
        log.info("Built Facebook callback parameters with enhanced attribution: {}", params.toJSONString());

        return params.toJSONString();
    }

    private JSONObject getUserData(RetargetingEventData eventData) {
        // 2. 获取Facebook归因数据（优先从事件数据，然后从数据库备份）
        FacebookAttributionData attributionData = getEnhancedFacebookAttribution(eventData);

        JSONObject userData = new JSONObject();

        // 高优先级参数 - fbc (Facebook Click ID)
        if (null != attributionData && StringUtils.isNotEmpty(attributionData.getFbc())) {
            userData.put("fbc", attributionData.getFbc());
            log.info("Added fbc parameter: {}", attributionData.getFbc());
        }

        // 中等优先级参数 - fbp (Facebook Browser ID)
        if (null != attributionData && StringUtils.isNotEmpty(attributionData.getFbp())) {
            userData.put("fbp", attributionData.getFbp());
            log.info("Added fbp parameter: {}", attributionData.getFbp());
        }

        // 中等优先级参数 - external_id
        if (null != eventData.getUserAttribution() && eventData.getUserAttribution().containsKey("externalId")) {
            userData.put("external_id", hashData(eventData.getUserAttribution().getString("externalId")));
            log.info("Added external_id parameter");
        } else if (StringUtils.isNotEmpty(eventData.getSid())) {
            userData.put("external_id", hashData(eventData.getSid()));
            log.info("Added external_id parameter from session");
        }

        // 高优先级参数 - Email
        if (StringUtils.isNotEmpty(eventData.getEmail())) {
            userData.put("em", hashData(eventData.getEmail()));
            log.info("Added hashed email parameter");
        }

        // 中等优先级参数 - Phone
        if (StringUtils.isNotEmpty(eventData.getPhone())) {
            userData.put("ph", hashData(eventData.getPhone()));
            log.info("Added hashed phone parameter");
        }

        // 低优先级参数
        if (StringUtils.isNotEmpty(eventData.getClientIp())) {
            userData.put("client_ip_address", eventData.getClientIp());
        }
        if (StringUtils.isNotEmpty(eventData.getClientUserAgent())) {
            userData.put("client_user_agent", eventData.getClientUserAgent());
        }
        return userData;
    }

    private static JSONObject getCustomData(RetargetingEventData eventData) {
        JSONObject customData = new JSONObject();

        // 支付事件
        switch (eventData) {
            case RetargetingEventDataSubscription subscription -> {
                // 确保有value参数
                if (null != subscription.getAmount()) {
                    customData.put("value", subscription.getAmount());
                } else {
                    customData.put("value", 0.0); // 默认值
                }

                // 确保有currency参数
                if (StringUtils.isNotEmpty(subscription.getCurrency())) {
                    customData.put("currency", subscription.getCurrency());
                } else {
                    customData.put("currency", "USD"); // 默认货币
                }

                // 添加订单ID（如果有）
                if (StringUtils.isNotEmpty(subscription.getOrderId())) {
                    customData.put("order_id", subscription.getOrderId());
                }
            }
            case RetargetingEventDataPayment payment -> {
                // 确保有value参数
                if (null != payment.getAmount()) {
                    customData.put("value", payment.getAmount());
                } else {
                    customData.put("value", 0.0); // 默认值
                }

                // 确保有currency参数
                if (StringUtils.isNotEmpty(payment.getCurrency())) {
                    customData.put("currency", payment.getCurrency());
                } else {
                    customData.put("currency", "USD"); // 默认货币
                }

                // 添加订单ID（如果有）
                if (StringUtils.isNotEmpty(payment.getOrderId())) {
                    customData.put("order_id", payment.getOrderId());
                }
            }
            // 订单创建
            case RetargetingEventDataOrderCreate orderCreate -> {
                // 非购物事件，按需添加参数
                if (null != orderCreate.getAmount()) {
                    customData.put("value", orderCreate.getAmount());
                }
                if (StringUtils.isNotEmpty(orderCreate.getCurrency())) {
                    customData.put("currency", orderCreate.getCurrency());
                }
                // 添加订单ID（如果有）
                if (StringUtils.isNotEmpty(orderCreate.getOrderId())) {
                    customData.put("order_id", orderCreate.getOrderId());
                }
            }
            case RetargetingEventDataView view -> {
                customData.put("content_id", view.getContentId());
                customData.put("number", view.getSerialNumber());
                customData.put("fee", view.getFee());
            }
            default -> {
            }
        }
        return customData;
    }

    /**
     * 获取增强的Facebook归因数据
     * 1. 优先从事件数据中获取（来自同步阶段提取的Cookie）
     * 2. 从数据库中查找作为备份
     * 3. 合并数据确保完整性
     */
    private FacebookAttributionData getEnhancedFacebookAttribution(RetargetingEventData eventData) {
        // 1. 从事件数据中获取归因数据（来自同步阶段提取的Cookie）
        FacebookAttributionData eventAttribution = extractAttributionFromEventData(eventData);

        // 2. 从数据库中查找归因数据作为备份
        FacebookAttributionData dbAttribution = facebookAttributionService.findUserAttribution(eventData.getSid(), eventData.getClientIp(), eventData.getDeviceId(), eventData.getTfid());

        // 3. 合并数据，事件数据优先
        FacebookAttributionData finalAttribution = mergeAttributionData(eventAttribution, dbAttribution);

        if (finalAttribution != null) {
            log.info("Enhanced Facebook attribution found - fbc: {}, fbp: {}, tfid: {}, source: {}",
                    finalAttribution.getFbc(), finalAttribution.getFbp(), finalAttribution.getTfid(),
                    eventAttribution != null ? "Event+DB" : "DB");
        } else {
            log.info("No Facebook attribution found for session: {}", eventData.getSid());
        }

        return finalAttribution;
    }

    /**
     * 从事件数据中提取Facebook归因数据
     * 这些数据是在同步阶段从Cookie中提取的
     */
    private FacebookAttributionData extractAttributionFromEventData(RetargetingEventData eventData) {
        try {
            if (null == eventData.getUserAttribution() || !eventData.getUserAttribution().containsKey(AdPlatformEnum.Facebook.getValue())) {
                return null;
            }

            JSONObject facebook = eventData.getUserAttribution().getJSONObject(AdPlatformEnum.Facebook.getValue());

            String fbc = facebook.getString("fbc");
            String fbp = facebook.getString("fbp");
            String fbclid = facebook.getString("fbclid");

            if (fbc != null || fbp != null || fbclid != null) {
                FacebookAttributionData attribution = new FacebookAttributionData();
                attribution.setFbc(fbc);
                attribution.setFbp(fbp);
                attribution.setFbclid(fbclid);
                attribution.setSessionId(eventData.getSid());
                attribution.setClientIp(eventData.getClientIp());

                // 从fbc/fbp中提取时间戳
                long timestamp = extractTimestampFromFbParameter(fbc != null ? fbc : fbp);
                attribution.setAttributionTimestamp(timestamp);
                attribution.setAdTraffic(fbclid != null || fbc != null);

                log.debug("Extracted attribution from event data - fbc: {}, fbp: {}, fbclid: {}",
                    fbc, fbp, fbclid);
                return attribution;
            }

            return null;
        } catch (Exception e) {
            log.warn("Failed to extract attribution from event data", e);
            return null;
        }
    }

    /**
     * 合并事件数据和数据库的归因数据
     */
    private FacebookAttributionData mergeAttributionData(FacebookAttributionData eventData, FacebookAttributionData dbData) {
        if (eventData == null && dbData == null) {
            return null;
        }

        FacebookAttributionData result = new FacebookAttributionData();

        // 事件数据优先（来自同步阶段提取的Cookie）
        if (eventData != null) {
            result.setFbc(eventData.getFbc());
            result.setFbp(eventData.getFbp());
            result.setFbclid(eventData.getFbclid());
            result.setAttributionTimestamp(eventData.getAttributionTimestamp());
            result.setAdTraffic(eventData.isAdTraffic());
            result.setSessionId(eventData.getSessionId());
            result.setClientIp(eventData.getClientIp());
        }

        // 用数据库数据补充缺失的字段
        if (dbData != null) {
            if (result.getFbc() == null) result.setFbc(dbData.getFbc());
            if (result.getFbp() == null) result.setFbp(dbData.getFbp());
            if (result.getFbclid() == null) result.setFbclid(dbData.getFbclid());
            if (result.getAttributionTimestamp() == 0) result.setAttributionTimestamp(dbData.getAttributionTimestamp());
            if (!result.isAdTraffic()) result.setAdTraffic(dbData.isAdTraffic());

            // 数据库数据包含的tfid信息
            result.setTfid(dbData.getTfid());
            if (result.getSessionId() == null) result.setSessionId(dbData.getSessionId());
            if (result.getClientIp() == null) result.setClientIp(dbData.getClientIp());
        }

        return result;
    }

    /**
     * 从fb参数中提取时间戳
     */
    private long extractTimestampFromFbParameter(String fbParam) {
        if (fbParam == null) {
            return System.currentTimeMillis();
        }

        try {
            String[] parts = fbParam.split("\\.");
            if (parts.length >= 3) {
                return Long.parseLong(parts[2]);
            }
        } catch (Exception e) {
            log.warn("Failed to extract timestamp from fb parameter: {}", fbParam);
        }

        return System.currentTimeMillis();
    }

    /**
     * 判断是否为购物相关事件
     */
    private boolean isPurchaseRelatedEvent(String eventType) {
        return eventType.equals("Purchase") ||
               eventType.equals("AddToCart") ||
               eventType.equals("InitiateCheckout") ||
               eventType.equals("AddPaymentInfo");
    }

    /**
     * 使用SHA256对数据进行哈希处理
     */
    private String hashData(String data) {
        if (StringUtils.isEmpty(data)) {
            return null;
        }

        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hash = digest.digest(data.toLowerCase().trim().getBytes(StandardCharsets.UTF_8));
            StringBuilder hexString = new StringBuilder();
            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (NoSuchAlgorithmException e) {
            log.error("SHA-256 algorithm not available", e);
            return data; // 降级返回原始数据
        }
    }

    @Override
    public String executeCallback(String url, String params, String method) throws IOException {
        // Create HttpClient with proxy configuration for development environment
        CloseableHttpClient client;
        if (SysEnv.LOCAL.getValue().equals(SpringUtils.getActiveProfile())) {
            HttpHost proxy = new HttpHost("127.0.0.1", 10809);
            client = HttpClients.custom()
                    .setProxy(proxy)
                    .build();
        } else {
            client = HttpClients.createDefault();
        }

        try {
            if ("GET".equalsIgnoreCase(method)) {
                HttpGet request = new HttpGet(url + "?" + params);
                return client.execute(request, response -> {
                    int statusCode = response.getCode();
                    String responseBody = new String(response.getEntity().getContent().readAllBytes());
                    if (statusCode >= 200 && statusCode < 300) {
                        return responseBody;
                    } else {
                        throw new IOException("Facebook API request failed with status code: " + statusCode + ", response: " + responseBody);
                    }
                });
            } else {
                HttpPost request = new HttpPost(url);
                // 设置请求头
                request.setHeader("Content-Type", "application/json");
                request.setHeader("Accept", "application/json");

                // 设置请求体
                request.setEntity(new StringEntity(params, ContentType.APPLICATION_JSON));

                // 记录请求信息
                log.info("Facebook API Request - URL: {}, Method: {}, Params: {}", url, method, params);

                return client.execute(request, response -> {
                    int statusCode = response.getCode();
                    String responseBody = new String(response.getEntity().getContent().readAllBytes());
                    // 记录响应信息
                    log.info("Facebook API Response - Status: {}, Body: {}", statusCode, responseBody);

                    if (statusCode >= 200 && statusCode < 300) {
                        return responseBody;
                    } else {
                        throw new IOException("Facebook API request failed with status code: " + statusCode + ", response: " + responseBody);
                    }
                });
            }
        } finally {
            client.close();
        }
    }
}
