package tv.shorthub.ad.callback;

import tv.shorthub.ad.domain.RetargetingEventData;
import tv.shorthub.system.domain.AdRetargetingStrategy;
import tv.shorthub.system.domain.AdRetargetingEvent;
import java.io.IOException;
import java.util.Map;

/**
 * 回传处理器接口
 * 用于处理不同广告平台的回传逻辑
 */
public interface CallbackHandler {

    /**
     * 获取处理器对应的广告平台
     * @return 广告平台标识
     */
    String getPlatform();

    /**
     * 构建回传URL
     * @param strategy 回传策略
     * @param event 回传事件
     * @param eventData 事件数据
     * @return 回传URL
     */
    String buildCallbackUrl(AdRetargetingStrategy strategy, AdRetargetingEvent event, RetargetingEventData eventData);

    /**
     * 构建回传参数
     * @param strategy 回传策略
     * @param event 回传事件
     * @param eventData 事件数据
     * @return 回传参数(JSON格式)
     */
    String buildCallbackParams(AdRetargetingStrategy strategy, AdRetargetingEvent event, RetargetingEventData eventData);

    /**
     * 执行回传请求
     * @param url 回传URL
     * @param params 回传参数
     * @param method 请求方法(GET/POST)
     * @return 响应结果
     * @throws IOException 请求异常
     */
    String executeCallback(String url, String params, String method) throws IOException;
}
