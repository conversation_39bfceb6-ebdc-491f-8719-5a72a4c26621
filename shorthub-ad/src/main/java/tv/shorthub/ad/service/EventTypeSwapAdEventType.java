package tv.shorthub.ad.service;

import tv.shorthub.ad.config.EventTypeEnum;
import tv.shorthub.ad.config.FacebookEventType;
import tv.shorthub.common.enums.AdPlatformEnum;

public class EventTypeSwapAdEventType {
    public static String convert(EventTypeEnum eventType, AdPlatformEnum platform) {
        switch (platform) {
            case Facebook -> {
                return convertFacebook(eventType).getEventName();
            }
            case Google -> {
                return eventType.getEventName();
            }
        }

        throw new RuntimeException(String.format("Not supported [%s]", platform.name()));
    }

    public static FacebookEventType convertFacebook(EventTypeEnum eventType) {
        switch (eventType) {
            case REGISTRY -> {
                return FacebookEventType.COMPLETE_REGISTRATION;
            }
            case CREATE_ORDER -> {
                return FacebookEventType.INITIATE_CHECKOUT;
            }
            case PURCHASE -> {
                return FacebookEventType.PURCHASE;
            }
            case SUBSCRIBE -> {
                return FacebookEventType.SUBSCRIBE;
            }
            case VIEW -> {
                return FacebookEventType.VIEW_CONTENT;
            }
        }
        throw new RuntimeException(String.format("Not supported [%s]", eventType.name()));
    }
}
