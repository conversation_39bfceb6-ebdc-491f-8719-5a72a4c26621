package tv.shorthub.ad.service;

import tv.shorthub.ad.domain.RetargetingEventData;
import tv.shorthub.system.domain.AdRetargetingEvent;
import tv.shorthub.system.domain.AdRetargetingLog;
import tv.shorthub.system.domain.AdRetargetingRule;
import tv.shorthub.system.domain.AdRetargetingStrategy;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.Map;

/**
 * 回传服务接口
 */
public interface RetargetingService {

    void processEvent(RetargetingEventData eventData);

    void processEvent(RetargetingEventData eventData, AdRetargetingStrategy strategy) throws IOException;

    AdRetargetingLog executeCallback(AdRetargetingStrategy strategy, AdRetargetingEvent event, RetargetingEventData eventData);

    boolean checkRule(AdRetargetingRule rule, AdRetargetingEvent event, RetargetingEventData eventData);

    AdRetargetingLog retryCallback(AdRetargetingLog adLog);
}
