package tv.shorthub.ad.service.impl;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.hc.client5.http.classic.methods.HttpGet;
import org.apache.hc.client5.http.classic.methods.HttpPost;
import org.apache.hc.client5.http.impl.classic.CloseableHttpClient;
import org.apache.hc.client5.http.impl.classic.HttpClients;
import org.apache.hc.core5.http.ContentType;
import org.apache.hc.core5.http.io.entity.StringEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tv.shorthub.ad.callback.CallbackHandler;
import tv.shorthub.ad.domain.RetargetingEventData;
import tv.shorthub.ad.rule.FrequencyRule;
import tv.shorthub.ad.rule.RetargetingRuleFactory;
import tv.shorthub.ad.service.EventTypeSwapAdEventType;
import tv.shorthub.ad.service.RetargetingService;
import tv.shorthub.system.utils.AttributionCacheHolder;
import tv.shorthub.common.core.cache.CacheKeyUtils;
import tv.shorthub.common.core.cache.CacheService;
import tv.shorthub.common.enums.AdPlatformEnum;
import tv.shorthub.common.utils.DateUtils;
import tv.shorthub.system.domain.*;
import tv.shorthub.system.service.IAdRetargetingEventService;
import tv.shorthub.system.service.IAdRetargetingLogService;
import tv.shorthub.system.service.IAdRetargetingRuleService;
import tv.shorthub.system.service.IAdRetargetingStrategyService;

import java.io.IOException;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * 回传服务实现类
 */
@Slf4j
@Service
public class RetargetingServiceImpl implements RetargetingService {

    @Autowired
    private IAdRetargetingStrategyService strategyService;

    @Autowired
    private IAdRetargetingEventService eventService;

    @Autowired
    private IAdRetargetingLogService logService;

    @Autowired
    private IAdRetargetingRuleService ruleService;

    @Autowired
    private CacheService cacheService;

    @Autowired
    private AttributionCacheHolder attributionCacheHolder;

    @Autowired
    private List<CallbackHandler> callbackHandlers;

    private final FrequencyRule frequencyRule;

    // 缓存最近的回传记录，用于频率控制
    private final Cache<String, List<AdRetargetingLog>> recentLogsCache = Caffeine.newBuilder()
            .maximumSize(10000)
            .expireAfterWrite(24, TimeUnit.HOURS)
            .build();

    // 缓存冷却时间记录
    private final Cache<String, LocalDateTime> cooldownCache = Caffeine.newBuilder()
            .maximumSize(10000)
            .expireAfterWrite(24, TimeUnit.HOURS)
            .build();

    public RetargetingServiceImpl(RetargetingRuleFactory ruleFactory, FrequencyRule frequencyRule) {
        this.frequencyRule = frequencyRule;
    }

    @Override
    public void processEvent(RetargetingEventData eventData) {
        try {
            // 1. 查询推广链接配置
            AppPromotion promotion = cacheService.getCacheObject(CacheKeyUtils.getAppPromotion(eventData.getTfid()));
            if (null == promotion) {
                log.info("App promotion not found: {}", eventData.getTfid());
                return;
            } else if (null == promotion.getStrategyId()) {
                log.info("Strategy not found: {}", eventData.getTfid());
                return;
            }

            long strategyId = promotion.getStrategyId();

            // 2. 查询策略配置
            AdRetargetingStrategy strategy = cacheService.getCacheMapValue(CacheKeyUtils.MAP_AD_RETARGETING_STRATEGY, promotion.getStrategyId().toString());
            if (strategy == null || !strategy.getStatus()) {
                log.warn("Strategy not found or disabled: {}", strategyId);
                return;
            }

            // 3. 设置归因缓存
            if (null == eventData.getUserAttribution()) {
                eventData.setUserAttribution(
                        attributionCacheHolder.getAndVerifyAttribution(eventData.getDeviceId(), eventData.getClientIp(), eventData.getTfid())
                );
                log.info("获取到归因缓存: deviceId:{}, clientIp:{}, body:{}", eventData.getDeviceId(), eventData.getClientIp(), eventData.getUserAttribution());
            }

            processEvent(eventData, strategy);
        } catch (Exception e) {
            log.error("Failed to process event: {}", eventData.getEventType(), e);
        }
    }

    public boolean checkRules(RetargetingEventData eventData, AdRetargetingEvent event, AdRetargetingStrategy strategy) {
        // 判断当前用户是不是投放用户
        if (strategy.getAllowNotUser() == false && StringUtils.isEmpty(eventData.getTfid())) {
            log.info("Not a delivery user, terminate the return, eventType={}, eventData={}", eventData.getEventType(), JSONObject.toJSONString(eventData));
            return false;
        }

        // 1. 获取事件关联的规则列表
        List<AdRetargetingRule> rules = ruleService.selectList(new AdRetargetingRule() {{
            setEventId(event.getId());
            setStatus(true);
        }});

        if (CollectionUtils.isEmpty(rules)) {
            log.info("No rules found for event: {}", event.getId());
            return true;
        }

        // 3. 检查规则是否满足
        for (AdRetargetingRule rule : rules) {
            if (!checkRule(rule, event, eventData)) {
                log.info("Rule not matched: {}", rule.getId());
                // 4. 终止回传
                return false;
            }
        }

        return true;

    }

    @Override
    public void processEvent(RetargetingEventData eventData, AdRetargetingStrategy strategy) throws IOException {
        if (StringUtils.isNotEmpty(strategy.getCallbackParams())) {
            strategy.getParams().put("callbackParams", JSONObject.parseObject(strategy.getCallbackParams()));
        }

        // 把类型转换为平台事件值
        eventData.setEventValue(
                EventTypeSwapAdEventType.convert(eventData.getEventType(), AdPlatformEnum.ofValue(strategy.getPlatform()))
        );

        // 2. 查询关联的事件
        AdRetargetingEvent event = eventService.getMapper().selectOne(new QueryWrapper<>(
                new AdRetargetingEvent() {{
                    setStrategyId(strategy.getId());
                    setEventType(eventData.getEventValue());
                    setStatus(true);
                }}
        ));

        if (event == null) {
            log.warn("Event type not found: {}", eventData.getEventType());
            return;
        }


        // 2. 事件规则校验
        if (!checkRules(eventData, event, strategy)) {
            // 规则校验不通过，不执行回传
            log.info("Event type not matched: {}", eventData.getEventType());
            return;
        }



        // 3. 获取对应的处理器
        CallbackHandler handler = callbackHandlers.stream()
                .filter(h -> h.getPlatform().equals(strategy.getPlatform()))
                .findFirst()
                .orElse(null);

        if (handler == null) {
            log.warn("Handler not found for platform: {}", strategy.getPlatform());
            return;
        }

        String callbackUrl = "";
        String callbackParams = "";
        String callbackResponse;
        boolean callbackStatus = true;

        try {
            // 4. 构建回传数据
            callbackUrl = handler.buildCallbackUrl(strategy, event, eventData);
            callbackParams = handler.buildCallbackParams(strategy, event, eventData);

            // 5. 执行回传
            callbackResponse = handler.executeCallback(callbackUrl, callbackParams, strategy.getCallbackMethod());
        } catch (Exception e) {
            log.error("Callback execution failed: {}", e.getMessage());
            callbackResponse = e.getMessage();
            callbackStatus = false;
        }

        // 6. 记录回传日志
        AdRetargetingLog adLog = new AdRetargetingLog();
        adLog.setStrategyId(strategy.getId());
        adLog.setEventId(event.getId());
        adLog.setDeviceId(eventData.getDeviceId());
        adLog.setRequestId(IdUtil.getSnowflakeNextIdStr());
        adLog.setRelationId(eventData.getRelationId());
        adLog.setUserId(eventData.getUserId());
        adLog.setEventData(JSON.toJSONString(eventData));
        adLog.setCallbackData(JSON.toJSONString(Map.of(
                "url", callbackUrl,
                "params", callbackParams,
                "response", callbackResponse
            )));
        adLog.setCallbackStatus(callbackStatus);
        adLog.setRetryCount(0L);
        logService.insert(adLog);

        log.info("Callback executed, requestId: {}", adLog.getRequestId());

    }

    @Override
    public AdRetargetingLog executeCallback(AdRetargetingStrategy strategy, AdRetargetingEvent event, RetargetingEventData eventData) {
        AdRetargetingLog adLog = new AdRetargetingLog();
        adLog.setStrategyId(strategy.getId());
        adLog.setEventId(event.getId());
        adLog.setRequestId(UUID.randomUUID().toString());
        adLog.setUserId(eventData.getUserId());
        adLog.setRelationId(eventData.getRelationId());
        adLog.setEventData(JSON.toJSONString(eventData));
        adLog.setRetryCount(0L);
        adLog.setCallbackStatus(false);

        try {
            // 1. 构建回传参数
            String callbackParams = buildCallbackParams(strategy, event, eventData);
            adLog.setCallbackData(callbackParams);

            // 2. 执行HTTP请求
            String response = executeHttpRequest(strategy, callbackParams);
            adLog.setResponseData(response);

            // 3. 更新回传状态
            adLog.setCallbackStatus(true);
            adLog.setCreateTime(DateUtils.getNowDate());
            logService.insert(adLog);

            // 4. 更新缓存
            updateRecentLogsCache(event.getId(), adLog);

            // 5. 更新频次规则缓存
            frequencyRule.updateCache(eventData.getUserId(), eventData.getDeviceId(), adLog);

            return adLog;
        } catch (Exception e) {
            log.error("Callback execution failed: {}", e.getMessage());
            adLog.setErrorMessage(e.getMessage());
            adLog.setCreateTime(DateUtils.getNowDate());
            logService.insert(adLog);

            // 6. 如果配置了重试，则加入重试队列
            if (strategy.getRetryTimes() > 0) {
                scheduleRetry(adLog, strategy.getRetryInterval());
            }

            return adLog;
        }
    }

    @Override
    public boolean checkRule(AdRetargetingRule rule, AdRetargetingEvent event, RetargetingEventData eventData) {
        try {
            JSONObject ruleParams = JSON.parseObject(rule.getRuleParams());
            Long ruleType = rule.getRuleType();

            return switch (ruleType.intValue()) {
                case 1 -> // 时间规则
                        checkTimeRule(ruleParams, eventData);
                case 2 -> // 条件规则
                        checkConditionRule(ruleParams, eventData);
                case 3 -> // 频率规则
                        checkFrequencyRule(ruleParams, event, eventData);
                default -> true;
            };
        } catch (Exception e) {
            log.error("Rule check failed: {}", e.getMessage());
            return false;
        }
    }

    @Override
    public AdRetargetingLog retryCallback(AdRetargetingLog adLog) {
        if (adLog.getRetryCount() >= 3) {
            return adLog;
        }

        try {
            // 1. 获取策略和事件信息
            AdRetargetingStrategy strategy = strategyService.getById(adLog.getStrategyId());
            AdRetargetingEvent event = new AdRetargetingEvent();
            event.setId(adLog.getEventId());

            // 2. 解析事件数据
            RetargetingEventData eventData = JSON.parseObject(adLog.getEventData(), RetargetingEventData.class);

            // 3. 重新执行回传
            adLog.setRetryCount(adLog.getRetryCount() + 1);
            return executeCallback(strategy, event, eventData);
        } catch (Exception e) {
            log.error("Retry callback failed: {}", e.getMessage());
            return adLog;
        }
    }

    /**
     * 检查时间规则
     */
    private boolean checkTimeRule(JSONObject ruleParams, RetargetingEventData eventData) {
        try {
            JSONObject effectiveTime = ruleParams.getJSONObject("effectiveTime");
            String timezone = effectiveTime.getString("timezone");
            ZoneId zoneId = ZoneId.of(timezone);
            LocalDateTime now = LocalDateTime.now(zoneId);

            // 1. 检查生效时间范围
            String startDate = effectiveTime.getString("startDate");
            String endDate = effectiveTime.getString("endDate");
            if (StringUtils.isNotEmpty(startDate) && StringUtils.isNotEmpty(endDate)) {
                LocalDateTime start = LocalDateTime.parse(startDate + " 00:00:00",
                    DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                LocalDateTime end = LocalDateTime.parse(endDate + " 23:59:59",
                    DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                if (now.isBefore(start) || now.isAfter(end)) {
                    log.info("Outside time window for user: {}, device: {}, now: {}, start: {}, end: {}",
                        eventData.getUserId(), eventData.getDeviceId(), now, start, end);
                    return false;
                }
            }

            // 2. 检查星期几
            JSONObject weeklySchedule = ruleParams.getJSONObject("weeklySchedule");
            String dayOfWeek = now.getDayOfWeek().toString().toLowerCase();
            if (!weeklySchedule.getBoolean(dayOfWeek)) {
                log.info("Not allowed on {} for user: {}, device: {}, weeklySchedule: {}",
                    dayOfWeek, eventData.getUserId(), eventData.getDeviceId(), weeklySchedule);
                return false;
            }

            // 3. 检查时间段
            LocalTime currentTime = now.toLocalTime();
            List<JSONObject> dailySchedule = ruleParams.getJSONArray("dailySchedule").toList(JSONObject.class);
            boolean isInTimeWindow = false;
            for (JSONObject schedule : dailySchedule) {
                String startTimeStr = schedule.getString("startTime");
                String endTimeStr = schedule.getString("endTime");
                LocalTime startTime = LocalTime.parse(startTimeStr);
                LocalTime endTime = LocalTime.parse(endTimeStr);

                if (currentTime.isAfter(startTime) && currentTime.isBefore(endTime) ||
                    currentTime.equals(startTime) || currentTime.equals(endTime)) {
                    isInTimeWindow = true;
                    break;
                }
            }
            if (!isInTimeWindow) {
                log.info("Outside daily schedule time window, current time: {}, user: {}, device: {}, dailySchedule: {}",
                    currentTime, eventData.getUserId(), eventData.getDeviceId(), dailySchedule);
                return false;
            }

            return true;
        } catch (Exception e) {
            log.error("Time rule check failed: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 检查条件规则
     */
    private boolean checkConditionRule(JSONObject ruleParams, RetargetingEventData eventData) {
        try {
            // 1. 检查用户分群
//            JSONObject userConditions = ruleParams.getJSONObject("userConditions");
//            List<String> segments = userConditions.getJSONArray("segments").toList(String.class);
//            if (!segments.isEmpty()) {
//                String userSegment = String.valueOf(eventData.get("user_segment"));
//                if (!segments.contains(userSegment)) {
//                    return false;
//                }
//            }
//
//            // 2. 检查用户价值
//            JSONObject valueRange = userConditions.getJSONObject("valueRange");
//            if (valueRange != null) {
//                double userValue = Double.parseDouble(String.valueOf(eventData.get("user_value")));
//                double minValue = valueRange.getDoubleValue("min");
//                double maxValue = valueRange.getDoubleValue("max");
//                if (userValue < minValue || userValue > maxValue) {
//                    return false;
//                }
//            }
//
//            // 3. 检查设备类型
//            JSONObject deviceConditions = ruleParams.getJSONObject("deviceConditions");
//            List<String> deviceTypes = deviceConditions.getJSONArray("types").toList(String.class);
//            if (!deviceTypes.isEmpty()) {
//                String deviceType = String.valueOf(eventData.get("device_type"));
//                if (!deviceTypes.contains(deviceType)) {
//                    return false;
//                }
//            }
//
//            // 4. 检查操作系统
//            List<String> operatingSystems = deviceConditions.getJSONArray("operatingSystems").toList(String.class);
//            if (!operatingSystems.isEmpty()) {
//                String os = String.valueOf(eventData.get("os"));
//                if (!operatingSystems.contains(os)) {
//                    return false;
//                }
//            }
//
//            // 5. 检查地理位置
//            JSONObject locationConditions = ruleParams.getJSONObject("locationConditions");
//            List<String> countries = locationConditions.getJSONArray("countries").toList(String.class);
//            if (!countries.isEmpty()) {
//                String country = String.valueOf(eventData.get("country"));
//                if (!countries.contains(country)) {
//                    return false;
//                }
//            }

            return true;
        } catch (Exception e) {
            log.error("Condition rule check failed: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 检查频率规则
     */
    private boolean checkFrequencyRule(JSONObject ruleParams, AdRetargetingEvent event, RetargetingEventData eventData) {
        try {
            String frequencyType = ruleParams.getString("frequencyType");
            String userId = eventData.getUserId();

            if (StringUtils.isEmpty(userId)) {
                log.warn("User ID is empty, skipping frequency check");
                return false;
            }

            // 检查时间窗口
            JSONObject timeWindow = ruleParams.getJSONObject("timeWindow");
            String startTime = timeWindow.getString("start");
            String endTime = timeWindow.getString("end");
            String currentTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("HH:mm:ss"));
            if (currentTime.compareTo(startTime) < 0 || currentTime.compareTo(endTime) > 0) {
                log.info("Outside time window for user: {}", userId);
                return false;
            }

            // 按时间 & 混合模式
            if ("time".equals(frequencyType) || "hybrid".equals(frequencyType)) {
                // 获取最近一次成功的回传记录
                AdRetargetingLog lastLog = logService.getLatestSuccessfulLog(userId, event.getId());

                // 使用之前查询的lastLog
                if (lastLog != null) {
                    JSONObject cooldown = ruleParams.getJSONObject("cooldown");
                    long cooldownPeriod = cooldown.getLongValue("period");
                    String cooldownUnit = cooldown.getString("unit");
                    if (cooldownUnit == null) {
                        cooldownUnit = "seconds";
                    }

                    // 将冷却时间转换为秒
                    long cooldownSeconds = switch (cooldownUnit.toLowerCase()) {
                        case "minutes" -> cooldownPeriod * 60;
                        case "hours" -> cooldownPeriod * 3600;
                        case "days" -> cooldownPeriod * 86400;
                        default -> cooldownPeriod; // seconds
                    };

                    // 检查冷却时间
                    Duration duration = Duration.between(
                            lastLog.getCreateTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime(),
                            LocalDateTime.now()
                    );
                    if (duration.getSeconds() < cooldownSeconds) {
                        log.info("In cooldown period for user: {}, remaining {} seconds", userId, cooldownSeconds - duration.getSeconds());
                        return false;
                    }
                }
            }

            // 按次数 & 混合模式
            if ("count".equals(frequencyType) || "hybrid".equals(frequencyType)) {
                JSONObject countBased = ruleParams.getJSONObject("countBased");
                int maxCount = countBased.getIntValue("maxCount");

                String deduplicationType = ruleParams.getString("deduplicationType");
                long count;
                String queryRelationId = eventData.getRelationId();
                // 默认都要按关联事件去重, 同一个策略中的关联事件只允许回传一次
                count = logService.countSuccessfulLogsByRelationId(event.getId(), queryRelationId);
                // 按用户去重
                if (count == 0 && ("userId".equals(deduplicationType) || null == deduplicationType)) {
                    count = logService.countSuccessfulLogsByUserId(event.getId(), userId);
                }

                // 查询成功回传记录数
                if (count >= maxCount) {
                    log.info("Count-based frequency limit reached for user: {}, eventId: {}, relationId: {}, deduplicationType: {}, count: {}", userId, event.getId(), queryRelationId, deduplicationType, count);
                    return false;
                }
            }

            return true;
        } catch (Exception e) {
            log.error("Frequency rule check failed for user: {}, error: {}", eventData.getUserId(), e.getMessage());
            return false;
        }
    }

    /**
     * 构建回传参数
     */
    private String buildCallbackParams(AdRetargetingStrategy strategy, AdRetargetingEvent event, RetargetingEventData eventData) {
        try {
            JSONObject template = JSON.parseObject(strategy.getCallbackParams());
            JSONObject params = new JSONObject();

            // 替换模板中的变量
            for (String key : template.keySet()) {
                String value = template.getString(key);
                {
                    params.put(key, value);
                }
            }

            return params.toJSONString();
        } catch (Exception e) {
            log.error("Build callback params failed: {}", e.getMessage());
            throw new RuntimeException("Build callback params failed: " + e.getMessage());
        }
    }

    /**
     * 执行HTTP请求
     */
    private String executeHttpRequest(AdRetargetingStrategy strategy, String params) throws IOException {
        try (CloseableHttpClient client = HttpClients.createDefault()) {
            if ("GET".equalsIgnoreCase(strategy.getCallbackMethod())) {
                HttpGet request = new HttpGet(strategy.getCallbackUrl() + "?" + params);
                return client.execute(request, response -> {
                    int statusCode = response.getCode();
                    if (statusCode >= 200 && statusCode < 300) {
                        return "success";
                    } else {
                        throw new IOException("HTTP request failed with status code: " + statusCode);
                    }
                });
            } else {
                HttpPost request = new HttpPost(strategy.getCallbackUrl());
                request.setEntity(new StringEntity(params, ContentType.APPLICATION_JSON));
                return client.execute(request, response -> {
                    int statusCode = response.getCode();
                    if (statusCode >= 200 && statusCode < 300) {
                        return "success";
                    } else {
                        throw new IOException("HTTP request failed with status code: " + statusCode);
                    }
                });
            }
        }
    }

    /**
     * 更新最近回传记录缓存
     */
    private void updateRecentLogsCache(Long eventId, AdRetargetingLog log) {
        String cacheKey = log.getUserId() + ":" + log.getDeviceId();
        List<AdRetargetingLog> logs = recentLogsCache.getIfPresent(cacheKey);
        if (logs == null) {
            logs = List.of(log);
        } else {
            logs.add(log);
            // 只保留最近100条记录
            if (logs.size() > 100) {
                logs = logs.subList(logs.size() - 100, logs.size());
            }
        }
        recentLogsCache.put(cacheKey, logs);
    }

    /**
     * 调度重试
     */
    private void scheduleRetry(AdRetargetingLog adLog, Long retryInterval) {
        // TODO: 实现重试调度逻辑，可以使用定时任务或消息队列
        // 这里简单实现，实际项目中建议使用消息队列
        new Thread(() -> {
            try {
                Thread.sleep(retryInterval * 1000);
                retryCallback(adLog);
            } catch (InterruptedException e) {
                log.error("Retry schedule interrupted: {}", e.getMessage());
            }
        }).start();
    }
}
