package tv.shorthub.ad.service;

import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tv.shorthub.ad.domain.FacebookAttributionData;
import tv.shorthub.system.domain.AppPromotionRequestLog;
import tv.shorthub.system.service.IAppPromotionRequestLogService;

import java.util.List;

/**
 * Facebook归因服务
 * 处理Facebook广告点击归因和转化事件
 */
@Service
@Slf4j
public class FacebookAttributionService {

    @Autowired
    private IAppPromotionRequestLogService appPromotionRequestLogService;

//    @Autowired
//    private RetargetingService retargetingService;

    /** Facebook归因窗口期（默认7天） */
    private static final long ATTRIBUTION_WINDOW_DAYS = 7;

    /** Facebook归因窗口期（毫秒） */
    private static final long ATTRIBUTION_WINDOW_MS = ATTRIBUTION_WINDOW_DAYS * 24 * 60 * 60 * 1000;

    /**
     * 查找用户的Facebook归因数据
     * 按优先级查找：1. 当前session 2. 同IP最近的归因 3. 同设备ID的归因
     */
    public FacebookAttributionData findUserAttribution(String sessionId, String clientIp, String deviceId, String tfid) {
        long time = System.currentTimeMillis();
        try {
            // 1. 首先尝试从当前session查找
            FacebookAttributionData sessionAttribution = findAttributionBySession(sessionId);
            if (isValidAttribution(sessionAttribution)) {
                log.info("Found Facebook attribution by session: {}, cost: {}ms", sessionAttribution, System.currentTimeMillis() - time);
                return sessionAttribution;
            }

            // 2. 按DeviceId查找（作为设备指纹）
            FacebookAttributionData deviceAttribution = findAttributionByDeviceId(deviceId, tfid);
            if (isValidAttribution(deviceAttribution)) {
                log.info("Found Facebook attribution by DeviceId: sid:{}, {}, cost: {}ms", sessionId, deviceAttribution, System.currentTimeMillis() - time);
                return deviceAttribution;
            }

            // 3. 按IP查找最近的归因（7天内）
            FacebookAttributionData ipAttribution = findAttributionByIp(clientIp, tfid);
            if (isValidAttribution(ipAttribution)) {
                log.info("Found Facebook attribution by IP: {}, cost: {}ms", ipAttribution, System.currentTimeMillis() - time);
                return ipAttribution;
            }

            log.info("No valid Facebook attribution found for session: {}, IP: {}, cost: {}ms", sessionId, clientIp, System.currentTimeMillis() - time);
            return null;

        } catch (Exception e) {
            log.error("Error finding Facebook attribution for session: {}, IP: {}, cost: {}ms", sessionId, clientIp, System.currentTimeMillis() - time, e);
            return null;
        }
    }

    /**
     * 通过session ID查找归因数据
     */
    private FacebookAttributionData findAttributionBySession(String sessionId) {
        // 实现查询逻辑 - 这里需要根据你的数据库设计来实现
        // 示例实现：
        AppPromotionRequestLog requestLog = appPromotionRequestLogService
            .findLatestBySessionId(sessionId);

        if (requestLog != null && requestLog.getReqParams() != null) {
            return extractAttributionFromLog(requestLog);
        }

        return null;
    }

    /**
     * 通过IP查找归因数据
     */
    private FacebookAttributionData findAttributionByIp(String clientIp, String tfid) {
        long cutoffTime = System.currentTimeMillis() - ATTRIBUTION_WINDOW_MS;

        List<AppPromotionRequestLog> logs = appPromotionRequestLogService
            .findByIpAndTimeRange(clientIp, tfid, cutoffTime);

        for (AppPromotionRequestLog log : logs) {
            FacebookAttributionData attribution = extractAttributionFromLog(log);
            if (attribution != null && isValidAttribution(attribution)) {
                return attribution;
            }
        }

        return null;
    }

    /**
     * 通过DeviceId查找归因数据
     */
    private FacebookAttributionData findAttributionByDeviceId(String deviceId, String tfid) {
        AppPromotionRequestLog log = appPromotionRequestLogService.findByDeviceId(deviceId, tfid);
        FacebookAttributionData attribution = extractAttributionFromLog(log);
        if (isValidAttribution(attribution)) {
            return attribution;
        }

        return null;
    }

    /**
     * 从日志中提取归因数据
     */
    private FacebookAttributionData extractAttributionFromLog(AppPromotionRequestLog log) {
        if (log == null || log.getReqParams() == null) {
            return null;
        }

        JSONObject params = log.getReqParams();
        String fbc = params.getString("fbc");
        String fbp = params.getString("fbp");
        String fbclid = params.getString("fbclid");
        Boolean isAdTraffic = params.getBoolean("isAdTraffic");
        Long attributionTimestamp = params.getLong("attributionTimestamp");

        if (fbc != null || fbp != null || (isAdTraffic != null && isAdTraffic)) {
            FacebookAttributionData attribution = new FacebookAttributionData();
            attribution.setFbc(fbc);
            attribution.setFbp(fbp);
            attribution.setFbclid(fbclid);
            attribution.setTfid(log.getTid());
            attribution.setSessionId(log.getSid());
            attribution.setClientIp(log.getIp());
            attribution.setAttributionTimestamp(attributionTimestamp != null ? attributionTimestamp :
                log.getCreateTime().getTime());
            attribution.setAdTraffic(isAdTraffic != null ? isAdTraffic : false);

            return attribution;
        }

        return null;
    }

    /**
     * 验证归因数据是否有效
     */
    private boolean isValidAttribution(FacebookAttributionData attribution) {
        if (attribution == null) {
            return false;
        }

        // 检查时间窗口
        long timeDiff = System.currentTimeMillis() - attribution.getAttributionTimestamp();
        if (timeDiff > ATTRIBUTION_WINDOW_MS) {
            log.debug("Attribution expired: {} ms ago", timeDiff);
            return false;
        }

        // 必须有Facebook相关参数
        return attribution.getFbc() != null || attribution.getFbp() != null || attribution.isAdTraffic();
    }

//    /**
//     * 发送Facebook转化事件
//     */
//    public void sendConversionEvent(String eventType, FacebookAttributionData attribution,
//                                   String email, String phone, BigDecimal amount, String currency,
//                                   String orderId) {
//        try {
//            RetargetingEventData eventData;
//
//            // 根据事件类型创建相应的事件数据
//            if ("Purchase".equals(eventType)) {
//                eventData = RetargetingEventDataPayment.builder()
//                    .orderId(orderId)
//                    .amount(amount)
//                    .currency(currency != null ? currency : "USD")
//                    .build();
//            } else if ("InitiateCheckout".equals(eventType)) {
//                eventData = RetargetingEventDataOrderCreate.builder()
//                    .orderId(orderId)
//                    .amount(amount)
//                    .currency(currency != null ? currency : "USD")
//                    .build();
//            } else {
//                // 其他事件类型，使用基础事件数据
//                eventData = RetargetingEventDataOrderCreate.builder().build();
//            }
//
//            // 设置公共数据
//            eventData.setTfid(attribution.getTfid());
//            eventData.setSid(attribution.getSessionId());
//            eventData.setClientIp(attribution.getClientIp());
//            eventData.setEmail(email);
//            eventData.setPhone(phone);
//            eventData.setActionSource("website");
//            eventData.setEventTime(System.currentTimeMillis() / 1000);
//
//            // 发送事件到重定向服务
////            retargetingService.processEvent(eventData);
//
//            log.info("Facebook conversion event sent - Type: {}, tfid: {}, fbc: {}, fbp: {}, amount: {}",
//                eventType, attribution.getTfid(), attribution.getFbc(), attribution.getFbp(), amount);
//
//        } catch (Exception e) {
//            log.error("Failed to send Facebook conversion event - Type: {}, tfid: {}",
//                eventType, attribution.getTfid(), e);
//        }
//    }
}
