package tv.shorthub.ad.domain;

import com.alibaba.fastjson2.JSONObject;
import lombok.Data;
import lombok.experimental.SuperBuilder;
import tv.shorthub.ad.config.EventTypeEnum;

@SuperBuilder
@Data
public abstract class RetargetingEventData {
    private String clientIp;
    private String clientUserAgent;
    private String deviceId;
    private String userId;
    private String email;
    private String phone;
    private String tfid;
    private String sid;
    private String eventValue;
    private String actionSource;
    private String relationId;
    private Long eventTime;

    private JSONObject userAttribution;

    public void setUserAttribution(JSONObject userAttribution) {
        this.userAttribution = userAttribution;
    }

    public JSONObject getUserAttribution() {
        return userAttribution;
    }

    // Add default constructor for FastJSON2
    public RetargetingEventData() {
    }

    public abstract EventTypeEnum getEventType();
}
