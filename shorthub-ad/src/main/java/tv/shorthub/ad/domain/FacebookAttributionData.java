package tv.shorthub.ad.domain;

import lombok.Data;

/**
 * Facebook归因数据模型
 */
@Data
public class FacebookAttributionData {
    private String fbc;
    private String fbp;
    private String fbclid;
    private String tfid;
    private String sessionId;
    private String clientIp;
    private long attributionTimestamp;
    private boolean isAdTraffic;

    @Override
    public String toString() {
        return String.format("FacebookAttributionData{fbc='%s', fbp='%s', tfid='%s', isAdTraffic=%s}", 
            fbc, fbp, tfid, isAdTraffic);
    }
}
