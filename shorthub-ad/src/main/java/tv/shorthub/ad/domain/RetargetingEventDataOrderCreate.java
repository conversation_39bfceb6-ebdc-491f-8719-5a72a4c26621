package tv.shorthub.ad.domain;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.experimental.SuperBuilder;
import tv.shorthub.ad.config.EventTypeEnum;

import java.math.BigDecimal;

@SuperBuilder
@EqualsAndHashCode(callSuper = true)
@Data
public class RetargetingEventDataOrderCreate extends RetargetingEventData {
    private String orderId;
    private BigDecimal amount;
    private String currency;

    // Add default constructor for FastJSON2
    public RetargetingEventDataOrderCreate() {
        super();
    }

    @Override
    public EventTypeEnum getEventType() {
        return EventTypeEnum.CREATE_ORDER;
    }
}
