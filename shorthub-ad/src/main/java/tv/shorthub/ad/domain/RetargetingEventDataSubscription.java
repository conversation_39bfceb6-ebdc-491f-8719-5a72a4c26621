package tv.shorthub.ad.domain;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;
import tv.shorthub.ad.config.EventTypeEnum;

import java.math.BigDecimal;

@SuperBuilder
@EqualsAndHashCode(callSuper = true)
@Data
public class RetargetingEventDataSubscription extends RetargetingEventData {
    private String orderId;
    private BigDecimal amount;
    private String currency;
    private String paymentMethod;

    // Add default constructor for FastJSON2
    public RetargetingEventDataSubscription() {
        super();
    }

    @Override
    public EventTypeEnum getEventType() {
        return EventTypeEnum.SUBSCRIBE;
    }
}
