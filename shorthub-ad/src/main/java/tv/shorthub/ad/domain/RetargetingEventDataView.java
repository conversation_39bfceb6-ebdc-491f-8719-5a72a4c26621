package tv.shorthub.ad.domain;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;
import tv.shorthub.ad.config.EventTypeEnum;

@SuperBuilder
@EqualsAndHashCode(callSuper = true)
@Data
public class RetargetingEventDataView extends RetargetingEventData {
    private String contentId;
    private Long serialNumber;
    // 是否付费集
    private Boolean fee;

    // Add default constructor for FastJSON2
    public RetargetingEventDataView() {
        super();
    }

    @Override
    public EventTypeEnum getEventType() {
        return EventTypeEnum.VIEW;
    }
}
