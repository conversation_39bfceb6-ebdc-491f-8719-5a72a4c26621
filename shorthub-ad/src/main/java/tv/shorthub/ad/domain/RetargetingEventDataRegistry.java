package tv.shorthub.ad.domain;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.experimental.SuperBuilder;
import tv.shorthub.ad.config.EventTypeEnum;

@SuperBuilder
@EqualsAndHashCode(callSuper = true)
@Data
public class RetargetingEventDataRegistry extends RetargetingEventData {
    private String email;
    private String provider;

    // Add default constructor for FastJSON2
    public RetargetingEventDataRegistry() {
        super();
    }

    @Override
    public EventTypeEnum getEventType() {
        return EventTypeEnum.REGISTRY;
    }
}
