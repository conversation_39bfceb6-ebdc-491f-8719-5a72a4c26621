package tv.shorthub.ad.rule;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import tv.shorthub.system.domain.AdRetargetingRule;
import java.util.Map;

/**
 * 回传规则抽象基类
 */
@Slf4j
public abstract class AbstractRetargetingRule implements RetargetingRule {

    @Override
    public boolean check(AdRetargetingRule rule, Map<String, Object> eventData) {
        try {
            JSONObject ruleParams = JSON.parseObject(rule.getRuleParams());
            return doCheck(ruleParams, eventData);
        } catch (Exception e) {
            log.error("规则检查失败", e);
            return false;
        }
    }

    /**
     * 执行具体的规则检查
     *
     * @param ruleParams 规则参数
     * @param eventData 事件数据
     * @return 是否满足规则
     */
    protected abstract boolean doCheck(JSONObject ruleParams, Map<String, Object> eventData);
} 