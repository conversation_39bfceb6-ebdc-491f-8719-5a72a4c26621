package tv.shorthub.ad.rule;

import tv.shorthub.system.domain.AdRetargetingRule;
import java.util.Map;

/**
 * 回传规则接口
 */
public interface RetargetingRule {
    
    /**
     * 检查规则是否满足
     *
     * @param rule 回传规则
     * @param eventData 事件数据
     * @return 是否满足规则
     */
    boolean check(AdRetargetingRule rule, Map<String, Object> eventData);

    /**
     * 获取规则类型
     *
     * @return 规则类型
     */
    Long getRuleType();
} 