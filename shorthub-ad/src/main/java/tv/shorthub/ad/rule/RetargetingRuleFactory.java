package tv.shorthub.ad.rule;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import tv.shorthub.system.domain.AdRetargetingRule;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 回传规则工厂
 */
@Component
@RequiredArgsConstructor
public class RetargetingRuleFactory {

    private final List<RetargetingRule> rules;

    /**
     * 获取规则实现
     *
     * @param ruleType 规则类型
     * @return 规则实现
     */
    public Optional<RetargetingRule> getRule(Long ruleType) {
        return rules.stream()
                .filter(rule -> rule.getRuleType().equals(ruleType))
                .findFirst();
    }

    /**
     * 检查规则是否满足
     *
     * @param rule 回传规则
     * @param eventData 事件数据
     * @return 是否满足规则
     */
    public boolean checkRule(AdRetargetingRule rule, Map<String, Object> eventData) {
        return getRule(rule.getRuleType())
                .map(ruleImpl -> ruleImpl.check(rule, eventData))
                .orElse(false);
    }
} 