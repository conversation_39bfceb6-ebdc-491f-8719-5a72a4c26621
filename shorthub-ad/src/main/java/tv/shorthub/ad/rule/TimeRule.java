package tv.shorthub.ad.rule;

import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

/**
 * 时间规则实现
 */
@Slf4j
@Component
public class TimeRule extends AbstractRetargetingRule {

    @Override
    public Long getRuleType() {
        return 1L;
    }

    @Override
    protected boolean doCheck(JSONObject ruleParams, Map<String, Object> eventData) {
        try {
            JSONObject effectiveTime = ruleParams.getJSONObject("effectiveTime");
            String timezone = effectiveTime.getString("timezone");
            ZoneId zoneId = ZoneId.of(timezone);
            LocalDateTime now = LocalDateTime.now(zoneId);

            // 1. 检查生效时间范围
            String startDate = effectiveTime.getString("startDate");
            String endDate = effectiveTime.getString("endDate");
            if (StringUtils.isNotEmpty(startDate) && StringUtils.isNotEmpty(endDate)) {
                LocalDateTime start = LocalDateTime.parse(startDate + " 00:00:00",
                    DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                LocalDateTime end = LocalDateTime.parse(endDate + " 23:59:59",
                    DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                if (now.isBefore(start) || now.isAfter(end)) {
                    return false;
                }
            }

            // 2. 检查星期几
            JSONObject weeklySchedule = ruleParams.getJSONObject("weeklySchedule");
            String dayOfWeek = now.getDayOfWeek().toString().toLowerCase();
            if (!weeklySchedule.getBoolean(dayOfWeek)) {
                return false;
            }

            // 3. 检查时间段
            String currentTime = now.format(DateTimeFormatter.ofPattern("HH:mm:ss"));
            List<JSONObject> dailySchedule = ruleParams.getJSONArray("dailySchedule").toList(JSONObject.class);
            for (JSONObject schedule : dailySchedule) {
                String startTime = schedule.getString("startTime");
                String endTime = schedule.getString("endTime");
                if (currentTime.compareTo(startTime) >= 0 && currentTime.compareTo(endTime) <= 0) {
                    return true;
                }
            }
            return false;
        } catch (Exception e) {
            log.error("时间规则检查失败", e);
            return false;
        }
    }
} 