package tv.shorthub.ad.rule;

import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import java.util.List;
import java.util.Map;

/**
 * 条件规则实现
 */
@Slf4j
@Component
public class ConditionRule extends AbstractRetargetingRule {

    @Override
    public Long getRuleType() {
        return 2L;
    }

    @Override
    protected boolean doCheck(JSONObject ruleParams, Map<String, Object> eventData) {
        try {
            // 1. 检查用户分群
            JSONObject userConditions = ruleParams.getJSONObject("userConditions");
            List<String> segments = userConditions.getJSONArray("segments").toList(String.class);
            if (!segments.isEmpty()) {
                String userSegment = String.valueOf(eventData.get("user_segment"));
                if (!segments.contains(userSegment)) {
                    return false;
                }
            }

            // 2. 检查用户价值
            JSONObject valueRange = userConditions.getJSONObject("valueRange");
            if (valueRange != null) {
                double userValue = Double.parseDouble(String.valueOf(eventData.get("user_value")));
                double minValue = valueRange.getDoubleValue("min");
                double maxValue = valueRange.getDoubleValue("max");
                if (userValue < minValue || userValue > maxValue) {
                    return false;
                }
            }

            // 3. 检查设备类型
            JSONObject deviceConditions = ruleParams.getJSONObject("deviceConditions");
            List<String> deviceTypes = deviceConditions.getJSONArray("types").toList(String.class);
            if (!deviceTypes.isEmpty()) {
                String deviceType = String.valueOf(eventData.get("device_type"));
                if (!deviceTypes.contains(deviceType)) {
                    return false;
                }
            }

            // 4. 检查操作系统
            List<String> operatingSystems = deviceConditions.getJSONArray("operatingSystems").toList(String.class);
            if (!operatingSystems.isEmpty()) {
                String os = String.valueOf(eventData.get("os"));
                if (!operatingSystems.contains(os)) {
                    return false;
                }
            }

            // 5. 检查地理位置
            JSONObject locationConditions = ruleParams.getJSONObject("locationConditions");
            List<String> countries = locationConditions.getJSONArray("countries").toList(String.class);
            if (!countries.isEmpty()) {
                String country = String.valueOf(eventData.get("country"));
                if (!countries.contains(country)) {
                    return false;
                }
            }

            return true;
        } catch (Exception e) {
            log.error("条件规则检查失败", e);
            return false;
        }
    }
} 