package tv.shorthub.ad.rule;

import com.alibaba.fastjson2.JSONObject;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import tv.shorthub.system.domain.AdRetargetingLog;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 频次规则实现
 */
@Slf4j
@Component
public class FrequencyRule extends AbstractRetargetingRule {

    private final Cache<String, List<AdRetargetingLog>> recentLogsCache;
    private final Cache<String, LocalDateTime> cooldownCache;

    public FrequencyRule() {
        this.recentLogsCache = Caffeine.newBuilder()
                .maximumSize(10000)
                .expireAfterWrite(24, TimeUnit.HOURS)
                .build();
        this.cooldownCache = Caffeine.newBuilder()
                .maximumSize(10000)
                .expireAfterWrite(24, TimeUnit.HOURS)
                .build();
    }

    @Override
    public Long getRuleType() {
        return 3L;
    }

    @Override
    protected boolean doCheck(JSONObject ruleParams, Map<String, Object> eventData) {
        try {
            String userId = String.valueOf(eventData.get("user_id"));
            String deviceId = String.valueOf(eventData.get("device_id"));
            String cacheKey = userId + ":" + deviceId;

            // 1. 检查冷却时间
            LocalDateTime lastCallbackTime = cooldownCache.getIfPresent(cacheKey);
            if (lastCallbackTime != null) {
                int cooldownMinutes = ruleParams.getIntValue("cooldownMinutes");
                if (Duration.between(lastCallbackTime, LocalDateTime.now()).toMinutes() < cooldownMinutes) {
                    return false;
                }
            }

            // 2. 检查时间窗口内的回调次数
            List<AdRetargetingLog> recentLogs = recentLogsCache.getIfPresent(cacheKey);
            if (recentLogs != null && !recentLogs.isEmpty()) {
                int timeWindow = ruleParams.getIntValue("timeWindow");
                int maxCount = ruleParams.getIntValue("maxCount");
                LocalDateTime windowStart = LocalDateTime.now().minusMinutes(timeWindow);

                long count = recentLogs.stream()
                        .filter(log -> log.getCreateTime().toInstant()
                                .atZone(ZoneId.systemDefault())
                                .toLocalDateTime()
                                .isAfter(windowStart))
                        .count();

                if (count >= maxCount) {
                    return false;
                }
            }

            return true;
        } catch (Exception e) {
            log.error("频次规则检查失败", e);
            return false;
        }
    }

    public void updateCache(String userId, String deviceId, AdRetargetingLog log) {
        String cacheKey = userId + ":" + deviceId;
        List<AdRetargetingLog> recentLogs = recentLogsCache.getIfPresent(cacheKey);
        if (recentLogs != null) {
            recentLogs.add(log);
            recentLogsCache.put(cacheKey, recentLogs);
        }
        cooldownCache.put(cacheKey, LocalDateTime.now());
    }
}
