package tv.shorthub.ad.config;

import lombok.extern.slf4j.Slf4j;

import java.util.Objects;

/**
 * Facebook事件类型映射
 * 参考：https://developers.facebook.com/docs/marketing-api/conversions-api/parameters#event-name
 */
@Slf4j
public enum FacebookEventType {
    // 标准事件
    VIEW_CONTENT("ViewContent", "查看内容"),
    // SEARCH("Search", "搜索"),
    // ADD_TO_CART("AddToCart", "加入购物车"),
    // ADD_TO_WISHLIST("AddToWishlist", "加入愿望清单"),
    INITIATE_CHECKOUT("InitiateCheckout", "开始结账"),
    // ADD_PAYMENT_INFO("AddPaymentInfo", "添加支付信息"),
    PURCHASE("Purchase", "购买"),
    SUBSCRIBE("Subscribe", "订阅"),
    // LEAD("Lead", "潜在客户"),
    COMPLETE_REGISTRATION("CompleteRegistration", "完成注册"),

    // 自定义事件
    // CUSTOM("Custom", "自定义事件")
    ;

    private final String eventName;
    private final String description;

    FacebookEventType(String eventName, String description) {
        this.eventName = eventName;
        this.description = description;
    }

    public String getEventName() {
        return eventName;
    }

    public String getDescription() {
        return description;
    }

    public static FacebookEventType ofValue(String eventName) {
        for (FacebookEventType eventType : values()) {
            if (eventType.getEventName().equals(eventName)) {
                return eventType;
            }
        }
        return null;
    }
}
