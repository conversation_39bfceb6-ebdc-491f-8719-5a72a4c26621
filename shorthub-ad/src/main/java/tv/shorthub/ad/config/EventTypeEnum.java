package tv.shorthub.ad.config;


public enum EventTypeEnum {
    REGISTRY("REGISTRY", "注册"),
    CREATE_ORDER("CREATE_ORDER", "创建订单"),
    PURCHASE("PURCHASE", "付费"),
    SUBSCRIBE("SUBSCRIBE", "订阅"),
    VIEW("VIEW", "查看内容"),
    ;

    private final String eventName;
    private final String description;

    EventTypeEnum(String eventName, String description)
    {
        this.eventName = eventName;
        this.description = description;
    }

    public String getEventName() {
        return eventName;
    }

    public String getDescription() {
        return description;
    }

    public static EventTypeEnum ofValue(String value) {
        EventTypeEnum[] values = values();
        for (EventTypeEnum stateEnum : values) {
            if (stateEnum.eventName.equals(value)) {
                return stateEnum;
            }
        }
        return null;
    }
}
