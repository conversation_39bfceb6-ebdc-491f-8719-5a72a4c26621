<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>shorthub</artifactId>
        <groupId>tv.shorthub</groupId>
        <version>1.0.0</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>

    <artifactId>shorthub-system</artifactId>

    <description>
        system系统模块
    </description>

    <dependencies>

        <!-- 通用工具-->
        <dependency>
            <groupId>tv.shorthub</groupId>
            <artifactId>shorthub-common</artifactId>
        </dependency>

        <!-- PayPal服务-->
        <dependency>
            <groupId>tv.shorthub</groupId>
            <artifactId>shorthub-paypal</artifactId>
        </dependency>

        <!-- Airwallex服务-->
        <dependency>
            <groupId>tv.shorthub</groupId>
            <artifactId>shorthub-airwallex</artifactId>
        </dependency>

        <dependency>
            <groupId>tv.shorthub</groupId>
            <artifactId>shorthub-payermax</artifactId>
        </dependency>

        <dependency>
            <groupId>tv.shorthub</groupId>
            <artifactId>shorthub-googleplay</artifactId>
        </dependency>

        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.github.jsqlparser</groupId>
                    <artifactId>jsqlparser</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.github.jsqlparser</groupId>
            <artifactId>jsqlparser</artifactId>
            <version>${jsqlparse.version}</version>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-csv</artifactId>
            <version>1.8</version>
            <scope>compile</scope>
        </dependency>

    </dependencies>

</project>
