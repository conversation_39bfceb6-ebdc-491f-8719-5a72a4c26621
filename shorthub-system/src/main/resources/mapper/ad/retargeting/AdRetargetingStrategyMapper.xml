<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tv.shorthub.system.mapper.AdRetargetingStrategyMapper">

    <resultMap type="AdRetargetingStrategy" id="AdRetargetingStrategyResult">
        <result property="id"    column="id"    />
        <result property="strategyName"    column="strategy_name"    />
        <result property="strategyType"    column="strategy_type"    />
        <result property="status"    column="status"    />
        <result property="platform"    column="platform"    />
        <result property="callbackUrl"    column="callback_url"    />
        <result property="callbackMethod"    column="callback_method"    />
        <result property="callbackParams"    column="callback_params"    />
        <result property="retryTimes"    column="retry_times"    />
        <result property="retryInterval"    column="retry_interval"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
    </resultMap>

    <sql id="selectAdRetargetingStrategyVo">
        select id, strategy_name, strategy_type, status, platform, callback_url, callback_method, callback_params, retry_times, retry_interval, create_time, update_time, create_by, update_by from ad_retargeting_strategy
    </sql>

    <insert id="batchInsertOrUpdate">
        <foreach item="item" index="index" collection="list" separator=";">
            insert into ad_retargeting_strategy
            <trim prefix="(" suffix=")" suffixOverrides=",">
                        <if test="item.strategyName != null and item.strategyName != ''">strategy_name,</if>
                        <if test="item.strategyType != null">strategy_type,</if>
                        <if test="item.status != null">status,</if>
                        <if test="item.platform != null and item.platform != ''">platform,</if>
                        <if test="item.callbackUrl != null and item.callbackUrl != ''">callback_url,</if>
                        <if test="item.callbackMethod != null and item.callbackMethod != ''">callback_method,</if>
                        <if test="item.callbackParams != null">callback_params,</if>
                        <if test="item.retryTimes != null">retry_times,</if>
                        <if test="item.retryInterval != null">retry_interval,</if>
                        <if test="item.createTime != null">create_time,</if>
                        <if test="item.updateTime != null">update_time,</if>
                        <if test="item.createBy != null">create_by,</if>
                        <if test="item.updateBy != null">update_by,</if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                        <if test="item.strategyName != null and item.strategyName != ''">#{item.strategyName},</if>
                        <if test="item.strategyType != null">#{item.strategyType},</if>
                        <if test="item.status != null">#{item.status},</if>
                        <if test="item.platform != null and item.platform != ''">#{item.platform},</if>
                        <if test="item.callbackUrl != null and item.callbackUrl != ''">#{item.callbackUrl},</if>
                        <if test="item.callbackMethod != null and item.callbackMethod != ''">#{item.callbackMethod},</if>
                        <if test="item.callbackParams != null">#{item.callbackParams},</if>
                        <if test="item.retryTimes != null">#{item.retryTimes},</if>
                        <if test="item.retryInterval != null">#{item.retryInterval},</if>
                        <if test="item.createTime != null">#{item.createTime},</if>
                        <if test="item.updateTime != null">#{item.updateTime},</if>
                        <if test="item.createBy != null">#{item.createBy},</if>
                        <if test="item.updateBy != null">#{item.updateBy},</if>
            </trim>
            on duplicate key update
            <trim suffixOverrides=",">
                        <if test="item.strategyName != null and item.strategyName != ''">strategy_name = values(strategy_name),</if>
                        <if test="item.strategyType != null">strategy_type = values(strategy_type),</if>
                        <if test="item.status != null">status = values(status),</if>
                        <if test="item.platform != null and item.platform != ''">platform = values(platform),</if>
                        <if test="item.callbackUrl != null and item.callbackUrl != ''">callback_url = values(callback_url),</if>
                        <if test="item.callbackMethod != null and item.callbackMethod != ''">callback_method = values(callback_method),</if>
                        <if test="item.callbackParams != null">callback_params = values(callback_params),</if>
                        <if test="item.retryTimes != null">retry_times = values(retry_times),</if>
                        <if test="item.retryInterval != null">retry_interval = values(retry_interval),</if>
                        <if test="item.createTime != null">create_time = values(create_time),</if>
                        <if test="item.updateTime != null">update_time = values(update_time),</if>
                        <if test="item.createBy != null">create_by = values(create_by),</if>
                        <if test="item.updateBy != null">update_by = values(update_by),</if>
            </trim>
        </foreach>

    </insert>

    <select id="getSummary" parameterType="AdRetargetingStrategy" resultMap="AdRetargetingStrategyResult">
        select
            max(id) as id
        from ad_retargeting_strategy
        <where>
                    <if test="strategyName != null  and strategyName != ''"> and strategy_name like concat('%', #{strategyName}, '%')</if>
                    <if test="strategyType != null "> and strategy_type = #{strategyType}</if>
                    <if test="status != null "> and status = #{status}</if>
                    <if test="platform != null  and platform != ''"> and platform = #{platform}</if>
                    <if test="callbackUrl != null  and callbackUrl != ''"> and callback_url = #{callbackUrl}</if>
                    <if test="callbackMethod != null  and callbackMethod != ''"> and callback_method = #{callbackMethod}</if>
                    <if test="callbackParams != null  and callbackParams != ''"> and callback_params = #{callbackParams}</if>
                    <if test="retryTimes != null "> and retry_times = #{retryTimes}</if>
                    <if test="retryInterval != null "> and retry_interval = #{retryInterval}</if>
                </where>
    </select>



    <select id="summary" parameterType="tv.shorthub.common.core.domain.SummaryRequest" resultMap="AdRetargetingStrategyResult">
        
    </select>
    <select id="allSummary" parameterType="tv.shorthub.common.core.domain.SummaryRequest" resultMap="AdRetargetingStrategyResult">
        
    </select>

</mapper>
