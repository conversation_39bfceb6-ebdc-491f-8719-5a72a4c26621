<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tv.shorthub.system.mapper.AdRetargetingEventMapper">

    <resultMap type="AdRetargetingEvent" id="AdRetargetingEventResult">
        <result property="id"    column="id"    />
        <result property="strategyId"    column="strategy_id"    />
        <result property="eventName"    column="event_name"    />
        <result property="eventType"    column="event_type"    />
        <result property="eventParams"    column="event_params"    />
        <result property="status"    column="status"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
    </resultMap>

    <sql id="selectAdRetargetingEventVo">
        select id, strategy_id, event_name, event_type, event_params, status, create_time, update_time, create_by, update_by from ad_retargeting_event
    </sql>

    <insert id="batchInsertOrUpdate">
        <foreach item="item" index="index" collection="list" separator=";">
            insert into ad_retargeting_event
            <trim prefix="(" suffix=")" suffixOverrides=",">
                        <if test="item.strategyId != null">strategy_id,</if>
                        <if test="item.eventName != null and item.eventName != ''">event_name,</if>
                        <if test="item.eventType != null and item.eventType != ''">event_type,</if>
                        <if test="item.eventParams != null">event_params,</if>
                        <if test="item.status != null">status,</if>
                        <if test="item.createTime != null">create_time,</if>
                        <if test="item.updateTime != null">update_time,</if>
                        <if test="item.createBy != null">create_by,</if>
                        <if test="item.updateBy != null">update_by,</if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                        <if test="item.strategyId != null">#{item.strategyId},</if>
                        <if test="item.eventName != null and item.eventName != ''">#{item.eventName},</if>
                        <if test="item.eventType != null and item.eventType != ''">#{item.eventType},</if>
                        <if test="item.eventParams != null">#{item.eventParams},</if>
                        <if test="item.status != null">#{item.status},</if>
                        <if test="item.createTime != null">#{item.createTime},</if>
                        <if test="item.updateTime != null">#{item.updateTime},</if>
                        <if test="item.createBy != null">#{item.createBy},</if>
                        <if test="item.updateBy != null">#{item.updateBy},</if>
            </trim>
            on duplicate key update
            <trim suffixOverrides=",">
                        <if test="item.strategyId != null">strategy_id = values(strategy_id),</if>
                        <if test="item.eventName != null and item.eventName != ''">event_name = values(event_name),</if>
                        <if test="item.eventType != null and item.eventType != ''">event_type = values(event_type),</if>
                        <if test="item.eventParams != null">event_params = values(event_params),</if>
                        <if test="item.status != null">status = values(status),</if>
                        <if test="item.createTime != null">create_time = values(create_time),</if>
                        <if test="item.updateTime != null">update_time = values(update_time),</if>
                        <if test="item.createBy != null">create_by = values(create_by),</if>
                        <if test="item.updateBy != null">update_by = values(update_by),</if>
            </trim>
        </foreach>

    </insert>

    <select id="getSummary" parameterType="AdRetargetingEvent" resultMap="AdRetargetingEventResult">
        select
            max(id) as id
        from ad_retargeting_event
        <where>
                    <if test="strategyId != null "> and strategy_id = #{strategyId}</if>
                    <if test="eventName != null  and eventName != ''"> and event_name like concat('%', #{eventName}, '%')</if>
                    <if test="eventType != null  and eventType != ''"> and event_type = #{eventType}</if>
                    <if test="eventParams != null  and eventParams != ''"> and event_params = #{eventParams}</if>
                    <if test="status != null "> and status = #{status}</if>
                </where>
    </select>



    <select id="summary" parameterType="tv.shorthub.common.core.domain.SummaryRequest" resultMap="AdRetargetingEventResult">
        
    </select>
    <select id="allSummary" parameterType="tv.shorthub.common.core.domain.SummaryRequest" resultMap="AdRetargetingEventResult">
        
    </select>

</mapper>
