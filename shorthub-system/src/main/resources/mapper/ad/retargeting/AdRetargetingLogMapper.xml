<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tv.shorthub.system.mapper.AdRetargetingLogMapper">

    <resultMap type="AdRetargetingLog" id="AdRetargetingLogResult">
        <result property="id"    column="id"    />
        <result property="strategyId"    column="strategy_id"    />
        <result property="eventId"    column="event_id"    />
        <result property="requestId"    column="request_id"    />
        <result property="userId"    column="user_id"    />
        <result property="deviceId"    column="device_id"    />
        <result property="eventData"    column="event_data"    />
        <result property="callbackData"    column="callback_data"    />
        <result property="callbackStatus"    column="callback_status"    />
        <result property="retryCount"    column="retry_count"    />
        <result property="errorMessage"    column="error_message"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
    </resultMap>

    <sql id="selectAdRetargetingLogVo">
        select id, strategy_id, event_id, request_id, user_id, device_id, event_data, callback_data, callback_status, retry_count, error_message, create_time, update_time, create_by, update_by from ad_retargeting_log
    </sql>

    <insert id="batchInsertOrUpdate">
        <foreach item="item" index="index" collection="list" separator=";">
            insert into ad_retargeting_log
            <trim prefix="(" suffix=")" suffixOverrides=",">
                        <if test="item.strategyId != null">strategy_id,</if>
                        <if test="item.eventId != null">event_id,</if>
                        <if test="item.requestId != null and item.requestId != ''">request_id,</if>
                        <if test="item.userId != null">user_id,</if>
                        <if test="item.deviceId != null">device_id,</if>
                        <if test="item.eventData != null and item.eventData != ''">event_data,</if>
                        <if test="item.callbackData != null">callback_data,</if>
                        <if test="item.callbackStatus != null">callback_status,</if>
                        <if test="item.retryCount != null">retry_count,</if>
                        <if test="item.errorMessage != null">error_message,</if>
                        <if test="item.createTime != null">create_time,</if>
                        <if test="item.updateTime != null">update_time,</if>
                        <if test="item.createBy != null">create_by,</if>
                        <if test="item.updateBy != null">update_by,</if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                        <if test="item.strategyId != null">#{item.strategyId},</if>
                        <if test="item.eventId != null">#{item.eventId},</if>
                        <if test="item.requestId != null and item.requestId != ''">#{item.requestId},</if>
                        <if test="item.userId != null">#{item.userId},</if>
                        <if test="item.deviceId != null">#{item.deviceId},</if>
                        <if test="item.eventData != null and item.eventData != ''">#{item.eventData},</if>
                        <if test="item.callbackData != null">#{item.callbackData},</if>
                        <if test="item.callbackStatus != null">#{item.callbackStatus},</if>
                        <if test="item.retryCount != null">#{item.retryCount},</if>
                        <if test="item.errorMessage != null">#{item.errorMessage},</if>
                        <if test="item.createTime != null">#{item.createTime},</if>
                        <if test="item.updateTime != null">#{item.updateTime},</if>
                        <if test="item.createBy != null">#{item.createBy},</if>
                        <if test="item.updateBy != null">#{item.updateBy},</if>
            </trim>
            on duplicate key update
            <trim suffixOverrides=",">
                        <if test="item.strategyId != null">strategy_id = values(strategy_id),</if>
                        <if test="item.eventId != null">event_id = values(event_id),</if>
                        <if test="item.requestId != null and item.requestId != ''">request_id = values(request_id),</if>
                        <if test="item.userId != null">user_id = values(user_id),</if>
                        <if test="item.deviceId != null">device_id = values(device_id),</if>
                        <if test="item.eventData != null and item.eventData != ''">event_data = values(event_data),</if>
                        <if test="item.callbackData != null">callback_data = values(callback_data),</if>
                        <if test="item.callbackStatus != null">callback_status = values(callback_status),</if>
                        <if test="item.retryCount != null">retry_count = values(retry_count),</if>
                        <if test="item.errorMessage != null">error_message = values(error_message),</if>
                        <if test="item.createTime != null">create_time = values(create_time),</if>
                        <if test="item.updateTime != null">update_time = values(update_time),</if>
                        <if test="item.createBy != null">create_by = values(create_by),</if>
                        <if test="item.updateBy != null">update_by = values(update_by),</if>
            </trim>
        </foreach>

    </insert>

    <select id="getSummary" parameterType="AdRetargetingLog" resultMap="AdRetargetingLogResult">
        select
            max(id) as id
        from ad_retargeting_log
        <where>
                    <if test="strategyId != null "> and strategy_id = #{strategyId}</if>
                    <if test="eventId != null "> and event_id = #{eventId}</if>
                    <if test="requestId != null  and requestId != ''"> and request_id = #{requestId}</if>
                    <if test="userId != null  and userId != ''"> and user_id = #{userId}</if>
                    <if test="deviceId != null  and deviceId != ''"> and device_id = #{deviceId}</if>
                    <if test="eventData != null  and eventData != ''"> and event_data = #{eventData}</if>
                    <if test="callbackData != null  and callbackData != ''"> and callback_data = #{callbackData}</if>
                    <if test="callbackStatus != null "> and callback_status = #{callbackStatus}</if>
                    <if test="retryCount != null "> and retry_count = #{retryCount}</if>
                    <if test="errorMessage != null  and errorMessage != ''"> and error_message = #{errorMessage}</if>
                </where>
    </select>



    <select id="summary" parameterType="tv.shorthub.common.core.domain.SummaryRequest" resultMap="AdRetargetingLogResult">
        
    </select>
    <select id="allSummary" parameterType="tv.shorthub.common.core.domain.SummaryRequest" resultMap="AdRetargetingLogResult">
        
    </select>

</mapper>
