<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tv.shorthub.system.mapper.AdRetargetingRuleMapper">

    <resultMap type="AdRetargetingRule" id="AdRetargetingRuleResult">
        <result property="id"    column="id"    />
        <result property="eventId"    column="event_id"    />
        <result property="ruleName"    column="rule_name"    />
        <result property="ruleType"    column="rule_type"    />
        <result property="ruleParams"    column="rule_params"    />
        <result property="priority"    column="priority"    />
        <result property="status"    column="status"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
    </resultMap>

    <sql id="selectAdRetargetingRuleVo">
        select id, event_id, rule_name, rule_type, rule_params, priority, status, create_time, update_time, create_by, update_by from ad_retargeting_rule
    </sql>

    <insert id="batchInsertOrUpdate">
        <foreach item="item" index="index" collection="list" separator=";">
            insert into ad_retargeting_rule
            <trim prefix="(" suffix=")" suffixOverrides=",">
                        <if test="item.eventId != null">event_id,</if>
                        <if test="item.ruleName != null and item.ruleName != ''">rule_name,</if>
                        <if test="item.ruleType != null">rule_type,</if>
                        <if test="item.ruleParams != null and item.ruleParams != ''">rule_params,</if>
                        <if test="item.priority != null">priority,</if>
                        <if test="item.status != null">status,</if>
                        <if test="item.createTime != null">create_time,</if>
                        <if test="item.updateTime != null">update_time,</if>
                        <if test="item.createBy != null">create_by,</if>
                        <if test="item.updateBy != null">update_by,</if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                        <if test="item.eventId != null">#{item.eventId},</if>
                        <if test="item.ruleName != null and item.ruleName != ''">#{item.ruleName},</if>
                        <if test="item.ruleType != null">#{item.ruleType},</if>
                        <if test="item.ruleParams != null and item.ruleParams != ''">#{item.ruleParams},</if>
                        <if test="item.priority != null">#{item.priority},</if>
                        <if test="item.status != null">#{item.status},</if>
                        <if test="item.createTime != null">#{item.createTime},</if>
                        <if test="item.updateTime != null">#{item.updateTime},</if>
                        <if test="item.createBy != null">#{item.createBy},</if>
                        <if test="item.updateBy != null">#{item.updateBy},</if>
            </trim>
            on duplicate key update
            <trim suffixOverrides=",">
                        <if test="item.eventId != null">event_id = values(event_id),</if>
                        <if test="item.ruleName != null and item.ruleName != ''">rule_name = values(rule_name),</if>
                        <if test="item.ruleType != null">rule_type = values(rule_type),</if>
                        <if test="item.ruleParams != null and item.ruleParams != ''">rule_params = values(rule_params),</if>
                        <if test="item.priority != null">priority = values(priority),</if>
                        <if test="item.status != null">status = values(status),</if>
                        <if test="item.createTime != null">create_time = values(create_time),</if>
                        <if test="item.updateTime != null">update_time = values(update_time),</if>
                        <if test="item.createBy != null">create_by = values(create_by),</if>
                        <if test="item.updateBy != null">update_by = values(update_by),</if>
            </trim>
        </foreach>

    </insert>

    <select id="getSummary" parameterType="AdRetargetingRule" resultMap="AdRetargetingRuleResult">
        select
            max(id) as id
        from ad_retargeting_rule
        <where>
                    <if test="eventId != null "> and event_id = #{eventId}</if>
                    <if test="ruleName != null  and ruleName != ''"> and rule_name like concat('%', #{ruleName}, '%')</if>
                    <if test="ruleType != null "> and rule_type = #{ruleType}</if>
                    <if test="ruleParams != null  and ruleParams != ''"> and rule_params = #{ruleParams}</if>
                    <if test="priority != null "> and priority = #{priority}</if>
                    <if test="status != null "> and status = #{status}</if>
                </where>
    </select>



    <select id="summary" parameterType="tv.shorthub.common.core.domain.SummaryRequest" resultMap="AdRetargetingRuleResult">
        
    </select>
    <select id="allSummary" parameterType="tv.shorthub.common.core.domain.SummaryRequest" resultMap="AdRetargetingRuleResult">
        
    </select>

</mapper>
