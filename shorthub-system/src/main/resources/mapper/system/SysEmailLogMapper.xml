<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tv.shorthub.system.mapper.SysEmailLogMapper">

    <resultMap type="SysEmailLog" id="SysEmailLogResult">
        <result property="id"    column="id"    />
        <result property="fromEmail"    column="from_email"    />
        <result property="toEmail"    column="to_email"    />
        <result property="orderId"    column="order_id"    />
        <result property="subject"    column="subject"    />
        <result property="content"    column="content"    />
        <result property="emailId"    column="email_id"    />
        <result property="status"    column="status"    />
        <result property="errorMessage"    column="error_message"    />
        <result property="responseBody"    column="response_body"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectSysEmailLogVo">
        select id, from_email, to_email, order_id, subject, content, email_id, status, error_message, response_body, create_time, update_time from sys_email_log
    </sql>

    <insert id="batchInsertOrUpdate">
        <foreach item="item" index="index" collection="list" separator=";">
            insert into sys_email_log
            <trim prefix="(" suffix=")" suffixOverrides=",">
                        <if test="item.fromEmail != null and item.fromEmail != ''">from_email,</if>
                        <if test="item.toEmail != null and item.toEmail != ''">to_email,</if>
                        <if test="item.orderId != null">order_id,</if>
                        <if test="item.subject != null and item.subject != ''">subject,</if>
                        <if test="item.content != null">content,</if>
                        <if test="item.emailId != null">email_id,</if>
                        <if test="item.status != null and item.status != ''">status,</if>
                        <if test="item.errorMessage != null">error_message,</if>
                        <if test="item.responseBody != null">response_body,</if>
                        <if test="item.createTime != null">create_time,</if>
                        <if test="item.updateTime != null">update_time,</if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                        <if test="item.fromEmail != null and item.fromEmail != ''">#{item.fromEmail},</if>
                        <if test="item.toEmail != null and item.toEmail != ''">#{item.toEmail},</if>
                        <if test="item.orderId != null">#{item.orderId},</if>
                        <if test="item.subject != null and item.subject != ''">#{item.subject},</if>
                        <if test="item.content != null">#{item.content},</if>
                        <if test="item.emailId != null">#{item.emailId},</if>
                        <if test="item.status != null and item.status != ''">#{item.status},</if>
                        <if test="item.errorMessage != null">#{item.errorMessage},</if>
                        <if test="item.responseBody != null">#{item.responseBody},</if>
                        <if test="item.createTime != null">#{item.createTime},</if>
                        <if test="item.updateTime != null">#{item.updateTime},</if>
            </trim>
            on duplicate key update
            <trim suffixOverrides=",">
                        <if test="item.fromEmail != null and item.fromEmail != ''">from_email = values(from_email),</if>
                        <if test="item.toEmail != null and item.toEmail != ''">to_email = values(to_email),</if>
                        <if test="item.orderId != null">order_id = values(order_id),</if>
                        <if test="item.subject != null and item.subject != ''">subject = values(subject),</if>
                        <if test="item.content != null">content = values(content),</if>
                        <if test="item.emailId != null">email_id = values(email_id),</if>
                        <if test="item.status != null and item.status != ''">status = values(status),</if>
                        <if test="item.errorMessage != null">error_message = values(error_message),</if>
                        <if test="item.responseBody != null">response_body = values(response_body),</if>
                        <if test="item.createTime != null">create_time = values(create_time),</if>
                        <if test="item.updateTime != null">update_time = values(update_time),</if>
            </trim>
        </foreach>

    </insert>

    <select id="getSummary" parameterType="SysEmailLog" resultMap="SysEmailLogResult">
        select
            max(id) as id
        from sys_email_log
        <where>
                    <if test="fromEmail != null  and fromEmail != ''"> and from_email = #{fromEmail}</if>
                    <if test="toEmail != null  and toEmail != ''"> and to_email = #{toEmail}</if>
                    <if test="orderId != null  and orderId != ''"> and order_id = #{orderId}</if>
                    <if test="subject != null  and subject != ''"> and subject = #{subject}</if>
                    <if test="content != null  and content != ''"> and content = #{content}</if>
                    <if test="emailId != null  and emailId != ''"> and email_id = #{emailId}</if>
                    <if test="status != null  and status != ''"> and status = #{status}</if>
                    <if test="errorMessage != null  and errorMessage != ''"> and error_message = #{errorMessage}</if>
                    <if test="responseBody != null  and responseBody != ''"> and response_body = #{responseBody}</if>
                </where>
    </select>



    <select id="summary" parameterType="tv.shorthub.common.core.domain.SummaryRequest" resultMap="SysEmailLogResult">
        
    </select>
    <select id="allSummary" parameterType="tv.shorthub.common.core.domain.SummaryRequest" resultMap="SysEmailLogResult">
        
    </select>

</mapper>
