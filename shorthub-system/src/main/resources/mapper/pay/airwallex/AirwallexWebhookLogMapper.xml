<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tv.shorthub.system.mapper.AirwallexWebhookLogMapper">

    <resultMap type="AirwallexWebhookLog" id="AirwallexWebhookLogResult">
        <result property="id"    column="id"    />
        <result property="pid"    column="pid"    />
        <result property="eventId"    column="event_id"    />
        <result property="signature"    column="signature"    />
        <result property="timestamp"    column="timestamp"    />
        <result property="eventData"    column="event_data"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectAirwallexWebhookLogVo">
        select id, pid, event_id, signature, timestamp, event_data, create_by, create_time, update_by, update_time from airwallex_webhook_log
    </sql>

    <insert id="batchInsertOrUpdate">
        <foreach item="item" index="index" collection="list" separator=";">
            insert into airwallex_webhook_log
            <trim prefix="(" suffix=")" suffixOverrides=",">
                        <if test="item.pid != null">pid,</if>
                        <if test="item.eventId != null and item.eventId != ''">event_id,</if>
                        <if test="item.signature != null">signature,</if>
                        <if test="item.timestamp != null">timestamp,</if>
                        <if test="item.eventData != null">event_data,</if>
                        <if test="item.createBy != null">create_by,</if>
                        <if test="item.createTime != null">create_time,</if>
                        <if test="item.updateBy != null">update_by,</if>
                        <if test="item.updateTime != null">update_time,</if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                        <if test="item.pid != null">#{item.pid},</if>
                        <if test="item.eventId != null and item.eventId != ''">#{item.eventId},</if>
                        <if test="item.signature != null">#{item.signature},</if>
                        <if test="item.timestamp != null">#{item.timestamp},</if>
                        <if test="item.eventData != null">#{item.eventData},</if>
                        <if test="item.createBy != null">#{item.createBy},</if>
                        <if test="item.createTime != null">#{item.createTime},</if>
                        <if test="item.updateBy != null">#{item.updateBy},</if>
                        <if test="item.updateTime != null">#{item.updateTime},</if>
            </trim>
            on duplicate key update
            <trim suffixOverrides=",">
                        <if test="item.pid != null">pid = values(pid),</if>
                        <if test="item.eventId != null and item.eventId != ''">event_id = values(event_id),</if>
                        <if test="item.signature != null">signature = values(signature),</if>
                        <if test="item.timestamp != null">timestamp = values(timestamp),</if>
                        <if test="item.eventData != null">event_data = values(event_data),</if>
                        <if test="item.createBy != null">create_by = values(create_by),</if>
                        <if test="item.createTime != null">create_time = values(create_time),</if>
                        <if test="item.updateBy != null">update_by = values(update_by),</if>
                        <if test="item.updateTime != null">update_time = values(update_time),</if>
            </trim>
        </foreach>

    </insert>

    <select id="getSummary" parameterType="AirwallexWebhookLog" resultMap="AirwallexWebhookLogResult">
        select
            max(id) as id
        from airwallex_webhook_log
        <where>
                    <if test="pid != null "> and pid = #{pid}</if>
                    <if test="eventId != null  and eventId != ''"> and event_id = #{eventId}</if>
                    <if test="signature != null  and signature != ''"> and signature = #{signature}</if>
                    <if test="timestamp != null  and timestamp != ''"> and timestamp = #{timestamp}</if>
                    <if test="eventData != null "> and event_data = #{eventData}</if>
                </where>
    </select>



    <select id="summary" parameterType="tv.shorthub.common.core.domain.SummaryRequest" resultMap="AirwallexWebhookLogResult">
        
    </select>
    <select id="allSummary" parameterType="tv.shorthub.common.core.domain.SummaryRequest" resultMap="AirwallexWebhookLogResult">
        
    </select>

</mapper>
