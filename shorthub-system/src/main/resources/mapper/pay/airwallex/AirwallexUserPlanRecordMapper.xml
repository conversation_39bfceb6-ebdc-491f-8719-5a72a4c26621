<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tv.shorthub.system.mapper.AirwallexUserPlanRecordMapper">

    <resultMap type="AirwallexUserPlanRecord" id="AirwallexUserPlanRecordResult">
        <result property="id"    column="id"    />
        <result property="orderNo"    column="order_no"    />
        <result property="paymentId"    column="payment_id"    />
        <result property="period"    column="period"    />
        <result property="periodNum"    column="period_num"    />
        <result property="amount"    column="amount"    />
        <result property="currency"    column="currency"    />
        <result property="success"    column="success"    />
        <result property="failureCnt"    column="failure_cnt"    />
        <result property="extendData"    column="extend_data"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectAirwallexUserPlanRecordVo">
        select id, order_no, payment_id, period, period_num, amount, currency, success, failure_cnt, extend_data, create_by, create_time, update_by, update_time from airwallex_user_plan_record
    </sql>

    <insert id="batchInsertOrUpdate">
        <foreach item="item" index="index" collection="list" separator=";">
            insert into airwallex_user_plan_record
            <trim prefix="(" suffix=")" suffixOverrides=",">
                        <if test="item.orderNo != null and item.orderNo != ''">order_no,</if>
                        <if test="item.paymentId != null and item.paymentId != ''">payment_id,</if>
                        <if test="item.period != null and item.period != ''">period,</if>
                        <if test="item.periodNum != null">period_num,</if>
                        <if test="item.amount != null">amount,</if>
                        <if test="item.currency != null and item.currency != ''">currency,</if>
                        <if test="item.success != null">success,</if>
                        <if test="item.failureCnt != null">failure_cnt,</if>
                        <if test="item.extendData != null">extend_data,</if>
                        <if test="item.createBy != null">create_by,</if>
                        <if test="item.createTime != null">create_time,</if>
                        <if test="item.updateBy != null">update_by,</if>
                        <if test="item.updateTime != null">update_time,</if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                        <if test="item.orderNo != null and item.orderNo != ''">#{item.orderNo},</if>
                        <if test="item.paymentId != null and item.paymentId != ''">#{item.paymentId},</if>
                        <if test="item.period != null and item.period != ''">#{item.period},</if>
                        <if test="item.periodNum != null">#{item.periodNum},</if>
                        <if test="item.amount != null">#{item.amount},</if>
                        <if test="item.currency != null and item.currency != ''">#{item.currency},</if>
                        <if test="item.success != null">#{item.success},</if>
                        <if test="item.failureCnt != null">#{item.failureCnt},</if>
                        <if test="item.extendData != null">#{item.extendData},</if>
                        <if test="item.createBy != null">#{item.createBy},</if>
                        <if test="item.createTime != null">#{item.createTime},</if>
                        <if test="item.updateBy != null">#{item.updateBy},</if>
                        <if test="item.updateTime != null">#{item.updateTime},</if>
            </trim>
            on duplicate key update
            <trim suffixOverrides=",">
                        <if test="item.orderNo != null and item.orderNo != ''">order_no = values(order_no),</if>
                        <if test="item.paymentId != null and item.paymentId != ''">payment_id = values(payment_id),</if>
                        <if test="item.period != null and item.period != ''">period = values(period),</if>
                        <if test="item.periodNum != null">period_num = values(period_num),</if>
                        <if test="item.amount != null">amount = values(amount),</if>
                        <if test="item.currency != null and item.currency != ''">currency = values(currency),</if>
                        <if test="item.success != null">success = values(success),</if>
                        <if test="item.failureCnt != null">failure_cnt = values(failure_cnt),</if>
                        <if test="item.extendData != null">extend_data = values(extend_data),</if>
                        <if test="item.createBy != null">create_by = values(create_by),</if>
                        <if test="item.createTime != null">create_time = values(create_time),</if>
                        <if test="item.updateBy != null">update_by = values(update_by),</if>
                        <if test="item.updateTime != null">update_time = values(update_time),</if>
            </trim>
        </foreach>

    </insert>

    <select id="getSummary" parameterType="AirwallexUserPlanRecord" resultMap="AirwallexUserPlanRecordResult">
        select
            max(id) as id
        from airwallex_user_plan_record
        <where>
                    <if test="orderNo != null  and orderNo != ''"> and order_no = #{orderNo}</if>
                    <if test="paymentId != null  and paymentId != ''"> and payment_id = #{paymentId}</if>
                    <if test="period != null  and period != ''"> and period = #{period}</if>
                    <if test="periodNum != null "> and period_num = #{periodNum}</if>
                    <if test="amount != null "> and amount = #{amount}</if>
                    <if test="currency != null  and currency != ''"> and currency = #{currency}</if>
                    <if test="success != null "> and success = #{success}</if>
                    <if test="failureCnt != null "> and failure_cnt = #{failureCnt}</if>
                    <if test="extendData != null "> and extend_data = #{extendData}</if>
                </where>
    </select>



    <select id="summary" parameterType="tv.shorthub.common.core.domain.SummaryRequest" resultMap="AirwallexUserPlanRecordResult">
        
    </select>
    <select id="allSummary" parameterType="tv.shorthub.common.core.domain.SummaryRequest" resultMap="AirwallexUserPlanRecordResult">
        
    </select>

</mapper>
