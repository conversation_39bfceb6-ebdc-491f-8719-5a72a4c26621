<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tv.shorthub.system.mapper.AirwallexPaymentConfigMapper">

    <resultMap type="AirwallexPaymentConfig" id="AirwallexPaymentConfigResult">
        <result property="id"    column="id"    />
        <result property="clientId"    column="client_id"    />
        <result property="apiKey"    column="api_key"    />
        <result property="version"    column="version"    />
        <result property="sandbox"    column="sandbox"    />
        <result property="webhookId"    column="webhook_id"    />
        <result property="enable"    column="enable"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectAirwallexPaymentConfigVo">
        select id, client_id, api_key, version, sandbox, webhook_id, enable, create_by, create_time, update_by, update_time from airwallex_payment_config
    </sql>

    <insert id="batchInsertOrUpdate">
        <foreach item="item" index="index" collection="list" separator=";">
            insert into airwallex_payment_config
            <trim prefix="(" suffix=")" suffixOverrides=",">
                        <if test="item.clientId != null">client_id,</if>
                        <if test="item.apiKey != null">api_key,</if>
                        <if test="item.version != null">version,</if>
                        <if test="item.sandbox != null">sandbox,</if>
                        <if test="item.webhookId != null">webhook_id,</if>
                        <if test="item.enable != null">enable,</if>
                        <if test="item.createBy != null">create_by,</if>
                        <if test="item.createTime != null">create_time,</if>
                        <if test="item.updateBy != null">update_by,</if>
                        <if test="item.updateTime != null">update_time,</if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                        <if test="item.clientId != null">#{item.clientId},</if>
                        <if test="item.apiKey != null">#{item.apiKey},</if>
                        <if test="item.version != null">#{item.version},</if>
                        <if test="item.sandbox != null">#{item.sandbox},</if>
                        <if test="item.webhookId != null">#{item.webhookId},</if>
                        <if test="item.enable != null">#{item.enable},</if>
                        <if test="item.createBy != null">#{item.createBy},</if>
                        <if test="item.createTime != null">#{item.createTime},</if>
                        <if test="item.updateBy != null">#{item.updateBy},</if>
                        <if test="item.updateTime != null">#{item.updateTime},</if>
            </trim>
            on duplicate key update
            <trim suffixOverrides=",">
                        <if test="item.clientId != null">client_id = values(client_id),</if>
                        <if test="item.apiKey != null">api_key = values(api_key),</if>
                        <if test="item.version != null">version = values(version),</if>
                        <if test="item.sandbox != null">sandbox = values(sandbox),</if>
                        <if test="item.webhookId != null">webhook_id = values(webhook_id),</if>
                        <if test="item.enable != null">enable = values(enable),</if>
                        <if test="item.createBy != null">create_by = values(create_by),</if>
                        <if test="item.createTime != null">create_time = values(create_time),</if>
                        <if test="item.updateBy != null">update_by = values(update_by),</if>
                        <if test="item.updateTime != null">update_time = values(update_time),</if>
            </trim>
        </foreach>

    </insert>

    <select id="getSummary" parameterType="AirwallexPaymentConfig" resultMap="AirwallexPaymentConfigResult">
        select
            max(id) as id
        from airwallex_payment_config
        <where>
                    <if test="clientId != null  and clientId != ''"> and client_id = #{clientId}</if>
                    <if test="apiKey != null  and apiKey != ''"> and api_key = #{apiKey}</if>
                    <if test="version != null  and version != ''"> and version = #{version}</if>
                    <if test="sandbox != null "> and sandbox = #{sandbox}</if>
                    <if test="webhookId != null  and webhookId != ''"> and webhook_id = #{webhookId}</if>
                    <if test="enable != null "> and enable = #{enable}</if>
                </where>
    </select>



    <select id="summary" parameterType="tv.shorthub.common.core.domain.SummaryRequest" resultMap="AirwallexPaymentConfigResult">
        
    </select>
    <select id="allSummary" parameterType="tv.shorthub.common.core.domain.SummaryRequest" resultMap="AirwallexPaymentConfigResult">
        
    </select>

</mapper>
