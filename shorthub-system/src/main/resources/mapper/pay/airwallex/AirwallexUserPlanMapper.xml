<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tv.shorthub.system.mapper.AirwallexUserPlanMapper">

    <resultMap type="AirwallexUserPlan" id="AirwallexUserPlanResult">
        <result property="id"    column="id"    />
        <result property="orderNo"    column="order_no"    />
        <result property="paymentId"    column="payment_id"    />
        <result property="paymentConsentId"    column="payment_consent_id"    />
        <result property="period"    column="period"    />
        <result property="enabled"    column="enabled"    />
        <result property="extendData"    column="extend_data"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectAirwallexUserPlanVo">
        select id, order_no, payment_id, payment_consent_id, period, enabled, extend_data, create_by, create_time, update_by, update_time from airwallex_user_plan
    </sql>

    <insert id="batchInsertOrUpdate">
        <foreach item="item" index="index" collection="list" separator=";">
            insert into airwallex_user_plan
            <trim prefix="(" suffix=")" suffixOverrides=",">
                        <if test="item.orderNo != null and item.orderNo != ''">order_no,</if>
                        <if test="item.paymentId != null and item.paymentId != ''">payment_id,</if>
                        <if test="item.paymentConsentId != null and item.paymentConsentId != ''">payment_consent_id,</if>
                        <if test="item.period != null and item.period != ''">period,</if>
                        <if test="item.enabled != null">enabled,</if>
                        <if test="item.extendData != null">extend_data,</if>
                        <if test="item.createBy != null">create_by,</if>
                        <if test="item.createTime != null">create_time,</if>
                        <if test="item.updateBy != null">update_by,</if>
                        <if test="item.updateTime != null">update_time,</if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                        <if test="item.orderNo != null and item.orderNo != ''">#{item.orderNo},</if>
                        <if test="item.paymentId != null and item.paymentId != ''">#{item.paymentId},</if>
                        <if test="item.paymentConsentId != null and item.paymentConsentId != ''">#{item.paymentConsentId},</if>
                        <if test="item.period != null and item.period != ''">#{item.period},</if>
                        <if test="item.enabled != null">#{item.enabled},</if>
                        <if test="item.extendData != null">#{item.extendData},</if>
                        <if test="item.createBy != null">#{item.createBy},</if>
                        <if test="item.createTime != null">#{item.createTime},</if>
                        <if test="item.updateBy != null">#{item.updateBy},</if>
                        <if test="item.updateTime != null">#{item.updateTime},</if>
            </trim>
            on duplicate key update
            <trim suffixOverrides=",">
                        <if test="item.orderNo != null and item.orderNo != ''">order_no = values(order_no),</if>
                        <if test="item.paymentId != null and item.paymentId != ''">payment_id = values(payment_id),</if>
                        <if test="item.paymentConsentId != null and item.paymentConsentId != ''">payment_consent_id = values(payment_consent_id),</if>
                        <if test="item.period != null and item.period != ''">period = values(period),</if>
                        <if test="item.enabled != null">enabled = values(enabled),</if>
                        <if test="item.extendData != null">extend_data = values(extend_data),</if>
                        <if test="item.createBy != null">create_by = values(create_by),</if>
                        <if test="item.createTime != null">create_time = values(create_time),</if>
                        <if test="item.updateBy != null">update_by = values(update_by),</if>
                        <if test="item.updateTime != null">update_time = values(update_time),</if>
            </trim>
        </foreach>

    </insert>

    <select id="getSummary" parameterType="AirwallexUserPlan" resultMap="AirwallexUserPlanResult">
        select
            max(id) as id
        from airwallex_user_plan
        <where>
                    <if test="orderNo != null  and orderNo != ''"> and order_no = #{orderNo}</if>
                    <if test="paymentId != null  and paymentId != ''"> and payment_id = #{paymentId}</if>
                    <if test="paymentConsentId != null  and paymentConsentId != ''"> and payment_consent_id = #{paymentConsentId}</if>
                    <if test="period != null  and period != ''"> and period = #{period}</if>
                    <if test="enabled != null "> and enabled = #{enabled}</if>
                    <if test="extendData != null "> and extend_data = #{extendData}</if>
                </where>
    </select>



    <select id="summary" parameterType="tv.shorthub.common.core.domain.SummaryRequest" resultMap="AirwallexUserPlanResult">
        
    </select>
    <select id="allSummary" parameterType="tv.shorthub.common.core.domain.SummaryRequest" resultMap="AirwallexUserPlanResult">
        
    </select>

</mapper>
