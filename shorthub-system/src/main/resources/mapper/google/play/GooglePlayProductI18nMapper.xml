<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tv.shorthub.system.mapper.GooglePlayProductI18nMapper">

    <resultMap type="GooglePlayProductI18n" id="GooglePlayProductI18nResult">
        <result property="id"    column="id"    />
        <result property="productId"    column="product_id"    />
        <result property="language"    column="language"    />
        <result property="name"    column="name"    />
        <result property="description"    column="description"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
    </resultMap>

    <sql id="selectGooglePlayProductI18nVo">
        select id, product_id, language, name, description, create_time, update_time, create_by, update_by from google_play_product_i18n
    </sql>

    <insert id="batchInsertOrUpdate">
        <foreach item="item" index="index" collection="list" separator=";">
            insert into google_play_product_i18n
            <trim prefix="(" suffix=")" suffixOverrides=",">
                        <if test="item.productId != null">product_id,</if>
                        <if test="item.language != null and item.language != ''">language,</if>
                        <if test="item.name != null and item.name != ''">name,</if>
                        <if test="item.description != null">description,</if>
                        <if test="item.createTime != null">create_time,</if>
                        <if test="item.updateTime != null">update_time,</if>
                        <if test="item.createBy != null">create_by,</if>
                        <if test="item.updateBy != null">update_by,</if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                        <if test="item.productId != null">#{item.productId},</if>
                        <if test="item.language != null and item.language != ''">#{item.language},</if>
                        <if test="item.name != null and item.name != ''">#{item.name},</if>
                        <if test="item.description != null">#{item.description},</if>
                        <if test="item.createTime != null">#{item.createTime},</if>
                        <if test="item.updateTime != null">#{item.updateTime},</if>
                        <if test="item.createBy != null">#{item.createBy},</if>
                        <if test="item.updateBy != null">#{item.updateBy},</if>
            </trim>
            on duplicate key update
            <trim suffixOverrides=",">
                        <if test="item.productId != null">product_id = values(product_id),</if>
                        <if test="item.language != null and item.language != ''">language = values(language),</if>
                        <if test="item.name != null and item.name != ''">name = values(name),</if>
                        <if test="item.description != null">description = values(description),</if>
                        <if test="item.createTime != null">create_time = values(create_time),</if>
                        <if test="item.updateTime != null">update_time = values(update_time),</if>
                        <if test="item.createBy != null">create_by = values(create_by),</if>
                        <if test="item.updateBy != null">update_by = values(update_by),</if>
            </trim>
        </foreach>

    </insert>

    <select id="getSummary" parameterType="GooglePlayProductI18n" resultMap="GooglePlayProductI18nResult">
        select
            max(id) as id
        from google_play_product_i18n
        <where>
                    <if test="productId != null "> and product_id = #{productId}</if>
                    <if test="language != null  and language != ''"> and language = #{language}</if>
                    <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
                    <if test="description != null  and description != ''"> and description = #{description}</if>
                </where>
    </select>



    <select id="summary" parameterType="tv.shorthub.common.core.domain.SummaryRequest" resultMap="GooglePlayProductI18nResult">
        
    </select>
    <select id="allSummary" parameterType="tv.shorthub.common.core.domain.SummaryRequest" resultMap="GooglePlayProductI18nResult">
        
    </select>

</mapper>
