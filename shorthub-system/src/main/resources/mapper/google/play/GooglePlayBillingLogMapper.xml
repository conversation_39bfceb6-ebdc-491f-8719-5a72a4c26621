<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tv.shorthub.system.mapper.GooglePlayBillingLogMapper">

    <resultMap type="GooglePlayBillingLog" id="GooglePlayBillingLogResult">
        <result property="id"    column="id"    />
        <result property="orderNo"    column="order_no"    />
        <result property="packageName"    column="package_name"    />
        <result property="rawData"    column="raw_data"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectGooglePlayBillingLogVo">
        select id, order_no, package_name, raw_data, create_by, create_time, update_by, update_time from google_play_billing_log
    </sql>

    <insert id="batchInsertOrUpdate">
        <foreach item="item" index="index" collection="list" separator=";">
            insert into google_play_billing_log
            <trim prefix="(" suffix=")" suffixOverrides=",">
                        <if test="item.orderNo != null">order_no,</if>
                        <if test="item.packageName != null">package_name,</if>
                        <if test="item.rawData != null">raw_data,</if>
                        <if test="item.createBy != null">create_by,</if>
                        <if test="item.createTime != null">create_time,</if>
                        <if test="item.updateBy != null">update_by,</if>
                        <if test="item.updateTime != null">update_time,</if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                        <if test="item.orderNo != null">#{item.orderNo},</if>
                        <if test="item.packageName != null">#{item.packageName},</if>
                        <if test="item.rawData != null">#{item.rawData},</if>
                        <if test="item.createBy != null">#{item.createBy},</if>
                        <if test="item.createTime != null">#{item.createTime},</if>
                        <if test="item.updateBy != null">#{item.updateBy},</if>
                        <if test="item.updateTime != null">#{item.updateTime},</if>
            </trim>
            on duplicate key update
            <trim suffixOverrides=",">
                        <if test="item.orderNo != null">order_no = values(order_no),</if>
                        <if test="item.packageName != null">package_name = values(package_name),</if>
                        <if test="item.rawData != null">raw_data = values(raw_data),</if>
                        <if test="item.createBy != null">create_by = values(create_by),</if>
                        <if test="item.createTime != null">create_time = values(create_time),</if>
                        <if test="item.updateBy != null">update_by = values(update_by),</if>
                        <if test="item.updateTime != null">update_time = values(update_time),</if>
            </trim>
        </foreach>

    </insert>

    <select id="getSummary" parameterType="GooglePlayBillingLog" resultMap="GooglePlayBillingLogResult">
        select
            max(id) as id
        from google_play_billing_log
        <where>
                    <if test="orderNo != null  and orderNo != ''"> and order_no = #{orderNo}</if>
                    <if test="packageName != null  and packageName != ''"> and package_name like concat('%', #{packageName}, '%')</if>
                    <if test="rawData != null "> and raw_data = #{rawData}</if>
                </where>
    </select>



    <select id="summary" parameterType="tv.shorthub.common.core.domain.SummaryRequest" resultMap="GooglePlayBillingLogResult">
        
    </select>
    <select id="allSummary" parameterType="tv.shorthub.common.core.domain.SummaryRequest" resultMap="GooglePlayBillingLogResult">
        
    </select>

</mapper>
