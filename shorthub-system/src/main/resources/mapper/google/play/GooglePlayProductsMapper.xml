<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tv.shorthub.system.mapper.GooglePlayProductsMapper">

    <resultMap type="GooglePlayProducts" id="GooglePlayProductsResult">
        <result property="id"    column="id"    />
        <result property="productId"    column="product_id"    />
        <result property="packageName"    column="package_name"    />
        <result property="productType"    column="product_type"    />
        <result property="status"    column="status"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
    </resultMap>

    <sql id="selectGooglePlayProductsVo">
        select id, product_id, package_name, product_type, status, create_time, update_time, create_by, update_by from google_play_products
    </sql>

    <insert id="batchInsertOrUpdate">
        <foreach item="item" index="index" collection="list" separator=";">
            insert into google_play_products
            <trim prefix="(" suffix=")" suffixOverrides=",">
                        <if test="item.productId != null and item.productId != ''">product_id,</if>
                        <if test="item.packageName != null and item.packageName != ''">package_name,</if>
                        <if test="item.productType != null and item.productType != ''">product_type,</if>
                        <if test="item.status != null and item.status != ''">status,</if>
                        <if test="item.createTime != null">create_time,</if>
                        <if test="item.updateTime != null">update_time,</if>
                        <if test="item.createBy != null">create_by,</if>
                        <if test="item.updateBy != null">update_by,</if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                        <if test="item.productId != null and item.productId != ''">#{item.productId},</if>
                        <if test="item.packageName != null and item.packageName != ''">#{item.packageName},</if>
                        <if test="item.productType != null and item.productType != ''">#{item.productType},</if>
                        <if test="item.status != null and item.status != ''">#{item.status},</if>
                        <if test="item.createTime != null">#{item.createTime},</if>
                        <if test="item.updateTime != null">#{item.updateTime},</if>
                        <if test="item.createBy != null">#{item.createBy},</if>
                        <if test="item.updateBy != null">#{item.updateBy},</if>
            </trim>
            on duplicate key update
            <trim suffixOverrides=",">
                        <if test="item.productId != null and item.productId != ''">product_id = values(product_id),</if>
                        <if test="item.packageName != null and item.packageName != ''">package_name = values(package_name),</if>
                        <if test="item.productType != null and item.productType != ''">product_type = values(product_type),</if>
                        <if test="item.status != null and item.status != ''">status = values(status),</if>
                        <if test="item.createTime != null">create_time = values(create_time),</if>
                        <if test="item.updateTime != null">update_time = values(update_time),</if>
                        <if test="item.createBy != null">create_by = values(create_by),</if>
                        <if test="item.updateBy != null">update_by = values(update_by),</if>
            </trim>
        </foreach>

    </insert>

    <select id="getSummary" parameterType="GooglePlayProducts" resultMap="GooglePlayProductsResult">
        select
            max(id) as id
        from google_play_products
        <where>
                    <if test="productId != null  and productId != ''"> and product_id = #{productId}</if>
                    <if test="packageName != null  and packageName != ''"> and package_name like concat('%', #{packageName}, '%')</if>
                    <if test="productType != null  and productType != ''"> and product_type = #{productType}</if>
                    <if test="status != null  and status != ''"> and status = #{status}</if>
                </where>
    </select>



    <select id="summary" parameterType="tv.shorthub.common.core.domain.SummaryRequest" resultMap="GooglePlayProductsResult">
        
    </select>
    <select id="allSummary" parameterType="tv.shorthub.common.core.domain.SummaryRequest" resultMap="GooglePlayProductsResult">
        
    </select>

</mapper>
