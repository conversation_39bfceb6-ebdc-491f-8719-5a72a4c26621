<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tv.shorthub.system.mapper.GooglePlayProductPricesMapper">

    <resultMap type="GooglePlayProductPrices" id="GooglePlayProductPricesResult">
        <result property="id"    column="id"    />
        <result property="productId"    column="product_id"    />
        <result property="currencyCode"    column="currency_code"    />
        <result property="priceAmount"    column="price_amount"    />
        <result property="priceMicros"    column="price_micros"    />
        <result property="isDefault"    column="is_default"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
    </resultMap>

    <sql id="selectGooglePlayProductPricesVo">
        select id, product_id, currency_code, price_amount, price_micros, is_default, create_time, update_time, create_by, update_by from google_play_product_prices
    </sql>

    <insert id="batchInsertOrUpdate">
        <foreach item="item" index="index" collection="list" separator=";">
            insert into google_play_product_prices
            <trim prefix="(" suffix=")" suffixOverrides=",">
                        <if test="item.productId != null">product_id,</if>
                        <if test="item.currencyCode != null and item.currencyCode != ''">currency_code,</if>
                        <if test="item.priceAmount != null">price_amount,</if>
                        <if test="item.priceMicros != null">price_micros,</if>
                        <if test="item.isDefault != null">is_default,</if>
                        <if test="item.createTime != null">create_time,</if>
                        <if test="item.updateTime != null">update_time,</if>
                        <if test="item.createBy != null">create_by,</if>
                        <if test="item.updateBy != null">update_by,</if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                        <if test="item.productId != null">#{item.productId},</if>
                        <if test="item.currencyCode != null and item.currencyCode != ''">#{item.currencyCode},</if>
                        <if test="item.priceAmount != null">#{item.priceAmount},</if>
                        <if test="item.priceMicros != null">#{item.priceMicros},</if>
                        <if test="item.isDefault != null">#{item.isDefault},</if>
                        <if test="item.createTime != null">#{item.createTime},</if>
                        <if test="item.updateTime != null">#{item.updateTime},</if>
                        <if test="item.createBy != null">#{item.createBy},</if>
                        <if test="item.updateBy != null">#{item.updateBy},</if>
            </trim>
            on duplicate key update
            <trim suffixOverrides=",">
                        <if test="item.productId != null">product_id = values(product_id),</if>
                        <if test="item.currencyCode != null and item.currencyCode != ''">currency_code = values(currency_code),</if>
                        <if test="item.priceAmount != null">price_amount = values(price_amount),</if>
                        <if test="item.priceMicros != null">price_micros = values(price_micros),</if>
                        <if test="item.isDefault != null">is_default = values(is_default),</if>
                        <if test="item.createTime != null">create_time = values(create_time),</if>
                        <if test="item.updateTime != null">update_time = values(update_time),</if>
                        <if test="item.createBy != null">create_by = values(create_by),</if>
                        <if test="item.updateBy != null">update_by = values(update_by),</if>
            </trim>
        </foreach>

    </insert>

    <select id="getSummary" parameterType="GooglePlayProductPrices" resultMap="GooglePlayProductPricesResult">
        select
            max(id) as id
        from google_play_product_prices
        <where>
                    <if test="productId != null "> and product_id = #{productId}</if>
                    <if test="currencyCode != null  and currencyCode != ''"> and currency_code = #{currencyCode}</if>
                    <if test="priceAmount != null "> and price_amount = #{priceAmount}</if>
                    <if test="priceMicros != null "> and price_micros = #{priceMicros}</if>
                    <if test="isDefault != null "> and is_default = #{isDefault}</if>
                </where>
    </select>



    <select id="summary" parameterType="tv.shorthub.common.core.domain.SummaryRequest" resultMap="GooglePlayProductPricesResult">
        
    </select>
    <select id="allSummary" parameterType="tv.shorthub.common.core.domain.SummaryRequest" resultMap="GooglePlayProductPricesResult">
        
    </select>

</mapper>
