<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tv.shorthub.system.mapper.GooglePlaySubscriptionConfigMapper">

    <resultMap type="GooglePlaySubscriptionConfig" id="GooglePlaySubscriptionConfigResult">
        <result property="id"    column="id"    />
        <result property="basePlanId"    column="base_plan_id"    />
        <result property="subscriptionPeriod"    column="subscription_period"    />
        <result property="freeTrialPeriod"    column="free_trial_period"    />
        <result property="introductoryPricePeriod"    column="introductory_price_period"    />
        <result property="introductoryPriceAmount"    column="introductory_price_amount"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
    </resultMap>

    <sql id="selectGooglePlaySubscriptionConfigVo">
        select id, base_plan_id, subscription_period, free_trial_period, introductory_price_period, introductory_price_amount, create_time, update_time, create_by, update_by from google_play_subscription_config
    </sql>

    <select id="getSummary" parameterType="GooglePlaySubscriptionConfig" resultMap="GooglePlaySubscriptionConfigResult">
        select
            max(id) as id
        from google_play_subscription_config
        <where>
                    <if test="basePlanId != null "> and base_plan_id = #{basePlanId}</if>
                    <if test="subscriptionPeriod != null  and subscriptionPeriod != ''"> and subscription_period = #{subscriptionPeriod}</if>
                    <if test="freeTrialPeriod != null  and freeTrialPeriod != ''"> and free_trial_period = #{freeTrialPeriod}</if>
                    <if test="introductoryPricePeriod != null  and introductoryPricePeriod != ''"> and introductory_price_period = #{introductoryPricePeriod}</if>
                    <if test="introductoryPriceAmount != null "> and introductory_price_amount = #{introductoryPriceAmount}</if>
                </where>
    </select>



    <select id="summary" parameterType="tv.shorthub.common.core.domain.SummaryRequest" resultMap="GooglePlaySubscriptionConfigResult">
        
    </select>
    <select id="allSummary" parameterType="tv.shorthub.common.core.domain.SummaryRequest" resultMap="GooglePlaySubscriptionConfigResult">
        
    </select>

</mapper>
