<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tv.shorthub.system.mapper.PayermaxPaymentLogMapper">

    <resultMap type="PayermaxPaymentLog" id="PayermaxPaymentLogResult">
        <result property="id"    column="id"    />
        <result property="orderNo"    column="order_no"    />
        <result property="tradeToken"    column="trade_token"    />
        <result property="mcnId"    column="mcn_id"    />
        <result property="userId"    column="user_id"    />
        <result property="status"    column="status"    />
        <result property="totalAmount"    column="total_amount"    />
        <result property="currency"    column="currency"    />
        <result property="description"    column="description"    />
        <result property="returnUrl"    column="return_url"    />
        <result property="cancelUrl"    column="cancel_url"    />
        <result property="paymentMethod"    column="payment_method"    />
        <result property="rawData"    column="raw_data"    />
        <result property="relatedUserId"    column="related_user_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectPayermaxPaymentLogVo">
        select id, order_no, trade_token, mcn_id, user_id, status, total_amount, currency, description, return_url, cancel_url, payment_method, raw_data, related_user_id, create_by, create_time, update_by, update_time from payermax_payment_log
    </sql>

    <insert id="batchInsertOrUpdate">
        <foreach item="item" index="index" collection="list" separator=";">
            insert into payermax_payment_log
            <trim prefix="(" suffix=")" suffixOverrides=",">
                        <if test="item.orderNo != null">order_no,</if>
                        <if test="item.tradeToken != null and item.tradeToken != ''">trade_token,</if>
                        <if test="item.mcnId != null">mcn_id,</if>
                        <if test="item.userId != null">user_id,</if>
                        <if test="item.status != null and item.status != ''">status,</if>
                        <if test="item.totalAmount != null">total_amount,</if>
                        <if test="item.currency != null and item.currency != ''">currency,</if>
                        <if test="item.description != null">description,</if>
                        <if test="item.returnUrl != null and item.returnUrl != ''">return_url,</if>
                        <if test="item.cancelUrl != null and item.cancelUrl != ''">cancel_url,</if>
                        <if test="item.paymentMethod != null and item.paymentMethod != ''">payment_method,</if>
                        <if test="item.rawData != null">raw_data,</if>
                        <if test="item.relatedUserId != null">related_user_id,</if>
                        <if test="item.createBy != null">create_by,</if>
                        <if test="item.createTime != null">create_time,</if>
                        <if test="item.updateBy != null">update_by,</if>
                        <if test="item.updateTime != null">update_time,</if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                        <if test="item.orderNo != null">#{item.orderNo},</if>
                        <if test="item.tradeToken != null and item.tradeToken != ''">#{item.tradeToken},</if>
                        <if test="item.mcnId != null">#{item.mcnId},</if>
                        <if test="item.userId != null">#{item.userId},</if>
                        <if test="item.status != null and item.status != ''">#{item.status},</if>
                        <if test="item.totalAmount != null">#{item.totalAmount},</if>
                        <if test="item.currency != null and item.currency != ''">#{item.currency},</if>
                        <if test="item.description != null">#{item.description},</if>
                        <if test="item.returnUrl != null and item.returnUrl != ''">#{item.returnUrl},</if>
                        <if test="item.cancelUrl != null and item.cancelUrl != ''">#{item.cancelUrl},</if>
                        <if test="item.paymentMethod != null and item.paymentMethod != ''">#{item.paymentMethod},</if>
                        <if test="item.rawData != null">#{item.rawData},</if>
                        <if test="item.relatedUserId != null">#{item.relatedUserId},</if>
                        <if test="item.createBy != null">#{item.createBy},</if>
                        <if test="item.createTime != null">#{item.createTime},</if>
                        <if test="item.updateBy != null">#{item.updateBy},</if>
                        <if test="item.updateTime != null">#{item.updateTime},</if>
            </trim>
            on duplicate key update
            <trim suffixOverrides=",">
                        <if test="item.orderNo != null">order_no = values(order_no),</if>
                        <if test="item.tradeToken != null and item.tradeToken != ''">trade_token = values(trade_token),</if>
                        <if test="item.mcnId != null">mcn_id = values(mcn_id),</if>
                        <if test="item.userId != null">user_id = values(user_id),</if>
                        <if test="item.status != null and item.status != ''">status = values(status),</if>
                        <if test="item.totalAmount != null">total_amount = values(total_amount),</if>
                        <if test="item.currency != null and item.currency != ''">currency = values(currency),</if>
                        <if test="item.description != null">description = values(description),</if>
                        <if test="item.returnUrl != null and item.returnUrl != ''">return_url = values(return_url),</if>
                        <if test="item.cancelUrl != null and item.cancelUrl != ''">cancel_url = values(cancel_url),</if>
                        <if test="item.paymentMethod != null and item.paymentMethod != ''">payment_method = values(payment_method),</if>
                        <if test="item.rawData != null">raw_data = values(raw_data),</if>
                        <if test="item.relatedUserId != null">related_user_id = values(related_user_id),</if>
                        <if test="item.createBy != null">create_by = values(create_by),</if>
                        <if test="item.createTime != null">create_time = values(create_time),</if>
                        <if test="item.updateBy != null">update_by = values(update_by),</if>
                        <if test="item.updateTime != null">update_time = values(update_time),</if>
            </trim>
        </foreach>

    </insert>

    <select id="getSummary" parameterType="PayermaxPaymentLog" resultMap="PayermaxPaymentLogResult">
        select
            max(id) as id
        from payermax_payment_log
        <where>
                    <if test="orderNo != null  and orderNo != ''"> and order_no = #{orderNo}</if>
                    <if test="tradeToken != null  and tradeToken != ''"> and trade_token = #{tradeToken}</if>
                    <if test="mcnId != null  and mcnId != ''"> and mcn_id = #{mcnId}</if>
                    <if test="userId != null  and userId != ''"> and user_id = #{userId}</if>
                    <if test="status != null  and status != ''"> and status = #{status}</if>
                    <if test="totalAmount != null "> and total_amount = #{totalAmount}</if>
                    <if test="currency != null  and currency != ''"> and currency = #{currency}</if>
                    <if test="description != null  and description != ''"> and description = #{description}</if>
                    <if test="returnUrl != null  and returnUrl != ''"> and return_url = #{returnUrl}</if>
                    <if test="cancelUrl != null  and cancelUrl != ''"> and cancel_url = #{cancelUrl}</if>
                    <if test="paymentMethod != null  and paymentMethod != ''"> and payment_method = #{paymentMethod}</if>
                    <if test="rawData != null "> and raw_data = #{rawData}</if>
                    <if test="relatedUserId != null  and relatedUserId != ''"> and related_user_id = #{relatedUserId}</if>
                </where>
    </select>



    <select id="summary" parameterType="tv.shorthub.common.core.domain.SummaryRequest" resultMap="PayermaxPaymentLogResult">
        
    </select>
    <select id="allSummary" parameterType="tv.shorthub.common.core.domain.SummaryRequest" resultMap="PayermaxPaymentLogResult">
        
    </select>

</mapper>
