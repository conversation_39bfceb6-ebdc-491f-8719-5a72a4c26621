<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tv.shorthub.system.mapper.StatisticsHourlyDramaRechargeStatsMapper">

    <resultMap type="StatisticsHourlyDramaRechargeStats" id="StatisticsHourlyDramaRechargeStatsResult">
        <result property="id"    column="id"    />
        <result property="statHour"    column="stat_hour"    />
        <result property="contentId"    column="content_id"    />
        <result property="dramaId"    column="drama_id"    />
        <result property="appid"    column="appid"    />
        <result property="tfid"    column="tfid"    />
        <result property="rechargeUserCount"    column="recharge_user_count"    />
        <result property="rechargeOrderCount"    column="recharge_order_count"    />
        <result property="rechargeAmount"    column="recharge_amount"    />
        <result property="coinRechargeCount"    column="coin_recharge_count"    />
        <result property="coinRechargeAmount"    column="coin_recharge_amount"    />
        <result property="subscriptionRechargeCount"    column="subscription_recharge_count"    />
        <result property="subscriptionRechargeAmount"    column="subscription_recharge_amount"    />
        <result property="consumptionUserCount"    column="consumption_user_count"    />
        <result property="consumptionOrderCount"    column="consumption_order_count"    />
        <result property="coinConsumptionAmount"    column="coin_consumption_amount"    />
        <result property="unlockEpisodeCount"    column="unlock_episode_count"    />
        <result property="rechargeToConsumptionRate"    column="recharge_to_consumption_rate"    />
        <result property="avgRechargeAmount"    column="avg_recharge_amount"    />
        <result property="avgConsumptionAmount"    column="avg_consumption_amount"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <!-- 带剧目信息的 DTO ResultMap -->
    <resultMap type="tv.shorthub.system.domain.dto.StatisticsHourlyDramaRechargeStatsDTO" id="StatisticsHourlyDramaRechargeStatsDTOResult" extends="StatisticsHourlyDramaRechargeStatsResult">
        <result property="dramaTitle"    column="drama_title"    />
        <result property="contentTitle"    column="content_title"    />
        <result property="tfidDisplayName"    column="tfid_display_name"    />
    </resultMap>

    <sql id="selectStatisticsHourlyDramaRechargeStatsVo">
        select id, stat_hour, content_id, drama_id, appid, tfid, recharge_user_count, recharge_order_count, recharge_amount, coin_recharge_count, coin_recharge_amount, subscription_recharge_count, subscription_recharge_amount, consumption_user_count, consumption_order_count, coin_consumption_amount, unlock_episode_count, recharge_to_consumption_rate, avg_recharge_amount, avg_consumption_amount, create_by, create_time, update_by, update_time from statistics_hourly_drama_recharge_stats
    </sql>

    <insert id="batchInsertOrUpdateStats">
        INSERT INTO statistics_hourly_drama_recharge_stats
        (stat_hour, content_id, drama_id, appid, tfid, recharge_user_count, recharge_order_count, recharge_amount,
        coin_recharge_count, coin_recharge_amount, subscription_recharge_count, subscription_recharge_amount,
        consumption_user_count, consumption_order_count, coin_consumption_amount, unlock_episode_count,
        recharge_to_consumption_rate, avg_recharge_amount, avg_consumption_amount,
        create_by, create_time, update_by, update_time)
        VALUES
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.statHour}, #{item.contentId}, #{item.dramaId}, #{item.appid}, COALESCE(#{item.tfid}, ''),
            #{item.rechargeUserCount}, #{item.rechargeOrderCount}, #{item.rechargeAmount},
            #{item.coinRechargeCount}, #{item.coinRechargeAmount}, #{item.subscriptionRechargeCount}, #{item.subscriptionRechargeAmount},
            #{item.consumptionUserCount}, #{item.consumptionOrderCount}, #{item.coinConsumptionAmount}, #{item.unlockEpisodeCount},
            #{item.rechargeToConsumptionRate}, #{item.avgRechargeAmount}, #{item.avgConsumptionAmount},
            #{item.createBy}, #{item.createTime}, #{item.updateBy}, #{item.updateTime})
        </foreach>
        ON DUPLICATE KEY UPDATE
        recharge_user_count = VALUES(recharge_user_count),
        recharge_order_count = VALUES(recharge_order_count),
        recharge_amount = VALUES(recharge_amount),
        coin_recharge_count = VALUES(coin_recharge_count),
        coin_recharge_amount = VALUES(coin_recharge_amount),
        subscription_recharge_count = VALUES(subscription_recharge_count),
        subscription_recharge_amount = VALUES(subscription_recharge_amount),
        consumption_user_count = VALUES(consumption_user_count),
        consumption_order_count = VALUES(consumption_order_count),
        coin_consumption_amount = VALUES(coin_consumption_amount),
        unlock_episode_count = VALUES(unlock_episode_count),
        recharge_to_consumption_rate = VALUES(recharge_to_consumption_rate),
        avg_recharge_amount = VALUES(avg_recharge_amount),
        avg_consumption_amount = VALUES(avg_consumption_amount),
        update_by = VALUES(update_by),
        update_time = VALUES(update_time)
    </insert>

    <select id="getSummary" parameterType="StatisticsHourlyDramaRechargeStats" resultMap="StatisticsHourlyDramaRechargeStatsResult">
        WITH filtered_stats AS (
        SELECT *
        FROM statistics_hourly_drama_recharge_stats
        <where>
            <if test="statHour != null "> and stat_hour = #{statHour}</if>
            <if test="contentId != null  and contentId != ''"> and content_id = #{contentId}</if>
            <if test="dramaId != null  and dramaId != ''"> and drama_id = #{dramaId}</if>
            <if test="appid != null  and appid != ''"> and appid = #{appid}</if>
            <if test="tfid != null">
                <choose>
                    <when test="tfid == ''">and (tfid IS NULL or tfid = '')</when>
                    <otherwise>and tfid = #{tfid}</otherwise>
                </choose>
            </if>
            <if test="rechargeUserCount != null "> and recharge_user_count = #{rechargeUserCount}</if>
            <if test="rechargeOrderCount != null "> and recharge_order_count = #{rechargeOrderCount}</if>
            <if test="rechargeAmount != null "> and recharge_amount = #{rechargeAmount}</if>
            <if test="coinRechargeCount != null "> and coin_recharge_count = #{coinRechargeCount}</if>
            <if test="coinRechargeAmount != null "> and coin_recharge_amount = #{coinRechargeAmount}</if>
            <if test="subscriptionRechargeCount != null "> and subscription_recharge_count = #{subscriptionRechargeCount}</if>
            <if test="subscriptionRechargeAmount != null "> and subscription_recharge_amount = #{subscriptionRechargeAmount}</if>
            <if test="consumptionUserCount != null "> and consumption_user_count = #{consumptionUserCount}</if>
            <if test="consumptionOrderCount != null "> and consumption_order_count = #{consumptionOrderCount}</if>
            <if test="coinConsumptionAmount != null "> and coin_consumption_amount = #{coinConsumptionAmount}</if>
            <if test="unlockEpisodeCount != null "> and unlock_episode_count = #{unlockEpisodeCount}</if>
            <if test="rechargeToConsumptionRate != null "> and recharge_to_consumption_rate = #{rechargeToConsumptionRate}</if>
            <if test="avgRechargeAmount != null "> and avg_recharge_amount = #{avgRechargeAmount}</if>
            <if test="avgConsumptionAmount != null "> and avg_consumption_amount = #{avgConsumptionAmount}</if>
            <if test="createBy != null  and createBy != ''"> and create_by = #{createBy}</if>
            <if test="updateBy != null  and updateBy != ''"> and update_by = #{updateBy}</if>
        </where>
        )
        SELECT
        NULL as id,
        NULL as stat_hour,
        NULL as content_id,
        NULL as drama_id,
        NULL as appid,
        NULL as tfid,
        SUM(recharge_user_count) AS recharge_user_count,
        SUM(recharge_order_count) AS recharge_order_count,
        SUM(recharge_amount) AS recharge_amount,
        SUM(coin_recharge_count) AS coin_recharge_count,
        SUM(coin_recharge_amount) AS coin_recharge_amount,
        SUM(subscription_recharge_count) AS subscription_recharge_count,
        SUM(subscription_recharge_amount) AS subscription_recharge_amount,
        SUM(consumption_user_count) AS consumption_user_count,
        SUM(consumption_order_count) AS consumption_order_count,
        SUM(coin_consumption_amount) AS coin_consumption_amount,
        SUM(unlock_episode_count) AS unlock_episode_count,
        CASE
        WHEN SUM(recharge_user_count) &gt; 0
        THEN ROUND(SUM(consumption_user_count) * 1.0 / SUM(recharge_user_count), 4)
        ELSE 0
        END as recharge_to_consumption_rate,
        CASE
        WHEN SUM(recharge_order_count) &gt; 0
        THEN ROUND(SUM(recharge_amount) / SUM(recharge_order_count), 4)
        ELSE 0
        END as avg_recharge_amount,
        CASE
        WHEN SUM(consumption_order_count) &gt; 0
        THEN ROUND(SUM(coin_consumption_amount) / SUM(consumption_order_count), 4)
        ELSE 0
        END as avg_consumption_amount
        FROM filtered_stats
    </select>
    <insert id="batchInsertOrUpdate">
        <foreach item="item" index="index" collection="list" separator=";">
            insert into statistics_hourly_drama_recharge_stats
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="item.statHour != null">stat_hour,</if>
                <if test="item.contentId != null and item.contentId != ''">content_id,</if>
                <if test="item.dramaId != null and item.dramaId != ''">drama_id,</if>
                <if test="item.appid != null">appid,</if>
                <if test="item.tfid != null and item.tfid != ''">tfid,</if>
                <if test="item.rechargeUserCount != null">recharge_user_count,</if>
                <if test="item.rechargeOrderCount != null">recharge_order_count,</if>
                <if test="item.rechargeAmount != null">recharge_amount,</if>
                <if test="item.coinRechargeCount != null">coin_recharge_count,</if>
                <if test="item.coinRechargeAmount != null">coin_recharge_amount,</if>
                <if test="item.subscriptionRechargeCount != null">subscription_recharge_count,</if>
                <if test="item.subscriptionRechargeAmount != null">subscription_recharge_amount,</if>
                <if test="item.consumptionUserCount != null">consumption_user_count,</if>
                <if test="item.consumptionOrderCount != null">consumption_order_count,</if>
                <if test="item.coinConsumptionAmount != null">coin_consumption_amount,</if>
                <if test="item.unlockEpisodeCount != null">unlock_episode_count,</if>
                <if test="item.rechargeToConsumptionRate != null">recharge_to_consumption_rate,</if>
                <if test="item.avgRechargeAmount != null">avg_recharge_amount,</if>
                <if test="item.avgConsumptionAmount != null">avg_consumption_amount,</if>
                <if test="item.createBy != null">create_by,</if>
                <if test="item.createTime != null">create_time,</if>
                <if test="item.updateBy != null">update_by,</if>
                <if test="item.updateTime != null">update_time,</if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="item.statHour != null">#{item.statHour},</if>
                <if test="item.contentId != null and item.contentId != ''">#{item.contentId},</if>
                <if test="item.dramaId != null and item.dramaId != ''">#{item.dramaId},</if>
                <if test="item.appid != null">#{item.appid},</if>
                <if test="item.tfid != null and item.tfid != ''">#{item.tfid},</if>
                <if test="item.rechargeUserCount != null">#{item.rechargeUserCount},</if>
                <if test="item.rechargeOrderCount != null">#{item.rechargeOrderCount},</if>
                <if test="item.rechargeAmount != null">#{item.rechargeAmount},</if>
                <if test="item.coinRechargeCount != null">#{item.coinRechargeCount},</if>
                <if test="item.coinRechargeAmount != null">#{item.coinRechargeAmount},</if>
                <if test="item.subscriptionRechargeCount != null">#{item.subscriptionRechargeCount},</if>
                <if test="item.subscriptionRechargeAmount != null">#{item.subscriptionRechargeAmount},</if>
                <if test="item.consumptionUserCount != null">#{item.consumptionUserCount},</if>
                <if test="item.consumptionOrderCount != null">#{item.consumptionOrderCount},</if>
                <if test="item.coinConsumptionAmount != null">#{item.coinConsumptionAmount},</if>
                <if test="item.unlockEpisodeCount != null">#{item.unlockEpisodeCount},</if>
                <if test="item.rechargeToConsumptionRate != null">#{item.rechargeToConsumptionRate},</if>
                <if test="item.avgRechargeAmount != null">#{item.avgRechargeAmount},</if>
                <if test="item.avgConsumptionAmount != null">#{item.avgConsumptionAmount},</if>
                <if test="item.createBy != null">#{item.createBy},</if>
                <if test="item.createTime != null">#{item.createTime},</if>
                <if test="item.updateBy != null">#{item.updateBy},</if>
                <if test="item.updateTime != null">#{item.updateTime},</if>
            </trim>
            on duplicate key update
            <trim suffixOverrides=",">
                <if test="item.statHour != null">stat_hour = values(stat_hour),</if>
                <if test="item.contentId != null and item.contentId != ''">content_id = values(content_id),</if>
                <if test="item.dramaId != null and item.dramaId != ''">drama_id = values(drama_id),</if>
                <if test="item.appid != null">appid = values(appid),</if>
                <if test="item.tfid != null and item.tfid != ''">tfid = values(tfid),</if>
                <if test="item.rechargeUserCount != null">recharge_user_count = values(recharge_user_count),</if>
                <if test="item.rechargeOrderCount != null">recharge_order_count = values(recharge_order_count),</if>
                <if test="item.rechargeAmount != null">recharge_amount = values(recharge_amount),</if>
                <if test="item.coinRechargeCount != null">coin_recharge_count = values(coin_recharge_count),</if>
                <if test="item.coinRechargeAmount != null">coin_recharge_amount = values(coin_recharge_amount),</if>
                <if test="item.subscriptionRechargeCount != null">subscription_recharge_count = values(subscription_recharge_count),</if>
                <if test="item.subscriptionRechargeAmount != null">subscription_recharge_amount = values(subscription_recharge_amount),</if>
                <if test="item.consumptionUserCount != null">consumption_user_count = values(consumption_user_count),</if>
                <if test="item.consumptionOrderCount != null">consumption_order_count = values(consumption_order_count),</if>
                <if test="item.coinConsumptionAmount != null">coin_consumption_amount = values(coin_consumption_amount),</if>
                <if test="item.unlockEpisodeCount != null">unlock_episode_count = values(unlock_episode_count),</if>
                <if test="item.rechargeToConsumptionRate != null">recharge_to_consumption_rate = values(recharge_to_consumption_rate),</if>
                <if test="item.avgRechargeAmount != null">avg_recharge_amount = values(avg_recharge_amount),</if>
                <if test="item.avgConsumptionAmount != null">avg_consumption_amount = values(avg_consumption_amount),</if>
                <if test="item.createBy != null">create_by = values(create_by),</if>
                <if test="item.createTime != null">create_time = values(create_time),</if>
                <if test="item.updateBy != null">update_by = values(update_by),</if>
                <if test="item.updateTime != null">update_time = values(update_time),</if>
            </trim>
        </foreach>

    </insert>
    <!-- 根据时间范围查询每小时剧目充值统计数据 -->
    <select id="selectHourlyDramaRechargeStats" resultMap="StatisticsHourlyDramaRechargeStatsResult">
        WITH recharge_stats AS (
            SELECT
                DATE_FORMAT(o.create_time, '%Y-%m-%d %H:00:00') as stat_hour,
                COALESCE(o.appid, 'default') as appid,
                COALESCE(o.tfid, '') as tfid,
                o.content_id,
                dc.drama_id,
                COUNT(DISTINCT o.user_id) as recharge_user_count,
                COUNT(*) as recharge_order_count,
                SUM(o.order_amount) as recharge_amount,
                SUM(CASE WHEN o.pay_type = '1' THEN 1 ELSE 0 END) as coin_recharge_count,
                SUM(CASE WHEN o.pay_type = '1' THEN o.order_amount ELSE 0 END) as coin_recharge_amount,
                SUM(CASE WHEN o.pay_type = '2' THEN 1 ELSE 0 END) as subscription_recharge_count,
                SUM(CASE WHEN o.pay_type = '2' THEN o.order_amount ELSE 0 END) as subscription_recharge_amount
            FROM app_order_info o
                     INNER JOIN app_drama_contents dc ON o.content_id = dc.content_id
            WHERE o.order_status = 1
              AND o.pay_time IS NOT NULL
              AND o.content_id IS NOT NULL
              AND o.create_time BETWEEN #{startTime} AND #{endTime}
            GROUP BY DATE_FORMAT(o.create_time, '%Y-%m-%d %H:00:00'), o.appid, o.tfid, o.content_id, dc.drama_id
        ),
             consumption_stats AS (
                 SELECT
                     DATE_FORMAT(c.create_time, '%Y-%m-%d %H:00:00') as stat_hour,
                     COALESCE(c.appid, 'default') as appid,
                     COALESCE(first_order.tfid, '') as tfid,  -- 关联用户首次充值的推广链接
                     c.content_id,
                     dc.drama_id,
                     COUNT(DISTINCT c.user_id) as consumption_user_count,
                     COUNT(*) as consumption_order_count,
                     CAST(SUM(ABS(c.amount)) AS DECIMAL(20,2)) as coin_consumption_amount,
                     COUNT(DISTINCT CONCAT(c.content_id, '_', c.serial_number)) as unlock_episode_count
                 FROM app_consumption c
                          INNER JOIN app_drama_contents dc ON c.content_id = dc.content_id
                          LEFT JOIN (
                              -- 获取每个用户的首次成功充值推广链接
                              SELECT 
                                  user_id, 
                                  tfid,
                                  ROW_NUMBER() OVER(PARTITION BY user_id ORDER BY create_time ASC) as rn
                              FROM app_order_info 
                              WHERE order_status = 1 AND pay_time IS NOT NULL
                          ) first_order ON c.user_id = first_order.user_id AND first_order.rn = 1
                 WHERE c.amount &lt; 0
                   AND c.content_id IS NOT NULL
                   AND c.create_time BETWEEN #{startTime} AND #{endTime}
                 GROUP BY DATE_FORMAT(c.create_time, '%Y-%m-%d %H:00:00'), COALESCE(c.appid, 'default'), COALESCE(first_order.tfid, ''), c.content_id, dc.drama_id
             ),
             recharge_and_consumption_users AS (
                 SELECT
                     DATE_FORMAT(o.create_time, '%Y-%m-%d %H:00:00') as stat_hour,
                     COALESCE(o.appid, 'default') as appid,
                     COALESCE(o.tfid, '') as tfid,
                     o.content_id,
                     dc.drama_id,
                     COUNT(DISTINCT o.user_id) as recharge_and_consumption_user_count
                 FROM app_order_info o
                          INNER JOIN app_drama_contents dc ON o.content_id = dc.content_id
                          INNER JOIN app_consumption c ON o.user_id = c.user_id
                     AND o.content_id = c.content_id
                     AND DATE_FORMAT(o.create_time, '%Y-%m-%d %H:00:00') = DATE_FORMAT(c.create_time, '%Y-%m-%d %H:00:00')
                 WHERE o.order_status = 1
                   AND o.pay_time IS NOT NULL
                   AND o.content_id IS NOT NULL
                   AND c.amount &lt; 0
                   AND o.create_time BETWEEN #{startTime} AND #{endTime}
                 GROUP BY DATE_FORMAT(o.create_time, '%Y-%m-%d %H:00:00'), o.appid, o.tfid, o.content_id, dc.drama_id
             )
        -- 合并充值和消费数据，确保两者都能被完整统计
        , combined_stats AS (
            SELECT 
                rs.stat_hour,
                rs.content_id,
                rs.drama_id,
                rs.appid,
                rs.tfid,
                rs.recharge_user_count,
                rs.recharge_order_count,
                rs.recharge_amount,
                rs.coin_recharge_count,
                rs.coin_recharge_amount,
                rs.subscription_recharge_count,
                rs.subscription_recharge_amount,
                COALESCE(cs.consumption_user_count, 0) as consumption_user_count,
                COALESCE(cs.consumption_order_count, 0) as consumption_order_count,
                COALESCE(cs.coin_consumption_amount, 0) as coin_consumption_amount,
                COALESCE(cs.unlock_episode_count, 0) as unlock_episode_count
            FROM recharge_stats rs
            LEFT JOIN consumption_stats cs ON rs.stat_hour = cs.stat_hour
                AND rs.content_id = cs.content_id
                AND rs.appid = cs.appid
            
            UNION
            
            SELECT 
                cs.stat_hour,
                cs.content_id,
                cs.drama_id,
                cs.appid,
                '' as tfid,
                0 as recharge_user_count,
                0 as recharge_order_count,
                0 as recharge_amount,
                0 as coin_recharge_count,
                0 as coin_recharge_amount,
                0 as subscription_recharge_count,
                0 as subscription_recharge_amount,
                cs.consumption_user_count,
                cs.consumption_order_count,
                cs.coin_consumption_amount,
                cs.unlock_episode_count
            FROM consumption_stats cs
            LEFT JOIN recharge_stats rs ON rs.stat_hour = cs.stat_hour
                AND rs.content_id = cs.content_id
                AND rs.appid = cs.appid
            WHERE rs.content_id IS NULL
        )
        SELECT
            STR_TO_DATE(combined.stat_hour, '%Y-%m-%d %H:%i:%s') as stat_hour,
            combined.content_id,
            combined.drama_id,
            combined.appid,
            combined.tfid,
            SUM(combined.recharge_user_count) as recharge_user_count,
            SUM(combined.recharge_order_count) as recharge_order_count,
            SUM(combined.recharge_amount) as recharge_amount,
            SUM(combined.coin_recharge_count) as coin_recharge_count,
            SUM(combined.coin_recharge_amount) as coin_recharge_amount,
            SUM(combined.subscription_recharge_count) as subscription_recharge_count,
            SUM(combined.subscription_recharge_amount) as subscription_recharge_amount,
            SUM(combined.consumption_user_count) as consumption_user_count,
            SUM(combined.consumption_order_count) as consumption_order_count,
            CAST(SUM(combined.coin_consumption_amount) AS DECIMAL(20,2)) as coin_consumption_amount,
            SUM(combined.unlock_episode_count) as unlock_episode_count,
            CASE
                WHEN SUM(combined.recharge_user_count) &gt; 0 AND SUM(combined.consumption_user_count) &gt; 0
                    THEN ROUND(SUM(combined.consumption_user_count) * 1.0 / SUM(combined.recharge_user_count), 4)
                ELSE 0
                END as recharge_to_consumption_rate,
            CASE
                WHEN SUM(combined.recharge_order_count) &gt; 0
                    THEN ROUND(SUM(combined.recharge_amount) / SUM(combined.recharge_order_count), 4)
                ELSE 0
                END as avg_recharge_amount,
            CASE
                WHEN SUM(combined.consumption_order_count) &gt; 0
                    THEN ROUND(CAST(SUM(combined.coin_consumption_amount) AS DECIMAL(20,2)) / SUM(combined.consumption_order_count), 4)
                ELSE 0
                END as avg_consumption_amount,
            'system' as create_by,
            NOW() as create_time,
            'system' as update_by,
            NOW() as update_time
        FROM combined_stats combined
        GROUP BY combined.stat_hour, combined.content_id, combined.drama_id, combined.appid, combined.tfid
        ORDER BY combined.stat_hour, combined.content_id
    </select>

    <!-- 根据时间范围获取剧目充值统计汇总数据 -->
    <select id="getRangeSummary" resultMap="StatisticsHourlyDramaRechargeStatsResult">
        WITH filtered_stats AS (
        SELECT *
        FROM statistics_hourly_drama_recharge_stats
        WHERE stat_hour BETWEEN #{startTime} AND #{endTime}
        <if test="appid != null and appid != ''">
            AND appid = #{appid}
        </if>
        <if test="tfid != null">
            <choose>
                <when test="tfid == ''">AND (tfid IS NULL or tfid = '')</when>
                <otherwise>AND tfid = #{tfid}</otherwise>
            </choose>
        </if>
        <if test="contentId != null and contentId != ''">
            AND content_id = #{contentId}
        </if>
        <if test="dramaId != null and dramaId != ''">
            AND drama_id = #{dramaId}
        </if>
        ),
        real_recharge_users AS (
        SELECT COUNT(DISTINCT o.user_id) as real_recharge_user_count
        FROM app_order_info o
        INNER JOIN app_drama_contents dc ON o.content_id = dc.content_id
        WHERE o.order_status = 1
          AND o.pay_time IS NOT NULL
          AND o.content_id IS NOT NULL
          AND o.create_time BETWEEN #{startTime} AND #{endTime}
        <if test="appid != null and appid != '' and appid != 'all'">
            AND o.appid = #{appid}
        </if>
        <if test="tfid != null and tfid != ''">
            AND o.tfid = #{tfid}
        </if>
        <if test="contentId != null and contentId != ''">
            AND o.content_id = #{contentId}
        </if>
        <if test="dramaId != null and dramaId != ''">
            AND dc.drama_id = #{dramaId}
        </if>
        ),
        real_consumption_users AS (
        SELECT COUNT(DISTINCT c.user_id) as real_consumption_user_count
        FROM app_consumption c
        INNER JOIN app_drama_contents dc ON c.content_id = dc.content_id
        INNER JOIN app_order_info o ON c.user_id = o.user_id AND dc.content_id = o.content_id
        WHERE c.amount &lt; 0
          AND c.content_id IS NOT NULL
          AND c.create_time BETWEEN #{startTime} AND #{endTime}
          AND o.order_status = 1
          AND o.pay_time IS NOT NULL
          AND o.create_time BETWEEN #{startTime} AND #{endTime}
        <if test="appid != null and appid != '' and appid != 'all'">
            AND c.appid = #{appid}
        </if>
        <if test="contentId != null and contentId != ''">
            AND c.content_id = #{contentId}
        </if>
        <if test="dramaId != null and dramaId != ''">
            AND dc.drama_id = #{dramaId}
        </if>
        ),
        real_unlock_stats AS (
        SELECT COUNT(DISTINCT CONCAT(c.content_id, '_', c.serial_number)) as real_unlock_count
        FROM app_consumption c
        INNER JOIN app_drama_contents dc ON c.content_id = dc.content_id
        WHERE c.amount &lt; 0
        AND c.content_id IS NOT NULL
        AND c.create_time BETWEEN #{startTime} AND #{endTime}
        <if test="appid != null and appid != '' and appid != 'all'">
            AND c.appid = #{appid}
        </if>
        <if test="tfid != null and tfid != ''">
            AND EXISTS (
                SELECT 1 FROM app_order_info o
                WHERE o.user_id = c.user_id
                AND o.tfid = #{tfid}
                AND o.create_time BETWEEN #{startTime} AND #{endTime}
            )
        </if>
        <if test="contentId != null and contentId != ''">
            AND c.content_id = #{contentId}
        </if>
        <if test="dramaId != null and dramaId != ''">
            AND dc.drama_id = #{dramaId}
        </if>
        )
        SELECT
        NULL as id,
        NULL as stat_hour,
        <choose>
            <when test="contentId != null and contentId != ''">#{contentId}</when>
            <otherwise>NULL</otherwise>
        </choose> as content_id,
        <choose>
            <when test="dramaId != null and dramaId != ''">#{dramaId}</when>
            <otherwise>NULL</otherwise>
        </choose> as drama_id,
        <choose>
            <when test="appid != null and appid != ''">#{appid}</when>
            <otherwise>'all'</otherwise>
        </choose> as appid,
        <choose>
            <when test="tfid != null">#{tfid}</when>
            <otherwise>NULL</otherwise>
        </choose> as tfid,
        (SELECT real_recharge_user_count FROM real_recharge_users) AS recharge_user_count,
        SUM(recharge_order_count) AS recharge_order_count,
        SUM(recharge_amount) AS recharge_amount,
        SUM(coin_recharge_count) AS coin_recharge_count,
        SUM(coin_recharge_amount) AS coin_recharge_amount,
        SUM(subscription_recharge_count) AS subscription_recharge_count,
        SUM(subscription_recharge_amount) AS subscription_recharge_amount,
        (SELECT real_consumption_user_count FROM real_consumption_users) AS consumption_user_count,
        SUM(consumption_order_count) AS consumption_order_count,
        SUM(coin_consumption_amount) AS coin_consumption_amount,
        (SELECT real_unlock_count FROM real_unlock_stats) AS unlock_episode_count,
        CASE
        WHEN (SELECT real_recharge_user_count FROM real_recharge_users) &gt; 0
        THEN ROUND((SELECT real_consumption_user_count FROM real_consumption_users) * 1.0 / (SELECT real_recharge_user_count FROM real_recharge_users), 4)
        ELSE 0
        END as recharge_to_consumption_rate,
        CASE
        WHEN SUM(recharge_order_count) &gt; 0
        THEN ROUND(SUM(recharge_amount) / SUM(recharge_order_count), 4)
        ELSE 0
        END as avg_recharge_amount,
        CASE
        WHEN SUM(consumption_order_count) &gt; 0
        THEN ROUND(SUM(coin_consumption_amount) / SUM(consumption_order_count), 4)
        ELSE 0
        END as avg_consumption_amount,
        NULL as create_by,
        NOW() as create_time,
        NULL as update_by,
        NOW() as update_time
        FROM filtered_stats
        CROSS JOIN real_recharge_users
        CROSS JOIN real_consumption_users
        CROSS JOIN real_unlock_stats
    </select>

    <select id="selectTimeRange" resultType="java.util.Map">
        SELECT MIN(create_time) as min_date, MAX(create_time) as max_date FROM app_order_info
    </select>

    <!-- 查询解锁剧集详情 -->
    <select id="getUnlockEpisodeDetails" resultType="tv.shorthub.system.domain.dto.StatisticsHourlyDramaRechargeStatsDetailDTO$UnlockEpisodeDetail">
        SELECT
        c.content_id,
        dc.drama_id,
        d.title as drama_title,
        dc.title as content_title,
        COUNT(DISTINCT CONCAT(c.content_id, '_', c.serial_number)) as unlock_count,
        SUM(ABS(c.amount)) as consumption_amount,
        COUNT(DISTINCT c.user_id) as consumption_user_count,
        MAX(DATE_FORMAT(c.create_time, '%Y-%m-%d %H:00:00')) as stat_time
        FROM app_consumption c
        INNER JOIN app_drama_contents dc ON c.content_id = dc.content_id
        LEFT JOIN app_drama d ON dc.drama_id = d.id
        WHERE c.amount &lt; 0
        AND c.content_id IS NOT NULL
        AND c.create_time BETWEEN #{startTime} AND #{endTime}
        <if test="appid != null and appid != '' and appid != 'all'">
            AND c.appid = #{appid}
        </if>
        <if test="tfid != null and tfid != ''">
            AND EXISTS (
            SELECT 1 FROM app_order_info o
            WHERE o.user_id = c.user_id
            AND o.tfid = #{tfid}
            AND o.create_time BETWEEN #{startTime} AND #{endTime}
            )
        </if>
        <if test="contentId != null and contentId != ''">
            AND c.content_id = #{contentId}
        </if>
        <if test="dramaId != null and dramaId != ''">
            AND dc.drama_id = #{dramaId}
        </if>
        GROUP BY c.content_id, dc.drama_id, d.title, dc.title
        ORDER BY unlock_count DESC, consumption_amount DESC
    </select>

</mapper>
