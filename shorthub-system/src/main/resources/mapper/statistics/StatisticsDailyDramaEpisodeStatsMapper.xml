<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tv.shorthub.system.mapper.StatisticsDailyDramaEpisodeStatsMapper">

    <resultMap type="StatisticsDailyDramaEpisodeStats" id="StatisticsDailyDramaEpisodeStatsResult">
        <result property="id"    column="id"    />
        <result property="appid"    column="appid"    />
        <result property="contentId"    column="content_id"    />
        <result property="serialNumber"    column="serial_number"    />
        <result property="statDate"    column="stat_date"    />
        <result property="tfid"    column="tfid"    />
        <result property="adChannel"    column="ad_channel"    />
        <result property="uniqueViewers"    column="unique_viewers"    />
        <result property="totalViews"    column="total_views"    />
        <result property="videoSeconds"    column="video_seconds"    />
        <result property="avgWatchSeconds"    column="avg_watch_seconds"    />
        <result property="medianWatchSeconds"    column="median_watch_seconds"    />
        <result property="totalWatchSeconds"    column="total_watch_seconds"    />
        <result property="avgCompletionRate"    column="avg_completion_rate"    />
        <result property="highCompletionViewers"    column="high_completion_viewers"    />
        <result property="timezone"    column="timezone"    />
        <result property="extendJson"    column="extend_json"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectStatisticsDailyDramaEpisodeStatsVo">
        select id, appid, content_id, serial_number, stat_date, tfid, ad_channel, unique_viewers, total_views, video_seconds, avg_watch_seconds, median_watch_seconds, total_watch_seconds, avg_completion_rate, high_completion_viewers, timezone, extend_json, create_by, create_time, update_by, update_time from statistics_daily_drama_episode_stats
    </sql>

    <select id="getSummary" parameterType="StatisticsDailyDramaEpisodeStats" resultMap="StatisticsDailyDramaEpisodeStatsResult">
        select
            max(id) as id
        from statistics_daily_drama_episode_stats
        <where>
                    <if test="appid != null  and appid != ''"> and appid = #{appid}</if>
                    <if test="contentId != null  and contentId != ''"> and content_id = #{contentId}</if>
                    <if test="serialNumber != null "> and serial_number = #{serialNumber}</if>
                    <if test="statDate != null "> and stat_date = #{statDate}</if>
                    <if test="tfid != null  and tfid != ''"> and tfid = #{tfid}</if>
                    <if test="adChannel != null  and adChannel != ''"> and ad_channel = #{adChannel}</if>
                    <if test="uniqueViewers != null "> and unique_viewers = #{uniqueViewers}</if>
                    <if test="totalViews != null "> and total_views = #{totalViews}</if>
                    <if test="videoSeconds != null "> and video_seconds = #{videoSeconds}</if>
                    <if test="avgWatchSeconds != null "> and avg_watch_seconds = #{avgWatchSeconds}</if>
                    <if test="medianWatchSeconds != null "> and median_watch_seconds = #{medianWatchSeconds}</if>
                    <if test="totalWatchSeconds != null "> and total_watch_seconds = #{totalWatchSeconds}</if>
                    <if test="avgCompletionRate != null "> and avg_completion_rate = #{avgCompletionRate}</if>
                    <if test="highCompletionViewers != null "> and high_completion_viewers = #{highCompletionViewers}</if>
                    <if test="timezone != null  and timezone != ''"> and timezone = #{timezone}</if>
                    <if test="extendJson != null "> and extend_json = #{extendJson}</if>
                </where>
    </select>

    <select id="summary" parameterType="tv.shorthub.common.core.domain.SummaryRequest" resultMap="StatisticsDailyDramaEpisodeStatsResult">
        --默认为空--
    </select>
    <select id="allSummary" parameterType="tv.shorthub.common.core.domain.SummaryRequest" resultMap="StatisticsDailyDramaEpisodeStatsResult">
        --默认为空--
    </select>

    <select id="selectDailyDramaEpisodeStats" resultMap="StatisticsDailyDramaEpisodeStatsResult">
        SELECT
        uwr.appid,
        uwr.content_id,
        uwr.serial_number,
        DATE(DATE_ADD(uwr.watched_at, INTERVAL (#{timezoneOffset} - 8) HOUR)) AS stat_date,
        COALESCE(uwr.tfid, '') AS tfid,
        COALESCE(MAX(p.ad_channel), '') AS ad_channel,
        COALESCE(MAX(p.create_by), 'system') AS create_by,
        #{timezone} AS timezone,

        -- 基础观看指标
        COUNT(DISTINCT uwr.user_id) AS unique_viewers,
        COUNT(*) AS total_views,
        MAX(dcs.video_seconds) AS video_seconds,

        -- 观看时长统计
        ROUND(AVG(uwr.serial_second), 2) AS avg_watch_seconds,
        SUM(uwr.serial_second) AS total_watch_seconds,

        -- 完成率统计
        ROUND(AVG(CASE
        WHEN dcs.video_seconds > 0
        THEN uwr.serial_second * 100.0 / dcs.video_seconds
        ELSE 0
        END), 2) AS avg_completion_rate,

        COUNT(DISTINCT CASE
        WHEN dcs.video_seconds > 0
        AND uwr.serial_second * 100.0 / dcs.video_seconds > 80
        THEN uwr.user_id
        END) AS high_completion_viewers

        FROM app_user_watch_records uwr
        LEFT JOIN app_promotion p ON uwr.tfid = p.tfid
        JOIN app_drama_contents_serial dcs
        ON uwr.content_id = dcs.content_id
        AND uwr.serial_number = dcs.serial_number
        WHERE uwr.watched_at >= #{startTime}
        AND uwr.watched_at &lt; #{endTime}
        GROUP BY
        uwr.appid, uwr.content_id, uwr.serial_number,
        DATE(DATE_ADD(uwr.watched_at, INTERVAL (#{timezoneOffset} - 8) HOUR)), COALESCE(uwr.tfid, '')
        ORDER BY uwr.content_id, uwr.serial_number, COALESCE(uwr.tfid, '')
    </select>



    <!-- 批量插入或更新剧集分集统计数据 (由拦截器处理) -->
    <insert id="batchInsertOrUpdate" parameterType="list">
        <!-- 此方法由 BatchInsertOrUpdateInterceptor 拦截器处理，无需手动实现 SQL -->
        SELECT 1 FROM DUAL
    </insert>

    <!-- 获取指定剧集分集的观看时长数据用于计算中位数 -->
    <select id="getWatchSecondsForMedian" resultType="java.lang.Long">
        SELECT uwr.serial_second
        FROM app_user_watch_records uwr
        WHERE uwr.content_id = #{contentId}
        AND uwr.serial_number = #{serialNumber}
        AND uwr.watched_at >= #{startTime}
        AND uwr.watched_at &lt; #{endTime}
        AND COALESCE(uwr.tfid, '') = #{tfid}
        AND uwr.serial_second > 0
        ORDER BY uwr.serial_second
    </select>

    <!-- 获取剧集分集统计区间汇总数据 -->
    <select id="getRangeSummary" resultMap="StatisticsDailyDramaEpisodeStatsResult">
        SELECT
            -- 基础观看指标 (直接求和)
            SUM(unique_viewers) AS unique_viewers,
            SUM(total_views) AS total_views,
            SUM(total_watch_seconds) AS total_watch_seconds,
            SUM(high_completion_viewers) AS high_completion_viewers,

            -- 视频时长 (取最大值，因为同一剧集分集的时长应该相同)
            MAX(video_seconds) AS video_seconds,

            -- 平均观看时长 (重新计算)
            CASE
                WHEN SUM(total_views) > 0
                THEN ROUND(SUM(total_watch_seconds) * 1.0 / SUM(total_views), 2)
                ELSE 0
            END AS avg_watch_seconds,

            -- 平均完成率 (重新计算)
            CASE
                WHEN SUM(total_views) > 0 AND MAX(video_seconds) > 0
                THEN ROUND(SUM(total_watch_seconds) * 100.0 / (SUM(total_views) * MAX(video_seconds)), 2)
                ELSE 0
            END AS avg_completion_rate,

            -- 中位数观看时长 (取平均值作为近似)
            CASE
                WHEN COUNT(*) > 0
                THEN ROUND(AVG(median_watch_seconds))
                ELSE 0
            END AS median_watch_seconds,

            -- 统计维度信息
            #{timezone} AS timezone,
            COUNT(DISTINCT stat_date) AS stat_days_count,
            MIN(stat_date) AS start_date,
            MAX(stat_date) AS end_date

        FROM statistics_daily_drama_episode_stats
        WHERE stat_date >= #{startTime} AND stat_date &lt;= #{endTime}
        <if test="appid != null and appid != ''">
            AND appid = #{appid}
        </if>
        <if test="tfid != null and tfid != ''">
            AND tfid = #{tfid}
        </if>
        <if test="contentId != null and contentId != ''">
            AND content_id = #{contentId}
        </if>
        <if test="serialNumber != null">
            AND serial_number = #{serialNumber}
        </if>
        <if test="timezone != null and timezone != ''">
            AND timezone = #{timezone}
        </if>
        <if test="delivererName != null and delivererName != ''">
            AND create_by = #{delivererName}
        </if>
    </select>

</mapper>
