<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tv.shorthub.system.mapper.StatisticsDailyFunnelStatsMapper">

    <resultMap type="StatisticsDailyFunnelStats" id="StatisticsDailyFunnelStatsResult">
        <result property="id"    column="id"    />
        <result property="statDate"    column="stat_date"    />
        <result property="appid"    column="appid"    />
        <result property="tfid"    column="tfid"    />
        <result property="uvCount"    column="uv_count"    />
        <result property="watchCount"    column="watch_count"    />
        <result property="paidEpisodeCount"    column="paid_episode_count"    />
        <result property="orderUserCount"    column="order_user_count"    />
        <result property="paidUserCount"    column="paid_user_count"    />
        <result property="timezone"    column="timezone"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectStatisticsDailyFunnelStatsVo">
        select id, stat_date, appid, tfid, uv_count, watch_count, paid_episode_count, order_user_count, paid_user_count, timezone, create_by, create_time, update_by, update_time from statistics_daily_funnel_stats
    </sql>


    <select id="getSummary" parameterType="StatisticsDailyFunnelStats" resultMap="StatisticsDailyFunnelStatsResult">
        select
            max(id) as id
        from statistics_daily_funnel_stats
        <where>
                    <if test="statDate != null "> and stat_date = #{statDate}</if>
                    <if test="appid != null  and appid != ''"> and appid = #{appid}</if>
                    <if test="tfid != null  and tfid != ''"> and tfid = #{tfid}</if>
                    <if test="uvCount != null "> and uv_count = #{uvCount}</if>
                    <if test="watchCount != null "> and watch_count = #{watchCount}</if>
                    <if test="paidEpisodeCount != null "> and paid_episode_count = #{paidEpisodeCount}</if>
                    <if test="orderUserCount != null "> and order_user_count = #{orderUserCount}</if>
                    <if test="paidUserCount != null "> and paid_user_count = #{paidUserCount}</if>
                </where>
    </select>






    <!-- 根据时间范围查询每日漏斗分析统计数据（支持时区） -->
    <select id="selectStatisticsByTimeRange" resultMap="StatisticsDailyFunnelStatsResult">
        SELECT
            c.stat_date,
            c.appid,
            c.tfid,
            COALESCE(h.uv_count, 0) as uv_count,
            COALESCE(w.watch_count, 0) as watch_count,
            COALESCE(p.paid_episode_count, 0) as paid_episode_count,
            COALESCE(o.order_user_count, 0) as order_user_count,
            COALESCE(o.paid_user_count, 0) as paid_user_count,
            #{timezone} as timezone,
            'system' as create_by,
            NOW() as create_time,
            'system' as update_by,
            NOW() as update_time
        FROM (
                 SELECT DISTINCT stat_date, appid, tfid FROM (
                                                        -- UV 来源 (不变)
                                                        SELECT DATE_FORMAT(DATE_ADD(create_time, INTERVAL (#{timezoneOffset} - 8) HOUR), '%Y-%m-%d') AS stat_date,
                                                               COALESCE(appid, 'default') as appid,
                                                               COALESCE(tid, '') as tfid
                                                        FROM app_promotion_request_log
                                                        WHERE create_time BETWEEN #{startTime} AND #{endTime}
                                                          AND user_agent != 'facebookexternalhit/1.1 (+http://www.facebook.com/externalhit_uatext.php)'
              AND user_agent != 'meta-externalads/1.1 (+https://developers.facebook.com/docs/sharing/webmasters/crawler)'
              AND JSON_EXTRACT(req_params, '$.fbclid') IS NOT NULL
                                                        GROUP BY DATE_FORMAT(DATE_ADD(create_time, INTERVAL (#{timezoneOffset} - 8) HOUR), '%Y-%m-%d'), COALESCE(appid, 'default'), COALESCE(tid, '')
                                                        UNION ALL
                                                        -- 观看用户来源 (不变)
                                                        SELECT DATE_FORMAT(DATE_ADD(watched_at, INTERVAL (#{timezoneOffset} - 8) HOUR), '%Y-%m-%d'),
                                                               COALESCE(appid, 'default'),
                                                               COALESCE(tfid, '') as tfid
                                                        FROM app_user_watch_records
                                                        WHERE watched_at BETWEEN #{startTime} AND #{endTime}
                                                          AND user_id IS NOT NULL
                                                        GROUP BY DATE_FORMAT(DATE_ADD(watched_at, INTERVAL (#{timezoneOffset} - 8) HOUR), '%Y-%m-%d'), COALESCE(appid, 'default'), COALESCE(tfid, '')
                                                        UNION ALL
                                                        -- 【已修改】到达付费集的用户来源，使用新逻辑判断
                                                        SELECT DATE_FORMAT(DATE_ADD(r.watched_at, INTERVAL (#{timezoneOffset} - 8) HOUR), '%Y-%m-%d'),
                                                               COALESCE(r.appid, 'default'),
                                                               COALESCE(r.tfid, '') as tfid
                                                        FROM app_user_watch_records r
                                                            -- 核心修改点 1: LEFT JOIN 推广表，INNER JOIN 内容表
                                                            LEFT JOIN app_promotion p ON r.tfid = p.tfid
                                                            INNER JOIN app_drama_contents d_c ON r.content_id = d_c.content_id
                                                        WHERE r.watched_at BETWEEN #{startTime} AND #{endTime}
                                                          AND r.user_id IS NOT NULL
                                                        -- 核心修改点 2: 使用 COALESCE 判断付费集
                                                          AND r.serial_number >= COALESCE(p.video_fee_begin, d_c.fee_begin)
                                                        GROUP BY DATE_FORMAT(DATE_ADD(r.watched_at, INTERVAL (#{timezoneOffset} - 8) HOUR), '%Y-%m-%d'), COALESCE(r.appid, 'default'), COALESCE(r.tfid, '')
                                                        UNION ALL
                                                        -- 订单来源 (支持时区) - 确保订单数据能独立生成基础记录
                                                        SELECT DATE_FORMAT(stat_date, '%Y-%m-%d'),
                                                               COALESCE(appid, 'default'),
                                                               COALESCE(tfid, '') as tfid
                                                        FROM statistics_daily_order_stats
                                                        WHERE stat_date BETWEEN DATE_FORMAT(#{startTime}, '%Y-%m-%d') AND DATE_FORMAT(#{endTime}, '%Y-%m-%d')
                                                          AND timezone = #{timezone}
                                                        GROUP BY DATE_FORMAT(stat_date, '%Y-%m-%d'), COALESCE(appid, 'default'), COALESCE(tfid, '')
                                                    ) as all_combinations
             ) c
                 -- UV 统计 (h子查询, 不变)
                 LEFT JOIN (
            SELECT DATE_FORMAT(DATE_ADD(create_time, INTERVAL (#{timezoneOffset} - 8) HOUR), '%Y-%m-%d') AS stat_date,
                   COALESCE(appid, 'default') as appid,
                   COALESCE(tid, '') as tfid,
                   COUNT(DISTINCT COALESCE(sid, CONCAT(COALESCE(ip, ''), COALESCE(user_agent, '')))) AS uv_count
            FROM app_promotion_request_log
            WHERE create_time BETWEEN #{startTime} AND #{endTime}
              AND user_agent != 'facebookexternalhit/1.1 (+http://www.facebook.com/externalhit_uatext.php)'
          AND user_agent != 'meta-externalads/1.1 (+https://developers.facebook.com/docs/sharing/webmasters/crawler)'
          AND JSON_EXTRACT(req_params, '$.fbclid') IS NOT NULL
            GROUP BY DATE_FORMAT(DATE_ADD(create_time, INTERVAL (#{timezoneOffset} - 8) HOUR), '%Y-%m-%d'), COALESCE(appid, 'default'), COALESCE(tid, '')
        ) h ON c.stat_date = h.stat_date AND c.appid = h.appid AND c.tfid = h.tfid
            -- 观看用户数统计 (w子查询, 不变)
                 LEFT JOIN (
            SELECT DATE_FORMAT(DATE_ADD(watched_at, INTERVAL (#{timezoneOffset} - 8) HOUR), '%Y-%m-%d') AS stat_date,
                   COALESCE(appid, 'default') as appid,
                   COALESCE(tfid, '') as tfid,
                   COUNT(DISTINCT user_id) AS watch_count
            FROM app_user_watch_records
            WHERE watched_at BETWEEN #{startTime} AND #{endTime}
              AND user_id IS NOT NULL
            GROUP BY DATE_FORMAT(DATE_ADD(watched_at, INTERVAL (#{timezoneOffset} - 8) HOUR), '%Y-%m-%d'), COALESCE(appid, 'default'), COALESCE(tfid, '')
        ) w ON c.stat_date = w.stat_date AND c.appid = w.appid AND c.tfid = w.tfid
            -- 【已修改】付费观看用户数统计，使用新逻辑
                 LEFT JOIN (
            SELECT DATE_FORMAT(DATE_ADD(r.watched_at, INTERVAL (#{timezoneOffset} - 8) HOUR), '%Y-%m-%d') AS stat_date,
                   COALESCE(r.appid, 'default') as appid,
                   COALESCE(r.tfid, '') as tfid,
                   COUNT(DISTINCT r.user_id) AS paid_episode_count
            FROM app_user_watch_records r
                     -- 核心修改点 1: LEFT JOIN 推广表，INNER JOIN 内容表
                     LEFT JOIN app_promotion p ON r.tfid = p.tfid
                     INNER JOIN app_drama_contents d_c ON r.content_id = d_c.content_id
            WHERE r.watched_at BETWEEN #{startTime} AND #{endTime}
              AND r.user_id IS NOT NULL
              -- 核心修改点 2: 使用 COALESCE 判断付费集
              AND r.serial_number >= COALESCE(p.video_fee_begin, d_c.fee_begin)
            GROUP BY DATE_FORMAT(DATE_ADD(r.watched_at, INTERVAL (#{timezoneOffset} - 8) HOUR), '%Y-%m-%d'), COALESCE(r.appid, 'default'), COALESCE(r.tfid, '')
        ) p ON c.stat_date = p.stat_date AND c.appid = p.appid AND c.tfid = p.tfid
-- 订单统计 (o子查询，支持时区)
                 LEFT JOIN (
            SELECT
                DATE_FORMAT(stat_date, '%Y-%m-%d') AS stat_date,
                COALESCE(appid, 'default') as appid,
                COALESCE(tfid, '') as tfid,
                SUM(total_order_count) AS order_user_count,
                -- 【修正点】应该使用已经计算好的 paid_user_count 列
                SUM(paid_user_count) AS paid_user_count
            FROM
                statistics_daily_order_stats
            WHERE
                stat_date BETWEEN DATE_FORMAT(#{startTime}, '%Y-%m-%d') AND DATE_FORMAT(#{endTime}, '%Y-%m-%d')
                AND timezone = #{timezone}
            GROUP BY
                DATE_FORMAT(stat_date, '%Y-%m-%d'), COALESCE(appid, 'default'), COALESCE(tfid, '')
        ) o ON c.stat_date = o.stat_date AND c.appid = o.appid AND c.tfid = o.tfid
    </select>

    <!-- 查询漏斗分析数据的最早和最晚日期 -->
    <select id="selectTimeRange" resultType="java.util.Map">
        SELECT
            MIN(stat_date) as min_date,
            MAX(stat_date) as max_date
        FROM statistics_daily_funnel_stats
    </select>



    <!-- 批量插入或更新每日漏斗分析统计数据 V1版本 -->
    <insert id="batchInsertOrUpdateV1" parameterType="java.util.List">
        INSERT INTO statistics_daily_funnel_stats
        (stat_date, appid, tfid, uv_count, watch_count, paid_episode_count, order_user_count, paid_user_count, timezone, create_by, create_time, update_by, update_time)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.statDate}, #{item.appid}, #{item.tfid}, #{item.uvCount}, #{item.watchCount}, #{item.paidEpisodeCount}, #{item.orderUserCount}, #{item.paidUserCount}, #{item.timezone}, #{item.createBy}, #{item.createTime}, #{item.updateBy}, #{item.updateTime})
        </foreach>
        ON DUPLICATE KEY UPDATE
            uv_count = VALUES(uv_count),
            watch_count = VALUES(watch_count),
            paid_episode_count = VALUES(paid_episode_count),
            order_user_count = VALUES(order_user_count),
            paid_user_count = VALUES(paid_user_count),
            update_by = VALUES(update_by),
            update_time = VALUES(update_time)
    </insert>

    <!-- 根据时间范围获取每日漏斗统计汇总数据 -->
    <select id="getRangeSummary" resultMap="StatisticsDailyFunnelStatsResult">
        WITH
        -- 计算UV（从推广请求日志中获取）
        uv_data AS (
            SELECT COUNT(DISTINCT COALESCE(sid, CONCAT(COALESCE(ip, ''), COALESCE(user_agent, '')))) AS uv_count
            FROM app_promotion_request_log
            WHERE create_time >= #{startTime} AND create_time &lt;= DATE_ADD(#{endTime}, INTERVAL 1 DAY)
              AND user_agent != 'facebookexternalhit/1.1 (+http://www.facebook.com/externalhit_uatext.php)'
              AND user_agent != 'meta-externalads/1.1 (+https://developers.facebook.com/docs/sharing/webmasters/crawler)'
              AND JSON_EXTRACT(req_params, '$.fbclid') IS NOT NULL
              <if test="appid != null and appid != ''">
                  AND COALESCE(appid, 'default') = #{appid}
              </if>
              <if test="tfid != null and tfid != ''">
                  AND tid = #{tfid}
              </if>
        ),
        -- 计算观看用户数
        watch_data AS (
            SELECT COUNT(DISTINCT user_id) AS watch_count
            FROM app_user_watch_records
            WHERE watched_at >= #{startTime} AND watched_at &lt;= DATE_ADD(#{endTime}, INTERVAL 1 DAY)
              AND user_id IS NOT NULL
              <if test="appid != null and appid != ''">
                  AND COALESCE(appid, 'default') = #{appid}
              </if>
              <if test="tfid != null and tfid != ''">
                  AND tfid = #{tfid}
              </if>
        ),
        -- 计算付费集用户数（只统计推广流量）
        paid_episode_data AS (
            SELECT COUNT(DISTINCT r.user_id) AS paid_episode_count
            FROM app_user_watch_records r
            INNER JOIN app_promotion p ON r.tfid = p.tfid
            WHERE r.watched_at >= #{startTime} AND r.watched_at &lt;= DATE_ADD(#{endTime}, INTERVAL 1 DAY)
              AND r.user_id IS NOT NULL
              AND r.serial_number &gt;= p.video_fee_begin
              <if test="appid != null and appid != ''">
                  AND COALESCE(r.appid, 'default') = #{appid}
              </if>
              <if test="tfid != null and tfid != ''">
                  AND r.tfid = #{tfid}
              </if>
        ),
        -- 从天订单统计表获取订单数据
        order_data AS (
            SELECT
                SUM(total_order_count) AS order_user_count,
                SUM(paid_user_count) AS paid_user_count
            FROM statistics_daily_order_stats
            WHERE stat_date >= DATE_FORMAT(#{startTime}, '%Y-%m-%d')
              AND stat_date &lt;= DATE_FORMAT(#{endTime}, '%Y-%m-%d')
              <if test="appid != null and appid != ''">
                  AND appid = #{appid}
              </if>
              <if test="tfid != null and tfid != ''">
                  AND tfid = #{tfid}
              </if>
              <if test="timezone != null and timezone != ''">
                  AND timezone = #{timezone}
              </if>
        )
        SELECT
            NULL as id,
            NULL as stat_date,
            <choose>
                <when test="appid != null and appid != ''">#{appid}</when>
                <otherwise>'all'</otherwise>
            </choose> as appid,
            <choose>
                <when test="tfid != null and tfid != ''">#{tfid}</when>
                <otherwise>NULL</otherwise>
            </choose> as tfid,
            COALESCE(uv.uv_count, 0) as uv_count,
            COALESCE(w.watch_count, 0) as watch_count,
            COALESCE(pe.paid_episode_count, 0) as paid_episode_count,
            COALESCE(o.order_user_count, 0) as order_user_count,
            COALESCE(o.paid_user_count, 0) as paid_user_count,
            <choose>
                <when test="timezone != null and timezone != ''">#{timezone}</when>
                <otherwise>'UTC+8'</otherwise>
            </choose> as timezone,
            NULL as create_by,
            NOW() as create_time,
            NULL as update_by,
            NOW() as update_time
        FROM uv_data uv
        CROSS JOIN watch_data w
        CROSS JOIN paid_episode_data pe
        CROSS JOIN order_data o
    </select>

</mapper>
