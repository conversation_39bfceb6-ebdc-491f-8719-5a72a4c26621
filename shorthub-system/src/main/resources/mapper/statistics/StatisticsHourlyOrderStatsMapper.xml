<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tv.shorthub.system.mapper.StatisticsHourlyOrderStatsMapper">

    <resultMap type="StatisticsHourlyOrderStats" id="StatisticsHourlyOrderStatsResult">
        <result property="id"    column="id"    />
        <result property="statHour"    column="stat_hour"    />
        <result property="appid"    column="appid"    />
        <result property="tfid"    column="tfid"    />
        <result property="orderChannel"    column="order_channel"    />
        <result property="totalOrderCount"    column="total_order_count"    />
        <result property="paidOrderCount"    column="paid_order_count"    />
        <result property="paidUserCount"    column="paid_user_count"    />
        <result property="paidOrderAmount"    column="paid_order_amount"    />
        <result property="unpaidOrderCount"    column="unpaid_order_count"    />
        <result property="unpaidCoinOrderAmount"    column="unpaid_coin_order_amount"    />
        <result property="unpaidSubscriptionOrderAmount"    column="unpaid_subscription_order_amount"    />
        <result property="memberOrderCount"    column="member_order_count"    />
        <result property="memberOrderAmount"    column="member_order_amount"    />
        <result property="coinOrderCount"    column="coin_order_count"    />
        <result property="coinOrderAmount"    column="coin_order_amount"    />
        <result property="subscriptionOrderCount"    column="subscription_order_count"    />
        <result property="subscriptionOrderAmount"    column="subscription_order_amount"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectStatisticsHourlyOrderStatsVo">
        select id, stat_hour, appid, tfid, order_channel, total_order_count, paid_order_count, paid_user_count, paid_order_amount, unpaid_order_count, unpaid_coin_order_amount, unpaid_subscription_order_amount, member_order_count, member_order_amount, coin_order_count, coin_order_amount, subscription_order_count, subscription_order_amount, create_by, create_time, update_by, update_time from statistics_hourly_order_stats
    </sql>

    <insert id="batchInsertOrUpdateStats">
        INSERT INTO statistics_hourly_order_stats
        (stat_hour, appid, tfid, order_channel, total_order_count, paid_order_count, paid_user_count, paid_order_amount,
        unpaid_order_count, unpaid_coin_order_amount, unpaid_subscription_order_amount,
        member_order_count, member_order_amount, coin_order_count,
        coin_order_amount, subscription_order_count, subscription_order_amount,
        create_by, create_time, update_by, update_time)
        VALUES
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.statHour}, #{item.appid}, COALESCE(#{item.tfid}, ''), #{item.orderChannel}, #{item.totalOrderCount},
            #{item.paidOrderCount}, #{item.paidUserCount}, #{item.paidOrderAmount}, #{item.unpaidOrderCount},
            #{item.unpaidCoinOrderAmount}, #{item.unpaidSubscriptionOrderAmount},
            #{item.memberOrderCount}, #{item.memberOrderAmount}, #{item.coinOrderCount},
            #{item.coinOrderAmount}, #{item.subscriptionOrderCount}, #{item.subscriptionOrderAmount},
            #{item.createBy}, #{item.createTime}, #{item.updateBy}, #{item.updateTime})
        </foreach>
        ON DUPLICATE KEY UPDATE
        total_order_count = VALUES(total_order_count),
        paid_order_count = VALUES(paid_order_count),
        paid_user_count = VALUES(paid_user_count),
        paid_order_amount = VALUES(paid_order_amount),
        unpaid_order_count = VALUES(unpaid_order_count),
        unpaid_coin_order_amount = VALUES(unpaid_coin_order_amount),
        unpaid_subscription_order_amount = VALUES(unpaid_subscription_order_amount),
        member_order_count = VALUES(member_order_count),
        member_order_amount = VALUES(member_order_amount),
        coin_order_count = VALUES(coin_order_count),
        coin_order_amount = VALUES(coin_order_amount),
        subscription_order_count = VALUES(subscription_order_count),
        subscription_order_amount = VALUES(subscription_order_amount),
        order_channel = VALUES(order_channel),
        update_by = VALUES(update_by),
        update_time = VALUES(update_time)
    </insert>

    <select id="getSummary" parameterType="StatisticsHourlyOrderStats" resultMap="StatisticsHourlyOrderStatsResult">
        WITH filtered_stats AS (
        SELECT *
        FROM statistics_hourly_order_stats
        <where>
            <if test="statHour != null "> and stat_hour = #{statHour}</if>
            <if test="appid != null  and appid != ''"> and appid = #{appid}</if>
            <if test="tfid != null">
                <choose>
                    <when test="tfid == ''">and (tfid IS NULL or tfid = '')</when>
                    <otherwise>and tfid = #{tfid}</otherwise>
                </choose>
            </if>
            <if test="orderChannel != null and orderChannel != ''">
                and order_channel = #{orderChannel}
            </if>
            <if test="totalOrderCount != null "> and total_order_count = #{totalOrderCount}</if>
            <if test="paidOrderCount != null "> and paid_order_count = #{paidOrderCount}</if>
            <if test="paidOrderAmount != null "> and paid_order_amount = #{paidOrderAmount}</if>
            <if test="unpaidOrderCount != null "> and unpaid_order_count = #{unpaidOrderCount}</if>
            <if test="unpaidCoinOrderAmount != null "> and unpaid_coin_order_amount = #{unpaidCoinOrderAmount}</if>
            <if test="unpaidSubscriptionOrderAmount != null "> and unpaid_subscription_order_amount = #{unpaidSubscriptionOrderAmount}</if>
            <if test="memberOrderCount != null "> and member_order_count = #{memberOrderCount}</if>
            <if test="memberOrderAmount != null "> and member_order_amount = #{memberOrderAmount}</if>
            <if test="coinOrderCount != null "> and coin_order_count = #{coinOrderCount}</if>
            <if test="coinOrderAmount != null "> and coin_order_amount = #{coinOrderAmount}</if>
            <if test="subscriptionOrderCount != null "> and subscription_order_count = #{subscriptionOrderCount}</if>
            <if test="subscriptionOrderAmount != null "> and subscription_order_amount = #{subscriptionOrderAmount}</if>
            <if test="createBy != null  and createBy != ''"> and create_by = #{createBy}</if>
            <if test="updateBy != null  and updateBy != ''"> and update_by = #{updateBy}</if>
        </where>
        )
        SELECT
        NULL as id,
        NULL as stat_hour,
        NULL as appid,
        NULL as tfid,
        NULL as order_channel,
        SUM(total_order_count) AS total_order_count,
        SUM(paid_order_count) AS paid_order_count,
        SUM(paid_user_count) AS paid_user_count,
        SUM(paid_order_amount) AS paid_order_amount,
        SUM(unpaid_order_count) AS unpaid_order_count,
        SUM(unpaid_coin_order_amount) AS unpaid_coin_order_amount,
        SUM(unpaid_subscription_order_amount) AS unpaid_subscription_order_amount,
        SUM(member_order_count) AS member_order_count,
        SUM(member_order_amount) AS member_order_amount,
        SUM(coin_order_count) AS coin_order_count,
        SUM(coin_order_amount) AS coin_order_amount,
        SUM(subscription_order_count) AS subscription_order_count,
        SUM(subscription_order_amount) AS subscription_order_amount
        FROM filtered_stats
    </select>

    <select id="summary" parameterType="tv.shorthub.common.core.domain.SummaryRequest" resultMap="StatisticsHourlyOrderStatsResult">

    </select>
    <select id="allSummary" parameterType="tv.shorthub.common.core.domain.SummaryRequest" resultMap="StatisticsHourlyOrderStatsResult">

    </select>

    <!-- 根据时间范围查询每小时订单统计数据 -->
    <select id="selectHourlyOrderStats" resultMap="StatisticsHourlyOrderStatsResult">
        WITH base_orders AS (
            SELECT
                DATE_FORMAT(aoi.create_time, '%Y-%m-%d %H:00:00') AS stat_hour,
                aoi.id,
                aoi.user_id,
                COALESCE(aoi.appid, 'default') as appid,
                COALESCE(aoi.tfid, '') as tfid,
                aoi.order_channel,
                aoi.order_status,
                aoi.order_amount,
                aoi.pay_type,
                aoi.create_time,
                aoi.pay_time,
                JSON_UNQUOTE(JSON_EXTRACT(aoi.extend_json, '$.clientIp')) AS client_ip
            FROM app_order_info aoi
            WHERE aoi.create_time BETWEEN #{startTime} AND #{endTime}
        ),
        successful_attempts AS (
            SELECT DISTINCT client_ip
            FROM base_orders
            WHERE order_status = 1 AND pay_time IS NOT NULL AND client_ip IS NOT NULL
        ),
        orders_for_aggregation AS (
            SELECT id, stat_hour, appid, tfid, order_channel, user_id, order_status, order_amount, pay_type, create_time, pay_time, client_ip
            FROM base_orders
            WHERE order_status = 1 AND pay_time IS NOT NULL

            UNION ALL

            SELECT id, stat_hour, appid, tfid, order_channel, user_id, order_status, order_amount, pay_type, create_time, pay_time, client_ip
            FROM (
                SELECT
                    b.*,
                    ROW_NUMBER() OVER(PARTITION BY b.client_ip, b.stat_hour ORDER BY b.create_time ASC) as rn
                FROM
                    base_orders b
                LEFT JOIN
                    successful_attempts s ON b.client_ip = s.client_ip
                WHERE
                    b.order_status = 0 AND s.client_ip IS NULL
            ) AS truly_abandoned_unpaid_orders
            WHERE rn = 1
        )
        SELECT
            STR_TO_DATE(stat_hour, '%Y-%m-%d %H:%i:%s') as stat_hour,
            appid,
            COALESCE(tfid, '') as tfid,
            order_channel,
            -- 总订单数 = 所有去重的客户端IP（CTE已正确处理去重逻辑）
            COUNT(DISTINCT client_ip) AS total_order_count,
            -- 支付成功订单数（不去重）
            COUNT(CASE WHEN order_status = 1 AND pay_time IS NOT NULL THEN 1 END) AS paid_order_count,
            -- 订单成功人数（按IP去重）
            COUNT(DISTINCT CASE WHEN order_status = 1 AND pay_time IS NOT NULL THEN client_ip END) AS paid_user_count,
            SUM(IF(order_status = 1 AND pay_time IS NOT NULL, order_amount, 0)) AS paid_order_amount,
            SUM(IF(order_status = 0, 1, 0)) AS unpaid_order_count,
            SUM(IF(order_status = 0 AND pay_type = '1', order_amount, 0)) AS unpaid_coin_order_amount,
            SUM(IF(order_status = 0 AND pay_type = '2', order_amount, 0)) AS unpaid_subscription_order_amount,
            SUM(IF(order_status = 1 AND pay_time IS NOT NULL AND pay_type = '0', 1, 0)) AS member_order_count,
            SUM(IF(order_status = 1 AND pay_time IS NOT NULL AND pay_type = '0', order_amount, 0)) AS member_order_amount,
            SUM(IF(order_status = 1 AND pay_time IS NOT NULL AND pay_type = '1', 1, 0)) AS coin_order_count,
            SUM(IF(order_status = 1 AND pay_time IS NOT NULL AND pay_type = '1', order_amount, 0)) AS coin_order_amount,
            SUM(IF(order_status = 1 AND pay_time IS NOT NULL AND pay_type = '2', 1, 0)) AS subscription_order_count,
            SUM(IF(order_status = 1 AND pay_time IS NOT NULL AND pay_type = '2', order_amount, 0)) AS subscription_order_amount,
            'system' as create_by,
            NOW() as create_time,
            'system' as update_by,
            NOW() as update_time
        FROM orders_for_aggregation
        GROUP BY stat_hour, appid, COALESCE(tfid, ''), order_channel
        ORDER BY stat_hour, appid, COALESCE(tfid, ''), order_channel
    </select>

    <!-- 根据时间范围获取订单统计汇总数据 -->
    <select id="getRangeSummary" resultMap="StatisticsHourlyOrderStatsResult">
        WITH filtered_stats AS (
        SELECT *
        FROM statistics_hourly_order_stats
        WHERE stat_hour BETWEEN #{startTime} AND #{endTime}
        <if test="appid != null and appid != ''">
            AND appid = #{appid}
        </if>
        <if test="tfid != null">
            <choose>
                <when test="tfid == ''">AND (tfid IS NULL or tfid = '')</when>
                <otherwise>AND tfid = #{tfid}</otherwise>
            </choose>
        </if>
        <if test="orderChannel != null and orderChannel != ''">
            AND order_channel = #{orderChannel}
        </if>
        )
        SELECT
        NULL as id,
        NULL as stat_hour,
        <choose>
            <when test="appid != null and appid != ''">#{appid}</when>
            <otherwise>'all'</otherwise>
        </choose> as appid,
        <choose>
            <when test="tfid != null">#{tfid}</when>
            <otherwise>NULL</otherwise>
        </choose> as tfid,
        <choose>
            <when test="orderChannel != null">#{orderChannel}</when>
            <otherwise>NULL</otherwise>
        </choose> as order_channel,
        SUM(total_order_count) AS total_order_count,
        SUM(paid_order_count) AS paid_order_count,
        SUM(paid_user_count) AS paid_user_count,
        SUM(paid_order_amount) AS paid_order_amount,
        SUM(unpaid_order_count) AS unpaid_order_count,
        SUM(unpaid_coin_order_amount) AS unpaid_coin_order_amount,
        SUM(unpaid_subscription_order_amount) AS unpaid_subscription_order_amount,
        SUM(member_order_count) AS member_order_count,
        SUM(member_order_amount) AS member_order_amount,
        SUM(coin_order_count) AS coin_order_count,
        SUM(coin_order_amount) AS coin_order_amount,
        SUM(subscription_order_count) AS subscription_order_count,
        SUM(subscription_order_amount) AS subscription_order_amount,
        NULL as create_by,
        NOW() as create_time,
        NULL as update_by,
        NOW() as update_time
        FROM filtered_stats
    </select>

    <!-- 根据 tfid 列表获取汇总数据 -->
    <select id="getSummaryByTfids" resultMap="StatisticsHourlyOrderStatsResult">
        SELECT
        NULL as id,
        NULL as stat_hour,
        NULL as appid,
        NULL as tfid,
        NULL as order_channel,
        SUM(total_order_count) AS total_order_count,
        SUM(paid_order_count) AS paid_order_count,
        SUM(paid_user_count) AS paid_user_count,
        SUM(paid_order_amount) AS paid_order_amount,
        SUM(unpaid_order_count) AS unpaid_order_count,
        SUM(unpaid_coin_order_amount) AS unpaid_coin_order_amount,
        SUM(unpaid_subscription_order_amount) AS unpaid_subscription_order_amount,
        SUM(member_order_count) AS member_order_count,
        SUM(member_order_amount) AS member_order_amount,
        SUM(coin_order_count) AS coin_order_count,
        SUM(coin_order_amount) AS coin_order_amount,
        SUM(subscription_order_count) AS subscription_order_count,
        SUM(subscription_order_amount) AS subscription_order_amount
        FROM statistics_hourly_order_stats
        WHERE tfid IN
        <foreach item="tfid" collection="tfids" open="(" separator="," close=")">
            #{tfid}
        </foreach>
        <if test="startTime != null">
            AND stat_hour BETWEEN #{startTime} AND #{endTime}
        </if>
        <if test="appid != null and appid != ''">
            AND appid = #{appid}
        </if>
        <if test="orderChannel != null and orderChannel != ''">
            AND order_channel = #{orderChannel}
        </if>
    </select>

    <select id="selectTimeRange" resultType="java.util.Map">
        SELECT MIN(create_time) as min_date, MAX(create_time) as max_date FROM app_order_info
    </select>

</mapper>
