<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tv.shorthub.system.mapper.StatisticsHourlyFunnelStatsMapper">

    <resultMap type="StatisticsHourlyFunnelStats" id="StatisticsHourlyFunnelStatsResult">
        <result property="id"    column="id"    />
        <result property="statHour"    column="stat_hour"    />
        <result property="appid"    column="appid"    />
        <result property="tfid"    column="tfid"    />
        <result property="uvCount"    column="uv_count"    />
        <result property="watchCount"    column="watch_count"    />
        <result property="paidEpisodeCount"    column="paid_episode_count"    />
        <result property="orderUserCount"    column="order_user_count"    />
        <result property="paidUserCount"    column="paid_user_count"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectStatisticsHourlyFunnelStatsVo">
        select id, stat_hour, appid, tfid, uv_count, watch_count, paid_episode_count, order_user_count, paid_user_count, create_by, create_time, update_by, update_time from statistics_hourly_funnel_stats
    </sql>


    <select id="getSummary" parameterType="StatisticsHourlyFunnelStats" resultMap="StatisticsHourlyFunnelStatsResult">
        select
            max(id) as id
        from statistics_hourly_funnel_stats
        <where>
                    <if test="statHour != null "> and stat_hour = #{statHour}</if>
                    <if test="appid != null  and appid != ''"> and appid = #{appid}</if>
                    <if test="tfid != null  and tfid != ''"> and tfid = #{tfid}</if>
                    <if test="uvCount != null "> and uv_count = #{uvCount}</if>
                    <if test="watchCount != null "> and watch_count = #{watchCount}</if>
                    <if test="paidEpisodeCount != null "> and paid_episode_count = #{paidEpisodeCount}</if>
                    <if test="orderUserCount != null "> and order_user_count = #{orderUserCount}</if>
                    <if test="paidUserCount != null "> and paid_user_count = #{paidUserCount}</if>
                </where>
    </select>




    <!-- 根据时间范围查询每小时漏斗分析统计数据 -->
    <select id="selectHourlyFunnelStats" resultMap="StatisticsHourlyFunnelStatsResult">
        SELECT
            c.stat_hour,
            c.appid,
            c.tfid,
            COALESCE(h.uv_count, 0) as uv_count,
            COALESCE(w.watch_count, 0) as watch_count,
            COALESCE(p.paid_episode_count, 0) as paid_episode_count,
            COALESCE(o.order_user_count, 0) as order_user_count,
            COALESCE(o.paid_user_count, 0) as paid_user_count,
            'system' as create_by,
            NOW() as create_time,
            'system' as update_by,
            NOW() as update_time
        FROM (
                 SELECT stat_hour, appid, tfid FROM (
                                                        -- UV 来源
                                                        SELECT DATE_FORMAT(create_time, '%Y-%m-%d %H:00:00') AS stat_hour, COALESCE(appid, 'default') as appid, COALESCE(tid, '') as tfid
                                                        FROM app_promotion_request_log
                                                        WHERE create_time BETWEEN #{startTime} AND #{endTime}
                                                          AND user_agent != 'facebookexternalhit/1.1 (+http://www.facebook.com/externalhit_uatext.php)'
                                                          AND user_agent != 'meta-externalads/1.1 (+https://developers.facebook.com/docs/sharing/webmasters/crawler)'
                                                          AND JSON_EXTRACT(req_params, '$.fbclid') IS NOT NULL
                                                        GROUP BY DATE_FORMAT(create_time, '%Y-%m-%d %H:00:00'), appid, COALESCE(tid, '')
                                                        UNION ALL
                                                        -- 观看用户来源
                                                        SELECT DATE_FORMAT(watched_at, '%Y-%m-%d %H:00:00'), COALESCE(appid, 'default'), COALESCE(tfid, '') as tfid
                                                        FROM app_user_watch_records
                                                        WHERE watched_at BETWEEN #{startTime} AND #{endTime}
                                                          AND user_id IS NOT NULL
                                                        GROUP BY DATE_FORMAT(watched_at, '%Y-%m-%d %H:00:00'), appid, COALESCE(tfid, '')
                                                        UNION ALL
                                                        -- 到达付费集的用户来源
                                                        SELECT DATE_FORMAT(r.watched_at, '%Y-%m-%d %H:00:00'), COALESCE(r.appid, 'default'), COALESCE(r.tfid, '') as tfid
                                                        FROM app_user_watch_records r
                                                            LEFT JOIN app_promotion p ON r.tfid = p.tfid
                                                            INNER JOIN app_drama_contents d_c ON r.content_id = d_c.content_id
                                                        WHERE r.watched_at BETWEEN #{startTime} AND #{endTime}
                                                          AND r.user_id IS NOT NULL
                                                          AND r.serial_number >= COALESCE(p.video_fee_begin, d_c.fee_begin)
                                                        GROUP BY DATE_FORMAT(r.watched_at, '%Y-%m-%d %H:00:00'), r.appid, COALESCE(r.tfid, '')
                                                        UNION ALL
                                                        -- 订单来源
                                                        SELECT DATE_FORMAT(stat_hour, '%Y-%m-%d %H:00:00'), COALESCE(appid, 'default'), COALESCE(tfid, '') as tfid
                                                        FROM statistics_hourly_order_stats
                                                        WHERE stat_hour BETWEEN #{startTime} AND #{endTime}
                                                        GROUP BY DATE_FORMAT(stat_hour, '%Y-%m-%d %H:00:00'), appid, COALESCE(tfid, '')
                                                    ) as all_combinations
             ) c
                 LEFT JOIN (
            -- UV 统计
            SELECT DATE_FORMAT(create_time, '%Y-%m-%d %H:00:00') AS stat_hour, COALESCE(appid, 'default') as appid, COALESCE(tid, '') as tfid,
                   COUNT(DISTINCT COALESCE(sid, CONCAT(COALESCE(ip, ''), COALESCE(user_agent, '')))) AS uv_count
            FROM app_promotion_request_log
            WHERE create_time BETWEEN #{startTime} AND #{endTime}
              AND user_agent != 'facebookexternalhit/1.1 (+http://www.facebook.com/externalhit_uatext.php)'
              AND user_agent != 'meta-externalads/1.1 (+https://developers.facebook.com/docs/sharing/webmasters/crawler)'
              AND JSON_EXTRACT(req_params, '$.fbclid') IS NOT NULL
            GROUP BY DATE_FORMAT(create_time, '%Y-%m-%d %H:00:00'), appid, COALESCE(tid, '')
        ) h ON c.stat_hour = h.stat_hour AND c.appid = h.appid AND c.tfid = h.tfid
                 LEFT JOIN (
            -- 观看用户数统计
            SELECT DATE_FORMAT(watched_at, '%Y-%m-%d %H:00:00') AS stat_hour, COALESCE(appid, 'default') as appid, COALESCE(tfid, '') as tfid,
                   COUNT(DISTINCT user_id) AS watch_count
            FROM app_user_watch_records
            WHERE watched_at BETWEEN #{startTime} AND #{endTime}
              AND user_id IS NOT NULL
            GROUP BY DATE_FORMAT(watched_at, '%Y-%m-%d %H:00:00'), appid, COALESCE(tfid, '')
        ) w ON c.stat_hour = w.stat_hour AND c.appid = w.appid AND c.tfid = w.tfid
                 LEFT JOIN (
            -- 付费观看用户数统计
            SELECT DATE_FORMAT(r.watched_at, '%Y-%m-%d %H:00:00') AS stat_hour, COALESCE(r.appid, 'default') as appid, COALESCE(r.tfid, '') as tfid,
                   COUNT(DISTINCT r.user_id) AS paid_episode_count
            FROM app_user_watch_records r
                     LEFT JOIN app_promotion p ON r.tfid = p.tfid
                     INNER JOIN app_drama_contents d_c ON r.content_id = d_c.content_id
            WHERE r.watched_at BETWEEN #{startTime} AND #{endTime}
              AND r.user_id IS NOT NULL
              AND r.serial_number >= COALESCE(p.video_fee_begin, d_c.fee_begin)
            GROUP BY DATE_FORMAT(r.watched_at, '%Y-%m-%d %H:00:00'), COALESCE(r.appid, 'default'), COALESCE(r.tfid, '')
        ) p ON c.stat_hour = p.stat_hour AND c.appid = p.appid AND c.tfid = p.tfid
                 LEFT JOIN (
            -- 订单统计
            SELECT
                DATE_FORMAT(stat_hour, '%Y-%m-%d %H:00:00') AS stat_hour,
                COALESCE(appid, 'default') as appid,
                COALESCE(tfid, '') as tfid,
                SUM(total_order_count) AS order_user_count,
                SUM(paid_user_count) AS paid_user_count
            FROM
                statistics_hourly_order_stats
            WHERE
                stat_hour BETWEEN #{startTime} AND #{endTime}
            GROUP BY
                DATE_FORMAT(stat_hour, '%Y-%m-%d %H:00:00'), appid, COALESCE(tfid, '')
        ) o ON c.stat_hour = o.stat_hour AND c.appid = o.appid AND c.tfid = o.tfid
        ORDER BY c.stat_hour, c.appid, c.tfid
    </select>
    <!-- 批量插入或更新漏斗分析统计数据 -->
    <insert id="batchInsertOrUpdate">
        INSERT INTO statistics_hourly_funnel_stats
        (stat_hour, appid, tfid, uv_count, watch_count, paid_episode_count, order_user_count, paid_user_count,
         create_by, create_time, update_by, update_time)
        VALUES
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.statHour}, #{item.appid}, COALESCE(#{item.tfid}, ''), #{item.uvCount}, #{item.watchCount},
             #{item.paidEpisodeCount}, #{item.orderUserCount}, #{item.paidUserCount},
             #{item.createBy}, #{item.createTime}, #{item.updateBy}, #{item.updateTime})
        </foreach>
        ON DUPLICATE KEY UPDATE
        uv_count = VALUES(uv_count),
        watch_count = VALUES(watch_count),
        paid_episode_count = VALUES(paid_episode_count),
        order_user_count = VALUES(order_user_count),
        paid_user_count = VALUES(paid_user_count),
        update_by = VALUES(update_by),
        update_time = VALUES(update_time)
    </insert>

    <!-- 根据时间范围获取漏斗分析统计汇总数据 -->
    <select id="getRangeSummary" resultMap="StatisticsHourlyFunnelStatsResult">
        SELECT
        NULL as id,
        NULL as stat_hour,
        <choose>
            <when test="appid != null and appid != ''">#{appid}</when>
            <otherwise>'all'</otherwise>
        </choose> as appid,
        <choose>
            <when test="tfid != null">#{tfid}</when>
            <otherwise>'all'</otherwise>
        </choose> as tfid,

        -- 直接对每小时汇总结果求和
        SUM(uv_count) AS uv_count,
        SUM(watch_count) AS watch_count,
        SUM(paid_episode_count) AS paid_episode_count,
        SUM(order_user_count) AS order_user_count,
        SUM(paid_user_count) AS paid_user_count

        FROM
        statistics_hourly_funnel_stats -- 直接从漏斗汇总表聚合
        WHERE
        stat_hour >= #{startTime} AND stat_hour &lt; #{endTime}
        <if test="appid != null and appid != ''">
            AND COALESCE(appid, 'default') = #{appid}
        </if>
        <if test="tfid != null">
            AND COALESCE(tfid, '') = #{tfid}
        </if>
    </select>
    <!-- 根据 tfid 列表获取汇总数据 -->
    <select id="getSummaryByTfids" resultMap="StatisticsHourlyFunnelStatsResult">
        SELECT
        NULL as id,
        NULL as stat_hour,
        NULL as appid,
        NULL as tfid,
        SUM(uv_count) AS uv_count,
        SUM(watch_count) AS watch_count,
        SUM(paid_episode_count) AS paid_episode_count,
        SUM(order_user_count) AS order_user_count,
        SUM(paid_user_count) AS paid_user_count,
        NULL as create_by,
        NOW() as create_time,
        NULL as update_by,
        NOW() as update_time
        FROM statistics_hourly_funnel_stats
        WHERE tfid IN
        <foreach item="tfid" collection="tfids" open="(" separator="," close=")">
            #{tfid}
        </foreach>
        <if test="startTime != null">
            AND stat_hour >= #{startTime}
        </if>
        <if test="endTime != null">
            AND stat_hour &lt; #{endTime}
        </if>
        <if test="appid != null and appid != ''">
            AND appid = #{appid}
        </if>
    </select>

    <!-- 根据创建者获取汇总数据 -->
    <select id="getSummaryByCreator" resultMap="StatisticsHourlyFunnelStatsResult">
        SELECT
        NULL as id,
        NULL as stat_hour,
        NULL as appid,
        NULL as tfid,
        SUM(s.uv_count) AS uv_count,
        SUM(s.watch_count) AS watch_count,
        SUM(s.paid_episode_count) AS paid_episode_count,
        SUM(s.order_user_count) AS order_user_count,
        SUM(s.paid_user_count) AS paid_user_count,
        NULL as create_by,
        NOW() as create_time,
        NULL as update_by,
        NOW() as update_time
        FROM statistics_hourly_funnel_stats s
        LEFT JOIN app_promotion p ON s.tfid = p.tfid
        WHERE s.stat_hour >= #{startTime}
          AND s.stat_hour &lt; #{endTime}
          AND (p.create_by = #{creator} OR s.tfid = '' OR s.tfid IS NULL)
    </select>

</mapper>
