<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tv.shorthub.system.mapper.StatisticsHourlyDramaStatsMapper">

    <resultMap type="StatisticsHourlyDramaStats" id="StatisticsHourlyDramaStatsResult">
        <result property="id"    column="id"    />
        <result property="statHour"    column="stat_hour"    />
        <result property="appid"    column="appid"    />
        <result property="contentId"    column="content_id"    />
        <result property="tfid"    column="tfid"    />
        <result property="vvCount"    column="vv_count"    />
        <result property="uvCount"    column="uv_count"    />
        <result property="totalWatchSeconds"    column="total_watch_seconds"    />
        <result property="paidUnlockCount"    column="paid_unlock_count"    />
        <result property="memberUnlockCount"    column="member_unlock_count"    />
        <result property="totalCoinSpent"    column="total_coin_spent"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectStatisticsHourlyDramaStatsVo">
        select id, stat_hour, appid, content_id, tfid, vv_count, uv_count, total_watch_seconds, paid_unlock_count, member_unlock_count, total_coin_spent, create_by, create_time, update_by, update_time from statistics_hourly_drama_stats
    </sql>

    <insert id="batchInsertOrUpdate">
        <foreach item="item" index="index" collection="list" separator=";">
            insert into statistics_hourly_drama_stats
            <trim prefix="(" suffix=")" suffixOverrides=",">
                        <if test="item.statHour != null">stat_hour,</if>
                        <if test="item.appid != null and item.appid != ''">appid,</if>
                        <if test="item.contentId != null and item.contentId != ''">content_id,</if>
                        <if test="item.tfid != null and item.tfid != ''">tfid,</if>
                        <if test="item.vvCount != null">vv_count,</if>
                        <if test="item.uvCount != null">uv_count,</if>
                        <if test="item.totalWatchSeconds != null">total_watch_seconds,</if>
                        <if test="item.paidUnlockCount != null">paid_unlock_count,</if>
                        <if test="item.memberUnlockCount != null">member_unlock_count,</if>
                        <if test="item.totalCoinSpent != null">total_coin_spent,</if>
                        <if test="item.createBy != null">create_by,</if>
                        <if test="item.createTime != null">create_time,</if>
                        <if test="item.updateBy != null">update_by,</if>
                        <if test="item.updateTime != null">update_time,</if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                        <if test="item.statHour != null">#{item.statHour},</if>
                        <if test="item.appid != null and item.appid != ''">#{item.appid},</if>
                        <if test="item.contentId != null and item.contentId != ''">#{item.contentId},</if>
                        <if test="item.tfid != null and item.tfid != ''">#{item.tfid},</if>
                        <if test="item.vvCount != null">#{item.vvCount},</if>
                        <if test="item.uvCount != null">#{item.uvCount},</if>
                        <if test="item.totalWatchSeconds != null">#{item.totalWatchSeconds},</if>
                        <if test="item.paidUnlockCount != null">#{item.paidUnlockCount},</if>
                        <if test="item.memberUnlockCount != null">#{item.memberUnlockCount},</if>
                        <if test="item.totalCoinSpent != null">#{item.totalCoinSpent},</if>
                        <if test="item.createBy != null">#{item.createBy},</if>
                        <if test="item.createTime != null">#{item.createTime},</if>
                        <if test="item.updateBy != null">#{item.updateBy},</if>
                        <if test="item.updateTime != null">#{item.updateTime},</if>
            </trim>
            on duplicate key update
            <trim suffixOverrides=",">
                        <if test="item.statHour != null">stat_hour = values(stat_hour),</if>
                        <if test="item.appid != null and item.appid != ''">appid = values(appid),</if>
                        <if test="item.contentId != null and item.contentId != ''">content_id = values(content_id),</if>
                        <if test="item.tfid != null and item.tfid != ''">tfid = values(tfid),</if>
                        <if test="item.vvCount != null">vv_count = values(vv_count),</if>
                        <if test="item.uvCount != null">uv_count = values(uv_count),</if>
                        <if test="item.totalWatchSeconds != null">total_watch_seconds = values(total_watch_seconds),</if>
                        <if test="item.paidUnlockCount != null">paid_unlock_count = values(paid_unlock_count),</if>
                        <if test="item.memberUnlockCount != null">member_unlock_count = values(member_unlock_count),</if>
                        <if test="item.totalCoinSpent != null">total_coin_spent = values(total_coin_spent),</if>
                        <if test="item.createBy != null">create_by = values(create_by),</if>
                        <if test="item.createTime != null">create_time = values(create_time),</if>
                        <if test="item.updateBy != null">update_by = values(update_by),</if>
                        <if test="item.updateTime != null">update_time = values(update_time),</if>
            </trim>
        </foreach>

    </insert>

    <select id="getSummary" parameterType="StatisticsHourlyDramaStats" resultMap="StatisticsHourlyDramaStatsResult">
        select
            max(id) as id
        from statistics_hourly_drama_stats
        <where>
                    <if test="statHour != null "> and stat_hour = #{statHour}</if>
                    <if test="appid != null  and appid != ''"> and appid = #{appid}</if>
                    <if test="contentId != null  and contentId != ''"> and content_id = #{contentId}</if>
                    <if test="tfid != null  and tfid != ''"> and tfid = #{tfid}</if>
                    <if test="vvCount != null "> and vv_count = #{vvCount}</if>
                    <if test="uvCount != null "> and uv_count = #{uvCount}</if>
                    <if test="totalWatchSeconds != null "> and total_watch_seconds = #{totalWatchSeconds}</if>
                    <if test="paidUnlockCount != null "> and paid_unlock_count = #{paidUnlockCount}</if>
                    <if test="memberUnlockCount != null "> and member_unlock_count = #{memberUnlockCount}</if>
                    <if test="totalCoinSpent != null "> and total_coin_spent = #{totalCoinSpent}</if>
                </where>
    </select>




    <select id="selectHourlyDramaStats" resultMap="StatisticsHourlyDramaStatsResult">
        WITH base_watch_data AS (
            -- 基础观看数据 - 确保appid不为null
            SELECT 
                DATE_FORMAT(w.watched_at, '%Y-%m-%d %H:00:00') as stat_hour,
                COALESCE(w.appid, 'default') as appid,
                w.content_id,
                COALESCE(w.tfid, '') as tfid,
                w.user_id,
                w.serial_number,
                -- 每用户每小时每内容只取最大观看时长，避免重复计算
                MAX(CASE WHEN w.max_watch_second > 0 THEN w.max_watch_second ELSE 60 END) as watch_seconds,
                -- 付费集数逻辑：有tfid用推广链接设置，没有tfid用内容默认设置
                MAX(CASE 
                    WHEN w.tfid IS NOT NULL AND w.tfid != '' AND ap.video_fee_begin IS NOT NULL 
                    THEN ap.video_fee_begin 
                    ELSE COALESCE(dc.fee_begin, 1)
                END) as fee_begin
            FROM app_user_watch_records w
            INNER JOIN app_drama_contents dc ON w.content_id = dc.content_id
            LEFT JOIN app_promotion ap ON w.tfid = ap.tfid AND w.tfid IS NOT NULL AND w.tfid != ''
            WHERE w.watched_at >= #{startTime} AND w.watched_at &lt; #{endTime}
              AND w.content_id IS NOT NULL
              AND COALESCE(w.appid, 'default') IS NOT NULL  -- 确保appid处理后不为null
            -- 关键：按用户、小时、内容分组去重
            GROUP BY DATE_FORMAT(w.watched_at, '%Y-%m-%d %H:00:00'), COALESCE(w.appid, 'default'), 
                     w.content_id, COALESCE(w.tfid, ''), w.user_id, w.serial_number
        ),
        watch_stats AS (
            SELECT 
                stat_hour,
                appid,
                content_id,
                tfid,
                COUNT(*) as vv_count,
                COUNT(DISTINCT user_id) as uv_count,
                SUM(watch_seconds) as total_watch_seconds
            FROM base_watch_data
            GROUP BY stat_hour, appid, content_id, tfid
        ),
        consumption_stats AS (
            -- 金币消费统计 - 通过观看记录关联获取appid和tfid
            SELECT 
                DATE_FORMAT(c.create_time, '%Y-%m-%d %H:00:00') as stat_hour,
                COALESCE(w.appid, c.appid, 'default') as appid,  -- 优先使用观看记录的appid
                COALESCE(w.tfid, '') as tfid,                    -- 从观看记录获取tfid
                c.content_id,
                COUNT(DISTINCT c.user_id) as paid_unlock_count,
                SUM(ABS(c.amount)) as total_coin_spent
            FROM app_consumption c
            -- 关联观看记录获取tfid和appid
            LEFT JOIN (
                SELECT DISTINCT user_id, content_id, appid, tfid
                FROM app_user_watch_records 
                WHERE watched_at >= #{startTime} AND watched_at &lt; #{endTime}
            ) w ON c.user_id = w.user_id AND c.content_id = w.content_id
            WHERE c.amount &lt; 0  -- 消费记录（负数）
              AND c.create_time >= #{startTime} AND c.create_time &lt; #{endTime}
              AND c.content_id IS NOT NULL
            GROUP BY 
                DATE_FORMAT(c.create_time, '%Y-%m-%d %H:00:00'),
                COALESCE(w.appid, c.appid, 'default'),
                COALESCE(w.tfid, ''),
                c.content_id
        ),
        subscribers AS (
            -- 订阅用户
            SELECT DISTINCT 
                user_id, 
                COALESCE(appid, 'default') as appid
            FROM app_order_info 
            WHERE order_status = 1 AND pay_type = '2' AND pay_time IS NOT NULL
        ),
        member_unlock_stats AS (
            -- 会员解锁统计
            SELECT 
                w.stat_hour,
                w.appid,
                w.content_id,
                COUNT(DISTINCT w.user_id) as member_unlock_count
            FROM base_watch_data w
            INNER JOIN subscribers s ON w.user_id = s.user_id AND w.appid = s.appid
            WHERE w.serial_number >= w.fee_begin
            GROUP BY w.stat_hour, w.appid, w.content_id
        )
        SELECT 
            STR_TO_DATE(ws.stat_hour, '%Y-%m-%d %H:%i:%s') as stat_hour,
            ws.appid,
            ws.content_id,
            ws.tfid,
            ws.vv_count,
            ws.uv_count,
            ws.total_watch_seconds,
            COALESCE(cs.paid_unlock_count, 0) as paid_unlock_count,
            COALESCE(ms.member_unlock_count, 0) as member_unlock_count,
            COALESCE(cs.total_coin_spent, 0) as total_coin_spent,
            'system' as create_by,
            NOW() as create_time,
            'system' as update_by,
            NOW() as update_time
        FROM watch_stats ws
        -- 完整关联：按时间、appid、tfid、content_id精确匹配
        LEFT JOIN consumption_stats cs ON ws.stat_hour = cs.stat_hour
            AND ws.appid = cs.appid
            AND ws.tfid = cs.tfid
            AND ws.content_id = cs.content_id
        LEFT JOIN member_unlock_stats ms ON ws.stat_hour = ms.stat_hour
            AND ws.appid = ms.appid 
            AND ws.content_id = ms.content_id
        ORDER BY ws.stat_hour, ws.appid, ws.content_id;
    </select>

    <!-- 根据时间范围获取剧集统计汇总数据 - 修复重复计算问题 -->
    <select id="getRangeSummary" resultMap="StatisticsHourlyDramaStatsResult">
        WITH range_data AS (
            SELECT 
                stat_hour,
                appid,
                content_id,
                tfid,
                vv_count,
                uv_count,
                total_watch_seconds,
                paid_unlock_count,
                member_unlock_count,
                total_coin_spent
            FROM statistics_hourly_drama_stats
            WHERE stat_hour >= #{startTime} AND stat_hour &lt; #{endTime}
            <if test="appid != null and appid != ''">
                AND appid = #{appid}
            </if>
            <if test="tfid != null and tfid != ''">
                AND tfid = #{tfid}
            </if>
        ),
        unique_users AS (
            -- 重新从原始数据计算去重的用户统计，避免小时间重复计算
            SELECT 
                COALESCE(w.appid, 'default') as appid,
                w.content_id,
                COALESCE(w.tfid, '') as tfid,
                COUNT(DISTINCT w.user_id) as unique_uv_count,
                -- 会员用户去重统计
                COUNT(DISTINCT CASE WHEN s.user_id IS NOT NULL AND w.serial_number >= 
                    CASE 
                        WHEN w.tfid IS NOT NULL AND w.tfid != '' AND ap.video_fee_begin IS NOT NULL 
                        THEN ap.video_fee_begin 
                        ELSE COALESCE(dc.fee_begin, 1)
                    END
                THEN w.user_id END) as unique_member_unlock_count
            FROM app_user_watch_records w
            INNER JOIN app_drama_contents dc ON w.content_id = dc.content_id
            LEFT JOIN app_promotion ap ON w.tfid = ap.tfid AND w.tfid IS NOT NULL AND w.tfid != ''
            -- 关联订阅用户
            LEFT JOIN (
                SELECT DISTINCT user_id, COALESCE(appid, 'default') as appid
                FROM app_order_info 
                WHERE order_status = 1 AND pay_type = '2' AND pay_time IS NOT NULL
            ) s ON w.user_id = s.user_id AND COALESCE(w.appid, 'default') = s.appid
            WHERE w.watched_at >= #{startTime} AND w.watched_at &lt; #{endTime}
              AND w.content_id IS NOT NULL
              AND COALESCE(w.appid, 'default') IS NOT NULL
            <if test="appid != null and appid != ''">
                AND COALESCE(w.appid, 'default') = #{appid}
            </if>
            <if test="tfid != null and tfid != ''">
                AND COALESCE(w.tfid, '') = #{tfid}
            </if>
            GROUP BY COALESCE(w.appid, 'default'), w.content_id, COALESCE(w.tfid, '')
        )
        SELECT 
            SUM(rd.vv_count) as vv_count,
            -- 使用去重后的用户数据，避免重复计算
            COALESCE(SUM(uu.unique_uv_count), 0) as uv_count,
            SUM(rd.total_watch_seconds) as total_watch_seconds,
            SUM(rd.paid_unlock_count) as paid_unlock_count,
            -- 使用去重后的会员解锁数据，避免重复计算
            COALESCE(SUM(uu.unique_member_unlock_count), 0) as member_unlock_count,
            SUM(rd.total_coin_spent) as total_coin_spent
        FROM range_data rd
        LEFT JOIN unique_users uu ON rd.appid = uu.appid 
            AND rd.content_id = uu.content_id 
            AND rd.tfid = uu.tfid
    </select>

    <!-- 根据时间范围查询剧集统计列表 -->
    <select id="selectListByTimeRange" resultMap="StatisticsHourlyDramaStatsResult">
        <include refid="selectStatisticsHourlyDramaStatsVo"/>
        WHERE stat_hour >= #{startTime} AND stat_hour &lt; #{endTime}
        <if test="query.appid != null and query.appid != ''">
            AND appid = #{query.appid}
        </if>
        <if test="query.tfid != null and query.tfid != ''">
            AND tfid = #{query.tfid}
        </if>
        <if test="query.contentId != null and query.contentId != ''">
            AND content_id = #{query.contentId}
        </if>
        ORDER BY stat_hour DESC
    </select>

    <!-- 根据 tfid 列表获取汇总数据 - 修复重复计算问题 -->
    <select id="getSummaryByTfids" resultMap="StatisticsHourlyDramaStatsResult">
        WITH range_data AS (
            SELECT 
                stat_hour,
                appid,
                content_id,
                tfid,
                vv_count,
                uv_count,
                total_watch_seconds,
                paid_unlock_count,
                member_unlock_count,
                total_coin_spent
            FROM statistics_hourly_drama_stats
            WHERE tfid IN
            <foreach collection="tfids" item="tfid" open="(" separator="," close=")">
                #{tfid}
            </foreach>
            <if test="startTime != null">
                AND stat_hour >= #{startTime}
            </if>
            <if test="endTime != null">
                AND stat_hour &lt; #{endTime}
            </if>
            <if test="appid != null and appid != ''">
                AND appid = #{appid}
            </if>
        ),
        unique_users AS (
            -- 重新从原始数据计算去重的用户统计，避免小时间重复计算
            SELECT 
                COALESCE(w.appid, 'default') as appid,
                w.content_id,
                COALESCE(w.tfid, '') as tfid,
                COUNT(DISTINCT w.user_id) as unique_uv_count,
                -- 会员用户去重统计
                COUNT(DISTINCT CASE WHEN s.user_id IS NOT NULL AND w.serial_number >= 
                    CASE 
                        WHEN w.tfid IS NOT NULL AND w.tfid != '' AND ap.video_fee_begin IS NOT NULL 
                        THEN ap.video_fee_begin 
                        ELSE COALESCE(dc.fee_begin, 1)
                    END
                THEN w.user_id END) as unique_member_unlock_count
            FROM app_user_watch_records w
            INNER JOIN app_drama_contents dc ON w.content_id = dc.content_id
            LEFT JOIN app_promotion ap ON w.tfid = ap.tfid AND w.tfid IS NOT NULL AND w.tfid != ''
            -- 关联订阅用户
            LEFT JOIN (
                SELECT DISTINCT user_id, COALESCE(appid, 'default') as appid
                FROM app_order_info 
                WHERE order_status = 1 AND pay_type = '2' AND pay_time IS NOT NULL
            ) s ON w.user_id = s.user_id AND COALESCE(w.appid, 'default') = s.appid
            WHERE COALESCE(w.tfid, '') IN
            <foreach collection="tfids" item="tfid" open="(" separator="," close=")">
                #{tfid}
            </foreach>
            <if test="startTime != null">
                AND w.watched_at >= #{startTime}
            </if>
            <if test="endTime != null">
                AND w.watched_at &lt; #{endTime}
            </if>
            <if test="appid != null and appid != ''">
                AND COALESCE(w.appid, 'default') = #{appid}
            </if>
            AND w.content_id IS NOT NULL
            AND COALESCE(w.appid, 'default') IS NOT NULL
            GROUP BY COALESCE(w.appid, 'default'), w.content_id, COALESCE(w.tfid, '')
        )
        SELECT 
            SUM(rd.vv_count) as vv_count,
            -- 使用去重后的用户数据，避免重复计算
            COALESCE(SUM(uu.unique_uv_count), 0) as uv_count,
            SUM(rd.total_watch_seconds) as total_watch_seconds,
            SUM(rd.paid_unlock_count) as paid_unlock_count,
            -- 使用去重后的会员解锁数据，避免重复计算
            COALESCE(SUM(uu.unique_member_unlock_count), 0) as member_unlock_count,
            SUM(rd.total_coin_spent) as total_coin_spent
        FROM range_data rd
        LEFT JOIN unique_users uu ON rd.appid = uu.appid 
            AND rd.content_id = uu.content_id 
            AND rd.tfid = uu.tfid
    </select>

    <!-- 根据创建者获取汇总数据 - 修复重复计算问题 -->
    <select id="getSummaryByCreator" resultMap="StatisticsHourlyDramaStatsResult">
        WITH range_data AS (
            SELECT 
                h.stat_hour,
                h.appid,
                h.content_id,
                h.tfid,
                h.vv_count,
                h.uv_count,
                h.total_watch_seconds,
                h.paid_unlock_count,
                h.member_unlock_count,
                h.total_coin_spent
            FROM statistics_hourly_drama_stats h
            INNER JOIN app_promotion ap ON h.tfid = ap.tfid
            WHERE ap.create_by = #{creatorName}
            <if test="startTime != null">
                AND h.stat_hour >= #{startTime}
            </if>
            <if test="endTime != null">
                AND h.stat_hour &lt; #{endTime}
            </if>
        ),
        unique_users AS (
            -- 重新从原始数据计算去重的用户统计，避免小时间重复计算
            SELECT 
                COALESCE(w.appid, 'default') as appid,
                w.content_id,
                COALESCE(w.tfid, '') as tfid,
                COUNT(DISTINCT w.user_id) as unique_uv_count,
                -- 会员用户去重统计
                COUNT(DISTINCT CASE WHEN s.user_id IS NOT NULL AND w.serial_number >= 
                    CASE 
                        WHEN w.tfid IS NOT NULL AND w.tfid != '' AND ap.video_fee_begin IS NOT NULL 
                        THEN ap.video_fee_begin 
                        ELSE COALESCE(dc.fee_begin, 1)
                    END
                THEN w.user_id END) as unique_member_unlock_count
            FROM app_user_watch_records w
            INNER JOIN app_drama_contents dc ON w.content_id = dc.content_id
            LEFT JOIN app_promotion ap ON w.tfid = ap.tfid AND w.tfid IS NOT NULL AND w.tfid != ''
            -- 关联订阅用户
            LEFT JOIN (
                SELECT DISTINCT user_id, COALESCE(appid, 'default') as appid
                FROM app_order_info 
                WHERE order_status = 1 AND pay_type = '2' AND pay_time IS NOT NULL
            ) s ON w.user_id = s.user_id AND COALESCE(w.appid, 'default') = s.appid
            WHERE ap.create_by = #{creatorName}
            <if test="startTime != null">
                AND w.watched_at >= #{startTime}
            </if>
            <if test="endTime != null">
                AND w.watched_at &lt; #{endTime}
            </if>
            AND w.content_id IS NOT NULL
            AND COALESCE(w.appid, 'default') IS NOT NULL
            GROUP BY COALESCE(w.appid, 'default'), w.content_id, COALESCE(w.tfid, '')
        )
        SELECT 
            SUM(rd.vv_count) as vv_count,
            -- 使用去重后的用户数据，避免重复计算
            COALESCE(SUM(uu.unique_uv_count), 0) as uv_count,
            SUM(rd.total_watch_seconds) as total_watch_seconds,
            SUM(rd.paid_unlock_count) as paid_unlock_count,
            -- 使用去重后的会员解锁数据，避免重复计算
            COALESCE(SUM(uu.unique_member_unlock_count), 0) as member_unlock_count,
            SUM(rd.total_coin_spent) as total_coin_spent
        FROM range_data rd
        LEFT JOIN unique_users uu ON rd.appid = uu.appid 
            AND rd.content_id = uu.content_id 
            AND rd.tfid = uu.tfid
    </select>

</mapper>
