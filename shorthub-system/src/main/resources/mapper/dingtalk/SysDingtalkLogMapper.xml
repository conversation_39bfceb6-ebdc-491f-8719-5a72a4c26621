<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tv.shorthub.system.mapper.SysDingtalkLogMapper">

    <resultMap type="SysDingtalkLog" id="SysDingtalkLogResult">
        <result property="id"    column="id"    />
        <result property="businessId"    column="business_id"    />
        <result property="businessType"    column="business_type"    />
        <result property="eventType"    column="event_type"    />
        <result property="webhookUrl"    column="webhook_url"    />
        <result property="messageContent"    column="message_content"    />
        <result property="responseCode"    column="response_code"    />
        <result property="responseMessage"    column="response_message"    />
        <result property="status"    column="status"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectSysDingtalkLogVo">
        select id, business_id, business_type, event_type, webhook_url, message_content, response_code, response_message, status, create_time from sys_dingtalk_log
    </sql>

    <insert id="batchInsertOrUpdate">
        <foreach item="item" index="index" collection="list" separator=";">
            insert into sys_dingtalk_log
            <trim prefix="(" suffix=")" suffixOverrides=",">
                        <if test="item.businessId != null and item.businessId != ''">business_id,</if>
                        <if test="item.businessType != null">business_type,</if>
                        <if test="item.eventType != null and item.eventType != ''">event_type,</if>
                        <if test="item.webhookUrl != null and item.webhookUrl != ''">webhook_url,</if>
                        <if test="item.messageContent != null">message_content,</if>
                        <if test="item.responseCode != null">response_code,</if>
                        <if test="item.responseMessage != null">response_message,</if>
                        <if test="item.status != null and item.status != ''">status,</if>
                        <if test="item.createTime != null">create_time,</if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                        <if test="item.businessId != null and item.businessId != ''">#{item.businessId},</if>
                        <if test="item.businessType != null">#{item.businessType},</if>
                        <if test="item.eventType != null and item.eventType != ''">#{item.eventType},</if>
                        <if test="item.webhookUrl != null and item.webhookUrl != ''">#{item.webhookUrl},</if>
                        <if test="item.messageContent != null">#{item.messageContent},</if>
                        <if test="item.responseCode != null">#{item.responseCode},</if>
                        <if test="item.responseMessage != null">#{item.responseMessage},</if>
                        <if test="item.status != null and item.status != ''">#{item.status},</if>
                        <if test="item.createTime != null">#{item.createTime},</if>
            </trim>
            on duplicate key update
            <trim suffixOverrides=",">
                        <if test="item.businessId != null and item.businessId != ''">business_id = values(business_id),</if>
                        <if test="item.businessType != null">business_type = values(business_type),</if>
                        <if test="item.eventType != null and item.eventType != ''">event_type = values(event_type),</if>
                        <if test="item.webhookUrl != null and item.webhookUrl != ''">webhook_url = values(webhook_url),</if>
                        <if test="item.messageContent != null">message_content = values(message_content),</if>
                        <if test="item.responseCode != null">response_code = values(response_code),</if>
                        <if test="item.responseMessage != null">response_message = values(response_message),</if>
                        <if test="item.status != null and item.status != ''">status = values(status),</if>
                        <if test="item.createTime != null">create_time = values(create_time),</if>
            </trim>
        </foreach>

    </insert>

    <select id="getSummary" parameterType="SysDingtalkLog" resultMap="SysDingtalkLogResult">
        select
            max(id) as id
        from sys_dingtalk_log
        <where>
                    <if test="businessId != null  and businessId != ''"> and business_id = #{businessId}</if>
                    <if test="businessType != null  and businessType != ''"> and business_type = #{businessType}</if>
                    <if test="eventType != null  and eventType != ''"> and event_type = #{eventType}</if>
                    <if test="webhookUrl != null  and webhookUrl != ''"> and webhook_url = #{webhookUrl}</if>
                    <if test="messageContent != null  and messageContent != ''"> and message_content = #{messageContent}</if>
                    <if test="responseCode != null  and responseCode != ''"> and response_code = #{responseCode}</if>
                    <if test="responseMessage != null  and responseMessage != ''"> and response_message = #{responseMessage}</if>
                    <if test="status != null  and status != ''"> and status = #{status}</if>
                </where>
    </select>



    <select id="summary" parameterType="tv.shorthub.common.core.domain.SummaryRequest" resultMap="SysDingtalkLogResult">
        
    </select>
    <select id="allSummary" parameterType="tv.shorthub.common.core.domain.SummaryRequest" resultMap="SysDingtalkLogResult">
        
    </select>

</mapper>
