<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tv.shorthub.system.mapper.CryptoTronBlockMapper">

    <resultMap type="CryptoTronBlock" id="CryptoTronBlockResult">
        <result property="id"    column="id"    />
        <result property="blockHash"    column="block_hash"    />
        <result property="blockNumber"    column="block_number"    />
        <result property="parentHash"    column="parent_hash"    />
        <result property="timestamp"    column="timestamp"    />
        <result property="transactionsCount"    column="transactions_count"    />
        <result property="witnessAddress"    column="witness_address"    />
        <result property="witnessSignature"    column="witness_signature"    />
        <result property="size"    column="size"    />
        <result property="gasUsed"    column="gas_used"    />
        <result property="gasLimit"    column="gas_limit"    />
        <result property="difficulty"    column="difficulty"    />
        <result property="nonce"    column="nonce"    />
        <result property="merkleRoot"    column="merkle_root"    />
        <result property="stateRoot"    column="state_root"    />
        <result property="receiptsRoot"    column="receipts_root"    />
        <result property="extraData"    column="extra_data"    />
        <result property="rawData"    column="raw_data"    />
        <result property="fullContent"    column="full_content"    />
        <result property="isConfirmed"    column="is_confirmed"    />
        <result property="confirmations"    column="confirmations"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectCryptoTronBlockVo">
        select id, block_hash, block_number, parent_hash, timestamp, transactions_count, witness_address, witness_signature, size, gas_used, gas_limit, difficulty, nonce, merkle_root, state_root, receipts_root, extra_data, raw_data, full_content, is_confirmed, confirmations, create_time, update_time from crypto_tron_block
    </sql>

    <insert id="batchInsertOrUpdate">
        <foreach item="item" index="index" collection="list" separator=";">
            insert into crypto_tron_block
            <trim prefix="(" suffix=")" suffixOverrides=",">
                        <if test="item.blockHash != null and item.blockHash != ''">block_hash,</if>
                        <if test="item.blockNumber != null">block_number,</if>
                        <if test="item.parentHash != null">parent_hash,</if>
                        <if test="item.timestamp != null">timestamp,</if>
                        <if test="item.transactionsCount != null">transactions_count,</if>
                        <if test="item.witnessAddress != null">witness_address,</if>
                        <if test="item.witnessSignature != null">witness_signature,</if>
                        <if test="item.size != null">size,</if>
                        <if test="item.gasUsed != null">gas_used,</if>
                        <if test="item.gasLimit != null">gas_limit,</if>
                        <if test="item.difficulty != null">difficulty,</if>
                        <if test="item.nonce != null">nonce,</if>
                        <if test="item.merkleRoot != null">merkle_root,</if>
                        <if test="item.stateRoot != null">state_root,</if>
                        <if test="item.receiptsRoot != null">receipts_root,</if>
                        <if test="item.extraData != null">extra_data,</if>
                        <if test="item.rawData != null">raw_data,</if>
                        <if test="item.fullContent != null">full_content,</if>
                        <if test="item.isConfirmed != null">is_confirmed,</if>
                        <if test="item.confirmations != null">confirmations,</if>
                        <if test="item.createTime != null">create_time,</if>
                        <if test="item.updateTime != null">update_time,</if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                        <if test="item.blockHash != null and item.blockHash != ''">#{item.blockHash},</if>
                        <if test="item.blockNumber != null">#{item.blockNumber},</if>
                        <if test="item.parentHash != null">#{item.parentHash},</if>
                        <if test="item.timestamp != null">#{item.timestamp},</if>
                        <if test="item.transactionsCount != null">#{item.transactionsCount},</if>
                        <if test="item.witnessAddress != null">#{item.witnessAddress},</if>
                        <if test="item.witnessSignature != null">#{item.witnessSignature},</if>
                        <if test="item.size != null">#{item.size},</if>
                        <if test="item.gasUsed != null">#{item.gasUsed},</if>
                        <if test="item.gasLimit != null">#{item.gasLimit},</if>
                        <if test="item.difficulty != null">#{item.difficulty},</if>
                        <if test="item.nonce != null">#{item.nonce},</if>
                        <if test="item.merkleRoot != null">#{item.merkleRoot},</if>
                        <if test="item.stateRoot != null">#{item.stateRoot},</if>
                        <if test="item.receiptsRoot != null">#{item.receiptsRoot},</if>
                        <if test="item.extraData != null">#{item.extraData},</if>
                        <if test="item.rawData != null">#{item.rawData},</if>
                        <if test="item.fullContent != null">#{item.fullContent},</if>
                        <if test="item.isConfirmed != null">#{item.isConfirmed},</if>
                        <if test="item.confirmations != null">#{item.confirmations},</if>
                        <if test="item.createTime != null">#{item.createTime},</if>
                        <if test="item.updateTime != null">#{item.updateTime},</if>
            </trim>
            on duplicate key update
            <trim suffixOverrides=",">
                        <if test="item.blockHash != null and item.blockHash != ''">block_hash = values(block_hash),</if>
                        <if test="item.blockNumber != null">block_number = values(block_number),</if>
                        <if test="item.parentHash != null">parent_hash = values(parent_hash),</if>
                        <if test="item.timestamp != null">timestamp = values(timestamp),</if>
                        <if test="item.transactionsCount != null">transactions_count = values(transactions_count),</if>
                        <if test="item.witnessAddress != null">witness_address = values(witness_address),</if>
                        <if test="item.witnessSignature != null">witness_signature = values(witness_signature),</if>
                        <if test="item.size != null">size = values(size),</if>
                        <if test="item.gasUsed != null">gas_used = values(gas_used),</if>
                        <if test="item.gasLimit != null">gas_limit = values(gas_limit),</if>
                        <if test="item.difficulty != null">difficulty = values(difficulty),</if>
                        <if test="item.nonce != null">nonce = values(nonce),</if>
                        <if test="item.merkleRoot != null">merkle_root = values(merkle_root),</if>
                        <if test="item.stateRoot != null">state_root = values(state_root),</if>
                        <if test="item.receiptsRoot != null">receipts_root = values(receipts_root),</if>
                        <if test="item.extraData != null">extra_data = values(extra_data),</if>
                        <if test="item.rawData != null">raw_data = values(raw_data),</if>
                        <if test="item.fullContent != null">full_content = values(full_content),</if>
                        <if test="item.isConfirmed != null">is_confirmed = values(is_confirmed),</if>
                        <if test="item.confirmations != null">confirmations = values(confirmations),</if>
                        <if test="item.createTime != null">create_time = values(create_time),</if>
                        <if test="item.updateTime != null">update_time = values(update_time),</if>
            </trim>
        </foreach>

    </insert>

    <select id="getSummary" parameterType="CryptoTronBlock" resultMap="CryptoTronBlockResult">
        select
            max(id) as id
        from crypto_tron_block
        <where>
                    <if test="blockHash != null  and blockHash != ''"> and block_hash = #{blockHash}</if>
                    <if test="blockNumber != null "> and block_number = #{blockNumber}</if>
                    <if test="parentHash != null  and parentHash != ''"> and parent_hash = #{parentHash}</if>
                    <if test="timestamp != null "> and timestamp = #{timestamp}</if>
                    <if test="transactionsCount != null "> and transactions_count = #{transactionsCount}</if>
                    <if test="witnessAddress != null  and witnessAddress != ''"> and witness_address = #{witnessAddress}</if>
                    <if test="witnessSignature != null  and witnessSignature != ''"> and witness_signature = #{witnessSignature}</if>
                    <if test="size != null "> and size = #{size}</if>
                    <if test="gasUsed != null "> and gas_used = #{gasUsed}</if>
                    <if test="gasLimit != null "> and gas_limit = #{gasLimit}</if>
                    <if test="difficulty != null  and difficulty != ''"> and difficulty = #{difficulty}</if>
                    <if test="nonce != null  and nonce != ''"> and nonce = #{nonce}</if>
                    <if test="merkleRoot != null  and merkleRoot != ''"> and merkle_root = #{merkleRoot}</if>
                    <if test="stateRoot != null  and stateRoot != ''"> and state_root = #{stateRoot}</if>
                    <if test="receiptsRoot != null  and receiptsRoot != ''"> and receipts_root = #{receiptsRoot}</if>
                    <if test="extraData != null  and extraData != ''"> and extra_data = #{extraData}</if>
                    <if test="rawData != null  and rawData != ''"> and raw_data = #{rawData}</if>
                    <if test="fullContent != null  and fullContent != ''"> and full_content = #{fullContent}</if>
                    <if test="isConfirmed != null "> and is_confirmed = #{isConfirmed}</if>
                    <if test="confirmations != null "> and confirmations = #{confirmations}</if>
                </where>
    </select>



    <select id="summary" parameterType="tv.shorthub.common.core.domain.SummaryRequest" resultMap="CryptoTronBlockResult">
        
    </select>
    <select id="allSummary" parameterType="tv.shorthub.common.core.domain.SummaryRequest" resultMap="CryptoTronBlockResult">
        
    </select>

</mapper>
