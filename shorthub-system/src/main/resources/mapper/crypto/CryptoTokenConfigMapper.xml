<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tv.shorthub.system.mapper.CryptoTokenConfigMapper">

    <resultMap type="CryptoTokenConfig" id="CryptoTokenConfigResult">
        <result property="id"    column="id"    />
        <result property="blockchain"    column="blockchain"    />
        <result property="tokenType"    column="token_type"    />
        <result property="tokenSymbol"    column="token_symbol"    />
        <result property="tokenName"    column="token_name"    />
        <result property="contractAddress"    column="contract_address"    />
        <result property="decimals"    column="decimals"    />
        <result property="minDepositAmount"    column="min_deposit_amount"    />
        <result property="maxDepositAmount"    column="max_deposit_amount"    />
        <result property="requiredConfirmations"    column="required_confirmations"    />
        <result property="isActive"    column="is_active"    />
        <result property="gasLimit"    column="gas_limit"    />
        <result property="gasPrice"    column="gas_price"    />
        <result property="networkFee"    column="network_fee"    />
        <result property="configJson"    column="config_json"    />
        <result property="remark"    column="remark"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
    </resultMap>

    <sql id="selectCryptoTokenConfigVo">
        select id, blockchain, token_type, token_symbol, token_name, contract_address, decimals, min_deposit_amount, max_deposit_amount, required_confirmations, is_active, gas_limit, gas_price, network_fee, config_json, remark, create_time, update_time, create_by, update_by from crypto_token_config
    </sql>

    <insert id="batchInsertOrUpdate">
        <foreach item="item" index="index" collection="list" separator=";">
            insert into crypto_token_config
            <trim prefix="(" suffix=")" suffixOverrides=",">
                        <if test="item.blockchain != null and item.blockchain != ''">blockchain,</if>
                        <if test="item.tokenType != null and item.tokenType != ''">token_type,</if>
                        <if test="item.tokenSymbol != null and item.tokenSymbol != ''">token_symbol,</if>
                        <if test="item.tokenName != null and item.tokenName != ''">token_name,</if>
                        <if test="item.contractAddress != null">contract_address,</if>
                        <if test="item.decimals != null">decimals,</if>
                        <if test="item.minDepositAmount != null">min_deposit_amount,</if>
                        <if test="item.maxDepositAmount != null">max_deposit_amount,</if>
                        <if test="item.requiredConfirmations != null">required_confirmations,</if>
                        <if test="item.isActive != null">is_active,</if>
                        <if test="item.gasLimit != null">gas_limit,</if>
                        <if test="item.gasPrice != null">gas_price,</if>
                        <if test="item.networkFee != null">network_fee,</if>
                        <if test="item.configJson != null">config_json,</if>
                        <if test="item.remark != null">remark,</if>
                        <if test="item.createTime != null">create_time,</if>
                        <if test="item.updateTime != null">update_time,</if>
                        <if test="item.createBy != null">create_by,</if>
                        <if test="item.updateBy != null">update_by,</if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                        <if test="item.blockchain != null and item.blockchain != ''">#{item.blockchain},</if>
                        <if test="item.tokenType != null and item.tokenType != ''">#{item.tokenType},</if>
                        <if test="item.tokenSymbol != null and item.tokenSymbol != ''">#{item.tokenSymbol},</if>
                        <if test="item.tokenName != null and item.tokenName != ''">#{item.tokenName},</if>
                        <if test="item.contractAddress != null">#{item.contractAddress},</if>
                        <if test="item.decimals != null">#{item.decimals},</if>
                        <if test="item.minDepositAmount != null">#{item.minDepositAmount},</if>
                        <if test="item.maxDepositAmount != null">#{item.maxDepositAmount},</if>
                        <if test="item.requiredConfirmations != null">#{item.requiredConfirmations},</if>
                        <if test="item.isActive != null">#{item.isActive},</if>
                        <if test="item.gasLimit != null">#{item.gasLimit},</if>
                        <if test="item.gasPrice != null">#{item.gasPrice},</if>
                        <if test="item.networkFee != null">#{item.networkFee},</if>
                        <if test="item.configJson != null">#{item.configJson},</if>
                        <if test="item.remark != null">#{item.remark},</if>
                        <if test="item.createTime != null">#{item.createTime},</if>
                        <if test="item.updateTime != null">#{item.updateTime},</if>
                        <if test="item.createBy != null">#{item.createBy},</if>
                        <if test="item.updateBy != null">#{item.updateBy},</if>
            </trim>
            on duplicate key update
            <trim suffixOverrides=",">
                        <if test="item.blockchain != null and item.blockchain != ''">blockchain = values(blockchain),</if>
                        <if test="item.tokenType != null and item.tokenType != ''">token_type = values(token_type),</if>
                        <if test="item.tokenSymbol != null and item.tokenSymbol != ''">token_symbol = values(token_symbol),</if>
                        <if test="item.tokenName != null and item.tokenName != ''">token_name = values(token_name),</if>
                        <if test="item.contractAddress != null">contract_address = values(contract_address),</if>
                        <if test="item.decimals != null">decimals = values(decimals),</if>
                        <if test="item.minDepositAmount != null">min_deposit_amount = values(min_deposit_amount),</if>
                        <if test="item.maxDepositAmount != null">max_deposit_amount = values(max_deposit_amount),</if>
                        <if test="item.requiredConfirmations != null">required_confirmations = values(required_confirmations),</if>
                        <if test="item.isActive != null">is_active = values(is_active),</if>
                        <if test="item.gasLimit != null">gas_limit = values(gas_limit),</if>
                        <if test="item.gasPrice != null">gas_price = values(gas_price),</if>
                        <if test="item.networkFee != null">network_fee = values(network_fee),</if>
                        <if test="item.configJson != null">config_json = values(config_json),</if>
                        <if test="item.remark != null">remark = values(remark),</if>
                        <if test="item.createTime != null">create_time = values(create_time),</if>
                        <if test="item.updateTime != null">update_time = values(update_time),</if>
                        <if test="item.createBy != null">create_by = values(create_by),</if>
                        <if test="item.updateBy != null">update_by = values(update_by),</if>
            </trim>
        </foreach>

    </insert>

    <select id="getSummary" parameterType="CryptoTokenConfig" resultMap="CryptoTokenConfigResult">
        select
            max(id) as id
        from crypto_token_config
        <where>
                    <if test="blockchain != null  and blockchain != ''"> and blockchain = #{blockchain}</if>
                    <if test="tokenType != null  and tokenType != ''"> and token_type = #{tokenType}</if>
                    <if test="tokenSymbol != null  and tokenSymbol != ''"> and token_symbol = #{tokenSymbol}</if>
                    <if test="tokenName != null  and tokenName != ''"> and token_name like concat('%', #{tokenName}, '%')</if>
                    <if test="contractAddress != null  and contractAddress != ''"> and contract_address = #{contractAddress}</if>
                    <if test="decimals != null "> and decimals = #{decimals}</if>
                    <if test="minDepositAmount != null "> and min_deposit_amount = #{minDepositAmount}</if>
                    <if test="maxDepositAmount != null "> and max_deposit_amount = #{maxDepositAmount}</if>
                    <if test="requiredConfirmations != null "> and required_confirmations = #{requiredConfirmations}</if>
                    <if test="isActive != null "> and is_active = #{isActive}</if>
                    <if test="gasLimit != null "> and gas_limit = #{gasLimit}</if>
                    <if test="gasPrice != null "> and gas_price = #{gasPrice}</if>
                    <if test="networkFee != null "> and network_fee = #{networkFee}</if>
                    <if test="configJson != null "> and config_json = #{configJson}</if>
                </where>
    </select>



    <select id="summary" parameterType="tv.shorthub.common.core.domain.SummaryRequest" resultMap="CryptoTokenConfigResult">
        
    </select>
    <select id="allSummary" parameterType="tv.shorthub.common.core.domain.SummaryRequest" resultMap="CryptoTokenConfigResult">
        
    </select>

</mapper>
