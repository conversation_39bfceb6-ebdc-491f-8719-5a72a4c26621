<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tv.shorthub.system.mapper.CryptoMonitorLogMapper">

    <resultMap type="CryptoMonitorLog" id="CryptoMonitorLogResult">
        <result property="id"    column="id"    />
        <result property="blockchain"    column="blockchain"    />
        <result property="walletAddress"    column="wallet_address"    />
        <result property="lastBlockHeight"    column="last_block_height"    />
        <result property="lastMonitorTime"    column="last_monitor_time"    />
        <result property="monitorStatus"    column="monitor_status"    />
        <result property="errorCount"    column="error_count"    />
        <result property="lastError"    column="last_error"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectCryptoMonitorLogVo">
        select id, blockchain, wallet_address, last_block_height, last_monitor_time, monitor_status, error_count, last_error, create_time, update_time from crypto_monitor_log
    </sql>

    <insert id="batchInsertOrUpdate">
        <foreach item="item" index="index" collection="list" separator=";">
            insert into crypto_monitor_log
            <trim prefix="(" suffix=")" suffixOverrides=",">
                        <if test="item.blockchain != null and item.blockchain != ''">blockchain,</if>
                        <if test="item.walletAddress != null and item.walletAddress != ''">wallet_address,</if>
                        <if test="item.lastBlockHeight != null">last_block_height,</if>
                        <if test="item.lastMonitorTime != null">last_monitor_time,</if>
                        <if test="item.monitorStatus != null">monitor_status,</if>
                        <if test="item.errorCount != null">error_count,</if>
                        <if test="item.lastError != null">last_error,</if>
                        <if test="item.createTime != null">create_time,</if>
                        <if test="item.updateTime != null">update_time,</if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                        <if test="item.blockchain != null and item.blockchain != ''">#{item.blockchain},</if>
                        <if test="item.walletAddress != null and item.walletAddress != ''">#{item.walletAddress},</if>
                        <if test="item.lastBlockHeight != null">#{item.lastBlockHeight},</if>
                        <if test="item.lastMonitorTime != null">#{item.lastMonitorTime},</if>
                        <if test="item.monitorStatus != null">#{item.monitorStatus},</if>
                        <if test="item.errorCount != null">#{item.errorCount},</if>
                        <if test="item.lastError != null">#{item.lastError},</if>
                        <if test="item.createTime != null">#{item.createTime},</if>
                        <if test="item.updateTime != null">#{item.updateTime},</if>
            </trim>
            on duplicate key update
            <trim suffixOverrides=",">
                        <if test="item.blockchain != null and item.blockchain != ''">blockchain = values(blockchain),</if>
                        <if test="item.walletAddress != null and item.walletAddress != ''">wallet_address = values(wallet_address),</if>
                        <if test="item.lastBlockHeight != null">last_block_height = values(last_block_height),</if>
                        <if test="item.lastMonitorTime != null">last_monitor_time = values(last_monitor_time),</if>
                        <if test="item.monitorStatus != null">monitor_status = values(monitor_status),</if>
                        <if test="item.errorCount != null">error_count = values(error_count),</if>
                        <if test="item.lastError != null">last_error = values(last_error),</if>
                        <if test="item.createTime != null">create_time = values(create_time),</if>
                        <if test="item.updateTime != null">update_time = values(update_time),</if>
            </trim>
        </foreach>

    </insert>

    <select id="getSummary" parameterType="CryptoMonitorLog" resultMap="CryptoMonitorLogResult">
        select
            max(id) as id
        from crypto_monitor_log
        <where>
                    <if test="blockchain != null  and blockchain != ''"> and blockchain = #{blockchain}</if>
                    <if test="walletAddress != null  and walletAddress != ''"> and wallet_address = #{walletAddress}</if>
                    <if test="lastBlockHeight != null "> and last_block_height = #{lastBlockHeight}</if>
                    <if test="lastMonitorTime != null "> and last_monitor_time = #{lastMonitorTime}</if>
                    <if test="monitorStatus != null "> and monitor_status = #{monitorStatus}</if>
                    <if test="errorCount != null "> and error_count = #{errorCount}</if>
                    <if test="lastError != null  and lastError != ''"> and last_error = #{lastError}</if>
                </where>
    </select>



    <select id="summary" parameterType="tv.shorthub.common.core.domain.SummaryRequest" resultMap="CryptoMonitorLogResult">
        
    </select>
    <select id="allSummary" parameterType="tv.shorthub.common.core.domain.SummaryRequest" resultMap="CryptoMonitorLogResult">
        
    </select>

</mapper>
