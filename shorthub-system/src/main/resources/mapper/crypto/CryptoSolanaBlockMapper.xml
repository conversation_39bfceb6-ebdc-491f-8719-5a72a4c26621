<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tv.shorthub.system.mapper.CryptoSolanaBlockMapper">

    <resultMap type="CryptoSolanaBlock" id="CryptoSolanaBlockResult">
        <result property="id"    column="id"    />
        <result property="blockHash"    column="block_hash"    />
        <result property="blockNumber"    column="block_number"    />
        <result property="parentSlot"    column="parent_slot"    />
        <result property="timestamp"    column="timestamp"    />
        <result property="transactionsCount"    column="transactions_count"    />
        <result property="leader"    column="leader"    />
        <result property="rewards"    column="rewards"    />
        <result property="blockTime"    column="block_time"    />
        <result property="blockHeight"    column="block_height"    />
        <result property="previousBlockhash"    column="previous_blockhash"    />
        <result property="blockhash"    column="blockhash"    />
        <result property="parentBlockhash"    column="parent_blockhash"    />
        <result property="executedTransactionCount"    column="executed_transaction_count"    />
        <result property="transactionCount"    column="transaction_count"    />
        <result property="validators"    column="validators"    />
        <result property="meta"    column="meta"    />
        <result property="rawData"    column="raw_data"    />
        <result property="fullContent"    column="full_content"    />
        <result property="isConfirmed"    column="is_confirmed"    />
        <result property="confirmations"    column="confirmations"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectCryptoSolanaBlockVo">
        select id, block_hash, block_number, parent_slot, timestamp, transactions_count, leader, rewards, block_time, block_height, previous_blockhash, blockhash, parent_blockhash, executed_transaction_count, transaction_count, validators, meta, raw_data, full_content, is_confirmed, confirmations, create_time, update_time from crypto_solana_block
    </sql>

    <insert id="batchInsertOrUpdate">
        <foreach item="item" index="index" collection="list" separator=";">
            insert into crypto_solana_block
            <trim prefix="(" suffix=")" suffixOverrides=",">
                        <if test="item.blockHash != null and item.blockHash != ''">block_hash,</if>
                        <if test="item.blockNumber != null">block_number,</if>
                        <if test="item.parentSlot != null">parent_slot,</if>
                        <if test="item.timestamp != null">timestamp,</if>
                        <if test="item.transactionsCount != null">transactions_count,</if>
                        <if test="item.leader != null">leader,</if>
                        <if test="item.rewards != null">rewards,</if>
                        <if test="item.blockTime != null">block_time,</if>
                        <if test="item.blockHeight != null">block_height,</if>
                        <if test="item.previousBlockhash != null">previous_blockhash,</if>
                        <if test="item.blockhash != null">blockhash,</if>
                        <if test="item.parentBlockhash != null">parent_blockhash,</if>
                        <if test="item.executedTransactionCount != null">executed_transaction_count,</if>
                        <if test="item.transactionCount != null">transaction_count,</if>
                        <if test="item.validators != null">validators,</if>
                        <if test="item.meta != null">meta,</if>
                        <if test="item.rawData != null">raw_data,</if>
                        <if test="item.fullContent != null">full_content,</if>
                        <if test="item.isConfirmed != null">is_confirmed,</if>
                        <if test="item.confirmations != null">confirmations,</if>
                        <if test="item.createTime != null">create_time,</if>
                        <if test="item.updateTime != null">update_time,</if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                        <if test="item.blockHash != null and item.blockHash != ''">#{item.blockHash},</if>
                        <if test="item.blockNumber != null">#{item.blockNumber},</if>
                        <if test="item.parentSlot != null">#{item.parentSlot},</if>
                        <if test="item.timestamp != null">#{item.timestamp},</if>
                        <if test="item.transactionsCount != null">#{item.transactionsCount},</if>
                        <if test="item.leader != null">#{item.leader},</if>
                        <if test="item.rewards != null">#{item.rewards},</if>
                        <if test="item.blockTime != null">#{item.blockTime},</if>
                        <if test="item.blockHeight != null">#{item.blockHeight},</if>
                        <if test="item.previousBlockhash != null">#{item.previousBlockhash},</if>
                        <if test="item.blockhash != null">#{item.blockhash},</if>
                        <if test="item.parentBlockhash != null">#{item.parentBlockhash},</if>
                        <if test="item.executedTransactionCount != null">#{item.executedTransactionCount},</if>
                        <if test="item.transactionCount != null">#{item.transactionCount},</if>
                        <if test="item.validators != null">#{item.validators},</if>
                        <if test="item.meta != null">#{item.meta},</if>
                        <if test="item.rawData != null">#{item.rawData},</if>
                        <if test="item.fullContent != null">#{item.fullContent},</if>
                        <if test="item.isConfirmed != null">#{item.isConfirmed},</if>
                        <if test="item.confirmations != null">#{item.confirmations},</if>
                        <if test="item.createTime != null">#{item.createTime},</if>
                        <if test="item.updateTime != null">#{item.updateTime},</if>
            </trim>
            on duplicate key update
            <trim suffixOverrides=",">
                        <if test="item.blockHash != null and item.blockHash != ''">block_hash = values(block_hash),</if>
                        <if test="item.blockNumber != null">block_number = values(block_number),</if>
                        <if test="item.parentSlot != null">parent_slot = values(parent_slot),</if>
                        <if test="item.timestamp != null">timestamp = values(timestamp),</if>
                        <if test="item.transactionsCount != null">transactions_count = values(transactions_count),</if>
                        <if test="item.leader != null">leader = values(leader),</if>
                        <if test="item.rewards != null">rewards = values(rewards),</if>
                        <if test="item.blockTime != null">block_time = values(block_time),</if>
                        <if test="item.blockHeight != null">block_height = values(block_height),</if>
                        <if test="item.previousBlockhash != null">previous_blockhash = values(previous_blockhash),</if>
                        <if test="item.blockhash != null">blockhash = values(blockhash),</if>
                        <if test="item.parentBlockhash != null">parent_blockhash = values(parent_blockhash),</if>
                        <if test="item.executedTransactionCount != null">executed_transaction_count = values(executed_transaction_count),</if>
                        <if test="item.transactionCount != null">transaction_count = values(transaction_count),</if>
                        <if test="item.validators != null">validators = values(validators),</if>
                        <if test="item.meta != null">meta = values(meta),</if>
                        <if test="item.rawData != null">raw_data = values(raw_data),</if>
                        <if test="item.fullContent != null">full_content = values(full_content),</if>
                        <if test="item.isConfirmed != null">is_confirmed = values(is_confirmed),</if>
                        <if test="item.confirmations != null">confirmations = values(confirmations),</if>
                        <if test="item.createTime != null">create_time = values(create_time),</if>
                        <if test="item.updateTime != null">update_time = values(update_time),</if>
            </trim>
        </foreach>

    </insert>

    <select id="getSummary" parameterType="CryptoSolanaBlock" resultMap="CryptoSolanaBlockResult">
        select
            max(id) as id
        from crypto_solana_block
        <where>
                    <if test="blockHash != null  and blockHash != ''"> and block_hash = #{blockHash}</if>
                    <if test="blockNumber != null "> and block_number = #{blockNumber}</if>
                    <if test="parentSlot != null "> and parent_slot = #{parentSlot}</if>
                    <if test="timestamp != null "> and timestamp = #{timestamp}</if>
                    <if test="transactionsCount != null "> and transactions_count = #{transactionsCount}</if>
                    <if test="leader != null  and leader != ''"> and leader = #{leader}</if>
                    <if test="rewards != null  and rewards != ''"> and rewards = #{rewards}</if>
                    <if test="blockTime != null "> and block_time = #{blockTime}</if>
                    <if test="blockHeight != null "> and block_height = #{blockHeight}</if>
                    <if test="previousBlockhash != null  and previousBlockhash != ''"> and previous_blockhash = #{previousBlockhash}</if>
                    <if test="blockhash != null  and blockhash != ''"> and blockhash = #{blockhash}</if>
                    <if test="parentBlockhash != null  and parentBlockhash != ''"> and parent_blockhash = #{parentBlockhash}</if>
                    <if test="executedTransactionCount != null "> and executed_transaction_count = #{executedTransactionCount}</if>
                    <if test="transactionCount != null "> and transaction_count = #{transactionCount}</if>
                    <if test="validators != null  and validators != ''"> and validators = #{validators}</if>
                    <if test="meta != null  and meta != ''"> and meta = #{meta}</if>
                    <if test="rawData != null  and rawData != ''"> and raw_data = #{rawData}</if>
                    <if test="fullContent != null  and fullContent != ''"> and full_content = #{fullContent}</if>
                    <if test="isConfirmed != null "> and is_confirmed = #{isConfirmed}</if>
                    <if test="confirmations != null "> and confirmations = #{confirmations}</if>
                </where>
    </select>



    <select id="summary" parameterType="tv.shorthub.common.core.domain.SummaryRequest" resultMap="CryptoSolanaBlockResult">
        
    </select>
    <select id="allSummary" parameterType="tv.shorthub.common.core.domain.SummaryRequest" resultMap="CryptoSolanaBlockResult">
        
    </select>

</mapper>
