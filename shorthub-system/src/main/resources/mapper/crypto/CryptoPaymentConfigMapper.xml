<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tv.shorthub.system.mapper.CryptoPaymentConfigMapper">

    <resultMap type="CryptoPaymentConfig" id="CryptoPaymentConfigResult">
        <result property="id"    column="id"    />
        <result property="configName"    column="config_name"    />
        <result property="blockchain"    column="blockchain"    />
        <result property="tokenType"    column="token_type"    />
        <result property="contractAddress"    column="contract_address"    />
        <result property="rpcUrl"    column="rpc_url"    />
        <result property="enabled"    column="enabled"    />
        <result property="testnet"    column="testnet"    />
        <result property="confirmations"    column="confirmations"    />
        <result property="extendConfig"    column="extend_config"    />
        <result property="remark"    column="remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectCryptoPaymentConfigVo">
        select id, config_name, blockchain, token_type, contract_address, rpc_url, enabled, testnet, confirmations, extend_config, remark, create_by, create_time, update_by, update_time from crypto_payment_config
    </sql>

    <insert id="batchInsertOrUpdate">
        <foreach item="item" index="index" collection="list" separator=";">
            insert into crypto_payment_config
            <trim prefix="(" suffix=")" suffixOverrides=",">
                        <if test="item.configName != null and item.configName != ''">config_name,</if>
                        <if test="item.blockchain != null and item.blockchain != ''">blockchain,</if>
                        <if test="item.tokenType != null and item.tokenType != ''">token_type,</if>
                        <if test="item.contractAddress != null">contract_address,</if>
                        <if test="item.rpcUrl != null and item.rpcUrl != ''">rpc_url,</if>
                        <if test="item.enabled != null">enabled,</if>
                        <if test="item.testnet != null">testnet,</if>
                        <if test="item.confirmations != null">confirmations,</if>
                        <if test="item.extendConfig != null">extend_config,</if>
                        <if test="item.remark != null">remark,</if>
                        <if test="item.createBy != null">create_by,</if>
                        <if test="item.createTime != null">create_time,</if>
                        <if test="item.updateBy != null">update_by,</if>
                        <if test="item.updateTime != null">update_time,</if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                        <if test="item.configName != null and item.configName != ''">#{item.configName},</if>
                        <if test="item.blockchain != null and item.blockchain != ''">#{item.blockchain},</if>
                        <if test="item.tokenType != null and item.tokenType != ''">#{item.tokenType},</if>
                        <if test="item.contractAddress != null">#{item.contractAddress},</if>
                        <if test="item.rpcUrl != null and item.rpcUrl != ''">#{item.rpcUrl},</if>
                        <if test="item.enabled != null">#{item.enabled},</if>
                        <if test="item.testnet != null">#{item.testnet},</if>
                        <if test="item.confirmations != null">#{item.confirmations},</if>
                        <if test="item.extendConfig != null">#{item.extendConfig},</if>
                        <if test="item.remark != null">#{item.remark},</if>
                        <if test="item.createBy != null">#{item.createBy},</if>
                        <if test="item.createTime != null">#{item.createTime},</if>
                        <if test="item.updateBy != null">#{item.updateBy},</if>
                        <if test="item.updateTime != null">#{item.updateTime},</if>
            </trim>
            on duplicate key update
            <trim suffixOverrides=",">
                        <if test="item.configName != null and item.configName != ''">config_name = values(config_name),</if>
                        <if test="item.blockchain != null and item.blockchain != ''">blockchain = values(blockchain),</if>
                        <if test="item.tokenType != null and item.tokenType != ''">token_type = values(token_type),</if>
                        <if test="item.contractAddress != null">contract_address = values(contract_address),</if>
                        <if test="item.rpcUrl != null and item.rpcUrl != ''">rpc_url = values(rpc_url),</if>
                        <if test="item.enabled != null">enabled = values(enabled),</if>
                        <if test="item.testnet != null">testnet = values(testnet),</if>
                        <if test="item.confirmations != null">confirmations = values(confirmations),</if>
                        <if test="item.extendConfig != null">extend_config = values(extend_config),</if>
                        <if test="item.remark != null">remark = values(remark),</if>
                        <if test="item.createBy != null">create_by = values(create_by),</if>
                        <if test="item.createTime != null">create_time = values(create_time),</if>
                        <if test="item.updateBy != null">update_by = values(update_by),</if>
                        <if test="item.updateTime != null">update_time = values(update_time),</if>
            </trim>
        </foreach>

    </insert>

    <select id="getSummary" parameterType="CryptoPaymentConfig" resultMap="CryptoPaymentConfigResult">
        select
            max(id) as id
        from crypto_payment_config
        <where>
                    <if test="configName != null  and configName != ''"> and config_name like concat('%', #{configName}, '%')</if>
                    <if test="blockchain != null  and blockchain != ''"> and blockchain = #{blockchain}</if>
                    <if test="tokenType != null  and tokenType != ''"> and token_type = #{tokenType}</if>
                    <if test="contractAddress != null  and contractAddress != ''"> and contract_address = #{contractAddress}</if>
                    <if test="rpcUrl != null  and rpcUrl != ''"> and rpc_url = #{rpcUrl}</if>
                    <if test="enabled != null "> and enabled = #{enabled}</if>
                    <if test="testnet != null "> and testnet = #{testnet}</if>
                    <if test="confirmations != null "> and confirmations = #{confirmations}</if>
                    <if test="extendConfig != null "> and extend_config = #{extendConfig}</if>
                </where>
    </select>



    <select id="summary" parameterType="tv.shorthub.common.core.domain.SummaryRequest" resultMap="CryptoPaymentConfigResult">
        
    </select>
    <select id="allSummary" parameterType="tv.shorthub.common.core.domain.SummaryRequest" resultMap="CryptoPaymentConfigResult">
        
    </select>

</mapper>
