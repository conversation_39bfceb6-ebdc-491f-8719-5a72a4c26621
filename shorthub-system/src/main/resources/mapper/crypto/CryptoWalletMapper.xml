<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tv.shorthub.system.mapper.CryptoWalletMapper">

    <resultMap type="CryptoWallet" id="CryptoWalletResult">
        <result property="id"    column="id"    />
        <result property="address"    column="address"    />
        <result property="blockchain"    column="blockchain"    />
        <result property="privateKey"    column="private_key"    />
        <result property="mnemonic"    column="mnemonic"    />
        <result property="status"    column="status"    />
        <result property="assignedUserId"    column="assigned_user_id"    />
        <result property="assignedTime"    column="assigned_time"    />
        <result property="lastUsedTime"    column="last_used_time"    />
        <result property="walletTag"    column="wallet_tag"    />
        <result property="isHotWallet"    column="is_hot_wallet"    />
        <result property="extendData"    column="extend_data"    />
        <result property="remark"    column="remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectCryptoWalletVo">
        select id, address, blockchain, private_key, mnemonic, status, assigned_user_id, assigned_time, last_used_time, wallet_tag, is_hot_wallet, extend_data, remark, create_by, create_time, update_by, update_time from crypto_wallet
    </sql>

    <insert id="batchInsertOrUpdate">
        <foreach item="item" index="index" collection="list" separator=";">
            insert into crypto_wallet
            <trim prefix="(" suffix=")" suffixOverrides=",">
                        <if test="item.address != null and item.address != ''">address,</if>
                        <if test="item.blockchain != null and item.blockchain != ''">blockchain,</if>
                        <if test="item.privateKey != null">private_key,</if>
                        <if test="item.mnemonic != null">mnemonic,</if>
                        <if test="item.status != null">status,</if>
                        <if test="item.assignedUserId != null">assigned_user_id,</if>
                        <if test="item.assignedTime != null">assigned_time,</if>
                        <if test="item.lastUsedTime != null">last_used_time,</if>
                        <if test="item.walletTag != null">wallet_tag,</if>
                        <if test="item.isHotWallet != null">is_hot_wallet,</if>
                        <if test="item.extendData != null">extend_data,</if>
                        <if test="item.remark != null">remark,</if>
                        <if test="item.createBy != null">create_by,</if>
                        <if test="item.createTime != null">create_time,</if>
                        <if test="item.updateBy != null">update_by,</if>
                        <if test="item.updateTime != null">update_time,</if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                        <if test="item.address != null and item.address != ''">#{item.address},</if>
                        <if test="item.blockchain != null and item.blockchain != ''">#{item.blockchain},</if>
                        <if test="item.privateKey != null">#{item.privateKey},</if>
                        <if test="item.mnemonic != null">#{item.mnemonic},</if>
                        <if test="item.status != null">#{item.status},</if>
                        <if test="item.assignedUserId != null">#{item.assignedUserId},</if>
                        <if test="item.assignedTime != null">#{item.assignedTime},</if>
                        <if test="item.lastUsedTime != null">#{item.lastUsedTime},</if>
                        <if test="item.walletTag != null">#{item.walletTag},</if>
                        <if test="item.isHotWallet != null">#{item.isHotWallet},</if>
                        <if test="item.extendData != null">#{item.extendData},</if>
                        <if test="item.remark != null">#{item.remark},</if>
                        <if test="item.createBy != null">#{item.createBy},</if>
                        <if test="item.createTime != null">#{item.createTime},</if>
                        <if test="item.updateBy != null">#{item.updateBy},</if>
                        <if test="item.updateTime != null">#{item.updateTime},</if>
            </trim>
            on duplicate key update
            <trim suffixOverrides=",">
                        <if test="item.address != null and item.address != ''">address = values(address),</if>
                        <if test="item.blockchain != null and item.blockchain != ''">blockchain = values(blockchain),</if>
                        <if test="item.privateKey != null">private_key = values(private_key),</if>
                        <if test="item.mnemonic != null">mnemonic = values(mnemonic),</if>
                        <if test="item.status != null">status = values(status),</if>
                        <if test="item.assignedUserId != null">assigned_user_id = values(assigned_user_id),</if>
                        <if test="item.assignedTime != null">assigned_time = values(assigned_time),</if>
                        <if test="item.lastUsedTime != null">last_used_time = values(last_used_time),</if>
                        <if test="item.walletTag != null">wallet_tag = values(wallet_tag),</if>
                        <if test="item.isHotWallet != null">is_hot_wallet = values(is_hot_wallet),</if>
                        <if test="item.extendData != null">extend_data = values(extend_data),</if>
                        <if test="item.remark != null">remark = values(remark),</if>
                        <if test="item.createBy != null">create_by = values(create_by),</if>
                        <if test="item.createTime != null">create_time = values(create_time),</if>
                        <if test="item.updateBy != null">update_by = values(update_by),</if>
                        <if test="item.updateTime != null">update_time = values(update_time),</if>
            </trim>
        </foreach>

    </insert>

    <select id="getSummary" parameterType="CryptoWallet" resultMap="CryptoWalletResult">
        select
            max(id) as id
        from crypto_wallet
        <where>
                    <if test="address != null  and address != ''"> and address = #{address}</if>
                    <if test="blockchain != null  and blockchain != ''"> and blockchain = #{blockchain}</if>
                    <if test="privateKey != null  and privateKey != ''"> and private_key = #{privateKey}</if>
                    <if test="mnemonic != null  and mnemonic != ''"> and mnemonic = #{mnemonic}</if>
                    <if test="status != null "> and status = #{status}</if>
                    <if test="assignedUserId != null  and assignedUserId != ''"> and assigned_user_id = #{assignedUserId}</if>
                    <if test="assignedTime != null "> and assigned_time = #{assignedTime}</if>
                    <if test="lastUsedTime != null "> and last_used_time = #{lastUsedTime}</if>
                    <if test="walletTag != null  and walletTag != ''"> and wallet_tag = #{walletTag}</if>
                    <if test="isHotWallet != null "> and is_hot_wallet = #{isHotWallet}</if>
                    <if test="extendData != null "> and extend_data = #{extendData}</if>
                </where>
    </select>



    <select id="summary" parameterType="tv.shorthub.common.core.domain.SummaryRequest" resultMap="CryptoWalletResult">
        
    </select>
    <select id="allSummary" parameterType="tv.shorthub.common.core.domain.SummaryRequest" resultMap="CryptoWalletResult">
        
    </select>

</mapper>
