<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tv.shorthub.system.mapper.CryptoDepositRecordMapper">

    <resultMap type="CryptoDepositRecord" id="CryptoDepositRecordResult">
        <result property="id"    column="id"    />
        <result property="orderNo"    column="order_no"    />
        <result property="userId"    column="user_id"    />
        <result property="txHash"    column="tx_hash"    />
        <result property="blockchain"    column="blockchain"    />
        <result property="tokenType"    column="token_type"    />
        <result property="contractAddress"    column="contract_address"    />
        <result property="fromAddress"    column="from_address"    />
        <result property="toAddress"    column="to_address"    />
        <result property="amount"    column="amount"    />
        <result property="rawAmount"    column="raw_amount"    />
        <result property="status"    column="status"    />
        <result property="blockHeight"    column="block_height"    />
        <result property="confirmations"    column="confirmations"    />
        <result property="requiredConfirmations"    column="required_confirmations"    />
        <result property="transactionTime"    column="transaction_time"    />
        <result property="confirmedTime"    column="confirmed_time"    />
        <result property="processStatus"    column="process_status"    />
        <result property="processTime"    column="process_time"    />
        <result property="failureReason"    column="failure_reason"    />
        <result property="retryCount"    column="retry_count"    />
        <result property="gasFee"    column="gas_fee"    />
        <result property="rawData"    column="raw_data"    />
        <result property="remark"    column="remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectCryptoDepositRecordVo">
        select id, order_no, user_id, tx_hash, blockchain, token_type, contract_address, from_address, to_address, amount, raw_amount, status, block_height, confirmations, required_confirmations, transaction_time, confirmed_time, process_status, process_time, failure_reason, retry_count, gas_fee, raw_data, remark, create_by, create_time, update_by, update_time from crypto_deposit_record
    </sql>

    <insert id="batchInsertOrUpdate">
        <foreach item="item" index="index" collection="list" separator=";">
            insert into crypto_deposit_record
            <trim prefix="(" suffix=")" suffixOverrides=",">
                        <if test="item.orderNo != null and item.orderNo != ''">order_no,</if>
                        <if test="item.userId != null and item.userId != ''">user_id,</if>
                        <if test="item.txHash != null">tx_hash,</if>
                        <if test="item.blockchain != null and item.blockchain != ''">blockchain,</if>
                        <if test="item.tokenType != null and item.tokenType != ''">token_type,</if>
                        <if test="item.contractAddress != null">contract_address,</if>
                        <if test="item.fromAddress != null">from_address,</if>
                        <if test="item.toAddress != null and item.toAddress != ''">to_address,</if>
                        <if test="item.amount != null">amount,</if>
                        <if test="item.rawAmount != null">raw_amount,</if>
                        <if test="item.status != null and item.status != ''">status,</if>
                        <if test="item.blockHeight != null">block_height,</if>
                        <if test="item.confirmations != null">confirmations,</if>
                        <if test="item.requiredConfirmations != null">required_confirmations,</if>
                        <if test="item.transactionTime != null">transaction_time,</if>
                        <if test="item.confirmedTime != null">confirmed_time,</if>
                        <if test="item.processStatus != null">process_status,</if>
                        <if test="item.processTime != null">process_time,</if>
                        <if test="item.failureReason != null">failure_reason,</if>
                        <if test="item.retryCount != null">retry_count,</if>
                        <if test="item.gasFee != null">gas_fee,</if>
                        <if test="item.rawData != null">raw_data,</if>
                        <if test="item.remark != null">remark,</if>
                        <if test="item.createBy != null">create_by,</if>
                        <if test="item.createTime != null">create_time,</if>
                        <if test="item.updateBy != null">update_by,</if>
                        <if test="item.updateTime != null">update_time,</if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                        <if test="item.orderNo != null and item.orderNo != ''">#{item.orderNo},</if>
                        <if test="item.userId != null and item.userId != ''">#{item.userId},</if>
                        <if test="item.txHash != null">#{item.txHash},</if>
                        <if test="item.blockchain != null and item.blockchain != ''">#{item.blockchain},</if>
                        <if test="item.tokenType != null and item.tokenType != ''">#{item.tokenType},</if>
                        <if test="item.contractAddress != null">#{item.contractAddress},</if>
                        <if test="item.fromAddress != null">#{item.fromAddress},</if>
                        <if test="item.toAddress != null and item.toAddress != ''">#{item.toAddress},</if>
                        <if test="item.amount != null">#{item.amount},</if>
                        <if test="item.rawAmount != null">#{item.rawAmount},</if>
                        <if test="item.status != null and item.status != ''">#{item.status},</if>
                        <if test="item.blockHeight != null">#{item.blockHeight},</if>
                        <if test="item.confirmations != null">#{item.confirmations},</if>
                        <if test="item.requiredConfirmations != null">#{item.requiredConfirmations},</if>
                        <if test="item.transactionTime != null">#{item.transactionTime},</if>
                        <if test="item.confirmedTime != null">#{item.confirmedTime},</if>
                        <if test="item.processStatus != null">#{item.processStatus},</if>
                        <if test="item.processTime != null">#{item.processTime},</if>
                        <if test="item.failureReason != null">#{item.failureReason},</if>
                        <if test="item.retryCount != null">#{item.retryCount},</if>
                        <if test="item.gasFee != null">#{item.gasFee},</if>
                        <if test="item.rawData != null">#{item.rawData},</if>
                        <if test="item.remark != null">#{item.remark},</if>
                        <if test="item.createBy != null">#{item.createBy},</if>
                        <if test="item.createTime != null">#{item.createTime},</if>
                        <if test="item.updateBy != null">#{item.updateBy},</if>
                        <if test="item.updateTime != null">#{item.updateTime},</if>
            </trim>
            on duplicate key update
            <trim suffixOverrides=",">
                        <if test="item.orderNo != null and item.orderNo != ''">order_no = values(order_no),</if>
                        <if test="item.userId != null and item.userId != ''">user_id = values(user_id),</if>
                        <if test="item.txHash != null">tx_hash = values(tx_hash),</if>
                        <if test="item.blockchain != null and item.blockchain != ''">blockchain = values(blockchain),</if>
                        <if test="item.tokenType != null and item.tokenType != ''">token_type = values(token_type),</if>
                        <if test="item.contractAddress != null">contract_address = values(contract_address),</if>
                        <if test="item.fromAddress != null">from_address = values(from_address),</if>
                        <if test="item.toAddress != null and item.toAddress != ''">to_address = values(to_address),</if>
                        <if test="item.amount != null">amount = values(amount),</if>
                        <if test="item.rawAmount != null">raw_amount = values(raw_amount),</if>
                        <if test="item.status != null and item.status != ''">status = values(status),</if>
                        <if test="item.blockHeight != null">block_height = values(block_height),</if>
                        <if test="item.confirmations != null">confirmations = values(confirmations),</if>
                        <if test="item.requiredConfirmations != null">required_confirmations = values(required_confirmations),</if>
                        <if test="item.transactionTime != null">transaction_time = values(transaction_time),</if>
                        <if test="item.confirmedTime != null">confirmed_time = values(confirmed_time),</if>
                        <if test="item.processStatus != null">process_status = values(process_status),</if>
                        <if test="item.processTime != null">process_time = values(process_time),</if>
                        <if test="item.failureReason != null">failure_reason = values(failure_reason),</if>
                        <if test="item.retryCount != null">retry_count = values(retry_count),</if>
                        <if test="item.gasFee != null">gas_fee = values(gas_fee),</if>
                        <if test="item.rawData != null">raw_data = values(raw_data),</if>
                        <if test="item.remark != null">remark = values(remark),</if>
                        <if test="item.createBy != null">create_by = values(create_by),</if>
                        <if test="item.createTime != null">create_time = values(create_time),</if>
                        <if test="item.updateBy != null">update_by = values(update_by),</if>
                        <if test="item.updateTime != null">update_time = values(update_time),</if>
            </trim>
        </foreach>

    </insert>

    <select id="getSummary" parameterType="CryptoDepositRecord" resultMap="CryptoDepositRecordResult">
        select
            max(id) as id
        from crypto_deposit_record
        <where>
                    <if test="orderNo != null  and orderNo != ''"> and order_no = #{orderNo}</if>
                    <if test="userId != null  and userId != ''"> and user_id = #{userId}</if>
                    <if test="txHash != null  and txHash != ''"> and tx_hash = #{txHash}</if>
                    <if test="blockchain != null  and blockchain != ''"> and blockchain = #{blockchain}</if>
                    <if test="tokenType != null  and tokenType != ''"> and token_type = #{tokenType}</if>
                    <if test="contractAddress != null  and contractAddress != ''"> and contract_address = #{contractAddress}</if>
                    <if test="fromAddress != null  and fromAddress != ''"> and from_address = #{fromAddress}</if>
                    <if test="toAddress != null  and toAddress != ''"> and to_address = #{toAddress}</if>
                    <if test="amount != null "> and amount = #{amount}</if>
                    <if test="rawAmount != null  and rawAmount != ''"> and raw_amount = #{rawAmount}</if>
                    <if test="status != null  and status != ''"> and status = #{status}</if>
                    <if test="blockHeight != null "> and block_height = #{blockHeight}</if>
                    <if test="confirmations != null "> and confirmations = #{confirmations}</if>
                    <if test="requiredConfirmations != null "> and required_confirmations = #{requiredConfirmations}</if>
                    <if test="transactionTime != null "> and transaction_time = #{transactionTime}</if>
                    <if test="confirmedTime != null "> and confirmed_time = #{confirmedTime}</if>
                    <if test="processStatus != null "> and process_status = #{processStatus}</if>
                    <if test="processTime != null "> and process_time = #{processTime}</if>
                    <if test="failureReason != null  and failureReason != ''"> and failure_reason = #{failureReason}</if>
                    <if test="retryCount != null "> and retry_count = #{retryCount}</if>
                    <if test="gasFee != null "> and gas_fee = #{gasFee}</if>
                    <if test="rawData != null "> and raw_data = #{rawData}</if>
                </where>
    </select>



    <select id="summary" parameterType="tv.shorthub.common.core.domain.SummaryRequest" resultMap="CryptoDepositRecordResult">
        
    </select>
    <select id="allSummary" parameterType="tv.shorthub.common.core.domain.SummaryRequest" resultMap="CryptoDepositRecordResult">
        
    </select>

</mapper>
