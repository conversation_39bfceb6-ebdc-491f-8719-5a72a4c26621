<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tv.shorthub.system.mapper.AppDramaContentsSerialMapper">

    <resultMap type="AppDramaContentsSerial" id="AppDramaContentsSerialResult">
        <result property="id"    column="id"    />
        <result property="contentId"    column="content_id"    />
        <result property="serialNumber"    column="serial_number"    />
        <result property="serialUrl"    column="serial_url"    />
        <result property="title"    column="title"    />
        <result property="description"    column="description"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectAppDramaContentsSerialVo">
        select id, content_id, serial_number, serial_url, title, description, create_by, create_time, update_by, update_time from app_drama_contents_serial
    </sql>

    <update id="updateOrder">
        UPDATE app_drama_contents_serial
        SET serial_number = CASE id
        <foreach collection="list" item="item">
            WHEN #{item.id} THEN #{item.serialNumber}
        </foreach>
        END
        WHERE id IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id}
        </foreach>
        AND content_id = #{list[0].contentId}
    </update>



    <insert id="batchInsertOrUpdate">
        <foreach item="item" index="index" collection="list" separator=";">
            insert into app_drama_contents_serial
            <trim prefix="(" suffix=")" suffixOverrides=",">
                        <if test="item.contentId != null">content_id,</if>
                        <if test="item.serialNumber != null">serial_number,</if>
                        <if test="item.serialUrl != null and item.serialUrl != ''">serial_url,</if>
                        <if test="item.title != null">title,</if>
                        <if test="item.description != null">description,</if>
                        <if test="item.createBy != null">create_by,</if>
                        <if test="item.createTime != null">create_time,</if>
                        <if test="item.updateBy != null">update_by,</if>
                        <if test="item.updateTime != null">update_time,</if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                        <if test="item.contentId != null">#{item.contentId},</if>
                        <if test="item.serialNumber != null">#{item.serialNumber},</if>
                        <if test="item.serialUrl != null and item.serialUrl != ''">#{item.serialUrl},</if>
                        <if test="item.title != null">#{item.title},</if>
                        <if test="item.description != null">#{item.description},</if>
                        <if test="item.createBy != null">#{item.createBy},</if>
                        <if test="item.createTime != null">#{item.createTime},</if>
                        <if test="item.updateBy != null">#{item.updateBy},</if>
                        <if test="item.updateTime != null">#{item.updateTime},</if>
            </trim>
            on duplicate key update
            <trim suffixOverrides=",">
                        <if test="item.contentId != null">content_id = values(content_id),</if>
                        <if test="item.serialNumber != null">serial_number = values(serial_number),</if>
                        <if test="item.serialUrl != null and item.serialUrl != ''">serial_url = values(serial_url),</if>
                        <if test="item.title != null">title = values(title),</if>
                        <if test="item.description != null">description = values(description),</if>
                        <if test="item.createBy != null">create_by = values(create_by),</if>
                        <if test="item.createTime != null">create_time = values(create_time),</if>
                        <if test="item.updateBy != null">update_by = values(update_by),</if>
                        <if test="item.updateTime != null">update_time = values(update_time),</if>
            </trim>
        </foreach>

    </insert>

    <select id="getSummary" parameterType="AppDramaContentsSerial" resultMap="AppDramaContentsSerialResult">
        select
            max(id) as id
        from app_drama_contents_serial
        <where>
                    <if test="contentId != null "> and content_id = #{contentId}</if>
                    <if test="serialNumber != null "> and serial_number = #{serialNumber}</if>
                    <if test="serialUrl != null  and serialUrl != ''"> and serial_url = #{serialUrl}</if>
                    <if test="title != null  and title != ''"> and title = #{title}</if>
                    <if test="description != null  and description != ''"> and description = #{description}</if>
                </where>
    </select>



    <select id="summary" parameterType="tv.shorthub.common.core.domain.SummaryRequest" resultMap="AppDramaContentsSerialResult">
        
    </select>
    <select id="allSummary" parameterType="tv.shorthub.common.core.domain.SummaryRequest" resultMap="AppDramaContentsSerialResult">
        
    </select>

</mapper>
