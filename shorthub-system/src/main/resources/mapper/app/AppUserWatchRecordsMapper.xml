<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tv.shorthub.system.mapper.AppUserWatchRecordsMapper">

    <resultMap type="AppUserWatchRecords" id="AppUserWatchRecordsResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="contentId"    column="content_id"    />
        <result property="serialSecond"    column="serial_second"    />
        <result property="watchedAt"    column="watched_at"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectAppUserWatchRecordsVo">
        select id, user_id, content_id, content_id, watched_at, create_by, create_time, update_by, update_time from app_user_watch_records
    </sql>

    <insert id="batchInsertOrUpdateCustom">
        <foreach item="item" index="index" collection="list" separator=";">
            insert into app_user_watch_records
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="item.userId != null and item.userId != ''">user_id,</if>
                <if test="item.appid != null and item.appid != ''">appid,</if>
                <if test="item.contentId != null and item.contentId != ''">content_id,</if>
                <if test="item.tfid != null and item.tfid != ''">tfid,</if>
                <if test="item.serialNumber != null">serial_number,</if>
                <if test="item.serialSecond != null">serial_second,</if>
                <if test="item.totalWatchSecond != null">total_watch_second,</if>
                <if test="item.maxWatchSecond != null">max_watch_second,</if>
                <if test="item.watchedAt != null">watched_at,</if>
                <if test="item.createBy != null">create_by,</if>
                <if test="item.createTime != null">create_time,</if>
                <if test="item.updateBy != null">update_by,</if>
                <if test="item.updateTime != null">update_time,</if>
                <if test="item.remark != null">remark,</if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="item.userId != null and item.userId != ''">#{item.userId},</if>
                <if test="item.appid != null and item.appid != ''">#{item.appid},</if>
                <if test="item.contentId != null and item.contentId != ''">#{item.contentId},</if>
                <if test="item.tfid != null and item.tfid != ''">#{item.tfid},</if>
                <if test="item.serialNumber != null">#{item.serialNumber},</if>
                <if test="item.serialSecond != null">#{item.serialSecond},</if>
                <if test="item.totalWatchSecond != null">#{item.totalWatchSecond},</if>
                <if test="item.maxWatchSecond != null">#{item.maxWatchSecond},</if>
                <if test="item.watchedAt != null">#{item.watchedAt},</if>
                <if test="item.createBy != null">#{item.createBy},</if>
                <if test="item.createTime != null">#{item.createTime},</if>
                <if test="item.updateBy != null">#{item.updateBy},</if>
                <if test="item.updateTime != null">#{item.updateTime},</if>
                <if test="item.remark != null">#{item.remark},</if>
            </trim>
            on duplicate key update
            <trim suffixOverrides=",">
                <if test="item.userId != null and item.userId != ''">user_id = values(user_id),</if>
                <if test="item.contentId != null and item.contentId != ''">content_id = values(content_id),</if>
                <if test="item.appid != null and item.appid != ''">appid = values(appid),</if>
                <if test="item.tfid != null and item.tfid != ''">tfid = values(tfid),</if>
                <if test="item.serialNumber != null">serial_number = values(serial_number),</if>
                <if test="item.serialSecond != null">serial_second = IF(#{item.serialSecond} > serial_second, #{item.serialSecond}, serial_second),</if>
                <if test="item.totalWatchSecond != null">total_watch_second = max_watch_second + values(max_watch_second),</if>
                <if test="item.maxWatchSecond != null">max_watch_second = IF(#{item.maxWatchSecond} > max_watch_second, #{item.maxWatchSecond}, max_watch_second),</if>
                <if test="item.watchedAt != null">watched_at = values(watched_at),</if>
                <if test="item.updateBy != null">update_by = values(update_by),</if>
                <if test="item.updateTime != null">update_time = values(update_time),</if>
                <if test="item.remark != null">remark = values(remark),</if>
            </trim>
        </foreach>

    </insert>


    <select id="getSummary" parameterType="AppUserWatchRecords" resultMap="AppUserWatchRecordsResult">
        select
            max(id) as id
        from app_user_watch_records
        <where>
                    <if test="userId != null "> and user_id = #{userId}</if>
                    <if test="contentId != null "> and content_id = #{contentId}</if>
                    <if test="serialSecond != null "> and serial_second = #{serialSecond}</if>
                    <if test="watchedAt != null "> and watched_at = #{watchedAt}</if>
                </where>
    </select>

    <select id="selectListByPromotionUser" parameterType="String" resultMap="AppUserWatchRecordsResult">
        SELECT r.*
        FROM app_user_watch_records r
        INNER JOIN (
            SELECT a.user_id 
            FROM app_users a
            INNER JOIN app_promotion p ON a.tfid = p.tfid 
            WHERE p.create_by = #{userId}
        ) t ON r.user_id = t.user_id
        order by r.update_time desc
    </select>



    <select id="summary" parameterType="tv.shorthub.common.core.domain.SummaryRequest" resultMap="AppUserWatchRecordsResult">
            <include refid="tv.shorthub.common.mapper.CommonMapper.summarySql">
                <property name="tableName" value="app_user_watch_records"/>
            </include>
    </select>
    <select id="allSummary" parameterType="tv.shorthub.common.core.domain.SummaryRequest" resultMap="AppUserWatchRecordsResult">
            <include refid="tv.shorthub.common.mapper.CommonMapper.allSummarySql">
                <property name="tableName" value="app_user_watch_records"/>
            </include>
    </select>

</mapper>
