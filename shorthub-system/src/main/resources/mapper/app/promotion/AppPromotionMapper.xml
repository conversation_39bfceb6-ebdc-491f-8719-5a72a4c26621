<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tv.shorthub.system.mapper.AppPromotionMapper">

    <resultMap type="AppPromotion" id="AppPromotionResult">
        <result property="id"    column="id"    />
        <result property="appid"    column="appid"    />
        <result property="tfid"    column="tfid"    />
        <result property="title"    column="title"    />
        <result property="contentId"    column="content_id"    />
        <result property="serialId"    column="serial_id"    />
        <result property="seriesNumber"    column="series_number"    />
        <result property="floorType"    column="floor_type"    />
        <result property="adChannel"    column="ad_channel"    />
        <result property="webUrl"    column="web_url"    />
        <result property="listenUrl"    column="listen_url"    />
        <result property="feeTemplateId"    column="fee_template_id"    />
        <result property="videoFeeBegin"    column="video_fee_begin"    />
        <result property="videoEveryMoney"    column="video_every_money"    />
        <result property="unlockJump"    column="unlock_jump"    />
        <result property="enable"    column="enable"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectAppPromotionVo">
        select id, appid, tfid, title, content_id, serial_id, series_number, floor_type, ad_channel, web_url, listen_url, fee_template_id, video_fee_begin, video_every_money, unlock_jump, enable, create_by, create_time, update_by, update_time from app_promotion
    </sql>

    <insert id="batchInsertOrUpdate">
        <foreach item="item" index="index" collection="list" separator=";">
            insert into app_promotion
            <trim prefix="(" suffix=")" suffixOverrides=",">
                        <if test="item.appid != null and item.appid != ''">appid,</if>
                        <if test="item.tfid != null and item.tfid != ''">tfid,</if>
                        <if test="item.title != null and item.title != ''">title,</if>
                        <if test="item.contentId != null and item.contentId != ''">content_id,</if>
                        <if test="item.serialId != null">serial_id,</if>
                        <if test="item.seriesNumber != null">series_number,</if>
                        <if test="item.floorType != null and item.floorType != ''">floor_type,</if>
                        <if test="item.adChannel != null">ad_channel,</if>
                        <if test="item.webUrl != null and item.webUrl != ''">web_url,</if>
                        <if test="item.listenUrl != null and item.listenUrl != ''">listen_url,</if>
                        <if test="item.feeTemplateId != null">fee_template_id,</if>
                        <if test="item.videoFeeBegin != null">video_fee_begin,</if>
                        <if test="item.videoEveryMoney != null">video_every_money,</if>
                        <if test="item.unlockJump != null">unlock_jump,</if>
                        <if test="item.enable != null">enable,</if>
                        <if test="item.createBy != null">create_by,</if>
                        <if test="item.createTime != null">create_time,</if>
                        <if test="item.updateBy != null">update_by,</if>
                        <if test="item.updateTime != null">update_time,</if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                        <if test="item.appid != null and item.appid != ''">#{item.appid},</if>
                        <if test="item.tfid != null and item.tfid != ''">#{item.tfid},</if>
                        <if test="item.title != null and item.title != ''">#{item.title},</if>
                        <if test="item.contentId != null and item.contentId != ''">#{item.contentId},</if>
                        <if test="item.serialId != null">#{item.serialId},</if>
                        <if test="item.seriesNumber != null">#{item.seriesNumber},</if>
                        <if test="item.floorType != null and item.floorType != ''">#{item.floorType},</if>
                        <if test="item.adChannel != null">#{item.adChannel},</if>
                        <if test="item.webUrl != null and item.webUrl != ''">#{item.webUrl},</if>
                        <if test="item.listenUrl != null and item.listenUrl != ''">#{item.listenUrl},</if>
                        <if test="item.feeTemplateId != null">#{item.feeTemplateId},</if>
                        <if test="item.videoFeeBegin != null">#{item.videoFeeBegin},</if>
                        <if test="item.videoEveryMoney != null">#{item.videoEveryMoney},</if>
                        <if test="item.unlockJump != null">#{item.unlockJump},</if>
                        <if test="item.enable != null">#{item.enable},</if>
                        <if test="item.createBy != null">#{item.createBy},</if>
                        <if test="item.createTime != null">#{item.createTime},</if>
                        <if test="item.updateBy != null">#{item.updateBy},</if>
                        <if test="item.updateTime != null">#{item.updateTime},</if>
            </trim>
            on duplicate key update
            <trim suffixOverrides=",">
                        <if test="item.appid != null and item.appid != ''">appid = values(appid),</if>
                        <if test="item.tfid != null and item.tfid != ''">tfid = values(tfid),</if>
                        <if test="item.title != null and item.title != ''">title = values(title),</if>
                        <if test="item.contentId != null and item.contentId != ''">content_id = values(content_id),</if>
                        <if test="item.serialId != null">serial_id = values(serial_id),</if>
                        <if test="item.seriesNumber != null">series_number = values(series_number),</if>
                        <if test="item.floorType != null and item.floorType != ''">floor_type = values(floor_type),</if>
                        <if test="item.adChannel != null">ad_channel = values(ad_channel),</if>
                        <if test="item.webUrl != null and item.webUrl != ''">web_url = values(web_url),</if>
                        <if test="item.listenUrl != null and item.listenUrl != ''">listen_url = values(listen_url),</if>
                        <if test="item.feeTemplateId != null">fee_template_id = values(fee_template_id),</if>
                        <if test="item.videoFeeBegin != null">video_fee_begin = values(video_fee_begin),</if>
                        <if test="item.videoEveryMoney != null">video_every_money = values(video_every_money),</if>
                        <if test="item.unlockJump != null">unlock_jump = values(unlock_jump),</if>
                        <if test="item.enable != null">enable = values(enable),</if>
                        <if test="item.createBy != null">create_by = values(create_by),</if>
                        <if test="item.createTime != null">create_time = values(create_time),</if>
                        <if test="item.updateBy != null">update_by = values(update_by),</if>
                        <if test="item.updateTime != null">update_time = values(update_time),</if>
            </trim>
        </foreach>

    </insert>

    <select id="getSummary" parameterType="AppPromotion" resultMap="AppPromotionResult">
        select
            max(id) as id
        from app_promotion
        <where>
                    <if test="appid != null  and appid != ''"> and appid = #{appid}</if>
                    <if test="tfid != null  and tfid != ''"> and tfid = #{tfid}</if>
                    <if test="title != null  and title != ''"> and title = #{title}</if>
                    <if test="contentId != null  and contentId != ''"> and content_id = #{contentId}</if>
                    <if test="serialId != null  and serialId != ''"> and serial_id = #{serialId}</if>
                    <if test="seriesNumber != null "> and series_number = #{seriesNumber}</if>
                    <if test="floorType != null  and floorType != ''"> and floor_type = #{floorType}</if>
                    <if test="adChannel != null  and adChannel != ''"> and ad_channel = #{adChannel}</if>
                    <if test="webUrl != null  and webUrl != ''"> and web_url = #{webUrl}</if>
                    <if test="listenUrl != null  and listenUrl != ''"> and listen_url = #{listenUrl}</if>
                    <if test="feeTemplateId != null  and feeTemplateId != ''"> and fee_template_id = #{feeTemplateId}</if>
                    <if test="videoFeeBegin != null "> and video_fee_begin = #{videoFeeBegin}</if>
                    <if test="videoEveryMoney != null "> and video_every_money = #{videoEveryMoney}</if>
                    <if test="unlockJump != null "> and unlock_jump = #{unlockJump}</if>
                    <if test="enable != null "> and enable = #{enable}</if>
                </where>
    </select>



    <select id="summary" parameterType="tv.shorthub.common.core.domain.SummaryRequest" resultMap="AppPromotionResult">

    </select>
    <select id="allSummary" parameterType="tv.shorthub.common.core.domain.SummaryRequest" resultMap="AppPromotionResult">

    </select>

    <select id="selectTfidsByCreator" resultType="java.lang.String">
        SELECT tfid FROM app_promotion WHERE create_by = #{creatorName}
    </select>

</mapper>
