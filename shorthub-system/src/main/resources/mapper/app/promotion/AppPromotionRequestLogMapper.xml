<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tv.shorthub.system.mapper.AppPromotionRequestLogMapper">

    <resultMap type="AppPromotionRequestLog" id="AppPromotionRequestLogResult">
        <result property="id"    column="id"    />
        <result property="tid"    column="tid"    />
        <result property="ip"    column="ip"    />
        <result property="sid"    column="sid"    />
        <result property="headers"    column="headers"    />
        <result property="reqParams"    column="req_params"    />
        <result property="body"    column="body"    />
        <result property="state"    column="state"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectAppPromotionRequestLogVo">
        select id, tid, ip, sid, headers, req_params, body, state, create_by, create_time, update_by, update_time from app_promotion_request_log
    </sql>



    <select id="selectListByPromotionUser" parameterType="String" resultMap="AppPromotionRequestLogResult">
        SELECT r.*
        FROM app_promotion_request_log r
        INNER JOIN (
            SELECT
                p.tfid
            from app_promotion p
            WHERE p.create_by = #{userId}
        ) t ON r.tid = t.tfid
        order by r.id desc
    </select>

    <insert id="batchInsertOrUpdate">
        <foreach item="item" index="index" collection="list" separator=";">
            insert into app_promotion_request_log
            <trim prefix="(" suffix=")" suffixOverrides=",">
                        <if test="item.tid != null">tid,</if>
                        <if test="item.ip != null">ip,</if>
                        <if test="item.sid != null">sid,</if>
                        <if test="item.headers != null">headers,</if>
                        <if test="item.reqParams != null">req_params,</if>
                        <if test="item.body != null">body,</if>
                        <if test="item.state != null">state,</if>
                        <if test="item.createBy != null">create_by,</if>
                        <if test="item.createTime != null">create_time,</if>
                        <if test="item.updateBy != null">update_by,</if>
                        <if test="item.updateTime != null">update_time,</if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                        <if test="item.tid != null">#{item.tid},</if>
                        <if test="item.ip != null">#{item.ip},</if>
                        <if test="item.sid != null">#{item.sid},</if>
                        <if test="item.headers != null">#{item.headers},</if>
                        <if test="item.reqParams != null">#{item.reqParams},</if>
                        <if test="item.body != null">#{item.body},</if>
                        <if test="item.state != null">#{item.state},</if>
                        <if test="item.createBy != null">#{item.createBy},</if>
                        <if test="item.createTime != null">#{item.createTime},</if>
                        <if test="item.updateBy != null">#{item.updateBy},</if>
                        <if test="item.updateTime != null">#{item.updateTime},</if>
            </trim>
            on duplicate key update
            <trim suffixOverrides=",">
                        <if test="item.tid != null">tid = values(tid),</if>
                        <if test="item.ip != null">ip = values(ip),</if>
                        <if test="item.sid != null">sid = values(sid),</if>
                        <if test="item.headers != null">headers = values(headers),</if>
                        <if test="item.reqParams != null">req_params = values(req_params),</if>
                        <if test="item.body != null">body = values(body),</if>
                        <if test="item.state != null">state = values(state),</if>
                        <if test="item.createBy != null">create_by = values(create_by),</if>
                        <if test="item.createTime != null">create_time = values(create_time),</if>
                        <if test="item.updateBy != null">update_by = values(update_by),</if>
                        <if test="item.updateTime != null">update_time = values(update_time),</if>
            </trim>
        </foreach>

    </insert>

    <select id="getSummary" parameterType="AppPromotionRequestLog" resultMap="AppPromotionRequestLogResult">
        select
            max(id) as id
        from app_promotion_request_log
        <where>
                    <if test="tid != null  and tid != ''"> and tid = #{tid}</if>
                    <if test="ip != null  and ip != ''"> and ip = #{ip}</if>
                    <if test="sid != null  and sid != ''"> and sid = #{sid}</if>
                    <if test="headers != null  and headers != ''"> and headers = #{headers}</if>
                    <if test="reqParams != null  and reqParams != ''"> and req_params = #{reqParams}</if>
                    <if test="body != null  and body != ''"> and body = #{body}</if>
                    <if test="state != null  and state != ''"> and state = #{state}</if>
                </where>
    </select>



    <select id="summary" parameterType="tv.shorthub.common.core.domain.SummaryRequest" resultMap="AppPromotionRequestLogResult">
        
    </select>
    <select id="allSummary" parameterType="tv.shorthub.common.core.domain.SummaryRequest" resultMap="AppPromotionRequestLogResult">
        
    </select>

</mapper>
