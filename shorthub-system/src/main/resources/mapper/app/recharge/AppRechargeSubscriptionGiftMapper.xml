<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tv.shorthub.system.mapper.AppRechargeSubscriptionGiftMapper">

    <resultMap type="AppRechargeSubscriptionGift" id="AppRechargeSubscriptionGiftResult">
        <result property="id"    column="id"    />
        <result property="appid"    column="appid"    />
        <result property="itemId"    column="item_id"    />
        <result property="giftPeriodBegin"    column="gift_period_begin"    />
        <result property="giftPeriodNumber"    column="gift_period_number"    />
        <result property="giftPrice"    column="gift_price"    />
        <result property="enable"    column="enable"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectAppRechargeSubscriptionGiftVo">
        select id, appid, item_id, gift_period_begin, gift_period_number, gift_price, enable, create_by, create_time, update_by, update_time from app_recharge_subscription_gift
    </sql>

    <insert id="batchInsertOrUpdate">
        <foreach item="item" index="index" collection="list" separator=";">
            insert into app_recharge_subscription_gift
            <trim prefix="(" suffix=")" suffixOverrides=",">
                        <if test="item.appid != null">appid,</if>
                        <if test="item.itemId != null and item.itemId != ''">item_id,</if>
                        <if test="item.giftPeriodBegin != null">gift_period_begin,</if>
                        <if test="item.giftPeriodNumber != null">gift_period_number,</if>
                        <if test="item.giftPrice != null">gift_price,</if>
                        <if test="item.enable != null">enable,</if>
                        <if test="item.createBy != null">create_by,</if>
                        <if test="item.createTime != null">create_time,</if>
                        <if test="item.updateBy != null">update_by,</if>
                        <if test="item.updateTime != null">update_time,</if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                        <if test="item.appid != null">#{item.appid},</if>
                        <if test="item.itemId != null and item.itemId != ''">#{item.itemId},</if>
                        <if test="item.giftPeriodBegin != null">#{item.giftPeriodBegin},</if>
                        <if test="item.giftPeriodNumber != null">#{item.giftPeriodNumber},</if>
                        <if test="item.giftPrice != null">#{item.giftPrice},</if>
                        <if test="item.enable != null">#{item.enable},</if>
                        <if test="item.createBy != null">#{item.createBy},</if>
                        <if test="item.createTime != null">#{item.createTime},</if>
                        <if test="item.updateBy != null">#{item.updateBy},</if>
                        <if test="item.updateTime != null">#{item.updateTime},</if>
            </trim>
            on duplicate key update
            <trim suffixOverrides=",">
                        <if test="item.appid != null">appid = values(appid),</if>
                        <if test="item.itemId != null and item.itemId != ''">item_id = values(item_id),</if>
                        <if test="item.giftPeriodBegin != null">gift_period_begin = values(gift_period_begin),</if>
                        <if test="item.giftPeriodNumber != null">gift_period_number = values(gift_period_number),</if>
                        <if test="item.giftPrice != null">gift_price = values(gift_price),</if>
                        <if test="item.enable != null">enable = values(enable),</if>
                        <if test="item.createBy != null">create_by = values(create_by),</if>
                        <if test="item.createTime != null">create_time = values(create_time),</if>
                        <if test="item.updateBy != null">update_by = values(update_by),</if>
                        <if test="item.updateTime != null">update_time = values(update_time),</if>
            </trim>
        </foreach>

    </insert>

    <select id="getSummary" parameterType="AppRechargeSubscriptionGift" resultMap="AppRechargeSubscriptionGiftResult">
        select
            max(id) as id
        from app_recharge_subscription_gift
        <where>
                    <if test="appid != null  and appid != ''"> and appid = #{appid}</if>
                    <if test="itemId != null  and itemId != ''"> and item_id = #{itemId}</if>
                    <if test="giftPeriodBegin != null "> and gift_period_begin = #{giftPeriodBegin}</if>
                    <if test="giftPeriodNumber != null "> and gift_period_number = #{giftPeriodNumber}</if>
                    <if test="giftPrice != null "> and gift_price = #{giftPrice}</if>
                    <if test="enable != null "> and enable = #{enable}</if>
                </where>
    </select>



    <select id="summary" parameterType="tv.shorthub.common.core.domain.SummaryRequest" resultMap="AppRechargeSubscriptionGiftResult">
        
    </select>
    <select id="allSummary" parameterType="tv.shorthub.common.core.domain.SummaryRequest" resultMap="AppRechargeSubscriptionGiftResult">
        
    </select>

</mapper>
