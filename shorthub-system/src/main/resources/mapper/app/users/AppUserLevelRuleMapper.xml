<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tv.shorthub.system.mapper.AppUserLevelRuleMapper">

    <resultMap type="AppUserLevelRule" id="AppUserLevelRuleResult">
        <result property="id"    column="id"    />
        <result property="level"    column="level"    />
        <result property="ruleCode"    column="rule_code"    />
        <result property="ruleDesc"    column="rule_desc"    />
        <result property="ruleParams"    column="rule_params"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectAppUserLevelRuleVo">
        select id, level, rule_code, rule_desc, rule_params, create_by, create_time, update_by, update_time from app_user_level_rule
    </sql>

    <insert id="batchInsertOrUpdate">
        <foreach item="item" index="index" collection="list" separator=";">
            insert into app_user_level_rule
            <trim prefix="(" suffix=")" suffixOverrides=",">
                        <if test="item.level != null">level,</if>
                        <if test="item.ruleCode != null">rule_code,</if>
                        <if test="item.ruleDesc != null">rule_desc,</if>
                        <if test="item.ruleParams != null">rule_params,</if>
                        <if test="item.createBy != null">create_by,</if>
                        <if test="item.createTime != null">create_time,</if>
                        <if test="item.updateBy != null">update_by,</if>
                        <if test="item.updateTime != null">update_time,</if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                        <if test="item.level != null">#{item.level},</if>
                        <if test="item.ruleCode != null">#{item.ruleCode},</if>
                        <if test="item.ruleDesc != null">#{item.ruleDesc},</if>
                        <if test="item.ruleParams != null">#{item.ruleParams},</if>
                        <if test="item.createBy != null">#{item.createBy},</if>
                        <if test="item.createTime != null">#{item.createTime},</if>
                        <if test="item.updateBy != null">#{item.updateBy},</if>
                        <if test="item.updateTime != null">#{item.updateTime},</if>
            </trim>
            on duplicate key update
            <trim suffixOverrides=",">
                        <if test="item.level != null">level = values(level),</if>
                        <if test="item.ruleCode != null">rule_code = values(rule_code),</if>
                        <if test="item.ruleDesc != null">rule_desc = values(rule_desc),</if>
                        <if test="item.ruleParams != null">rule_params = values(rule_params),</if>
                        <if test="item.createBy != null">create_by = values(create_by),</if>
                        <if test="item.createTime != null">create_time = values(create_time),</if>
                        <if test="item.updateBy != null">update_by = values(update_by),</if>
                        <if test="item.updateTime != null">update_time = values(update_time),</if>
            </trim>
        </foreach>

    </insert>

    <select id="getSummary" parameterType="AppUserLevelRule" resultMap="AppUserLevelRuleResult">
        select
            max(id) as id
        from app_user_level_rule
        <where>
                    <if test="level != null  and level != ''"> and level = #{level}</if>
                    <if test="ruleCode != null  and ruleCode != ''"> and rule_code = #{ruleCode}</if>
                    <if test="ruleDesc != null  and ruleDesc != ''"> and rule_desc = #{ruleDesc}</if>
                    <if test="ruleParams != null "> and rule_params = #{ruleParams}</if>
                </where>
    </select>



    <select id="summary" parameterType="tv.shorthub.common.core.domain.SummaryRequest" resultMap="AppUserLevelRuleResult">
        
    </select>
    <select id="allSummary" parameterType="tv.shorthub.common.core.domain.SummaryRequest" resultMap="AppUserLevelRuleResult">
        
    </select>

</mapper>
