<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tv.shorthub.system.mapper.AppCountriesLanguagesMapper">
    
    <resultMap type="AppCountriesLanguages" id="AppCountriesLanguagesResult">
        <result property="id"    column="id"    />
        <result property="countryCode"    column="country_code"    />
        <result property="languageCode"    column="language_code"    />
        <result property="isDefault"    column="is_default"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectAppCountriesLanguagesVo">
        select id, country_code, language_code, is_default, create_by, create_time, update_by, update_time from app_countries_languages
    </sql>



    <select id="getSummary" parameterType="AppCountriesLanguages" resultMap="AppCountriesLanguagesResult">
        select
            max(id) as id
        from app_countries_languages
        <where>
                    <if test="countryCode != null  and countryCode != ''"> and country_code = #{countryCode}</if>
                    <if test="languageCode != null  and languageCode != ''"> and language_code = #{languageCode}</if>
                    <if test="isDefault != null "> and is_default = #{isDefault}</if>
                </where>
    </select>



    <select id="summary" parameterType="tv.shorthub.common.core.domain.SummaryRequest" resultMap="AppCountriesLanguagesResult">
            <include refid="tv.shorthub.common.mapper.CommonMapper.summarySql">
                <property name="tableName" value="app_countries_languages"/>
            </include>
    </select>
    <select id="allSummary" parameterType="tv.shorthub.common.core.domain.SummaryRequest" resultMap="AppCountriesLanguagesResult">
            <include refid="tv.shorthub.common.mapper.CommonMapper.allSummarySql">
                <property name="tableName" value="app_countries_languages"/>
            </include>
    </select>

</mapper>