<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tv.shorthub.system.mapper.AppConsumptionUnlockContentMapper">

    <resultMap type="AppConsumptionUnlockContent" id="AppConsumptionUnlockContentResult">
        <result property="id"    column="id"    />
        <result property="appid"    column="appid"    />
        <result property="userId"    column="user_id"    />
        <result property="dramaId"    column="drama_id"    />
        <result property="contentId"    column="content_id"    />
        <result property="serialNumber"    column="serial_number"    />
        <result property="expireTime"    column="expire_time"    />
        <result property="extendJson"    column="extend_json"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectAppConsumptionUnlockContentVo">
        select id, appid, user_id, drama_id, content_id, serial_number, expire_time, extend_json, create_by, create_time, update_by, update_time from app_consumption_unlock_content
    </sql>

    <insert id="batchInsertOrUpdate">
        <foreach item="item" index="index" collection="list" separator=";">
            insert into app_consumption_unlock_content
            <trim prefix="(" suffix=")" suffixOverrides=",">
                        <if test="item.appid != null">appid,</if>
                        <if test="item.userId != null and item.userId != ''">user_id,</if>
                        <if test="item.dramaId != null">drama_id,</if>
                        <if test="item.contentId != null">content_id,</if>
                        <if test="item.serialNumber != null">serial_number,</if>
                        <if test="item.expireTime != null">expire_time,</if>
                        <if test="item.extendJson != null">extend_json,</if>
                        <if test="item.createBy != null">create_by,</if>
                        <if test="item.createTime != null">create_time,</if>
                        <if test="item.updateBy != null">update_by,</if>
                        <if test="item.updateTime != null">update_time,</if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                        <if test="item.appid != null">#{item.appid},</if>
                        <if test="item.userId != null and item.userId != ''">#{item.userId},</if>
                        <if test="item.dramaId != null">#{item.dramaId},</if>
                        <if test="item.contentId != null">#{item.contentId},</if>
                        <if test="item.serialNumber != null">#{item.serialNumber},</if>
                        <if test="item.expireTime != null">#{item.expireTime},</if>
                        <if test="item.extendJson != null">#{item.extendJson},</if>
                        <if test="item.createBy != null">#{item.createBy},</if>
                        <if test="item.createTime != null">#{item.createTime},</if>
                        <if test="item.updateBy != null">#{item.updateBy},</if>
                        <if test="item.updateTime != null">#{item.updateTime},</if>
            </trim>
            on duplicate key update
            <trim suffixOverrides=",">
                        <if test="item.appid != null">appid = values(appid),</if>
                        <if test="item.userId != null and item.userId != ''">user_id = values(user_id),</if>
                        <if test="item.dramaId != null">drama_id = values(drama_id),</if>
                        <if test="item.contentId != null">content_id = values(content_id),</if>
                        <if test="item.serialNumber != null">serial_number = values(serial_number),</if>
                        <if test="item.expireTime != null">expire_time = values(expire_time),</if>
                        <if test="item.extendJson != null">extend_json = values(extend_json),</if>
                        <if test="item.createBy != null">create_by = values(create_by),</if>
                        <if test="item.createTime != null">create_time = values(create_time),</if>
                        <if test="item.updateBy != null">update_by = values(update_by),</if>
                        <if test="item.updateTime != null">update_time = values(update_time),</if>
            </trim>
        </foreach>

    </insert>

    <select id="getSummary" parameterType="AppConsumptionUnlockContent" resultMap="AppConsumptionUnlockContentResult">
        select
            max(id) as id
        from app_consumption_unlock_content
        <where>
                    <if test="appid != null  and appid != ''"> and appid = #{appid}</if>
                    <if test="userId != null  and userId != ''"> and user_id = #{userId}</if>
                    <if test="dramaId != null  and dramaId != ''"> and drama_id = #{dramaId}</if>
                    <if test="contentId != null  and contentId != ''"> and content_id = #{contentId}</if>
                    <if test="serialNumber != null "> and serial_number = #{serialNumber}</if>
                    <if test="expireTime != null "> and expire_time = #{expireTime}</if>
                    <if test="extendJson != null "> and extend_json = #{extendJson}</if>
                </where>
    </select>



    <select id="summary" parameterType="tv.shorthub.common.core.domain.SummaryRequest" resultMap="AppConsumptionUnlockContentResult">
        
    </select>
    <select id="allSummary" parameterType="tv.shorthub.common.core.domain.SummaryRequest" resultMap="AppConsumptionUnlockContentResult">
        
    </select>

</mapper>
