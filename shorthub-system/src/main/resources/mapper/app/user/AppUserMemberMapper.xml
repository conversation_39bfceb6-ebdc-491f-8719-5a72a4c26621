<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tv.shorthub.system.mapper.AppUserMemberMapper">

    <resultMap type="AppUserMember" id="AppUserMemberResult">
        <result property="id"    column="id"    />
        <result property="memberId"    column="member_id"    />
        <result property="memberLevel"    column="member_level"    />
        <result property="userId"    column="user_id"    />
        <result property="unlockNumber"    column="unlock_number"    />
        <result property="expireTime"    column="expire_time"    />
        <result property="beginTime"    column="begin_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectAppUserMemberVo">
        select id, member_id, member_level, user_id, unlock_number, expire_time, begin_time, create_by, create_time, update_by, update_time from app_user_member
    </sql>

    <insert id="batchInsertOrUpdate">
        <foreach item="item" index="index" collection="list" separator=";">
            insert into app_user_member
            <trim prefix="(" suffix=")" suffixOverrides=",">
                        <if test="item.memberId != null">member_id,</if>
                        <if test="item.memberLevel != null">member_level,</if>
                        <if test="item.userId != null">user_id,</if>
                        <if test="item.unlockNumber != null">unlock_number,</if>
                        <if test="item.expireTime != null">expire_time,</if>
                        <if test="item.beginTime != null">begin_time,</if>
                        <if test="item.createBy != null">create_by,</if>
                        <if test="item.createTime != null">create_time,</if>
                        <if test="item.updateBy != null">update_by,</if>
                        <if test="item.updateTime != null">update_time,</if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                        <if test="item.memberId != null">#{item.memberId},</if>
                        <if test="item.memberLevel != null">#{item.memberLevel},</if>
                        <if test="item.userId != null">#{item.userId},</if>
                        <if test="item.unlockNumber != null">#{item.unlockNumber},</if>
                        <if test="item.expireTime != null">#{item.expireTime},</if>
                        <if test="item.beginTime != null">#{item.beginTime},</if>
                        <if test="item.createBy != null">#{item.createBy},</if>
                        <if test="item.createTime != null">#{item.createTime},</if>
                        <if test="item.updateBy != null">#{item.updateBy},</if>
                        <if test="item.updateTime != null">#{item.updateTime},</if>
            </trim>
            on duplicate key update
            <trim suffixOverrides=",">
                        <if test="item.memberId != null">member_id = values(member_id),</if>
                        <if test="item.memberLevel != null">member_level = values(member_level),</if>
                        <if test="item.userId != null">user_id = values(user_id),</if>
                        <if test="item.unlockNumber != null">unlock_number = values(unlock_number),</if>
                        <if test="item.expireTime != null">expire_time = values(expire_time),</if>
                        <if test="item.beginTime != null">begin_time = values(begin_time),</if>
                        <if test="item.createBy != null">create_by = values(create_by),</if>
                        <if test="item.createTime != null">create_time = values(create_time),</if>
                        <if test="item.updateBy != null">update_by = values(update_by),</if>
                        <if test="item.updateTime != null">update_time = values(update_time),</if>
            </trim>
        </foreach>

    </insert>

    <select id="getSummary" parameterType="AppUserMember" resultMap="AppUserMemberResult">
        select
            max(id) as id
        from app_user_member
        <where>
                    <if test="memberId != null  and memberId != ''"> and member_id = #{memberId}</if>
                    <if test="memberLevel != null  and memberLevel != ''"> and member_level = #{memberLevel}</if>
                    <if test="userId != null  and userId != ''"> and user_id = #{userId}</if>
                    <if test="unlockNumber != null "> and unlock_number = #{unlockNumber}</if>
                    <if test="expireTime != null "> and expire_time = #{expireTime}</if>
                    <if test="beginTime != null "> and begin_time = #{beginTime}</if>
                </where>
    </select>



    <select id="summary" parameterType="tv.shorthub.common.core.domain.SummaryRequest" resultMap="AppUserMemberResult">
        
    </select>
    <select id="allSummary" parameterType="tv.shorthub.common.core.domain.SummaryRequest" resultMap="AppUserMemberResult">
        
    </select>

</mapper>
