<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tv.shorthub.system.mapper.AppBlacklistMapper">

    <resultMap type="AppBlacklist" id="AppBlacklistResult">
        <result property="id"    column="id"    />
        <result property="type"    column="type"    />
        <result property="value"    column="value"    />
        <result property="reason"    column="reason"    />
        <result property="status"    column="status"    />
        <result property="extendJson"    column="extend_json"    />
        <result property="createdBy"    column="created_by"    />
        <result property="createdAt"    column="created_at"    />
        <result property="updatedAt"    column="updated_at"    />
    </resultMap>

    <sql id="selectAppBlacklistVo">
        select id, type, value, reason, status, extend_json, created_by, created_at, updated_at from app_blacklist
    </sql>

    <insert id="batchInsertOrUpdate">
        <foreach item="item" index="index" collection="list" separator=";">
            insert into app_blacklist
            <trim prefix="(" suffix=")" suffixOverrides=",">
                        <if test="item.type != null and item.type != ''">type,</if>
                        <if test="item.value != null and item.value != ''">value,</if>
                        <if test="item.reason != null">reason,</if>
                        <if test="item.status != null">status,</if>
                        <if test="item.extendJson != null">extend_json,</if>
                        <if test="item.createdBy != null">created_by,</if>
                        <if test="item.createdAt != null">created_at,</if>
                        <if test="item.updatedAt != null">updated_at,</if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                        <if test="item.type != null and item.type != ''">#{item.type},</if>
                        <if test="item.value != null and item.value != ''">#{item.value},</if>
                        <if test="item.reason != null">#{item.reason},</if>
                        <if test="item.status != null">#{item.status},</if>
                        <if test="item.extendJson != null">#{item.extendJson},</if>
                        <if test="item.createdBy != null">#{item.createdBy},</if>
                        <if test="item.createdAt != null">#{item.createdAt},</if>
                        <if test="item.updatedAt != null">#{item.updatedAt},</if>
            </trim>
            on duplicate key update
            <trim suffixOverrides=",">
                        <if test="item.type != null and item.type != ''">type = values(type),</if>
                        <if test="item.value != null and item.value != ''">value = values(value),</if>
                        <if test="item.reason != null">reason = values(reason),</if>
                        <if test="item.status != null">status = values(status),</if>
                        <if test="item.extendJson != null">extend_json = values(extend_json),</if>
                        <if test="item.createdBy != null">created_by = values(created_by),</if>
                        <if test="item.createdAt != null">created_at = values(created_at),</if>
                        <if test="item.updatedAt != null">updated_at = values(updated_at),</if>
            </trim>
        </foreach>

    </insert>

    <select id="getSummary" parameterType="AppBlacklist" resultMap="AppBlacklistResult">
        select
            max(id) as id
        from app_blacklist
        <where>
                    <if test="type != null  and type != ''"> and type = #{type}</if>
                    <if test="value != null  and value != ''"> and value = #{value}</if>
                    <if test="reason != null  and reason != ''"> and reason = #{reason}</if>
                    <if test="status != null "> and status = #{status}</if>
                    <if test="extendJson != null "> and extend_json = #{extendJson}</if>
                    <if test="createdBy != null  and createdBy != ''"> and created_by = #{createdBy}</if>
                    <if test="createdAt != null "> and created_at = #{createdAt}</if>
                    <if test="updatedAt != null "> and updated_at = #{updatedAt}</if>
                </where>
    </select>



    <select id="summary" parameterType="tv.shorthub.common.core.domain.SummaryRequest" resultMap="AppBlacklistResult">
        
    </select>
    <select id="allSummary" parameterType="tv.shorthub.common.core.domain.SummaryRequest" resultMap="AppBlacklistResult">
        
    </select>

</mapper>
