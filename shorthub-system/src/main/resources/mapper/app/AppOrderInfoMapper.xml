<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tv.shorthub.system.mapper.AppOrderInfoMapper">

    <resultMap type="AppOrderInfo" id="AppOrderInfoResult">
        <result property="id"    column="id"    />
        <result property="orderNo"    column="order_no"    />
        <result property="userId"    column="user_id"    />
        <result property="appid"    column="appid"    />
        <result property="orderChannel"    column="order_channel"    />
        <result property="orderStatus"    column="order_status"    />
        <result property="orderAmount"    column="order_amount"    />
        <result property="payTime"    column="pay_time"    />
        <result property="payType"    column="pay_type"    />
        <result property="orderNumber"    column="order_number"    />
        <result property="giftOrderNumber"    column="gift_order_number"    />
        <result property="tfid"    column="tfid"    />
        <result property="contentId"    column="content_id"    />
        <result property="seriesIndex"    column="series_index"    />
        <result property="payMchId"    column="pay_mch_id"    />
        <result property="feeItemId"    column="fee_item_id"    />
        <result property="pushType"    column="push_type"    />
        <result property="pushResult"    column="push_result"    />
        <result property="adPlanId"    column="ad_plan_id"    />
        <result property="adId"    column="ad_id"    />
        <result property="refreshOrderType"    column="refresh_order_type"    />
        <result property="deviceType"    column="device_type"    />
        <result property="extendJson"    column="extend_json"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectAppOrderInfoVo">
        select id, order_no, user_id, appid, order_channel, order_status, order_amount, pay_time, pay_type, order_number, gift_order_number, tfid, content_id, series_index, pay_mch_id, fee_item_id, push_type, push_result, ad_plan_id, ad_id, refresh_order_type, device_type, extend_json, create_by, create_time, update_by, update_time from app_order_info
    </sql>

    <insert id="batchInsertOrUpdate">
        <foreach item="item" index="index" collection="list" separator=";">
            insert into app_order_info
            <trim prefix="(" suffix=")" suffixOverrides=",">
                        <if test="item.orderNo != null and item.orderNo != ''">order_no,</if>
                        <if test="item.userId != null and item.userId != ''">user_id,</if>
                        <if test="item.appid != null and item.appid != ''">appid,</if>
                        <if test="item.orderChannel != null">order_channel,</if>
                        <if test="item.orderStatus != null">order_status,</if>
                        <if test="item.orderAmount != null">order_amount,</if>
                        <if test="item.payTime != null">pay_time,</if>
                        <if test="item.payType != null">pay_type,</if>
                        <if test="item.orderNumber != null">order_number,</if>
                        <if test="item.giftOrderNumber != null">gift_order_number,</if>
                        <if test="item.tfid != null">tfid,</if>
                        <if test="item.contentId != null">content_id,</if>
                        <if test="item.seriesIndex != null">series_index,</if>
                        <if test="item.payMchId != null">pay_mch_id,</if>
                        <if test="item.feeItemId != null">fee_item_id,</if>
                        <if test="item.pushType != null">push_type,</if>
                        <if test="item.pushResult != null">push_result,</if>
                        <if test="item.adPlanId != null">ad_plan_id,</if>
                        <if test="item.adId != null">ad_id,</if>
                        <if test="item.refreshOrderType != null">refresh_order_type,</if>
                        <if test="item.deviceType != null">device_type,</if>
                        <if test="item.extendJson != null">extend_json,</if>
                        <if test="item.createBy != null">create_by,</if>
                        <if test="item.createTime != null">create_time,</if>
                        <if test="item.updateBy != null">update_by,</if>
                        <if test="item.updateTime != null">update_time,</if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                        <if test="item.orderNo != null and item.orderNo != ''">#{item.orderNo},</if>
                        <if test="item.userId != null and item.userId != ''">#{item.userId},</if>
                        <if test="item.appid != null and item.appid != ''">#{item.appid},</if>
                        <if test="item.orderChannel != null">#{item.orderChannel},</if>
                        <if test="item.orderStatus != null">#{item.orderStatus},</if>
                        <if test="item.orderAmount != null">#{item.orderAmount},</if>
                        <if test="item.payTime != null">#{item.payTime},</if>
                        <if test="item.payType != null">#{item.payType},</if>
                        <if test="item.orderNumber != null">#{item.orderNumber},</if>
                        <if test="item.giftOrderNumber != null">#{item.giftOrderNumber},</if>
                        <if test="item.tfid != null">#{item.tfid},</if>
                        <if test="item.contentId != null">#{item.contentId},</if>
                        <if test="item.seriesIndex != null">#{item.seriesIndex},</if>
                        <if test="item.payMchId != null">#{item.payMchId},</if>
                        <if test="item.feeItemId != null">#{item.feeItemId},</if>
                        <if test="item.pushType != null">#{item.pushType},</if>
                        <if test="item.pushResult != null">#{item.pushResult},</if>
                        <if test="item.adPlanId != null">#{item.adPlanId},</if>
                        <if test="item.adId != null">#{item.adId},</if>
                        <if test="item.refreshOrderType != null">#{item.refreshOrderType},</if>
                        <if test="item.deviceType != null">#{item.deviceType},</if>
                        <if test="item.extendJson != null">#{item.extendJson},</if>
                        <if test="item.createBy != null">#{item.createBy},</if>
                        <if test="item.createTime != null">#{item.createTime},</if>
                        <if test="item.updateBy != null">#{item.updateBy},</if>
                        <if test="item.updateTime != null">#{item.updateTime},</if>
            </trim>
            on duplicate key update
            <trim suffixOverrides=",">
                        <if test="item.orderNo != null and item.orderNo != ''">order_no = values(order_no),</if>
                        <if test="item.userId != null and item.userId != ''">user_id = values(user_id),</if>
                        <if test="item.appid != null and item.appid != ''">appid = values(appid),</if>
                        <if test="item.orderChannel != null">order_channel = values(order_channel),</if>
                        <if test="item.orderStatus != null">order_status = values(order_status),</if>
                        <if test="item.orderAmount != null">order_amount = values(order_amount),</if>
                        <if test="item.payTime != null">pay_time = values(pay_time),</if>
                        <if test="item.payType != null">pay_type = values(pay_type),</if>
                        <if test="item.orderNumber != null">order_number = values(order_number),</if>
                        <if test="item.giftOrderNumber != null">gift_order_number = values(gift_order_number),</if>
                        <if test="item.tfid != null">tfid = values(tfid),</if>
                        <if test="item.contentId != null">content_id = values(content_id),</if>
                        <if test="item.seriesIndex != null">series_index = values(series_index),</if>
                        <if test="item.payMchId != null">pay_mch_id = values(pay_mch_id),</if>
                        <if test="item.feeItemId != null">fee_item_id = values(fee_item_id),</if>
                        <if test="item.pushType != null">push_type = values(push_type),</if>
                        <if test="item.pushResult != null">push_result = values(push_result),</if>
                        <if test="item.adPlanId != null">ad_plan_id = values(ad_plan_id),</if>
                        <if test="item.adId != null">ad_id = values(ad_id),</if>
                        <if test="item.refreshOrderType != null">refresh_order_type = values(refresh_order_type),</if>
                        <if test="item.deviceType != null">device_type = values(device_type),</if>
                        <if test="item.extendJson != null">extend_json = values(extend_json),</if>
                        <if test="item.createBy != null">create_by = values(create_by),</if>
                        <if test="item.createTime != null">create_time = values(create_time),</if>
                        <if test="item.updateBy != null">update_by = values(update_by),</if>
                        <if test="item.updateTime != null">update_time = values(update_time),</if>
            </trim>
        </foreach>

    </insert>

    <select id="getSummary" parameterType="AppOrderInfo" resultMap="AppOrderInfoResult">
        select
            max(id) as id
        from app_order_info
        <where>
                    <if test="orderNo != null  and orderNo != ''"> and order_no = #{orderNo}</if>
                    <if test="userId != null  and userId != ''"> and user_id = #{userId}</if>
                    <if test="appid != null  and appid != ''"> and appid = #{appid}</if>
                    <if test="orderChannel != null  and orderChannel != ''"> and order_channel = #{orderChannel}</if>
                    <if test="orderStatus != null "> and order_status = #{orderStatus}</if>
                    <if test="orderAmount != null "> and order_amount = #{orderAmount}</if>
                    <if test="payTime != null "> and pay_time = #{payTime}</if>
                    <if test="payType != null  and payType != ''"> and pay_type = #{payType}</if>
                    <if test="orderNumber != null "> and order_number = #{orderNumber}</if>
                    <if test="giftOrderNumber != null "> and gift_order_number = #{giftOrderNumber}</if>
                    <if test="tfid != null  and tfid != ''"> and tfid = #{tfid}</if>
                    <if test="contentId != null  and contentId != ''"> and content_id = #{contentId}</if>
                    <if test="seriesIndex != null "> and series_index = #{seriesIndex}</if>
                    <if test="payMchId != null  and payMchId != ''"> and pay_mch_id = #{payMchId}</if>
                    <if test="feeItemId != null  and feeItemId != ''"> and fee_item_id = #{feeItemId}</if>
                    <if test="pushType != null "> and push_type = #{pushType}</if>
                    <if test="pushResult != null  and pushResult != ''"> and push_result = #{pushResult}</if>
                    <if test="adPlanId != null  and adPlanId != ''"> and ad_plan_id = #{adPlanId}</if>
                    <if test="adId != null  and adId != ''"> and ad_id = #{adId}</if>
                    <if test="refreshOrderType != null "> and refresh_order_type = #{refreshOrderType}</if>
                    <if test="deviceType != null  and deviceType != ''"> and device_type = #{deviceType}</if>
                    <if test="extendJson != null  and extendJson != ''"> and extend_json = #{extendJson}</if>
                </where>
    </select>



    <select id="summary" parameterType="tv.shorthub.common.core.domain.SummaryRequest" resultMap="AppOrderInfoResult">

    </select>
    <select id="allSummary" parameterType="tv.shorthub.common.core.domain.SummaryRequest" resultMap="AppOrderInfoResult">

    </select>


</mapper>
