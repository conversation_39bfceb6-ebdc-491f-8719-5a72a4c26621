<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tv.shorthub.system.mapper.AppDramaContentsSerialUrlMapper">

    <resultMap type="AppDramaContentsSerialUrl" id="AppDramaContentsSerialUrlResult">
        <result property="id"    column="id"    />
        <result property="serialId"    column="serial_id"    />
        <result property="storagePath"    column="storage_path"    />
        <result property="compressType"    column="compress_type"    />
        <result property="videoFormat"    column="video_format"    />
        <result property="videoUrl"    column="video_url"    />
        <result property="videoUrlExpireTime"    column="video_url_expire_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectAppDramaContentsSerialUrlVo">
        select id, serial_id, storage_path, compress_type, video_format, video_url, video_url_expire_time, create_by, create_time, update_by, update_time from app_drama_contents_serial_url
    </sql>

    <insert id="batchInsertOrUpdate">
        <foreach item="item" index="index" collection="list" separator=";">
            insert into app_drama_contents_serial_url
            <trim prefix="(" suffix=")" suffixOverrides=",">
                        <if test="item.serialId != null and item.serialId != ''">serial_id,</if>
                        <if test="item.storagePath != null">storage_path,</if>
                        <if test="item.compressType != null and item.compressType != ''">compress_type,</if>
                        <if test="item.videoFormat != null and item.videoFormat != ''">video_format,</if>
                        <if test="item.videoUrl != null and item.videoUrl != ''">video_url,</if>
                        <if test="item.videoUrlExpireTime != null">video_url_expire_time,</if>
                        <if test="item.createBy != null">create_by,</if>
                        <if test="item.createTime != null">create_time,</if>
                        <if test="item.updateBy != null">update_by,</if>
                        <if test="item.updateTime != null">update_time,</if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                        <if test="item.serialId != null and item.serialId != ''">#{item.serialId},</if>
                        <if test="item.storagePath != null">#{item.storagePath},</if>
                        <if test="item.compressType != null and item.compressType != ''">#{item.compressType},</if>
                        <if test="item.videoFormat != null and item.videoFormat != ''">#{item.videoFormat},</if>
                        <if test="item.videoUrl != null and item.videoUrl != ''">#{item.videoUrl},</if>
                        <if test="item.videoUrlExpireTime != null">#{item.videoUrlExpireTime},</if>
                        <if test="item.createBy != null">#{item.createBy},</if>
                        <if test="item.createTime != null">#{item.createTime},</if>
                        <if test="item.updateBy != null">#{item.updateBy},</if>
                        <if test="item.updateTime != null">#{item.updateTime},</if>
            </trim>
            on duplicate key update
            <trim suffixOverrides=",">
                        <if test="item.serialId != null and item.serialId != ''">serial_id = values(serial_id),</if>
                        <if test="item.storagePath != null">storage_path = values(storage_path),</if>
                        <if test="item.compressType != null and item.compressType != ''">compress_type = values(compress_type),</if>
                        <if test="item.videoFormat != null and item.videoFormat != ''">video_format = values(video_format),</if>
                        <if test="item.videoUrl != null and item.videoUrl != ''">video_url = values(video_url),</if>
                        <if test="item.videoUrlExpireTime != null">video_url_expire_time = values(video_url_expire_time),</if>
                        <if test="item.createBy != null">create_by = values(create_by),</if>
                        <if test="item.createTime != null">create_time = values(create_time),</if>
                        <if test="item.updateBy != null">update_by = values(update_by),</if>
                        <if test="item.updateTime != null">update_time = values(update_time),</if>
            </trim>
        </foreach>

    </insert>

    <select id="getSummary" parameterType="AppDramaContentsSerialUrl" resultMap="AppDramaContentsSerialUrlResult">
        select
            max(id) as id
        from app_drama_contents_serial_url
        <where>
                    <if test="serialId != null  and serialId != ''"> and serial_id = #{serialId}</if>
                    <if test="storagePath != null  and storagePath != ''"> and storage_path = #{storagePath}</if>
                    <if test="compressType != null  and compressType != ''"> and compress_type = #{compressType}</if>
                    <if test="videoFormat != null  and videoFormat != ''"> and video_format = #{videoFormat}</if>
                    <if test="videoUrl != null  and videoUrl != ''"> and video_url = #{videoUrl}</if>
                    <if test="videoUrlExpireTime != null "> and video_url_expire_time = #{videoUrlExpireTime}</if>
                </where>
    </select>



    <select id="summary" parameterType="tv.shorthub.common.core.domain.SummaryRequest" resultMap="AppDramaContentsSerialUrlResult">
        
    </select>
    <select id="allSummary" parameterType="tv.shorthub.common.core.domain.SummaryRequest" resultMap="AppDramaContentsSerialUrlResult">
        
    </select>

</mapper>
