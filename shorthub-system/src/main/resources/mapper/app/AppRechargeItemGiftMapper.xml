<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tv.shorthub.system.mapper.AppRechargeItemGiftMapper">

    <resultMap type="AppRechargeItemGift" id="AppRechargeItemGiftResult">
        <result property="id"    column="id"    />
        <result property="appid"    column="appid"    />
        <result property="itemId"    column="item_id"    />
        <result property="giftNumber"    column="gift_number"    />
        <result property="giftRemark"    column="gift_remark"    />
        <result property="giftText"    column="gift_text"    />
        <result property="enable"    column="enable"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectAppRechargeItemGiftVo">
        select id, appid, item_id, gift_number, gift_remark, gift_text, enable, create_by, create_time, update_by, update_time from app_recharge_item_gift
    </sql>

    <insert id="batchInsertOrUpdate">
        <foreach item="item" index="index" collection="list" separator=";">
            insert into app_recharge_item_gift
            <trim prefix="(" suffix=")" suffixOverrides=",">
                        <if test="item.appid != null">appid,</if>
                        <if test="item.itemId != null and item.itemId != ''">item_id,</if>
                        <if test="item.giftNumber != null">gift_number,</if>
                        <if test="item.giftRemark != null">gift_remark,</if>
                        <if test="item.giftText != null">gift_text,</if>
                        <if test="item.enable != null">enable,</if>
                        <if test="item.createBy != null">create_by,</if>
                        <if test="item.createTime != null">create_time,</if>
                        <if test="item.updateBy != null">update_by,</if>
                        <if test="item.updateTime != null">update_time,</if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                        <if test="item.appid != null">#{item.appid},</if>
                        <if test="item.itemId != null and item.itemId != ''">#{item.itemId},</if>
                        <if test="item.giftNumber != null">#{item.giftNumber},</if>
                        <if test="item.giftRemark != null">#{item.giftRemark},</if>
                        <if test="item.giftText != null">#{item.giftText},</if>
                        <if test="item.enable != null">#{item.enable},</if>
                        <if test="item.createBy != null">#{item.createBy},</if>
                        <if test="item.createTime != null">#{item.createTime},</if>
                        <if test="item.updateBy != null">#{item.updateBy},</if>
                        <if test="item.updateTime != null">#{item.updateTime},</if>
            </trim>
            on duplicate key update
            <trim suffixOverrides=",">
                        <if test="item.appid != null">appid = values(appid),</if>
                        <if test="item.itemId != null and item.itemId != ''">item_id = values(item_id),</if>
                        <if test="item.giftNumber != null">gift_number = values(gift_number),</if>
                        <if test="item.giftRemark != null">gift_remark = values(gift_remark),</if>
                        <if test="item.giftText != null">gift_text = values(gift_text),</if>
                        <if test="item.enable != null">enable = values(enable),</if>
                        <if test="item.createBy != null">create_by = values(create_by),</if>
                        <if test="item.createTime != null">create_time = values(create_time),</if>
                        <if test="item.updateBy != null">update_by = values(update_by),</if>
                        <if test="item.updateTime != null">update_time = values(update_time),</if>
            </trim>
        </foreach>

    </insert>

    <select id="getSummary" parameterType="AppRechargeItemGift" resultMap="AppRechargeItemGiftResult">
        select
            max(id) as id
        from app_recharge_item_gift
        <where>
                    <if test="appid != null  and appid != ''"> and appid = #{appid}</if>
                    <if test="itemId != null  and itemId != ''"> and item_id = #{itemId}</if>
                    <if test="giftNumber != null "> and gift_number = #{giftNumber}</if>
                    <if test="giftRemark != null  and giftRemark != ''"> and gift_remark = #{giftRemark}</if>
                    <if test="giftText != null  and giftText != ''"> and gift_text = #{giftText}</if>
                    <if test="enable != null "> and enable = #{enable}</if>
                </where>
    </select>



    <select id="summary" parameterType="tv.shorthub.common.core.domain.SummaryRequest" resultMap="AppRechargeItemGiftResult">
        
    </select>
    <select id="allSummary" parameterType="tv.shorthub.common.core.domain.SummaryRequest" resultMap="AppRechargeItemGiftResult">
        
    </select>

</mapper>
