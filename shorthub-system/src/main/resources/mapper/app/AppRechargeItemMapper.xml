<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tv.shorthub.system.mapper.AppRechargeItemMapper">

    <resultMap type="AppRechargeItem" id="AppRechargeItemResult">
        <result property="id"    column="id"    />
        <result property="appid"    column="appid"    />
        <result property="templateId"    column="template_id"    />
        <result property="itemId"    column="item_id"    />
        <result property="parentItemId"    column="parent_item_id"    />
        <result property="userType"    column="user_type"    />
        <result property="price"    column="price"    />
        <result property="number"    column="number"    />
        <result property="remake"    column="remake"    />
        <result property="type"    column="type"    />
        <result property="showProm"    column="show_prom"    />
        <result property="backcolor"    column="backcolor"    />
        <result property="textBackcolor"    column="text_backcolor"    />
        <result property="textColor"    column="text_color"    />
        <result property="borderColor"    column="border_color"    />
        <result property="backgroundImage"    column="background_image"    />
        <result property="showTag"    column="show_tag"    />
        <result property="tagBackColour"    column="tag_back_colour"    />
        <result property="tagText"    column="tag_text"    />
        <result property="tagTextColour"    column="tag_text_colour"    />
        <result property="showHandTab"    column="show_hand_tab"    />
        <result property="bottomTextColour"    column="bottom_text_colour"    />
        <result property="bottomBackColour"    column="bottom_back_colour"    />
        <result property="sort"    column="sort"    />
        <result property="enable"    column="enable"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectAppRechargeItemVo">
        select id, appid, template_id, item_id, user_type, price, number, remake, type, show_prom, backcolor, text_backcolor, text_color, border_color, background_image, show_tag, tag_back_colour, tag_text, tag_text_colour, show_hand_tab, bottom_text_colour, bottom_back_colour, sort, enable, create_by, create_time, update_by, update_time from app_recharge_item
    </sql>

    <insert id="batchInsertOrUpdate">
        <foreach item="item" index="index" collection="list" separator=";">
            insert into app_recharge_item
            <trim prefix="(" suffix=")" suffixOverrides=",">
                        <if test="item.appid != null">appid,</if>
                        <if test="item.templateId != null and item.templateId != ''">template_id,</if>
                        <if test="item.itemId != null and item.itemId != ''">item_id,</if>
                        <if test="item.parentItemId != null and item.parentItemId != ''">parent_item_id,</if>
                        <if test="item.userType != null and item.userType != ''">user_type,</if>
                        <if test="item.price != null">price,</if>
                        <if test="item.number != null">number,</if>
                        <if test="item.remake != null">remake,</if>
                        <if test="item.type != null">type,</if>
                        <if test="item.showProm != null">show_prom,</if>
                        <if test="item.backcolor != null">backcolor,</if>
                        <if test="item.textBackcolor != null">text_backcolor,</if>
                        <if test="item.textColor != null">text_color,</if>
                        <if test="item.borderColor != null">border_color,</if>
                        <if test="item.backgroundImage != null">background_image,</if>
                        <if test="item.showTag != null">show_tag,</if>
                        <if test="item.tagBackColour != null">tag_back_colour,</if>
                        <if test="item.tagText != null">tag_text,</if>
                        <if test="item.tagTextColour != null">tag_text_colour,</if>
                        <if test="item.showHandTab != null">show_hand_tab,</if>
                        <if test="item.bottomTextColour != null">bottom_text_colour,</if>
                        <if test="item.bottomBackColour != null">bottom_back_colour,</if>
                        <if test="item.sort != null">sort,</if>
                        <if test="item.enable != null">enable,</if>
                        <if test="item.createBy != null">create_by,</if>
                        <if test="item.createTime != null">create_time,</if>
                        <if test="item.updateBy != null">update_by,</if>
                        <if test="item.updateTime != null">update_time,</if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                        <if test="item.appid != null">#{item.appid},</if>
                        <if test="item.templateId != null and item.templateId != ''">#{item.templateId},</if>
                        <if test="item.itemId != null and item.itemId != ''">#{item.itemId},</if>
                        <if test="item.parentItemId != null and item.parentItemId != ''">parent_item_id,</if>
                        <if test="item.userType != null and item.userType != ''">#{item.userType},</if>
                        <if test="item.price != null">#{item.price},</if>
                        <if test="item.number != null">#{item.number},</if>
                        <if test="item.remake != null">#{item.remake},</if>
                        <if test="item.type != null">#{item.type},</if>
                        <if test="item.showProm != null">#{item.showProm},</if>
                        <if test="item.backcolor != null">#{item.backcolor},</if>
                        <if test="item.textBackcolor != null">#{item.textBackcolor},</if>
                        <if test="item.textColor != null">#{item.textColor},</if>
                        <if test="item.borderColor != null">#{item.borderColor},</if>
                        <if test="item.backgroundImage != null">#{item.backgroundImage},</if>
                        <if test="item.showTag != null">#{item.showTag},</if>
                        <if test="item.tagBackColour != null">#{item.tagBackColour},</if>
                        <if test="item.tagText != null">#{item.tagText},</if>
                        <if test="item.tagTextColour != null">#{item.tagTextColour},</if>
                        <if test="item.showHandTab != null">#{item.showHandTab},</if>
                        <if test="item.bottomTextColour != null">#{item.bottomTextColour},</if>
                        <if test="item.bottomBackColour != null">#{item.bottomBackColour},</if>
                        <if test="item.sort != null">#{item.sort},</if>
                        <if test="item.enable != null">#{item.enable},</if>
                        <if test="item.createBy != null">#{item.createBy},</if>
                        <if test="item.createTime != null">#{item.createTime},</if>
                        <if test="item.updateBy != null">#{item.updateBy},</if>
                        <if test="item.updateTime != null">#{item.updateTime},</if>
            </trim>
            on duplicate key update
            <trim suffixOverrides=",">
                        <if test="item.appid != null">appid = values(appid),</if>
                        <if test="item.templateId != null and item.templateId != ''">template_id = values(template_id),</if>
                        <if test="item.itemId != null and item.itemId != ''">item_id = values(item_id),</if>
                        <if test="item.parentItemId != null and item.parentItemId != ''">parent_item_id,</if>
                        <if test="item.userType != null and item.userType != ''">user_type = values(user_type),</if>
                        <if test="item.price != null">price = values(price),</if>
                        <if test="item.number != null">number = values(number),</if>
                        <if test="item.remake != null">remake = values(remake),</if>
                        <if test="item.type != null">type = values(type),</if>
                        <if test="item.showProm != null">show_prom = values(show_prom),</if>
                        <if test="item.backcolor != null">backcolor = values(backcolor),</if>
                        <if test="item.textBackcolor != null">text_backcolor = values(text_backcolor),</if>
                        <if test="item.textColor != null">text_color = values(text_color),</if>
                        <if test="item.borderColor != null">border_color = values(border_color),</if>
                        <if test="item.backgroundImage != null">background_image = values(background_image),</if>
                        <if test="item.showTag != null">show_tag = values(show_tag),</if>
                        <if test="item.tagBackColour != null">tag_back_colour = values(tag_back_colour),</if>
                        <if test="item.tagText != null">tag_text = values(tag_text),</if>
                        <if test="item.tagTextColour != null">tag_text_colour = values(tag_text_colour),</if>
                        <if test="item.showHandTab != null">show_hand_tab = values(show_hand_tab),</if>
                        <if test="item.bottomTextColour != null">bottom_text_colour = values(bottom_text_colour),</if>
                        <if test="item.bottomBackColour != null">bottom_back_colour = values(bottom_back_colour),</if>
                        <if test="item.sort != null">sort = values(sort),</if>
                        <if test="item.enable != null">enable = values(enable),</if>
                        <if test="item.createBy != null">create_by = values(create_by),</if>
                        <if test="item.createTime != null">create_time = values(create_time),</if>
                        <if test="item.updateBy != null">update_by = values(update_by),</if>
                        <if test="item.updateTime != null">update_time = values(update_time),</if>
            </trim>
        </foreach>

    </insert>

    <select id="getSummary" parameterType="AppRechargeItem" resultMap="AppRechargeItemResult">
        select
            max(id) as id
        from app_recharge_item
        <where>
                    <if test="appid != null  and appid != ''"> and appid = #{appid}</if>
                    <if test="templateId != null  and templateId != ''"> and template_id = #{templateId}</if>
                    <if test="itemId != null  and itemId != ''"> and item_id = #{itemId}</if>
                    <if test="item.parentItemId != null and item.parentItemId != ''">parent_item_id,</if>
                    <if test="userType != null  and userType != ''"> and user_type = #{userType}</if>
                    <if test="price != null "> and price = #{price}</if>
                    <if test="number != null "> and number = #{number}</if>
                    <if test="remake != null  and remake != ''"> and remake = #{remake}</if>
                    <if test="type != null "> and type = #{type}</if>
                    <if test="showProm != null "> and show_prom = #{showProm}</if>
                    <if test="backcolor != null  and backcolor != ''"> and backcolor = #{backcolor}</if>
                    <if test="textBackcolor != null  and textBackcolor != ''"> and text_backcolor = #{textBackcolor}</if>
                    <if test="textColor != null  and textColor != ''"> and text_color = #{textColor}</if>
                    <if test="borderColor != null  and borderColor != ''"> and border_color = #{borderColor}</if>
                    <if test="backgroundImage != null  and backgroundImage != ''"> and background_image = #{backgroundImage}</if>
                    <if test="showTag != null "> and show_tag = #{showTag}</if>
                    <if test="tagBackColour != null  and tagBackColour != ''"> and tag_back_colour = #{tagBackColour}</if>
                    <if test="tagText != null  and tagText != ''"> and tag_text = #{tagText}</if>
                    <if test="tagTextColour != null  and tagTextColour != ''"> and tag_text_colour = #{tagTextColour}</if>
                    <if test="showHandTab != null "> and show_hand_tab = #{showHandTab}</if>
                    <if test="bottomTextColour != null  and bottomTextColour != ''"> and bottom_text_colour = #{bottomTextColour}</if>
                    <if test="bottomBackColour != null  and bottomBackColour != ''"> and bottom_back_colour = #{bottomBackColour}</if>
                    <if test="sort != null "> and sort = #{sort}</if>
                    <if test="enable != null "> and enable = #{enable}</if>
                </where>
    </select>



    <select id="summary" parameterType="tv.shorthub.common.core.domain.SummaryRequest" resultMap="AppRechargeItemResult">
        
    </select>
    <select id="allSummary" parameterType="tv.shorthub.common.core.domain.SummaryRequest" resultMap="AppRechargeItemResult">
        
    </select>

</mapper>
