<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tv.shorthub.system.mapper.AppUserCollectMapper">

    <resultMap type="AppUserCollect" id="AppUserCollectResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="contentId"    column="content_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectAppUserCollectVo">
        select id, user_id, content_id, create_by, create_time, update_by, update_time from app_user_collect
    </sql>

    <insert id="batchInsertOrUpdate">
        <foreach item="item" index="index" collection="list" separator=";">
            insert into app_user_collect
            <trim prefix="(" suffix=")" suffixOverrides=",">
                        <if test="item.userId != null">user_id,</if>
                        <if test="item.contentId != null">content_id,</if>
                        <if test="item.createBy != null">create_by,</if>
                        <if test="item.createTime != null">create_time,</if>
                        <if test="item.updateBy != null">update_by,</if>
                        <if test="item.updateTime != null">update_time,</if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                        <if test="item.userId != null">#{item.userId},</if>
                        <if test="item.contentId != null">#{item.contentId},</if>
                        <if test="item.createBy != null">#{item.createBy},</if>
                        <if test="item.createTime != null">#{item.createTime},</if>
                        <if test="item.updateBy != null">#{item.updateBy},</if>
                        <if test="item.updateTime != null">#{item.updateTime},</if>
            </trim>
            on duplicate key update
            <trim suffixOverrides=",">
                        <if test="item.userId != null">user_id = values(user_id),</if>
                        <if test="item.contentId != null">content_id = values(content_id),</if>
                        <if test="item.createBy != null">create_by = values(create_by),</if>
                        <if test="item.createTime != null">create_time = values(create_time),</if>
                        <if test="item.updateBy != null">update_by = values(update_by),</if>
                        <if test="item.updateTime != null">update_time = values(update_time),</if>
            </trim>
        </foreach>

    </insert>

    <select id="getSummary" parameterType="AppUserCollect" resultMap="AppUserCollectResult">
        select
            max(id) as id
        from app_user_collect
        <where>
                    <if test="userId != null "> and user_id = #{userId}</if>
                    <if test="contentId != null "> and content_id = #{contentId}</if>
                </where>
    </select>



    <select id="summary" parameterType="tv.shorthub.common.core.domain.SummaryRequest" resultMap="AppUserCollectResult">
        
    </select>
    <select id="allSummary" parameterType="tv.shorthub.common.core.domain.SummaryRequest" resultMap="AppUserCollectResult">
        
    </select>

</mapper>
