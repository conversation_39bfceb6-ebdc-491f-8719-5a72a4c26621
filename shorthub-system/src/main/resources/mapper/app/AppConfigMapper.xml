<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tv.shorthub.system.mapper.AppConfigMapper">

    <resultMap type="AppConfig" id="AppConfigResult">
        <result property="id"    column="id"    />
        <result property="appid"    column="appid"    />
        <result property="appname"    column="appname"    />
        <result property="channel"    column="channel"    />
        <result property="language"    column="language"    />
        <result property="extendJson"    column="extend_json"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectAppConfigVo">
        select id, appid, appname, channel, language, extend_json, create_by, create_time, update_by, update_time from app_config
    </sql>

    <insert id="batchInsertOrUpdate">
        <foreach item="item" index="index" collection="list" separator=";">
            insert into app_config
            <trim prefix="(" suffix=")" suffixOverrides=",">
                        <if test="item.appid != null and item.appid != ''">appid,</if>
                        <if test="item.appname != null">appname,</if>
                        <if test="item.channel != null and item.channel != ''">channel,</if>
                        <if test="item.language != null and item.language != ''">language,</if>
                        <if test="item.extendJson != null">extend_json,</if>
                        <if test="item.createBy != null">create_by,</if>
                        <if test="item.createTime != null">create_time,</if>
                        <if test="item.updateBy != null">update_by,</if>
                        <if test="item.updateTime != null">update_time,</if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                        <if test="item.appid != null and item.appid != ''">#{item.appid},</if>
                        <if test="item.appname != null">#{item.appname},</if>
                        <if test="item.channel != null and item.channel != ''">#{item.channel},</if>
                        <if test="item.language != null and item.language != ''">#{item.language},</if>
                        <if test="item.extendJson != null">#{item.extendJson},</if>
                        <if test="item.createBy != null">#{item.createBy},</if>
                        <if test="item.createTime != null">#{item.createTime},</if>
                        <if test="item.updateBy != null">#{item.updateBy},</if>
                        <if test="item.updateTime != null">#{item.updateTime},</if>
            </trim>
            on duplicate key update
            <trim suffixOverrides=",">
                        <if test="item.appid != null and item.appid != ''">appid = values(appid),</if>
                        <if test="item.appname != null">appname = values(appname),</if>
                        <if test="item.channel != null and item.channel != ''">channel = values(channel),</if>
                        <if test="item.language != null and item.language != ''">language = values(language),</if>
                        <if test="item.extendJson != null">extend_json = values(extend_json),</if>
                        <if test="item.createBy != null">create_by = values(create_by),</if>
                        <if test="item.createTime != null">create_time = values(create_time),</if>
                        <if test="item.updateBy != null">update_by = values(update_by),</if>
                        <if test="item.updateTime != null">update_time = values(update_time),</if>
            </trim>
        </foreach>

    </insert>

    <select id="getSummary" parameterType="AppConfig" resultMap="AppConfigResult">
        select
            max(id) as id
        from app_config
        <where>
                    <if test="appid != null  and appid != ''"> and appid = #{appid}</if>
                    <if test="appname != null  and appname != ''"> and appname like concat('%', #{appname}, '%')</if>
                    <if test="channel != null  and channel != ''"> and channel = #{channel}</if>
                    <if test="language != null  and language != ''"> and language = #{language}</if>
                    <if test="extendJson != null "> and extend_json = #{extendJson}</if>
                </where>
    </select>



    <select id="summary" parameterType="tv.shorthub.common.core.domain.SummaryRequest" resultMap="AppConfigResult">
        
    </select>
    <select id="allSummary" parameterType="tv.shorthub.common.core.domain.SummaryRequest" resultMap="AppConfigResult">
        
    </select>

</mapper>
