<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tv.shorthub.system.mapper.PaypalPaymentConfigMapper">

    <resultMap type="PaypalPaymentConfig" id="PaypalPaymentConfigResult">
        <result property="id"    column="id"    />
        <result property="clientId"    column="client_id"    />
        <result property="clientSecret"    column="client_secret"    />
        <result property="mode"    column="mode"    />
        <result property="enable"    column="enable"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectPaypalPaymentConfigVo">
        select id, client_id, client_secret, mode, enable, create_by, create_time, update_by, update_time from paypal_payment_config
    </sql>

    <insert id="batchInsertOrUpdate">
        <foreach item="item" index="index" collection="list" separator=";">
            insert into paypal_payment_config
            <trim prefix="(" suffix=")" suffixOverrides=",">
                        <if test="item.clientId != null">client_id,</if>
                        <if test="item.clientSecret != null">client_secret,</if>
                        <if test="item.mode != null">mode,</if>
                        <if test="item.enable != null">enable,</if>
                        <if test="item.createBy != null">create_by,</if>
                        <if test="item.createTime != null">create_time,</if>
                        <if test="item.updateBy != null">update_by,</if>
                        <if test="item.updateTime != null">update_time,</if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                        <if test="item.clientId != null">#{item.clientId},</if>
                        <if test="item.clientSecret != null">#{item.clientSecret},</if>
                        <if test="item.mode != null">#{item.mode},</if>
                        <if test="item.enable != null">#{item.enable},</if>
                        <if test="item.createBy != null">#{item.createBy},</if>
                        <if test="item.createTime != null">#{item.createTime},</if>
                        <if test="item.updateBy != null">#{item.updateBy},</if>
                        <if test="item.updateTime != null">#{item.updateTime},</if>
            </trim>
            on duplicate key update
            <trim suffixOverrides=",">
                        <if test="item.clientId != null">client_id = values(client_id),</if>
                        <if test="item.clientSecret != null">client_secret = values(client_secret),</if>
                        <if test="item.mode != null">mode = values(mode),</if>
                        <if test="item.enable != null">enable = values(enable),</if>
                        <if test="item.createBy != null">create_by = values(create_by),</if>
                        <if test="item.createTime != null">create_time = values(create_time),</if>
                        <if test="item.updateBy != null">update_by = values(update_by),</if>
                        <if test="item.updateTime != null">update_time = values(update_time),</if>
            </trim>
        </foreach>

    </insert>

    <select id="getSummary" parameterType="PaypalPaymentConfig" resultMap="PaypalPaymentConfigResult">
        select
            max(id) as id
        from paypal_payment_config
        <where>
                    <if test="clientId != null  and clientId != ''"> and client_id = #{clientId}</if>
                    <if test="clientSecret != null  and clientSecret != ''"> and client_secret = #{clientSecret}</if>
                    <if test="mode != null  and mode != ''"> and mode = #{mode}</if>
                    <if test="enable != null "> and enable = #{enable}</if>
                </where>
    </select>



    <select id="summary" parameterType="tv.shorthub.common.core.domain.SummaryRequest" resultMap="PaypalPaymentConfigResult">
        
    </select>
    <select id="allSummary" parameterType="tv.shorthub.common.core.domain.SummaryRequest" resultMap="PaypalPaymentConfigResult">
        
    </select>

</mapper>
