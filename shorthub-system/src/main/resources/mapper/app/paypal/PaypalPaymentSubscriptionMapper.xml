<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tv.shorthub.system.mapper.PaypalPaymentSubscriptionMapper">

    <resultMap type="PaypalPaymentSubscription" id="PaypalPaymentSubscriptionResult">
        <result property="id"    column="id"    />
        <result property="orderNo"    column="order_no"    />
        <result property="userId"    column="user_id"    />
        <result property="paymentId"    column="payment_id"    />
        <result property="sequence"    column="sequence"    />
        <result property="state"    column="state"    />
        <result property="cycleExecution"    column="cycle_execution"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectPaypalPaymentSubscriptionVo">
        select id, order_no, user_id, payment_id, sequence, state, cycle_execution, create_by, create_time, update_by, update_time from paypal_payment_subscription
    </sql>

    <insert id="batchInsertOrUpdate">
        <foreach item="item" index="index" collection="list" separator=";">
            insert into paypal_payment_subscription
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="item.orderNo != null">order_no,</if>
                <if test="item.userId != null">user_id,</if>
                <if test="item.paymentId != null and item.paymentId != ''">payment_id,</if>
                <if test="item.sequence != null">sequence,</if>
                <if test="item.state != null and item.state != ''">state,</if>
                <if test="item.cycleExecution != null">cycle_execution,</if>
                <if test="item.createBy != null">create_by,</if>
                <if test="item.createTime != null">create_time,</if>
                <if test="item.updateBy != null">update_by,</if>
                <if test="item.updateTime != null">update_time,</if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="item.orderNo != null">#{item.orderNo},</if>
                <if test="item.userId != null">#{item.userId},</if>
                <if test="item.paymentId != null and item.paymentId != ''">#{item.paymentId},</if>
                <if test="item.sequence != null">#{item.sequence},</if>
                <if test="item.state != null and item.state != ''">#{item.state},</if>
                <if test="item.cycleExecution != null">#{item.cycleExecution},</if>
                <if test="item.createBy != null">#{item.createBy},</if>
                <if test="item.createTime != null">#{item.createTime},</if>
                <if test="item.updateBy != null">#{item.updateBy},</if>
                <if test="item.updateTime != null">#{item.updateTime},</if>
            </trim>
            on duplicate key update
            <trim suffixOverrides=",">
                <if test="item.orderNo != null">order_no = values(order_no),</if>
                <if test="item.userId != null">user_id = values(user_id),</if>
                <if test="item.paymentId != null and item.paymentId != ''">payment_id = values(payment_id),</if>
                <if test="item.sequence != null">sequence = values(sequence),</if>
                <if test="item.state != null and item.state != ''">state = values(state),</if>
                <if test="item.cycleExecution != null">cycle_execution = values(cycle_execution),</if>
                <if test="item.createBy != null">create_by = values(create_by),</if>
                <if test="item.createTime != null">create_time = values(create_time),</if>
                <if test="item.updateBy != null">update_by = values(update_by),</if>
                <if test="item.updateTime != null">update_time = values(update_time),</if>
            </trim>
        </foreach>

    </insert>

    <select id="getSummary" parameterType="PaypalPaymentSubscription" resultMap="PaypalPaymentSubscriptionResult">
        select
        max(id) as id
        from paypal_payment_subscription
        <where>
            <if test="orderNo != null  and orderNo != ''"> and order_no = #{orderNo}</if>
            <if test="userId != null  and userId != ''"> and user_id = #{userId}</if>
            <if test="paymentId != null  and paymentId != ''"> and payment_id = #{paymentId}</if>
            <if test="sequence != null "> and sequence = #{sequence}</if>
            <if test="state != null  and state != ''"> and state = #{state}</if>
            <if test="cycleExecution != null "> and cycle_execution = #{cycleExecution}</if>
        </where>
    </select>



    <select id="summary" parameterType="tv.shorthub.common.core.domain.SummaryRequest" resultMap="PaypalPaymentSubscriptionResult">

    </select>
    <select id="allSummary" parameterType="tv.shorthub.common.core.domain.SummaryRequest" resultMap="PaypalPaymentSubscriptionResult">

    </select>

</mapper>
