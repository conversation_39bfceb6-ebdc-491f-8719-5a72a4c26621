<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tv.shorthub.system.mapper.PaypalSubscriptionRenewalMapper">

    <resultMap type="PaypalSubscriptionRenewal" id="PaypalSubscriptionRenewalResult">
        <result property="id"    column="id"    />
        <result property="subscriptionId"    column="subscription_id"    />
        <result property="orderNo"    column="order_no"    />
        <result property="planId"    column="plan_id"    />
        <result property="status"    column="status"    />
        <result property="amount"    column="amount"    />
        <result property="currency"    column="currency"    />
        <result property="cycleNumber"    column="cycle_number"    />
        <result property="totalCycles"    column="total_cycles"    />
        <result property="nextBillingTime"    column="next_billing_time"    />
        <result property="renewalTime"    column="renewal_time"    />
        <result property="rawData"    column="raw_data"    />
        <result property="errorMessage"    column="error_message"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectPaypalSubscriptionRenewalVo">
        select id, subscription_id, order_no, plan_id, status, amount, currency, cycle_number, total_cycles, next_billing_time, renewal_time, raw_data, error_message, create_by, create_time, update_by, update_time from paypal_subscription_renewal
    </sql>

    <insert id="batchInsertOrUpdate">
        <foreach item="item" index="index" collection="list" separator=";">
            insert into paypal_subscription_renewal
            <trim prefix="(" suffix=")" suffixOverrides=",">
                        <if test="item.subscriptionId != null and item.subscriptionId != ''">subscription_id,</if>
                        <if test="item.orderNo != null and item.orderNo != ''">order_no,</if>
                        <if test="item.planId != null and item.planId != ''">plan_id,</if>
                        <if test="item.status != null and item.status != ''">status,</if>
                        <if test="item.amount != null">amount,</if>
                        <if test="item.currency != null and item.currency != ''">currency,</if>
                        <if test="item.cycleNumber != null">cycle_number,</if>
                        <if test="item.totalCycles != null">total_cycles,</if>
                        <if test="item.nextBillingTime != null">next_billing_time,</if>
                        <if test="item.renewalTime != null">renewal_time,</if>
                        <if test="item.rawData != null">raw_data,</if>
                        <if test="item.errorMessage != null">error_message,</if>
                        <if test="item.createBy != null">create_by,</if>
                        <if test="item.createTime != null">create_time,</if>
                        <if test="item.updateBy != null">update_by,</if>
                        <if test="item.updateTime != null">update_time,</if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                        <if test="item.subscriptionId != null and item.subscriptionId != ''">#{item.subscriptionId},</if>
                        <if test="item.orderNo != null and item.orderNo != ''">#{item.orderNo},</if>
                        <if test="item.planId != null and item.planId != ''">#{item.planId},</if>
                        <if test="item.status != null and item.status != ''">#{item.status},</if>
                        <if test="item.amount != null">#{item.amount},</if>
                        <if test="item.currency != null and item.currency != ''">#{item.currency},</if>
                        <if test="item.cycleNumber != null">#{item.cycleNumber},</if>
                        <if test="item.totalCycles != null">#{item.totalCycles},</if>
                        <if test="item.nextBillingTime != null">#{item.nextBillingTime},</if>
                        <if test="item.renewalTime != null">#{item.renewalTime},</if>
                        <if test="item.rawData != null">#{item.rawData},</if>
                        <if test="item.errorMessage != null">#{item.errorMessage},</if>
                        <if test="item.createBy != null">#{item.createBy},</if>
                        <if test="item.createTime != null">#{item.createTime},</if>
                        <if test="item.updateBy != null">#{item.updateBy},</if>
                        <if test="item.updateTime != null">#{item.updateTime},</if>
            </trim>
            on duplicate key update
            <trim suffixOverrides=",">
                        <if test="item.subscriptionId != null and item.subscriptionId != ''">subscription_id = values(subscription_id),</if>
                        <if test="item.orderNo != null and item.orderNo != ''">order_no = values(order_no),</if>
                        <if test="item.planId != null and item.planId != ''">plan_id = values(plan_id),</if>
                        <if test="item.status != null and item.status != ''">status = values(status),</if>
                        <if test="item.amount != null">amount = values(amount),</if>
                        <if test="item.currency != null and item.currency != ''">currency = values(currency),</if>
                        <if test="item.cycleNumber != null">cycle_number = values(cycle_number),</if>
                        <if test="item.totalCycles != null">total_cycles = values(total_cycles),</if>
                        <if test="item.nextBillingTime != null">next_billing_time = values(next_billing_time),</if>
                        <if test="item.renewalTime != null">renewal_time = values(renewal_time),</if>
                        <if test="item.rawData != null">raw_data = values(raw_data),</if>
                        <if test="item.errorMessage != null">error_message = values(error_message),</if>
                        <if test="item.createBy != null">create_by = values(create_by),</if>
                        <if test="item.createTime != null">create_time = values(create_time),</if>
                        <if test="item.updateBy != null">update_by = values(update_by),</if>
                        <if test="item.updateTime != null">update_time = values(update_time),</if>
            </trim>
        </foreach>

    </insert>

    <select id="getSummary" parameterType="PaypalSubscriptionRenewal" resultMap="PaypalSubscriptionRenewalResult">
        select
            max(id) as id
        from paypal_subscription_renewal
        <where>
                    <if test="subscriptionId != null  and subscriptionId != ''"> and subscription_id = #{subscriptionId}</if>
                    <if test="orderNo != null  and orderNo != ''"> and order_no = #{orderNo}</if>
                    <if test="planId != null  and planId != ''"> and plan_id = #{planId}</if>
                    <if test="status != null  and status != ''"> and status = #{status}</if>
                    <if test="amount != null "> and amount = #{amount}</if>
                    <if test="currency != null  and currency != ''"> and currency = #{currency}</if>
                    <if test="cycleNumber != null "> and cycle_number = #{cycleNumber}</if>
                    <if test="totalCycles != null "> and total_cycles = #{totalCycles}</if>
                    <if test="nextBillingTime != null "> and next_billing_time = #{nextBillingTime}</if>
                    <if test="renewalTime != null "> and renewal_time = #{renewalTime}</if>
                    <if test="rawData != null  and rawData != ''"> and raw_data = #{rawData}</if>
                    <if test="errorMessage != null  and errorMessage != ''"> and error_message = #{errorMessage}</if>
                </where>
    </select>



    <select id="summary" parameterType="tv.shorthub.common.core.domain.SummaryRequest" resultMap="PaypalSubscriptionRenewalResult">
        
    </select>
    <select id="allSummary" parameterType="tv.shorthub.common.core.domain.SummaryRequest" resultMap="PaypalSubscriptionRenewalResult">
        
    </select>

</mapper>
