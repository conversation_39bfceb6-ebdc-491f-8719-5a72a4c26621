<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tv.shorthub.system.mapper.PaypalPaymentLifecycleMapper">

    <resultMap type="PaypalPaymentLifecycle" id="PaypalPaymentLifecycleResult">
        <result property="id"    column="id"    />
        <result property="orderNo"    column="order_no"    />
        <result property="state"    column="state"    />
        <result property="errmsg"    column="errmsg"    />
        <result property="extendData"    column="extend_data"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectPaypalPaymentLifecycleVo">
        select id, order_no, state, errmsg, extend_data, create_by, create_time, update_by, update_time from paypal_payment_lifecycle
    </sql>

    <select id="getSummary" parameterType="PaypalPaymentLifecycle" resultMap="PaypalPaymentLifecycleResult">
        select
            max(id) as id
        from paypal_payment_lifecycle
        <where>
                    <if test="orderNo != null  and orderNo != ''"> and order_no = #{orderNo}</if>
                    <if test="state != null  and state != ''"> and state = #{state}</if>
                    <if test="errmsg != null  and errmsg != ''"> and errmsg = #{errmsg}</if>
                    <if test="extendData != null "> and extend_data = #{extendData}</if>
                </where>
    </select>



    <select id="summary" parameterType="tv.shorthub.common.core.domain.SummaryRequest" resultMap="PaypalPaymentLifecycleResult">
        
    </select>
    <select id="allSummary" parameterType="tv.shorthub.common.core.domain.SummaryRequest" resultMap="PaypalPaymentLifecycleResult">
        
    </select>

</mapper>
