<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tv.shorthub.system.mapper.PaypalPaymentLogMapper">

    <resultMap type="PaypalPaymentLog" id="PaypalPaymentLogResult">
        <result property="id"    column="id"    />
        <result property="orderNo"    column="order_no"    />
        <result property="paymentId"    column="payment_id"    />
        <result property="payerId"    column="payer_id"    />
        <result property="state"    column="state"    />
        <result property="totalAmount"    column="total_amount"    />
        <result property="currency"    column="currency"    />
        <result property="description"    column="description"    />
        <result property="returnUrl"    column="return_url"    />
        <result property="cancelUrl"    column="cancel_url"    />
        <result property="paymentMethod"    column="payment_method"    />
        <result property="rawData"    column="raw_data"    />
        <result property="relatedUserId"    column="related_user_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectPaypalPaymentLogVo">
        select id, order_no, payment_id, payer_id, state, total_amount, currency, description, return_url, cancel_url, payment_method, raw_data, related_user_id, create_by, create_time, update_by, update_time from paypal_payment_log
    </sql>

    <insert id="batchInsertOrUpdate">
        <foreach item="item" index="index" collection="list" separator=";">
            insert into paypal_payment_log
            <trim prefix="(" suffix=")" suffixOverrides=",">
                        <if test="item.orderNo != null">order_no,</if>
                        <if test="item.paymentId != null and item.paymentId != ''">payment_id,</if>
                        <if test="item.payerId != null">payer_id,</if>
                        <if test="item.state != null and item.state != ''">state,</if>
                        <if test="item.totalAmount != null">total_amount,</if>
                        <if test="item.currency != null and item.currency != ''">currency,</if>
                        <if test="item.description != null">description,</if>
                        <if test="item.returnUrl != null and item.returnUrl != ''">return_url,</if>
                        <if test="item.cancelUrl != null and item.cancelUrl != ''">cancel_url,</if>
                        <if test="item.paymentMethod != null and item.paymentMethod != ''">payment_method,</if>
                        <if test="item.rawData != null">raw_data,</if>
                        <if test="item.relatedUserId != null">related_user_id,</if>
                        <if test="item.createBy != null">create_by,</if>
                        <if test="item.createTime != null">create_time,</if>
                        <if test="item.updateBy != null">update_by,</if>
                        <if test="item.updateTime != null">update_time,</if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                        <if test="item.orderNo != null">#{item.orderNo},</if>
                        <if test="item.paymentId != null and item.paymentId != ''">#{item.paymentId},</if>
                        <if test="item.payerId != null">#{item.payerId},</if>
                        <if test="item.state != null and item.state != ''">#{item.state},</if>
                        <if test="item.totalAmount != null">#{item.totalAmount},</if>
                        <if test="item.currency != null and item.currency != ''">#{item.currency},</if>
                        <if test="item.description != null">#{item.description},</if>
                        <if test="item.returnUrl != null and item.returnUrl != ''">#{item.returnUrl},</if>
                        <if test="item.cancelUrl != null and item.cancelUrl != ''">#{item.cancelUrl},</if>
                        <if test="item.paymentMethod != null and item.paymentMethod != ''">#{item.paymentMethod},</if>
                        <if test="item.rawData != null">#{item.rawData},</if>
                        <if test="item.relatedUserId != null">#{item.relatedUserId},</if>
                        <if test="item.createBy != null">#{item.createBy},</if>
                        <if test="item.createTime != null">#{item.createTime},</if>
                        <if test="item.updateBy != null">#{item.updateBy},</if>
                        <if test="item.updateTime != null">#{item.updateTime},</if>
            </trim>
            on duplicate key update
            <trim suffixOverrides=",">
                        <if test="item.orderNo != null">order_no = values(order_no),</if>
                        <if test="item.paymentId != null and item.paymentId != ''">payment_id = values(payment_id),</if>
                        <if test="item.payerId != null">payer_id = values(payer_id),</if>
                        <if test="item.state != null and item.state != ''">state = values(state),</if>
                        <if test="item.totalAmount != null">total_amount = values(total_amount),</if>
                        <if test="item.currency != null and item.currency != ''">currency = values(currency),</if>
                        <if test="item.description != null">description = values(description),</if>
                        <if test="item.returnUrl != null and item.returnUrl != ''">return_url = values(return_url),</if>
                        <if test="item.cancelUrl != null and item.cancelUrl != ''">cancel_url = values(cancel_url),</if>
                        <if test="item.paymentMethod != null and item.paymentMethod != ''">payment_method = values(payment_method),</if>
                        <if test="item.rawData != null">raw_data = values(raw_data),</if>
                        <if test="item.relatedUserId != null">related_user_id = values(related_user_id),</if>
                        <if test="item.createBy != null">create_by = values(create_by),</if>
                        <if test="item.createTime != null">create_time = values(create_time),</if>
                        <if test="item.updateBy != null">update_by = values(update_by),</if>
                        <if test="item.updateTime != null">update_time = values(update_time),</if>
            </trim>
        </foreach>

    </insert>

    <select id="getSummary" parameterType="PaypalPaymentLog" resultMap="PaypalPaymentLogResult">
        select
            max(id) as id
        from paypal_payment_log
        <where>
                    <if test="orderNo != null  and orderNo != ''"> and order_no = #{orderNo}</if>
                    <if test="paymentId != null  and paymentId != ''"> and payment_id = #{paymentId}</if>
                    <if test="payerId != null  and payerId != ''"> and payer_id = #{payerId}</if>
                    <if test="state != null  and state != ''"> and state = #{state}</if>
                    <if test="totalAmount != null "> and total_amount = #{totalAmount}</if>
                    <if test="currency != null  and currency != ''"> and currency = #{currency}</if>
                    <if test="description != null  and description != ''"> and description = #{description}</if>
                    <if test="returnUrl != null  and returnUrl != ''"> and return_url = #{returnUrl}</if>
                    <if test="cancelUrl != null  and cancelUrl != ''"> and cancel_url = #{cancelUrl}</if>
                    <if test="paymentMethod != null  and paymentMethod != ''"> and payment_method = #{paymentMethod}</if>
                    <if test="rawData != null "> and raw_data = #{rawData}</if>
                    <if test="relatedUserId != null "> and related_user_id = #{relatedUserId}</if>
                </where>
    </select>



    <select id="summary" parameterType="tv.shorthub.common.core.domain.SummaryRequest" resultMap="PaypalPaymentLogResult">
        
    </select>
    <select id="allSummary" parameterType="tv.shorthub.common.core.domain.SummaryRequest" resultMap="PaypalPaymentLogResult">
        
    </select>

</mapper>
