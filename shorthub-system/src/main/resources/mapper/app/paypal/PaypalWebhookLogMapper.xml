<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tv.shorthub.system.mapper.PaypalWebhookLogMapper">

    <resultMap type="PaypalWebhookLog" id="PaypalWebhookLogResult">
        <result property="id"    column="id"    />
        <result property="pid"    column="pid"    />
        <result property="transmissionId"    column="transmission_id"    />
        <result property="transmissionTime"    column="transmission_time"    />
        <result property="certUrl"    column="cert_url"    />
        <result property="transmissionSig"    column="transmission_sig"    />
        <result property="state"    column="state"    />
        <result property="eventData"    column="event_data"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectPaypalWebhookLogVo">
        select id, pid, transmission_id, transmission_time, cert_url, transmission_sig, state, event_data, create_by, create_time, update_by, update_time from paypal_webhook_log
    </sql>

    <insert id="batchInsertOrUpdate">
        <foreach item="item" index="index" collection="list" separator=";">
            insert into paypal_webhook_log
            <trim prefix="(" suffix=")" suffixOverrides=",">
                        <if test="item.pid != null">pid,</if>
                        <if test="item.transmissionId != null and item.transmissionId != ''">transmission_id,</if>
                        <if test="item.transmissionTime != null">transmission_time,</if>
                        <if test="item.certUrl != null">cert_url,</if>
                        <if test="item.transmissionSig != null">transmission_sig,</if>
                        <if test="item.state != null and item.state != ''">state,</if>
                        <if test="item.eventData != null">event_data,</if>
                        <if test="item.createBy != null">create_by,</if>
                        <if test="item.createTime != null">create_time,</if>
                        <if test="item.updateBy != null">update_by,</if>
                        <if test="item.updateTime != null">update_time,</if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                        <if test="item.pid != null">#{item.pid},</if>
                        <if test="item.transmissionId != null and item.transmissionId != ''">#{item.transmissionId},</if>
                        <if test="item.transmissionTime != null">#{item.transmissionTime},</if>
                        <if test="item.certUrl != null">#{item.certUrl},</if>
                        <if test="item.transmissionSig != null">#{item.transmissionSig},</if>
                        <if test="item.state != null and item.state != ''">#{item.state},</if>
                        <if test="item.eventData != null">#{item.eventData},</if>
                        <if test="item.createBy != null">#{item.createBy},</if>
                        <if test="item.createTime != null">#{item.createTime},</if>
                        <if test="item.updateBy != null">#{item.updateBy},</if>
                        <if test="item.updateTime != null">#{item.updateTime},</if>
            </trim>
            on duplicate key update
            <trim suffixOverrides=",">
                        <if test="item.pid != null">pid = values(pid),</if>
                        <if test="item.transmissionId != null and item.transmissionId != ''">transmission_id = values(transmission_id),</if>
                        <if test="item.transmissionTime != null">transmission_time = values(transmission_time),</if>
                        <if test="item.certUrl != null">cert_url = values(cert_url),</if>
                        <if test="item.transmissionSig != null">transmission_sig = values(transmission_sig),</if>
                        <if test="item.state != null and item.state != ''">state = values(state),</if>
                        <if test="item.eventData != null">event_data = values(event_data),</if>
                        <if test="item.createBy != null">create_by = values(create_by),</if>
                        <if test="item.createTime != null">create_time = values(create_time),</if>
                        <if test="item.updateBy != null">update_by = values(update_by),</if>
                        <if test="item.updateTime != null">update_time = values(update_time),</if>
            </trim>
        </foreach>

    </insert>

    <select id="getSummary" parameterType="PaypalWebhookLog" resultMap="PaypalWebhookLogResult">
        select
            max(id) as id
        from paypal_webhook_log
        <where>
                    <if test="pid != null "> and pid = #{pid}</if>
                    <if test="transmissionId != null  and transmissionId != ''"> and transmission_id = #{transmissionId}</if>
                    <if test="transmissionTime != null  and transmissionTime != ''"> and transmission_time = #{transmissionTime}</if>
                    <if test="certUrl != null  and certUrl != ''"> and cert_url = #{certUrl}</if>
                    <if test="transmissionSig != null  and transmissionSig != ''"> and transmission_sig = #{transmissionSig}</if>
                    <if test="state != null  and state != ''"> and state = #{state}</if>
                    <if test="eventData != null "> and event_data = #{eventData}</if>
                </where>
    </select>



    <select id="summary" parameterType="tv.shorthub.common.core.domain.SummaryRequest" resultMap="PaypalWebhookLogResult">
        
    </select>
    <select id="allSummary" parameterType="tv.shorthub.common.core.domain.SummaryRequest" resultMap="PaypalWebhookLogResult">
        
    </select>

</mapper>
