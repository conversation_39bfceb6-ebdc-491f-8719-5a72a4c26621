<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tv.shorthub.system.mapper.PaypalDisputeMapper">

    <resultMap type="PaypalDispute" id="PaypalDisputeResult">
        <result property="id"    column="id"    />
        <result property="disputeId"    column="dispute_id"    />
        <result property="paymentId"    column="payment_id"    />
        <result property="status"    column="status"    />
        <result property="reason"    column="reason"    />
        <result property="disputeType"    column="dispute_type"    />
        <result property="amount"    column="amount"    />
        <result property="currency"    column="currency"    />
        <result property="buyerId"    column="buyer_id"    />
        <result property="sellerId"    column="seller_id"    />
        <result property="rawData"    column="raw_data"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectPaypalDisputeVo">
        select id, dispute_id, payment_id, status, reason, dispute_type, amount, currency, buyer_id, seller_id, raw_data, create_time, update_time, create_by, update_by, remark from paypal_dispute
    </sql>

    <insert id="batchInsertOrUpdate">
        <foreach item="item" index="index" collection="list" separator=";">
            insert into paypal_dispute
            <trim prefix="(" suffix=")" suffixOverrides=",">
                        <if test="item.disputeId != null and item.disputeId != ''">dispute_id,</if>
                        <if test="item.paymentId != null and item.paymentId != ''">payment_id,</if>
                        <if test="item.status != null and item.status != ''">status,</if>
                        <if test="item.reason != null">reason,</if>
                        <if test="item.disputeType != null">dispute_type,</if>
                        <if test="item.amount != null">amount,</if>
                        <if test="item.currency != null">currency,</if>
                        <if test="item.buyerId != null">buyer_id,</if>
                        <if test="item.sellerId != null">seller_id,</if>
                        <if test="item.rawData != null">raw_data,</if>
                        <if test="item.createTime != null">create_time,</if>
                        <if test="item.updateTime != null">update_time,</if>
                        <if test="item.createBy != null">create_by,</if>
                        <if test="item.updateBy != null">update_by,</if>
                        <if test="item.remark != null">remark,</if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                        <if test="item.disputeId != null and item.disputeId != ''">#{item.disputeId},</if>
                        <if test="item.paymentId != null and item.paymentId != ''">#{item.paymentId},</if>
                        <if test="item.status != null and item.status != ''">#{item.status},</if>
                        <if test="item.reason != null">#{item.reason},</if>
                        <if test="item.disputeType != null">#{item.disputeType},</if>
                        <if test="item.amount != null">#{item.amount},</if>
                        <if test="item.currency != null">#{item.currency},</if>
                        <if test="item.buyerId != null">#{item.buyerId},</if>
                        <if test="item.sellerId != null">#{item.sellerId},</if>
                        <if test="item.rawData != null">#{item.rawData},</if>
                        <if test="item.createTime != null">#{item.createTime},</if>
                        <if test="item.updateTime != null">#{item.updateTime},</if>
                        <if test="item.createBy != null">#{item.createBy},</if>
                        <if test="item.updateBy != null">#{item.updateBy},</if>
                        <if test="item.remark != null">#{item.remark},</if>
            </trim>
            on duplicate key update
            <trim suffixOverrides=",">
                        <if test="item.disputeId != null and item.disputeId != ''">dispute_id = values(dispute_id),</if>
                        <if test="item.paymentId != null and item.paymentId != ''">payment_id = values(payment_id),</if>
                        <if test="item.status != null and item.status != ''">status = values(status),</if>
                        <if test="item.reason != null">reason = values(reason),</if>
                        <if test="item.disputeType != null">dispute_type = values(dispute_type),</if>
                        <if test="item.amount != null">amount = values(amount),</if>
                        <if test="item.currency != null">currency = values(currency),</if>
                        <if test="item.buyerId != null">buyer_id = values(buyer_id),</if>
                        <if test="item.sellerId != null">seller_id = values(seller_id),</if>
                        <if test="item.rawData != null">raw_data = values(raw_data),</if>
                        <if test="item.createTime != null">create_time = values(create_time),</if>
                        <if test="item.updateTime != null">update_time = values(update_time),</if>
                        <if test="item.createBy != null">create_by = values(create_by),</if>
                        <if test="item.updateBy != null">update_by = values(update_by),</if>
                        <if test="item.remark != null">remark = values(remark),</if>
            </trim>
        </foreach>

    </insert>

    <select id="getSummary" parameterType="PaypalDispute" resultMap="PaypalDisputeResult">
        select
            max(id) as id
        from paypal_dispute
        <where>
                    <if test="disputeId != null  and disputeId != ''"> and dispute_id = #{disputeId}</if>
                    <if test="paymentId != null  and paymentId != ''"> and payment_id = #{paymentId}</if>
                    <if test="status != null  and status != ''"> and status = #{status}</if>
                    <if test="reason != null  and reason != ''"> and reason = #{reason}</if>
                    <if test="disputeType != null  and disputeType != ''"> and dispute_type = #{disputeType}</if>
                    <if test="amount != null "> and amount = #{amount}</if>
                    <if test="currency != null  and currency != ''"> and currency = #{currency}</if>
                    <if test="buyerId != null  and buyerId != ''"> and buyer_id = #{buyerId}</if>
                    <if test="sellerId != null  and sellerId != ''"> and seller_id = #{sellerId}</if>
                    <if test="rawData != null "> and raw_data = #{rawData}</if>
                </where>
    </select>



    <select id="summary" parameterType="tv.shorthub.common.core.domain.SummaryRequest" resultMap="PaypalDisputeResult">
        
    </select>
    <select id="allSummary" parameterType="tv.shorthub.common.core.domain.SummaryRequest" resultMap="PaypalDisputeResult">
        
    </select>

    <select id="findOrderNoByCaptureId" resultType="java.lang.String">
        SELECT order_no
        FROM paypal_payment_log
        WHERE JSON_UNQUOTE(JSON_EXTRACT(raw_data, '$.purchase_units[0].payments.captures[0].id')) = #{captureId}
           OR JSON_UNQUOTE(JSON_EXTRACT(raw_data, '$.result.purchase_units[0].payments.captures[0].id')) = #{captureId}
        ORDER BY create_time DESC
        LIMIT 1
    </select>

</mapper>
