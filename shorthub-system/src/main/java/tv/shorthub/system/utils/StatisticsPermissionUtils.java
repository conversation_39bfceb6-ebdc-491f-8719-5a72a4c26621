package tv.shorthub.system.utils;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import tv.shorthub.common.utils.SecurityUtils;
import tv.shorthub.system.domain.AppPromotion;
import tv.shorthub.system.mapper.AppPromotionMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 统计权限工具类
 * 统一处理统计模块的权限验证和数据过滤逻辑
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
@Component
public class StatisticsPermissionUtils {

    @Autowired
    private AppPromotionMapper appPromotionMapper;

    /**
     * 检查用户是否只有投手权限
     *
     * @return true 如果只是投手，false 如果有更高权限
     */
    public boolean isDeliverOnly() {
        try {
            // 如果是主账号，可以看到所有数据
            if (SecurityUtils.isBusinessAdmin()) {
                return false;
            }
            // 否则就是投手，只能看到自己的数据
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 获取指定用户创建的所有推广链接ID
     *
     * @param username 用户名
     * @return 推广链接ID列表
     */
    public List<String> getUserTfids(String username) {
        // 优先使用AppPromotionMapper的专用方法
        try {
            return appPromotionMapper.selectTfidsByCreator(username);
        } catch (Exception e) {
            // 如果专用方法不存在，使用通用查询
            QueryWrapper<AppPromotion> promotionWrapper = new QueryWrapper<>();
            promotionWrapper.eq("create_by", username);
            List<AppPromotion> promotions = appPromotionMapper.selectList(promotionWrapper);

            if (CollectionUtils.isEmpty(promotions)) {
                return List.of();
            }

            return promotions.stream()
                    .map(AppPromotion::getTfid)
                    .collect(Collectors.toList());
        }
    }

    /**
     * 根据用户权限过滤数据
     * 如果用户只有投手权限，则只能看到自己创建的推广链接数据
     *
     * @param wrapper 查询条件包装器
     */
    public <T> void filterByDeliverPermission(QueryWrapper<T> wrapper) {
        try {
            // 判断用户是否只有投手权限
            if (isDeliverOnly()) {
                String currentUsername = SecurityUtils.getUsername();
                List<String> tfids = getUserTfids(currentUsername);

                if (!CollectionUtils.isEmpty(tfids)) {
                    // 添加 tfid 过滤条件
                    wrapper.in("tfid", tfids);
                } else {
                    // 如果没有推广链接，设置一个不可能的条件，返回空数据
                    wrapper.eq("1", "2");
                }
            }
        } catch (Exception e) {
            // 如果权限判断出错，不影响正常查询
            // 可以记录日志
        }
    }

    /**
     * 检查用户是否有权限访问指定的tfid
     *
     * @param tfid 推广链接ID
     * @return true 如果有权限，false 如果没有权限
     */
    public boolean hasPermissionForTfid(String tfid) {
        // 如果不是投手，则有权限访问任何tfid
        if (!isDeliverOnly()) {
            return true;
        }

        // 如果是投手，检查tfid是否属于自己
        String currentUsername = SecurityUtils.getUsername();
        List<String> userTfids = getUserTfids(currentUsername);

        return userTfids.contains(tfid);
    }
}
