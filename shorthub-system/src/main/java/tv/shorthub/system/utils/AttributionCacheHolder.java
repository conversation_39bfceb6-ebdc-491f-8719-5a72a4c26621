package tv.shorthub.system.utils;

import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tv.shorthub.common.core.cache.CacheKeyUtils;
import tv.shorthub.common.core.cache.CacheService;
import tv.shorthub.common.core.redis.RedisCache;
import tv.shorthub.system.domain.AppConfig;
import tv.shorthub.system.domain.AppPromotion;

import java.util.concurrent.TimeUnit;

@Component
@Slf4j
public class AttributionCacheHolder {

    @Autowired
    RedisCache redisCache;

    @Autowired
    CacheService cacheService;


    /**
     * 获取并校验归因
     * @param deviceId
     * @param ip
     * @return
     */
    public JSONObject getAndVerifyAttribution(String deviceId, String ip, String tfid) {
        AppPromotion promotion = cacheService.getCacheObject(CacheKeyUtils.getAppPromotion(tfid));
        AppConfig appConfig = cacheService.getCacheMapValue(CacheKeyUtils.APP_CONFIG_MAP, promotion.getAppid());
        JSONObject attribution = getAttribution(deviceId, ip, tfid);
        boolean verify = true;
        if (appConfig.getChannel().equals("Web")) {
            // 设备校验
            verify = verifyDevice(attribution, deviceId);
        }
        if (verify) {
            verify = verifyAppid(attribution, appConfig.getAppid());
        }
        return verify ? attribution : null;
    }

    public void addAttribution(String deviceId, String ip, String tfid, JSONObject data) {
        if (StringUtils.isNotEmpty(deviceId)) {
            if (StringUtils.isNotEmpty(tfid)) {
                addAttributionByDevice(deviceId + tfid, data);
            }
            addAttributionByDevice(deviceId, data);
        }
        if (StringUtils.isNotEmpty(ip)) {
            if (StringUtils.isNotEmpty(tfid)) {
                addAttributionByIp(ip + tfid, data);
            }
            addAttributionByIp(ip, data);
        }
    }

    public boolean verifyAppid(JSONObject data, String appid) {
        if (null != data && StringUtils.isNotEmpty(appid)) {
            String attributeAppid = data.getString("appid");
            if (StringUtils.isNotEmpty(attributeAppid) && !attributeAppid.equals(appid)) {
                log.info("Appid校验不通过 att:{}, req:{}", attributeAppid, appid);
                return false;
            }
        }
        return true;
    }
    public boolean verifyDevice(JSONObject data, String deviceId) {
        if (null != data && StringUtils.isNotEmpty(deviceId)) {
            String attributeDeviceId = data.getString("deviceId");
            if (null == attributeDeviceId && data.containsKey("facebook")) {
                attributeDeviceId = data.getJSONObject("facebook").getString("deviceId");
            }
            if (StringUtils.isNotEmpty(attributeDeviceId) && !attributeDeviceId.equals(deviceId)) {
                log.info("设备校验不通过 att:{}, req:{}", attributeDeviceId, deviceId);
                return false;
            }
        }
        return true;
    }

    /**
     * 获取归因参数, 优先从IP获取，没有则从设备ID获取
     * @param deviceId
     * @param ip
     * @return
     */
    public JSONObject getAttribution(String deviceId, String ip, String tfid) {
        if (StringUtils.isNotEmpty(ip)) {
            if (StringUtils.isNotEmpty(tfid)) {
                // 修复染色问题
                // 优先通过ip+tfid来获取归因参数，
                JSONObject data = getAttributionByIp(ip + tfid);
                if (null != data) {
                    return data;
                }
            }
            JSONObject data = getAttributionByIp(ip);
            if (null != data) {
                return data;
            }
        }
        if (StringUtils.isNotEmpty(deviceId)) {
            if (StringUtils.isNotEmpty(tfid)) {
                // 修复染色问题
                // 优先通过deviceId+tfid来获取归因参数，
                JSONObject data = getAttributionByDevice(deviceId + tfid);
                if (null != data) {
                    return data;
                }
            }
            return getAttributionByDevice(deviceId);
        }
        return null;
    }

    private void addAttributionByIp(String ip, JSONObject data) {
        redisCache.setCacheObject(CacheKeyUtils.getAttributionByIp(ip), data, 7, TimeUnit.DAYS);
    }

    private JSONObject getAttributionByIp(String ip) {
        if (StringUtils.isEmpty(ip)) {
            return null;
        }
        return cacheService.getCacheObject(CacheKeyUtils.getAttributionByIp(ip));
    }

    private void addAttributionByDevice(String deviceId, JSONObject data) {
        redisCache.setCacheObject(CacheKeyUtils.getAttributionByDevice(deviceId), data, 7, TimeUnit.DAYS);
    }

    private JSONObject getAttributionByDevice(String deviceId) {
        if (StringUtils.isEmpty(deviceId)) {
            return null;
        }
        return cacheService.getCacheObject(CacheKeyUtils.getAttributionByDevice(deviceId));
    }


}
