package tv.shorthub.system.domain;

import tv.shorthub.common.annotation.Excel;
import tv.shorthub.common.core.domain.BaseEntity;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 剧目内容对象 app_drama_contents
 * 
 * <AUTHOR>
 * @date 2025-05-06
 */
@Data
public class AppDramaContents extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 剧目 */
    @Excel(name = "剧目")
    private String dramaId;

    /** 剧目语种id */
    @Excel(name = "剧目语种id")
    private String contentId;

    /** 关联内容ID */
    @Excel(name = "关联内容ID")
    private String unionContentId;

    /** 语言 */
    @Excel(name = "语言")
    private String languageCode;

    /** 标题 */
    @Excel(name = "标题")
    private String title;

    /** 封面 */
    @Excel(name = "封面")
    private String image;

    /** 总集数 */
    @Excel(name = "总集数")
    private Long total;

    /** 第几集开始收费 */
    @Excel(name = "第几集开始收费")
    private Long feeBegin;

    /** 每集多少金币 */
    @Excel(name = "每集多少金币")
    private Long everyMoney;

    /** 充值模板id */
    @Excel(name = "充值模板id")
    private String feeTemplateId;

    /** 是否自动购买剧集, 0 -否，1-是 */
    @Excel(name = "是否自动购买剧集, 0 -否，1-是")
    private Boolean autoBuyVideo;

    /** 描述 */
    @Excel(name = "描述")
    private String description;

    /** 启用 */
    @Excel(name = "启用")
    private Boolean enable;



    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("dramaId", getDramaId())
                .append("languageCode", getLanguageCode())
                .append("title", getTitle())
                .append("image", getImage())
                .append("total", getTotal())
                .append("feeBegin", getFeeBegin())
                .append("everyMoney", getEveryMoney())
                .append("feeTemplateId", getFeeTemplateId())
                .append("autoBuyVideo", getAutoBuyVideo())
                .append("description", getDescription())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("remark", getRemark())
                .append("params", getParams())
                .toString();
    }
}
