package tv.shorthub.system.domain;

import com.alibaba.fastjson2.JSONObject;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import tv.shorthub.common.annotation.Excel;
import lombok.Data;
import tv.shorthub.common.core.domain.BaseEntity;

/**
 * 虚拟货币支付配置对象 crypto_payment_config
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
@Data
public class CryptoPaymentConfig extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 配置名称 */
    @Excel(name = "配置名称")
    private String configName;

    /** 区块链类型(tron,base,solana) */
    @Excel(name = "区块链类型(tron,base,solana)")
    private String blockchain;

    /** 代币类型(USDT,USDC) */
    @Excel(name = "代币类型(USDT,USDC)")
    private String tokenType;

    /** 代币合约地址 */
    @Excel(name = "代币合约地址")
    private String contractAddress;

    /** RPC节点地址 */
    @Excel(name = "RPC节点地址")
    private String rpcUrl;

    /** 是否启用(0-禁用,1-启用) */
    @Excel(name = "是否启用(0-禁用,1-启用)")
    private Boolean enabled;

    /** 是否为测试网(0-主网,1-测试网) */
    @Excel(name = "是否为测试网(0-主网,1-测试网)")
    private Boolean testnet;

    /** 确认数要求 */
    @Excel(name = "确认数要求")
    private Long confirmations;

    /** 扩展配置 */
    @Excel(name = "扩展配置")
    private JSONObject extendConfig;



    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("configName", getConfigName())
            .append("blockchain", getBlockchain())
            .append("tokenType", getTokenType())
            .append("contractAddress", getContractAddress())
            .append("rpcUrl", getRpcUrl())
            .append("enabled", getEnabled())
            .append("testnet", getTestnet())
            .append("confirmations", getConfirmations())
            .append("extendConfig", getExtendConfig())
            .append("remark", getRemark())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
