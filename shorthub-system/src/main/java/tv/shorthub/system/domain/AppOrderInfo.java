package tv.shorthub.system.domain;

import java.math.BigDecimal;
import java.util.Date;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import tv.shorthub.common.annotation.Excel;
import lombok.Data;
import tv.shorthub.common.core.domain.BaseEntity;

/**
 * 充值订单对象 app_order_info
 *
 * <AUTHOR>
 * @date 2025-05-10
 */
@Data
public class AppOrderInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 自增ID */
    @Excel(name = "自增ID")
    @TableId(type = IdType.AUTO)
    private Long id;

    /** 订单号 */
    @Excel(name = "订单号")
    private String orderNo;

    /** 买家ID */
    @Excel(name = "买家ID")
    private String userId;

    /** 应用端 */
    @Excel(name = "应用端")
    private String appid;

    /** 支付通道, paypal */
    @Excel(name = "支付通道, paypal")
    private String orderChannel;

    /** 支付状态 0未付款 1已付款 */
    @Excel(name = "支付状态 0未付款 1已付款")
    private Long orderStatus;

    /** 退款状态 */
    @Excel(name = "退款状态")
    private Boolean refundStatus;

    /** 取消订阅状态 */
    @Excel(name = "取消订阅状态")
    private Boolean unsubscriptionStatus;

    /** 订单金额 */
    @Excel(name = "订单金额")
    private BigDecimal orderAmount;

    /** 原始价格 */
    @Excel(name = "原始价格")
    private BigDecimal orderAmountOrigin;

    /** 币种 */
    @Excel(name = "币种")
    private String currency;

    /** 订单支付时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "订单支付时间", width = 60, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date payTime;

    /** 充值类型: 0=会员, 1=金币, 2=订阅 */
    @Excel(name = "充值类型: 0=会员, 1=金币, 2=订阅")
    private String payType;

    /** 金币数量 | 会员时长(单位/天) */
    @Excel(name = "金币数量 | 会员时长(单位/天)")
    private Long orderNumber;

    /** 赠送金币数量 | 赠送会员时长(单位/天) */
    @Excel(name = "赠送金币数量 | 赠送会员时长(单位/天)")
    private Long giftOrderNumber;

    /** 推广链接ID */
    @Excel(name = "推广链接ID")
    private String tfid;

    /** sid */
    @Excel(name = "sid")
    private String sid;

    /** 充值时观看的内容ID */
    @Excel(name = "充值时观看的内容ID")
    private String contentId;

    /** 充值所在集数 */
    @Excel(name = "充值所在集数")
    private Long seriesIndex;

    /** 支付商户 */
    @Excel(name = "支付商户")
    private String payMchId;

    /** 使用哪个充值模板id */
    @Excel(name = "使用哪个充值模板id")
    private String feeItemId;

    /** 使用哪个优惠模板id */
    @Excel(name = "使用哪个优惠模板id")
    private String discountItemId;

    /** 上报状态，0-未上报，1-已上报 */
    @Excel(name = "上报状态，0-未上报，1-已上报")
    private Long pushType;

    /** 上报结果 */
    @Excel(name = "上报结果")
    private String pushResult;

    /** 广告计划id */
    @Excel(name = "广告计划id")
    private String adPlanId;

    /** 广告ID */
    @Excel(name = "广告ID")
    private String adId;

    /** 订单刷新类型 0 未更新过   1 手动更新   2 定时任务更新 */
    @Excel(name = "订单刷新类型 0 未更新过   1 手动更新   2 定时任务更新")
    private Long refreshOrderType;

    /** 用户设备类型, web, android, ios */
    @Excel(name = "用户设备类型, web, android, ios")
    private String deviceType;

    /** 投放用户 */
    @Excel(name = "投放用户")
    private String deliverUsername;

    /** 扩展信息 */
    @Excel(name = "扩展信息")
    private JSONObject extendJson;



    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("orderNo", getOrderNo())
            .append("userId", getUserId())
            .append("appid", getAppid())
            .append("orderChannel", getOrderChannel())
            .append("orderStatus", getOrderStatus())
            .append("orderAmount", getOrderAmount())
            .append("payTime", getPayTime())
            .append("payType", getPayType())
            .append("orderNumber", getOrderNumber())
            .append("giftOrderNumber", getGiftOrderNumber())
            .append("tfid", getTfid())
            .append("contentId", getContentId())
            .append("seriesIndex", getSeriesIndex())
            .append("payMchId", getPayMchId())
            .append("feeItemId", getFeeItemId())
            .append("pushType", getPushType())
            .append("pushResult", getPushResult())
            .append("adPlanId", getAdPlanId())
            .append("adId", getAdId())
            .append("refreshOrderType", getRefreshOrderType())
            .append("deviceType", getDeviceType())
            .append("extendJson", getExtendJson())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
