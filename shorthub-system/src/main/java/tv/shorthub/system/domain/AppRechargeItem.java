package tv.shorthub.system.domain;

import java.math.BigDecimal;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import tv.shorthub.common.annotation.Excel;
import lombok.Data;
import tv.shorthub.common.core.domain.BaseEntity;

/**
 * 充值方案对象 app_recharge_item
 *
 * <AUTHOR>
 * @date 2025-05-10
 */
@Data
public class AppRechargeItem extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 来源 */
    @Excel(name = "来源")
    private String appid;

    /** 方案ID */
    @Excel(name = "方案ID")
    private String templateId;

    /** 模板ID */
    @Excel(name = "模板ID")
    private String itemId;

    /** 父类模板ID */
    private String parentItemId;

    /** 针对的用户类型, 0=未充值用户, 1=已充值用户 */
    @Excel(name = "针对的用户类型, 0=未充值用户, 1=已充值用户")
    private String userType;

    /** 金额, 统一美金计价 */
    @Excel(name = "金额, 统一美金计价")
    private BigDecimal price;

    /** 金币/天数 */
    @Excel(name = "金币/天数")
    private Long number;

    /** 会员等级 */
    @Excel(name = "会员等级")
    private String memberLevel;

    /** 赠送金币/天数 */
    @Excel(name = "赠送金币/天数")
    private Long bonusNumber;

    /** 金币/天数 */
    @Excel(name = "赠送比例")
    private Long bonusPercent;

    /** 配置说明 */
    @Excel(name = "配置说明")
    private String remake;

    /** 0是会员，1是金币充值类型 */
    @Excel(name = "0是会员，1是金币充值类型, 2是订阅")
    private Long type;

    /** 订阅时长 */
    private String subscriptionPeriod;

    /** 订阅多少个周期 */
    @Excel(name = "订阅多少个周期")
    private Long subscriptionPeriodNumber;

    /** 订阅续费模板 */
    private String subscriptionNext;

    /** 订阅续费金额，未配置续费模板时生效 */
    private BigDecimal subscriptionNextAmount;

    /** 显示优惠样式 */
    @Excel(name = "显示优惠样式")
    private Boolean showProm;

    /** 背景色 */
    @Excel(name = "背景色")
    private String backcolor;

    /** 文字背景色 */
    @Excel(name = "文字背景色")
    private String textBackcolor;

    /** 文字颜色 */
    @Excel(name = "文字颜色")
    private String textColor;

    /** 边框颜色 */
    @Excel(name = "边框颜色")
    private String borderColor;

    /** 背景图片地址 */
    @Excel(name = "背景图片地址")
    private String backgroundImage;

    /** 是否显示右上角标签 */
    @Excel(name = "是否显示右上角标签")
    private Long showTag;

    /** 右上角标签背景色 */
    @Excel(name = "右上角标签背景色")
    private String tagBackColour;

    /** 右上角标签文字 */
    @Excel(name = "右上角标签文字")
    private String tagText;

    /** 右上角标签文字颜色 */
    @Excel(name = "右上角标签文字颜色")
    private String tagTextColour;

    /** 是否显示小手图标 */
    @Excel(name = "是否显示小手图标")
    private Long showHandTab;

    /** 底部文字颜色 */
    @Excel(name = "底部文字颜色")
    private String bottomTextColour;

    /** 底部背景颜色 */
    @Excel(name = "底部背景颜色")
    private String bottomBackColour;

    /** 展示顺序 */
    @Excel(name = "展示顺序")
    private Long sort;

    /** 热门 */
    @Excel(name = "热门")
    private Boolean hot;

    /** 激活 */
    @Excel(name = "激活")
    private Boolean active;

    /** 启用状态, 1=启用 */
    @Excel(name = "启用状态, 1=启用")
    private Boolean enable;



    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("appid", getAppid())
            .append("templateId", getTemplateId())
            .append("itemId", getItemId())
            .append("userType", getUserType())
            .append("price", getPrice())
            .append("number", getNumber())
            .append("remake", getRemake())
            .append("type", getType())
            .append("showProm", getShowProm())
            .append("backcolor", getBackcolor())
            .append("textBackcolor", getTextBackcolor())
            .append("textColor", getTextColor())
            .append("borderColor", getBorderColor())
            .append("backgroundImage", getBackgroundImage())
            .append("showTag", getShowTag())
            .append("tagBackColour", getTagBackColour())
            .append("tagText", getTagText())
            .append("tagTextColour", getTagTextColour())
            .append("showHandTab", getShowHandTab())
            .append("bottomTextColour", getBottomTextColour())
            .append("bottomBackColour", getBottomBackColour())
            .append("sort", getSort())
            .append("enable", getEnable())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
