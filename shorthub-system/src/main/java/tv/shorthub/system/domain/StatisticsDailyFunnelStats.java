package tv.shorthub.system.domain;

import java.util.Date;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import tv.shorthub.common.annotation.Excel;
import lombok.Data;
import tv.shorthub.common.core.domain.BaseEntity;

/**
 * 每日漏斗分析统计对象 statistics_daily_funnel_stats
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
@Data
public class StatisticsDailyFunnelStats extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 统计日期（yyyy-MM-dd） */
    @Excel(name = "统计日期", readConverterExp = "y=yyy-MM-dd")
    private Date statDate;

    /** 应用ID */
    @Excel(name = "应用ID")
    private String appid;

    /** 推广链接ID */
    @Excel(name = "推广链接ID")
    private String tfid;

    /** 访问人数 */
    @Excel(name = "访问人数")
    private Integer uvCount;

    /** 观看人数 */
    @Excel(name = "观看人数")
    private Integer watchCount;

    /** 看到付费集人数 */
    @Excel(name = "看到付费集人数")
    private Integer paidEpisodeCount;

    /** 下单人数 */
    @Excel(name = "下单人数")
    private Integer orderUserCount;

    /** 支付人数 */
    @Excel(name = "支付人数")
    private Integer paidUserCount;

    /** 时区 */
    @Excel(name = "时区")
    private String timezone;

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("statDate", getStatDate())
            .append("appid", getAppid())
            .append("tfid", getTfid())
            .append("uvCount", getUvCount())
            .append("watchCount", getWatchCount())
            .append("paidEpisodeCount", getPaidEpisodeCount())
            .append("orderUserCount", getOrderUserCount())
            .append("paidUserCount", getPaidUserCount())
            .append("timezone", getTimezone())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
