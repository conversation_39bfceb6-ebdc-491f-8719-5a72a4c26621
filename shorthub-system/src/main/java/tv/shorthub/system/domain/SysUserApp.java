package tv.shorthub.system.domain;

import com.alibaba.fastjson2.JSONObject;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import tv.shorthub.common.annotation.Excel;
import lombok.Data;
import tv.shorthub.common.core.domain.BaseEntity;

/**
 * 系统用户app授权对象 sys_user_app
 * 
 * <AUTHOR>
 * @date 2025-07-21
 */
@Data
public class SysUserApp extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Long id;

    /** user_name */
    @Excel(name = "user_name")
    private String userName;

    /** appname */
    @Excel(name = "appname")
    private String appid;

    /** 扩展字段 */
    @Excel(name = "扩展字段")
    private JSONObject extendJson;



    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("userName", getUserName())
            .append("appid", getAppid())
            .append("extendJson", getExtendJson())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
