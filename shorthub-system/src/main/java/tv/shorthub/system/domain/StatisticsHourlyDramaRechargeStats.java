package tv.shorthub.system.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import tv.shorthub.common.annotation.Excel;
import lombok.Data;
import tv.shorthub.common.core.domain.BaseEntity;

/**
 * 小时级剧目充值统计对象 statistics_hourly_drama_recharge_stats
 * 
 * <AUTHOR>
 * @date 2025-07-15
 */
@Data
public class StatisticsHourlyDramaRechargeStats extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 统计小时(如: 2025-07-15 10:00:00) */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "统计小时(如: 2025-07-15 10:00:00)", width = 60, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date statHour;

    /** 剧目内容ID */
    @Excel(name = "剧目内容ID")
    private String contentId;

    /** 剧目ID */
    @Excel(name = "剧目ID")
    private String dramaId;

    /** 应用ID */
    @Excel(name = "应用ID")
    private String appid;

    /** 推广链接ID */
    @Excel(name = "推广链接ID")
    private String tfid;

    /** 充值用户数 */
    @Excel(name = "充值用户数")
    private Long rechargeUserCount;

    /** 充值订单数 */
    @Excel(name = "充值订单数")
    private Long rechargeOrderCount;

    /** 充值总金额(USD) */
    @Excel(name = "充值总金额(USD)")
    private BigDecimal rechargeAmount;

    /** 金币充值次数 */
    @Excel(name = "金币充值次数")
    private Long coinRechargeCount;

    /** 金币充值金额 */
    @Excel(name = "金币充值金额")
    private BigDecimal coinRechargeAmount;

    /** 订阅充值次数 */
    @Excel(name = "订阅充值次数")
    private Long subscriptionRechargeCount;

    /** 订阅充值金额 */
    @Excel(name = "订阅充值金额")
    private BigDecimal subscriptionRechargeAmount;

    /** 消费用户数 */
    @Excel(name = "消费用户数")
    private Long consumptionUserCount;

    /** 消费订单数 */
    @Excel(name = "消费订单数")
    private Long consumptionOrderCount;

    /** 金币消费数量 */
    @Excel(name = "金币消费数量")
    private BigDecimal coinConsumptionAmount;

    /** 解锁剧集数 */
    @Excel(name = "解锁剧集数")
    private Long unlockEpisodeCount;

    /** 充值到消费转化率 */
    @Excel(name = "充值到消费转化率")
    private BigDecimal rechargeToConsumptionRate;

    /** 平均充值金额 */
    @Excel(name = "平均充值金额")
    private BigDecimal avgRechargeAmount;

    /** 平均消费金币 */
    @Excel(name = "平均消费金币")
    private BigDecimal avgConsumptionAmount;



    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("statHour", getStatHour())
            .append("contentId", getContentId())
            .append("dramaId", getDramaId())
            .append("appid", getAppid())
            .append("tfid", getTfid())
            .append("rechargeUserCount", getRechargeUserCount())
            .append("rechargeOrderCount", getRechargeOrderCount())
            .append("rechargeAmount", getRechargeAmount())
            .append("coinRechargeCount", getCoinRechargeCount())
            .append("coinRechargeAmount", getCoinRechargeAmount())
            .append("subscriptionRechargeCount", getSubscriptionRechargeCount())
            .append("subscriptionRechargeAmount", getSubscriptionRechargeAmount())
            .append("consumptionUserCount", getConsumptionUserCount())
            .append("consumptionOrderCount", getConsumptionOrderCount())
            .append("coinConsumptionAmount", getCoinConsumptionAmount())
            .append("unlockEpisodeCount", getUnlockEpisodeCount())
            .append("rechargeToConsumptionRate", getRechargeToConsumptionRate())
            .append("avgRechargeAmount", getAvgRechargeAmount())
            .append("avgConsumptionAmount", getAvgConsumptionAmount())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
