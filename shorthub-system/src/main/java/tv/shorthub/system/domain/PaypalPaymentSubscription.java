package tv.shorthub.system.domain;

import com.alibaba.fastjson2.JSONObject;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import tv.shorthub.common.annotation.Excel;
import lombok.Data;
import tv.shorthub.common.core.domain.BaseEntity;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.baomidou.mybatisplus.annotation.TableField;

/**
 * paypal订阅履行对象 paypal_payment_subscription
 *
 * <AUTHOR>
 * @date 2025-06-06
 */
@Data
public class PaypalPaymentSubscription extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /**  */
    private Long id;

    /** 本地订单号 */
    @Excel(name = "本地订单号")
    private String orderNo;

    /** 关联用户ID */
    @Excel(name = "关联用户ID")
    private String userId;

    /** PayPal支付ID */
    @Excel(name = "PayPal支付ID")
    private String paymentId;

    /** 订阅轮次 */
    @Excel(name = "订阅轮次")
    private Long sequence;

    /** 订阅状态, 0=未订阅, 1=已订阅,未履行, 2=已履行 */
    @Excel(name = "订阅状态, 0=未订阅, 1=已订阅,未履行, 2=已履行")
    private String state;

    /** 订阅参数 */
    @Excel(name = "订阅参数")
    private JSONObject cycleExecution;

    /**
     * 本次订阅执行的时间
     */
    @Excel(name = "本次订阅执行的时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("execution_time")
    private Date executionTime;

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("orderNo", getOrderNo())
            .append("userId", getUserId())
            .append("paymentId", getPaymentId())
            .append("sequence", getSequence())
            .append("state", getState())
            .append("cycleExecution", getCycleExecution())
            .append("executionTime", getExecutionTime())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
