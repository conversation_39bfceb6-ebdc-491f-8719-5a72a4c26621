package tv.shorthub.system.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import tv.shorthub.common.annotation.Excel;
import lombok.Data;
import tv.shorthub.common.core.domain.BaseEntity;

/**
 * airwallex支付配置对象 airwallex_payment_config
 * 
 * <AUTHOR>
 * @date 2025-07-18
 */
@Data
public class AirwallexPaymentConfig extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** client_id */
    @Excel(name = "client_id")
    private String clientId;

    /** api_key */
    @Excel(name = "api_key")
    private String apiKey;

    /** version */
    @Excel(name = "version")
    private String version;

    /** 沙盒模式 */
    @Excel(name = "沙盒模式")
    private Boolean sandbox;

    /** webhookId */
    @Excel(name = "webhookId")
    private String webhookId;

    /** 是否启用 */
    @Excel(name = "是否启用")
    private Boolean enable;



    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("clientId", getClientId())
            .append("apiKey", getApiKey())
            .append("version", getVersion())
            .append("sandbox", getSandbox())
            .append("webhookId", getWebhookId())
            .append("enable", getEnable())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
