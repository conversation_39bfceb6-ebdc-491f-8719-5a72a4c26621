package tv.shorthub.system.domain;

import java.math.BigDecimal;
import com.alibaba.fastjson2.JSONObject;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import tv.shorthub.common.annotation.Excel;
import lombok.Data;
import tv.shorthub.common.core.domain.BaseEntity;

/**
 * paypal原始订单对象 paypal_payment_log
 *
 * <AUTHOR>
 * @date 2025-05-15
 */
@Data
public class PaypalPaymentLog extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 关联的本地订单号 */
    @Excel(name = "关联的本地订单号")
    private String orderNo;

    /** PayPal支付ID */
    @Excel(name = "PayPal支付ID")
    private String paymentId;

    /** PayPal支付ID */
    @Excel(name = "PayPal clientId")
    private String clientId;

    /** PayPal用户ID */
    @Excel(name = "PayPal用户ID")
    private String payerId;

    /** PayPal订单状态 */
    @Excel(name = "PayPal订单状态")
    private String state;

    /** 支付金额 */
    @Excel(name = "支付金额")
    private BigDecimal totalAmount;

    /** 币种 */
    @Excel(name = "币种")
    private String currency;

    /** 商品描述 */
    @Excel(name = "商品描述")
    private String description;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String returnUrl;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String cancelUrl;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String paymentMethod;

    /** 完整的PayPal订单JSON */
    @Excel(name = "完整的PayPal订单JSON")
    private JSONObject rawData;

    /** 关联用户ID */
    @Excel(name = "关联用户ID")
    private String relatedUserId;

    /** 查询状态 */
    @Excel(name = "查询状态")
    private String queryState;

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("orderNo", getOrderNo())
            .append("paymentId", getPaymentId())
            .append("payerId", getPayerId())
            .append("state", getState())
            .append("totalAmount", getTotalAmount())
            .append("currency", getCurrency())
            .append("description", getDescription())
            .append("returnUrl", getReturnUrl())
            .append("cancelUrl", getCancelUrl())
            .append("paymentMethod", getPaymentMethod())
            .append("rawData", getRawData())
            .append("relatedUserId", getRelatedUserId())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
