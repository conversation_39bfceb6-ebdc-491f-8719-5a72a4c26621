package tv.shorthub.system.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import tv.shorthub.common.annotation.Excel;
import lombok.Data;
import tv.shorthub.common.core.domain.BaseEntity;

/**
 * 充值优惠对象 app_recharge_item_gift
 * 
 * <AUTHOR>
 * @date 2025-05-10
 */
@Data
public class AppRechargeItemGift extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 来源 */
    @Excel(name = "来源")
    private String appid;

    /** 模板ID */
    @Excel(name = "模板ID")
    private String itemId;

    /** 优惠金币/天数 */
    @Excel(name = "优惠金币/天数")
    private Long giftNumber;

    /** 优惠说明 */
    @Excel(name = "优惠说明")
    private String giftRemark;

    /** 角标文案 */
    @Excel(name = "角标文案")
    private String giftText;

    /** 启用状态, 1=启用 */
    @Excel(name = "启用状态, 1=启用")
    private Boolean enable;



    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("appid", getAppid())
            .append("itemId", getItemId())
            .append("giftNumber", getGiftNumber())
            .append("giftRemark", getGiftRemark())
            .append("giftText", getGiftText())
            .append("enable", getEnable())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
