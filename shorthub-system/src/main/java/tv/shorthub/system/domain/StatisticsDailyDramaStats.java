package tv.shorthub.system.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import tv.shorthub.common.annotation.Excel;
import lombok.Data;
import tv.shorthub.common.core.domain.BaseEntity;

/**
 * 每日剧集分析统计对象 statistics_daily_drama_stats
 * 
 * <AUTHOR>
 * @date 2025-07-22
 */
@Data
public class StatisticsDailyDramaStats extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 自增主键 */
    private Long id;

    /** 统计日期（yyyy-MM-dd） */
    @Excel(name = "统计日期", readConverterExp = "y=yyy-MM-dd")
    private Date statDate;

    /** 应用ID */
    @Excel(name = "应用ID")
    private String appid;

    /** 剧集内容ID */
    @Excel(name = "剧集内容ID")
    private String contentId;

    /** 推广链接ID (空字符串表示自然流量) */
    @Excel(name = "推广链接ID (空字符串表示自然流量)")
    private String tfid;

    /** 播放量 (Video Views) */
    @Excel(name = "播放量 (Video Views)")
    private Long vvCount;

    /** 独立观看用户数 (Unique Visitors) */
    @Excel(name = "独立观看用户数 (Unique Visitors)")
    private Long uvCount;

    /** 总观看时长（秒） */
    @Excel(name = "总观看时长", readConverterExp = "秒=")
    private Long totalWatchSeconds;

    /** 付费解锁次数 */
    @Excel(name = "付费解锁次数")
    private Long paidUnlockCount;

    /** 会员解锁次数 */
    @Excel(name = "会员解锁次数")
    private Long memberUnlockCount;

    /** 总消费金币 */
    @Excel(name = "总消费金币")
    private Long totalCoinSpent;

    /** 时区，如UTC+8、UTC-8等 */
    @Excel(name = "时区，如UTC+8、UTC-8等")
    private String timezone;



    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("statDate", getStatDate())
            .append("appid", getAppid())
            .append("contentId", getContentId())
            .append("tfid", getTfid())
            .append("vvCount", getVvCount())
            .append("uvCount", getUvCount())
            .append("totalWatchSeconds", getTotalWatchSeconds())
            .append("paidUnlockCount", getPaidUnlockCount())
            .append("memberUnlockCount", getMemberUnlockCount())
            .append("totalCoinSpent", getTotalCoinSpent())
            .append("timezone", getTimezone())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
