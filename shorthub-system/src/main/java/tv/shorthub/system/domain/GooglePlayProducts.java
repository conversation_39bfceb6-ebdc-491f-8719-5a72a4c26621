package tv.shorthub.system.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import tv.shorthub.common.annotation.Excel;
import lombok.Data;
import tv.shorthub.common.core.domain.BaseEntity;

/**
 * Google Play产品基础信息对象 google_play_products
 * 
 * <AUTHOR>
 * @date 2025-07-01
 */
@Data
public class GooglePlayProducts extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** Google Play产品ID */
    @Excel(name = "Google Play产品ID")
    private String productId;

    /** 应用包名 */
    @Excel(name = "应用包名")
    private String packageName;

    /** 产品类型：inapp=一次性购买，subs=订阅 */
    @Excel(name = "产品类型：inapp=一次性购买，subs=订阅")
    private String productType;

    /** 产品状态 */
    @Excel(name = "产品状态")
    private String status;



    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("productId", getProductId())
            .append("packageName", getPackageName())
            .append("productType", getProductType())
            .append("status", getStatus())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .append("createBy", getCreateBy())
            .append("updateBy", getUpdateBy())
            .toString();
    }
}
