package tv.shorthub.system.domain;

import com.alibaba.fastjson2.JSONObject;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import tv.shorthub.common.annotation.Excel;
import lombok.Data;
import tv.shorthub.common.core.domain.BaseEntity;

/**
 * paypal配置对象 paypal_payment_config
 *
 * <AUTHOR>
 * @date 2025-05-22
 */
@Data
public class PaypalPaymentConfig extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** client_id */
    @Excel(name = "client_id")
    private String clientId;

    /** client_secret */
    @Excel(name = "client_secret")
    private String clientSecret;

    /** webhookId */
    @Excel(name = "webhookId")
    private String webhookId;

    /** live | sandbox */
    @Excel(name = "live | sandbox")
    private String mode;

    /** 是否启用 */
    @Excel(name = "是否启用")
    private Boolean enable;

    /** 扩展字段 */
    @Excel(name = "扩展字段")
    private JSONObject extendJson;



    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("clientId", getClientId())
            .append("clientSecret", getClientSecret())
            .append("mode", getMode())
            .append("enable", getEnable())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
