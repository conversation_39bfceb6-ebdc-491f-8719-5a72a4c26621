package tv.shorthub.system.domain;

import com.alibaba.fastjson2.JSONObject;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import tv.shorthub.common.annotation.Excel;
import lombok.Data;
import tv.shorthub.common.core.domain.BaseEntity;

/**
 * airwallex用户扣费计划对象 airwallex_user_plan
 * 
 * <AUTHOR>
 * @date 2025-07-18
 */
@Data
public class AirwallexUserPlan extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 本地订单号 */
    @Excel(name = "本地订单号")
    private String orderNo;

    /** 支付ID */
    @Excel(name = "支付ID")
    private String paymentId;

    /** 关键参数 */
    @Excel(name = "关键参数")
    private String paymentConsentId;

    /** 客户ID */
    @Excel(name = "客户ID")
    private String consumerId;

    /** 续费周期 */
    @Excel(name = "续费周期")
    private String period;

    /** 启用状态 */
    @Excel(name = "启用状态")
    private Boolean enabled;

    /** 扩展字段 */
    @Excel(name = "扩展字段")
    private JSONObject extendData;



    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("orderNo", getOrderNo())
            .append("paymentId", getPaymentId())
            .append("paymentConsentId", getPaymentConsentId())
            .append("period", getPeriod())
            .append("enabled", getEnabled())
            .append("extendData", getExtendData())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
