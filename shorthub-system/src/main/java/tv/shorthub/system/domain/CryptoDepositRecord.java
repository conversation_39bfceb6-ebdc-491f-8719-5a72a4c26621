package tv.shorthub.system.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import tv.shorthub.common.annotation.Excel;
import lombok.Data;
import tv.shorthub.common.core.domain.BaseEntity;

/**
 * 虚拟货币充值记录对象 crypto_deposit_record
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
@Data
public class CryptoDepositRecord extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 本地订单号 */
    @Excel(name = "本地订单号")
    private String orderNo;

    /** 用户ID */
    @Excel(name = "用户ID")
    private String userId;

    /** 交易哈希 */
    @Excel(name = "交易哈希")
    private String txHash;

    /** 区块链类型(tron,base,solana) */
    @Excel(name = "区块链类型(tron,base,solana)")
    private String blockchain;

    /** 代币类型(USDT,USDC) */
    @Excel(name = "代币类型(USDT,USDC)")
    private String tokenType;

    /** 代币合约地址 */
    @Excel(name = "代币合约地址")
    private String contractAddress;

    /** 发送方地址 */
    @Excel(name = "发送方地址")
    private String fromAddress;

    /** 接收方地址 */
    @Excel(name = "接收方地址")
    private String toAddress;

    /** 充值金额 */
    @Excel(name = "充值金额")
    private BigDecimal amount;

    /** 原始金额(包含小数位) */
    @Excel(name = "原始金额(包含小数位)")
    private String rawAmount;

    /** 交易状态(pending,confirmed,failed,timeout) */
    @Excel(name = "交易状态(pending,confirmed,failed,timeout)")
    private String status;

    /** 区块高度 */
    @Excel(name = "区块高度")
    private Long blockHeight;

    /** 当前确认数 */
    @Excel(name = "当前确认数")
    private Long confirmations;

    /** 需要确认数 */
    @Excel(name = "需要确认数")
    private Long requiredConfirmations;

    /** 交易时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "交易时间", width = 60, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date transactionTime;

    /** 确认时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "确认时间", width = 60, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date confirmedTime;

    /** 充值时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "充值时间", width = 60, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date rechargeTime;

    /** 处理状态(0-未处理,1-已处理,2-处理失败) */
    @Excel(name = "处理状态(0-未处理,1-已处理,2-处理失败)")
    private Integer processStatus;

    /** 处理时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "处理时间", width = 60, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date processTime;

    /** 失败原因 */
    @Excel(name = "失败原因")
    private String failureReason;

    /** 重试次数 */
    @Excel(name = "重试次数")
    private Long retryCount;

    /** Gas费用 */
    @Excel(name = "Gas费用")
    private BigDecimal gasFee;

    /** 交易原始数据 */
    @Excel(name = "交易原始数据")
    private JSONObject rawData;



    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("orderNo", getOrderNo())
            .append("userId", getUserId())
            .append("txHash", getTxHash())
            .append("blockchain", getBlockchain())
            .append("tokenType", getTokenType())
            .append("contractAddress", getContractAddress())
            .append("fromAddress", getFromAddress())
            .append("toAddress", getToAddress())
            .append("amount", getAmount())
            .append("rawAmount", getRawAmount())
            .append("status", getStatus())
            .append("blockHeight", getBlockHeight())
            .append("confirmations", getConfirmations())
            .append("requiredConfirmations", getRequiredConfirmations())
            .append("transactionTime", getTransactionTime())
            .append("confirmedTime", getConfirmedTime())
            .append("processStatus", getProcessStatus())
            .append("processTime", getProcessTime())
            .append("failureReason", getFailureReason())
            .append("retryCount", getRetryCount())
            .append("gasFee", getGasFee())
            .append("rawData", getRawData())
            .append("remark", getRemark())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
