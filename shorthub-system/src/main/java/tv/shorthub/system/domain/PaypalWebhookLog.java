package tv.shorthub.system.domain;

import com.alibaba.fastjson2.JSONObject;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import tv.shorthub.common.annotation.Excel;
import lombok.Data;
import tv.shorthub.common.core.domain.BaseEntity;

/**
 * webhook事件日志对象 paypal_webhook_log
 *
 * <AUTHOR>
 * @date 2025-05-27
 */
@Data
public class PaypalWebhookLog extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 关联的配置ID */
    @Excel(name = "关联的配置ID")
    private Long pid;

    /** transmission_id */
    @Excel(name = "transmission_id")
    private String transmissionId;

    /** transmission_time */
    @Excel(name = "transmission_time")
    private String transmissionTime;

    /** cert_url */
    @Excel(name = "cert_url")
    private String certUrl;

    /** transmission_sig */
    @Excel(name = "transmission_sig")
    private String transmissionSig;

    /** 处理状态 */
    @Excel(name = "处理状态")
    private String state;

    /** 事件报文 */
    @Excel(name = "事件报文")
    private String eventData;



    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("pid", getPid())
            .append("transmissionId", getTransmissionId())
            .append("transmissionTime", getTransmissionTime())
            .append("certUrl", getCertUrl())
            .append("transmissionSig", getTransmissionSig())
            .append("state", getState())
            .append("eventData", getEventData())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
