package tv.shorthub.system.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import tv.shorthub.common.annotation.Excel;
import lombok.Data;
import tv.shorthub.common.core.domain.BaseEntity;

/**
 * 充值方案对象 app_recharge
 *
 * <AUTHOR>
 * @date 2025-05-10
 */
@Data
public class AppRecharge extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 所属app */
    @Excel(name = "所属app")
    private String appid;

    /** 充值方案ID */
    @Excel(name = "充值方案ID")
    private String templateId;

    /** 充值方案名称 */
    @Excel(name = "充值方案名称")
    private String templateName;

    /** 模板说明 */
    @Excel(name = "模板说明")
    private String remake;

    /** 展示顺序 */
    @Excel(name = "展示顺序")
    private Long sort;

    /** 是否启用 */
    @Excel(name = "是否启用")
    private Boolean enabled;

    /** 展示金币总和 */
    @Excel(name = "展示金币总和")
    private Boolean compute;

    /** 必须先登录 */
    @Excel(name = "必须先登录")
    private Boolean mustLogin;

    /** 默认方案 */
    @Excel(name = "默认方案")
    private Boolean isDefault;



    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("appid", getAppid())
            .append("templateId", getTemplateId())
            .append("templateName", getTemplateName())
            .append("remake", getRemake())
            .append("sort", getSort())
            .append("enabled", getEnabled())
            .append("isDefault", getIsDefault())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
