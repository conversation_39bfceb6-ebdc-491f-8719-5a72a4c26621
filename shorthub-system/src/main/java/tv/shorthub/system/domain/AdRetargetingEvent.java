package tv.shorthub.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import tv.shorthub.common.annotation.Excel;
import lombok.Data;
import tv.shorthub.common.core.domain.BaseEntity;

/**
 * 回传事件对象 ad_retargeting_event
 * 
 * <AUTHOR>
 * @date 2025-05-23
 */
@Data
public class AdRetargetingEvent extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @TableId(type = IdType.AUTO)
    private Long id;

    /** 关联的策略ID */
    @Excel(name = "关联的策略ID")
    private Long strategyId;

    /** 事件名称 */
    @Excel(name = "事件名称")
    private String eventName;

    /** 事件类型 */
    @Excel(name = "事件类型")
    private String eventType;

    /** 事件参数(JSON格式) */
    @Excel(name = "事件参数(JSON格式)")
    private String eventParams;

    /** 状态：0-禁用 1-启用 */
    @Excel(name = "状态：0-禁用 1-启用")
    private Boolean status;



    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("strategyId", getStrategyId())
            .append("eventName", getEventName())
            .append("eventType", getEventType())
            .append("eventParams", getEventParams())
            .append("status", getStatus())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .append("createBy", getCreateBy())
            .append("updateBy", getUpdateBy())
            .toString();
    }
}
