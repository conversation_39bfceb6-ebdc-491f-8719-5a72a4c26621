package tv.shorthub.system.domain;

import com.alibaba.fastjson2.JSONObject;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import tv.shorthub.common.annotation.Excel;
import lombok.Data;
import tv.shorthub.common.core.domain.BaseEntity;

/**
 * webhook事件日志对象 airwallex_webhook_log
 * 
 * <AUTHOR>
 * @date 2025-07-19
 */
@Data
public class AirwallexWebhookLog extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 关联的配置ID */
    @Excel(name = "关联的配置ID")
    private Long pid;

    /** 事件ID */
    @Excel(name = "事件ID")
    private String eventId;

    /** signature */
    @Excel(name = "signature")
    private String signature;

    /** timestamp */
    @Excel(name = "timestamp")
    private String timestamp;

    /** 事件报文 */
    @Excel(name = "事件报文")
    private JSONObject eventData;



    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("pid", getPid())
            .append("eventId", getEventId())
            .append("signature", getSignature())
            .append("timestamp", getTimestamp())
            .append("eventData", getEventData())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
