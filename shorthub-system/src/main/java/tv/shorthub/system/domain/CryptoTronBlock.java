package tv.shorthub.system.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import tv.shorthub.common.annotation.Excel;
import lombok.Data;
import tv.shorthub.common.core.domain.BaseEntity;

/**
 * TRON区块链区块对象 crypto_tron_block
 * 
 * <AUTHOR>
 * @date 2025-08-05
 */
@Data
public class CryptoTronBlock extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 区块哈希 */
    @Excel(name = "区块哈希")
    private String blockHash;

    /** 区块高度 */
    @Excel(name = "区块高度")
    private Long blockNumber;

    /** 父区块哈希 */
    @Excel(name = "父区块哈希")
    private String parentHash;

    /** 区块时间戳 */
    @Excel(name = "区块时间戳")
    private Long timestamp;

    /** 交易数量 */
    @Excel(name = "交易数量")
    private Long transactionsCount;

    /** 见证人地址 */
    @Excel(name = "见证人地址")
    private String witnessAddress;

    /** 见证人签名 */
    @Excel(name = "见证人签名")
    private String witnessSignature;

    /** 区块大小(字节) */
    @Excel(name = "区块大小(字节)")
    private Long size;

    /** Gas使用量 */
    @Excel(name = "Gas使用量")
    private Long gasUsed;

    /** Gas限制 */
    @Excel(name = "Gas限制")
    private Long gasLimit;

    /** 难度值 */
    @Excel(name = "难度值")
    private String difficulty;

    /** 随机数 */
    @Excel(name = "随机数")
    private String nonce;

    /** 默克尔根 */
    @Excel(name = "默克尔根")
    private String merkleRoot;

    /** 状态根 */
    @Excel(name = "状态根")
    private String stateRoot;

    /** 收据根 */
    @Excel(name = "收据根")
    private String receiptsRoot;

    /** 额外数据 */
    @Excel(name = "额外数据")
    private String extraData;

    /** 原始区块数据(JSON) */
    @Excel(name = "原始区块数据(JSON)")
    private String rawData;

    /** 完整区块内容(JSON) */
    @Excel(name = "完整区块内容(JSON)")
    private String fullContent;

    /** 是否已确认 */
    @Excel(name = "是否已确认")
    private Boolean isConfirmed;

    /** 确认数 */
    @Excel(name = "确认数")
    private Long confirmations;



    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("blockHash", getBlockHash())
            .append("blockNumber", getBlockNumber())
            .append("parentHash", getParentHash())
            .append("timestamp", getTimestamp())
            .append("transactionsCount", getTransactionsCount())
            .append("witnessAddress", getWitnessAddress())
            .append("witnessSignature", getWitnessSignature())
            .append("size", getSize())
            .append("gasUsed", getGasUsed())
            .append("gasLimit", getGasLimit())
            .append("difficulty", getDifficulty())
            .append("nonce", getNonce())
            .append("merkleRoot", getMerkleRoot())
            .append("stateRoot", getStateRoot())
            .append("receiptsRoot", getReceiptsRoot())
            .append("extraData", getExtraData())
            .append("rawData", getRawData())
            .append("fullContent", getFullContent())
            .append("isConfirmed", getIsConfirmed())
            .append("confirmations", getConfirmations())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
