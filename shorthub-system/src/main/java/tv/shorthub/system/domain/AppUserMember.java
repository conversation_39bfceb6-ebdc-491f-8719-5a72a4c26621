package tv.shorthub.system.domain;

import java.util.Date;

import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import tv.shorthub.common.annotation.Excel;
import lombok.Data;
import tv.shorthub.common.core.domain.BaseEntity;

/**
 * 用户会员开通记录对象 app_user_member
 * 
 * <AUTHOR>
 * @date 2025-05-15
 */
@Data
public class AppUserMember extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 会员id */
    @Excel(name = "会员id")
    private String memberId;

    /** 会员级别 */
    @Excel(name = "会员级别")
    private String memberLevel;

    /** 会员配置 */
    @Excel(name = "会员配置")
    private JSONObject levelConfig;

    /** 关联用户 */
    @Excel(name = "关联用户")
    private String userId;

    /** 可解锁次数, -1=不限制 */
    @Excel(name = "可解锁次数, -1=不限制")
    private Long unlockNumber;

    /** 过期时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "过期时间", width = 60, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date expireTime;

    /** 开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "开始时间", width = 60, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date beginTime;



    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("memberId", getMemberId())
            .append("memberLevel", getMemberLevel())
            .append("userId", getUserId())
            .append("unlockNumber", getUnlockNumber())
            .append("expireTime", getExpireTime())
            .append("beginTime", getBeginTime())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
