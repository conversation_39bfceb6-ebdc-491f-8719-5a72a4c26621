package tv.shorthub.system.domain;

import java.math.BigDecimal;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import tv.shorthub.common.annotation.Excel;
import lombok.Data;
import tv.shorthub.common.core.domain.BaseEntity;

/**
 * 订阅优惠规则对象 app_recharge_subscription_gift
 * 
 * <AUTHOR>
 * @date 2025-05-14
 */
@Data
public class AppRechargeSubscriptionGift extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 来源 */
    @Excel(name = "来源")
    private String appid;

    /** 模板ID */
    @Excel(name = "模板ID")
    private String itemId;

    /** 从几个周期开始 */
    @Excel(name = "从几个周期开始")
    private Long giftPeriodBegin;

    /** 优惠几个周期 */
    @Excel(name = "优惠几个周期")
    private Long giftPeriodNumber;

    /** 优惠价格 */
    @Excel(name = "优惠价格")
    private BigDecimal giftPrice;

    /** 启用状态, 1=启用 */
    @Excel(name = "启用状态, 1=启用")
    private Boolean enable;



    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("appid", getAppid())
            .append("itemId", getItemId())
            .append("giftPeriodBegin", getGiftPeriodBegin())
            .append("giftPeriodNumber", getGiftPeriodNumber())
            .append("giftPrice", getGiftPrice())
            .append("enable", getEnable())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
