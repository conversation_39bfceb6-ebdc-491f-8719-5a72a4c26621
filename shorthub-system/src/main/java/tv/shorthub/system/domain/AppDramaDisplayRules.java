package tv.shorthub.system.domain;

import tv.shorthub.common.annotation.Excel;
import tv.shorthub.common.core.domain.BaseEntity;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 剧目显示规则对象 app_drama_display_rules
 *
 * <AUTHOR>
 * @date 2025-05-06
 */
@Data
public class AppDramaDisplayRules extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 剧目 */
    @Excel(name = "剧目")
    private String dramaId;

    /** 国家编码 */
    @Excel(name = "国家编码")
    private String countryCode;



    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("dramaId", getDramaId())
            .append("countryCode", getCountryCode())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
