package tv.shorthub.system.domain;

import java.math.BigDecimal;
import java.util.Date;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import tv.shorthub.common.annotation.Excel;
import lombok.Data;
import tv.shorthub.common.core.domain.BaseEntity;

/**
 * 每日订单统计对象 statistics_daily_order_stats
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
@Data
public class StatisticsDailyOrderStats extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 统计日期（yyyy-MM-dd） */
    @Excel(name = "统计日期", readConverterExp = "yyyy-MM-dd")
    private Date statDate;

    /** 应用ID */
    @Excel(name = "应用ID")
    private String appid;

    /** 推广链接ID */
    @Excel(name = "推广链接ID")
    private String tfid;

    /** 订单支付通道 */
    @Excel(name = "订单支付通道")
    private String orderChannel;

    /** 总订单用户数 */
    @Excel(name = "总订单用户数")
    private Long totalOrderCount;

    /** 支付用户数 */
    @Excel(name = "支付用户数")
    private Long paidOrderCount;

    /** 订单成功人数（按IP去重） */
    @Excel(name = "订单成功人数")
    private Long paidUserCount;

    /** 支付金额 */
    @Excel(name = "支付金额")
    private BigDecimal paidOrderAmount;

    /** 未支付用户数 */
    @Excel(name = "未支付用户数")
    private Long unpaidOrderCount;

    /** 金币未支付金额 */
    @Excel(name = "金币未支付金额")
    private BigDecimal unpaidCoinOrderAmount;

    /** 订阅未支付金额 */
    @Excel(name = "订阅未支付金额")
    private BigDecimal unpaidSubscriptionOrderAmount;

    /** 会员订单数 */
    @Excel(name = "会员订单数")
    private Long memberOrderCount;

    /** 会员订单金额 */
    @Excel(name = "会员订单金额")
    private BigDecimal memberOrderAmount;

    /** 金币订单数 */
    @Excel(name = "金币订单数")
    private Long coinOrderCount;

    /** 金币订单金额 */
    @Excel(name = "金币订单金额")
    private BigDecimal coinOrderAmount;

    /** 订阅订单数 */
    @Excel(name = "订阅订单数")
    private Long subscriptionOrderCount;

    /** 订阅订单金额 */
    @Excel(name = "订阅订单金额")
    private BigDecimal subscriptionOrderAmount;

    /** 时区 */
    @Excel(name = "时区")
    private String timezone;



    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("statDate", getStatDate())
            .append("appid", getAppid())
            .append("tfid", getTfid())
            .append("orderChannel", getOrderChannel())
            .append("totalOrderCount", getTotalOrderCount())
            .append("paidOrderCount", getPaidOrderCount())
            .append("paidOrderAmount", getPaidOrderAmount())
            .append("unpaidOrderCount", getUnpaidOrderCount())
            .append("unpaidCoinOrderAmount", getUnpaidCoinOrderAmount())
            .append("unpaidSubscriptionOrderAmount", getUnpaidSubscriptionOrderAmount())
            .append("memberOrderCount", getMemberOrderCount())
            .append("memberOrderAmount", getMemberOrderAmount())
            .append("coinOrderCount", getCoinOrderCount())
            .append("coinOrderAmount", getCoinOrderAmount())
            .append("subscriptionOrderCount", getSubscriptionOrderCount())
            .append("subscriptionOrderAmount", getSubscriptionOrderAmount())
            .append("paidUserCount", getPaidUserCount())
            .append("timezone", getTimezone())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
