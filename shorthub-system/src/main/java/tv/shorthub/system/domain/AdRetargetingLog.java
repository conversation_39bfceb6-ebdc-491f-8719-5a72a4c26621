package tv.shorthub.system.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import tv.shorthub.common.annotation.Excel;
import lombok.Data;
import tv.shorthub.common.core.domain.BaseEntity;

/**
 * 回传记录对象 ad_retargeting_log
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Data
public class AdRetargetingLog extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 关联的策略ID */
    @Excel(name = "关联的策略ID")
    private Long strategyId;

    /** 关联的事件ID */
    @Excel(name = "关联的事件ID")
    private Long eventId;

    /** 请求ID */
    @Excel(name = "请求ID")
    private String requestId;

    /** 用户ID */
    @Excel(name = "用户ID")
    private String userId;

    /** 关联ID */
    @Excel(name = "关联ID")
    private String relationId;

    /** 设备ID */
    @Excel(name = "设备ID")
    private String deviceId;

    /** 事件数据(JSON格式) */
    @Excel(name = "事件数据(JSON格式)")
    private String eventData;

    /** 回传数据(JSON格式) */
    @Excel(name = "回传数据(JSON格式)")
    private String callbackData;

    /** 结果数据(JSON格式) */
    @Excel(name = "结果数据(JSON格式)")
    private String responseData;

    /** 回传状态：0-失败 1-成功 */
    @Excel(name = "回传状态：0-失败 1-成功")
    private Boolean callbackStatus;

    /** 重试次数 */
    @Excel(name = "重试次数")
    private Long retryCount;

    /** 错误信息 */
    @Excel(name = "错误信息")
    private String errorMessage;



    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("strategyId", getStrategyId())
            .append("eventId", getEventId())
            .append("requestId", getRequestId())
            .append("userId", getUserId())
            .append("deviceId", getDeviceId())
            .append("eventData", getEventData())
            .append("callbackData", getCallbackData())
            .append("callbackStatus", getCallbackStatus())
            .append("retryCount", getRetryCount())
            .append("errorMessage", getErrorMessage())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .append("createBy", getCreateBy())
            .append("updateBy", getUpdateBy())
            .toString();
    }
}
