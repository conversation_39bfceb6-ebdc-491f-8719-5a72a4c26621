package tv.shorthub.system.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import tv.shorthub.common.annotation.Excel;
import lombok.Data;
import tv.shorthub.common.core.domain.BaseEntity;

/**
 * 金币消费对象 app_consumption
 * 
 * <AUTHOR>
 * @date 2025-05-10
 */
@Data
public class AppConsumption extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 自增id */
    private Long id;

    /** appid */
    @Excel(name = "appid")
    private String appid;

    /** 用户ID */
    @Excel(name = "用户ID")
    private String userId;

    /** 剧目ID */
    @Excel(name = "剧目ID")
    private String dramaId;

    /** 内容ID */
    @Excel(name = "内容ID")
    private String contentId;

    /** 消费金币 */
    @Excel(name = "消费金币")
    private Long amount;

    /** 消费赠送金币 */
    @Excel(name = "消费赠送金币")
    private Long giftAmount;

    /** 剧集ID */
    @Excel(name = "剧集ID")
    private String serialId;

    /** 消费类型 1充值，2，消费，3，任务 */
    @Excel(name = "消费类型 1充值，2，消费，3，任务")
    private Long consumptionType;

    /** 第几集 */
    @Excel(name = "第几集")
    private Long serialNumber;

    /** 备用字段 */
    @Excel(name = "备用字段")
    private String extendJson;



    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("appid", getAppid())
            .append("userId", getUserId())
            .append("dramaId", getDramaId())
            .append("contentId", getContentId())
            .append("amount", getAmount())
            .append("giftAmount", getGiftAmount())
            .append("serialId", getSerialId())
            .append("consumptionType", getConsumptionType())
            .append("serialNumber", getSerialNumber())
            .append("extendJson", getExtendJson())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
