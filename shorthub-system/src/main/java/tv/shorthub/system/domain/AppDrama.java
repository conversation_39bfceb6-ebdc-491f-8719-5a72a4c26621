package tv.shorthub.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import tv.shorthub.common.annotation.Excel;
import tv.shorthub.common.core.domain.BaseEntity;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 剧目对象 app_drama
 * 
 * <AUTHOR>
 * @date 2025-05-06
 */
@Data
public class AppDrama extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @TableId(type = IdType.AUTO)
    private Long id;

    /** 剧目id */
    @Excel(name = "剧目id")
    private String dramaId;

    /** 标题 */
    @Excel(name = "标题")
    private String title;

    /** 安全剧 */
    @Excel(name = "安全剧")
    private Boolean safety;

    /** 启用 */
    @Excel(name = "启用")
    private Boolean enable;

    /** 发布日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "发布日期", width = 60, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date releaseDate;

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("title", getTitle())
            .append("releaseDate", getReleaseDate())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
