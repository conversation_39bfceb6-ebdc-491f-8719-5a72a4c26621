package tv.shorthub.system.domain;

import com.alibaba.fastjson2.JSONObject;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import tv.shorthub.common.annotation.Excel;
import lombok.Data;
import tv.shorthub.common.core.domain.BaseEntity;

/**
 * 客户端配置对象 app_config
 * 
 * <AUTHOR>
 * @date 2025-07-21
 */
@Data
public class AppConfig extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Long id;

    /** appid */
    @Excel(name = "appid")
    private String appid;

    /** appname */
    @Excel(name = "appname")
    private String appname;

    /** 渠道, Web, Android, iOS */
    @Excel(name = "渠道, Web, Android, iOS")
    private String channel;

    /** 默认语言 */
    @Excel(name = "默认语言")
    private String language;

    /** 扩展字段 */
    @Excel(name = "扩展字段")
    private JSONObject extendJson;



    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("appid", getAppid())
            .append("appname", getAppname())
            .append("channel", getChannel())
            .append("language", getLanguage())
            .append("extendJson", getExtendJson())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
