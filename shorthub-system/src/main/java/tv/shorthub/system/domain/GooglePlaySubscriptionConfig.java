package tv.shorthub.system.domain;

import java.math.BigDecimal;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import tv.shorthub.common.annotation.Excel;
import lombok.Data;
import tv.shorthub.common.core.domain.BaseEntity;

/**
 * Google Play订阅产品配置对象 google_play_subscription_config
 * 
 * <AUTHOR>
 * @date 2025-07-01
 */
@Data
public class GooglePlaySubscriptionConfig extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 产品ID */
    @Excel(name = "产品ID")
    private String itemId;

    /** 基础计划ID */
    @Excel(name = "基础计划ID")
    private String basePlanId;

    /** 订阅ID */
    @Excel(name = "订阅ID")
    private String subscriptionId;

    /** 订阅周期(如：P1M, P1Y) */
    @Excel(name = "订阅周期(如：P1M, P1Y)")
    private String subscriptionPeriod;

    /** 免费试用期(如：P7D) */
    @Excel(name = "免费试用期(如：P7D)")
    private String freeTrialPeriod;

    /** 介绍价格期(如：P1M) */
    @Excel(name = "介绍价格期(如：P1M)")
    private String introductoryPricePeriod;

    /** 介绍价格金额 */
    @Excel(name = "介绍价格金额")
    private BigDecimal introductoryPriceAmount;



    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("basePlanId", getBasePlanId())
            .append("subscriptionPeriod", getSubscriptionPeriod())
            .append("freeTrialPeriod", getFreeTrialPeriod())
            .append("introductoryPricePeriod", getIntroductoryPricePeriod())
            .append("introductoryPriceAmount", getIntroductoryPriceAmount())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .append("createBy", getCreateBy())
            .append("updateBy", getUpdateBy())
            .toString();
    }
}
