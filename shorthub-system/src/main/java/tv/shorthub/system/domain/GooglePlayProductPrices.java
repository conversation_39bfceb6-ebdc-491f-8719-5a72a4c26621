package tv.shorthub.system.domain;

import java.math.BigDecimal;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import tv.shorthub.common.annotation.Excel;
import lombok.Data;
import tv.shorthub.common.core.domain.BaseEntity;

/**
 * Google Play产品价格配置对象 google_play_product_prices
 * 
 * <AUTHOR>
 * @date 2025-07-01
 */
@Data
public class GooglePlayProductPrices extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 产品ID */
    @Excel(name = "产品ID")
    private String productId;

    /** 货币代码(如：USD, CNY, HKD) */
    @Excel(name = "货币代码(如：USD, CNY, HKD)")
    private String currencyCode;

    /** 价格金额 */
    @Excel(name = "价格金额")
    private BigDecimal priceAmount;

    /** 价格微单位(Google Play标准) */
    @Excel(name = "价格微单位(Google Play标准)")
    private Long priceMicros;

    /** 是否默认价格 */
    @Excel(name = "是否默认价格")
    private Boolean isDefault;



    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("productId", getProductId())
            .append("currencyCode", getCurrencyCode())
            .append("priceAmount", getPriceAmount())
            .append("priceMicros", getPriceMicros())
            .append("isDefault", getIsDefault())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .append("createBy", getCreateBy())
            .append("updateBy", getUpdateBy())
            .toString();
    }
}
