package tv.shorthub.system.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import tv.shorthub.common.annotation.Excel;
import lombok.Data;
import tv.shorthub.common.core.domain.BaseEntity;

/**
 * 会员级别对象 app_user_level
 * 
 * <AUTHOR>
 * @date 2025-05-14
 */
@Data
public class AppUserLevel extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 会员级别 */
    @Excel(name = "会员级别")
    private String level;

    /** 会员级别 */
    @Excel(name = "会员级别")
    private String levelName;



    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("level", getLevel())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
