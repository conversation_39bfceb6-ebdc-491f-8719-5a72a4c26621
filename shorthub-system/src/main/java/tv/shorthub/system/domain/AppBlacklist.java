package tv.shorthub.system.domain;

import java.util.Date;
import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import tv.shorthub.common.annotation.Excel;
import lombok.Data;
import tv.shorthub.common.core.domain.BaseEntity;

/**
 * 黑名单对象 app_blacklist
 * 
 * <AUTHOR>
 * @date 2025-08-06
 */
@Data
public class AppBlacklist extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 黑名单类型：device_id, user_id, ip, email */
    @Excel(name = "黑名单类型：device_id, user_id, ip, email")
    private String type;

    /** 黑名单值 */
    @Excel(name = "黑名单值")
    private String value;

    /** 加入黑名单的原因 */
    @Excel(name = "加入黑名单的原因")
    private String reason;

    /** 状态：1-有效，0-无效 */
    @Excel(name = "状态：1-有效，0-无效")
    private Boolean status;

    /** 扩展字段 */
    @Excel(name = "扩展字段")
    private JSONObject extendJson;



    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("type", getType())
            .append("value", getValue())
            .append("reason", getReason())
            .append("status", getStatus())
            .append("extendJson", getExtendJson())
            .toString();
    }
}
