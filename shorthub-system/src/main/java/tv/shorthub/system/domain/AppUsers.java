package tv.shorthub.system.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import tv.shorthub.common.annotation.Excel;
import tv.shorthub.common.core.domain.BaseEntity;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

import java.util.Date;
import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import tv.shorthub.common.annotation.Excel;
import lombok.Data;
import tv.shorthub.common.core.domain.BaseEntity;

/**
 * 社交媒体登录对象 app_users
 *
 * <AUTHOR>
 * @date 2025-05-12
 */
@Data
public class AppUsers extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 用户ID */
    @Excel(name = "用户ID")
    private String userId;

    /** 用户名称 */
    @Excel(name = "用户名称")
    private String username;

    /** email邮箱 */
    @Excel(name = "email邮箱")
    private String email;

    /** 设备ID */
    @Excel(name = "设备ID")
    private String deviceId;

    /** password */
    @Excel(name = "password")
    private String password;

    /** tfid */
    @Excel(name = "tfid")
    private String tfid;

    /** 是否是广告用户 */
    @Excel(name = "广告用户")
    private Boolean adUser;

    /** 头像url */
    @Excel(name = "头像url")
    private String profilePicture;

    /** 手机号码 */
    @Excel(name = "手机号码")
    private String phoneNumber;

    /** 充值金币 */
    @Excel(name = "充值金币")
    private Long balanceCoin;

    /** 赠送金币 */
    @Excel(name = "赠送金币")
    private Long balanceBonus;

    /** 会员类型 */
    @Excel(name = "会员类型")
    private String memberLevel;

    /** 会员有效期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "会员有效期", width = 60, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date memberTime;

    /** 提供商 */
    @Excel(name = "提供商")
    private String provider;

    /** 提供商id */
    @Excel(name = "提供商id")
    private String providerId;

    /** 国家 */
    @Excel(name = "国家")
    private String countryCode;

    /** 语种 */
    @Excel(name = "语种")
    private String languageCode;

    /** access_token */
    @Excel(name = "access_token")
    private String accessToken;

    /** refresh_token */
    @Excel(name = "refresh_token")
    private String refreshToken;

    /** expires_at */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "expires_at", width = 60, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date expiresAt;

    /** 扩展字段 */
    @Excel(name = "扩展字段")
    private JSONObject extendJson;



    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("userId", getUserId())
                .append("username", getUsername())
                .append("email", getEmail())
                .append("profilePicture", getProfilePicture())
                .append("phoneNumber", getPhoneNumber())
                .append("balanceCoin", getBalanceCoin())
                .append("balanceBonus", getBalanceBonus())
                .append("memberLevel", getMemberLevel())
                .append("memberTime", getMemberTime())
                .append("provider", getProvider())
                .append("providerId", getProviderId())
                .append("countryCode", getCountryCode())
                .append("languageCode", getLanguageCode())
                .append("accessToken", getAccessToken())
                .append("refreshToken", getRefreshToken())
                .append("expiresAt", getExpiresAt())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("remark", getRemark())
                .append("params", getParams())
                .toString();
    }
}
