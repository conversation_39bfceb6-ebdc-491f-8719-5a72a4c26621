package tv.shorthub.system.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import tv.shorthub.common.annotation.Excel;
import lombok.Data;
import tv.shorthub.common.core.domain.BaseEntity;

/**
 * 每小时订单与订阅统计对象 statistics_hourly_subscription_stats
 * 
 * <AUTHOR>
 * @date 2025-06-29
 */
@Data
public class StatisticsHourlySubscriptionStats extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 自增ID */
    private Long id;

    /** 应用ID */
    @Excel(name = "应用ID")
    private String appid;

    /** 推广链接ID */
    @Excel(name = "推广链接ID")
    private String tfid;

    /** 统计的小时，例如 2025-06-24 10:00:00 (表示10点到11点的数据) */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "统计的小时，例如 2025-06-24 10:00:00 (表示10点到11点的数据)", width = 60, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date statHour;

    /** 该小时内首次订阅订单的数量 */
    @Excel(name = "该小时内首次订阅订单的数量")
    private Long newSubscriptionOrderCount;

    /** 该小时内首次订阅订单的总金额 */
    @Excel(name = "该小时内首次订阅订单的总金额")
    private BigDecimal newSubscriptionOrderAmount;

    /** 该小时内成功续订的数量 */
    @Excel(name = "该小时内成功续订的数量")
    private Long successfulRenewalCount;

    /** 该小时内成功续订的总金额 */
    @Excel(name = "该小时内成功续订的总金额")
    private BigDecimal successfulRenewalAmount;

    /** 该小时内总订单数量 (首次订阅 + 成功续订) */
    @Excel(name = "该小时内总订单数量 (首次订阅 + 成功续订)")
    private Long totalOrderCount;

    /** 该小时内总订单金额 (首次订阅金额 + 成功续订金额) */
    @Excel(name = "该小时内总订单金额 (首次订阅金额 + 成功续订金额)")
    private BigDecimal totalOrderAmount;



    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("appid", getAppid())
            .append("tfid", getTfid())
            .append("statHour", getStatHour())
            .append("newSubscriptionOrderCount", getNewSubscriptionOrderCount())
            .append("newSubscriptionOrderAmount", getNewSubscriptionOrderAmount())
            .append("successfulRenewalCount", getSuccessfulRenewalCount())
            .append("successfulRenewalAmount", getSuccessfulRenewalAmount())
            .append("totalOrderCount", getTotalOrderCount())
            .append("totalOrderAmount", getTotalOrderAmount())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
