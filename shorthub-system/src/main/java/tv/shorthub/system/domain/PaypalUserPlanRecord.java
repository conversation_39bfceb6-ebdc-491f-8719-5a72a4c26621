package tv.shorthub.system.domain;

import java.math.BigDecimal;
import com.alibaba.fastjson2.JSONObject;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import tv.shorthub.common.annotation.Excel;
import lombok.Data;
import tv.shorthub.common.core.domain.BaseEntity;

/**
 * paypal签约续订记录对象 paypal_user_plan_record
 * 
 * <AUTHOR>
 * @date 2025-06-27
 */
@Data
public class PaypalUserPlanRecord extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 本地订单号 */
    @Excel(name = "本地订单号")
    private String orderNo;

    /** PayPal支付ID */
    @Excel(name = "PayPal支付ID")
    private String paymentId;

    /** 续费周期 */
    @Excel(name = "续费周期")
    private String period;

    /** 续费次数 */
    @Excel(name = "续费次数")
    private Long periodNum;

    /** 续费金额 */
    @Excel(name = "续费金额")
    private BigDecimal amount;

    /** 币种 */
    @Excel(name = "币种")
    private String currency;

    /** 扩展字段 */
    @Excel(name = "扩展字段")
    private JSONObject extendData;



    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("orderNo", getOrderNo())
            .append("paymentId", getPaymentId())
            .append("period", getPeriod())
            .append("periodNum", getPeriodNum())
            .append("amount", getAmount())
            .append("currency", getCurrency())
            .append("extendData", getExtendData())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
