package tv.shorthub.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import tv.shorthub.common.annotation.Excel;
import lombok.Data;
import tv.shorthub.common.core.domain.BaseEntity;

/**
 * 回传策略对象 ad_retargeting_strategy
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Data
public class AdRetargetingStrategy extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @TableId(type = IdType.AUTO)
    private Long id;

    /** appid */
    @Excel(name = "appid")
    private String appid;

    /** 策略名称 */
    @Excel(name = "策略名称")
    private String strategyName;

    /** 策略类型：1-点击回传 2-转化回传 3-自定义事件 */
    @Excel(name = "策略类型：1-点击回传 2-转化回传 3-自定义事件")
    private Long strategyType;

    /** 状态：0-禁用 1-启用 */
    @Excel(name = "状态：0-禁用 1-启用")
    private Boolean status;

    /** 广告平台：facebook/google/tiktok等 */
    @Excel(name = "广告平台：facebook/google/tiktok等")
    private String platform;

    /** 回传URL */
    @Excel(name = "回传URL")
    private String callbackUrl;

    /** 回传方式：GET/POST */
    @Excel(name = "回传方式：GET/POST")
    private String callbackMethod;

    /** 回传参数模板(JSON格式) */
    @Excel(name = "回传参数模板(JSON格式)")
    private String callbackParams;

    /** 重试次数 */
    @Excel(name = "重试次数")
    private Long retryTimes;

    /** 重试间隔(秒) */
    @Excel(name = "重试间隔(秒)")
    private Long retryInterval;

    /** 允许非投放用户 */
    @Excel(name = "允许非投放用户")
    private Boolean allowNotUser;



    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("strategyName", getStrategyName())
            .append("strategyType", getStrategyType())
            .append("status", getStatus())
            .append("platform", getPlatform())
            .append("callbackUrl", getCallbackUrl())
            .append("callbackMethod", getCallbackMethod())
            .append("callbackParams", getCallbackParams())
            .append("retryTimes", getRetryTimes())
            .append("retryInterval", getRetryInterval())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .append("createBy", getCreateBy())
            .append("updateBy", getUpdateBy())
            .toString();
    }
}
