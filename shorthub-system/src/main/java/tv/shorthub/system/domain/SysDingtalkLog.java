package tv.shorthub.system.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import tv.shorthub.common.annotation.Excel;
import lombok.Data;
import tv.shorthub.common.core.domain.BaseEntity;

/**
 * 钉钉发送日志对象 sys_dingtalk_log
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Data
public class SysDingtalkLog extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ID */
    private Long id;

    /** 业务ID (如订单号, 订阅ID) */
    @Excel(name = "业务ID (如订单号, 订阅ID)")
    private String businessId;

    /** 业务类型 (如 PAYMENT, SUBSCRIPTION) */
    @Excel(name = "业务类型 (如 PAYMENT, SUBSCRIPTION)")
    private String businessType;

    /** 事件类型 (如 PAYMENT.CAPTURE.COMPLETED) */
    @Excel(name = "事件类型 (如 PAYMENT.CAPTURE.COMPLETED)")
    private String eventType;

    /** Webhook地址 */
    @Excel(name = "Webhook地址")
    private String webhookUrl;

    /** 消息内容 */
    @Excel(name = "消息内容")
    private String messageContent;

    /** 钉钉响应码 */
    @Excel(name = "钉钉响应码")
    private String responseCode;

    /** 钉钉响应消息 */
    @Excel(name = "钉钉响应消息")
    private String responseMessage;

    /** 发送状态 (0=成功, 1=失败) */
    @Excel(name = "发送状态 (0=成功, 1=失败)")
    private String status;



    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("businessId", getBusinessId())
            .append("businessType", getBusinessType())
            .append("eventType", getEventType())
            .append("webhookUrl", getWebhookUrl())
            .append("messageContent", getMessageContent())
            .append("responseCode", getResponseCode())
            .append("responseMessage", getResponseMessage())
            .append("status", getStatus())
            .append("createTime", getCreateTime())
            .toString();
    }
}
