package tv.shorthub.system.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import tv.shorthub.common.annotation.Excel;
import lombok.Data;
import tv.shorthub.common.core.domain.BaseEntity;

/**
 * 推广链接对象 app_promotion
 *
 * <AUTHOR>
 * @date 2025-05-15
 */
@Data
public class AppPromotion extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 应用 */
    @Excel(name = "应用")
    private String appid;

    /** tfid */
    @Excel(name = "tfid")
    private String tfid;

    /** 标题 */
    @Excel(name = "标题")
    private String title;

    /** 内容ID */
    @Excel(name = "内容ID")
    private String contentId;

    /** 剧集ID */
    @Excel(name = "剧集ID")
    private String serialId;

    /** 第几集开始播放 */
    @Excel(name = "第几集开始播放")
    private Long seriesNumber;

    /** 落地类型 */
    @Excel(name = "落地类型")
    private String floorType;

    /** 广告渠道, facebook, google  */
    @Excel(name = "广告渠道, facebook, google ")
    private String adChannel;

    /** 推广链接 */
    @Excel(name = "推广链接")
    private String webUrl;

    /** 监测链接 */
    @Excel(name = "监测链接")
    private String listenUrl;

    /** 充值模板id */
    @Excel(name = "充值模板id")
    private String feeTemplateId;

    /** 第几集开始收费 */
    @Excel(name = "第几集开始收费")
    private Long videoFeeBegin;

    /** 每集多少金币 */
    @Excel(name = "每集多少金币")
    private Long videoEveryMoney;

    /** 跳集解锁 0-不跳 1-允许跳 */
    @Excel(name = "跳集解锁 0-不跳 1-允许跳")
    private Boolean unlockJump;

    /** 策略ID */
    @Excel(name = "策略ID")
    private Long strategyId;

    /** 是否启用 */
    @Excel(name = "是否启用")
    private Boolean enable;

    /** 待分配 */
    @Excel(name = "待分配")
    private Boolean allocation;



    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("appid", getAppid())
            .append("tfid", getTfid())
            .append("title", getTitle())
            .append("contentId", getContentId())
            .append("serialId", getSerialId())
            .append("seriesNumber", getSeriesNumber())
            .append("floorType", getFloorType())
            .append("adChannel", getAdChannel())
            .append("webUrl", getWebUrl())
            .append("listenUrl", getListenUrl())
            .append("feeTemplateId", getFeeTemplateId())
            .append("videoFeeBegin", getVideoFeeBegin())
            .append("videoEveryMoney", getVideoEveryMoney())
            .append("unlockJump", getUnlockJump())
            .append("enable", getEnable())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
