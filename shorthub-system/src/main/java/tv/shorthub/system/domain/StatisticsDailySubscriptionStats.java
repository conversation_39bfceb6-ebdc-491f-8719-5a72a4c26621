package tv.shorthub.system.domain;

import java.math.BigDecimal;
import java.util.Date;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import tv.shorthub.common.annotation.Excel;
import lombok.Data;
import tv.shorthub.common.core.domain.BaseEntity;

/**
 * 每日订阅统计对象 statistics_daily_subscription_stats
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
@Data
public class StatisticsDailySubscriptionStats extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 统计日期（yyyy-MM-dd） */
    @Excel(name = "统计日期", readConverterExp = "y=yyy-MM-dd")
    private Date statDate;

    /** 应用ID */
    @Excel(name = "应用ID")
    private String appid;

    /** 推广链接ID */
    @Excel(name = "推广链接ID")
    private String tfid;

    /** 新订阅数 */
    @Excel(name = "新订阅数")
    private Long newSubscriptionOrderCount;

    /** 新订阅金额 */
    @Excel(name = "新订阅金额")
    private BigDecimal newSubscriptionOrderAmount;

    /** 续订数 */
    @Excel(name = "续订数")
    private Long successfulRenewalCount;

    /** 续订金额 */
    @Excel(name = "续订金额")
    private BigDecimal successfulRenewalAmount;

    /** 总订阅数 */
    @Excel(name = "总订阅数")
    private Long totalOrderCount;

    /** 总订阅金额 */
    @Excel(name = "总订阅金额")
    private BigDecimal totalOrderAmount;

    /** 时区 */
    @Excel(name = "时区")
    private String timezone;



    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("statDate", getStatDate())
            .append("appid", getAppid())
            .append("tfid", getTfid())
            .append("newSubscriptionOrderCount", getNewSubscriptionOrderCount())
            .append("newSubscriptionOrderAmount", getNewSubscriptionOrderAmount())
            .append("successfulRenewalCount", getSuccessfulRenewalCount())
            .append("successfulRenewalAmount", getSuccessfulRenewalAmount())
            .append("totalOrderCount", getTotalOrderCount())
            .append("totalOrderAmount", getTotalOrderAmount())
            .append("timezone", getTimezone())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
