package tv.shorthub.system.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.springframework.format.annotation.DateTimeFormat;
import tv.shorthub.common.annotation.Excel;
import lombok.Data;
import tv.shorthub.common.core.domain.BaseEntity;

import java.util.Date;

/**
 * 剧集对象 app_drama_contents_serial
 *
 * <AUTHOR>
 * @date 2025-05-08
 */
@Data
public class AppDramaContentsSerial extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 剧目内容id */
    @Excel(name = "剧目内容id")
    private String contentId;

    /** 剧集序号 */
    @Excel(name = "剧集序号")
    private Long serialNumber;

    /** 剧集地址 */
    @Excel(name = "剧集地址")
    private String serialUrl;

    /** 视频大小 */
    @Excel(name = "视频大小")
    private Long videoSize;

    /** 视频时长 */
    @Excel(name = "视频时长")
    private Long videoSeconds;

    /** 存储路径 */
    @Excel(name = "存储路径")
    private String storagePath;

    /** 标题 */
    @Excel(name = "标题")
    private String title;

    /** 描述 */
    @Excel(name = "描述")
    private String description;

    /** 剧集地址过期时间 */
    @Excel(name = "剧集地址过期时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date serialUrlExpireTime;


    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("contentId", getContentId())
            .append("serialNumber", getSerialNumber())
            .append("serialUrl", getSerialUrl())
            .append("title", getTitle())
            .append("description", getDescription())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
