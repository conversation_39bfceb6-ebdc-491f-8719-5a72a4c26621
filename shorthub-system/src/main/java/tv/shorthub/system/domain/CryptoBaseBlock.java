package tv.shorthub.system.domain;

import java.math.BigDecimal;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import tv.shorthub.common.annotation.Excel;
import lombok.Data;
import tv.shorthub.common.core.domain.BaseEntity;

/**
 * BASE区块链区块对象 crypto_base_block
 * 
 * <AUTHOR>
 * @date 2025-08-05
 */
@Data
public class CryptoBaseBlock extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 区块哈希 */
    @Excel(name = "区块哈希")
    private String blockHash;

    /** 区块高度 */
    @Excel(name = "区块高度")
    private Long blockNumber;

    /** 父区块哈希 */
    @Excel(name = "父区块哈希")
    private String parentHash;

    /** 区块时间戳 */
    @Excel(name = "区块时间戳")
    private Long timestamp;

    /** 交易数量 */
    @Excel(name = "交易数量")
    private Long transactionsCount;

    /** 矿工地址 */
    @Excel(name = "矿工地址")
    private String miner;

    /** 难度值 */
    @Excel(name = "难度值")
    private String difficulty;

    /** 总难度 */
    @Excel(name = "总难度")
    private String totalDifficulty;

    /** 区块大小(字节) */
    @Excel(name = "区块大小(字节)")
    private Long size;

    /** Gas使用量 */
    @Excel(name = "Gas使用量")
    private Long gasUsed;

    /** Gas限制 */
    @Excel(name = "Gas限制")
    private Long gasLimit;

    /** 基础Gas费用 */
    @Excel(name = "基础Gas费用")
    private BigDecimal baseFeePerGas;

    /** 额外数据 */
    @Excel(name = "额外数据")
    private String extraData;

    /** 日志布隆过滤器 */
    @Excel(name = "日志布隆过滤器")
    private String logsBloom;

    /** 收据根 */
    @Excel(name = "收据根")
    private String receiptsRoot;

    /** 状态根 */
    @Excel(name = "状态根")
    private String stateRoot;

    /** 交易根 */
    @Excel(name = "交易根")
    private String transactionsRoot;

    /** 叔块列表 */
    @Excel(name = "叔块列表")
    private String uncles;

    /** 原始区块数据(JSON) */
    @Excel(name = "原始区块数据(JSON)")
    private String rawData;

    /** 完整区块内容(JSON) */
    @Excel(name = "完整区块内容(JSON)")
    private String fullContent;

    /** 是否已确认 */
    @Excel(name = "是否已确认")
    private Boolean isConfirmed;

    /** 确认数 */
    @Excel(name = "确认数")
    private Long confirmations;



    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("blockHash", getBlockHash())
            .append("blockNumber", getBlockNumber())
            .append("parentHash", getParentHash())
            .append("timestamp", getTimestamp())
            .append("transactionsCount", getTransactionsCount())
            .append("miner", getMiner())
            .append("difficulty", getDifficulty())
            .append("totalDifficulty", getTotalDifficulty())
            .append("size", getSize())
            .append("gasUsed", getGasUsed())
            .append("gasLimit", getGasLimit())
            .append("baseFeePerGas", getBaseFeePerGas())
            .append("extraData", getExtraData())
            .append("logsBloom", getLogsBloom())
            .append("receiptsRoot", getReceiptsRoot())
            .append("stateRoot", getStateRoot())
            .append("transactionsRoot", getTransactionsRoot())
            .append("uncles", getUncles())
            .append("rawData", getRawData())
            .append("fullContent", getFullContent())
            .append("isConfirmed", getIsConfirmed())
            .append("confirmations", getConfirmations())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
