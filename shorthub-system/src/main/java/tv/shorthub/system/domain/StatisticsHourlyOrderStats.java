package tv.shorthub.system.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import tv.shorthub.common.annotation.Excel;
import lombok.Data;
import tv.shorthub.common.core.domain.BaseEntity;

/**
 * 每小时核心订单交易统计对象 statistics_hourly_order_stats
 *
 * <AUTHOR>
 * @date 2025-06-27
 */
@Data
public class StatisticsHourlyOrderStats extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 自增ID */
    private Long id;

    /** 统计的小时 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "统计的小时", width = 60, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date statHour;

    /** 应用ID */
    @Excel(name = "应用ID")
    private String appid;

    /** 推广链接ID */
    @Excel(name = "推广链接ID")
    private String tfid;

    /** 订单支付通道 */
    @Excel(name = "订单支付通道")
    private String orderChannel;

    /** 该小时内总订单数 (已支付+去重后的未支付) */
    @Excel(name = "该小时内总订单数 (已支付+去重后的未支付)")
    private Long totalOrderCount;

    /** 该小时内已付款订单 (order_status = 1) 的数量 */
    @Excel(name = "该小时内已付款订单 (order_status = 1) 的数量")
    private Long paidOrderCount;

    /** 该小时内已付款订单成功人数（按IP去重） */
    @Excel(name = "该小时内已付款订单成功人数")
    private Long paidUserCount;

    /** 该小时内已付款订单 (order_status = 1) 的总金额 */
    @Excel(name = "该小时内已付款订单 (order_status = 1) 的总金额")
    private BigDecimal paidOrderAmount;

    /** 该小时内"真正被放弃"的未支付订单数 */
    @Excel(name = "该小时内真正被放弃的未支付订单数")
    private Long unpaidOrderCount;

    /** 该小时内"真正被放弃"的未支付金币订单总金额 (pay_type = 1) */
    @Excel(name = "该小时内真正被放弃的未支付金币订单总金额")
    private BigDecimal unpaidCoinOrderAmount;

    /** 该小时内"真正被放弃"的未支付订阅订单总金额 (pay_type = 2) */
    @Excel(name = "该小时内真正被放弃的未支付订阅订单总金额")
    private BigDecimal unpaidSubscriptionOrderAmount;

    /** 该小时内会员充值订单 (pay_type = 0) 的数量 */
    @Excel(name = "该小时内会员充值订单 (pay_type = 0) 的数量")
    private Long memberOrderCount;

    /** 该小时内会员充值订单 (pay_type = 0) 的总金额 */
    @Excel(name = "该小时内会员充值订单 (pay_type = 0) 的总金额")
    private BigDecimal memberOrderAmount;

    /** 该小时内金币充值订单 (pay_type = 1) 的数量 */
    @Excel(name = "该小时内金币充值订单 (pay_type = 1) 的数量")
    private Long coinOrderCount;

    /** 该小时内金币充值订单 (pay_type = 1) 的总金额 */
    @Excel(name = "该小时内金币充值订单 (pay_type = 1) 的总金额")
    private BigDecimal coinOrderAmount;

    /** 该小时内订阅充值订单 (pay_type = 2) 的数量 */
    @Excel(name = "该小时内订阅充值订单 (pay_type = 2) 的数量")
    private Long subscriptionOrderCount;

    /** 该小时内订阅充值订单 (pay_type = 2) 的总金额 */
    @Excel(name = "该小时内订阅充值订单 (pay_type = 2) 的总金额")
    private BigDecimal subscriptionOrderAmount;



    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("statHour", getStatHour())
                .append("appid", getAppid())
                .append("tfid", getTfid())
                .append("orderChannel", getOrderChannel())
                .append("totalOrderCount", getTotalOrderCount())
                .append("paidOrderCount", getPaidOrderCount())
                .append("paidUserCount", getPaidUserCount())
                .append("paidOrderAmount", getPaidOrderAmount())
                .append("unpaidOrderCount", getUnpaidOrderCount())
                .append("unpaidCoinOrderAmount", getUnpaidCoinOrderAmount())
                .append("unpaidSubscriptionOrderAmount", getUnpaidSubscriptionOrderAmount())
                .append("memberOrderCount", getMemberOrderCount())
                .append("memberOrderAmount", getMemberOrderAmount())
                .append("coinOrderCount", getCoinOrderCount())
                .append("coinOrderAmount", getCoinOrderAmount())
                .append("subscriptionOrderCount", getSubscriptionOrderCount())
                .append("subscriptionOrderAmount", getSubscriptionOrderAmount())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}
