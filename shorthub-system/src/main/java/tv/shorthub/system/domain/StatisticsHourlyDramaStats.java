package tv.shorthub.system.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import tv.shorthub.common.annotation.Excel;
import lombok.Data;
import tv.shorthub.common.core.domain.BaseEntity;

/**
 * 每小时剧集分析统计对象 statistics_hourly_drama_stats
 * 
 * <AUTHOR>
 * @date 2025-07-05
 */
@Data
public class StatisticsHourlyDramaStats extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 自增主键 */
    private Long id;

    /** 统计小时 (例如: 2025-07-15 10:00:00) */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "统计小时 (例如: 2025-07-15 10:00:00)", width = 60, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date statHour;

    /** 应用ID */
    @Excel(name = "应用ID")
    private String appid;

    /** 剧集内容ID */
    @Excel(name = "剧集内容ID")
    private String contentId;

    /** 推广链接ID (空字符串表示自然流量) */
    @Excel(name = "推广链接ID (空字符串表示自然流量)")
    private String tfid;

    /** 播放量 (Video Views) */
    @Excel(name = "播放量 (Video Views)")
    private Long vvCount;

    /** 独立观看用户数 (Unique Visitors) */
    @Excel(name = "独立观看用户数 (Unique Visitors)")
    private Long uvCount;

    /** 总观看时长（秒） */
    @Excel(name = "总观看时长", readConverterExp = "秒=")
    private Long totalWatchSeconds;

    /** 付费解锁次数 */
    @Excel(name = "付费解锁次数")
    private Long paidUnlockCount;

    /** 会员解锁次数 */
    @Excel(name = "会员解锁次数")
    private Long memberUnlockCount;

    /** 总消费金币 */
    @Excel(name = "总消费金币")
    private Long totalCoinSpent;



    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("statHour", getStatHour())
            .append("appid", getAppid())
            .append("contentId", getContentId())
            .append("tfid", getTfid())
            .append("vvCount", getVvCount())
            .append("uvCount", getUvCount())
            .append("totalWatchSeconds", getTotalWatchSeconds())
            .append("paidUnlockCount", getPaidUnlockCount())
            .append("memberUnlockCount", getMemberUnlockCount())
            .append("totalCoinSpent", getTotalCoinSpent())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
