package tv.shorthub.system.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import tv.shorthub.common.annotation.Excel;
import lombok.Data;
import tv.shorthub.common.core.domain.BaseEntity;

/**
 * 虚拟货币交易监听记录对象 crypto_monitor_log
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
@Data
public class CryptoMonitorLog extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 区块链类型 */
    @Excel(name = "区块链类型")
    private String blockchain;

    /** 监听的钱包地址 */
    @Excel(name = "监听的合约地址")
    private String contractAddress;

    /** 最后监听的区块高度 */
    @Excel(name = "最后监听的区块高度")
    private Long lastBlockHeight;

    /** 最后监听时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "最后监听时间", width = 60, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date lastMonitorTime;

    /** 监听状态(0-停止,1-运行) */
    @Excel(name = "监听状态(0-停止,1-运行)")
    private Boolean monitorStatus;

    /** 错误次数 */
    @Excel(name = "错误次数")
    private Long errorCount;

    /** 最后错误信息 */
    @Excel(name = "最后错误信息")
    private String lastError;



    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("blockchain", getBlockchain())
            .append("contractAddress", getContractAddress())
            .append("lastBlockHeight", getLastBlockHeight())
            .append("lastMonitorTime", getLastMonitorTime())
            .append("monitorStatus", getMonitorStatus())
            .append("errorCount", getErrorCount())
            .append("lastError", getLastError())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
