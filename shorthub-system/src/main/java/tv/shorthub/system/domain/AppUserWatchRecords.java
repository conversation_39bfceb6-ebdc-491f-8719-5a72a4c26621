package tv.shorthub.system.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import tv.shorthub.common.annotation.Excel;
import tv.shorthub.common.core.domain.BaseEntity;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 用户观看记录对象 app_user_watch_records
 *
 * <AUTHOR>
 * @date 2025-05-06
 */
@Data
public class AppUserWatchRecords extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Long id;

    /** 用户 */
    @Excel(name = "用户")
    private String userId;

    /** tfid */
    @Excel(name = "tfid")
    private String tfid;

    /** appid */
    @Excel(name = "appid")
    private String appid;

    /** 剧目 */
    @Excel(name = "剧目")
    private String contentId;

    /** 第几集 */
    @Excel(name = "第几集")
    private Long serialNumber;

    /** 第几秒 */
    @Excel(name = "第几秒")
    private Long serialSecond;

    /** 总观看秒数 */
    @Excel(name = "总观看秒数")
    private Long totalWatchSecond;

    /** 最大观看到第几秒 */
    @Excel(name = "最大观看到第几秒")
    private Long maxWatchSecond;

    /** 观看时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "观看时间", width = 60, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date watchedAt;



    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("userId", getUserId())
            .append("contentId", getContentId())
            .append("watchedAt", getWatchedAt())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
