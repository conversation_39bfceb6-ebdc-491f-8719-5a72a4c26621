package tv.shorthub.system.domain;

import java.math.BigDecimal;
import com.alibaba.fastjson2.JSONObject;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import tv.shorthub.common.annotation.Excel;
import lombok.Data;
import tv.shorthub.common.core.domain.BaseEntity;

/**
 * airwallex支付日志对象 airwallex_payment_log
 * 
 * <AUTHOR>
 * @date 2025-07-17
 */
@Data
public class AirwallexPaymentLog extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 关联用户ID */
    @Excel(name = "关联用户ID")
    private String relatedUserId;

    /** 关联的本地订单号 */
    @Excel(name = "关联的本地订单号")
    private String orderNo;

    /** airwallex支付ID */
    @Excel(name = "airwallex支付ID")
    private String paymentId;

    /** 授权ID */
    @Excel(name = "授权ID")
    private String consentId;

    /** 商户号 */
    @Excel(name = "商户号")
    private String mcnId;

    /** 用户ID */
    @Excel(name = "用户ID")
    private String userId;

    /** airwallex订单状态 */
    @Excel(name = "airwallex订单状态")
    private String status;

    /** 支付金额 */
    @Excel(name = "支付金额")
    private BigDecimal totalAmount;

    /** 币种 */
    @Excel(name = "币种")
    private String currency;

    /** 商品描述 */
    @Excel(name = "商品描述")
    private String description;

    /** 跳转链接 */
    @Excel(name = "跳转链接")
    private String returnUrl;

    /** 充值/订阅 */
    @Excel(name = "充值/订阅")
    private String paymentMethod;

    /** 授权信息 */
    @Excel(name = "授权信息")
    private JSONObject consent;

    /** 原始信息 */
    @Excel(name = "原始信息")
    private JSONObject rawData;

    /** MIT授权 */
    @Excel(name = "MIT授权")
    private JSONObject consentMit;



    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("relatedUserId", getRelatedUserId())
            .append("orderNo", getOrderNo())
            .append("paymentId", getPaymentId())
            .append("consentId", getConsentId())
            .append("mcnId", getMcnId())
            .append("userId", getUserId())
            .append("status", getStatus())
            .append("totalAmount", getTotalAmount())
            .append("currency", getCurrency())
            .append("description", getDescription())
            .append("returnUrl", getReturnUrl())
            .append("paymentMethod", getPaymentMethod())
            .append("rawData", getRawData())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
