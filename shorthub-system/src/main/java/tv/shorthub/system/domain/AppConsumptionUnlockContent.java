package tv.shorthub.system.domain;

import java.util.Date;
import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import tv.shorthub.common.annotation.Excel;
import lombok.Data;
import tv.shorthub.common.core.domain.BaseEntity;

/**
 * 剧目解锁对象 app_consumption_unlock_content
 * 
 * <AUTHOR>
 * @date 2025-05-15
 */
@Data
public class AppConsumptionUnlockContent extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 自增id */
    private Long id;

    /** appid */
    @Excel(name = "appid")
    private String appid;

    /** 用户ID */
    @Excel(name = "用户ID")
    private String userId;

    /** 会员ID */
    @Excel(name = "会员ID")
    private String memberId;

    /** 剧目ID */
    @Excel(name = "剧目ID")
    private String dramaId;

    /** 内容ID */
    @Excel(name = "内容ID")
    private String contentId;

    /** 第几集, 0=整部剧 */
    @Excel(name = "第几集, 0=整部剧")
    private Long serialNumber;

    /** 过期时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "过期时间", width = 60, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date expireTime;

    /** 备用字段 */
    @Excel(name = "备用字段")
    private JSONObject extendJson;



    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("appid", getAppid())
            .append("userId", getUserId())
            .append("dramaId", getDramaId())
            .append("contentId", getContentId())
            .append("serialNumber", getSerialNumber())
            .append("expireTime", getExpireTime())
            .append("extendJson", getExtendJson())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
