package tv.shorthub.system.domain.dto;

import tv.shorthub.common.annotation.Excel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import tv.shorthub.system.domain.StatisticsHourlyDramaRechargeStats;

/**
 * 小时级剧目充值统计DTO，包含剧目名称等显示信息
 * 
 * <AUTHOR>
 * @date 2025-07-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class StatisticsHourlyDramaRechargeStatsDTO extends StatisticsHourlyDramaRechargeStats
{
    private static final long serialVersionUID = 1L;

    /** 剧目名称 */
    @Excel(name = "剧目名称")
    private String dramaTitle;

    /** 剧目语言版本标题 */
    @Excel(name = "剧目语言版本")
    private String contentTitle;

    /** 推广链接显示名称 */
    @Excel(name = "推广链接名称")
    private String tfidDisplayName;
}