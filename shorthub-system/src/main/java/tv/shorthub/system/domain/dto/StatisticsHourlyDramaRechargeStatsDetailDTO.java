package tv.shorthub.system.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import tv.shorthub.system.domain.StatisticsHourlyDramaRechargeStats;

import java.util.Date;
import java.util.List;

/**
 * 小时级剧目充值统计详情DTO
 * 
 * <AUTHOR>
 * @date 2025-07-16
 */
@Data
public class StatisticsHourlyDramaRechargeStatsDetailDTO extends StatisticsHourlyDramaRechargeStats {
    
    /** 解锁剧集详情列表 */
    private List<UnlockEpisodeDetail> unlockEpisodeDetails;
    
    /** 解锁剧集详情总数 */
    private Long unlockEpisodeDetailsTotal;
    
    /**
     * 解锁剧集详情
     */
    @Data
    public static class UnlockEpisodeDetail {
        /** 内容ID */
        private String contentId;
        
        /** 剧目ID */
        private String dramaId;
        
        /** 剧目标题 */
        private String dramaTitle;
        
        /** 内容标题 */
        private String contentTitle;
        
        /** 解锁剧集数量 */
        private Long unlockCount;
        
        /** 消费金币数量 */
        private Long consumptionAmount;
        
        /** 消费用户数 */
        private Long consumptionUserCount;
        
        /** 统计时间 */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private Date statTime;
    }
}