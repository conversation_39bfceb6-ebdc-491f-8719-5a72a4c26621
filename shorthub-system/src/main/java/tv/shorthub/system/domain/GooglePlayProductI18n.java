package tv.shorthub.system.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import tv.shorthub.common.annotation.Excel;
import lombok.Data;
import tv.shorthub.common.core.domain.BaseEntity;

/**
 * Google Play产品多语言信息对象 google_play_product_i18n
 * 
 * <AUTHOR>
 * @date 2025-07-01
 */
@Data
public class GooglePlayProductI18n extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 产品ID */
    @Excel(name = "产品ID")
    private String productId;

    /** 语言代码(如：zh-CN, en-US) */
    @Excel(name = "语言代码(如：zh-CN, en-US)")
    private String language;

    /** 产品名称 */
    @Excel(name = "产品名称")
    private String name;

    /** 产品描述 */
    @Excel(name = "产品描述")
    private String description;



    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("productId", getProductId())
            .append("language", getLanguage())
            .append("name", getName())
            .append("description", getDescription())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .append("createBy", getCreateBy())
            .append("updateBy", getUpdateBy())
            .toString();
    }
}
