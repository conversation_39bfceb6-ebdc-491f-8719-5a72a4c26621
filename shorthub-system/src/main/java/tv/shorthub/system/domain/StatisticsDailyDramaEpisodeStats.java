package tv.shorthub.system.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import tv.shorthub.common.annotation.Excel;
import lombok.Data;
import tv.shorthub.common.core.domain.BaseEntity;

/**
 * 剧集每日统计对象 statistics_daily_drama_episode_stats
 * 
 * <AUTHOR>
 * @date 2025-08-01
 */
@Data
public class StatisticsDailyDramaEpisodeStats extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 应用标识 */
    @Excel(name = "应用标识")
    private String appid;

    /** 剧目内容ID */
    @Excel(name = "剧目内容ID")
    private String contentId;

    /** 集数序号 */
    @Excel(name = "集数序号")
    private Long serialNumber;

    /** 统计日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "统计日期", width = 60, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date statDate;

    /** 推广标识(空=自然流量) */
    @Excel(name = "推广标识(空=自然流量)")
    private String tfid;

    /** 广告渠道 */
    @Excel(name = "广告渠道")
    private String adChannel;

    /** 独立观看用户数 */
    @Excel(name = "独立观看用户数")
    private Long uniqueViewers;

    /** 总观看次数 */
    @Excel(name = "总观看次数")
    private Long totalViews;

    /** 视频总时长(秒) */
    @Excel(name = "视频总时长(秒)")
    private Long videoSeconds;

    /** 平均观看时长(秒) */
    @Excel(name = "平均观看时长(秒)")
    private BigDecimal avgWatchSeconds;

    /** 中位数观看时长(秒) */
    @Excel(name = "中位数观看时长(秒)")
    private Long medianWatchSeconds;

    /** 总观看时长(秒) */
    @Excel(name = "总观看时长(秒)")
    private Long totalWatchSeconds;

    /** 平均完成率(%) */
    @Excel(name = "平均完成率(%)")
    private BigDecimal avgCompletionRate;

    /** 高完成率用户数(>80%) */
    @Excel(name = "高完成率用户数(>80%)")
    private Long highCompletionViewers;

    /** 时区 */
    @Excel(name = "时区")
    private String timezone;

    /** 备用字段 */
    @Excel(name = "备用字段")
    private JSONObject extendJson;



    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("appid", getAppid())
            .append("contentId", getContentId())
            .append("serialNumber", getSerialNumber())
            .append("statDate", getStatDate())
            .append("tfid", getTfid())
            .append("adChannel", getAdChannel())
            .append("uniqueViewers", getUniqueViewers())
            .append("totalViews", getTotalViews())
            .append("videoSeconds", getVideoSeconds())
            .append("avgWatchSeconds", getAvgWatchSeconds())
            .append("medianWatchSeconds", getMedianWatchSeconds())
            .append("totalWatchSeconds", getTotalWatchSeconds())
            .append("avgCompletionRate", getAvgCompletionRate())
            .append("highCompletionViewers", getHighCompletionViewers())
            .append("timezone", getTimezone())
            .append("extendJson", getExtendJson())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
