package tv.shorthub.system.domain;

import java.math.BigDecimal;
import com.alibaba.fastjson2.JSONObject;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import tv.shorthub.common.annotation.Excel;
import lombok.Data;
import tv.shorthub.common.core.domain.BaseEntity;

/**
 * 虚拟货币代币配置对象 crypto_token_config
 * 
 * <AUTHOR>
 * @date 2025-08-05
 */
@Data
public class CryptoTokenConfig extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 区块链类型(TRON/BASE/SOLANA) */
    @Excel(name = "区块链类型(TRON/BASE/SOLANA)")
    private String blockchain;

    /** 代币类型(USDT/USDC) */
    @Excel(name = "代币类型(USDT/USDC)")
    private String tokenType;

    /** 代币符号 */
    @Excel(name = "代币符号")
    private String tokenSymbol;

    /** 代币名称 */
    @Excel(name = "代币名称")
    private String tokenName;

    /** 合约地址 */
    @Excel(name = "合约地址")
    private String contractAddress;

    /** 小数位数 */
    @Excel(name = "小数位数")
    private Long decimals;

    /** 最小充值金额 */
    @Excel(name = "最小充值金额")
    private BigDecimal minDepositAmount;

    /** 最大充值金额 */
    @Excel(name = "最大充值金额")
    private BigDecimal maxDepositAmount;

    /** 所需确认数 */
    @Excel(name = "所需确认数")
    private Long requiredConfirmations;

    /** 是否启用(0:禁用 1:启用) */
    @Excel(name = "是否启用(0:禁用 1:启用)")
    private Boolean isActive;

    /** Gas限制 */
    @Excel(name = "Gas限制")
    private Long gasLimit;

    /** Gas价格 */
    @Excel(name = "Gas价格")
    private BigDecimal gasPrice;

    /** 网络手续费 */
    @Excel(name = "网络手续费")
    private BigDecimal networkFee;

    /** 额外配置JSON */
    @Excel(name = "额外配置JSON")
    private JSONObject configJson;



    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("blockchain", getBlockchain())
            .append("tokenType", getTokenType())
            .append("tokenSymbol", getTokenSymbol())
            .append("tokenName", getTokenName())
            .append("contractAddress", getContractAddress())
            .append("decimals", getDecimals())
            .append("minDepositAmount", getMinDepositAmount())
            .append("maxDepositAmount", getMaxDepositAmount())
            .append("requiredConfirmations", getRequiredConfirmations())
            .append("isActive", getIsActive())
            .append("gasLimit", getGasLimit())
            .append("gasPrice", getGasPrice())
            .append("networkFee", getNetworkFee())
            .append("configJson", getConfigJson())
            .append("remark", getRemark())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .append("createBy", getCreateBy())
            .append("updateBy", getUpdateBy())
            .toString();
    }
}
