package tv.shorthub.system.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import tv.shorthub.common.annotation.Excel;
import lombok.Data;
import tv.shorthub.common.core.domain.BaseEntity;

/**
 * 剧集链接对象 app_drama_contents_serial_url
 * 
 * <AUTHOR>
 * @date 2025-05-16
 */
@Data
public class AppDramaContentsSerialUrl extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 剧目内容id */
    @Excel(name = "剧目内容id")
    private String contentId;

    /** 剧集序号 */
    @Excel(name = "剧集序号")
    private Long serialNumber;

    /** 剧集id */
    @Excel(name = "剧集id")
    private String serialId;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String storagePath;

    /** original, compressed */
    @Excel(name = "original, compressed")
    private String compressType;

    /** mp4, m3u8 */
    @Excel(name = "mp4, m3u8")
    private String videoFormat;

    /** 视频大小 */
    @Excel(name = "视频大小")
    private Long videoSize;

    /** 视频时长 */
    @Excel(name = "视频时长")
    private Long videoSeconds;

    /** 剧集地址 */
    @Excel(name = "剧集地址")
    private String videoUrl;

    /** 链接过期时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "链接过期时间", width = 60, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date videoUrlExpireTime;

    /** 压缩是否成功 */
    @Excel(name = "压缩是否成功")
    private Boolean compressionSuccess;

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("serialId", getSerialId())
            .append("storagePath", getStoragePath())
            .append("compressType", getCompressType())
            .append("videoFormat", getVideoFormat())
            .append("videoUrl", getVideoUrl())
            .append("videoUrlExpireTime", getVideoUrlExpireTime())
            .append("compressionSuccess", getCompressionSuccess())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
