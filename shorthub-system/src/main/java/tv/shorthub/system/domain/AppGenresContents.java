package tv.shorthub.system.domain;

import tv.shorthub.common.annotation.Excel;
import tv.shorthub.common.core.domain.BaseEntity;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 剧目类型内容对象 app_genres_contents
 * 
 * <AUTHOR>
 * @date 2025-05-06
 */
@Data
public class AppGenresContents extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 类型ID */
    @Excel(name = "类型ID")
    private Long genreId;

    /** 语言编码 */
    @Excel(name = "语言编码")
    private String languageCode;

    /** 标题 */
    @Excel(name = "标题")
    private String title;

    /** 描述 */
    @Excel(name = "描述")
    private String description;

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("genreId", getGenreId())
            .append("languageCode", getLanguageCode())
            .append("title", getTitle())
            .append("description", getDescription())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
} 