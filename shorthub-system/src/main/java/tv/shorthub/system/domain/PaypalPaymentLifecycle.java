package tv.shorthub.system.domain;

import com.alibaba.fastjson2.JSONObject;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import tv.shorthub.common.annotation.Excel;
import lombok.Data;
import tv.shorthub.common.core.domain.BaseEntity;

/**
 * paypal组件生命周期对象 paypal_payment_lifecycle
 * 
 * <AUTHOR>
 * @date 2025-07-04
 */
@Data
public class PaypalPaymentLifecycle extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** userId */
    @Excel(name = "userId")
    private String userId;

    /** 关联的本地订单号 */
    @Excel(name = "关联的本地订单号")
    private String orderNo;

    /** PayPal组件状态 */
    @Excel(name = "PayPal组件状态")
    private String state;

    /** 错误内容 */
    @Excel(name = "错误内容")
    private String errmsg;

    /** 扩展字段 */
    @Excel(name = "扩展字段")
    private JSONObject extendData;



    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("orderNo", getOrderNo())
            .append("state", getState())
            .append("errmsg", getErrmsg())
            .append("extendData", getExtendData())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
