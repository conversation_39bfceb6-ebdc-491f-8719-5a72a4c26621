package tv.shorthub.system.domain;

import java.math.BigDecimal;
import com.alibaba.fastjson2.JSONObject;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import tv.shorthub.common.annotation.Excel;
import lombok.Data;
import tv.shorthub.common.core.domain.BaseEntity;

/**
 * PayPal争议对象 paypal_dispute
 * 
 * <AUTHOR>
 * @date 2025-05-21
 */
@Data
public class PaypalDispute extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** PayPal争议ID */
    @Excel(name = "PayPal争议ID")
    private String disputeId;

    /** 关联的支付ID */
    @Excel(name = "关联的支付ID")
    private String paymentId;

    /** 关联的系统订单号 */
    @Excel(name = "关联的系统订单号")
    private String orderNo;

    /** 争议状态 */
    @Excel(name = "争议状态")
    private String status;

    /** 争议原因 */
    @Excel(name = "争议原因")
    private String reason;

    /** 争议类型 */
    @Excel(name = "争议类型")
    private String disputeType;

    /** 争议金额 */
    @Excel(name = "争议金额")
    private BigDecimal amount;

    /** 币种 */
    @Excel(name = "币种")
    private String currency;

    /** 买家ID */
    @Excel(name = "买家ID")
    private String buyerId;

    /** 卖家ID */
    @Excel(name = "卖家ID")
    private String sellerId;

    /** 原始数据 */
    @Excel(name = "原始数据")
    private JSONObject rawData;



    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("disputeId", getDisputeId())
            .append("paymentId", getPaymentId())
            .append("orderNo", getOrderNo())
            .append("status", getStatus())
            .append("reason", getReason())
            .append("disputeType", getDisputeType())
            .append("amount", getAmount())
            .append("currency", getCurrency())
            .append("buyerId", getBuyerId())
            .append("sellerId", getSellerId())
            .append("rawData", getRawData())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .append("createBy", getCreateBy())
            .append("updateBy", getUpdateBy())
            .append("remark", getRemark())
            .toString();
    }
}
