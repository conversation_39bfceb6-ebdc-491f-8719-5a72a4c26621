package tv.shorthub.system.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import tv.shorthub.common.annotation.Excel;
import lombok.Data;
import tv.shorthub.common.core.domain.BaseEntity;

/**
 * SOLANA区块链区块对象 crypto_solana_block
 * 
 * <AUTHOR>
 * @date 2025-08-05
 */
@Data
public class CryptoSolanaBlock extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 区块哈希 */
    @Excel(name = "区块哈希")
    private String blockHash;

    /** 区块高度 */
    @Excel(name = "区块高度")
    private Long blockNumber;

    /** 父槽位 */
    @Excel(name = "父槽位")
    private Long parentSlot;

    /** 区块时间戳 */
    @Excel(name = "区块时间戳")
    private Long timestamp;

    /** 交易数量 */
    @Excel(name = "交易数量")
    private Long transactionsCount;

    /** 领导者地址 */
    @Excel(name = "领导者地址")
    private String leader;

    /** 奖励信息(JSON) */
    @Excel(name = "奖励信息(JSON)")
    private String rewards;

    /** 区块时间 */
    @Excel(name = "区块时间")
    private Long blockTime;

    /** 区块高度 */
    @Excel(name = "区块高度")
    private Long blockHeight;

    /** 前一个区块哈希 */
    @Excel(name = "前一个区块哈希")
    private String previousBlockhash;

    /** 当前区块哈希 */
    @Excel(name = "当前区块哈希")
    private String blockhash;

    /** 父区块哈希 */
    @Excel(name = "父区块哈希")
    private String parentBlockhash;

    /** 已执行交易数量 */
    @Excel(name = "已执行交易数量")
    private Long executedTransactionCount;

    /** 总交易数量 */
    @Excel(name = "总交易数量")
    private Long transactionCount;

    /** 验证者列表(JSON) */
    @Excel(name = "验证者列表(JSON)")
    private String validators;

    /** 元数据(JSON) */
    @Excel(name = "元数据(JSON)")
    private String meta;

    /** 原始区块数据(JSON) */
    @Excel(name = "原始区块数据(JSON)")
    private String rawData;

    /** 完整区块内容(JSON) */
    @Excel(name = "完整区块内容(JSON)")
    private String fullContent;

    /** 是否已确认 */
    @Excel(name = "是否已确认")
    private Boolean isConfirmed;

    /** 确认数 */
    @Excel(name = "确认数")
    private Long confirmations;



    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("blockHash", getBlockHash())
            .append("blockNumber", getBlockNumber())
            .append("parentSlot", getParentSlot())
            .append("timestamp", getTimestamp())
            .append("transactionsCount", getTransactionsCount())
            .append("leader", getLeader())
            .append("rewards", getRewards())
            .append("blockTime", getBlockTime())
            .append("blockHeight", getBlockHeight())
            .append("previousBlockhash", getPreviousBlockhash())
            .append("parentBlockhash", getParentBlockhash())
            .append("executedTransactionCount", getExecutedTransactionCount())
            .append("transactionCount", getTransactionCount())
            .append("validators", getValidators())
            .append("meta", getMeta())
            .append("rawData", getRawData())
            .append("fullContent", getFullContent())
            .append("isConfirmed", getIsConfirmed())
            .append("confirmations", getConfirmations())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
