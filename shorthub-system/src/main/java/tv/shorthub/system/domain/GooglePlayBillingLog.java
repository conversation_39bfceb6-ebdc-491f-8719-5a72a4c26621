package tv.shorthub.system.domain;

import com.alibaba.fastjson2.JSONObject;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import tv.shorthub.common.annotation.Excel;
import lombok.Data;
import tv.shorthub.common.core.domain.BaseEntity;

/**
 * googleplay结算日志对象 google_play_billing_log
 * 
 * <AUTHOR>
 * @date 2025-07-02
 */
@Data
public class GooglePlayBillingLog extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 关联的本地订单号 */
    @Excel(name = "关联的本地订单号")
    private String orderNo;

    /** packageName */
    @Excel(name = "packageName")
    private String packageName;

    /** 完整的订单JSON */
    @Excel(name = "完整的订单JSON")
    private JSONObject rawData;



    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("orderNo", getOrderNo())
            .append("packageName", getPackageName())
            .append("rawData", getRawData())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
