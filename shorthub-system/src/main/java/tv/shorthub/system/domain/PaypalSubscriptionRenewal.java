package tv.shorthub.system.domain;

import java.math.BigDecimal;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import tv.shorthub.common.annotation.Excel;
import lombok.Data;
import tv.shorthub.common.core.domain.BaseEntity;

/**
 * PayPal订阅续订记录对象 paypal_subscription_renewal
 * 
 * <AUTHOR>
 * @date 2025-06-12
 */
@Data
public class PaypalSubscriptionRenewal extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @TableId(type = IdType.AUTO)
    private Long id;

    /** PayPal订阅ID */
    @Excel(name = "PayPal订阅ID")
    private String subscriptionId;

    /** 订单号 */
    @Excel(name = "订单号")
    private String orderNo;

    /** 订阅计划ID */
    @Excel(name = "订阅计划ID")
    private String planId;

    /** 续订状态：SUCCESS-成功，FAILED-失败，PENDING-处理中 */
    @Excel(name = "续订状态：SUCCESS-成功，FAILED-失败，PENDING-处理中")
    private String status;

    /** 续订金额 */
    @Excel(name = "续订金额")
    private BigDecimal amount;

    /** 货币类型 */
    @Excel(name = "货币类型")
    private String currency;

    /** 当前周期数 */
    @Excel(name = "当前周期数")
    private Long cycleNumber;

    /** 总周期数 */
    @Excel(name = "总周期数")
    private Long totalCycles;

    /** 下次续订时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "下次续订时间", width = 60, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date nextBillingTime;

    /** 续订时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "续订时间", width = 60, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date renewalTime;

    /** PayPal原始响应数据 */
    @Excel(name = "PayPal原始响应数据")
    private String rawData;

    /** 错误信息 */
    @Excel(name = "错误信息")
    private String errorMessage;



    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("subscriptionId", getSubscriptionId())
            .append("orderNo", getOrderNo())
            .append("planId", getPlanId())
            .append("status", getStatus())
            .append("amount", getAmount())
            .append("currency", getCurrency())
            .append("cycleNumber", getCycleNumber())
            .append("totalCycles", getTotalCycles())
            .append("nextBillingTime", getNextBillingTime())
            .append("renewalTime", getRenewalTime())
            .append("rawData", getRawData())
            .append("errorMessage", getErrorMessage())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
