package tv.shorthub.system.domain;

import com.alibaba.fastjson2.JSONObject;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import tv.shorthub.common.annotation.Excel;
import lombok.Data;
import tv.shorthub.common.core.domain.BaseEntity;

/**
 * 会员权限对象 app_user_level_rule
 * 
 * <AUTHOR>
 * @date 2025-05-14
 */
@Data
public class AppUserLevelRule extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 会员级别 */
    @Excel(name = "会员级别")
    private String level;

    /** 规则编码 */
    @Excel(name = "规则编码")
    private String ruleCode;

    /** 规则描述 */
    @Excel(name = "规则描述")
    private String ruleDesc;

    /** 规则参数 */
    @Excel(name = "规则参数")
    private JSONObject ruleParams;



    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("level", getLevel())
            .append("ruleCode", getRuleCode())
            .append("ruleDesc", getRuleDesc())
            .append("ruleParams", getRuleParams())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
