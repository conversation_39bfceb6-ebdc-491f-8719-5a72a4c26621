package tv.shorthub.system.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import tv.shorthub.common.annotation.Excel;
import tv.shorthub.common.core.domain.BaseEntity;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 剧目-类型关联对象 app_drama_genres
 * 
 * <AUTHOR>
 * @date 2025-05-06
 */
@Data
@TableName("app_drama_genres")
public class AppDramaGenres extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @TableId
    private Long id;

    /** 剧目ID */
    @Excel(name = "剧目ID")
    private String dramaId;

    /** 类型ID */
    @Excel(name = "类型ID")
    private Long genreId;

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("dramaId", getDramaId())
            .append("genreId", getGenreId())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
} 