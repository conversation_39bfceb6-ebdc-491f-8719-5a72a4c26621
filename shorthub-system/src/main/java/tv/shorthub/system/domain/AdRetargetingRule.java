package tv.shorthub.system.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import tv.shorthub.common.annotation.Excel;
import lombok.Data;
import tv.shorthub.common.core.domain.BaseEntity;

/**
 * 回传规则对象 ad_retargeting_rule
 * 
 * <AUTHOR>
 * @date 2025-05-23
 */
@Data
public class AdRetargetingRule extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 关联的事件ID */
    @Excel(name = "关联的事件ID")
    private Long eventId;

    /** 规则名称 */
    @Excel(name = "规则名称")
    private String ruleName;

    /** 规则类型：1-时间规则 2-条件规则 3-频率规则 */
    @Excel(name = "规则类型：1-时间规则 2-条件规则 3-频率规则")
    private Long ruleType;

    /** 规则参数(JSON格式) */
    @Excel(name = "规则参数(JSON格式)")
    private String ruleParams;

    /** 优先级 */
    @Excel(name = "优先级")
    private Long priority;

    /** 状态：0-禁用 1-启用 */
    @Excel(name = "状态：0-禁用 1-启用")
    private Boolean status;



    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("eventId", getEventId())
            .append("ruleName", getRuleName())
            .append("ruleType", getRuleType())
            .append("ruleParams", getRuleParams())
            .append("priority", getPriority())
            .append("status", getStatus())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .append("createBy", getCreateBy())
            .append("updateBy", getUpdateBy())
            .toString();
    }
}
