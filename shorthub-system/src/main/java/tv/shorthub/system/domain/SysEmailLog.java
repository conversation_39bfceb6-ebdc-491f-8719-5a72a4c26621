package tv.shorthub.system.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import tv.shorthub.common.annotation.Excel;
import lombok.Data;
import tv.shorthub.common.core.domain.BaseEntity;

/**
 * 邮件发送日志对象 sys_email_log
 * 
 * <AUTHOR>
 * @date 2025-06-09
 */
@Data
public class SysEmailLog extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 发件人邮箱 */
    @Excel(name = "发件人邮箱")
    private String fromEmail;

    /** 收件人邮箱 */
    @Excel(name = "收件人邮箱")
    private String toEmail;

    /** 订单号 */
    @Excel(name = "订单号")
    private String orderId;

    /** 邮件主题 */
    @Excel(name = "邮件主题")
    private String subject;

    /** 邮件内容 */
    @Excel(name = "邮件内容")
    private String content;

    /** 邮件服务商返回的邮件ID */
    @Excel(name = "邮件服务商返回的邮件ID")
    private String emailId;

    /** 发送状态（0成功 1失败） */
    @Excel(name = "发送状态", readConverterExp = "0=成功,1=失败")
    private String status;

    /** 错误信息 */
    @Excel(name = "错误信息")
    private String errorMessage;

    /** 响应内容 */
    @Excel(name = "响应内容")
    private String responseBody;



    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("fromEmail", getFromEmail())
            .append("toEmail", getToEmail())
            .append("orderId", getOrderId())
            .append("subject", getSubject())
            .append("content", getContent())
            .append("emailId", getEmailId())
            .append("status", getStatus())
            .append("errorMessage", getErrorMessage())
            .append("responseBody", getResponseBody())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
