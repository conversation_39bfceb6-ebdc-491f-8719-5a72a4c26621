package tv.shorthub.system.domain;

import java.util.Date;
import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import tv.shorthub.common.annotation.Excel;
import lombok.Data;
import tv.shorthub.common.core.domain.BaseEntity;

/**
 * 虚拟货币钱包地址对象 crypto_wallet
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
@Data
public class CryptoWallet extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 钱包地址 */
    @Excel(name = "钱包地址")
    private String address;

    /** 区块链类型(tron,base,solana) */
    @Excel(name = "区块链类型(tron,base,solana)")
    private String blockchain;

    @Excel(name = "收款单位")
    private String contractAddress;

    /** 私钥(加密存储) */
    @Excel(name = "私钥(加密存储)")
    private String privateKey;

    /** 助记词(加密存储) */
    @Excel(name = "助记词(加密存储)")
    private String mnemonic;

    /** 钱包状态(0-可用,1-已分配,2-已禁用) */
    @Excel(name = "钱包状态(0-可用,1-已分配,2-已禁用)")
    private Integer status;

    /** 分配给的用户ID */
    @Excel(name = "分配给的用户ID")
    private String assignedUserId;

    /** 分配给的用户ID */
    @Excel(name = "分配给的用户ID")
    private String assignedOrderNo;

    /** 钱包版本 */
    @Excel(name = "钱包版本")
    private String version;

    /** 分配时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "分配时间", width = 60, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date assignedTime;

    /** 最后使用时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "最后使用时间", width = 60, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date lastUsedTime;

    /** 钱包标签 */
    @Excel(name = "钱包标签")
    private String walletTag;

    /** 是否为热钱包(0-冷钱包,1-热钱包) */
    @Excel(name = "是否为热钱包(0-冷钱包,1-热钱包)")
    private Boolean isHotWallet;

    /** 扩展信息 */
    @Excel(name = "扩展信息")
    private JSONObject extendData;



    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("address", getAddress())
            .append("blockchain", getBlockchain())
            .append("privateKey", getPrivateKey())
            .append("mnemonic", getMnemonic())
            .append("status", getStatus())
            .append("assignedUserId", getAssignedUserId())
            .append("assignedTime", getAssignedTime())
            .append("lastUsedTime", getLastUsedTime())
            .append("walletTag", getWalletTag())
            .append("isHotWallet", getIsHotWallet())
            .append("extendData", getExtendData())
            .append("remark", getRemark())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
