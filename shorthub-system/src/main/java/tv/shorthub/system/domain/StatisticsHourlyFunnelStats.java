package tv.shorthub.system.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import tv.shorthub.common.annotation.Excel;
import lombok.Data;
import tv.shorthub.common.core.domain.BaseEntity;

/**
 * 每小时漏斗分析统计对象 statistics_hourly_funnel_stats
 * 
 * <AUTHOR>
 * @date 2025-07-07
 */
@Data
public class StatisticsHourlyFunnelStats extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 统计小时 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "统计小时", width = 60, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date statHour;

    /** 应用ID */
    @Excel(name = "应用ID")
    private String appid;

    /** 推广链接ID */
    @Excel(name = "推广链接ID")
    private String tfid;

    /** 访问人数（UV） */
    @Excel(name = "访问人数", readConverterExp = "U=V")
    private Long uvCount;

    /** 观看人数 */
    @Excel(name = "观看人数")
    private Long watchCount;

    /** 看到付费集人数 */
    @Excel(name = "看到付费集人数")
    private Long paidEpisodeCount;

    /** 创建订单人数 */
    @Excel(name = "创建订单人数")
    private Long orderUserCount;

    /** 支付人数 */
    @Excel(name = "支付人数")
    private Long paidUserCount;



    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("statHour", getStatHour())
            .append("appid", getAppid())
            .append("tfid", getTfid())
            .append("uvCount", getUvCount())
            .append("watchCount", getWatchCount())
            .append("paidEpisodeCount", getPaidEpisodeCount())
            .append("orderUserCount", getOrderUserCount())
            .append("paidUserCount", getPaidUserCount())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
