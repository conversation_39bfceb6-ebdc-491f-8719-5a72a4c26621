package tv.shorthub.system.domain;

import java.math.BigDecimal;
import com.alibaba.fastjson2.JSONObject;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import tv.shorthub.common.annotation.Excel;
import lombok.Data;
import tv.shorthub.common.core.domain.BaseEntity;

/**
 * payermax支付日志对象 payermax_payment_log
 * 
 * <AUTHOR>
 * @date 2025-06-16
 */
@Data
public class PayermaxPaymentLog extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 关联的本地订单号 */
    @Excel(name = "关联的本地订单号")
    private String orderNo;

    /** Payermax支付ID */
    @Excel(name = "Payermax支付ID")
    private String tradeToken;

    /** 商户号 */
    @Excel(name = "商户号")
    private String mcnId;

    /** 用户ID */
    @Excel(name = "用户ID")
    private String userId;

    /** payermax订单状态 */
    @Excel(name = "payermax订单状态")
    private String status;

    /** 支付金额 */
    @Excel(name = "支付金额")
    private BigDecimal totalAmount;

    /** 币种 */
    @Excel(name = "币种")
    private String currency;

    /** 商品描述 */
    @Excel(name = "商品描述")
    private String description;

    /** return_url */
    @Excel(name = "return_url")
    private String returnUrl;

    /** cancel_url */
    @Excel(name = "cancel_url")
    private String cancelUrl;

    /** redirectUrl */
    @Excel(name = "redirectUrl")
    private String redirectUrl;

    /** payment_method */
    @Excel(name = "payment_method")
    private String paymentMethod;

    /** 原始信息 */
    @Excel(name = "原始信息")
    private JSONObject rawData;

    /** 详细的订单JSON */
    @Excel(name = "详细的订单JSON")
    private JSONObject detailRawData;

    public void setDetailRawData(JSONObject detailRawData) {
        this.detailRawData = detailRawData;
    }

    /** 关联用户ID */
    @Excel(name = "关联用户ID")
    private String relatedUserId;



    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("orderNo", getOrderNo())
            .append("tradeToken", getTradeToken())
            .append("mcnId", getMcnId())
            .append("userId", getUserId())
            .append("status", getStatus())
            .append("totalAmount", getTotalAmount())
            .append("currency", getCurrency())
            .append("description", getDescription())
            .append("returnUrl", getReturnUrl())
            .append("cancelUrl", getCancelUrl())
            .append("paymentMethod", getPaymentMethod())
            .append("rawData", getRawData())
            .append("relatedUserId", getRelatedUserId())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
