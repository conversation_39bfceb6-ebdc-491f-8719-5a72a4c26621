package tv.shorthub.system.domain;

import tv.shorthub.common.annotation.Excel;
import tv.shorthub.common.core.domain.BaseEntity;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 国家语言配置对象 app_countries_languages
 *
 * <AUTHOR>
 * @date 2025-05-06
 */
@Data
public class AppCountriesLanguages extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 国家编码（ISO 3166-1 Alpha-2） */
    @Excel(name = "地区编码", readConverterExp = "I=SO,3=166-1,A=lpha-2")
    private String countryCode;

    @Excel(name = "地区名称", readConverterExp = "I=SO,3=166-1,A=lpha-2")
    private String countryName;

    /** 语言（ISO 639-1 或其他标准）  */
    @Excel(name = "语言", readConverterExp = "I=SO,6=39-1,或=其他标准")
    private String languageCode;

    /** 国家的默认语言 */
    @Excel(name = "国家的默认语言")
    private Integer isDefault;



    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("countryCode", getCountryCode())
            .append("languageCode", getLanguageCode())
            .append("isDefault", getIsDefault())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
