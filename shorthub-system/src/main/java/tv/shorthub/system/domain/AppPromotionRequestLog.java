package tv.shorthub.system.domain;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.annotation.TableId;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import tv.shorthub.common.annotation.Excel;
import lombok.Data;
import tv.shorthub.common.core.domain.BaseEntity;

/**
 * promotion访问日志对象 app_promotion_request_log
 *
 * <AUTHOR>
 * @date 2025-05-28
 */
@Data
public class AppPromotionRequestLog extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /**  */
    @TableId
    private Long id;

    /** appid */
    @Excel(name = "appid")
    private String appid;

    /** tid */
    @Excel(name = "tid")
    private String tid;

    /** ip */
    @Excel(name = "ip")
    private String ip;

    /** sid */
    @Excel(name = "sid")
    private String sid;

    /** deviceId */
    @Excel(name = "deviceId")
    private String deviceId;

    /** userAgent */
    @Excel(name = "userAgent")
    private String userAgent;

    /** 请求头 */
    @Excel(name = "请求头")
    private JSONObject headers;

    /** 参数 */
    @Excel(name = "参数")
    private JSONObject reqParams;

    /** 扩展字段 */
    @Excel(name = "扩展字段")
    private JSONObject extendJson;

    /** body */
    @Excel(name = "body")
    private String body;

    /** 处理状态 */
    @Excel(name = "处理状态")
    private String state;


    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("tid", getTid())
            .append("ip", getIp())
            .append("sid", getSid())
            .append("userAgent", getUserAgent())
            .append("headers", getHeaders())
            .append("reqParams", getReqParams())
            .append("body", getBody())
            .append("state", getState())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
