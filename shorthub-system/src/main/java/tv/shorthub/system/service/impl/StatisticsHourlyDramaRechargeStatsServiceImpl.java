package tv.shorthub.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import tv.shorthub.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tv.shorthub.system.mapper.StatisticsHourlyDramaRechargeStatsMapper;
import tv.shorthub.system.domain.StatisticsHourlyDramaRechargeStats;
import tv.shorthub.system.domain.dto.StatisticsHourlyDramaRechargeStatsDTO;
import tv.shorthub.system.domain.dto.StatisticsHourlyDramaRechargeStatsDetailDTO;
import tv.shorthub.system.service.IStatisticsHourlyDramaRechargeStatsService;
import tv.shorthub.common.core.service.BaseService;
import java.util.Date;
import java.util.List;

/**
 * 小时级剧目充值统计Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-15
 */
@Service
public class StatisticsHourlyDramaRechargeStatsServiceImpl extends BaseService<StatisticsHourlyDramaRechargeStats> implements IStatisticsHourlyDramaRechargeStatsService
{
    @Autowired
    private StatisticsHourlyDramaRechargeStatsMapper statisticsHourlyDramaRechargeStatsMapper;

    @Override
    public StatisticsHourlyDramaRechargeStatsMapper getMapper() {
        return statisticsHourlyDramaRechargeStatsMapper;
    }

    /**
     * 查询小时级剧目充值统计
     *
     * @param id 小时级剧目充值统计主键
     * @return 小时级剧目充值统计
     */
    @Override
    public StatisticsHourlyDramaRechargeStats getById(Long id)
    {
        return statisticsHourlyDramaRechargeStatsMapper.selectById(id);
    }

    /**
     * 查询小时级剧目充值统计列表
     *
     * @param query 小时级剧目充值统计
     * @return 小时级剧目充值统计
     */
    @Override
    public List<StatisticsHourlyDramaRechargeStats> selectList(StatisticsHourlyDramaRechargeStats query)
    {
        return statisticsHourlyDramaRechargeStatsMapper.selectList(new QueryWrapper<>(query));
    }


    /**
     * 查询小时级剧目充值统计数据汇总
     *
     * @param query 小时级剧目充值统计
     * @return 小时级剧目充值统计
     */
    @Override
    public StatisticsHourlyDramaRechargeStats getSummary(StatisticsHourlyDramaRechargeStats query)
    {
        return statisticsHourlyDramaRechargeStatsMapper.getSummary(query);
    }



    /**
     * 查询自定义分析数据
     *
     * @param query 小时级剧目充值统计
     * @return 小时级剧目充值统计
     */
    @Override
    public List<StatisticsHourlyDramaRechargeStats> summary(SummaryRequest query)
    {
        return statisticsHourlyDramaRechargeStatsMapper.summary(query);
    }

    @Override
    public StatisticsHourlyDramaRechargeStats allSummary(SummaryRequest query)
    {
        return statisticsHourlyDramaRechargeStatsMapper.allSummary(query);
    }

    /**
     * 新增小时级剧目充值统计
     *
     * @param statisticsHourlyDramaRechargeStats 小时级剧目充值统计
     * @return 结果
     */
    @Override
    public int insert(StatisticsHourlyDramaRechargeStats statisticsHourlyDramaRechargeStats)
    {
        statisticsHourlyDramaRechargeStats.setCreateTime(DateUtils.getNowDate());
        return statisticsHourlyDramaRechargeStatsMapper.insert(statisticsHourlyDramaRechargeStats);
    }

    /**
     * 修改小时级剧目充值统计
     *
     * @param statisticsHourlyDramaRechargeStats 小时级剧目充值统计
     * @return 结果
     */
    @Override
    public int update(StatisticsHourlyDramaRechargeStats statisticsHourlyDramaRechargeStats)
    {
        statisticsHourlyDramaRechargeStats.setUpdateTime(DateUtils.getNowDate());
        return statisticsHourlyDramaRechargeStatsMapper.updateById(statisticsHourlyDramaRechargeStats);
    }

    /**
     * 批量删除小时级剧目充值统计
     *
     * @param ids 需要删除的小时级剧目充值统计主键
     * @return 结果
     */
    @Override
    public int deleteByIds(List<Long> ids)
    {
        return statisticsHourlyDramaRechargeStatsMapper.deleteBatchIds(ids);
    }

    /**
     * 删除小时级剧目充值统计信息
     *
     * @param id 小时级剧目充值统计主键
     * @return 结果
     */
    @Override
    public int deleteById(Long id)
    {
        return statisticsHourlyDramaRechargeStatsMapper.deleteById(id);
    }

    /**
     * 根据时间范围获取剧目充值统计汇总数据
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param appid 应用ID
     * @param tfid 推广链接ID
     * @param contentId 内容ID
     * @param dramaId 剧目ID
     * @return 汇总统计数据
     */
    @Override
    public StatisticsHourlyDramaRechargeStats getRangeSummary(Date startTime, Date endTime, String appid, String tfid, String contentId, String dramaId)
    {
        return statisticsHourlyDramaRechargeStatsMapper.getRangeSummary(startTime, endTime, appid, tfid, contentId, dramaId);
    }

    /**
     * 根据时间范围获取剧目充值统计详情数据（包含解锁剧集详情）
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param appid 应用ID
     * @param tfid 推广链接ID
     * @param contentId 内容ID
     * @param dramaId 剧目ID
     * @return 详情统计数据
     */
    @Override
    public StatisticsHourlyDramaRechargeStatsDetailDTO getRangeSummaryWithDetails(Date startTime, Date endTime, String appid, String tfid, String contentId, String dramaId)
    {
        // 获取基础汇总数据
        StatisticsHourlyDramaRechargeStats baseSummary = statisticsHourlyDramaRechargeStatsMapper.getRangeSummary(startTime, endTime, appid, tfid, contentId, dramaId);
        
        // 创建详情DTO
        StatisticsHourlyDramaRechargeStatsDetailDTO detailDTO = new StatisticsHourlyDramaRechargeStatsDetailDTO();
        
        // 复制基础数据
        if (baseSummary != null) {
            detailDTO.setId(baseSummary.getId());
            detailDTO.setStatHour(baseSummary.getStatHour());
            detailDTO.setContentId(baseSummary.getContentId());
            detailDTO.setDramaId(baseSummary.getDramaId());
            detailDTO.setAppid(baseSummary.getAppid());
            detailDTO.setTfid(baseSummary.getTfid());
            detailDTO.setRechargeUserCount(baseSummary.getRechargeUserCount());
            detailDTO.setRechargeOrderCount(baseSummary.getRechargeOrderCount());
            detailDTO.setRechargeAmount(baseSummary.getRechargeAmount());
            detailDTO.setCoinRechargeCount(baseSummary.getCoinRechargeCount());
            detailDTO.setCoinRechargeAmount(baseSummary.getCoinRechargeAmount());
            detailDTO.setSubscriptionRechargeCount(baseSummary.getSubscriptionRechargeCount());
            detailDTO.setSubscriptionRechargeAmount(baseSummary.getSubscriptionRechargeAmount());
            detailDTO.setConsumptionUserCount(baseSummary.getConsumptionUserCount());
            detailDTO.setConsumptionOrderCount(baseSummary.getConsumptionOrderCount());
            detailDTO.setCoinConsumptionAmount(baseSummary.getCoinConsumptionAmount());
            detailDTO.setUnlockEpisodeCount(baseSummary.getUnlockEpisodeCount());
            detailDTO.setRechargeToConsumptionRate(baseSummary.getRechargeToConsumptionRate());
            detailDTO.setAvgRechargeAmount(baseSummary.getAvgRechargeAmount());
            detailDTO.setAvgConsumptionAmount(baseSummary.getAvgConsumptionAmount());
            detailDTO.setCreateBy(baseSummary.getCreateBy());
            detailDTO.setCreateTime(baseSummary.getCreateTime());
            detailDTO.setUpdateBy(baseSummary.getUpdateBy());
            detailDTO.setUpdateTime(baseSummary.getUpdateTime());
        }
        
        // 获取解锁剧集详情（不再分页）
        List<StatisticsHourlyDramaRechargeStatsDetailDTO.UnlockEpisodeDetail> unlockDetails = 
            statisticsHourlyDramaRechargeStatsMapper.getUnlockEpisodeDetails(startTime, endTime, appid, tfid, contentId, dramaId);
        detailDTO.setUnlockEpisodeDetails(unlockDetails);
        
        // 设置总数为实际返回的数据量
        Long unlockDetailsTotal = (long) unlockDetails.size();
        detailDTO.setUnlockEpisodeDetailsTotal(unlockDetailsTotal);
        
        return detailDTO;
    }

}
