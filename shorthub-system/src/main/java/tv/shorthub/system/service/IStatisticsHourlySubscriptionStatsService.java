package tv.shorthub.system.service;

import tv.shorthub.system.mapper.StatisticsHourlySubscriptionStatsMapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.Date;
import java.util.List;
import tv.shorthub.system.domain.StatisticsHourlySubscriptionStats;
import tv.shorthub.common.core.service.IBaseService;
import org.apache.ibatis.annotations.Param;
import tv.shorthub.system.dto.SubscriptionSummaryDto;

/**
 * 每小时订单与订阅统计Service接口
 *
 * <AUTHOR>
 * @date 2025-06-29
 */
public interface IStatisticsHourlySubscriptionStatsService extends IBaseService<StatisticsHourlySubscriptionStats>
{
    /**
     * 查询每小时订单与订阅统计
     *
     * @param id 每小时订单与订阅统计主键
     * @return 每小时订单与订阅统计
     */
    public StatisticsHourlySubscriptionStats getById(Long id);

    /**
     * 查询每小时订单与订阅统计数据汇总
     *
     * @param query 每小时订单与订阅统计
     * @return 每小时订单与订阅统计数据汇总
     */
    public StatisticsHourlySubscriptionStats getSummary(StatisticsHourlySubscriptionStats query);

    /**
     * 查询每小时订单与订阅统计列表
     *
     * @param query 每小时订单与订阅统计
     * @return 每小时订单与订阅统计集合
     */
    public List<StatisticsHourlySubscriptionStats> selectList(StatisticsHourlySubscriptionStats query);

    /**
     * 新增每小时订单与订阅统计
     *
     * @param statisticsHourlySubscriptionStats 每小时订单与订阅统计
     * @return 结果
     */
    public int insert(StatisticsHourlySubscriptionStats statisticsHourlySubscriptionStats);

    /**
     * 修改每小时订单与订阅统计
     *
     * @param statisticsHourlySubscriptionStats 每小时订单与订阅统计
     * @return 结果
     */
    public int update(StatisticsHourlySubscriptionStats statisticsHourlySubscriptionStats);

    /**
     * 批量删除每小时订单与订阅统计
     *
     * @param ids 需要删除的每小时订单与订阅统计主键集合
     * @return 结果
     */
    public int deleteByIds(List<Long> ids);

    /**
     * 删除每小时订单与订阅统计信息
     *
     * @param id 每小时订单与订阅统计主键
     * @return 结果
     */
    public int deleteById(Long id);


    /**
     * 查询自定义分析数据
     *
     * @param query 每小时订单与订阅统计
     * @return 每小时订单与订阅统计集合
     */
    public List<StatisticsHourlySubscriptionStats> summary(SummaryRequest query);

    StatisticsHourlySubscriptionStats allSummary(SummaryRequest query);

    StatisticsHourlySubscriptionStatsMapper getMapper();

    /**
     * 根据时间范围获取订阅统计汇总数据
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param appid 应用ID
     * @param tfid 推广链接ID
     * @return 包含待扣款金额的汇总统计DTO
     */
    SubscriptionSummaryDto getRangeSummary(@Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("appid") String appid, @Param("tfid") String tfid);

    /**
     * 根据时间范围查询订阅统计列表
     *
     * @param query 查询条件
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计数据列表
     */
    List<StatisticsHourlySubscriptionStats> selectListByTimeRange(StatisticsHourlySubscriptionStats query, Date startTime, Date endTime);

    /**
     * 根据 tfid 列表获取汇总数据
     *
     * @param tfids tfid 列表
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @return 汇总统计数据
     */
    StatisticsHourlySubscriptionStats getSummaryByTfids(@Param("tfids") List<String> tfids, @Param("startTime") Date startTime, @Param("endTime") Date endTime);



    /**
     * 根据创建者获取其所有子账号的时间范围汇总数据
     *
     * @param creatorName 创建者用户名
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 包含待扣款金额的汇总统计DTO
     */
    SubscriptionSummaryDto getRangeSummaryByCreator(@Param("creatorName") String creatorName, @Param("startTime") Date startTime, @Param("endTime") Date endTime);
}
