package tv.shorthub.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import tv.shorthub.common.core.service.BaseService;
import tv.shorthub.common.utils.DateUtils;
import tv.shorthub.system.domain.AppDramaGenres;
import tv.shorthub.system.mapper.AppDramaGenresMapper;
import tv.shorthub.system.service.IAppDramaGenresService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 剧目-类型关联Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-05-06
 */
@Service
public class AppDramaGenresServiceImpl extends BaseService<AppDramaGenres> implements IAppDramaGenresService
{
    @Autowired
    private AppDramaGenresMapper appDramaGenresMapper;

    @Override
    public AppDramaGenresMapper getMapper() {
        return appDramaGenresMapper;
    }

    /**
     * 查询剧目-类型关联
     * 
     * @param id 剧目-类型关联主键
     * @return 剧目-类型关联
     */
    @Override
    public AppDramaGenres getById(Long id)
    {
        return appDramaGenresMapper.selectById(id);
    }

    /**
     * 查询剧目-类型关联列表
     * 
     * @param query 剧目-类型关联
     * @return 剧目-类型关联
     */
    @Override
    public List<AppDramaGenres> selectList(AppDramaGenres query)
    {
        return appDramaGenresMapper.selectList(new QueryWrapper<>(query));
    }

    /**
     * 新增剧目-类型关联
     * 
     * @param appDramaGenres 剧目-类型关联
     * @return 结果
     */
    @Override
    public int insert(AppDramaGenres appDramaGenres)
    {
        appDramaGenres.setCreateTime(DateUtils.getNowDate());
        return appDramaGenresMapper.insert(appDramaGenres);
    }

    /**
     * 批量新增剧目-类型关联
     * 
     * @param dramaGenresList 剧目-类型关联列表
     * @return 结果
     */
    @Override
    public int batchInsert(List<AppDramaGenres> dramaGenresList)
    {
        return appDramaGenresMapper.batchInsertOrUpdate(dramaGenresList);
    }

    /**
     * 修改剧目-类型关联
     * 
     * @param appDramaGenres 剧目-类型关联
     * @return 结果
     */
    @Override
    public int update(AppDramaGenres appDramaGenres)
    {
        appDramaGenres.setUpdateTime(DateUtils.getNowDate());
        return appDramaGenresMapper.updateById(appDramaGenres);
    }

    /**
     * 批量删除剧目-类型关联
     * 
     * @param ids 需要删除的剧目-类型关联主键
     * @return 结果
     */
    @Override
    public int deleteByIds(List<Long> ids)
    {
        return appDramaGenresMapper.deleteBatchIds(ids);
    }

    /**
     * 删除剧目-类型关联信息
     * 
     * @param id 剧目-类型关联主键
     * @return 结果
     */
    @Override
    public int deleteById(Long id)
    {
        return appDramaGenresMapper.deleteById(id);
    }

    /**
     * 通过剧目ID删除剧目-类型关联
     * 
     * @param dramaId 剧目ID
     * @return 结果
     */
    @Override
    public int deleteByDramaId(String dramaId)
    {
        return appDramaGenresMapper.delete(new QueryWrapper<AppDramaGenres>().eq("drama_id", dramaId));
    }

    /**
     * 获取剧目关联的类型ID列表
     * 
     * @param dramaId 剧目ID
     * @return 类型ID列表
     */
    @Override
    public List<Long> getGenreIdsByDramaId(String dramaId)
    {
        List<AppDramaGenres> dramaIds = appDramaGenresMapper.selectList(new QueryWrapper<AppDramaGenres>().eq("drama_id", dramaId));
        return dramaIds.stream().map(AppDramaGenres::getGenreId).collect(Collectors.toList());
    }
} 