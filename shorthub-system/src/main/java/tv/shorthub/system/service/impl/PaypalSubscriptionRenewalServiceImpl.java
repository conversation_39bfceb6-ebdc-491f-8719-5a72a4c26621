package tv.shorthub.system.service.impl;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import tv.shorthub.common.core.domain.SummaryRequest;

import java.text.ParseException;
import java.util.Date;
import java.util.List;
import tv.shorthub.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tv.shorthub.system.mapper.PaypalSubscriptionRenewalMapper;
import tv.shorthub.system.domain.PaypalSubscriptionRenewal;
import tv.shorthub.system.service.IPaypalSubscriptionRenewalService;
import tv.shorthub.common.core.service.BaseService;

/**
 * PayPal订阅续订记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-12
 */
@Service
@Slf4j
public class PaypalSubscriptionRenewalServiceImpl extends BaseService<PaypalSubscriptionRenewal> implements IPaypalSubscriptionRenewalService
{
    @Autowired
    private PaypalSubscriptionRenewalMapper paypalSubscriptionRenewalMapper;

    @Override
    public PaypalSubscriptionRenewalMapper getMapper() {
        return paypalSubscriptionRenewalMapper;
    }

    /**
     * 查询PayPal订阅续订记录
     *
     * @param id PayPal订阅续订记录主键
     * @return PayPal订阅续订记录
     */
    @Override
    public PaypalSubscriptionRenewal getById(Long id)
    {
        return paypalSubscriptionRenewalMapper.selectById(id);
    }

    /**
     * 查询PayPal订阅续订记录列表
     *
     * @param query PayPal订阅续订记录
     * @return PayPal订阅续订记录
     */
    @Override
    public List<PaypalSubscriptionRenewal> selectList(PaypalSubscriptionRenewal query)
    {
        return paypalSubscriptionRenewalMapper.selectList(new QueryWrapper<>(query));
    }


    /**
     * 查询PayPal订阅续订记录数据汇总
     *
     * @param query PayPal订阅续订记录
     * @return PayPal订阅续订记录
     */
    @Override
    public PaypalSubscriptionRenewal getSummary(PaypalSubscriptionRenewal query)
    {
        return paypalSubscriptionRenewalMapper.getSummary(query);
    }



    /**
     * 查询自定义分析数据
     *
     * @param query PayPal订阅续订记录
     * @return PayPal订阅续订记录
     */
    @Override
    public List<PaypalSubscriptionRenewal> summary(SummaryRequest query)
    {
        return paypalSubscriptionRenewalMapper.summary(query);
    }

    @Override
    public PaypalSubscriptionRenewal allSummary(SummaryRequest query)
    {
        return paypalSubscriptionRenewalMapper.allSummary(query);
    }

    /**
     * 新增PayPal订阅续订记录
     *
     * @param paypalSubscriptionRenewal PayPal订阅续订记录
     * @return 结果
     */
    @Override
    public int insert(PaypalSubscriptionRenewal paypalSubscriptionRenewal)
    {
        paypalSubscriptionRenewal.setCreateTime(DateUtils.getNowDate());
        return paypalSubscriptionRenewalMapper.insert(paypalSubscriptionRenewal);
    }


    @Override
    public int insert(String orderNo, JSONObject rawData, Long cyclesCompleted, Long totalCycles) throws ParseException {
        JSONObject billingInfo = rawData.getJSONObject("billing_info");
        JSONObject lastPayment = billingInfo.getJSONObject("last_payment");

        PaypalSubscriptionRenewal renewal = new PaypalSubscriptionRenewal();
        renewal.setSubscriptionId(rawData.getString("id"));
        renewal.setOrderNo(orderNo);
        renewal.setPlanId(rawData.getString("plan_id"));
        renewal.setStatus(rawData.getString("status"));
        renewal.setAmount(lastPayment.getJSONObject("amount").getBigDecimal("value"));
        renewal.setCurrency(lastPayment.getJSONObject("amount").getString("currency_code"));
        renewal.setCycleNumber(cyclesCompleted);
        renewal.setTotalCycles(totalCycles);
        renewal.setNextBillingTime(DateUtils.parseDate(billingInfo.getString("next_billing_time"), "yyyy-MM-dd'T'HH:mm:ss'Z'"));
        renewal.setRenewalTime(DateUtils.parseDate(billingInfo.getJSONObject("last_payment").getString("time"), "yyyy-MM-dd'T'HH:mm:ss'Z'"));
        renewal.setRawData(rawData.toJSONString());
        renewal.setUpdateTime(new Date());
        renewal.setUpdateBy("system");
        renewal.setCreateTime(new Date());
        renewal.setUpdateBy("system");

        try {
            return insert(renewal);
        } catch (Exception exception) {
            // 可能会存在重复调用的问题，这里如果出错直接拦截，避免对外部造成影响
            log.error("新增订阅日志数据失败:{}", exception.getMessage());
            return 0;
        }
    }

    /**
     * 修改PayPal订阅续订记录
     *
     * @param paypalSubscriptionRenewal PayPal订阅续订记录
     * @return 结果
     */
    @Override
    public int update(PaypalSubscriptionRenewal paypalSubscriptionRenewal)
    {
        paypalSubscriptionRenewal.setUpdateTime(DateUtils.getNowDate());
        return paypalSubscriptionRenewalMapper.updateById(paypalSubscriptionRenewal);
    }

    /**
     * 批量删除PayPal订阅续订记录
     *
     * @param ids 需要删除的PayPal订阅续订记录主键
     * @return 结果
     */
    @Override
    public int deleteByIds(List<Long> ids)
    {
        return paypalSubscriptionRenewalMapper.deleteBatchIds(ids);
    }

    /**
     * 删除PayPal订阅续订记录信息
     *
     * @param id PayPal订阅续订记录主键
     * @return 结果
     */
    @Override
    public int deleteById(Long id)
    {
        return paypalSubscriptionRenewalMapper.deleteById(id);
    }
}
