package tv.shorthub.system.service;

import tv.shorthub.system.mapper.PaypalWebhookLogMapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import tv.shorthub.system.domain.PaypalWebhookLog;
import tv.shorthub.common.core.service.IBaseService;

/**
 * webhook事件日志Service接口
 *
 * <AUTHOR>
 * @date 2025-05-27
 */
public interface IPaypalWebhookLogService extends IBaseService<PaypalWebhookLog>
{
    /**
     * 查询webhook事件日志
     *
     * @param id webhook事件日志主键
     * @return webhook事件日志
     */
    public PaypalWebhookLog getById(Long id);

    /**
     * 查询webhook事件日志数据汇总
     *
     * @param query webhook事件日志
     * @return webhook事件日志数据汇总
     */
    public PaypalWebhookLog getSummary(PaypalWebhookLog query);

    /**
     * 查询webhook事件日志列表
     *
     * @param query webhook事件日志
     * @return webhook事件日志集合
     */
    public List<PaypalWebhookLog> selectList(PaypalWebhookLog query);

    /**
     * 新增webhook事件日志
     *
     * @param paypalWebhookLog webhook事件日志
     * @return 结果
     */
    public int insert(PaypalWebhookLog paypalWebhookLog);

    /**
     * 修改webhook事件日志
     *
     * @param paypalWebhookLog webhook事件日志
     * @return 结果
     */
    public int update(PaypalWebhookLog paypalWebhookLog);

    /**
     * 批量删除webhook事件日志
     *
     * @param ids 需要删除的webhook事件日志主键集合
     * @return 结果
     */
    public int deleteByIds(List<Long> ids);

    /**
     * 删除webhook事件日志信息
     *
     * @param id webhook事件日志主键
     * @return 结果
     */
    public int deleteById(Long id);


    /**
     * 查询自定义分析数据
     *
     * @param query webhook事件日志
     * @return webhook事件日志集合
     */
    public List<PaypalWebhookLog> summary(SummaryRequest query);

    PaypalWebhookLog allSummary(SummaryRequest query);

    PaypalWebhookLogMapper getMapper();
}
