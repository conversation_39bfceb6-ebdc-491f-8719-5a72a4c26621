package tv.shorthub.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import tv.shorthub.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tv.shorthub.system.mapper.PaypalDisputeMapper;
import tv.shorthub.system.domain.PaypalDispute;
import tv.shorthub.system.service.IPaypalDisputeService;
import tv.shorthub.common.core.service.BaseService;

/**
 * PayPal争议Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
@Service
public class PaypalDisputeServiceImpl extends BaseService<PaypalDispute> implements IPaypalDisputeService
{
    @Autowired
    private PaypalDisputeMapper paypalDisputeMapper;

    @Override
    public PaypalDisputeMapper getMapper() {
        return paypalDisputeMapper;
    }

    /**
     * 查询PayPal争议
     *
     * @param id PayPal争议主键
     * @return PayPal争议
     */
    @Override
    public PaypalDispute getById(Long id)
    {
        return paypalDisputeMapper.selectById(id);
    }

    /**
     * 查询PayPal争议列表
     *
     * @param query PayPal争议
     * @return PayPal争议
     */
    @Override
    public List<PaypalDispute> selectList(PaypalDispute query)
    {
        return paypalDisputeMapper.selectList(new QueryWrapper<>(query));
    }


    /**
     * 查询PayPal争议数据汇总
     *
     * @param query PayPal争议
     * @return PayPal争议
     */
    @Override
    public PaypalDispute getSummary(PaypalDispute query)
    {
        return paypalDisputeMapper.getSummary(query);
    }



    /**
     * 查询自定义分析数据
     *
     * @param query PayPal争议
     * @return PayPal争议
     */
    @Override
    public List<PaypalDispute> summary(SummaryRequest query)
    {
        return paypalDisputeMapper.summary(query);
    }

    @Override
    public PaypalDispute allSummary(SummaryRequest query)
    {
        return paypalDisputeMapper.allSummary(query);
    }

    /**
     * 新增PayPal争议
     *
     * @param paypalDispute PayPal争议
     * @return 结果
     */
    @Override
    public int insert(PaypalDispute paypalDispute)
    {
        paypalDispute.setCreateTime(DateUtils.getNowDate());
        return paypalDisputeMapper.insert(paypalDispute);
    }

    /**
     * 修改PayPal争议
     *
     * @param paypalDispute PayPal争议
     * @return 结果
     */
    @Override
    public int update(PaypalDispute paypalDispute)
    {
        paypalDispute.setUpdateTime(DateUtils.getNowDate());
        return paypalDisputeMapper.updateById(paypalDispute);
    }

    /**
     * 批量删除PayPal争议
     *
     * @param ids 需要删除的PayPal争议主键
     * @return 结果
     */
    @Override
    public int deleteByIds(List<Long> ids)
    {
        return paypalDisputeMapper.deleteBatchIds(ids);
    }

    /**
     * 删除PayPal争议信息
     *
     * @param id PayPal争议主键
     * @return 结果
     */
    @Override
    public int deleteById(Long id)
    {
        return paypalDisputeMapper.deleteById(id);
    }

    @Override
    public String findOrderNoByCaptureId(String captureId) {
        return paypalDisputeMapper.findOrderNoByCaptureId(captureId);
    }
}
