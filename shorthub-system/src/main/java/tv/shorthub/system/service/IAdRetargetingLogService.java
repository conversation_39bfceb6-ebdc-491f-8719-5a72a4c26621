package tv.shorthub.system.service;

import tv.shorthub.system.mapper.AdRetargetingLogMapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import tv.shorthub.system.domain.AdRetargetingLog;
import tv.shorthub.common.core.service.IBaseService;

/**
 * 回传记录Service接口
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
public interface IAdRetargetingLogService extends IBaseService<AdRetargetingLog>
{
    /**
     * 查询回传记录
     *
     * @param id 回传记录主键
     * @return 回传记录
     */
    public AdRetargetingLog getById(Long id);

    /**
     * 查询回传记录数据汇总
     *
     * @param query 回传记录
     * @return 回传记录数据汇总
     */
    public AdRetargetingLog getSummary(AdRetargetingLog query);

    /**
     * 查询回传记录列表
     *
     * @param query 回传记录
     * @return 回传记录集合
     */
    public List<AdRetargetingLog> selectList(AdRetargetingLog query);

    /**
     * 新增回传记录
     *
     * @param adRetargetingLog 回传记录
     * @return 结果
     */
    public int insert(AdRetargetingLog adRetargetingLog);

    /**
     * 修改回传记录
     *
     * @param adRetargetingLog 回传记录
     * @return 结果
     */
    public int update(AdRetargetingLog adRetargetingLog);

    /**
     * 批量删除回传记录
     *
     * @param ids 需要删除的回传记录主键集合
     * @return 结果
     */
    public int deleteByIds(List<Long> ids);

    /**
     * 删除回传记录信息
     *
     * @param id 回传记录主键
     * @return 结果
     */
    public int deleteById(Long id);


    /**
     * 查询自定义分析数据
     *
     * @param query 回传记录
     * @return 回传记录集合
     */
    public List<AdRetargetingLog> summary(SummaryRequest query);

    AdRetargetingLog allSummary(SummaryRequest query);

    AdRetargetingLogMapper getMapper();

    /**
     * 查询用户最近一次成功的回传记录
     *
     * @param userId 用户ID
     * @return 最近一次成功的回传记录
     */
    public AdRetargetingLog getLatestSuccessfulLog(String userId, Long eventId);

    /**
     * 查询用户在指定时间窗口内的成功回传记录数
     *
     * @param userId 用户ID
     * @return 成功回传记录数
     */
    public long countSuccessfulLogsByUserId(Long eventId, String userId);

    /**
     * 查询已回传次数
     * @param eventId
     * @param relationId
     * @return 成功回传记录数
     */
    public long countSuccessfulLogsByRelationId(Long eventId, String relationId);
}
