package tv.shorthub.system.service.impl;

import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import tv.shorthub.common.config.AppConfig;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import tv.shorthub.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tv.shorthub.common.utils.SecurityUtils;
import tv.shorthub.system.mapper.AppPromotionMapper;
import tv.shorthub.system.domain.AppPromotion;
import tv.shorthub.system.dto.PromotionOptionDto;
import tv.shorthub.system.service.IAppPromotionService;
import tv.shorthub.common.core.service.BaseService;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.stream.Collectors;

/**
 * 推广链接Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-15
 */
@Service
public class AppPromotionServiceImpl extends BaseService<AppPromotion> implements IAppPromotionService
{
    @Autowired
    private AppPromotionMapper appPromotionMapper;

    @Override
    public AppPromotionMapper getMapper() {
        return appPromotionMapper;
    }

    /**
     * 查询推广链接
     *
     * @param id 推广链接主键
     * @return 推广链接
     */
    @Override
    public AppPromotion getById(Long id)
    {
        return appPromotionMapper.selectById(id);
    }

    /**
     * 查询推广链接列表
     *
     * @param query 推广链接
     * @return 推广链接
     */
    @Override
    public List<AppPromotion> selectList(AppPromotion query)
    {
        QueryWrapper<AppPromotion> queryWrapper = new QueryWrapper<>(query);
        if (!SecurityUtils.isSystemAdmin() || (query.getParams().containsKey("onlyMe") && query.getParams().get("onlyMe").equals("true"))) {
            queryWrapper.eq("appid", SecurityUtils.getAppid());
            if (!SecurityUtils.isBusinessAdmin() || (query.getParams().containsKey("onlyMe") && query.getParams().get("onlyMe").equals("true"))) {
                queryWrapper.eq("create_by", SecurityUtils.getUsername());
            }
        }
        return appPromotionMapper.selectList(queryWrapper);
    }


    /**
     * 查询推广链接数据汇总
     *
     * @param query 推广链接
     * @return 推广链接
     */
    @Override
    public AppPromotion getSummary(AppPromotion query)
    {
        return appPromotionMapper.getSummary(query);
    }



    /**
     * 查询自定义分析数据
     *
     * @param query 推广链接
     * @return 推广链接
     */
    @Override
    public List<AppPromotion> summary(SummaryRequest query)
    {
        return appPromotionMapper.summary(query);
    }

    @Override
    public AppPromotion allSummary(SummaryRequest query)
    {
        return appPromotionMapper.allSummary(query);
    }

    /**
     * 新增推广链接
     *
     * @param appPromotion 推广链接
     * @return 结果
     */
    @Override
    public int insert(AppPromotion appPromotion)
    {
        appPromotion.setAppid(SecurityUtils.getAppid());
        appPromotion.setCreateBy(SecurityUtils.getUsername());
        appPromotion.setTfid(IdUtil.getSnowflakeNextIdStr());
        appPromotion.setCreateTime(DateUtils.getNowDate());
        appPromotion.setWebUrl(AppConfig.getDomainApi() + "/api/jump?tid=" + appPromotion.getTfid());
        return appPromotionMapper.insert(appPromotion);
    }

    /**
     * 修改推广链接
     *
     * @param appPromotion 推广链接
     * @return 结果
     */
    @Override
    public int update(AppPromotion appPromotion)
    {
        appPromotion.setUpdateTime(DateUtils.getNowDate());
        return appPromotionMapper.updateById(appPromotion);
    }

    @Override
    public int allocation(AppPromotion appPromotion) {
        AppPromotion old = getById(appPromotion.getId());
        if (!old.getCreateBy().equals(SecurityUtils.getUsername())) {
            throw new RuntimeException("您没有权限分配该推广链接");
        }
        appPromotion.setUpdateBy(SecurityUtils.getUsername());
        appPromotion.setAllocation(false);
        return update(appPromotion);
    }

    /**
     * 批量删除推广链接
     *
     * @param ids 需要删除的推广链接主键
     * @return 结果
     */
    @Override
    public int deleteByIds(List<Long> ids)
    {
        return appPromotionMapper.deleteBatchIds(ids);
    }

    /**
     * 删除推广链接信息
     *
     * @param id 推广链接主键
     * @return 结果
     */
    @Override
    public int deleteById(Long id)
    {
        return appPromotionMapper.deleteById(id);
    }

    @Override
    public List<PromotionOptionDto> getPromotionOptions(String appid, String creatorName) {
        List<PromotionOptionDto> options = new ArrayList<>();
        QueryWrapper<AppPromotion> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("tfid", "title"); // 只查询需要的字段

        // 1. 基础权限判断，确定 appid 的范围
        if (SecurityUtils.isSystemAdmin()) {
            // 系统管理员：必须提供 appid 才能查询
            if (StringUtils.isEmpty(appid)) {
                return options; // 管理员未选择 appid，返回空列表
            }
            queryWrapper.eq("appid", appid);
            options.add(new PromotionOptionDto("", "自然流量"));

        } else if (SecurityUtils.isBusinessAdmin()) {
            // 业务主账号：只能看自己 appid 下的
            queryWrapper.eq("appid", SecurityUtils.getAppid());
            options.add(new PromotionOptionDto("", "自然流量"));

        } else {
            // 投手：只能看自己创建的，且在自己 appid 下的
            queryWrapper.eq("create_by", SecurityUtils.getUsername());
            queryWrapper.eq("appid", SecurityUtils.getAppid());
        }
        
        // 2. 叠加投手筛选条件
        if (StringUtils.hasText(creatorName)) {
            queryWrapper.eq("create_by", creatorName);
        }

        // 3. 查询数据库
        List<AppPromotion> promotions = appPromotionMapper.selectList(queryWrapper);

        // 4. 转换为 DTO
        List<PromotionOptionDto> promotionDtos = promotions.stream()
                .map(p -> new PromotionOptionDto(p.getTfid(), p.getTitle() + " (" + p.getTfid() + ")"))
                .collect(Collectors.toList());
        
        options.addAll(promotionDtos);

        return options;
    }
}
