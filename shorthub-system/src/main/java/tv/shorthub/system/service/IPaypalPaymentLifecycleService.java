package tv.shorthub.system.service;

import tv.shorthub.system.mapper.PaypalPaymentLifecycleMapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import tv.shorthub.system.domain.PaypalPaymentLifecycle;
import tv.shorthub.common.core.service.IBaseService;

/**
 * paypal组件生命周期Service接口
 *
 * <AUTHOR>
 * @date 2025-07-04
 */
public interface IPaypalPaymentLifecycleService extends IBaseService<PaypalPaymentLifecycle>
{
    /**
     * 查询paypal组件生命周期
     *
     * @param id paypal组件生命周期主键
     * @return paypal组件生命周期
     */
    public PaypalPaymentLifecycle getById(Long id);

    /**
     * 查询paypal组件生命周期数据汇总
     *
     * @param query paypal组件生命周期
     * @return paypal组件生命周期数据汇总
     */
    public PaypalPaymentLifecycle getSummary(PaypalPaymentLifecycle query);

    /**
     * 查询paypal组件生命周期列表
     *
     * @param query paypal组件生命周期
     * @return paypal组件生命周期集合
     */
    public List<PaypalPaymentLifecycle> selectList(PaypalPaymentLifecycle query);

    /**
     * 新增paypal组件生命周期
     *
     * @param paypalPaymentLifecycle paypal组件生命周期
     * @return 结果
     */
    public int insert(PaypalPaymentLifecycle paypalPaymentLifecycle);

    /**
     * 修改paypal组件生命周期
     *
     * @param paypalPaymentLifecycle paypal组件生命周期
     * @return 结果
     */
    public int update(PaypalPaymentLifecycle paypalPaymentLifecycle);

    /**
     * 批量删除paypal组件生命周期
     *
     * @param ids 需要删除的paypal组件生命周期主键集合
     * @return 结果
     */
    public int deleteByIds(List<Long> ids);

    /**
     * 删除paypal组件生命周期信息
     *
     * @param id paypal组件生命周期主键
     * @return 结果
     */
    public int deleteById(Long id);


    /**
     * 查询自定义分析数据
     *
     * @param query paypal组件生命周期
     * @return paypal组件生命周期集合
     */
    public List<PaypalPaymentLifecycle> summary(SummaryRequest query);

    PaypalPaymentLifecycle allSummary(SummaryRequest query);

    PaypalPaymentLifecycleMapper getMapper();
}
