package tv.shorthub.system.service.impl;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import tv.shorthub.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tv.shorthub.googleplay.service.GooglePlayService;
import tv.shorthub.system.mapper.GooglePlayBillingLogMapper;
import tv.shorthub.system.domain.GooglePlayBillingLog;
import tv.shorthub.system.service.IGooglePlayBillingLogService;
import tv.shorthub.common.core.service.BaseService;

/**
 * googleplay结算日志Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
@Service
public class GooglePlayBillingLogServiceImpl extends BaseService<GooglePlayBillingLog> implements IGooglePlayBillingLogService
{
    @Autowired
    private GooglePlayBillingLogMapper googlePlayBillingLogMapper;

    @Autowired
    private GooglePlayService googlePlayService;

    @Override
    public GooglePlayBillingLogMapper getMapper() {
        return googlePlayBillingLogMapper;
    }

    @Override
    public JSONObject processUnsubscribe(String orderNo, String unsubscribe) {

        GooglePlayBillingLog billingLog = googlePlayBillingLogMapper.selectOne(new QueryWrapper<GooglePlayBillingLog>().eq("order_no", orderNo));
        String productId = billingLog.getRawData().getString("id");
        String purchaseToken = billingLog.getRawData().getString("purchaseTokenAndroid");
//        googlePlayService.cancelSubscription(billingLog.getPackageName(), productId, purchaseToken);

        return null;
    }

    /**
     * 查询googleplay结算日志
     *
     * @param id googleplay结算日志主键
     * @return googleplay结算日志
     */
    @Override
    public GooglePlayBillingLog getById(Long id)
    {
        return googlePlayBillingLogMapper.selectById(id);
    }

    /**
     * 查询googleplay结算日志列表
     *
     * @param query googleplay结算日志
     * @return googleplay结算日志
     */
    @Override
    public List<GooglePlayBillingLog> selectList(GooglePlayBillingLog query)
    {
        return googlePlayBillingLogMapper.selectList(new QueryWrapper<>(query));
    }


    /**
     * 查询googleplay结算日志数据汇总
     *
     * @param query googleplay结算日志
     * @return googleplay结算日志
     */
    @Override
    public GooglePlayBillingLog getSummary(GooglePlayBillingLog query)
    {
        return googlePlayBillingLogMapper.getSummary(query);
    }



    /**
     * 查询自定义分析数据
     *
     * @param query googleplay结算日志
     * @return googleplay结算日志
     */
    @Override
    public List<GooglePlayBillingLog> summary(SummaryRequest query)
    {
        return googlePlayBillingLogMapper.summary(query);
    }

    @Override
    public GooglePlayBillingLog allSummary(SummaryRequest query)
    {
        return googlePlayBillingLogMapper.allSummary(query);
    }

    /**
     * 新增googleplay结算日志
     *
     * @param googlePlayBillingLog googleplay结算日志
     * @return 结果
     */
    @Override
    public int insert(GooglePlayBillingLog googlePlayBillingLog)
    {
        googlePlayBillingLog.setCreateTime(DateUtils.getNowDate());
        return googlePlayBillingLogMapper.insert(googlePlayBillingLog);
    }

    /**
     * 修改googleplay结算日志
     *
     * @param googlePlayBillingLog googleplay结算日志
     * @return 结果
     */
    @Override
    public int update(GooglePlayBillingLog googlePlayBillingLog)
    {
        googlePlayBillingLog.setUpdateTime(DateUtils.getNowDate());
        return googlePlayBillingLogMapper.updateById(googlePlayBillingLog);
    }

    /**
     * 批量删除googleplay结算日志
     *
     * @param ids 需要删除的googleplay结算日志主键
     * @return 结果
     */
    @Override
    public int deleteByIds(List<Long> ids)
    {
        return googlePlayBillingLogMapper.deleteBatchIds(ids);
    }

    /**
     * 删除googleplay结算日志信息
     *
     * @param id googleplay结算日志主键
     * @return 结果
     */
    @Override
    public int deleteById(Long id)
    {
        return googlePlayBillingLogMapper.deleteById(id);
    }
}
