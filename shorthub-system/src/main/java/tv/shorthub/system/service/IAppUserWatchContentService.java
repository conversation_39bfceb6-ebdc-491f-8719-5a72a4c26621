package tv.shorthub.system.service;

import tv.shorthub.system.mapper.AppUserWatchContentMapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import tv.shorthub.system.domain.AppUserWatchContent;
import tv.shorthub.common.core.service.IBaseService;

/**
 * 观看剧目记录Service接口
 *
 * <AUTHOR>
 * @date 2025-05-10
 */
public interface IAppUserWatchContentService extends IBaseService<AppUserWatchContent>
{
    /**
     * 查询观看剧目记录
     *
     * @param id 观看剧目记录主键
     * @return 观看剧目记录
     */
    public AppUserWatchContent getById(Long id);

    /**
     * 查询观看剧目记录数据汇总
     *
     * @param query 观看剧目记录
     * @return 观看剧目记录数据汇总
     */
    public AppUserWatchContent getSummary(AppUserWatchContent query);

    /**
     * 查询观看剧目记录列表
     *
     * @param query 观看剧目记录
     * @return 观看剧目记录集合
     */
    public List<AppUserWatchContent> selectList(AppUserWatchContent query);

    /**
     * 新增观看剧目记录
     *
     * @param appUserWatchContent 观看剧目记录
     * @return 结果
     */
    public int insert(AppUserWatchContent appUserWatchContent);

    /**
     * 修改观看剧目记录
     *
     * @param appUserWatchContent 观看剧目记录
     * @return 结果
     */
    public int update(AppUserWatchContent appUserWatchContent);

    /**
     * 批量删除观看剧目记录
     *
     * @param ids 需要删除的观看剧目记录主键集合
     * @return 结果
     */
    public int deleteByIds(List<Long> ids);

    /**
     * 删除观看剧目记录信息
     *
     * @param id 观看剧目记录主键
     * @return 结果
     */
    public int deleteById(Long id);


    /**
     * 查询自定义分析数据
     *
     * @param query 观看剧目记录
     * @return 观看剧目记录集合
     */
    public List<AppUserWatchContent> summary(SummaryRequest query);

    AppUserWatchContent allSummary(SummaryRequest query);

    AppUserWatchContentMapper getMapper();
}
