package tv.shorthub.system.service;

import tv.shorthub.system.mapper.GooglePlayProductsMapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import tv.shorthub.system.domain.GooglePlayProducts;
import tv.shorthub.common.core.service.IBaseService;

/**
 * Google Play产品基础信息Service接口
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
public interface IGooglePlayProductsService extends IBaseService<GooglePlayProducts>
{
    /**
     * 查询Google Play产品基础信息
     *
     * @param id Google Play产品基础信息主键
     * @return Google Play产品基础信息
     */
    public GooglePlayProducts getById(Long id);

    /**
     * 查询Google Play产品基础信息数据汇总
     *
     * @param query Google Play产品基础信息
     * @return Google Play产品基础信息数据汇总
     */
    public GooglePlayProducts getSummary(GooglePlayProducts query);

    /**
     * 查询Google Play产品基础信息列表
     *
     * @param query Google Play产品基础信息
     * @return Google Play产品基础信息集合
     */
    public List<GooglePlayProducts> selectList(GooglePlayProducts query);

    /**
     * 新增Google Play产品基础信息
     *
     * @param googlePlayProducts Google Play产品基础信息
     * @return 结果
     */
    public int insert(GooglePlayProducts googlePlayProducts);

    /**
     * 修改Google Play产品基础信息
     *
     * @param googlePlayProducts Google Play产品基础信息
     * @return 结果
     */
    public int update(GooglePlayProducts googlePlayProducts);

    /**
     * 批量删除Google Play产品基础信息
     *
     * @param ids 需要删除的Google Play产品基础信息主键集合
     * @return 结果
     */
    public int deleteByIds(List<Long> ids);

    /**
     * 删除Google Play产品基础信息信息
     *
     * @param id Google Play产品基础信息主键
     * @return 结果
     */
    public int deleteById(Long id);


    /**
     * 查询自定义分析数据
     *
     * @param query Google Play产品基础信息
     * @return Google Play产品基础信息集合
     */
    public List<GooglePlayProducts> summary(SummaryRequest query);

    GooglePlayProducts allSummary(SummaryRequest query);

    GooglePlayProductsMapper getMapper();
}
