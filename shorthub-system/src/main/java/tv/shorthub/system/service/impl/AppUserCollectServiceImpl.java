package tv.shorthub.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import tv.shorthub.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tv.shorthub.system.mapper.AppUserCollectMapper;
import tv.shorthub.system.domain.AppUserCollect;
import tv.shorthub.system.service.IAppUserCollectService;
import tv.shorthub.common.core.service.BaseService;

/**
 * 用户收藏Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-10
 */
@Service
public class AppUserCollectServiceImpl extends BaseService<AppUserCollect> implements IAppUserCollectService
{
    @Autowired
    private AppUserCollectMapper appUserCollectMapper;

    @Override
    public AppUserCollectMapper getMapper() {
        return appUserCollectMapper;
    }

    /**
     * 查询用户收藏
     *
     * @param id 用户收藏主键
     * @return 用户收藏
     */
    @Override
    public AppUserCollect getById(Long id)
    {
        return appUserCollectMapper.selectById(id);
    }

    /**
     * 查询用户收藏列表
     *
     * @param query 用户收藏
     * @return 用户收藏
     */
    @Override
    public List<AppUserCollect> selectList(AppUserCollect query)
    {
        return appUserCollectMapper.selectList(new QueryWrapper<>(query));
    }


    /**
     * 查询用户收藏数据汇总
     *
     * @param query 用户收藏
     * @return 用户收藏
     */
    @Override
    public AppUserCollect getSummary(AppUserCollect query)
    {
        return appUserCollectMapper.getSummary(query);
    }



    /**
     * 查询自定义分析数据
     *
     * @param query 用户收藏
     * @return 用户收藏
     */
    @Override
    public List<AppUserCollect> summary(SummaryRequest query)
    {
        return appUserCollectMapper.summary(query);
    }

    @Override
    public AppUserCollect allSummary(SummaryRequest query)
    {
        return appUserCollectMapper.allSummary(query);
    }

    /**
     * 新增用户收藏
     *
     * @param appUserCollect 用户收藏
     * @return 结果
     */
    @Override
    public int insert(AppUserCollect appUserCollect)
    {
        appUserCollect.setCreateTime(DateUtils.getNowDate());
        return appUserCollectMapper.insert(appUserCollect);
    }

    /**
     * 修改用户收藏
     *
     * @param appUserCollect 用户收藏
     * @return 结果
     */
    @Override
    public int update(AppUserCollect appUserCollect)
    {
        appUserCollect.setUpdateTime(DateUtils.getNowDate());
        return appUserCollectMapper.updateById(appUserCollect);
    }

    /**
     * 批量删除用户收藏
     *
     * @param ids 需要删除的用户收藏主键
     * @return 结果
     */
    @Override
    public int deleteByIds(List<Long> ids)
    {
        return appUserCollectMapper.deleteBatchIds(ids);
    }

    /**
     * 删除用户收藏信息
     *
     * @param id 用户收藏主键
     * @return 结果
     */
    @Override
    public int deleteById(Long id)
    {
        return appUserCollectMapper.deleteById(id);
    }
}
