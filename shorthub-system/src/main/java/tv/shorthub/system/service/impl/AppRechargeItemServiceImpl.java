package tv.shorthub.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import tv.shorthub.common.core.cache.CacheKeyUtils;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import java.util.concurrent.TimeUnit;

import tv.shorthub.common.core.redis.RedisCache;
import tv.shorthub.common.utils.DateUtils;
import tv.shorthub.common.utils.SecurityUtils;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tv.shorthub.system.mapper.AppRechargeItemMapper;
import tv.shorthub.system.mapper.AppRechargeMapper;
import tv.shorthub.system.domain.AppRecharge;
import tv.shorthub.system.domain.AppRechargeItem;
import tv.shorthub.system.service.IAppRechargeItemService;
import tv.shorthub.common.core.service.BaseService;
import tv.shorthub.common.exception.ServiceException;

/**
 * 充值方案Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-10
 */
@Service
public class AppRechargeItemServiceImpl extends BaseService<AppRechargeItem> implements IAppRechargeItemService
{
    @Autowired
    private AppRechargeItemMapper appRechargeItemMapper;
    @Autowired
    private AppRechargeMapper appRechargeMapper;

    @Autowired
    RedisCache redisCache;

    @Override
    public AppRechargeItemMapper getMapper() {
        return appRechargeItemMapper;
    }

    @Override
    public AppRechargeItem discountItemByItemId(String parentItemId) {
        String cacheKey = CacheKeyUtils.getDiscountItemMap(parentItemId);
        AppRechargeItem appRechargeItem = redisCache.getCacheObject(cacheKey);
        if (appRechargeItem == null) {
            QueryWrapper<AppRechargeItem> queryWrapper = new QueryWrapper<AppRechargeItem>()
                    .eq("parent_item_id", parentItemId)
                    .eq("enable", true);
            List<AppRechargeItem> appRechargeItems = appRechargeItemMapper.selectList(queryWrapper);
            if (appRechargeItems == null || appRechargeItems.isEmpty()){
                appRechargeItem = new  AppRechargeItem();
                redisCache.setCacheObject(cacheKey,appRechargeItem,2, TimeUnit.HOURS);
            }else {
                appRechargeItem = appRechargeItemMapper.selectList(queryWrapper).getFirst();
                redisCache.setCacheObject(cacheKey,appRechargeItem);
            }
        }

        //判断是否启用
        if (appRechargeItem.getEnable() == null || !appRechargeItem.getEnable()){
            //当前模版不是启用
            return null;
        }
        return appRechargeItem;
    }

    @Override
    public List<AppRechargeItem> queryParentItemList(String templateId) {
        //查询一级方案
        QueryWrapper<AppRechargeItem> queryWrapper = new QueryWrapper<AppRechargeItem>()
                .eq("template_id", templateId)
                .eq("parent_item_id", "");
        return appRechargeItemMapper.selectList(queryWrapper);
    }

    @Override
    public AppRechargeItem getAppRechargeParentItemIsEnableById(String itemId) {
        String cacheKey = CacheKeyUtils.getItemMap(itemId);
        AppRechargeItem cacheAppRechargeItem = redisCache.getCacheObject(cacheKey);
        if (cacheAppRechargeItem == null) {
            QueryWrapper<AppRechargeItem> queryWrapper = new QueryWrapper<AppRechargeItem>()
                    .eq("item_id", itemId)
                    .eq("enable", true)
                    .ne("parent_item_id", "");
            AppRechargeItem appRechargeItem =  appRechargeItemMapper.selectOne(queryWrapper);
            if (appRechargeItem == null) {
                appRechargeItem = new AppRechargeItem();
                redisCache.setCacheObject(cacheKey,appRechargeItem,2, TimeUnit.HOURS);
            }else {
                redisCache.setCacheObject(cacheKey,appRechargeItem);
            }
            cacheAppRechargeItem = appRechargeItem;
        }

        //判断是否启用
        if (cacheAppRechargeItem.getEnable() == null || !cacheAppRechargeItem.getEnable()){
            //当前模版不是启用
            return null;
        }
        return cacheAppRechargeItem;
    }

    @Override
    public List<AppRechargeItem> queryDiscountItemList(String templateId) {
        //查询一级方案
        QueryWrapper<AppRechargeItem> queryWrapper = new QueryWrapper<AppRechargeItem>()
                .eq("template_id", templateId)
                .ne("parent_item_id", "");
        return appRechargeItemMapper.selectList(queryWrapper);
    }

    /**
     * 查询充值方案
     *
     * @param id 充值方案主键
     * @return 充值方案
     */
    @Override
    public AppRechargeItem getById(Long id)
    {
        return appRechargeItemMapper.selectById(id);
    }

    /**
     * 查询充值方案列表
     *
     * @param query 充值方案
     * @return 充值方案
     */
    @Override
    public List<AppRechargeItem> selectList(AppRechargeItem query)
    {
        return appRechargeItemMapper.selectList(new QueryWrapper<>(query).eq("parent_item_id", "").orderByAsc("sort"));
    }


    /**
     * 查询充值方案数据汇总
     *
     * @param query 充值方案
     * @return 充值方案
     */
    @Override
    public AppRechargeItem getSummary(AppRechargeItem query)
    {
        return appRechargeItemMapper.getSummary(query);
    }



    /**
     * 查询自定义分析数据
     *
     * @param query 充值方案
     * @return 充值方案
     */
    @Override
    public List<AppRechargeItem> summary(SummaryRequest query)
    {
        return appRechargeItemMapper.summary(query);
    }

    @Override
    public AppRechargeItem allSummary(SummaryRequest query)
    {
        return appRechargeItemMapper.allSummary(query);
    }


    public boolean checkDuplicateActive(String templateId, Long id) {
        QueryWrapper<AppRechargeItem> queryWrapper = new QueryWrapper<AppRechargeItem>().eq("template_id", templateId).eq("active", true);
        if (null != id) {
            queryWrapper.ne("id", id);
        }
        Long count = appRechargeItemMapper.selectCount(queryWrapper);
        return count > 0;
    }

    /**
     * 新增充值方案
     *
     * @param appRechargeItem 充值方案
     * @return 结果
     */
    @Override
    public int insert(AppRechargeItem appRechargeItem)
    {
        AppRecharge oldEntity = appRechargeMapper.selectOne(new QueryWrapper<AppRecharge>().eq("template_id", appRechargeItem.getTemplateId()));
        if (oldEntity != null && oldEntity.getIsDefault() && !SecurityUtils.isSystemAdmin()) {
            throw new ServiceException("您没有权限修改默认方案");
        }
        //校验二级优惠方案
        appRechargeItem = checkParentIdSaveOrUpdate(appRechargeItem);

        appRechargeItem.setItemId(IdUtil.getSnowflakeNextIdStr());
        appRechargeItem.setCreateTime(DateUtils.getNowDate());
        appRechargeItem.setCreateBy(SecurityUtils.getUsername());
        if (appRechargeItem.getActive() && checkDuplicateActive(appRechargeItem.getTemplateId(), appRechargeItem.getId())) {
            throw new RuntimeException("只允许设置一个激活模板");
        }
        return appRechargeItemMapper.insert(appRechargeItem);
    }

    /**
     * 修改充值方案
     *
     * @param appRechargeItem 充值方案
     * @return 结果
     */
    @Override
    public int update(AppRechargeItem appRechargeItem)
    {
        AppRecharge oldEntity = appRechargeMapper.selectOne(new QueryWrapper<AppRecharge>().eq("template_id", appRechargeItem.getTemplateId()));
        if (oldEntity != null && oldEntity.getIsDefault() && !SecurityUtils.isSystemAdmin()) {
            throw new ServiceException("您没有权限修改默认方案");
        }
        if (appRechargeItem.getActive() && checkDuplicateActive(appRechargeItem.getTemplateId(), appRechargeItem.getId())) {
            throw new RuntimeException("只允许设置一个激活模板");
        }
        //校验二级优惠方案
        appRechargeItem = checkParentIdSaveOrUpdate(appRechargeItem);

        appRechargeItem.setUpdateTime(DateUtils.getNowDate());
        return appRechargeItemMapper.updateById(appRechargeItem);
    }

    /**
     * 批量删除充值方案
     *
     * @param ids 需要删除的充值方案主键
     * @return 结果
     */
    @Override
    public int deleteByIds(List<Long> ids)
    {
        for (Long id : ids) {
            AppRechargeItem item = appRechargeItemMapper.selectById(id);
            if (item != null) {
                AppRecharge oldEntity = appRechargeMapper.selectOne(new QueryWrapper<AppRecharge>().eq("template_id", item.getTemplateId()));
                if (oldEntity != null && oldEntity.getIsDefault() && !SecurityUtils.isSystemAdmin()) {
                    throw new ServiceException("您没有权限修改默认方案");
                }
                if (!(SecurityUtils.getUsername().equals(item.getCreateBy()) || SecurityUtils.isBusinessAdmin())) {
                    throw new ServiceException("您没有权限删除该方案");
                }

                if (StrUtil.isNotBlank(item.getParentItemId())) {
                    //删除优惠缓存
                    String cacheDiscountKey = CacheKeyUtils.getDiscountItemMap(item.getParentItemId());
                    redisCache.deleteObject(cacheDiscountKey);
                    //删除本身缓存
                    String cacheKey = CacheKeyUtils.getItemMap(item.getItemId());
                    redisCache.deleteObject(cacheKey);
                }
            }
        }
        return appRechargeItemMapper.deleteBatchIds(ids);
    }

    /**
     * 删除充值方案信息
     *
     * @param id 充值方案主键
     * @return 结果
     */
    @Override
    public int deleteById(Long id)
    {
        AppRechargeItem item = appRechargeItemMapper.selectById(id);
        if (item != null) {
            AppRecharge oldEntity = appRechargeMapper.selectOne(new QueryWrapper<AppRecharge>().eq("template_id", item.getTemplateId()));
            if (oldEntity != null && oldEntity.getIsDefault() && !SecurityUtils.isSystemAdmin()) {
                throw new ServiceException("您没有权限删除默认方案");
            }
            if (!(SecurityUtils.getUsername().equals(item.getCreateBy()) || SecurityUtils.isBusinessAdmin())) {
                throw new ServiceException("您没有权限删除该方案");
            }
            if (StrUtil.isNotBlank(item.getParentItemId())) {
                //删除优惠缓存
                String cacheDiscountKey = CacheKeyUtils.getDiscountItemMap(item.getParentItemId());
                redisCache.deleteObject(cacheDiscountKey);
                //删除本身缓存
                String cacheKey = CacheKeyUtils.getItemMap(item.getItemId());
                redisCache.deleteObject(cacheKey);
            }
        }

        return appRechargeItemMapper.deleteById(id);
    }

    /**
     * 校验挽留方案
     * @param appRechargeItem
     */
    AppRechargeItem  checkParentIdSaveOrUpdate(AppRechargeItem appRechargeItem){
        //判断是否有父类id,如果有需要校验
        if (StrUtil.isNotBlank(appRechargeItem.getParentItemId())) {
            //查询父级方案
            AppRechargeItem parentItem = appRechargeItemMapper.selectOne(new QueryWrapper<AppRechargeItem>()
                    .eq("template_id", appRechargeItem.getTemplateId())
                    .eq("item_id", appRechargeItem.getParentItemId()));
            if(parentItem == null){
                throw new ServiceException("当前模版父级ID不存在");
            }
            //需要做参数替换
            parentItem.setId(appRechargeItem.getId()); //id
            parentItem.setType(appRechargeItem.getType()); //充值类型
            parentItem.setPrice(appRechargeItem.getPrice()); //金额
            parentItem.setParentItemId(appRechargeItem.getParentItemId());//父级id
            parentItem.setEnable(appRechargeItem.getEnable()); //启动
            parentItem.setItemId(appRechargeItem.getItemId()); //方案id

            //单个父类模版下只允许有一个启用优惠方案
            if (appRechargeItem.getEnable()){
                AppRechargeItem enableItem = appRechargeItemMapper.selectOne(new QueryWrapper<AppRechargeItem>()
                        .eq("template_id", appRechargeItem.getTemplateId())
                        .eq("parent_item_id", appRechargeItem.getParentItemId())
                        .eq("enable", true));
                //存在使用挽留模版
                if(enableItem != null){
                    if (!(appRechargeItem.getId() != null && appRechargeItem.getId().equals(enableItem.getId()))){
                        throw new ServiceException("单个方案只允许启动一个优惠方案");
                    }
                }

            }
            //优惠key
            String cacheDiscountKey = CacheKeyUtils.getDiscountItemMap(appRechargeItem.getParentItemId());
            redisCache.setCacheObject(cacheDiscountKey, parentItem);
            //方案key
            String cacheKey = CacheKeyUtils.getItemMap(parentItem.getItemId());
            redisCache.setCacheObject(cacheKey, parentItem);
            //将父级信息填充给挽留模版
            return parentItem;
        }
        return appRechargeItem;
    }
}
