package tv.shorthub.system.service;

import tv.shorthub.system.mapper.CryptoMonitorLogMapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import tv.shorthub.system.domain.CryptoMonitorLog;
import tv.shorthub.common.core.service.IBaseService;

/**
 * 虚拟货币交易监听记录Service接口
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
public interface ICryptoMonitorLogService extends IBaseService<CryptoMonitorLog>
{
    /**
     * 查询虚拟货币交易监听记录
     *
     * @param id 虚拟货币交易监听记录主键
     * @return 虚拟货币交易监听记录
     */
    public CryptoMonitorLog getById(Long id);

    /**
     * 查询虚拟货币交易监听记录数据汇总
     *
     * @param query 虚拟货币交易监听记录
     * @return 虚拟货币交易监听记录数据汇总
     */
    public CryptoMonitorLog getSummary(CryptoMonitorLog query);

    /**
     * 查询虚拟货币交易监听记录列表
     *
     * @param query 虚拟货币交易监听记录
     * @return 虚拟货币交易监听记录集合
     */
    public List<CryptoMonitorLog> selectList(CryptoMonitorLog query);

    /**
     * 新增虚拟货币交易监听记录
     *
     * @param cryptoMonitorLog 虚拟货币交易监听记录
     * @return 结果
     */
    public int insert(CryptoMonitorLog cryptoMonitorLog);

    /**
     * 修改虚拟货币交易监听记录
     *
     * @param cryptoMonitorLog 虚拟货币交易监听记录
     * @return 结果
     */
    public int update(CryptoMonitorLog cryptoMonitorLog);

    /**
     * 批量删除虚拟货币交易监听记录
     *
     * @param ids 需要删除的虚拟货币交易监听记录主键集合
     * @return 结果
     */
    public int deleteByIds(List<Long> ids);

    /**
     * 删除虚拟货币交易监听记录信息
     *
     * @param id 虚拟货币交易监听记录主键
     * @return 结果
     */
    public int deleteById(Long id);


    /**
     * 查询自定义分析数据
     *
     * @param query 虚拟货币交易监听记录
     * @return 虚拟货币交易监听记录集合
     */
    public List<CryptoMonitorLog> summary(SummaryRequest query);

    CryptoMonitorLog allSummary(SummaryRequest query);

    CryptoMonitorLogMapper getMapper();
}
