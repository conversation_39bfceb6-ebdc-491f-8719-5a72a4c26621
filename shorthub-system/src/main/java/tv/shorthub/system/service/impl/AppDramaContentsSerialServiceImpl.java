package tv.shorthub.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import lombok.extern.slf4j.Slf4j;
import tv.shorthub.common.core.domain.SummaryRequest;

import java.time.Duration;
import java.util.Date;
import java.util.List;

import tv.shorthub.common.core.oss.ProviderOSS;
import tv.shorthub.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tv.shorthub.system.mapper.AppDramaContentsSerialMapper;
import tv.shorthub.system.domain.AppDramaContentsSerial;
import tv.shorthub.system.service.IAppDramaContentsSerialService;
import tv.shorthub.common.core.service.BaseService;

/**
 * 剧集Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-08
 */
@Service
@Slf4j
public class AppDramaContentsSerialServiceImpl extends BaseService<AppDramaContentsSerial> implements IAppDramaContentsSerialService
{
    @Autowired
    private AppDramaContentsSerialMapper appDramaContentsSerialMapper;

    @Autowired
    ProviderOSS providerOSS;

    @Override
    public AppDramaContentsSerialMapper getMapper() {
        return appDramaContentsSerialMapper;
    }

    /**
     * 查询剧集
     *
     * @param id 剧集主键
     * @return 剧集
     */
    @Override
    public AppDramaContentsSerial getById(Long id)
    {
        return appDramaContentsSerialMapper.selectById(id);
    }

    /**
     * 查询剧集列表
     *
     * @param query 剧集
     * @return 剧集
     */
    @Override
    public List<AppDramaContentsSerial> selectList(AppDramaContentsSerial query)
    {
        return appDramaContentsSerialMapper.selectList(new QueryWrapper<>(query).orderByAsc("serial_number"));
    }


    /**
     * 查询剧集数据汇总
     *
     * @param query 剧集
     * @return 剧集
     */
    @Override
    public AppDramaContentsSerial getSummary(AppDramaContentsSerial query)
    {
        return appDramaContentsSerialMapper.getSummary(query);
    }



    /**
     * 查询自定义分析数据
     *
     * @param query 剧集
     * @return 剧集
     */
    @Override
    public List<AppDramaContentsSerial> summary(SummaryRequest query)
    {
        return appDramaContentsSerialMapper.summary(query);
    }

    @Override
    public AppDramaContentsSerial allSummary(SummaryRequest query)
    {
        return appDramaContentsSerialMapper.allSummary(query);
    }

    /**
     * 新增剧集
     *
     * @param appDramaContentsSerial 剧集
     * @return 结果
     */
    @Override
    public int insert(AppDramaContentsSerial appDramaContentsSerial)
    {
        appDramaContentsSerial.setCreateTime(DateUtils.getNowDate());
        return appDramaContentsSerialMapper.insert(appDramaContentsSerial);
    }

    /**
     * 修改剧集
     *
     * @param appDramaContentsSerial 剧集
     * @return 结果
     */
    @Override
    public int update(AppDramaContentsSerial appDramaContentsSerial)
    {
        appDramaContentsSerial.setUpdateTime(DateUtils.getNowDate());
        return appDramaContentsSerialMapper.updateById(appDramaContentsSerial);
    }

    /**
     * 批量删除剧集
     *
     * @param ids 需要删除的剧集主键
     * @return 结果
     */
    @Override
    public int deleteByIds(List<Long> ids)
    {
        return appDramaContentsSerialMapper.deleteBatchIds(ids);
    }

    /**
     * 删除剧集信息
     *
     * @param id 剧集主键
     * @return 结果
     */
    @Override
    public int deleteById(Long id)
    {
        return appDramaContentsSerialMapper.deleteById(id);
    }

    @Override
    public int updateOrder(List<AppDramaContentsSerial> list) {
        return appDramaContentsSerialMapper.updateOrder(list);
    }

    @Override
    public void updateUrl(AppDramaContentsSerial appDramaContentsSerial) {
        Duration duration = Duration.ofDays(1);
        Date expireDate = new Date(System.currentTimeMillis() + duration.toMillis());
        String url = providerOSS.generateUrl(appDramaContentsSerial.getStoragePath(), duration);
        UpdateWrapper<AppDramaContentsSerial> updateWrapper = new UpdateWrapper<AppDramaContentsSerial>()
                .eq("id", appDramaContentsSerial.getId())
                .set("serial_url", url)
                .set("serial_url_expire_time", expireDate);
        getMapper().update(updateWrapper);
        log.info("更新剧集: id={}, url={}, 过期时间={}", appDramaContentsSerial.getId(), url, DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", expireDate));
    }

}
