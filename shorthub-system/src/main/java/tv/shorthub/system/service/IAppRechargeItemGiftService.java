package tv.shorthub.system.service;

import tv.shorthub.system.mapper.AppRechargeItemGiftMapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import tv.shorthub.system.domain.AppRechargeItemGift;
import tv.shorthub.common.core.service.IBaseService;

/**
 * 充值优惠Service接口
 *
 * <AUTHOR>
 * @date 2025-05-10
 */
public interface IAppRechargeItemGiftService extends IBaseService<AppRechargeItemGift>
{
    /**
     * 查询充值优惠
     *
     * @param id 充值优惠主键
     * @return 充值优惠
     */
    public AppRechargeItemGift getById(Long id);

    /**
     * 查询充值优惠数据汇总
     *
     * @param query 充值优惠
     * @return 充值优惠数据汇总
     */
    public AppRechargeItemGift getSummary(AppRechargeItemGift query);

    /**
     * 查询充值优惠列表
     *
     * @param query 充值优惠
     * @return 充值优惠集合
     */
    public List<AppRechargeItemGift> selectList(AppRechargeItemGift query);

    /**
     * 新增充值优惠
     *
     * @param appRechargeItemGift 充值优惠
     * @return 结果
     */
    public int insert(AppRechargeItemGift appRechargeItemGift);

    /**
     * 修改充值优惠
     *
     * @param appRechargeItemGift 充值优惠
     * @return 结果
     */
    public int update(AppRechargeItemGift appRechargeItemGift);

    /**
     * 批量删除充值优惠
     *
     * @param ids 需要删除的充值优惠主键集合
     * @return 结果
     */
    public int deleteByIds(List<Long> ids);

    /**
     * 删除充值优惠信息
     *
     * @param id 充值优惠主键
     * @return 结果
     */
    public int deleteById(Long id);


    /**
     * 查询自定义分析数据
     *
     * @param query 充值优惠
     * @return 充值优惠集合
     */
    public List<AppRechargeItemGift> summary(SummaryRequest query);

    AppRechargeItemGift allSummary(SummaryRequest query);

    AppRechargeItemGiftMapper getMapper();
}
