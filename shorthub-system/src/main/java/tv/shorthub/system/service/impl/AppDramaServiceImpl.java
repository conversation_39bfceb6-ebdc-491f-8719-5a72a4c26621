package tv.shorthub.system.service.impl;

import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import tv.shorthub.common.core.domain.SummaryRequest;
import tv.shorthub.common.core.service.BaseService;
import tv.shorthub.common.utils.DateUtils;
import tv.shorthub.system.domain.AppDrama;
import tv.shorthub.system.domain.AppDramaContents;
import tv.shorthub.system.domain.AppDramaGenres;
import tv.shorthub.system.mapper.AppDramaContentsMapper;
import tv.shorthub.system.mapper.AppDramaMapper;
import tv.shorthub.system.service.IAppDramaGenresService;
import tv.shorthub.system.service.IAppDramaService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 剧目Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-05-06
 */
@Service
public class AppDramaServiceImpl extends BaseService<AppDrama> implements IAppDramaService
{
    @Autowired
    private AppDramaMapper appDramaMapper;

    @Autowired
    private AppDramaContentsMapper appDramaContentsMapper;

    @Autowired
    private IAppDramaGenresService dramaGenresService;

    @Override
    public AppDramaMapper getMapper() {
        return appDramaMapper;
    }

    /**
     * 查询剧目
     * 
     * @param id 剧目主键
     * @return 剧目
     */
    @Override
    public AppDrama getById(Long id)
    {
        AppDrama drama = appDramaMapper.selectById(id);
        if (drama != null) {
            // 获取剧目关联的类型ID列表
            List<Long> genreIds = dramaGenresService.getGenreIdsByDramaId(drama.getDramaId());
            drama.setParams(Map.of("genreIds", genreIds));
        }
        return drama;
    }


    /**
     * 查询剧目列表
     * 
     * @param query 剧目
     * @return 剧目
     */
    @Override
    public List<AppDrama> selectList(AppDrama query)
    {
        // 按剧目类型筛选
        QueryWrapper<AppDrama> queryWrapper = new QueryWrapper<>();


        List<String> dramaIds = new ArrayList<>();

        // 按题材查询
        if (null != query.getParams().get("genreIds") && !query.getParams().get("genreIds").toString().isEmpty()) {
            List<String> genreIds = List.of(query.getParams().get("genreIds").toString().split(","));
            QueryWrapper<AppDramaGenres> genreQuery = new QueryWrapper<>(AppDramaGenres.class).in("genre_id", genreIds);
            List<AppDramaGenres> appDramaGenres = dramaGenresService.getMapper().selectList(genreQuery);
            if (CollectionUtils.isEmpty(appDramaGenres)) {
                return List.of();
            }
            dramaIds.addAll(appDramaGenres.stream().map(AppDramaGenres::getDramaId).toList());
        }


        // 从语种库查询
        if (StringUtils.isNotEmpty(query.getDramaId()) || StringUtils.isNotEmpty(query.getTitle())) {
            QueryWrapper<AppDramaContents> contentsQueryWrapper = new QueryWrapper<>();
            if (StringUtils.isNotEmpty(query.getDramaId())) {
                contentsQueryWrapper.eq("content_id", query.getDramaId());
            }
            if (StringUtils.isNotEmpty(query.getTitle())) {
                contentsQueryWrapper.like("title", query.getTitle());
                queryWrapper.like("title", query.getTitle());
            }

            List<AppDramaContents> appDramaContents = appDramaContentsMapper.selectList(contentsQueryWrapper);
            List<String> byDramas = new ArrayList<>(appDramaContents.stream().map(AppDramaContents::getDramaId).distinct().toList());
            byDramas.add(query.getDramaId());
            dramaIds.addAll(byDramas);
        }

        if (CollectionUtils.isNotEmpty(dramaIds)) {
            queryWrapper.in("drama_id", dramaIds);
        }

        List<AppDrama> list = appDramaMapper.selectList(queryWrapper);
        // 获取每个剧目关联的类型ID列表
        for (AppDrama drama : list) {
            List<Long> genreIds = dramaGenresService.getGenreIdsByDramaId(drama.getDramaId());
            drama.setParams(Map.of("genreIds", genreIds));
        }
        return list;
    }

    /**
     * 查询剧目数据汇总
     *
     * @param query 剧目
     * @return 剧目
     */
    @Override
    public AppDrama getSummary(AppDrama query)
    {
        return appDramaMapper.getSummary(query);
    }

    /**
     * 查询自定义分析数据
     *
     * @param query 剧目
     * @return 剧目
     */
    @Override
    public List<AppDrama> summary(SummaryRequest query)
    {
        return appDramaMapper.summary(query);
    }

    @Override
    public AppDrama allSummary(SummaryRequest query)
    {
        return appDramaMapper.allSummary(query);
    }

    /**
     * 新增剧目
     * 
     * @param appDrama 剧目
     * @return 结果
     */
    @Override
    @Transactional
    public int insert(AppDrama appDrama)
    {
        appDrama.setDramaId(IdUtil.getSnowflakeNextIdStr());
        appDrama.setCreateTime(DateUtils.getNowDate());
        //
        int rows = appDramaMapper.insert(appDrama);
        // 保存剧目-类型关联
        if (rows > 0 && appDrama.getParams() != null) {
            @SuppressWarnings("unchecked")
            List<String> genreIds = (List<String>) appDrama.getParams().get("genreIds");
            if (genreIds != null && !genreIds.isEmpty()) {
                List<AppDramaGenres> dramaGenresList = new ArrayList<>();
                for (String genreId : genreIds) {
                    AppDramaGenres dramaGenres = new AppDramaGenres();
                    dramaGenres.setDramaId(appDrama.getDramaId());
                    dramaGenres.setGenreId(Long.parseLong(genreId));
                    dramaGenres.setCreateBy(appDrama.getCreateBy());
                    dramaGenres.setCreateTime(appDrama.getCreateTime());
                    dramaGenresList.add(dramaGenres);
                }
                dramaGenresService.batchInsert(dramaGenresList);
            }
        }
        return rows;
    }

    /**
     * 修改剧目
     * 
     * @param appDrama 剧目
     * @return 结果
     */
    @Override
    @Transactional
    public int update(AppDrama appDrama)
    {
        appDrama.setUpdateTime(DateUtils.getNowDate());
        int rows = appDramaMapper.updateById(appDrama);
        // 更新剧目-类型关联
        if (rows > 0 && appDrama.getParams() != null) {
            // 删除原有的关联
            dramaGenresService.deleteByDramaId(appDrama.getDramaId());
            // 保存新的关联
            @SuppressWarnings("unchecked")
            List<String> genreIds = (List<String>) appDrama.getParams().get("genreIds");
            if (genreIds != null && !genreIds.isEmpty()) {
                List<AppDramaGenres> dramaGenresList = new ArrayList<>();
                for (String genreId : genreIds) {
                    AppDramaGenres dramaGenres = new AppDramaGenres();
                    dramaGenres.setDramaId(appDrama.getDramaId());
                    dramaGenres.setGenreId(Long.parseLong(genreId));
                    dramaGenres.setCreateBy(appDrama.getUpdateBy());
                    dramaGenres.setCreateTime(appDrama.getUpdateTime());
                    dramaGenresList.add(dramaGenres);
                }
                dramaGenresService.batchInsert(dramaGenresList);
            }
        }
        return rows;
    }

    /**
     * 批量删除剧目
     * 
     * @param ids 需要删除的剧目主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteByIds(List<Long> ids)
    {
        // 删除剧目-类型关联
        for (Long id : ids) {
            AppDrama drama = getById(id);
            dramaGenresService.deleteByDramaId(drama.getDramaId());
        }
        return appDramaMapper.deleteBatchIds(ids);
    }

    /**
     * 删除剧目信息
     * 
     * @param id 剧目主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteById(Long id)
    {
        // 删除剧目-类型关联
        AppDrama drama = getById(id);
        dramaGenresService.deleteByDramaId(drama.getDramaId());
        return appDramaMapper.deleteById(id);
    }
}
