package tv.shorthub.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import tv.shorthub.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tv.shorthub.system.mapper.GooglePlayProductI18nMapper;
import tv.shorthub.system.domain.GooglePlayProductI18n;
import tv.shorthub.system.service.IGooglePlayProductI18nService;
import tv.shorthub.common.core.service.BaseService;

/**
 * Google Play产品多语言信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
@Service
public class GooglePlayProductI18nServiceImpl extends BaseService<GooglePlayProductI18n> implements IGooglePlayProductI18nService
{
    @Autowired
    private GooglePlayProductI18nMapper googlePlayProductI18nMapper;

    @Override
    public GooglePlayProductI18nMapper getMapper() {
        return googlePlayProductI18nMapper;
    }

    /**
     * 查询Google Play产品多语言信息
     *
     * @param id Google Play产品多语言信息主键
     * @return Google Play产品多语言信息
     */
    @Override
    public GooglePlayProductI18n getById(Long id)
    {
        return googlePlayProductI18nMapper.selectById(id);
    }

    /**
     * 查询Google Play产品多语言信息列表
     *
     * @param query Google Play产品多语言信息
     * @return Google Play产品多语言信息
     */
    @Override
    public List<GooglePlayProductI18n> selectList(GooglePlayProductI18n query)
    {
        return googlePlayProductI18nMapper.selectList(new QueryWrapper<>(query));
    }


    /**
     * 查询Google Play产品多语言信息数据汇总
     *
     * @param query Google Play产品多语言信息
     * @return Google Play产品多语言信息
     */
    @Override
    public GooglePlayProductI18n getSummary(GooglePlayProductI18n query)
    {
        return googlePlayProductI18nMapper.getSummary(query);
    }



    /**
     * 查询自定义分析数据
     *
     * @param query Google Play产品多语言信息
     * @return Google Play产品多语言信息
     */
    @Override
    public List<GooglePlayProductI18n> summary(SummaryRequest query)
    {
        return googlePlayProductI18nMapper.summary(query);
    }

    @Override
    public GooglePlayProductI18n allSummary(SummaryRequest query)
    {
        return googlePlayProductI18nMapper.allSummary(query);
    }

    /**
     * 新增Google Play产品多语言信息
     *
     * @param googlePlayProductI18n Google Play产品多语言信息
     * @return 结果
     */
    @Override
    public int insert(GooglePlayProductI18n googlePlayProductI18n)
    {
        googlePlayProductI18n.setCreateTime(DateUtils.getNowDate());
        return googlePlayProductI18nMapper.insert(googlePlayProductI18n);
    }

    /**
     * 修改Google Play产品多语言信息
     *
     * @param googlePlayProductI18n Google Play产品多语言信息
     * @return 结果
     */
    @Override
    public int update(GooglePlayProductI18n googlePlayProductI18n)
    {
        googlePlayProductI18n.setUpdateTime(DateUtils.getNowDate());
        return googlePlayProductI18nMapper.updateById(googlePlayProductI18n);
    }

    /**
     * 批量删除Google Play产品多语言信息
     *
     * @param ids 需要删除的Google Play产品多语言信息主键
     * @return 结果
     */
    @Override
    public int deleteByIds(List<Long> ids)
    {
        return googlePlayProductI18nMapper.deleteBatchIds(ids);
    }

    /**
     * 删除Google Play产品多语言信息信息
     *
     * @param id Google Play产品多语言信息主键
     * @return 结果
     */
    @Override
    public int deleteById(Long id)
    {
        return googlePlayProductI18nMapper.deleteById(id);
    }
}
