package tv.shorthub.system.service;

import tv.shorthub.system.mapper.AirwallexPaymentConfigMapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import tv.shorthub.system.domain.AirwallexPaymentConfig;
import tv.shorthub.common.core.service.IBaseService;

/**
 * airwallex支付配置Service接口
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
public interface IAirwallexPaymentConfigService extends IBaseService<AirwallexPaymentConfig>
{
    /**
     * 查询airwallex支付配置
     *
     * @param id airwallex支付配置主键
     * @return airwallex支付配置
     */
    public AirwallexPaymentConfig getById(Long id);

    /**
     * 查询airwallex支付配置数据汇总
     *
     * @param query airwallex支付配置
     * @return airwallex支付配置数据汇总
     */
    public AirwallexPaymentConfig getSummary(AirwallexPaymentConfig query);

    /**
     * 查询airwallex支付配置列表
     *
     * @param query airwallex支付配置
     * @return airwallex支付配置集合
     */
    public List<AirwallexPaymentConfig> selectList(AirwallexPaymentConfig query);

    /**
     * 新增airwallex支付配置
     *
     * @param airwallexPaymentConfig airwallex支付配置
     * @return 结果
     */
    public int insert(AirwallexPaymentConfig airwallexPaymentConfig);

    /**
     * 修改airwallex支付配置
     *
     * @param airwallexPaymentConfig airwallex支付配置
     * @return 结果
     */
    public int update(AirwallexPaymentConfig airwallexPaymentConfig);

    /**
     * 批量删除airwallex支付配置
     *
     * @param ids 需要删除的airwallex支付配置主键集合
     * @return 结果
     */
    public int deleteByIds(List<Long> ids);

    /**
     * 删除airwallex支付配置信息
     *
     * @param id airwallex支付配置主键
     * @return 结果
     */
    public int deleteById(Long id);


    /**
     * 查询自定义分析数据
     *
     * @param query airwallex支付配置
     * @return airwallex支付配置集合
     */
    public List<AirwallexPaymentConfig> summary(SummaryRequest query);

    AirwallexPaymentConfig allSummary(SummaryRequest query);

    AirwallexPaymentConfigMapper getMapper();
}
