package tv.shorthub.system.service;

import tv.shorthub.system.mapper.CryptoTronBlockMapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import tv.shorthub.system.domain.CryptoTronBlock;
import tv.shorthub.common.core.service.IBaseService;

/**
 * TRON区块链区块Service接口
 *
 * <AUTHOR>
 * @date 2025-08-05
 */
public interface ICryptoTronBlockService extends IBaseService<CryptoTronBlock>
{
    /**
     * 查询TRON区块链区块
     *
     * @param id TRON区块链区块主键
     * @return TRON区块链区块
     */
    public CryptoTronBlock getById(Long id);

    /**
     * 查询TRON区块链区块数据汇总
     *
     * @param query TRON区块链区块
     * @return TRON区块链区块数据汇总
     */
    public CryptoTronBlock getSummary(CryptoTronBlock query);

    /**
     * 查询TRON区块链区块列表
     *
     * @param query TRON区块链区块
     * @return TRON区块链区块集合
     */
    public List<CryptoTronBlock> selectList(CryptoTronBlock query);

    /**
     * 新增TRON区块链区块
     *
     * @param cryptoTronBlock TRON区块链区块
     * @return 结果
     */
    public int insert(CryptoTronBlock cryptoTronBlock);

    /**
     * 修改TRON区块链区块
     *
     * @param cryptoTronBlock TRON区块链区块
     * @return 结果
     */
    public int update(CryptoTronBlock cryptoTronBlock);

    /**
     * 批量删除TRON区块链区块
     *
     * @param ids 需要删除的TRON区块链区块主键集合
     * @return 结果
     */
    public int deleteByIds(List<Long> ids);

    /**
     * 删除TRON区块链区块信息
     *
     * @param id TRON区块链区块主键
     * @return 结果
     */
    public int deleteById(Long id);


    /**
     * 查询自定义分析数据
     *
     * @param query TRON区块链区块
     * @return TRON区块链区块集合
     */
    public List<CryptoTronBlock> summary(SummaryRequest query);

    CryptoTronBlock allSummary(SummaryRequest query);

    CryptoTronBlockMapper getMapper();
}
