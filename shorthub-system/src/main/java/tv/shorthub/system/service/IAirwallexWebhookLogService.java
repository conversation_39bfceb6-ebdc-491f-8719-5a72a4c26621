package tv.shorthub.system.service;

import tv.shorthub.system.mapper.AirwallexWebhookLogMapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import tv.shorthub.system.domain.AirwallexWebhookLog;
import tv.shorthub.common.core.service.IBaseService;

/**
 * webhook事件日志Service接口
 *
 * <AUTHOR>
 * @date 2025-07-19
 */
public interface IAirwallexWebhookLogService extends IBaseService<AirwallexWebhookLog>
{
    /**
     * 查询webhook事件日志
     *
     * @param id webhook事件日志主键
     * @return webhook事件日志
     */
    public AirwallexWebhookLog getById(Long id);

    /**
     * 查询webhook事件日志数据汇总
     *
     * @param query webhook事件日志
     * @return webhook事件日志数据汇总
     */
    public AirwallexWebhookLog getSummary(AirwallexWebhookLog query);

    /**
     * 查询webhook事件日志列表
     *
     * @param query webhook事件日志
     * @return webhook事件日志集合
     */
    public List<AirwallexWebhookLog> selectList(AirwallexWebhookLog query);

    /**
     * 新增webhook事件日志
     *
     * @param airwallexWebhookLog webhook事件日志
     * @return 结果
     */
    public int insert(AirwallexWebhookLog airwallexWebhookLog);

    /**
     * 修改webhook事件日志
     *
     * @param airwallexWebhookLog webhook事件日志
     * @return 结果
     */
    public int update(AirwallexWebhookLog airwallexWebhookLog);

    /**
     * 批量删除webhook事件日志
     *
     * @param ids 需要删除的webhook事件日志主键集合
     * @return 结果
     */
    public int deleteByIds(List<Long> ids);

    /**
     * 删除webhook事件日志信息
     *
     * @param id webhook事件日志主键
     * @return 结果
     */
    public int deleteById(Long id);


    /**
     * 查询自定义分析数据
     *
     * @param query webhook事件日志
     * @return webhook事件日志集合
     */
    public List<AirwallexWebhookLog> summary(SummaryRequest query);

    AirwallexWebhookLog allSummary(SummaryRequest query);

    AirwallexWebhookLogMapper getMapper();
}
