package tv.shorthub.system.service;

import tv.shorthub.system.mapper.AdRetargetingEventMapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import tv.shorthub.system.domain.AdRetargetingEvent;
import tv.shorthub.common.core.service.IBaseService;

/**
 * 回传事件Service接口
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
public interface IAdRetargetingEventService extends IBaseService<AdRetargetingEvent>
{
    /**
     * 查询回传事件
     *
     * @param id 回传事件主键
     * @return 回传事件
     */
    public AdRetargetingEvent getById(Long id);

    /**
     * 查询回传事件数据汇总
     *
     * @param query 回传事件
     * @return 回传事件数据汇总
     */
    public AdRetargetingEvent getSummary(AdRetargetingEvent query);

    /**
     * 查询回传事件列表
     *
     * @param query 回传事件
     * @return 回传事件集合
     */
    public List<AdRetargetingEvent> selectList(AdRetargetingEvent query);

    /**
     * 新增回传事件
     *
     * @param adRetargetingEvent 回传事件
     * @return 结果
     */
    public int insert(AdRetargetingEvent adRetargetingEvent);

    /**
     * 修改回传事件
     *
     * @param adRetargetingEvent 回传事件
     * @return 结果
     */
    public int update(AdRetargetingEvent adRetargetingEvent);

    /**
     * 批量删除回传事件
     *
     * @param ids 需要删除的回传事件主键集合
     * @return 结果
     */
    public int deleteByIds(List<Long> ids);

    /**
     * 删除回传事件信息
     *
     * @param id 回传事件主键
     * @return 结果
     */
    public int deleteById(Long id);


    /**
     * 查询自定义分析数据
     *
     * @param query 回传事件
     * @return 回传事件集合
     */
    public List<AdRetargetingEvent> summary(SummaryRequest query);

    AdRetargetingEvent allSummary(SummaryRequest query);

    AdRetargetingEventMapper getMapper();
}
