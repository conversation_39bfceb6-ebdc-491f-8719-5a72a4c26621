package tv.shorthub.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import tv.shorthub.common.utils.SecurityUtils;
import java.util.Date;
import java.util.List;
import tv.shorthub.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tv.shorthub.system.domain.StatisticsDailyOrderStats;
import tv.shorthub.system.mapper.StatisticsDailyDramaStatsMapper;
import tv.shorthub.system.domain.StatisticsDailyDramaStats;
import tv.shorthub.system.service.IStatisticsDailyDramaStatsService;
import tv.shorthub.common.core.service.BaseService;
import tv.shorthub.system.utils.StatisticsPermissionUtils;

/**
 * 每日剧集分析统计Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Service
public class StatisticsDailyDramaStatsServiceImpl extends BaseService<StatisticsDailyDramaStats> implements IStatisticsDailyDramaStatsService
{
    @Autowired
    private StatisticsDailyDramaStatsMapper statisticsDailyDramaStatsMapper;

    @Autowired
    private StatisticsPermissionUtils permissionUtils;

    @Override
    public StatisticsDailyDramaStatsMapper getMapper() {
        return statisticsDailyDramaStatsMapper;
    }

    /**
     * 查询每日剧集分析统计
     *
     * @param id 每日剧集分析统计主键
     * @return 每日剧集分析统计
     */
    @Override
    public StatisticsDailyDramaStats getById(Long id)
    {
        return statisticsDailyDramaStatsMapper.selectById(id);
    }

    /**
     * 查询每日剧集分析统计列表
     *
     * @param query 每日剧集分析统计
     * @return 每日剧集分析统计
     */
    @Override
    public List<StatisticsDailyDramaStats> selectList(StatisticsDailyDramaStats query)
    {
        QueryWrapper<StatisticsDailyDramaStats> wrapper = new QueryWrapper<>(query);
        // 按时间倒序排列，最新数据在前
        wrapper.orderByDesc("stat_date");
        // 权限过滤
        permissionUtils.filterByDeliverPermission(wrapper);
        return statisticsDailyDramaStatsMapper.selectList(new QueryWrapper<>(query));
    }

    /**
     * 查询每日剧集分析统计数据汇总
     *
     * @param query 每日剧集分析统计
     * @return 每日剧集分析统计
     */
    @Override
    public StatisticsDailyDramaStats getSummary(StatisticsDailyDramaStats query)
    {
        return statisticsDailyDramaStatsMapper.getSummary(query);
    }

    /**
     * 查询自定义分析数据
     *
     * @param query 每日剧集分析统计
     * @return 每日剧集分析统计
     */
    @Override
    public List<StatisticsDailyDramaStats> summary(SummaryRequest query)
    {
        return statisticsDailyDramaStatsMapper.summary(query);
    }

    @Override
    public StatisticsDailyDramaStats allSummary(SummaryRequest query)
    {
        return statisticsDailyDramaStatsMapper.allSummary(query);
    }

    /**
     * 新增每日剧集分析统计
     *
     * @param statisticsDailyDramaStats 每日剧集分析统计
     * @return 结果
     */
    @Override
    public int insert(StatisticsDailyDramaStats statisticsDailyDramaStats)
    {
        statisticsDailyDramaStats.setCreateTime(DateUtils.getNowDate());
        return statisticsDailyDramaStatsMapper.insert(statisticsDailyDramaStats);
    }

    /**
     * 修改每日剧集分析统计
     *
     * @param statisticsDailyDramaStats 每日剧集分析统计
     * @return 结果
     */
    @Override
    public int update(StatisticsDailyDramaStats statisticsDailyDramaStats)
    {
        statisticsDailyDramaStats.setUpdateTime(DateUtils.getNowDate());
        return statisticsDailyDramaStatsMapper.updateById(statisticsDailyDramaStats);
    }

    /**
     * 批量删除每日剧集分析统计
     *
     * @param ids 需要删除的每日剧集分析统计主键
     * @return 结果
     */
    @Override
    public int deleteByIds(List<Long> ids)
    {
        return statisticsDailyDramaStatsMapper.deleteBatchIds(ids);
    }

    /**
     * 删除每日剧集分析统计信息
     *
     * @param id 每日剧集分析统计主键
     * @return 结果
     */
    @Override
    public int deleteById(Long id)
    {
        return statisticsDailyDramaStatsMapper.deleteById(id);
    }

    // 新增业务方法实现

    @Override
    public List<StatisticsDailyDramaStats> selectDailyDramaStats(Date startTime, Date endTime, String timezone, int timezoneOffset) {
        return statisticsDailyDramaStatsMapper.selectDailyDramaStats(startTime, endTime, timezone, timezoneOffset);
    }

    @Override
    public StatisticsDailyDramaStats getRangeSummary(Date startTime, Date endTime, String appid, String tfid, String timezone) {

        return statisticsDailyDramaStatsMapper.getRangeSummary(startTime, endTime, appid, tfid, timezone);
    }

    @Override
    public List<StatisticsDailyDramaStats> selectListByTimeRange(Date startTime, Date endTime, StatisticsDailyDramaStats query) {
        // 主账号强制限定 appid
        if (SecurityUtils.isBusinessAdmin() && !SecurityUtils.isSystemAdmin()) {
            query.setAppid(SecurityUtils.getAppid());
        }
        return statisticsDailyDramaStatsMapper.selectListByTimeRange(startTime, endTime, query);
    }

    @Override
    public StatisticsDailyDramaStats getSummaryByTfids(List<String> tfids, Date startTime, Date endTime, String appid, String timezone) {
        // 权限验证：如果是业务主账号但不是系统管理员，强制使用自己的appid
        if (SecurityUtils.isBusinessAdmin() && !SecurityUtils.isSystemAdmin()) {
            appid = SecurityUtils.getAppid();
        }
        return statisticsDailyDramaStatsMapper.getSummaryByTfids(tfids, startTime, endTime, appid, timezone);
    }

    @Override
    public StatisticsDailyDramaStats getSummaryByCreator(String creatorName, Date startTime, Date endTime, String timezone) {
        // 主账号自己看自己appid下所有数据（含自然流量）
        if (SecurityUtils.isBusinessAdmin() && !SecurityUtils.isSystemAdmin() &&
            (org.apache.commons.lang3.StringUtils.isEmpty(creatorName) || SecurityUtils.getUsername().equals(creatorName))) {
            if (startTime != null && endTime != null) {
                return statisticsDailyDramaStatsMapper.getRangeSummary(startTime, endTime, SecurityUtils.getAppid(), null, timezone);
            } else {
                StatisticsDailyDramaStats query = new StatisticsDailyDramaStats();
                query.setAppid(SecurityUtils.getAppid());
                return statisticsDailyDramaStatsMapper.getSummary(query);
            }
        }
        // 其他情况：系统管理员或主账号看指定子账号，调用getSummaryByCreator方法
        return statisticsDailyDramaStatsMapper.getSummaryByCreator(creatorName, startTime, endTime, timezone);
    }
}
