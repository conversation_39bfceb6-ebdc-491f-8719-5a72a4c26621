package tv.shorthub.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import tv.shorthub.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tv.shorthub.system.mapper.AirwallexUserPlanMapper;
import tv.shorthub.system.domain.AirwallexUserPlan;
import tv.shorthub.system.service.IAirwallexUserPlanService;
import tv.shorthub.common.core.service.BaseService;

/**
 * airwallex用户扣费计划Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
@Service
public class AirwallexUserPlanServiceImpl extends BaseService<AirwallexUserPlan> implements IAirwallexUserPlanService
{
    @Autowired
    private AirwallexUserPlanMapper airwallexUserPlanMapper;

    @Override
    public AirwallexUserPlanMapper getMapper() {
        return airwallexUserPlanMapper;
    }

    /**
     * 查询airwallex用户扣费计划
     *
     * @param id airwallex用户扣费计划主键
     * @return airwallex用户扣费计划
     */
    @Override
    public AirwallexUserPlan getById(Long id)
    {
        return airwallexUserPlanMapper.selectById(id);
    }

    /**
     * 查询airwallex用户扣费计划列表
     *
     * @param query airwallex用户扣费计划
     * @return airwallex用户扣费计划
     */
    @Override
    public List<AirwallexUserPlan> selectList(AirwallexUserPlan query)
    {
        return airwallexUserPlanMapper.selectList(new QueryWrapper<>(query));
    }


    /**
     * 查询airwallex用户扣费计划数据汇总
     *
     * @param query airwallex用户扣费计划
     * @return airwallex用户扣费计划
     */
    @Override
    public AirwallexUserPlan getSummary(AirwallexUserPlan query)
    {
        return airwallexUserPlanMapper.getSummary(query);
    }



    /**
     * 查询自定义分析数据
     *
     * @param query airwallex用户扣费计划
     * @return airwallex用户扣费计划
     */
    @Override
    public List<AirwallexUserPlan> summary(SummaryRequest query)
    {
        return airwallexUserPlanMapper.summary(query);
    }

    @Override
    public AirwallexUserPlan allSummary(SummaryRequest query)
    {
        return airwallexUserPlanMapper.allSummary(query);
    }

    /**
     * 新增airwallex用户扣费计划
     *
     * @param airwallexUserPlan airwallex用户扣费计划
     * @return 结果
     */
    @Override
    public int insert(AirwallexUserPlan airwallexUserPlan)
    {
        airwallexUserPlan.setCreateTime(DateUtils.getNowDate());
        return airwallexUserPlanMapper.insert(airwallexUserPlan);
    }

    /**
     * 修改airwallex用户扣费计划
     *
     * @param airwallexUserPlan airwallex用户扣费计划
     * @return 结果
     */
    @Override
    public int update(AirwallexUserPlan airwallexUserPlan)
    {
        airwallexUserPlan.setUpdateTime(DateUtils.getNowDate());
        return airwallexUserPlanMapper.updateById(airwallexUserPlan);
    }

    /**
     * 批量删除airwallex用户扣费计划
     *
     * @param ids 需要删除的airwallex用户扣费计划主键
     * @return 结果
     */
    @Override
    public int deleteByIds(List<Long> ids)
    {
        return airwallexUserPlanMapper.deleteBatchIds(ids);
    }

    /**
     * 删除airwallex用户扣费计划信息
     *
     * @param id airwallex用户扣费计划主键
     * @return 结果
     */
    @Override
    public int deleteById(Long id)
    {
        return airwallexUserPlanMapper.deleteById(id);
    }
}
