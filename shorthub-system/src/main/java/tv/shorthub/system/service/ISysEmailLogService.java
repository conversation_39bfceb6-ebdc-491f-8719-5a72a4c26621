package tv.shorthub.system.service;

import tv.shorthub.system.mapper.SysEmailLogMapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import tv.shorthub.system.domain.SysEmailLog;
import tv.shorthub.common.core.service.IBaseService;

/**
 * 邮件发送日志Service接口
 *
 * <AUTHOR>
 * @date 2025-06-09
 */
public interface ISysEmailLogService extends IBaseService<SysEmailLog>
{
    /**
     * 查询邮件发送日志
     *
     * @param id 邮件发送日志主键
     * @return 邮件发送日志
     */
    public SysEmailLog getById(String id);

    /**
     * 查询邮件发送日志数据汇总
     *
     * @param query 邮件发送日志
     * @return 邮件发送日志数据汇总
     */
    public SysEmailLog getSummary(SysEmailLog query);

    /**
     * 查询邮件发送日志列表
     *
     * @param query 邮件发送日志
     * @return 邮件发送日志集合
     */
    public List<SysEmailLog> selectList(SysEmailLog query);

    /**
     * 新增邮件发送日志
     *
     * @param sysEmailLog 邮件发送日志
     * @return 结果
     */
    public int insert(SysEmailLog sysEmailLog);

    /**
     * 修改邮件发送日志
     *
     * @param sysEmailLog 邮件发送日志
     * @return 结果
     */
    public int update(SysEmailLog sysEmailLog);

    /**
     * 批量删除邮件发送日志
     *
     * @param ids 需要删除的邮件发送日志主键集合
     * @return 结果
     */
    public int deleteByIds(List<String> ids);

    /**
     * 删除邮件发送日志信息
     *
     * @param id 邮件发送日志主键
     * @return 结果
     */
    public int deleteById(String id);


    /**
     * 查询自定义分析数据
     *
     * @param query 邮件发送日志
     * @return 邮件发送日志集合
     */
    public List<SysEmailLog> summary(SummaryRequest query);

    SysEmailLog allSummary(SummaryRequest query);

    SysEmailLogMapper getMapper();

    /**
     * 判断某订单的某类邮件是否已发送
     * @param orderId 订单号
     * @param toEmail 收件人
     * @param subject 邮件主题
     * @return true 已发送，false 未发送
     */
    boolean hasSent(String orderId, String toEmail, String subject);
}
