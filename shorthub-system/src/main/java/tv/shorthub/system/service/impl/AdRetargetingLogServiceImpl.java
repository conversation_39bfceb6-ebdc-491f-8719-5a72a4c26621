package tv.shorthub.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import tv.shorthub.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tv.shorthub.system.mapper.AdRetargetingLogMapper;
import tv.shorthub.system.domain.AdRetargetingLog;
import tv.shorthub.system.service.IAdRetargetingLogService;
import tv.shorthub.common.core.service.BaseService;
import org.springframework.util.StringUtils;

/**
 * 回传记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Service
public class AdRetargetingLogServiceImpl extends BaseService<AdRetargetingLog> implements IAdRetargetingLogService
{
    @Autowired
    private AdRetargetingLogMapper adRetargetingLogMapper;

    @Override
    public AdRetargetingLogMapper getMapper() {
        return adRetargetingLogMapper;
    }

    /**
     * 查询回传记录
     *
     * @param id 回传记录主键
     * @return 回传记录
     */
    @Override
    public AdRetargetingLog getById(Long id)
    {
        return adRetargetingLogMapper.selectById(id);
    }

    /**
     * 查询回传记录列表
     *
     * @param query 回传记录
     * @return 回传记录
     */
    @Override
    public List<AdRetargetingLog> selectList(AdRetargetingLog query)
    {
        return adRetargetingLogMapper.selectList(new QueryWrapper<>(query).orderByDesc("create_time"));
    }


    /**
     * 查询回传记录数据汇总
     *
     * @param query 回传记录
     * @return 回传记录
     */
    @Override
    public AdRetargetingLog getSummary(AdRetargetingLog query)
    {
        return adRetargetingLogMapper.getSummary(query);
    }



    /**
     * 查询自定义分析数据
     *
     * @param query 回传记录
     * @return 回传记录
     */
    @Override
    public List<AdRetargetingLog> summary(SummaryRequest query)
    {
        return adRetargetingLogMapper.summary(query);
    }

    @Override
    public AdRetargetingLog allSummary(SummaryRequest query)
    {
        return adRetargetingLogMapper.allSummary(query);
    }

    /**
     * 新增回传记录
     *
     * @param adRetargetingLog 回传记录
     * @return 结果
     */
    @Override
    public int insert(AdRetargetingLog adRetargetingLog)
    {
        adRetargetingLog.setCreateTime(DateUtils.getNowDate());
        return adRetargetingLogMapper.insert(adRetargetingLog);
    }

    /**
     * 修改回传记录
     *
     * @param adRetargetingLog 回传记录
     * @return 结果
     */
    @Override
    public int update(AdRetargetingLog adRetargetingLog)
    {
        adRetargetingLog.setUpdateTime(DateUtils.getNowDate());
        return adRetargetingLogMapper.updateById(adRetargetingLog);
    }

    /**
     * 批量删除回传记录
     *
     * @param ids 需要删除的回传记录主键
     * @return 结果
     */
    @Override
    public int deleteByIds(List<Long> ids)
    {
        return adRetargetingLogMapper.deleteBatchIds(ids);
    }

    /**
     * 删除回传记录信息
     *
     * @param id 回传记录主键
     * @return 结果
     */
    @Override
    public int deleteById(Long id)
    {
        return adRetargetingLogMapper.deleteById(id);
    }

    @Override
    public AdRetargetingLog getLatestSuccessfulLog(String userId, Long eventId) {
        if (StringUtils.isEmpty(userId)) {
            return null;
        }
        QueryWrapper<AdRetargetingLog> query = new QueryWrapper<AdRetargetingLog>()
                .eq("user_id", userId)
                .eq("event_id", eventId)
                .eq("callback_status", true)
                .orderByDesc("create_time")
                .last("limit 1");
        return adRetargetingLogMapper.selectOne(
                query
        );
    }

    @Override
    public long countSuccessfulLogsByUserId(Long eventId, String userId) {
        if (StringUtils.isEmpty(userId)) {
            return 0;
        }
        QueryWrapper<AdRetargetingLog> queryWrapper = new QueryWrapper<AdRetargetingLog>()
                .eq("event_id", eventId)
                .eq("user_id", userId)
                .eq("callback_status", true);
        return adRetargetingLogMapper.selectCount(
                queryWrapper
        );
    }

    @Override
    public long countSuccessfulLogsByRelationId(Long eventId, String relationId) {
        if (StringUtils.isEmpty(relationId)) {
            return 0;
        }
        QueryWrapper<AdRetargetingLog> queryWrapper = new QueryWrapper<AdRetargetingLog>()
                .eq("event_id", eventId)
                .eq("relation_id", relationId)
                .eq("callback_status", true);
        return adRetargetingLogMapper.selectCount(
                queryWrapper
        );
    }
}
