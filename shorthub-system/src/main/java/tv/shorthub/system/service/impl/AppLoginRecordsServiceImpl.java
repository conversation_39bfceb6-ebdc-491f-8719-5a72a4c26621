package tv.shorthub.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import tv.shorthub.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tv.shorthub.system.mapper.AppLoginRecordsMapper;
import tv.shorthub.system.domain.AppLoginRecords;
import tv.shorthub.system.service.IAppLoginRecordsService;
import tv.shorthub.common.core.service.BaseService;

/**
 * 登录历史Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-13
 */
@Service
public class AppLoginRecordsServiceImpl extends BaseService<AppLoginRecords> implements IAppLoginRecordsService
{
    @Autowired
    private AppLoginRecordsMapper appLoginRecordsMapper;

    @Override
    public AppLoginRecordsMapper getMapper() {
        return appLoginRecordsMapper;
    }

    /**
     * 查询登录历史
     *
     * @param id 登录历史主键
     * @return 登录历史
     */
    @Override
    public AppLoginRecords getById(Long id)
    {
        return appLoginRecordsMapper.selectById(id);
    }

    /**
     * 查询登录历史列表
     *
     * @param query 登录历史
     * @return 登录历史
     */
    @Override
    public List<AppLoginRecords> selectList(AppLoginRecords query)
    {
        return appLoginRecordsMapper.selectList(new QueryWrapper<>(query).orderByDesc("id"));
    }


    /**
     * 查询登录历史数据汇总
     *
     * @param query 登录历史
     * @return 登录历史
     */
    @Override
    public AppLoginRecords getSummary(AppLoginRecords query)
    {
        return appLoginRecordsMapper.getSummary(query);
    }



    /**
     * 查询自定义分析数据
     *
     * @param query 登录历史
     * @return 登录历史
     */
    @Override
    public List<AppLoginRecords> summary(SummaryRequest query)
    {
        return appLoginRecordsMapper.summary(query);
    }

    @Override
    public AppLoginRecords allSummary(SummaryRequest query)
    {
        return appLoginRecordsMapper.allSummary(query);
    }

    /**
     * 新增登录历史
     *
     * @param appLoginRecords 登录历史
     * @return 结果
     */
    @Override
    public int insert(AppLoginRecords appLoginRecords)
    {
        appLoginRecords.setCreateTime(DateUtils.getNowDate());
        return appLoginRecordsMapper.insert(appLoginRecords);
    }

    /**
     * 修改登录历史
     *
     * @param appLoginRecords 登录历史
     * @return 结果
     */
    @Override
    public int update(AppLoginRecords appLoginRecords)
    {
        appLoginRecords.setUpdateTime(DateUtils.getNowDate());
        return appLoginRecordsMapper.updateById(appLoginRecords);
    }

    /**
     * 批量删除登录历史
     *
     * @param ids 需要删除的登录历史主键
     * @return 结果
     */
    @Override
    public int deleteByIds(List<Long> ids)
    {
        return appLoginRecordsMapper.deleteBatchIds(ids);
    }

    /**
     * 删除登录历史信息
     *
     * @param id 登录历史主键
     * @return 结果
     */
    @Override
    public int deleteById(Long id)
    {
        return appLoginRecordsMapper.deleteById(id);
    }
}
