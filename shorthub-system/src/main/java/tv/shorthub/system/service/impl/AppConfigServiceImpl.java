package tv.shorthub.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import tv.shorthub.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tv.shorthub.system.mapper.AppConfigMapper;
import tv.shorthub.system.domain.AppConfig;
import tv.shorthub.system.service.IAppConfigService;
import tv.shorthub.common.core.service.BaseService;

/**
 * 客户端配置Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-21
 */
@Service
public class AppConfigServiceImpl extends BaseService<AppConfig> implements IAppConfigService
{
    @Autowired
    private AppConfigMapper appConfigMapper;

    @Override
    public AppConfigMapper getMapper() {
        return appConfigMapper;
    }

    /**
     * 查询客户端配置
     *
     * @param id 客户端配置主键
     * @return 客户端配置
     */
    @Override
    public AppConfig getById(Long id)
    {
        return appConfigMapper.selectById(id);
    }

    /**
     * 查询客户端配置列表
     *
     * @param query 客户端配置
     * @return 客户端配置
     */
    @Override
    public List<AppConfig> selectList(AppConfig query)
    {
        return appConfigMapper.selectList(new QueryWrapper<>(query));
    }


    /**
     * 查询客户端配置数据汇总
     *
     * @param query 客户端配置
     * @return 客户端配置
     */
    @Override
    public AppConfig getSummary(AppConfig query)
    {
        return appConfigMapper.getSummary(query);
    }



    /**
     * 查询自定义分析数据
     *
     * @param query 客户端配置
     * @return 客户端配置
     */
    @Override
    public List<AppConfig> summary(SummaryRequest query)
    {
        return appConfigMapper.summary(query);
    }

    @Override
    public AppConfig allSummary(SummaryRequest query)
    {
        return appConfigMapper.allSummary(query);
    }

    /**
     * 新增客户端配置
     *
     * @param appConfig 客户端配置
     * @return 结果
     */
    @Override
    public int insert(AppConfig appConfig)
    {
        appConfig.setCreateTime(DateUtils.getNowDate());
        return appConfigMapper.insert(appConfig);
    }

    /**
     * 修改客户端配置
     *
     * @param appConfig 客户端配置
     * @return 结果
     */
    @Override
    public int update(AppConfig appConfig)
    {
        appConfig.setUpdateTime(DateUtils.getNowDate());
        return appConfigMapper.updateById(appConfig);
    }

    /**
     * 批量删除客户端配置
     *
     * @param ids 需要删除的客户端配置主键
     * @return 结果
     */
    @Override
    public int deleteByIds(List<Long> ids)
    {
        return appConfigMapper.deleteBatchIds(ids);
    }

    /**
     * 删除客户端配置信息
     *
     * @param id 客户端配置主键
     * @return 结果
     */
    @Override
    public int deleteById(Long id)
    {
        return appConfigMapper.deleteById(id);
    }
}
