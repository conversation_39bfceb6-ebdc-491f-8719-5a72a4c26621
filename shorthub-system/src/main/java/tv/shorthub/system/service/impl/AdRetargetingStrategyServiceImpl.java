package tv.shorthub.system.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import tv.shorthub.common.core.domain.SummaryRequest;

import java.util.Date;
import java.util.List;
import tv.shorthub.common.utils.DateUtils;
import tv.shorthub.common.utils.SecurityUtils;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tv.shorthub.system.domain.AdRetargetingEvent;
import tv.shorthub.system.domain.AdRetargetingRule;
import tv.shorthub.system.mapper.AdRetargetingEventMapper;
import tv.shorthub.system.mapper.AdRetargetingRuleMapper;
import tv.shorthub.system.mapper.AdRetargetingStrategyMapper;
import tv.shorthub.system.domain.AdRetargetingStrategy;
import tv.shorthub.system.service.IAdRetargetingEventService;
import tv.shorthub.system.service.IAdRetargetingRuleService;
import tv.shorthub.system.service.IAdRetargetingStrategyService;
import tv.shorthub.common.core.service.BaseService;

/**
 * 回传策略Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Service
@Slf4j
public class AdRetargetingStrategyServiceImpl extends BaseService<AdRetargetingStrategy> implements IAdRetargetingStrategyService
{
    @Autowired
    private AdRetargetingStrategyMapper adRetargetingStrategyMapper;

    @Autowired
    private IAdRetargetingEventService eventService;

    @Autowired
    private IAdRetargetingRuleService ruleService;

    @Override
    public AdRetargetingStrategyMapper getMapper() {
        return adRetargetingStrategyMapper;
    }

    /**
     * 查询回传策略
     *
     * @param id 回传策略主键
     * @return 回传策略
     */
    @Override
    public AdRetargetingStrategy getById(Long id)
    {
        return adRetargetingStrategyMapper.selectById(id);
    }

    /**
     * 查询回传策略列表
     *
     * @param query 回传策略
     * @return 回传策略
     */
    @Override
    public List<AdRetargetingStrategy> selectList(AdRetargetingStrategy query)
    {
        QueryWrapper<AdRetargetingStrategy> queryWrapper = new QueryWrapper<>(query);
        if (!SecurityUtils.isSystemAdmin()) {
            if (!SecurityUtils.isBusinessAdmin()) {
                queryWrapper.eq("create_by", SecurityUtils.getUsername());
            } else {
                queryWrapper.eq("appid", SecurityUtils.getAppid());
            }
        }
        return adRetargetingStrategyMapper.selectList(queryWrapper);
    }


    /**
     * 查询回传策略数据汇总
     *
     * @param query 回传策略
     * @return 回传策略
     */
    @Override
    public AdRetargetingStrategy getSummary(AdRetargetingStrategy query)
    {
        return adRetargetingStrategyMapper.getSummary(query);
    }



    /**
     * 查询自定义分析数据
     *
     * @param query 回传策略
     * @return 回传策略
     */
    @Override
    public List<AdRetargetingStrategy> summary(SummaryRequest query)
    {
        return adRetargetingStrategyMapper.summary(query);
    }

    @Override
    public AdRetargetingStrategy allSummary(SummaryRequest query)
    {
        return adRetargetingStrategyMapper.allSummary(query);
    }

    /**
     * 新增回传策略
     *
     * @param adRetargetingStrategy 回传策略
     * @return 结果
     */
    @Override
    public int insert(AdRetargetingStrategy adRetargetingStrategy)
    {
        adRetargetingStrategy.setAppid(SecurityUtils.getAppid());
        adRetargetingStrategy.setCreateBy(SecurityUtils.getUsername());
        adRetargetingStrategy.setCreateTime(DateUtils.getNowDate());
        return adRetargetingStrategyMapper.insert(adRetargetingStrategy);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int clone(AdRetargetingStrategy adRetargetingStrategy) {
        if (null == adRetargetingStrategy.getId()) {
            throw new RuntimeException("要克隆的回传策略不存在[id]");
        }
        AdRetargetingStrategy oldStrategy = getById(adRetargetingStrategy.getId());
        if (null == oldStrategy) {
            throw new RuntimeException("要克隆的回传策略不存在");
        }

        Long oldId = adRetargetingStrategy.getId();

        // 1.克隆策略
        adRetargetingStrategy.setId(null);
        adRetargetingStrategy.setAppid(SecurityUtils.getAppid());
        adRetargetingStrategy.setCreateBy(SecurityUtils.getUsername());
        adRetargetingStrategy.setCreateTime(DateUtils.getNowDate());
        adRetargetingStrategyMapper.insert(adRetargetingStrategy);
        log.info("clone strategy:{}", oldStrategy.getStrategyName());

        // 2.克隆事件
        List<AdRetargetingEvent> oldEvents = eventService.getMapper().selectList(new QueryWrapper<AdRetargetingEvent>().eq("strategy_id", oldId));
        for (AdRetargetingEvent oldEvent : oldEvents) {

            Long oldEventId = oldEvent.getId();

            oldEvent.setId(null);
            oldEvent.setStrategyId(adRetargetingStrategy.getId());
            eventService.insert(oldEvent);
            log.info("clone strategy:{}, event:{}", oldStrategy.getStrategyName(), oldEvent.getEventName());

            // 3.克隆规则
            List<AdRetargetingRule> oldRules = ruleService.getMapper().selectList(new QueryWrapper<AdRetargetingRule>().eq("event_id", oldEventId));
            for (AdRetargetingRule oldRule : oldRules) {
                oldRule.setId(null);
                oldRule.setEventId(oldEvent.getId());
                ruleService.insert(oldRule);

                log.info("clone strategy:{}, event:{}, rule:{}", oldStrategy.getStrategyName(), oldEvent.getEventName(), oldRule.getRuleName());
            }
        }

        return  1;
    }

    /**
     * 修改回传策略
     *
     * @param adRetargetingStrategy 回传策略
     * @return 结果
     */
    @Override
    public int update(AdRetargetingStrategy adRetargetingStrategy)
    {
        adRetargetingStrategy.setUpdateTime(DateUtils.getNowDate());
        return adRetargetingStrategyMapper.updateById(adRetargetingStrategy);
    }

    /**
     * 批量删除回传策略
     *
     * @param ids 需要删除的回传策略主键
     * @return 结果
     */
    @Override
    public int deleteByIds(List<Long> ids)
    {
        return adRetargetingStrategyMapper.deleteBatchIds(ids);
    }

    /**
     * 删除回传策略信息
     *
     * @param id 回传策略主键
     * @return 结果
     */
    @Override
    public int deleteById(Long id)
    {
        return adRetargetingStrategyMapper.deleteById(id);
    }
}
