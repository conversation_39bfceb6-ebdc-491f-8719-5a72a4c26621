package tv.shorthub.system.service;

import com.alibaba.fastjson2.JSONObject;
import tv.shorthub.system.mapper.GooglePlayBillingLogMapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import tv.shorthub.system.domain.GooglePlayBillingLog;
import tv.shorthub.common.core.service.IBaseService;

/**
 * googleplay结算日志Service接口
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
public interface IGooglePlayBillingLogService extends IBaseService<GooglePlayBillingLog>
{
    /**
     * 查询googleplay结算日志
     *
     * @param id googleplay结算日志主键
     * @return googleplay结算日志
     */
    public GooglePlayBillingLog getById(Long id);

    /**
     * 查询googleplay结算日志数据汇总
     *
     * @param query googleplay结算日志
     * @return googleplay结算日志数据汇总
     */
    public GooglePlayBillingLog getSummary(GooglePlayBillingLog query);

    /**
     * 查询googleplay结算日志列表
     *
     * @param query googleplay结算日志
     * @return googleplay结算日志集合
     */
    public List<GooglePlayBillingLog> selectList(GooglePlayBillingLog query);

    /**
     * 新增googleplay结算日志
     *
     * @param googlePlayBillingLog googleplay结算日志
     * @return 结果
     */
    public int insert(GooglePlayBillingLog googlePlayBillingLog);

    /**
     * 修改googleplay结算日志
     *
     * @param googlePlayBillingLog googleplay结算日志
     * @return 结果
     */
    public int update(GooglePlayBillingLog googlePlayBillingLog);

    /**
     * 批量删除googleplay结算日志
     *
     * @param ids 需要删除的googleplay结算日志主键集合
     * @return 结果
     */
    public int deleteByIds(List<Long> ids);

    /**
     * 删除googleplay结算日志信息
     *
     * @param id googleplay结算日志主键
     * @return 结果
     */
    public int deleteById(Long id);


    /**
     * 查询自定义分析数据
     *
     * @param query googleplay结算日志
     * @return googleplay结算日志集合
     */
    public List<GooglePlayBillingLog> summary(SummaryRequest query);

    GooglePlayBillingLog allSummary(SummaryRequest query);

    GooglePlayBillingLogMapper getMapper();

    JSONObject processUnsubscribe(String orderNo, String unsubscribe);
}
