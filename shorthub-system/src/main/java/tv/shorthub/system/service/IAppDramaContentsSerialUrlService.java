package tv.shorthub.system.service;

import tv.shorthub.system.mapper.AppDramaContentsSerialUrlMapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import tv.shorthub.system.domain.AppDramaContentsSerialUrl;
import tv.shorthub.common.core.service.IBaseService;

/**
 * 剧集链接Service接口
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
public interface IAppDramaContentsSerialUrlService extends IBaseService<AppDramaContentsSerialUrl>
{
    /**
     * 查询剧集链接
     *
     * @param id 剧集链接主键
     * @return 剧集链接
     */
    public AppDramaContentsSerialUrl getById(Long id);

    /**
     * 查询剧集链接数据汇总
     *
     * @param query 剧集链接
     * @return 剧集链接数据汇总
     */
    public AppDramaContentsSerialUrl getSummary(AppDramaContentsSerialUrl query);

    /**
     * 查询剧集链接列表
     *
     * @param query 剧集链接
     * @return 剧集链接集合
     */
    public List<AppDramaContentsSerialUrl> selectList(AppDramaContentsSerialUrl query);

    /**
     * 新增剧集链接
     *
     * @param appDramaContentsSerialUrl 剧集链接
     * @return 结果
     */
    public int insert(AppDramaContentsSerialUrl appDramaContentsSerialUrl);

    /**
     * 修改剧集链接
     *
     * @param appDramaContentsSerialUrl 剧集链接
     * @return 结果
     */
    public int update(AppDramaContentsSerialUrl appDramaContentsSerialUrl);

    /**
     * 批量删除剧集链接
     *
     * @param ids 需要删除的剧集链接主键集合
     * @return 结果
     */
    public int deleteByIds(List<Long> ids);

    /**
     * 删除剧集链接信息
     *
     * @param id 剧集链接主键
     * @return 结果
     */
    public int deleteById(Long id);


    /**
     * 查询自定义分析数据
     *
     * @param query 剧集链接
     * @return 剧集链接集合
     */
    public List<AppDramaContentsSerialUrl> summary(SummaryRequest query);

    AppDramaContentsSerialUrl allSummary(SummaryRequest query);

    AppDramaContentsSerialUrlMapper getMapper();
}
