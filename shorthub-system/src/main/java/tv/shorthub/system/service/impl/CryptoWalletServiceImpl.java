package tv.shorthub.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import tv.shorthub.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tv.shorthub.system.mapper.CryptoWalletMapper;
import tv.shorthub.system.domain.CryptoWallet;
import tv.shorthub.system.service.ICryptoWalletService;
import tv.shorthub.common.core.service.BaseService;

/**
 * 虚拟货币钱包地址Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
@Service
public class CryptoWalletServiceImpl extends BaseService<CryptoWallet> implements ICryptoWalletService
{
    @Autowired
    private CryptoWalletMapper cryptoWalletMapper;

    @Override
    public CryptoWalletMapper getMapper() {
        return cryptoWalletMapper;
    }

    /**
     * 查询虚拟货币钱包地址
     *
     * @param id 虚拟货币钱包地址主键
     * @return 虚拟货币钱包地址
     */
    @Override
    public CryptoWallet getById(Long id)
    {
        return cryptoWalletMapper.selectById(id);
    }

    /**
     * 查询虚拟货币钱包地址列表
     *
     * @param query 虚拟货币钱包地址
     * @return 虚拟货币钱包地址
     */
    @Override
    public List<CryptoWallet> selectList(CryptoWallet query)
    {
        return cryptoWalletMapper.selectList(new QueryWrapper<>(query));
    }


    /**
     * 查询虚拟货币钱包地址数据汇总
     *
     * @param query 虚拟货币钱包地址
     * @return 虚拟货币钱包地址
     */
    @Override
    public CryptoWallet getSummary(CryptoWallet query)
    {
        return cryptoWalletMapper.getSummary(query);
    }



    /**
     * 查询自定义分析数据
     *
     * @param query 虚拟货币钱包地址
     * @return 虚拟货币钱包地址
     */
    @Override
    public List<CryptoWallet> summary(SummaryRequest query)
    {
        return cryptoWalletMapper.summary(query);
    }

    @Override
    public CryptoWallet allSummary(SummaryRequest query)
    {
        return cryptoWalletMapper.allSummary(query);
    }

    /**
     * 新增虚拟货币钱包地址
     *
     * @param cryptoWallet 虚拟货币钱包地址
     * @return 结果
     */
    @Override
    public int insert(CryptoWallet cryptoWallet)
    {
        cryptoWallet.setCreateTime(DateUtils.getNowDate());
        return cryptoWalletMapper.insert(cryptoWallet);
    }

    /**
     * 修改虚拟货币钱包地址
     *
     * @param cryptoWallet 虚拟货币钱包地址
     * @return 结果
     */
    @Override
    public int update(CryptoWallet cryptoWallet)
    {
        cryptoWallet.setUpdateTime(DateUtils.getNowDate());
        return cryptoWalletMapper.updateById(cryptoWallet);
    }

    /**
     * 批量删除虚拟货币钱包地址
     *
     * @param ids 需要删除的虚拟货币钱包地址主键
     * @return 结果
     */
    @Override
    public int deleteByIds(List<Long> ids)
    {
        return cryptoWalletMapper.deleteBatchIds(ids);
    }

    /**
     * 删除虚拟货币钱包地址信息
     *
     * @param id 虚拟货币钱包地址主键
     * @return 结果
     */
    @Override
    public int deleteById(Long id)
    {
        return cryptoWalletMapper.deleteById(id);
    }
}
