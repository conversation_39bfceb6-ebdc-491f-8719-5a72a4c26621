package tv.shorthub.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import tv.shorthub.common.core.service.BaseService;
import tv.shorthub.common.utils.DateUtils;
import tv.shorthub.common.utils.SecurityUtils;
import tv.shorthub.system.domain.AppPromotion;
import tv.shorthub.system.domain.AppUsers;
import tv.shorthub.system.mapper.AppUsersMapper;
import tv.shorthub.system.service.IAppPromotionService;
import tv.shorthub.system.service.IAppUsersService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 社交媒体用户Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-05-06
 */
@Service
public class AppUsersServiceImpl extends BaseService<AppUsers> implements IAppUsersService
{
    @Autowired
    private AppUsersMapper appUsersMapper;

    @Autowired
    private IAppPromotionService appPromotionService;

    @Override
    public AppUsersMapper getMapper() {
        return appUsersMapper;
    }

    @Override
    public int updateMemberByAdmin(String userId, String memberId, String memberLevel, Long value, Date currentTime) {
        return appUsersMapper.updateMemberByAdmin(userId, memberLevel, value, currentTime);
    }

    @Override
    public int updateMember(String userId, String memberId, String memberLevel, Long value, Date currentTime) {
        return appUsersMapper.updateMember(userId, memberId, memberLevel, value, currentTime);
    }

    @Override
    public int updateBalanceCoinByAdmin(String userId, Long value) {
        return appUsersMapper.updateBalanceCoinByAdmin(userId, value);
    }

    @Override
    public int updateBalanceCoin(String userId, Long value) {
        return appUsersMapper.updateBalanceCoin(userId, value);
    }

    @Override
    public int updateBalanceBonusByAdmin(String userId, Long value) {
        return appUsersMapper.updateBalanceBonusByAdmin(userId, value);
    }

    @Override
    public int updateBalanceBonus(String userId, Long value) {
        return appUsersMapper.updateBalanceBonus(userId, value);
    }

    /**
     * 查询社交媒体用户
     * 
     * @param id 社交媒体用户主键
     * @return 社交媒体用户
     */
    @Override
    public AppUsers getById(Long id)
    {
        return appUsersMapper.selectById(id);
    }

    /**
     * 查询社交媒体用户列表
     * 
     * @param query 社交媒体用户
     * @return 社交媒体用户
     */
    @Override
    public List<AppUsers> selectList(AppUsers query)
    {
        QueryWrapper<AppUsers> queryWrapper = new QueryWrapper<>(query);
        if (!SecurityUtils.isSystemAdmin()) {
            QueryWrapper<AppPromotion> appPromotionQueryWrapper = new QueryWrapper<>();
            if (!SecurityUtils.isBusinessAdmin()) {
                appPromotionQueryWrapper.eq("create_by", SecurityUtils.getUsername());
            } else {
                appPromotionQueryWrapper.eq("appid", SecurityUtils.getAppid());
            }
            List<AppPromotion> promotionList = appPromotionService.getMapper().selectList(appPromotionQueryWrapper);
            queryWrapper.in("tfid", promotionList.stream().map(AppPromotion::getTfid).collect(Collectors.toList()));
        }
        return appUsersMapper.selectList(queryWrapper);
    }


    /**
     * 查询社交媒体用户数据汇总
     *
     * @param query 社交媒体用户
     * @return 社交媒体用户
     */
    @Override
    public AppUsers getSummary(AppUsers query)
    {
        return appUsersMapper.getSummary(query);
    }



    /**
     * 查询自定义分析数据
     *
     * @param query 社交媒体用户
     * @return 社交媒体用户
     */
    @Override
    public List<AppUsers> summary(SummaryRequest query)
    {
        return appUsersMapper.summary(query);
    }

    @Override
    public AppUsers allSummary(SummaryRequest query)
    {
        return appUsersMapper.allSummary(query);
    }

    /**
     * 新增社交媒体用户
     * 
     * @param appUsers 社交媒体用户
     * @return 结果
     */
    @Override
    public int insert(AppUsers appUsers)
    {
        appUsers.setCreateTime(DateUtils.getNowDate());
        return appUsersMapper.insert(appUsers);
    }

    /**
     * 修改社交媒体用户
     * 
     * @param appUsers 社交媒体用户
     * @return 结果
     */
    @Override
    public int update(AppUsers appUsers)
    {
        appUsers.setUpdateTime(DateUtils.getNowDate());
        return appUsersMapper.updateById(appUsers);
    }

    /**
     * 批量删除社交媒体用户
     * 
     * @param ids 需要删除的社交媒体用户主键
     * @return 结果
     */
    @Override
    public int deleteByIds(List<Long> ids)
    {
        return appUsersMapper.deleteBatchIds(ids);
    }

    /**
     * 删除社交媒体用户信息
     * 
     * @param id 社交媒体用户主键
     * @return 结果
     */
    @Override
    public int deleteById(Long id)
    {
        return appUsersMapper.deleteById(id);
    }
}
