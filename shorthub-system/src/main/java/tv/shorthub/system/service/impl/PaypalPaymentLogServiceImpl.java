package tv.shorthub.system.service.impl;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;

import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;

import tv.shorthub.common.enums.CloudflareQueueEnum;
import tv.shorthub.common.queue.CloudflareQueueService;
import tv.shorthub.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tv.shorthub.paypal.config.PaypalConfigStorageHolder;
import tv.shorthub.paypal.constant.PayPalConstants;
import tv.shorthub.paypal.service.PayPalPaymentService;
import tv.shorthub.paypal.service.PayPalSubscriptionService;
import tv.shorthub.system.mapper.PaypalPaymentLogMapper;
import tv.shorthub.system.domain.PaypalPaymentLog;
import tv.shorthub.system.domain.PaypalUserPlan;
import tv.shorthub.system.mapper.PaypalUserPlanMapper;
import tv.shorthub.system.service.IPaypalPaymentLogService;
import tv.shorthub.common.core.service.BaseService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.alibaba.fastjson2.JSONArray;

/**
 * paypal原始订单Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-15
 */
@Service
public class PaypalPaymentLogServiceImpl extends BaseService<PaypalPaymentLog> implements IPaypalPaymentLogService
{
    private static final Logger log = LoggerFactory.getLogger(PaypalPaymentLogServiceImpl.class);

    @Autowired
    private PaypalPaymentLogMapper paypalPaymentLogMapper;

    @Autowired
    private PayPalPaymentService payPalPaymentService;

    @Autowired
    private PayPalSubscriptionService payPalSubscriptionService;

    @Autowired
    CloudflareQueueService cloudflareQueueService;

    @Autowired
    PaypalUserPlanMapper paypalUserPlanMapper;

    @Override
    public PaypalPaymentLogMapper getMapper() {
        return paypalPaymentLogMapper;
    }

    /**
     * 查询paypal原始订单
     *
     * @param id paypal原始订单主键
     * @return paypal原始订单
     */
    @Override
    public PaypalPaymentLog getById(Long id)
    {
        return paypalPaymentLogMapper.selectById(id);
    }

    /**
     * 查询paypal原始订单列表
     *
     * @param query paypal原始订单
     * @return paypal原始订单
     */
    @Override
    public List<PaypalPaymentLog> selectList(PaypalPaymentLog query)
    {
        QueryWrapper<PaypalPaymentLog> queryWrapper = new QueryWrapper<>(query);
        if (query.getParams().containsKey("capturesId") && query.getParams().get("capturesId") != null) {
            String capturesId = (String) query.getParams().get("capturesId");
            queryWrapper.apply("JSON_EXTRACT(raw_data, '$.purchase_units[0].payments.captures[0].id') = {0}", capturesId);
        }
        if (query.getParams().containsKey("email") && query.getParams().get("email") != null) {
            String email = (String) query.getParams().get("email");
            queryWrapper.apply("JSON_EXTRACT(raw_data, '$.payer.email_address') = {0}", email);
        }
        return paypalPaymentLogMapper.selectList(queryWrapper.orderByDesc("create_time"));
    }


    /**
     * 查询paypal原始订单数据汇总
     *
     * @param query paypal原始订单
     * @return paypal原始订单
     */
    @Override
    public PaypalPaymentLog getSummary(PaypalPaymentLog query)
    {
        return paypalPaymentLogMapper.getSummary(query);
    }



    /**
     * 查询自定义分析数据
     *
     * @param query paypal原始订单
     * @return paypal原始订单
     */
    @Override
    public List<PaypalPaymentLog> summary(SummaryRequest query)
    {
        return paypalPaymentLogMapper.summary(query);
    }

    @Override
    public PaypalPaymentLog allSummary(SummaryRequest query)
    {
        return paypalPaymentLogMapper.allSummary(query);
    }

    /**
     * 新增paypal原始订单
     *
     * @param paypalPaymentLog paypal原始订单
     * @return 结果
     */
    @Override
    public int insert(PaypalPaymentLog paypalPaymentLog)
    {
        paypalPaymentLog.setCreateTime(DateUtils.getNowDate());
        return paypalPaymentLogMapper.insert(paypalPaymentLog);
    }

    /**
     * 修改paypal原始订单
     *
     * @param paypalPaymentLog paypal原始订单
     * @return 结果
     */
    @Override
    public int update(PaypalPaymentLog paypalPaymentLog)
    {
        paypalPaymentLog.setUpdateTime(DateUtils.getNowDate());
        return paypalPaymentLogMapper.updateById(paypalPaymentLog);
    }

    /**
     * 批量删除paypal原始订单
     *
     * @param ids 需要删除的paypal原始订单主键
     * @return 结果
     */
    @Override
    public int deleteByIds(List<Long> ids)
    {
        return paypalPaymentLogMapper.deleteBatchIds(ids);
    }

    /**
     * 删除paypal原始订单信息
     *
     * @param id paypal原始订单主键
     * @return 结果
     */
    @Override
    public int deleteById(Long id)
    {
        return paypalPaymentLogMapper.deleteById(id);
    }

    /**
     * 更新PayPal订单状态并同步到本地订单
     *
     * @param orderNo 订单号
     * @return 更新结果
     */
    @Override
    public JSONObject updateOrderStatus(String orderNo) {
        // 查询PayPal支付记录
        PaypalPaymentLog paymentLog = new PaypalPaymentLog();
        paymentLog.setOrderNo(orderNo);
        List<PaypalPaymentLog> paymentLogs = selectList(paymentLog);
        if (paymentLogs == null || paymentLogs.isEmpty()) {
            throw new RuntimeException("未找到对应的PayPal支付记录");
        }
        PaypalPaymentLog paypalLog = paymentLogs.getFirst();

        try {
            PaypalConfigStorageHolder.set(paypalLog.getClientId());
            JSONObject rawData;

            // 根据支付方式查询不同的状态
            if (PayPalConstants.PaymentMethod.SUBSCRIPTION.equals(paypalLog.getPaymentMethod())) {
                rawData = payPalSubscriptionService.querySubscription(paypalLog.getPaymentId());
            } else {
                rawData = payPalPaymentService.queryOrder(paypalLog.getPaymentId());
            }

            log.info("paypal订单状态: {}", rawData);

            if (rawData != null) {
                String paypalState = rawData.getString("status");
                paypalLog.setState(paypalState);
                paypalLog.setRawData(rawData);
                update(paypalLog);

                // 检查退款状态
                if (PayPalConstants.PaymentMethod.SUBSCRIPTION.equals(paypalLog.getPaymentMethod())) {
                    // 订阅支付检查退款
                    if (rawData.containsKey("billing_info")) {
                        JSONObject billingInfo = rawData.getJSONObject("billing_info");
                        if (billingInfo.containsKey("last_payment")) {
                            JSONObject lastPayment = billingInfo.getJSONObject("last_payment");
                            if (lastPayment.containsKey("status") && 
                                "REFUNDED".equalsIgnoreCase(lastPayment.getString("status"))) {
                                rawData.put("hasRefund", true);
                            }
                        }
                    }


                    // 检查订阅是否已取消
                    if ("CANCELLED".equalsIgnoreCase(paypalState) ||
                            "SUSPENDED".equalsIgnoreCase(paypalState) ||
                            "EXPIRED".equalsIgnoreCase(paypalState)) {
                        rawData.put("isUnsubscribed", true);
                    }

                    // 检查订阅是否已暂停
                    if ("SUSPENDED".equalsIgnoreCase(paypalState)) {
                        rawData.put("isSuspended", true);
                    }

                    // 检查订阅是否已过期
                    if ("EXPIRED".equalsIgnoreCase(paypalState)) {
                        rawData.put("isExpired", true);
                    }
                } else {
                    // 普通支付检查退款
                    if (rawData.containsKey("purchase_units")) {
                        JSONArray purchaseUnits = rawData.getJSONArray("purchase_units");
                        if (!purchaseUnits.isEmpty()) {
                            JSONObject purchaseUnit = purchaseUnits.getJSONObject(0);
                            if (purchaseUnit.containsKey("payments")) {
                                JSONObject payments = purchaseUnit.getJSONObject("payments");
                                if (payments.containsKey("refunds") && 
                                    !payments.getJSONArray("refunds").isEmpty()) {
                                    rawData.put("hasRefund", true);
                                }
                            }
                        }
                    }
                }
            }

            JSONObject queueData = new JSONObject();
            queueData.put("orderNo", orderNo);
            cloudflareQueueService.sendMessage(CloudflareQueueEnum.PAYPAL_ORDER_UPDATE.getValue(), queueData);
            log.info("最新PayPal订单数据:orderNo:{}, rawData:{}", orderNo, rawData);
            return rawData;
        } catch (Exception e) {
            log.error("更新PayPal订单状态异常: {}", orderNo, e);
            throw new RuntimeException("更新PayPal订单状态失败: " + e.getMessage());
        }
    }

    /**
     * 处理PayPal退款
     *
     * @param orderNo 订单号
     * @param reason 退款原因
     * @return 退款结果
     */
    @Override
    public JSONObject processRefund(String orderNo, String reason) {
        // 查询PayPal支付记录
        PaypalPaymentLog paymentLog = new PaypalPaymentLog();
        paymentLog.setOrderNo(orderNo);
        List<PaypalPaymentLog> paymentLogs = selectList(paymentLog);
        if (paymentLogs == null || paymentLogs.isEmpty()) {
            throw new RuntimeException("未找到对应的PayPal支付记录");
        }
        PaypalPaymentLog paypalLog = paymentLogs.getFirst();
        if (!"completed".equalsIgnoreCase(paypalLog.getState()) && !"CANCELLED".equalsIgnoreCase(paypalLog.getState())) {
            throw new RuntimeException("PayPal支付记录状态不正确");
        }

        try {
            PaypalConfigStorageHolder.set(paypalLog.getClientId());
            // 调用PayPal退款接口
            JSONObject result = payPalPaymentService.refundPayment(
                paypalLog.getPaymentId(),
                paypalLog.getTotalAmount().toString(),
                paypalLog.getCurrency()
            );
            log.info("退款结果: {}", result);

            // 检查退款结果
            if (result != null && "COMPLETED".equalsIgnoreCase(result.getString("status"))) {
                // 更新PayPal状态
                updateOrderStatus(orderNo);
                return result;
            } else {
                log.error("PayPal退款失败: {}", result);
                throw new RuntimeException("PayPal退款失败: " + (result != null ? result.getString("message") : "未知错误"));
            }
        } catch (Exception e) {
            log.error("退款操作异常", e);
            throw new RuntimeException("退款操作失败: " + e.getMessage());
        }
    }

    /**
     * 处理PayPal退订
     *
     * @param orderNo 订单号
     * @param reason 退订原因
     * @return 退订结果
     */
    @Override
    public JSONObject processUnsubscribe(String orderNo, String reason) {
        // 查询PayPal支付记录
        PaypalPaymentLog paymentLog = new PaypalPaymentLog();
        paymentLog.setOrderNo(orderNo);
        List<PaypalPaymentLog> paymentLogs = selectList(paymentLog);
        if (paymentLogs == null || paymentLogs.isEmpty()) {
            throw new RuntimeException("未找到对应的PayPal支付记录");
        }
        PaypalPaymentLog paypalLog = paymentLogs.getFirst();

        try {
            PaypalConfigStorageHolder.set(paypalLog.getClientId());
            if (paypalLog.getPaymentMethod().equals("RECHARGE")) {
                // 取消签约支付
                int update = paypalUserPlanMapper.update(new UpdateWrapper<PaypalUserPlan>().eq("order_no", orderNo).set("enabled", false));
                log.info("取消签约支付: orderNo={}, size={}", orderNo, update);
            } else {
//                // 调用PayPal退订接口
//                JSONObject result = payPalSubscriptionService.cancelSubscription(paypalLog.getPaymentId());
//                log.info("退订结果: {}", result);
//
//                // 检查退订结果
//                if (null == result) {
//                    // 更新PayPal状态
//                    updateOrderStatus(orderNo);
//                    return null;
//                } else {
//                    log.error("PayPal退订失败: {}", result);
//                }
                return null;
            }

            return null;
           
        } catch (Exception e) {
            log.error("退订操作异常", e);
        }
        return null;
    }
}
