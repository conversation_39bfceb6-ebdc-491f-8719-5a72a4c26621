package tv.shorthub.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import tv.shorthub.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tv.shorthub.system.mapper.PaypalWebhookLogMapper;
import tv.shorthub.system.domain.PaypalWebhookLog;
import tv.shorthub.system.service.IPaypalWebhookLogService;
import tv.shorthub.common.core.service.BaseService;

/**
 * webhook事件日志Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-27
 */
@Service
public class PaypalWebhookLogServiceImpl extends BaseService<PaypalWebhookLog> implements IPaypalWebhookLogService
{
    @Autowired
    private PaypalWebhookLogMapper paypalWebhookLogMapper;

    @Override
    public PaypalWebhookLogMapper getMapper() {
        return paypalWebhookLogMapper;
    }

    /**
     * 查询webhook事件日志
     *
     * @param id webhook事件日志主键
     * @return webhook事件日志
     */
    @Override
    public PaypalWebhookLog getById(Long id)
    {
        return paypalWebhookLogMapper.selectById(id);
    }

    /**
     * 查询webhook事件日志列表
     *
     * @param query webhook事件日志
     * @return webhook事件日志
     */
    @Override
    public List<PaypalWebhookLog> selectList(PaypalWebhookLog query)
    {
        return paypalWebhookLogMapper.selectList(new QueryWrapper<>(query));
    }


    /**
     * 查询webhook事件日志数据汇总
     *
     * @param query webhook事件日志
     * @return webhook事件日志
     */
    @Override
    public PaypalWebhookLog getSummary(PaypalWebhookLog query)
    {
        return paypalWebhookLogMapper.getSummary(query);
    }



    /**
     * 查询自定义分析数据
     *
     * @param query webhook事件日志
     * @return webhook事件日志
     */
    @Override
    public List<PaypalWebhookLog> summary(SummaryRequest query)
    {
        return paypalWebhookLogMapper.summary(query);
    }

    @Override
    public PaypalWebhookLog allSummary(SummaryRequest query)
    {
        return paypalWebhookLogMapper.allSummary(query);
    }

    /**
     * 新增webhook事件日志
     *
     * @param paypalWebhookLog webhook事件日志
     * @return 结果
     */
    @Override
    public int insert(PaypalWebhookLog paypalWebhookLog)
    {
        paypalWebhookLog.setCreateTime(DateUtils.getNowDate());
        return paypalWebhookLogMapper.insert(paypalWebhookLog);
    }

    /**
     * 修改webhook事件日志
     *
     * @param paypalWebhookLog webhook事件日志
     * @return 结果
     */
    @Override
    public int update(PaypalWebhookLog paypalWebhookLog)
    {
        paypalWebhookLog.setUpdateTime(DateUtils.getNowDate());
        return paypalWebhookLogMapper.updateById(paypalWebhookLog);
    }

    /**
     * 批量删除webhook事件日志
     *
     * @param ids 需要删除的webhook事件日志主键
     * @return 结果
     */
    @Override
    public int deleteByIds(List<Long> ids)
    {
        return paypalWebhookLogMapper.deleteBatchIds(ids);
    }

    /**
     * 删除webhook事件日志信息
     *
     * @param id webhook事件日志主键
     * @return 结果
     */
    @Override
    public int deleteById(Long id)
    {
        return paypalWebhookLogMapper.deleteById(id);
    }
}
