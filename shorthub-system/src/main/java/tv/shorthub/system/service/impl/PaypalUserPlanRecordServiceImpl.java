package tv.shorthub.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import tv.shorthub.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tv.shorthub.system.mapper.PaypalUserPlanRecordMapper;
import tv.shorthub.system.domain.PaypalUserPlanRecord;
import tv.shorthub.system.service.IPaypalUserPlanRecordService;
import tv.shorthub.common.core.service.BaseService;

/**
 * paypal签约续订记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-27
 */
@Service
public class PaypalUserPlanRecordServiceImpl extends BaseService<PaypalUserPlanRecord> implements IPaypalUserPlanRecordService
{
    @Autowired
    private PaypalUserPlanRecordMapper paypalUserPlanRecordMapper;

    @Override
    public PaypalUserPlanRecordMapper getMapper() {
        return paypalUserPlanRecordMapper;
    }

    /**
     * 查询paypal签约续订记录
     *
     * @param id paypal签约续订记录主键
     * @return paypal签约续订记录
     */
    @Override
    public PaypalUserPlanRecord getById(Long id)
    {
        return paypalUserPlanRecordMapper.selectById(id);
    }

    /**
     * 查询paypal签约续订记录列表
     *
     * @param query paypal签约续订记录
     * @return paypal签约续订记录
     */
    @Override
    public List<PaypalUserPlanRecord> selectList(PaypalUserPlanRecord query)
    {
        return paypalUserPlanRecordMapper.selectList(new QueryWrapper<>(query));
    }


    /**
     * 查询paypal签约续订记录数据汇总
     *
     * @param query paypal签约续订记录
     * @return paypal签约续订记录
     */
    @Override
    public PaypalUserPlanRecord getSummary(PaypalUserPlanRecord query)
    {
        return paypalUserPlanRecordMapper.getSummary(query);
    }



    /**
     * 查询自定义分析数据
     *
     * @param query paypal签约续订记录
     * @return paypal签约续订记录
     */
    @Override
    public List<PaypalUserPlanRecord> summary(SummaryRequest query)
    {
        return paypalUserPlanRecordMapper.summary(query);
    }

    @Override
    public PaypalUserPlanRecord allSummary(SummaryRequest query)
    {
        return paypalUserPlanRecordMapper.allSummary(query);
    }

    /**
     * 新增paypal签约续订记录
     *
     * @param paypalUserPlanRecord paypal签约续订记录
     * @return 结果
     */
    @Override
    public int insert(PaypalUserPlanRecord paypalUserPlanRecord)
    {
        paypalUserPlanRecord.setCreateTime(DateUtils.getNowDate());
        return paypalUserPlanRecordMapper.insert(paypalUserPlanRecord);
    }

    /**
     * 修改paypal签约续订记录
     *
     * @param paypalUserPlanRecord paypal签约续订记录
     * @return 结果
     */
    @Override
    public int update(PaypalUserPlanRecord paypalUserPlanRecord)
    {
        paypalUserPlanRecord.setUpdateTime(DateUtils.getNowDate());
        return paypalUserPlanRecordMapper.updateById(paypalUserPlanRecord);
    }

    /**
     * 批量删除paypal签约续订记录
     *
     * @param ids 需要删除的paypal签约续订记录主键
     * @return 结果
     */
    @Override
    public int deleteByIds(List<Long> ids)
    {
        return paypalUserPlanRecordMapper.deleteBatchIds(ids);
    }

    /**
     * 删除paypal签约续订记录信息
     *
     * @param id paypal签约续订记录主键
     * @return 结果
     */
    @Override
    public int deleteById(Long id)
    {
        return paypalUserPlanRecordMapper.deleteById(id);
    }
}
