package tv.shorthub.system.service;

import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import tv.shorthub.system.domain.SysDingtalkLog;
import tv.shorthub.common.core.service.IBaseService;

/**
 * 钉钉发送日志Service接口
 *
 * <AUTHOR>
 * @date 2025-06-17
 */
public interface ISysDingtalkLogService extends IBaseService<SysDingtalkLog>
{
    /**
     * 查询钉钉发送日志
     *
     * @param id 钉钉发送日志主键
     * @return 钉钉发送日志
     */
    public SysDingtalkLog getById(Long id);

    /**
     * 查询钉钉发送日志数据汇总
     *
     * @param query 钉钉发送日志
     * @return 钉钉发送日志数据汇总
     */
    public SysDingtalkLog getSummary(SysDingtalkLog query);

    /**
     * 查询钉钉发送日志列表
     *
     * @param query 钉钉发送日志
     * @return 钉钉发送日志集合
     */
    public List<SysDingtalkLog> selectList(SysDingtalkLog query);

    /**
     * 新增钉钉发送日志
     *
     * @param sysDingtalkLog 钉钉发送日志
     * @return 结果
     */
    public int insert(SysDingtalkLog sysDingtalkLog);

    /**
     * 修改钉钉发送日志
     *
     * @param sysDingtalkLog 钉钉发送日志
     * @return 结果
     */
    public int update(SysDingtalkLog sysDingtalkLog);

    /**
     * 批量删除钉钉发送日志
     *
     * @param ids 需要删除的钉钉发送日志主键集合
     * @return 结果
     */
    public int deleteByIds(List<Long> ids);

    /**
     * 删除钉钉发送日志信息
     *
     * @param id 钉钉发送日志主键
     * @return 结果
     */
    public int deleteById(Long id);


    /**
     * 查询自定义分析数据
     *
     * @param query 钉钉发送日志
     * @return 钉钉发送日志集合
     */
    public List<SysDingtalkLog> summary(SummaryRequest query);

    SysDingtalkLog allSummary(SummaryRequest query);
}
