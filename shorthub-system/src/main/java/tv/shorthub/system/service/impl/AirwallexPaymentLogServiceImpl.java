package tv.shorthub.system.service.impl;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import lombok.extern.slf4j.Slf4j;
import tv.shorthub.airwallex.service.AirwallexConfigStorageHolder;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import tv.shorthub.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tv.shorthub.paypal.config.PaypalConfigStorageHolder;
import tv.shorthub.system.domain.AirwallexUserPlan;
import tv.shorthub.system.domain.PaypalPaymentLog;
import tv.shorthub.system.domain.PaypalUserPlan;
import tv.shorthub.system.mapper.AirwallexPaymentLogMapper;
import tv.shorthub.system.domain.AirwallexPaymentLog;
import tv.shorthub.system.mapper.AirwallexUserPlanMapper;
import tv.shorthub.system.service.IAirwallexPaymentLogService;
import tv.shorthub.common.core.service.BaseService;

/**
 * airwallex支付日志Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-17
 */
@Service
@Slf4j
public class AirwallexPaymentLogServiceImpl extends BaseService<AirwallexPaymentLog> implements IAirwallexPaymentLogService
{
    @Autowired
    private AirwallexPaymentLogMapper airwallexPaymentLogMapper;

    @Autowired
    AirwallexUserPlanMapper airwallexUserPlanMapper;

    @Override
    public AirwallexPaymentLogMapper getMapper() {
        return airwallexPaymentLogMapper;
    }

    /**
     * 查询airwallex支付日志
     *
     * @param id airwallex支付日志主键
     * @return airwallex支付日志
     */
    @Override
    public AirwallexPaymentLog getById(Long id)
    {
        return airwallexPaymentLogMapper.selectById(id);
    }

    /**
     * 查询airwallex支付日志列表
     *
     * @param query airwallex支付日志
     * @return airwallex支付日志
     */
    @Override
    public List<AirwallexPaymentLog> selectList(AirwallexPaymentLog query)
    {
        return airwallexPaymentLogMapper.selectList(new QueryWrapper<>(query));
    }


    /**
     * 查询airwallex支付日志数据汇总
     *
     * @param query airwallex支付日志
     * @return airwallex支付日志
     */
    @Override
    public AirwallexPaymentLog getSummary(AirwallexPaymentLog query)
    {
        return airwallexPaymentLogMapper.getSummary(query);
    }



    /**
     * 查询自定义分析数据
     *
     * @param query airwallex支付日志
     * @return airwallex支付日志
     */
    @Override
    public List<AirwallexPaymentLog> summary(SummaryRequest query)
    {
        return airwallexPaymentLogMapper.summary(query);
    }

    @Override
    public AirwallexPaymentLog allSummary(SummaryRequest query)
    {
        return airwallexPaymentLogMapper.allSummary(query);
    }

    /**
     * 新增airwallex支付日志
     *
     * @param airwallexPaymentLog airwallex支付日志
     * @return 结果
     */
    @Override
    public int insert(AirwallexPaymentLog airwallexPaymentLog)
    {
        airwallexPaymentLog.setCreateTime(DateUtils.getNowDate());
        return airwallexPaymentLogMapper.insert(airwallexPaymentLog);
    }

    /**
     * 修改airwallex支付日志
     *
     * @param airwallexPaymentLog airwallex支付日志
     * @return 结果
     */
    @Override
    public int update(AirwallexPaymentLog airwallexPaymentLog)
    {
        airwallexPaymentLog.setUpdateTime(DateUtils.getNowDate());
        return airwallexPaymentLogMapper.updateById(airwallexPaymentLog);
    }

    /**
     * 批量删除airwallex支付日志
     *
     * @param ids 需要删除的airwallex支付日志主键
     * @return 结果
     */
    @Override
    public int deleteByIds(List<Long> ids)
    {
        return airwallexPaymentLogMapper.deleteBatchIds(ids);
    }

    /**
     * 删除airwallex支付日志信息
     *
     * @param id airwallex支付日志主键
     * @return 结果
     */
    @Override
    public int deleteById(Long id)
    {
        return airwallexPaymentLogMapper.deleteById(id);
    }


    /**
     * 处理PayPal退订
     *
     * @param orderNo 订单号
     * @param reason 退订原因
     * @return 退订结果
     */
    @Override
    public JSONObject processUnsubscribe(String orderNo, String reason) {
        // 查询PayPal支付记录
        AirwallexPaymentLog paymentLog = new AirwallexPaymentLog();
        paymentLog.setOrderNo(orderNo);
        List<AirwallexPaymentLog> paymentLogs = selectList(paymentLog);
        if (paymentLogs == null || paymentLogs.isEmpty()) {
            throw new RuntimeException("未找到对应的PayPal支付记录");
        }

        try {

            int update = airwallexUserPlanMapper.update(new UpdateWrapper<AirwallexUserPlan>().eq("order_no", orderNo).set("enabled", false));
            log.info("[Airwallex]取消签约支付: orderNo={}, size={}", orderNo, update);
            return null;

        } catch (Exception e) {
            log.error("退订操作异常", e);
        }
        return null;
    }
}
