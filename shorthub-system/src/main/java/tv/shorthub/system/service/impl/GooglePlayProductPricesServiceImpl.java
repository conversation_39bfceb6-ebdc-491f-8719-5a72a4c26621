package tv.shorthub.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import tv.shorthub.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tv.shorthub.system.mapper.GooglePlayProductPricesMapper;
import tv.shorthub.system.domain.GooglePlayProductPrices;
import tv.shorthub.system.service.IGooglePlayProductPricesService;
import tv.shorthub.common.core.service.BaseService;

/**
 * Google Play产品价格配置Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
@Service
public class GooglePlayProductPricesServiceImpl extends BaseService<GooglePlayProductPrices> implements IGooglePlayProductPricesService
{
    @Autowired
    private GooglePlayProductPricesMapper googlePlayProductPricesMapper;

    @Override
    public GooglePlayProductPricesMapper getMapper() {
        return googlePlayProductPricesMapper;
    }

    /**
     * 查询Google Play产品价格配置
     *
     * @param id Google Play产品价格配置主键
     * @return Google Play产品价格配置
     */
    @Override
    public GooglePlayProductPrices getById(Long id)
    {
        return googlePlayProductPricesMapper.selectById(id);
    }

    /**
     * 查询Google Play产品价格配置列表
     *
     * @param query Google Play产品价格配置
     * @return Google Play产品价格配置
     */
    @Override
    public List<GooglePlayProductPrices> selectList(GooglePlayProductPrices query)
    {
        return googlePlayProductPricesMapper.selectList(new QueryWrapper<>(query));
    }


    /**
     * 查询Google Play产品价格配置数据汇总
     *
     * @param query Google Play产品价格配置
     * @return Google Play产品价格配置
     */
    @Override
    public GooglePlayProductPrices getSummary(GooglePlayProductPrices query)
    {
        return googlePlayProductPricesMapper.getSummary(query);
    }



    /**
     * 查询自定义分析数据
     *
     * @param query Google Play产品价格配置
     * @return Google Play产品价格配置
     */
    @Override
    public List<GooglePlayProductPrices> summary(SummaryRequest query)
    {
        return googlePlayProductPricesMapper.summary(query);
    }

    @Override
    public GooglePlayProductPrices allSummary(SummaryRequest query)
    {
        return googlePlayProductPricesMapper.allSummary(query);
    }

    /**
     * 新增Google Play产品价格配置
     *
     * @param googlePlayProductPrices Google Play产品价格配置
     * @return 结果
     */
    @Override
    public int insert(GooglePlayProductPrices googlePlayProductPrices)
    {
        googlePlayProductPrices.setCreateTime(DateUtils.getNowDate());
        return googlePlayProductPricesMapper.insert(googlePlayProductPrices);
    }

    /**
     * 修改Google Play产品价格配置
     *
     * @param googlePlayProductPrices Google Play产品价格配置
     * @return 结果
     */
    @Override
    public int update(GooglePlayProductPrices googlePlayProductPrices)
    {
        googlePlayProductPrices.setUpdateTime(DateUtils.getNowDate());
        return googlePlayProductPricesMapper.updateById(googlePlayProductPrices);
    }

    /**
     * 批量删除Google Play产品价格配置
     *
     * @param ids 需要删除的Google Play产品价格配置主键
     * @return 结果
     */
    @Override
    public int deleteByIds(List<Long> ids)
    {
        return googlePlayProductPricesMapper.deleteBatchIds(ids);
    }

    /**
     * 删除Google Play产品价格配置信息
     *
     * @param id Google Play产品价格配置主键
     * @return 结果
     */
    @Override
    public int deleteById(Long id)
    {
        return googlePlayProductPricesMapper.deleteById(id);
    }
}
