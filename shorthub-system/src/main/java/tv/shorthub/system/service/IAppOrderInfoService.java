package tv.shorthub.system.service;

import tv.shorthub.system.mapper.AppOrderInfoMapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import tv.shorthub.system.domain.AppOrderInfo;
import tv.shorthub.common.core.service.IBaseService;

/**
 * 充值订单Service接口
 *
 * <AUTHOR>
 * @date 2025-05-10
 */
public interface IAppOrderInfoService extends IBaseService<AppOrderInfo>
{
    /**
     * 查询充值订单
     *
     * @param id 充值订单主键
     * @return 充值订单
     */
    public AppOrderInfo getById(Long id);

    /**
     * 查询充值订单数据汇总
     *
     * @param query 充值订单
     * @return 充值订单数据汇总
     */
    public AppOrderInfo getSummary(AppOrderInfo query);

    /**
     * 查询充值订单列表
     *
     * @param query 充值订单
     * @return 充值订单集合
     */
    public List<AppOrderInfo> selectList(AppOrderInfo query);

    /**
     * 新增充值订单
     *
     * @param appOrderInfo 充值订单
     * @return 结果
     */
    public int insert(AppOrderInfo appOrderInfo);

    /**
     * 修改充值订单
     *
     * @param appOrderInfo 充值订单
     * @return 结果
     */
    public int update(AppOrderInfo appOrderInfo);

    /**
     * 批量删除充值订单
     *
     * @param ids 需要删除的充值订单主键集合
     * @return 结果
     */
    public int deleteByIds(List<Long> ids);

    /**
     * 删除充值订单信息
     *
     * @param id 充值订单主键
     * @return 结果
     */
    public int deleteById(Long id);


    /**
     * 查询自定义分析数据
     *
     * @param query 充值订单
     * @return 充值订单集合
     */
    public List<AppOrderInfo> summary(SummaryRequest query);

    AppOrderInfo allSummary(SummaryRequest query);

    AppOrderInfoMapper getMapper();

    /**
     * 退款
     *
     * @param appOrderInfo 充值订单
     * @return 结果
     */
    public int refund(AppOrderInfo appOrderInfo);

    /**
     * 退订
     *
     * @param appOrderInfo 充值订单
     * @return 结果
     */
    public int unsubscribe(AppOrderInfo appOrderInfo);

    /**
     * 更新订单支付状态
     *
     * @param orderNo 订单号
     * @return 更新结果
     */
    int updateOrderStatus(String orderNo);


}
