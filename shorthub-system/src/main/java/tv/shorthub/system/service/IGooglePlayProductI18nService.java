package tv.shorthub.system.service;

import tv.shorthub.system.mapper.GooglePlayProductI18nMapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import tv.shorthub.system.domain.GooglePlayProductI18n;
import tv.shorthub.common.core.service.IBaseService;

/**
 * Google Play产品多语言信息Service接口
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
public interface IGooglePlayProductI18nService extends IBaseService<GooglePlayProductI18n>
{
    /**
     * 查询Google Play产品多语言信息
     *
     * @param id Google Play产品多语言信息主键
     * @return Google Play产品多语言信息
     */
    public GooglePlayProductI18n getById(Long id);

    /**
     * 查询Google Play产品多语言信息数据汇总
     *
     * @param query Google Play产品多语言信息
     * @return Google Play产品多语言信息数据汇总
     */
    public GooglePlayProductI18n getSummary(GooglePlayProductI18n query);

    /**
     * 查询Google Play产品多语言信息列表
     *
     * @param query Google Play产品多语言信息
     * @return Google Play产品多语言信息集合
     */
    public List<GooglePlayProductI18n> selectList(GooglePlayProductI18n query);

    /**
     * 新增Google Play产品多语言信息
     *
     * @param googlePlayProductI18n Google Play产品多语言信息
     * @return 结果
     */
    public int insert(GooglePlayProductI18n googlePlayProductI18n);

    /**
     * 修改Google Play产品多语言信息
     *
     * @param googlePlayProductI18n Google Play产品多语言信息
     * @return 结果
     */
    public int update(GooglePlayProductI18n googlePlayProductI18n);

    /**
     * 批量删除Google Play产品多语言信息
     *
     * @param ids 需要删除的Google Play产品多语言信息主键集合
     * @return 结果
     */
    public int deleteByIds(List<Long> ids);

    /**
     * 删除Google Play产品多语言信息信息
     *
     * @param id Google Play产品多语言信息主键
     * @return 结果
     */
    public int deleteById(Long id);


    /**
     * 查询自定义分析数据
     *
     * @param query Google Play产品多语言信息
     * @return Google Play产品多语言信息集合
     */
    public List<GooglePlayProductI18n> summary(SummaryRequest query);

    GooglePlayProductI18n allSummary(SummaryRequest query);

    GooglePlayProductI18nMapper getMapper();
}
