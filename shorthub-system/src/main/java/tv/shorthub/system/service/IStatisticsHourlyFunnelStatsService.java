package tv.shorthub.system.service;

import tv.shorthub.system.mapper.StatisticsHourlyFunnelStatsMapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.Date;
import java.util.List;
import tv.shorthub.system.domain.StatisticsHourlyFunnelStats;
import tv.shorthub.common.core.service.IBaseService;

/**
 * 每小时漏斗分析统计Service接口
 *
 * <AUTHOR>
 * @date 2025-07-07
 */
public interface IStatisticsHourlyFunnelStatsService extends IBaseService<StatisticsHourlyFunnelStats>
{
    /**
     * 查询每小时漏斗分析统计
     *
     * @param id 每小时漏斗分析统计主键
     * @return 每小时漏斗分析统计
     */
    public StatisticsHourlyFunnelStats getById(Long id);

    /**
     * 查询每小时漏斗分析统计数据汇总
     *
     * @param query 每小时漏斗分析统计
     * @return 每小时漏斗分析统计数据汇总
     */
    public StatisticsHourlyFunnelStats getSummary(StatisticsHourlyFunnelStats query);

    /**
     * 查询每小时漏斗分析统计列表
     *
     * @param query 每小时漏斗分析统计
     * @return 每小时漏斗分析统计集合
     */
    public List<StatisticsHourlyFunnelStats> selectList(StatisticsHourlyFunnelStats query);

    /**
     * 新增每小时漏斗分析统计
     *
     * @param statisticsHourlyFunnelStats 每小时漏斗分析统计
     * @return 结果
     */
    public int insert(StatisticsHourlyFunnelStats statisticsHourlyFunnelStats);

    /**
     * 修改每小时漏斗分析统计
     *
     * @param statisticsHourlyFunnelStats 每小时漏斗分析统计
     * @return 结果
     */
    public int update(StatisticsHourlyFunnelStats statisticsHourlyFunnelStats);

    /**
     * 批量删除每小时漏斗分析统计
     *
     * @param ids 需要删除的每小时漏斗分析统计主键集合
     * @return 结果
     */
    public int deleteByIds(List<Long> ids);

    /**
     * 删除每小时漏斗分析统计信息
     *
     * @param id 每小时漏斗分析统计主键
     * @return 结果
     */
    public int deleteById(Long id);


    /**
     * 查询自定义分析数据
     *
     * @param query 每小时漏斗分析统计
     * @return 每小时漏斗分析统计集合
     */
    public List<StatisticsHourlyFunnelStats> summary(SummaryRequest query);

    StatisticsHourlyFunnelStats allSummary(SummaryRequest query);

    StatisticsHourlyFunnelStatsMapper getMapper();

    /**
     * 根据时间范围查询漏斗分析统计列表
     *
     * @param query 查询条件
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计列表
     */
    List<StatisticsHourlyFunnelStats> selectListByTimeRange(StatisticsHourlyFunnelStats query, Date startTime, Date endTime);

    /**
     * 根据时间范围获取漏斗分析统计汇总数据
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param appid 应用ID
     * @param tfid 推广链接ID
     * @return 汇总统计数据
     */
    StatisticsHourlyFunnelStats getRangeSummary(Date startTime, Date endTime, String appid, String tfid);

    /**
     * 根据 tfid 列表获取汇总数据
     *
     * @param tfids tfid列表
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 汇总统计数据
     */
    StatisticsHourlyFunnelStats getSummaryByTfids(List<String> tfids, Date startTime, Date endTime);

    /**
     * 根据创建者获取汇总数据
     *
     * @param creator 创建者用户名
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 汇总统计数据
     */
    StatisticsHourlyFunnelStats getSummaryByCreator(String creator, Date startTime, Date endTime);
}
