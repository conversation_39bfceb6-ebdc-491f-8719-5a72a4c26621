package tv.shorthub.system.service;

import tv.shorthub.system.mapper.AppPromotionMapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import tv.shorthub.system.domain.AppPromotion;
import tv.shorthub.common.core.service.IBaseService;
import tv.shorthub.system.dto.PromotionOptionDto;

/**
 * 推广链接Service接口
 *
 * <AUTHOR>
 * @date 2025-05-15
 */
public interface IAppPromotionService extends IBaseService<AppPromotion>
{
    /**
     * 查询推广链接
     *
     * @param id 推广链接主键
     * @return 推广链接
     */
    public AppPromotion getById(Long id);

    /**
     * 查询推广链接数据汇总
     *
     * @param query 推广链接
     * @return 推广链接数据汇总
     */
    public AppPromotion getSummary(AppPromotion query);

    /**
     * 查询推广链接列表
     *
     * @param query 推广链接
     * @return 推广链接集合
     */
    public List<AppPromotion> selectList(AppPromotion query);

    /**
     * 新增推广链接
     *
     * @param appPromotion 推广链接
     * @return 结果
     */
    public int insert(AppPromotion appPromotion);

    /**
     * 修改推广链接
     *
     * @param appPromotion 推广链接
     * @return 结果
     */
    public int update(AppPromotion appPromotion);

    int allocation(AppPromotion appPromotion);

    /**
     * 批量删除推广链接
     *
     * @param ids 需要删除的推广链接主键集合
     * @return 结果
     */
    public int deleteByIds(List<Long> ids);

    /**
     * 删除推广链接信息
     *
     * @param id 推广链接主键
     * @return 结果
     */
    public int deleteById(Long id);


    /**
     * 查询自定义分析数据
     *
     * @param query 推广链接
     * @return 推广链接集合
     */
    public List<AppPromotion> summary(SummaryRequest query);

    AppPromotion allSummary(SummaryRequest query);

    AppPromotionMapper getMapper();

    /**
     * 根据当前用户权限获取推广链接下拉框选项
     *
     * @param appid (可选) 仅当系统管理员需要筛选特定应用时使用
     * @param creatorName (可选) 用于筛选特定投手创建的链接
     * @return 选项列表
     */
    List<PromotionOptionDto> getPromotionOptions(String appid, String creatorName);
}
