package tv.shorthub.system.service;

import tv.shorthub.system.mapper.AppUserCollectMapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import tv.shorthub.system.domain.AppUserCollect;
import tv.shorthub.common.core.service.IBaseService;

/**
 * 用户收藏Service接口
 *
 * <AUTHOR>
 * @date 2025-05-10
 */
public interface IAppUserCollectService extends IBaseService<AppUserCollect>
{
    /**
     * 查询用户收藏
     *
     * @param id 用户收藏主键
     * @return 用户收藏
     */
    public AppUserCollect getById(Long id);

    /**
     * 查询用户收藏数据汇总
     *
     * @param query 用户收藏
     * @return 用户收藏数据汇总
     */
    public AppUserCollect getSummary(AppUserCollect query);

    /**
     * 查询用户收藏列表
     *
     * @param query 用户收藏
     * @return 用户收藏集合
     */
    public List<AppUserCollect> selectList(AppUserCollect query);

    /**
     * 新增用户收藏
     *
     * @param appUserCollect 用户收藏
     * @return 结果
     */
    public int insert(AppUserCollect appUserCollect);

    /**
     * 修改用户收藏
     *
     * @param appUserCollect 用户收藏
     * @return 结果
     */
    public int update(AppUserCollect appUserCollect);

    /**
     * 批量删除用户收藏
     *
     * @param ids 需要删除的用户收藏主键集合
     * @return 结果
     */
    public int deleteByIds(List<Long> ids);

    /**
     * 删除用户收藏信息
     *
     * @param id 用户收藏主键
     * @return 结果
     */
    public int deleteById(Long id);


    /**
     * 查询自定义分析数据
     *
     * @param query 用户收藏
     * @return 用户收藏集合
     */
    public List<AppUserCollect> summary(SummaryRequest query);

    AppUserCollect allSummary(SummaryRequest query);

    AppUserCollectMapper getMapper();
}
