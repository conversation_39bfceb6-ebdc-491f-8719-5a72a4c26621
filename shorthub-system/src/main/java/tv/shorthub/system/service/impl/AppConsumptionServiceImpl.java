package tv.shorthub.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import tv.shorthub.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tv.shorthub.system.mapper.AppConsumptionMapper;
import tv.shorthub.system.domain.AppConsumption;
import tv.shorthub.system.service.IAppConsumptionService;
import tv.shorthub.common.core.service.BaseService;

/**
 * 金币消费Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-10
 */
@Service
public class AppConsumptionServiceImpl extends BaseService<AppConsumption> implements IAppConsumptionService
{
    @Autowired
    private AppConsumptionMapper appConsumptionMapper;

    @Override
    public AppConsumptionMapper getMapper() {
        return appConsumptionMapper;
    }

    @Override
    public List<AppConsumption> getUserHistory(String userId, Integer page, int pageSize) {
        return List.of();
    }

    /**
     * 查询金币消费
     *
     * @param id 金币消费主键
     * @return 金币消费
     */
    @Override
    public AppConsumption getById(Long id)
    {
        return appConsumptionMapper.selectById(id);
    }

    /**
     * 查询金币消费列表
     *
     * @param query 金币消费
     * @return 金币消费
     */
    @Override
    public List<AppConsumption> selectList(AppConsumption query)
    {
        return appConsumptionMapper.selectList(new QueryWrapper<>(query));
    }


    /**
     * 查询金币消费数据汇总
     *
     * @param query 金币消费
     * @return 金币消费
     */
    @Override
    public AppConsumption getSummary(AppConsumption query)
    {
        return appConsumptionMapper.getSummary(query);
    }



    /**
     * 查询自定义分析数据
     *
     * @param query 金币消费
     * @return 金币消费
     */
    @Override
    public List<AppConsumption> summary(SummaryRequest query)
    {
        return appConsumptionMapper.summary(query);
    }

    @Override
    public AppConsumption allSummary(SummaryRequest query)
    {
        return appConsumptionMapper.allSummary(query);
    }

    /**
     * 新增金币消费
     *
     * @param appConsumption 金币消费
     * @return 结果
     */
    @Override
    public int insert(AppConsumption appConsumption)
    {
        appConsumption.setCreateTime(DateUtils.getNowDate());
        return appConsumptionMapper.insert(appConsumption);
    }

    /**
     * 修改金币消费
     *
     * @param appConsumption 金币消费
     * @return 结果
     */
    @Override
    public int update(AppConsumption appConsumption)
    {
        appConsumption.setUpdateTime(DateUtils.getNowDate());
        return appConsumptionMapper.updateById(appConsumption);
    }

    /**
     * 批量删除金币消费
     *
     * @param ids 需要删除的金币消费主键
     * @return 结果
     */
    @Override
    public int deleteByIds(List<Long> ids)
    {
        return appConsumptionMapper.deleteBatchIds(ids);
    }

    /**
     * 删除金币消费信息
     *
     * @param id 金币消费主键
     * @return 结果
     */
    @Override
    public int deleteById(Long id)
    {
        return appConsumptionMapper.deleteById(id);
    }
}
