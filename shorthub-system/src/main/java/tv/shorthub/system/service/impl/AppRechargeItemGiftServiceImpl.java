package tv.shorthub.system.service.impl;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;

import tv.shorthub.common.exception.ServiceException;
import tv.shorthub.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tv.shorthub.common.utils.SecurityUtils;
import tv.shorthub.system.domain.AppRechargeItem;
import tv.shorthub.system.mapper.AppRechargeItemGiftMapper;
import tv.shorthub.system.domain.AppRechargeItemGift;
import tv.shorthub.system.mapper.AppRechargeItemMapper;
import tv.shorthub.system.service.IAppRechargeItemGiftService;
import tv.shorthub.common.core.service.BaseService;

/**
 * 充值优惠Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-10
 */
@Service
@Slf4j
public class AppRechargeItemGiftServiceImpl extends BaseService<AppRechargeItemGift> implements IAppRechargeItemGiftService
{
    @Autowired
    private AppRechargeItemGiftMapper appRechargeItemGiftMapper;

    @Autowired
    private AppRechargeItemMapper appRechargeItemMapper;

    @Override
    public AppRechargeItemGiftMapper getMapper() {
        return appRechargeItemGiftMapper;
    }

    /**
     * 查询充值优惠
     *
     * @param id 充值优惠主键
     * @return 充值优惠
     */
    @Override
    public AppRechargeItemGift getById(Long id)
    {
        return appRechargeItemGiftMapper.selectById(id);
    }

    /**
     * 查询充值优惠列表
     *
     * @param query 充值优惠
     * @return 充值优惠
     */
    @Override
    public List<AppRechargeItemGift> selectList(AppRechargeItemGift query)
    {
        return appRechargeItemGiftMapper.selectList(new QueryWrapper<>(query));
    }


    /**
     * 查询充值优惠数据汇总
     *
     * @param query 充值优惠
     * @return 充值优惠
     */
    @Override
    public AppRechargeItemGift getSummary(AppRechargeItemGift query)
    {
        return appRechargeItemGiftMapper.getSummary(query);
    }



    /**
     * 查询自定义分析数据
     *
     * @param query 充值优惠
     * @return 充值优惠
     */
    @Override
    public List<AppRechargeItemGift> summary(SummaryRequest query)
    {
        return appRechargeItemGiftMapper.summary(query);
    }

    @Override
    public AppRechargeItemGift allSummary(SummaryRequest query)
    {
        return appRechargeItemGiftMapper.allSummary(query);
    }

    /**
     * 新增充值优惠
     *
     * @param appRechargeItemGift 充值优惠
     * @return 结果
     */
    @Override
    public int insert(AppRechargeItemGift appRechargeItemGift)
    {
        AppRechargeItem appRechargeItem = appRechargeItemMapper.selectById(appRechargeItemGift.getItemId());
        if (appRechargeItem == null) {
            throw new ServiceException("充值模板不存在");
        } else if (!(SecurityUtils.getUsername().equals(appRechargeItem.getCreateBy()) || SecurityUtils.isBusinessAdmin())) {
            throw new ServiceException("您没有权限修改该模板的优惠");
        }
        appRechargeItemGift.setCreateBy(SecurityUtils.getUsername());
        appRechargeItemGift.setCreateTime(DateUtils.getNowDate());
        return appRechargeItemGiftMapper.insert(appRechargeItemGift);
    }

    /**
     * 修改充值优惠
     *
     * @param appRechargeItemGift 充值优惠
     * @return 结果
     */
    @Override
    public int update(AppRechargeItemGift appRechargeItemGift)
    {
        AppRechargeItemGift oldGift = getById(appRechargeItemGift.getId());
        if (oldGift != null) {
            if (!(SecurityUtils.getUsername().equals(oldGift.getCreateBy()) || SecurityUtils.isBusinessAdmin())) {
                throw new ServiceException("您没有权限修改该优惠");
            }
            log.info("用户{}尝试修改优惠：{}", SecurityUtils.getUsername(), JSONObject.toJSONString(oldGift));
        }

        appRechargeItemGift.setUpdateBy(SecurityUtils.getUsername());
        appRechargeItemGift.setUpdateTime(DateUtils.getNowDate());
        return appRechargeItemGiftMapper.updateById(appRechargeItemGift);
    }

    /**
     * 批量删除充值优惠
     *
     * @param ids 需要删除的充值优惠主键
     * @return 结果
     */
    @Override
    public int deleteByIds(List<Long> ids)
    {
        for (Long id : ids) {
            AppRechargeItemGift item = appRechargeItemGiftMapper.selectById(id);
            if (item != null) {
                if (!(SecurityUtils.getUsername().equals(item.getCreateBy()) || SecurityUtils.isBusinessAdmin())) {
                    throw new ServiceException("您没有权限删除该优惠");
                }
                log.info("用户{}尝试删除优惠：{}", SecurityUtils.getUsername(), JSONObject.toJSONString(item));
            }
        }
        return appRechargeItemGiftMapper.deleteBatchIds(ids);
    }

    /**
     * 删除充值优惠信息
     *
     * @param id 充值优惠主键
     * @return 结果
     */
    @Override
    public int deleteById(Long id)
    {
        AppRechargeItemGift item = appRechargeItemGiftMapper.selectById(id);
        if (item != null) {
            if (!(SecurityUtils.getUsername().equals(item.getCreateBy()) || SecurityUtils.isBusinessAdmin())) {
                throw new ServiceException("您没有权限删除该优惠");
            }
            log.info("用户{}尝试删除优惠：{}", SecurityUtils.getUsername(), JSONObject.toJSONString(item));
        }
        return appRechargeItemGiftMapper.deleteById(id);
    }
}
