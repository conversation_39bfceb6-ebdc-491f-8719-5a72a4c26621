package tv.shorthub.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import tv.shorthub.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tv.shorthub.system.mapper.PaypalUserPlanMapper;
import tv.shorthub.system.domain.PaypalUserPlan;
import tv.shorthub.system.service.IPaypalUserPlanService;
import tv.shorthub.common.core.service.BaseService;

/**
 * paypal用户扣费计划Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-27
 */
@Service
public class PaypalUserPlanServiceImpl extends BaseService<PaypalUserPlan> implements IPaypalUserPlanService
{
    @Autowired
    private PaypalUserPlanMapper paypalUserPlanMapper;

    @Override
    public PaypalUserPlanMapper getMapper() {
        return paypalUserPlanMapper;
    }

    /**
     * 查询paypal用户扣费计划
     *
     * @param id paypal用户扣费计划主键
     * @return paypal用户扣费计划
     */
    @Override
    public PaypalUserPlan getById(Long id)
    {
        return paypalUserPlanMapper.selectById(id);
    }

    /**
     * 查询paypal用户扣费计划列表
     *
     * @param query paypal用户扣费计划
     * @return paypal用户扣费计划
     */
    @Override
    public List<PaypalUserPlan> selectList(PaypalUserPlan query)
    {
        return paypalUserPlanMapper.selectList(new QueryWrapper<>(query));
    }


    /**
     * 查询paypal用户扣费计划数据汇总
     *
     * @param query paypal用户扣费计划
     * @return paypal用户扣费计划
     */
    @Override
    public PaypalUserPlan getSummary(PaypalUserPlan query)
    {
        return paypalUserPlanMapper.getSummary(query);
    }



    /**
     * 查询自定义分析数据
     *
     * @param query paypal用户扣费计划
     * @return paypal用户扣费计划
     */
    @Override
    public List<PaypalUserPlan> summary(SummaryRequest query)
    {
        return paypalUserPlanMapper.summary(query);
    }

    @Override
    public PaypalUserPlan allSummary(SummaryRequest query)
    {
        return paypalUserPlanMapper.allSummary(query);
    }

    /**
     * 新增paypal用户扣费计划
     *
     * @param paypalUserPlan paypal用户扣费计划
     * @return 结果
     */
    @Override
    public int insert(PaypalUserPlan paypalUserPlan)
    {
        paypalUserPlan.setCreateTime(DateUtils.getNowDate());
        return paypalUserPlanMapper.insert(paypalUserPlan);
    }

    /**
     * 修改paypal用户扣费计划
     *
     * @param paypalUserPlan paypal用户扣费计划
     * @return 结果
     */
    @Override
    public int update(PaypalUserPlan paypalUserPlan)
    {
        paypalUserPlan.setUpdateTime(DateUtils.getNowDate());
        return paypalUserPlanMapper.updateById(paypalUserPlan);
    }

    /**
     * 批量删除paypal用户扣费计划
     *
     * @param ids 需要删除的paypal用户扣费计划主键
     * @return 结果
     */
    @Override
    public int deleteByIds(List<Long> ids)
    {
        return paypalUserPlanMapper.deleteBatchIds(ids);
    }

    /**
     * 删除paypal用户扣费计划信息
     *
     * @param id paypal用户扣费计划主键
     * @return 结果
     */
    @Override
    public int deleteById(Long id)
    {
        return paypalUserPlanMapper.deleteById(id);
    }
}
