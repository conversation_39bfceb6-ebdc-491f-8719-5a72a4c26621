package tv.shorthub.system.service;

import tv.shorthub.system.mapper.AppRechargeItemMapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import tv.shorthub.system.domain.AppRechargeItem;
import tv.shorthub.common.core.service.IBaseService;

/**
 * 充值方案Service接口
 *
 * <AUTHOR>
 * @date 2025-05-10
 */
public interface IAppRechargeItemService extends IBaseService<AppRechargeItem>
{
    /**
     * 查询充值方案
     *
     * @param id 充值方案主键
     * @return 充值方案
     */
    public AppRechargeItem getById(Long id);

    /**
     * 查询充值方案数据汇总
     *
     * @param query 充值方案
     * @return 充值方案数据汇总
     */
    public AppRechargeItem getSummary(AppRechargeItem query);

    /**
     * 查询充值方案列表
     *
     * @param query 充值方案
     * @return 充值方案集合
     */
    public List<AppRechargeItem> selectList(AppRechargeItem query);

    /**
     * 新增充值方案
     *
     * @param appRechargeItem 充值方案
     * @return 结果
     */
    public int insert(AppRechargeItem appRechargeItem);

    /**
     * 修改充值方案
     *
     * @param appRechargeItem 充值方案
     * @return 结果
     */
    public int update(AppRechargeItem appRechargeItem);

    /**
     * 批量删除充值方案
     *
     * @param ids 需要删除的充值方案主键集合
     * @return 结果
     */
    public int deleteByIds(List<Long> ids);

    /**
     * 删除充值方案信息
     *
     * @param id 充值方案主键
     * @return 结果
     */
    public int deleteById(Long id);


    /**
     * 查询自定义分析数据
     *
     * @param query 充值方案
     * @return 充值方案集合
     */
    public List<AppRechargeItem> summary(SummaryRequest query);

    AppRechargeItem allSummary(SummaryRequest query);

    AppRechargeItemMapper getMapper();

    /**
     * 根据方案id查询下级优惠方案
     * @param itemId
     * @return
     */
    AppRechargeItem discountItemByItemId(String itemId);

    /**
     * 查询一级方案
     * @return
     */
    List<AppRechargeItem> queryParentItemList(String templateId);

    /**
     * 根据方案id查询启动方案
     * @param itemId
     * @return
     */
    AppRechargeItem getAppRechargeParentItemIsEnableById(String itemId);

    /**
     * 查询优惠方案
     * @param templateId
     * @return
     */
    List<AppRechargeItem> queryDiscountItemList(String templateId);
}
