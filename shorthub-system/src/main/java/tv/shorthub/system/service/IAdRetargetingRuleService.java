package tv.shorthub.system.service;

import tv.shorthub.system.mapper.AdRetargetingRuleMapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import tv.shorthub.system.domain.AdRetargetingRule;
import tv.shorthub.common.core.service.IBaseService;

/**
 * 回传规则Service接口
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
public interface IAdRetargetingRuleService extends IBaseService<AdRetargetingRule>
{
    /**
     * 查询回传规则
     *
     * @param id 回传规则主键
     * @return 回传规则
     */
    public AdRetargetingRule getById(Long id);

    /**
     * 查询回传规则数据汇总
     *
     * @param query 回传规则
     * @return 回传规则数据汇总
     */
    public AdRetargetingRule getSummary(AdRetargetingRule query);

    /**
     * 查询回传规则列表
     *
     * @param query 回传规则
     * @return 回传规则集合
     */
    public List<AdRetargetingRule> selectList(AdRetargetingRule query);

    /**
     * 新增回传规则
     *
     * @param adRetargetingRule 回传规则
     * @return 结果
     */
    public int insert(AdRetargetingRule adRetargetingRule);

    /**
     * 修改回传规则
     *
     * @param adRetargetingRule 回传规则
     * @return 结果
     */
    public int update(AdRetargetingRule adRetargetingRule);

    /**
     * 批量删除回传规则
     *
     * @param ids 需要删除的回传规则主键集合
     * @return 结果
     */
    public int deleteByIds(List<Long> ids);

    /**
     * 删除回传规则信息
     *
     * @param id 回传规则主键
     * @return 结果
     */
    public int deleteById(Long id);


    /**
     * 查询自定义分析数据
     *
     * @param query 回传规则
     * @return 回传规则集合
     */
    public List<AdRetargetingRule> summary(SummaryRequest query);

    AdRetargetingRule allSummary(SummaryRequest query);

    AdRetargetingRuleMapper getMapper();
}
