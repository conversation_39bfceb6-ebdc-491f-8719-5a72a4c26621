package tv.shorthub.system.service;

import tv.shorthub.system.mapper.PaypalPaymentSubscriptionMapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import tv.shorthub.system.domain.PaypalPaymentSubscription;
import tv.shorthub.common.core.service.IBaseService;

/**
 * paypal订阅履行Service接口
 *
 * <AUTHOR>
 * @date 2025-06-06
 */
public interface IPaypalPaymentSubscriptionService extends IBaseService<PaypalPaymentSubscription>
{
    /**
     * 查询paypal订阅履行
     *
     * @param id paypal订阅履行主键
     * @return paypal订阅履行
     */
    public PaypalPaymentSubscription getById(Long id);

    /**
     * 查询paypal订阅履行数据汇总
     *
     * @param query paypal订阅履行
     * @return paypal订阅履行数据汇总
     */
    public PaypalPaymentSubscription getSummary(PaypalPaymentSubscription query);

    /**
     * 查询paypal订阅履行列表
     *
     * @param query paypal订阅履行
     * @return paypal订阅履行集合
     */
    public List<PaypalPaymentSubscription> selectList(PaypalPaymentSubscription query);

    /**
     * 新增paypal订阅履行
     *
     * @param paypalPaymentSubscription paypal订阅履行
     * @return 结果
     */
    public int insert(PaypalPaymentSubscription paypalPaymentSubscription);

    /**
     * 修改paypal订阅履行
     *
     * @param paypalPaymentSubscription paypal订阅履行
     * @return 结果
     */
    public int update(PaypalPaymentSubscription paypalPaymentSubscription);

    /**
     * 批量删除paypal订阅履行
     *
     * @param ids 需要删除的paypal订阅履行主键集合
     * @return 结果
     */
    public int deleteByIds(List<Long> ids);

    /**
     * 删除paypal订阅履行信息
     *
     * @param id paypal订阅履行主键
     * @return 结果
     */
    public int deleteById(Long id);

    /**
     * 查询自定义分析数据
     *
     * @param query paypal订阅履行
     * @return paypal订阅履行集合
     */
    public List<PaypalPaymentSubscription> summary(SummaryRequest query);

    PaypalPaymentSubscription allSummary(SummaryRequest query);

    PaypalPaymentSubscriptionMapper getMapper();
}
