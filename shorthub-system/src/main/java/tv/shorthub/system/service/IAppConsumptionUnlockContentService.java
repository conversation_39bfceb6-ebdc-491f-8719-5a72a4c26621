package tv.shorthub.system.service;

import tv.shorthub.system.mapper.AppConsumptionUnlockContentMapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import tv.shorthub.system.domain.AppConsumptionUnlockContent;
import tv.shorthub.common.core.service.IBaseService;

/**
 * 剧目解锁Service接口
 *
 * <AUTHOR>
 * @date 2025-05-15
 */
public interface IAppConsumptionUnlockContentService extends IBaseService<AppConsumptionUnlockContent>
{
    /**
     * 查询剧目解锁
     *
     * @param id 剧目解锁主键
     * @return 剧目解锁
     */
    public AppConsumptionUnlockContent getById(Long id);

    /**
     * 查询剧目解锁数据汇总
     *
     * @param query 剧目解锁
     * @return 剧目解锁数据汇总
     */
    public AppConsumptionUnlockContent getSummary(AppConsumptionUnlockContent query);

    /**
     * 查询剧目解锁列表
     *
     * @param query 剧目解锁
     * @return 剧目解锁集合
     */
    public List<AppConsumptionUnlockContent> selectList(AppConsumptionUnlockContent query);

    /**
     * 新增剧目解锁
     *
     * @param appConsumptionUnlockContent 剧目解锁
     * @return 结果
     */
    public int insert(AppConsumptionUnlockContent appConsumptionUnlockContent);

    /**
     * 修改剧目解锁
     *
     * @param appConsumptionUnlockContent 剧目解锁
     * @return 结果
     */
    public int update(AppConsumptionUnlockContent appConsumptionUnlockContent);

    /**
     * 批量删除剧目解锁
     *
     * @param ids 需要删除的剧目解锁主键集合
     * @return 结果
     */
    public int deleteByIds(List<Long> ids);

    /**
     * 删除剧目解锁信息
     *
     * @param id 剧目解锁主键
     * @return 结果
     */
    public int deleteById(Long id);


    /**
     * 查询自定义分析数据
     *
     * @param query 剧目解锁
     * @return 剧目解锁集合
     */
    public List<AppConsumptionUnlockContent> summary(SummaryRequest query);

    AppConsumptionUnlockContent allSummary(SummaryRequest query);

    AppConsumptionUnlockContentMapper getMapper();
}
