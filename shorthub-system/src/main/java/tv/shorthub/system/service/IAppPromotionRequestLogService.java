package tv.shorthub.system.service;

import tv.shorthub.system.mapper.AppPromotionRequestLogMapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import tv.shorthub.system.domain.AppPromotionRequestLog;
import tv.shorthub.common.core.service.IBaseService;

/**
 * promotion访问日志Service接口
 *
 * <AUTHOR>
 * @date 2025-05-28
 */
public interface IAppPromotionRequestLogService extends IBaseService<AppPromotionRequestLog>
{
    /**
     * 查询promotion访问日志
     *
     * @param id promotion访问日志主键
     * @return promotion访问日志
     */
    public AppPromotionRequestLog getById(Long id);

    /**
     * 查询promotion访问日志数据汇总
     *
     * @param query promotion访问日志
     * @return promotion访问日志数据汇总
     */
    public AppPromotionRequestLog getSummary(AppPromotionRequestLog query);

    /**
     * 查询promotion访问日志列表
     *
     * @param query promotion访问日志
     * @return promotion访问日志集合
     */
    public List<AppPromotionRequestLog> selectList(AppPromotionRequestLog query);

    /**
     * 新增promotion访问日志
     *
     * @param appPromotionRequestLog promotion访问日志
     * @return 结果
     */
    public int insert(AppPromotionRequestLog appPromotionRequestLog);

    /**
     * 修改promotion访问日志
     *
     * @param appPromotionRequestLog promotion访问日志
     * @return 结果
     */
    public int update(AppPromotionRequestLog appPromotionRequestLog);

    /**
     * 批量删除promotion访问日志
     *
     * @param ids 需要删除的promotion访问日志主键集合
     * @return 结果
     */
    public int deleteByIds(List<Long> ids);

    /**
     * 删除promotion访问日志信息
     *
     * @param id promotion访问日志主键
     * @return 结果
     */
    public int deleteById(Long id);


    /**
     * 查询自定义分析数据
     *
     * @param query promotion访问日志
     * @return promotion访问日志集合
     */
    public List<AppPromotionRequestLog> summary(SummaryRequest query);

    AppPromotionRequestLog allSummary(SummaryRequest query);

    AppPromotionRequestLogMapper getMapper();

    // Facebook归因相关方法
    /**
     * 根据session ID查找最新的日志记录
     */
    AppPromotionRequestLog findLatestBySessionId(String sessionId);

    /**
     * 根据IP和时间范围查找日志记录
     */
    List<AppPromotionRequestLog> findByIpAndTimeRange(String clientIp, String tfid, long cutoffTime);

    /**
     * 根据DeviceId查找日志记录
     */
    AppPromotionRequestLog findByDeviceId(String deviceId, String tfid);
}
