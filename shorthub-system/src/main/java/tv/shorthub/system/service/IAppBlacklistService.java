package tv.shorthub.system.service;

import tv.shorthub.system.mapper.AppBlacklistMapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import java.util.Map;
import java.util.Set;

import tv.shorthub.system.domain.AppBlacklist;
import tv.shorthub.common.core.service.IBaseService;

/**
 * 黑名单Service接口
 *
 * <AUTHOR>
 * @date 2025-08-06
 */
public interface IAppBlacklistService extends IBaseService<AppBlacklist>
{
    /**
     * 查询黑名单
     *
     * @param id 黑名单主键
     * @return 黑名单
     */
    public AppBlacklist getById(Long id);

    /**
     * 查询黑名单数据汇总
     *
     * @param query 黑名单
     * @return 黑名单数据汇总
     */
    public AppBlacklist getSummary(AppBlacklist query);

    /**
     * 查询黑名单列表
     *
     * @param query 黑名单
     * @return 黑名单集合
     */
    public List<AppBlacklist> selectList(AppBlacklist query);

    /**
     * 新增黑名单
     *
     * @param appBlacklist 黑名单
     * @return 结果
     */
    public int insert(AppBlacklist appBlacklist);

    /**
     * 修改黑名单
     *
     * @param appBlacklist 黑名单
     * @return 结果
     */
    public int update(AppBlacklist appBlacklist);

    /**
     * 批量删除黑名单
     *
     * @param ids 需要删除的黑名单主键集合
     * @return 结果
     */
    public int deleteByIds(List<Long> ids);

    /**
     * 删除黑名单信息
     *
     * @param id 黑名单主键
     * @return 结果
     */
    public int deleteById(Long id);


    /**
     * 查询自定义分析数据
     *
     * @param query 黑名单
     * @return 黑名单集合
     */
    public List<AppBlacklist> summary(SummaryRequest query);

    AppBlacklist allSummary(SummaryRequest query);

    AppBlacklistMapper getMapper();

    void refresh();

    boolean isBlacklisted(String deviceId, String userId, String ip, String email);
}
