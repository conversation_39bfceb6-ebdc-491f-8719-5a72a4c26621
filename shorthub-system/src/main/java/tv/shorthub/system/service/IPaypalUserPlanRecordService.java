package tv.shorthub.system.service;

import tv.shorthub.system.mapper.PaypalUserPlanRecordMapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import tv.shorthub.system.domain.PaypalUserPlanRecord;
import tv.shorthub.common.core.service.IBaseService;

/**
 * paypal签约续订记录Service接口
 *
 * <AUTHOR>
 * @date 2025-06-27
 */
public interface IPaypalUserPlanRecordService extends IBaseService<PaypalUserPlanRecord>
{
    /**
     * 查询paypal签约续订记录
     *
     * @param id paypal签约续订记录主键
     * @return paypal签约续订记录
     */
    public PaypalUserPlanRecord getById(Long id);

    /**
     * 查询paypal签约续订记录数据汇总
     *
     * @param query paypal签约续订记录
     * @return paypal签约续订记录数据汇总
     */
    public PaypalUserPlanRecord getSummary(PaypalUserPlanRecord query);

    /**
     * 查询paypal签约续订记录列表
     *
     * @param query paypal签约续订记录
     * @return paypal签约续订记录集合
     */
    public List<PaypalUserPlanRecord> selectList(PaypalUserPlanRecord query);

    /**
     * 新增paypal签约续订记录
     *
     * @param paypalUserPlanRecord paypal签约续订记录
     * @return 结果
     */
    public int insert(PaypalUserPlanRecord paypalUserPlanRecord);

    /**
     * 修改paypal签约续订记录
     *
     * @param paypalUserPlanRecord paypal签约续订记录
     * @return 结果
     */
    public int update(PaypalUserPlanRecord paypalUserPlanRecord);

    /**
     * 批量删除paypal签约续订记录
     *
     * @param ids 需要删除的paypal签约续订记录主键集合
     * @return 结果
     */
    public int deleteByIds(List<Long> ids);

    /**
     * 删除paypal签约续订记录信息
     *
     * @param id paypal签约续订记录主键
     * @return 结果
     */
    public int deleteById(Long id);


    /**
     * 查询自定义分析数据
     *
     * @param query paypal签约续订记录
     * @return paypal签约续订记录集合
     */
    public List<PaypalUserPlanRecord> summary(SummaryRequest query);

    PaypalUserPlanRecord allSummary(SummaryRequest query);

    PaypalUserPlanRecordMapper getMapper();
}
