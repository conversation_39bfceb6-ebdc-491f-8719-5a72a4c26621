package tv.shorthub.system.service;

import tv.shorthub.system.mapper.AppConfigMapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import tv.shorthub.system.domain.AppConfig;
import tv.shorthub.common.core.service.IBaseService;

/**
 * 客户端配置Service接口
 *
 * <AUTHOR>
 * @date 2025-07-21
 */
public interface IAppConfigService extends IBaseService<AppConfig>
{
    /**
     * 查询客户端配置
     *
     * @param id 客户端配置主键
     * @return 客户端配置
     */
    public AppConfig getById(Long id);

    /**
     * 查询客户端配置数据汇总
     *
     * @param query 客户端配置
     * @return 客户端配置数据汇总
     */
    public AppConfig getSummary(AppConfig query);

    /**
     * 查询客户端配置列表
     *
     * @param query 客户端配置
     * @return 客户端配置集合
     */
    public List<AppConfig> selectList(AppConfig query);

    /**
     * 新增客户端配置
     *
     * @param appConfig 客户端配置
     * @return 结果
     */
    public int insert(AppConfig appConfig);

    /**
     * 修改客户端配置
     *
     * @param appConfig 客户端配置
     * @return 结果
     */
    public int update(AppConfig appConfig);

    /**
     * 批量删除客户端配置
     *
     * @param ids 需要删除的客户端配置主键集合
     * @return 结果
     */
    public int deleteByIds(List<Long> ids);

    /**
     * 删除客户端配置信息
     *
     * @param id 客户端配置主键
     * @return 结果
     */
    public int deleteById(Long id);


    /**
     * 查询自定义分析数据
     *
     * @param query 客户端配置
     * @return 客户端配置集合
     */
    public List<AppConfig> summary(SummaryRequest query);

    AppConfig allSummary(SummaryRequest query);

    AppConfigMapper getMapper();
}
