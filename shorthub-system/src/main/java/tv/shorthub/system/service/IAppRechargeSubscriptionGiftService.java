package tv.shorthub.system.service;

import tv.shorthub.system.mapper.AppRechargeSubscriptionGiftMapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import tv.shorthub.system.domain.AppRechargeSubscriptionGift;
import tv.shorthub.common.core.service.IBaseService;

/**
 * 订阅优惠规则Service接口
 *
 * <AUTHOR>
 * @date 2025-05-14
 */
public interface IAppRechargeSubscriptionGiftService extends IBaseService<AppRechargeSubscriptionGift>
{
    /**
     * 查询订阅优惠规则
     *
     * @param id 订阅优惠规则主键
     * @return 订阅优惠规则
     */
    public AppRechargeSubscriptionGift getById(Long id);

    /**
     * 查询订阅优惠规则数据汇总
     *
     * @param query 订阅优惠规则
     * @return 订阅优惠规则数据汇总
     */
    public AppRechargeSubscriptionGift getSummary(AppRechargeSubscriptionGift query);

    /**
     * 查询订阅优惠规则列表
     *
     * @param query 订阅优惠规则
     * @return 订阅优惠规则集合
     */
    public List<AppRechargeSubscriptionGift> selectList(AppRechargeSubscriptionGift query);

    /**
     * 新增订阅优惠规则
     *
     * @param appRechargeSubscriptionGift 订阅优惠规则
     * @return 结果
     */
    public int insert(AppRechargeSubscriptionGift appRechargeSubscriptionGift);

    /**
     * 修改订阅优惠规则
     *
     * @param appRechargeSubscriptionGift 订阅优惠规则
     * @return 结果
     */
    public int update(AppRechargeSubscriptionGift appRechargeSubscriptionGift);

    /**
     * 批量删除订阅优惠规则
     *
     * @param ids 需要删除的订阅优惠规则主键集合
     * @return 结果
     */
    public int deleteByIds(List<Long> ids);

    /**
     * 删除订阅优惠规则信息
     *
     * @param id 订阅优惠规则主键
     * @return 结果
     */
    public int deleteById(Long id);


    /**
     * 查询自定义分析数据
     *
     * @param query 订阅优惠规则
     * @return 订阅优惠规则集合
     */
    public List<AppRechargeSubscriptionGift> summary(SummaryRequest query);

    AppRechargeSubscriptionGift allSummary(SummaryRequest query);

    AppRechargeSubscriptionGiftMapper getMapper();
}
