package tv.shorthub.system.service;

import tv.shorthub.system.mapper.CryptoDepositRecordMapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import tv.shorthub.system.domain.CryptoDepositRecord;
import tv.shorthub.common.core.service.IBaseService;

/**
 * 虚拟货币充值记录Service接口
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
public interface ICryptoDepositRecordService extends IBaseService<CryptoDepositRecord>
{
    /**
     * 查询虚拟货币充值记录
     *
     * @param id 虚拟货币充值记录主键
     * @return 虚拟货币充值记录
     */
    public CryptoDepositRecord getById(Long id);

    /**
     * 查询虚拟货币充值记录数据汇总
     *
     * @param query 虚拟货币充值记录
     * @return 虚拟货币充值记录数据汇总
     */
    public CryptoDepositRecord getSummary(CryptoDepositRecord query);

    /**
     * 查询虚拟货币充值记录列表
     *
     * @param query 虚拟货币充值记录
     * @return 虚拟货币充值记录集合
     */
    public List<CryptoDepositRecord> selectList(CryptoDepositRecord query);

    /**
     * 新增虚拟货币充值记录
     *
     * @param cryptoDepositRecord 虚拟货币充值记录
     * @return 结果
     */
    public int insert(CryptoDepositRecord cryptoDepositRecord);

    /**
     * 修改虚拟货币充值记录
     *
     * @param cryptoDepositRecord 虚拟货币充值记录
     * @return 结果
     */
    public int update(CryptoDepositRecord cryptoDepositRecord);

    /**
     * 批量删除虚拟货币充值记录
     *
     * @param ids 需要删除的虚拟货币充值记录主键集合
     * @return 结果
     */
    public int deleteByIds(List<Long> ids);

    /**
     * 删除虚拟货币充值记录信息
     *
     * @param id 虚拟货币充值记录主键
     * @return 结果
     */
    public int deleteById(Long id);


    /**
     * 查询自定义分析数据
     *
     * @param query 虚拟货币充值记录
     * @return 虚拟货币充值记录集合
     */
    public List<CryptoDepositRecord> summary(SummaryRequest query);

    CryptoDepositRecord allSummary(SummaryRequest query);

    CryptoDepositRecordMapper getMapper();
}
