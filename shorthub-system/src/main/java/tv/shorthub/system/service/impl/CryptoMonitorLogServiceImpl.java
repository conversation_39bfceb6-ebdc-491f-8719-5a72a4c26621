package tv.shorthub.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import tv.shorthub.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tv.shorthub.system.mapper.CryptoMonitorLogMapper;
import tv.shorthub.system.domain.CryptoMonitorLog;
import tv.shorthub.system.service.ICryptoMonitorLogService;
import tv.shorthub.common.core.service.BaseService;

/**
 * 虚拟货币交易监听记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
@Service
public class CryptoMonitorLogServiceImpl extends BaseService<CryptoMonitorLog> implements ICryptoMonitorLogService
{
    @Autowired
    private CryptoMonitorLogMapper cryptoMonitorLogMapper;

    @Override
    public CryptoMonitorLogMapper getMapper() {
        return cryptoMonitorLogMapper;
    }

    /**
     * 查询虚拟货币交易监听记录
     *
     * @param id 虚拟货币交易监听记录主键
     * @return 虚拟货币交易监听记录
     */
    @Override
    public CryptoMonitorLog getById(Long id)
    {
        return cryptoMonitorLogMapper.selectById(id);
    }

    /**
     * 查询虚拟货币交易监听记录列表
     *
     * @param query 虚拟货币交易监听记录
     * @return 虚拟货币交易监听记录
     */
    @Override
    public List<CryptoMonitorLog> selectList(CryptoMonitorLog query)
    {
        return cryptoMonitorLogMapper.selectList(new QueryWrapper<>(query));
    }


    /**
     * 查询虚拟货币交易监听记录数据汇总
     *
     * @param query 虚拟货币交易监听记录
     * @return 虚拟货币交易监听记录
     */
    @Override
    public CryptoMonitorLog getSummary(CryptoMonitorLog query)
    {
        return cryptoMonitorLogMapper.getSummary(query);
    }



    /**
     * 查询自定义分析数据
     *
     * @param query 虚拟货币交易监听记录
     * @return 虚拟货币交易监听记录
     */
    @Override
    public List<CryptoMonitorLog> summary(SummaryRequest query)
    {
        return cryptoMonitorLogMapper.summary(query);
    }

    @Override
    public CryptoMonitorLog allSummary(SummaryRequest query)
    {
        return cryptoMonitorLogMapper.allSummary(query);
    }

    /**
     * 新增虚拟货币交易监听记录
     *
     * @param cryptoMonitorLog 虚拟货币交易监听记录
     * @return 结果
     */
    @Override
    public int insert(CryptoMonitorLog cryptoMonitorLog)
    {
        cryptoMonitorLog.setCreateTime(DateUtils.getNowDate());
        return cryptoMonitorLogMapper.insert(cryptoMonitorLog);
    }

    /**
     * 修改虚拟货币交易监听记录
     *
     * @param cryptoMonitorLog 虚拟货币交易监听记录
     * @return 结果
     */
    @Override
    public int update(CryptoMonitorLog cryptoMonitorLog)
    {
        cryptoMonitorLog.setUpdateTime(DateUtils.getNowDate());
        return cryptoMonitorLogMapper.updateById(cryptoMonitorLog);
    }

    /**
     * 批量删除虚拟货币交易监听记录
     *
     * @param ids 需要删除的虚拟货币交易监听记录主键
     * @return 结果
     */
    @Override
    public int deleteByIds(List<Long> ids)
    {
        return cryptoMonitorLogMapper.deleteBatchIds(ids);
    }

    /**
     * 删除虚拟货币交易监听记录信息
     *
     * @param id 虚拟货币交易监听记录主键
     * @return 结果
     */
    @Override
    public int deleteById(Long id)
    {
        return cryptoMonitorLogMapper.deleteById(id);
    }
}
