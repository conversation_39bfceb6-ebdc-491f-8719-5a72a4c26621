package tv.shorthub.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import tv.shorthub.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tv.shorthub.system.mapper.PaypalPaymentConfigMapper;
import tv.shorthub.system.domain.PaypalPaymentConfig;
import tv.shorthub.system.service.IPaypalPaymentConfigService;
import tv.shorthub.common.core.service.BaseService;

/**
 * paypal配置Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-22
 */
@Service
public class PaypalPaymentConfigServiceImpl extends BaseService<PaypalPaymentConfig> implements IPaypalPaymentConfigService
{
    @Autowired
    private PaypalPaymentConfigMapper paypalPaymentConfigMapper;

    @Override
    public PaypalPaymentConfigMapper getMapper() {
        return paypalPaymentConfigMapper;
    }

    /**
     * 查询paypal配置
     *
     * @param id paypal配置主键
     * @return paypal配置
     */
    @Override
    public PaypalPaymentConfig getById(Long id)
    {
        return paypalPaymentConfigMapper.selectById(id);
    }

    /**
     * 查询paypal配置列表
     *
     * @param query paypal配置
     * @return paypal配置
     */
    @Override
    public List<PaypalPaymentConfig> selectList(PaypalPaymentConfig query)
    {
        return paypalPaymentConfigMapper.selectList(new QueryWrapper<>(query));
    }


    /**
     * 查询paypal配置数据汇总
     *
     * @param query paypal配置
     * @return paypal配置
     */
    @Override
    public PaypalPaymentConfig getSummary(PaypalPaymentConfig query)
    {
        return paypalPaymentConfigMapper.getSummary(query);
    }



    /**
     * 查询自定义分析数据
     *
     * @param query paypal配置
     * @return paypal配置
     */
    @Override
    public List<PaypalPaymentConfig> summary(SummaryRequest query)
    {
        return paypalPaymentConfigMapper.summary(query);
    }

    @Override
    public PaypalPaymentConfig allSummary(SummaryRequest query)
    {
        return paypalPaymentConfigMapper.allSummary(query);
    }

    /**
     * 新增paypal配置
     *
     * @param paypalPaymentConfig paypal配置
     * @return 结果
     */
    @Override
    public int insert(PaypalPaymentConfig paypalPaymentConfig)
    {
        paypalPaymentConfig.setCreateTime(DateUtils.getNowDate());
        return paypalPaymentConfigMapper.insert(paypalPaymentConfig);
    }

    /**
     * 修改paypal配置
     *
     * @param paypalPaymentConfig paypal配置
     * @return 结果
     */
    @Override
    public int update(PaypalPaymentConfig paypalPaymentConfig)
    {
        paypalPaymentConfig.setUpdateTime(DateUtils.getNowDate());
        return paypalPaymentConfigMapper.updateById(paypalPaymentConfig);
    }

    /**
     * 批量删除paypal配置
     *
     * @param ids 需要删除的paypal配置主键
     * @return 结果
     */
    @Override
    public int deleteByIds(List<Long> ids)
    {
        return paypalPaymentConfigMapper.deleteBatchIds(ids);
    }

    /**
     * 删除paypal配置信息
     *
     * @param id paypal配置主键
     * @return 结果
     */
    @Override
    public int deleteById(Long id)
    {
        return paypalPaymentConfigMapper.deleteById(id);
    }
}
