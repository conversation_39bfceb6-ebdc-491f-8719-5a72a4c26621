package tv.shorthub.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import tv.shorthub.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tv.shorthub.system.mapper.CryptoBaseBlockMapper;
import tv.shorthub.system.domain.CryptoBaseBlock;
import tv.shorthub.system.service.ICryptoBaseBlockService;
import tv.shorthub.common.core.service.BaseService;

/**
 * BASE区块链区块Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-05
 */
@Service
public class CryptoBaseBlockServiceImpl extends BaseService<CryptoBaseBlock> implements ICryptoBaseBlockService
{
    @Autowired
    private CryptoBaseBlockMapper cryptoBaseBlockMapper;

    @Override
    public CryptoBaseBlockMapper getMapper() {
        return cryptoBaseBlockMapper;
    }

    /**
     * 查询BASE区块链区块
     *
     * @param id BASE区块链区块主键
     * @return BASE区块链区块
     */
    @Override
    public CryptoBaseBlock getById(Long id)
    {
        return cryptoBaseBlockMapper.selectById(id);
    }

    /**
     * 查询BASE区块链区块列表
     *
     * @param query BASE区块链区块
     * @return BASE区块链区块
     */
    @Override
    public List<CryptoBaseBlock> selectList(CryptoBaseBlock query)
    {
        return cryptoBaseBlockMapper.selectList(new QueryWrapper<>(query));
    }


    /**
     * 查询BASE区块链区块数据汇总
     *
     * @param query BASE区块链区块
     * @return BASE区块链区块
     */
    @Override
    public CryptoBaseBlock getSummary(CryptoBaseBlock query)
    {
        return cryptoBaseBlockMapper.getSummary(query);
    }



    /**
     * 查询自定义分析数据
     *
     * @param query BASE区块链区块
     * @return BASE区块链区块
     */
    @Override
    public List<CryptoBaseBlock> summary(SummaryRequest query)
    {
        return cryptoBaseBlockMapper.summary(query);
    }

    @Override
    public CryptoBaseBlock allSummary(SummaryRequest query)
    {
        return cryptoBaseBlockMapper.allSummary(query);
    }

    /**
     * 新增BASE区块链区块
     *
     * @param cryptoBaseBlock BASE区块链区块
     * @return 结果
     */
    @Override
    public int insert(CryptoBaseBlock cryptoBaseBlock)
    {
        cryptoBaseBlock.setCreateTime(DateUtils.getNowDate());
        return cryptoBaseBlockMapper.insert(cryptoBaseBlock);
    }

    /**
     * 修改BASE区块链区块
     *
     * @param cryptoBaseBlock BASE区块链区块
     * @return 结果
     */
    @Override
    public int update(CryptoBaseBlock cryptoBaseBlock)
    {
        cryptoBaseBlock.setUpdateTime(DateUtils.getNowDate());
        return cryptoBaseBlockMapper.updateById(cryptoBaseBlock);
    }

    /**
     * 批量删除BASE区块链区块
     *
     * @param ids 需要删除的BASE区块链区块主键
     * @return 结果
     */
    @Override
    public int deleteByIds(List<Long> ids)
    {
        return cryptoBaseBlockMapper.deleteBatchIds(ids);
    }

    /**
     * 删除BASE区块链区块信息
     *
     * @param id BASE区块链区块主键
     * @return 结果
     */
    @Override
    public int deleteById(Long id)
    {
        return cryptoBaseBlockMapper.deleteById(id);
    }
}
