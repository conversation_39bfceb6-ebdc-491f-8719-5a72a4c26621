package tv.shorthub.system.service;

import tv.shorthub.system.mapper.PaypalDisputeMapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import tv.shorthub.system.domain.PaypalDispute;
import tv.shorthub.common.core.service.IBaseService;

/**
 * PayPal争议Service接口
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
public interface IPaypalDisputeService extends IBaseService<PaypalDispute>
{
    /**
     * 查询PayPal争议
     *
     * @param id PayPal争议主键
     * @return PayPal争议
     */
    public PaypalDispute getById(Long id);

    /**
     * 查询PayPal争议数据汇总
     *
     * @param query PayPal争议
     * @return PayPal争议数据汇总
     */
    public PaypalDispute getSummary(PaypalDispute query);

    /**
     * 查询PayPal争议列表
     *
     * @param query PayPal争议
     * @return PayPal争议集合
     */
    public List<PaypalDispute> selectList(PaypalDispute query);

    /**
     * 新增PayPal争议
     *
     * @param paypalDispute PayPal争议
     * @return 结果
     */
    public int insert(PaypalDispute paypalDispute);

    /**
     * 修改PayPal争议
     *
     * @param paypalDispute PayPal争议
     * @return 结果
     */
    public int update(PaypalDispute paypalDispute);

    /**
     * 批量删除PayPal争议
     *
     * @param ids 需要删除的PayPal争议主键集合
     * @return 结果
     */
    public int deleteByIds(List<Long> ids);

    /**
     * 删除PayPal争议信息
     *
     * @param id PayPal争议主键
     * @return 结果
     */
    public int deleteById(Long id);

    String findOrderNoByCaptureId(String captureId);

    /**
     * 查询自定义分析数据
     *
     * @param query PayPal争议
     * @return PayPal争议集合
     */
    public List<PaypalDispute> summary(SummaryRequest query);

    PaypalDispute allSummary(SummaryRequest query);

    PaypalDisputeMapper getMapper();
}
