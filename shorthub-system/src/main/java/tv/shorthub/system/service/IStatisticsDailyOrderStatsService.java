package tv.shorthub.system.service;

import tv.shorthub.system.mapper.StatisticsDailyOrderStatsMapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import tv.shorthub.system.domain.StatisticsDailyOrderStats;
import tv.shorthub.common.core.service.IBaseService;
import java.util.Date;

/**
 * 每日订单统计Service接口
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
public interface IStatisticsDailyOrderStatsService extends IBaseService<StatisticsDailyOrderStats>
{
    /**
     * 查询每日订单统计
     *
     * @param id 每日订单统计主键
     * @return 每日订单统计
     */
    public StatisticsDailyOrderStats getById(Long id);


    /**
     * 查询每日订单统计列表
     *
     * @param query 每日订单统计
     * @return 每日订单统计集合
     */
    public List<StatisticsDailyOrderStats> selectList(StatisticsDailyOrderStats query);

    /**
     * 新增每日订单统计
     *
     * @param statisticsDailyOrderStats 每日订单统计
     * @return 结果
     */
    public int insert(StatisticsDailyOrderStats statisticsDailyOrderStats);

    /**
     * 修改每日订单统计
     *
     * @param statisticsDailyOrderStats 每日订单统计
     * @return 结果
     */
    public int update(StatisticsDailyOrderStats statisticsDailyOrderStats);

    /**
     * 批量删除每日订单统计
     *
     * @param ids 需要删除的每日订单统计主键集合
     * @return 结果
     */
    public int deleteByIds(List<Long> ids);

    /**
     * 删除每日订单统计信息
     *
     * @param id 每日订单统计主键
     * @return 结果
     */
    public int deleteById(Long id);


    /**
     * 查询自定义分析数据
     *
     * @param query 每日订单统计
     * @return 每日订单统计集合
     */
    public List<StatisticsDailyOrderStats> summary(SummaryRequest query);

    StatisticsDailyOrderStats allSummary(SummaryRequest query);

    StatisticsDailyOrderStatsMapper getMapper();

    /**
     * 按创建者统计区间汇总
     */
    StatisticsDailyOrderStats getSummaryByCreator(String creator, Date startTime, Date endTime, String orderChannel, String timezone);

    /**
     * 按多个tfid统计区间汇总（支持时区和orderChannel）
     */
    StatisticsDailyOrderStats getSummaryByTfids(List<String> tfids, Date startTime, Date endTime, String appid, String orderChannel, String timezone);

    StatisticsDailyOrderStats getRangeSummary(Date startTime, Date endTime, String appid, String tfid, String orderChannel, String timezone);
}
