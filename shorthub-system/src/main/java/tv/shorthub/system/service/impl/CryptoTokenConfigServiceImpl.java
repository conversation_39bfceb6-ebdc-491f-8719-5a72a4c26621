package tv.shorthub.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import tv.shorthub.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tv.shorthub.system.mapper.CryptoTokenConfigMapper;
import tv.shorthub.system.domain.CryptoTokenConfig;
import tv.shorthub.system.service.ICryptoTokenConfigService;
import tv.shorthub.common.core.service.BaseService;

/**
 * 虚拟货币代币配置Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-05
 */
@Service
public class CryptoTokenConfigServiceImpl extends BaseService<CryptoTokenConfig> implements ICryptoTokenConfigService
{
    @Autowired
    private CryptoTokenConfigMapper cryptoTokenConfigMapper;

    @Override
    public CryptoTokenConfigMapper getMapper() {
        return cryptoTokenConfigMapper;
    }

    /**
     * 查询虚拟货币代币配置
     *
     * @param id 虚拟货币代币配置主键
     * @return 虚拟货币代币配置
     */
    @Override
    public CryptoTokenConfig getById(Long id)
    {
        return cryptoTokenConfigMapper.selectById(id);
    }

    /**
     * 查询虚拟货币代币配置列表
     *
     * @param query 虚拟货币代币配置
     * @return 虚拟货币代币配置
     */
    @Override
    public List<CryptoTokenConfig> selectList(CryptoTokenConfig query)
    {
        return cryptoTokenConfigMapper.selectList(new QueryWrapper<>(query));
    }


    /**
     * 查询虚拟货币代币配置数据汇总
     *
     * @param query 虚拟货币代币配置
     * @return 虚拟货币代币配置
     */
    @Override
    public CryptoTokenConfig getSummary(CryptoTokenConfig query)
    {
        return cryptoTokenConfigMapper.getSummary(query);
    }



    /**
     * 查询自定义分析数据
     *
     * @param query 虚拟货币代币配置
     * @return 虚拟货币代币配置
     */
    @Override
    public List<CryptoTokenConfig> summary(SummaryRequest query)
    {
        return cryptoTokenConfigMapper.summary(query);
    }

    @Override
    public CryptoTokenConfig allSummary(SummaryRequest query)
    {
        return cryptoTokenConfigMapper.allSummary(query);
    }

    /**
     * 新增虚拟货币代币配置
     *
     * @param cryptoTokenConfig 虚拟货币代币配置
     * @return 结果
     */
    @Override
    public int insert(CryptoTokenConfig cryptoTokenConfig)
    {
        cryptoTokenConfig.setCreateTime(DateUtils.getNowDate());
        return cryptoTokenConfigMapper.insert(cryptoTokenConfig);
    }

    /**
     * 修改虚拟货币代币配置
     *
     * @param cryptoTokenConfig 虚拟货币代币配置
     * @return 结果
     */
    @Override
    public int update(CryptoTokenConfig cryptoTokenConfig)
    {
        cryptoTokenConfig.setUpdateTime(DateUtils.getNowDate());
        return cryptoTokenConfigMapper.updateById(cryptoTokenConfig);
    }

    /**
     * 批量删除虚拟货币代币配置
     *
     * @param ids 需要删除的虚拟货币代币配置主键
     * @return 结果
     */
    @Override
    public int deleteByIds(List<Long> ids)
    {
        return cryptoTokenConfigMapper.deleteBatchIds(ids);
    }

    /**
     * 删除虚拟货币代币配置信息
     *
     * @param id 虚拟货币代币配置主键
     * @return 结果
     */
    @Override
    public int deleteById(Long id)
    {
        return cryptoTokenConfigMapper.deleteById(id);
    }
}
