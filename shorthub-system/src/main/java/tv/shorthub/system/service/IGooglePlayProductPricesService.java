package tv.shorthub.system.service;

import tv.shorthub.system.mapper.GooglePlayProductPricesMapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import tv.shorthub.system.domain.GooglePlayProductPrices;
import tv.shorthub.common.core.service.IBaseService;

/**
 * Google Play产品价格配置Service接口
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
public interface IGooglePlayProductPricesService extends IBaseService<GooglePlayProductPrices>
{
    /**
     * 查询Google Play产品价格配置
     *
     * @param id Google Play产品价格配置主键
     * @return Google Play产品价格配置
     */
    public GooglePlayProductPrices getById(Long id);

    /**
     * 查询Google Play产品价格配置数据汇总
     *
     * @param query Google Play产品价格配置
     * @return Google Play产品价格配置数据汇总
     */
    public GooglePlayProductPrices getSummary(GooglePlayProductPrices query);

    /**
     * 查询Google Play产品价格配置列表
     *
     * @param query Google Play产品价格配置
     * @return Google Play产品价格配置集合
     */
    public List<GooglePlayProductPrices> selectList(GooglePlayProductPrices query);

    /**
     * 新增Google Play产品价格配置
     *
     * @param googlePlayProductPrices Google Play产品价格配置
     * @return 结果
     */
    public int insert(GooglePlayProductPrices googlePlayProductPrices);

    /**
     * 修改Google Play产品价格配置
     *
     * @param googlePlayProductPrices Google Play产品价格配置
     * @return 结果
     */
    public int update(GooglePlayProductPrices googlePlayProductPrices);

    /**
     * 批量删除Google Play产品价格配置
     *
     * @param ids 需要删除的Google Play产品价格配置主键集合
     * @return 结果
     */
    public int deleteByIds(List<Long> ids);

    /**
     * 删除Google Play产品价格配置信息
     *
     * @param id Google Play产品价格配置主键
     * @return 结果
     */
    public int deleteById(Long id);


    /**
     * 查询自定义分析数据
     *
     * @param query Google Play产品价格配置
     * @return Google Play产品价格配置集合
     */
    public List<GooglePlayProductPrices> summary(SummaryRequest query);

    GooglePlayProductPrices allSummary(SummaryRequest query);

    GooglePlayProductPricesMapper getMapper();
}
