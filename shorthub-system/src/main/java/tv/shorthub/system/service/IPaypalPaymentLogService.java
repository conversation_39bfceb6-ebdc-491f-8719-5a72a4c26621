package tv.shorthub.system.service;

import com.alibaba.fastjson2.JSONObject;
import tv.shorthub.system.mapper.PaypalPaymentLogMapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import tv.shorthub.system.domain.PaypalPaymentLog;
import tv.shorthub.common.core.service.IBaseService;

/**
 * paypal原始订单Service接口
 *
 * <AUTHOR>
 * @date 2025-05-15
 */
public interface IPaypalPaymentLogService extends IBaseService<PaypalPaymentLog>
{
    /**
     * 查询paypal原始订单
     *
     * @param id paypal原始订单主键
     * @return paypal原始订单
     */
    public PaypalPaymentLog getById(Long id);

    /**
     * 查询paypal原始订单数据汇总
     *
     * @param query paypal原始订单
     * @return paypal原始订单数据汇总
     */
    public PaypalPaymentLog getSummary(PaypalPaymentLog query);

    /**
     * 查询paypal原始订单列表
     *
     * @param query paypal原始订单
     * @return paypal原始订单集合
     */
    public List<PaypalPaymentLog> selectList(PaypalPaymentLog query);

    /**
     * 新增paypal原始订单
     *
     * @param paypalPaymentLog paypal原始订单
     * @return 结果
     */
    public int insert(PaypalPaymentLog paypalPaymentLog);

    /**
     * 修改paypal原始订单
     *
     * @param paypalPaymentLog paypal原始订单
     * @return 结果
     */
    public int update(PaypalPaymentLog paypalPaymentLog);

    /**
     * 批量删除paypal原始订单
     *
     * @param ids 需要删除的paypal原始订单主键集合
     * @return 结果
     */
    public int deleteByIds(List<Long> ids);

    /**
     * 删除paypal原始订单信息
     *
     * @param id paypal原始订单主键
     * @return 结果
     */
    public int deleteById(Long id);


    /**
     * 查询自定义分析数据
     *
     * @param query paypal原始订单
     * @return paypal原始订单集合
     */
    public List<PaypalPaymentLog> summary(SummaryRequest query);

    PaypalPaymentLog allSummary(SummaryRequest query);

    PaypalPaymentLogMapper getMapper();

    /**
     * 更新PayPal订单状态并同步到本地订单
     *
     * @param orderNo 订单号
     * @return 更新结果
     */
    JSONObject updateOrderStatus(String orderNo);

    /**
     * 处理PayPal退款
     *
     * @param orderNo 订单号
     * @param reason 退款原因
     * @return 退款结果
     */
    JSONObject processRefund(String orderNo, String reason);

    /**
     * 处理PayPal退订
     *
     * @param orderNo 订单号
     * @param reason 退订原因
     * @return 退订结果
     */
    JSONObject processUnsubscribe(String orderNo, String reason);
}
