package tv.shorthub.system.service;

import tv.shorthub.system.mapper.AppUserMemberMapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import tv.shorthub.system.domain.AppUserMember;
import tv.shorthub.common.core.service.IBaseService;

/**
 * 用户会员开通记录Service接口
 *
 * <AUTHOR>
 * @date 2025-05-15
 */
public interface IAppUserMemberService extends IBaseService<AppUserMember>
{
    /**
     * 查询用户会员开通记录
     *
     * @param id 用户会员开通记录主键
     * @return 用户会员开通记录
     */
    public AppUserMember getById(Long id);

    /**
     * 查询用户会员开通记录数据汇总
     *
     * @param query 用户会员开通记录
     * @return 用户会员开通记录数据汇总
     */
    public AppUserMember getSummary(AppUserMember query);

    /**
     * 查询用户会员开通记录列表
     *
     * @param query 用户会员开通记录
     * @return 用户会员开通记录集合
     */
    public List<AppUserMember> selectList(AppUserMember query);

    /**
     * 新增用户会员开通记录
     *
     * @param appUserMember 用户会员开通记录
     * @return 结果
     */
    public int insert(AppUserMember appUserMember);

    /**
     * 修改用户会员开通记录
     *
     * @param appUserMember 用户会员开通记录
     * @return 结果
     */
    public int update(AppUserMember appUserMember);

    /**
     * 批量删除用户会员开通记录
     *
     * @param ids 需要删除的用户会员开通记录主键集合
     * @return 结果
     */
    public int deleteByIds(List<Long> ids);

    /**
     * 删除用户会员开通记录信息
     *
     * @param id 用户会员开通记录主键
     * @return 结果
     */
    public int deleteById(Long id);


    /**
     * 查询自定义分析数据
     *
     * @param query 用户会员开通记录
     * @return 用户会员开通记录集合
     */
    public List<AppUserMember> summary(SummaryRequest query);

    AppUserMember allSummary(SummaryRequest query);

    AppUserMemberMapper getMapper();
}
