package tv.shorthub.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import tv.shorthub.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tv.shorthub.system.mapper.AirwallexUserPlanRecordMapper;
import tv.shorthub.system.domain.AirwallexUserPlanRecord;
import tv.shorthub.system.service.IAirwallexUserPlanRecordService;
import tv.shorthub.common.core.service.BaseService;

/**
 * 签约续订记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
@Service
public class AirwallexUserPlanRecordServiceImpl extends BaseService<AirwallexUserPlanRecord> implements IAirwallexUserPlanRecordService
{
    @Autowired
    private AirwallexUserPlanRecordMapper airwallexUserPlanRecordMapper;

    @Override
    public AirwallexUserPlanRecordMapper getMapper() {
        return airwallexUserPlanRecordMapper;
    }

    /**
     * 查询签约续订记录
     *
     * @param id 签约续订记录主键
     * @return 签约续订记录
     */
    @Override
    public AirwallexUserPlanRecord getById(Long id)
    {
        return airwallexUserPlanRecordMapper.selectById(id);
    }

    /**
     * 查询签约续订记录列表
     *
     * @param query 签约续订记录
     * @return 签约续订记录
     */
    @Override
    public List<AirwallexUserPlanRecord> selectList(AirwallexUserPlanRecord query)
    {
        return airwallexUserPlanRecordMapper.selectList(new QueryWrapper<>(query));
    }


    /**
     * 查询签约续订记录数据汇总
     *
     * @param query 签约续订记录
     * @return 签约续订记录
     */
    @Override
    public AirwallexUserPlanRecord getSummary(AirwallexUserPlanRecord query)
    {
        return airwallexUserPlanRecordMapper.getSummary(query);
    }



    /**
     * 查询自定义分析数据
     *
     * @param query 签约续订记录
     * @return 签约续订记录
     */
    @Override
    public List<AirwallexUserPlanRecord> summary(SummaryRequest query)
    {
        return airwallexUserPlanRecordMapper.summary(query);
    }

    @Override
    public AirwallexUserPlanRecord allSummary(SummaryRequest query)
    {
        return airwallexUserPlanRecordMapper.allSummary(query);
    }

    /**
     * 新增签约续订记录
     *
     * @param airwallexUserPlanRecord 签约续订记录
     * @return 结果
     */
    @Override
    public int insert(AirwallexUserPlanRecord airwallexUserPlanRecord)
    {
        airwallexUserPlanRecord.setCreateTime(DateUtils.getNowDate());
        return airwallexUserPlanRecordMapper.insert(airwallexUserPlanRecord);
    }

    /**
     * 修改签约续订记录
     *
     * @param airwallexUserPlanRecord 签约续订记录
     * @return 结果
     */
    @Override
    public int update(AirwallexUserPlanRecord airwallexUserPlanRecord)
    {
        airwallexUserPlanRecord.setUpdateTime(DateUtils.getNowDate());
        return airwallexUserPlanRecordMapper.updateById(airwallexUserPlanRecord);
    }

    /**
     * 批量删除签约续订记录
     *
     * @param ids 需要删除的签约续订记录主键
     * @return 结果
     */
    @Override
    public int deleteByIds(List<Long> ids)
    {
        return airwallexUserPlanRecordMapper.deleteBatchIds(ids);
    }

    /**
     * 删除签约续订记录信息
     *
     * @param id 签约续订记录主键
     * @return 结果
     */
    @Override
    public int deleteById(Long id)
    {
        return airwallexUserPlanRecordMapper.deleteById(id);
    }
}
