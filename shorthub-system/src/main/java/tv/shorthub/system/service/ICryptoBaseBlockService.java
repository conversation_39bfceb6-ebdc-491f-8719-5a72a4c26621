package tv.shorthub.system.service;

import tv.shorthub.system.mapper.CryptoBaseBlockMapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import tv.shorthub.system.domain.CryptoBaseBlock;
import tv.shorthub.common.core.service.IBaseService;

/**
 * BASE区块链区块Service接口
 *
 * <AUTHOR>
 * @date 2025-08-05
 */
public interface ICryptoBaseBlockService extends IBaseService<CryptoBaseBlock>
{
    /**
     * 查询BASE区块链区块
     *
     * @param id BASE区块链区块主键
     * @return BASE区块链区块
     */
    public CryptoBaseBlock getById(Long id);

    /**
     * 查询BASE区块链区块数据汇总
     *
     * @param query BASE区块链区块
     * @return BASE区块链区块数据汇总
     */
    public CryptoBaseBlock getSummary(CryptoBaseBlock query);

    /**
     * 查询BASE区块链区块列表
     *
     * @param query BASE区块链区块
     * @return BASE区块链区块集合
     */
    public List<CryptoBaseBlock> selectList(CryptoBaseBlock query);

    /**
     * 新增BASE区块链区块
     *
     * @param cryptoBaseBlock BASE区块链区块
     * @return 结果
     */
    public int insert(CryptoBaseBlock cryptoBaseBlock);

    /**
     * 修改BASE区块链区块
     *
     * @param cryptoBaseBlock BASE区块链区块
     * @return 结果
     */
    public int update(CryptoBaseBlock cryptoBaseBlock);

    /**
     * 批量删除BASE区块链区块
     *
     * @param ids 需要删除的BASE区块链区块主键集合
     * @return 结果
     */
    public int deleteByIds(List<Long> ids);

    /**
     * 删除BASE区块链区块信息
     *
     * @param id BASE区块链区块主键
     * @return 结果
     */
    public int deleteById(Long id);


    /**
     * 查询自定义分析数据
     *
     * @param query BASE区块链区块
     * @return BASE区块链区块集合
     */
    public List<CryptoBaseBlock> summary(SummaryRequest query);

    CryptoBaseBlock allSummary(SummaryRequest query);

    CryptoBaseBlockMapper getMapper();
}
