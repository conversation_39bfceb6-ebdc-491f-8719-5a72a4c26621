package tv.shorthub.system.service;

import tv.shorthub.system.mapper.AppUserLevelMapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import tv.shorthub.system.domain.AppUserLevel;
import tv.shorthub.common.core.service.IBaseService;

/**
 * 会员级别Service接口
 *
 * <AUTHOR>
 * @date 2025-05-14
 */
public interface IAppUserLevelService extends IBaseService<AppUserLevel>
{
    /**
     * 查询会员级别
     *
     * @param id 会员级别主键
     * @return 会员级别
     */
    public AppUserLevel getById(Long id);

    /**
     * 查询会员级别数据汇总
     *
     * @param query 会员级别
     * @return 会员级别数据汇总
     */
    public AppUserLevel getSummary(AppUserLevel query);

    /**
     * 查询会员级别列表
     *
     * @param query 会员级别
     * @return 会员级别集合
     */
    public List<AppUserLevel> selectList(AppUserLevel query);

    /**
     * 新增会员级别
     *
     * @param appUserLevel 会员级别
     * @return 结果
     */
    public int insert(AppUserLevel appUserLevel);

    /**
     * 修改会员级别
     *
     * @param appUserLevel 会员级别
     * @return 结果
     */
    public int update(AppUserLevel appUserLevel);

    /**
     * 批量删除会员级别
     *
     * @param ids 需要删除的会员级别主键集合
     * @return 结果
     */
    public int deleteByIds(List<Long> ids);

    /**
     * 删除会员级别信息
     *
     * @param id 会员级别主键
     * @return 结果
     */
    public int deleteById(Long id);


    /**
     * 查询自定义分析数据
     *
     * @param query 会员级别
     * @return 会员级别集合
     */
    public List<AppUserLevel> summary(SummaryRequest query);

    AppUserLevel allSummary(SummaryRequest query);

    AppUserLevelMapper getMapper();
}
