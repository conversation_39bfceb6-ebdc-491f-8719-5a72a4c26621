package tv.shorthub.system.service;

import tv.shorthub.common.core.service.IBaseService;
import tv.shorthub.system.domain.AppDramaGenres;
import tv.shorthub.system.mapper.AppDramaGenresMapper;

import java.util.List;

/**
 * 剧目-类型关联Service接口
 * 
 * <AUTHOR>
 * @date 2025-05-06
 */
public interface IAppDramaGenresService extends IBaseService<AppDramaGenres>
{
    /**
     * 查询剧目-类型关联
     * 
     * @param id 剧目-类型关联主键
     * @return 剧目-类型关联
     */
    public AppDramaGenres getById(Long id);

    /**
     * 查询剧目-类型关联列表
     * 
     * @param query 剧目-类型关联
     * @return 剧目-类型关联集合
     */
    public List<AppDramaGenres> selectList(AppDramaGenres query);

    /**
     * 新增剧目-类型关联
     * 
     * @param appDramaGenres 剧目-类型关联
     * @return 结果
     */
    public int insert(AppDramaGenres appDramaGenres);

    /**
     * 批量新增剧目-类型关联
     * 
     * @param dramaGenresList 剧目-类型关联列表
     * @return 结果
     */
    public int batchInsert(List<AppDramaGenres> dramaGenresList);

    /**
     * 修改剧目-类型关联
     * 
     * @param appDramaGenres 剧目-类型关联
     * @return 结果
     */
    public int update(AppDramaGenres appDramaGenres);

    /**
     * 批量删除剧目-类型关联
     * 
     * @param ids 需要删除的剧目-类型关联主键集合
     * @return 结果
     */
    public int deleteByIds(List<Long> ids);

    /**
     * 删除剧目-类型关联信息
     * 
     * @param id 剧目-类型关联主键
     * @return 结果
     */
    public int deleteById(Long id);

    /**
     * 通过剧目ID删除剧目-类型关联
     * 
     * @param dramaId 剧目ID
     * @return 结果
     */
    public int deleteByDramaId(String dramaId);

    /**
     * 获取剧目关联的类型ID列表
     * 
     * @param dramaId 剧目ID
     * @return 类型ID列表
     */
    public List<Long> getGenreIdsByDramaId(String dramaId);
    
    AppDramaGenresMapper getMapper();
} 