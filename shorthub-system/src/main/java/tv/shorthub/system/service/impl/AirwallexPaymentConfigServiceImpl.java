package tv.shorthub.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import tv.shorthub.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tv.shorthub.system.mapper.AirwallexPaymentConfigMapper;
import tv.shorthub.system.domain.AirwallexPaymentConfig;
import tv.shorthub.system.service.IAirwallexPaymentConfigService;
import tv.shorthub.common.core.service.BaseService;

/**
 * airwallex支付配置Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
@Service
public class AirwallexPaymentConfigServiceImpl extends BaseService<AirwallexPaymentConfig> implements IAirwallexPaymentConfigService
{
    @Autowired
    private AirwallexPaymentConfigMapper airwallexPaymentConfigMapper;

    @Override
    public AirwallexPaymentConfigMapper getMapper() {
        return airwallexPaymentConfigMapper;
    }

    /**
     * 查询airwallex支付配置
     *
     * @param id airwallex支付配置主键
     * @return airwallex支付配置
     */
    @Override
    public AirwallexPaymentConfig getById(Long id)
    {
        return airwallexPaymentConfigMapper.selectById(id);
    }

    /**
     * 查询airwallex支付配置列表
     *
     * @param query airwallex支付配置
     * @return airwallex支付配置
     */
    @Override
    public List<AirwallexPaymentConfig> selectList(AirwallexPaymentConfig query)
    {
        return airwallexPaymentConfigMapper.selectList(new QueryWrapper<>(query));
    }


    /**
     * 查询airwallex支付配置数据汇总
     *
     * @param query airwallex支付配置
     * @return airwallex支付配置
     */
    @Override
    public AirwallexPaymentConfig getSummary(AirwallexPaymentConfig query)
    {
        return airwallexPaymentConfigMapper.getSummary(query);
    }



    /**
     * 查询自定义分析数据
     *
     * @param query airwallex支付配置
     * @return airwallex支付配置
     */
    @Override
    public List<AirwallexPaymentConfig> summary(SummaryRequest query)
    {
        return airwallexPaymentConfigMapper.summary(query);
    }

    @Override
    public AirwallexPaymentConfig allSummary(SummaryRequest query)
    {
        return airwallexPaymentConfigMapper.allSummary(query);
    }

    /**
     * 新增airwallex支付配置
     *
     * @param airwallexPaymentConfig airwallex支付配置
     * @return 结果
     */
    @Override
    public int insert(AirwallexPaymentConfig airwallexPaymentConfig)
    {
        airwallexPaymentConfig.setCreateTime(DateUtils.getNowDate());
        return airwallexPaymentConfigMapper.insert(airwallexPaymentConfig);
    }

    /**
     * 修改airwallex支付配置
     *
     * @param airwallexPaymentConfig airwallex支付配置
     * @return 结果
     */
    @Override
    public int update(AirwallexPaymentConfig airwallexPaymentConfig)
    {
        airwallexPaymentConfig.setUpdateTime(DateUtils.getNowDate());
        return airwallexPaymentConfigMapper.updateById(airwallexPaymentConfig);
    }

    /**
     * 批量删除airwallex支付配置
     *
     * @param ids 需要删除的airwallex支付配置主键
     * @return 结果
     */
    @Override
    public int deleteByIds(List<Long> ids)
    {
        return airwallexPaymentConfigMapper.deleteBatchIds(ids);
    }

    /**
     * 删除airwallex支付配置信息
     *
     * @param id airwallex支付配置主键
     * @return 结果
     */
    @Override
    public int deleteById(Long id)
    {
        return airwallexPaymentConfigMapper.deleteById(id);
    }
}
