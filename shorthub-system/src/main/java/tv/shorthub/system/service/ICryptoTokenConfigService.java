package tv.shorthub.system.service;

import tv.shorthub.system.mapper.CryptoTokenConfigMapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import tv.shorthub.system.domain.CryptoTokenConfig;
import tv.shorthub.common.core.service.IBaseService;

/**
 * 虚拟货币代币配置Service接口
 *
 * <AUTHOR>
 * @date 2025-08-05
 */
public interface ICryptoTokenConfigService extends IBaseService<CryptoTokenConfig>
{
    /**
     * 查询虚拟货币代币配置
     *
     * @param id 虚拟货币代币配置主键
     * @return 虚拟货币代币配置
     */
    public CryptoTokenConfig getById(Long id);

    /**
     * 查询虚拟货币代币配置数据汇总
     *
     * @param query 虚拟货币代币配置
     * @return 虚拟货币代币配置数据汇总
     */
    public CryptoTokenConfig getSummary(CryptoTokenConfig query);

    /**
     * 查询虚拟货币代币配置列表
     *
     * @param query 虚拟货币代币配置
     * @return 虚拟货币代币配置集合
     */
    public List<CryptoTokenConfig> selectList(CryptoTokenConfig query);

    /**
     * 新增虚拟货币代币配置
     *
     * @param cryptoTokenConfig 虚拟货币代币配置
     * @return 结果
     */
    public int insert(CryptoTokenConfig cryptoTokenConfig);

    /**
     * 修改虚拟货币代币配置
     *
     * @param cryptoTokenConfig 虚拟货币代币配置
     * @return 结果
     */
    public int update(CryptoTokenConfig cryptoTokenConfig);

    /**
     * 批量删除虚拟货币代币配置
     *
     * @param ids 需要删除的虚拟货币代币配置主键集合
     * @return 结果
     */
    public int deleteByIds(List<Long> ids);

    /**
     * 删除虚拟货币代币配置信息
     *
     * @param id 虚拟货币代币配置主键
     * @return 结果
     */
    public int deleteById(Long id);


    /**
     * 查询自定义分析数据
     *
     * @param query 虚拟货币代币配置
     * @return 虚拟货币代币配置集合
     */
    public List<CryptoTokenConfig> summary(SummaryRequest query);

    CryptoTokenConfig allSummary(SummaryRequest query);

    CryptoTokenConfigMapper getMapper();
}
