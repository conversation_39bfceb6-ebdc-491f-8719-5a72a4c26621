package tv.shorthub.system.service;

import com.alibaba.fastjson2.JSONObject;
import tv.shorthub.system.mapper.PaypalSubscriptionRenewalMapper;
import tv.shorthub.common.core.domain.SummaryRequest;

import java.text.ParseException;
import java.util.List;
import tv.shorthub.system.domain.PaypalSubscriptionRenewal;
import tv.shorthub.common.core.service.IBaseService;

/**
 * PayPal订阅续订记录Service接口
 *
 * <AUTHOR>
 * @date 2025-06-12
 */
public interface IPaypalSubscriptionRenewalService extends IBaseService<PaypalSubscriptionRenewal>
{
    /**
     * 查询PayPal订阅续订记录
     *
     * @param id PayPal订阅续订记录主键
     * @return PayPal订阅续订记录
     */
    public PaypalSubscriptionRenewal getById(Long id);

    /**
     * 查询PayPal订阅续订记录数据汇总
     *
     * @param query PayPal订阅续订记录
     * @return PayPal订阅续订记录数据汇总
     */
    public PaypalSubscriptionRenewal getSummary(PaypalSubscriptionRenewal query);

    /**
     * 查询PayPal订阅续订记录列表
     *
     * @param query PayPal订阅续订记录
     * @return PayPal订阅续订记录集合
     */
    public List<PaypalSubscriptionRenewal> selectList(PaypalSubscriptionRenewal query);

    /**
     * 新增PayPal订阅续订记录
     *
     * @param paypalSubscriptionRenewal PayPal订阅续订记录
     * @return 结果
     */
    public int insert(PaypalSubscriptionRenewal paypalSubscriptionRenewal);

    int insert(String orderNo, JSONObject rawData, Long cyclesCompleted, Long totalCycles) throws ParseException;

    /**
     * 修改PayPal订阅续订记录
     *
     * @param paypalSubscriptionRenewal PayPal订阅续订记录
     * @return 结果
     */
    public int update(PaypalSubscriptionRenewal paypalSubscriptionRenewal);

    /**
     * 批量删除PayPal订阅续订记录
     *
     * @param ids 需要删除的PayPal订阅续订记录主键集合
     * @return 结果
     */
    public int deleteByIds(List<Long> ids);

    /**
     * 删除PayPal订阅续订记录信息
     *
     * @param id PayPal订阅续订记录主键
     * @return 结果
     */
    public int deleteById(Long id);


    /**
     * 查询自定义分析数据
     *
     * @param query PayPal订阅续订记录
     * @return PayPal订阅续订记录集合
     */
    public List<PaypalSubscriptionRenewal> summary(SummaryRequest query);

    PaypalSubscriptionRenewal allSummary(SummaryRequest query);

    PaypalSubscriptionRenewalMapper getMapper();
}
