package tv.shorthub.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import tv.shorthub.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tv.shorthub.system.mapper.AppUserMemberMapper;
import tv.shorthub.system.domain.AppUserMember;
import tv.shorthub.system.service.IAppUserMemberService;
import tv.shorthub.common.core.service.BaseService;

/**
 * 用户会员开通记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-15
 */
@Service
public class AppUserMemberServiceImpl extends BaseService<AppUserMember> implements IAppUserMemberService
{
    @Autowired
    private AppUserMemberMapper appUserMemberMapper;

    @Override
    public AppUserMemberMapper getMapper() {
        return appUserMemberMapper;
    }

    /**
     * 查询用户会员开通记录
     *
     * @param id 用户会员开通记录主键
     * @return 用户会员开通记录
     */
    @Override
    public AppUserMember getById(Long id)
    {
        return appUserMemberMapper.selectById(id);
    }

    /**
     * 查询用户会员开通记录列表
     *
     * @param query 用户会员开通记录
     * @return 用户会员开通记录
     */
    @Override
    public List<AppUserMember> selectList(AppUserMember query)
    {
        return appUserMemberMapper.selectList(new QueryWrapper<>(query));
    }


    /**
     * 查询用户会员开通记录数据汇总
     *
     * @param query 用户会员开通记录
     * @return 用户会员开通记录
     */
    @Override
    public AppUserMember getSummary(AppUserMember query)
    {
        return appUserMemberMapper.getSummary(query);
    }



    /**
     * 查询自定义分析数据
     *
     * @param query 用户会员开通记录
     * @return 用户会员开通记录
     */
    @Override
    public List<AppUserMember> summary(SummaryRequest query)
    {
        return appUserMemberMapper.summary(query);
    }

    @Override
    public AppUserMember allSummary(SummaryRequest query)
    {
        return appUserMemberMapper.allSummary(query);
    }

    /**
     * 新增用户会员开通记录
     *
     * @param appUserMember 用户会员开通记录
     * @return 结果
     */
    @Override
    public int insert(AppUserMember appUserMember)
    {
        appUserMember.setCreateTime(DateUtils.getNowDate());
        return appUserMemberMapper.insert(appUserMember);
    }

    /**
     * 修改用户会员开通记录
     *
     * @param appUserMember 用户会员开通记录
     * @return 结果
     */
    @Override
    public int update(AppUserMember appUserMember)
    {
        appUserMember.setUpdateTime(DateUtils.getNowDate());
        return appUserMemberMapper.updateById(appUserMember);
    }

    /**
     * 批量删除用户会员开通记录
     *
     * @param ids 需要删除的用户会员开通记录主键
     * @return 结果
     */
    @Override
    public int deleteByIds(List<Long> ids)
    {
        return appUserMemberMapper.deleteBatchIds(ids);
    }

    /**
     * 删除用户会员开通记录信息
     *
     * @param id 用户会员开通记录主键
     * @return 结果
     */
    @Override
    public int deleteById(Long id)
    {
        return appUserMemberMapper.deleteById(id);
    }
}
