package tv.shorthub.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import tv.shorthub.common.core.domain.SummaryRequest;

import java.util.List;

import tv.shorthub.common.enums.OrderChannelEnums;
import tv.shorthub.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tv.shorthub.common.utils.SecurityUtils;
import tv.shorthub.common.utils.StringUtils;
import tv.shorthub.googleplay.service.GooglePlayService;
import tv.shorthub.system.mapper.AppOrderInfoMapper;
import tv.shorthub.system.domain.AppOrderInfo;
import tv.shorthub.system.service.*;
import tv.shorthub.common.core.service.BaseService;
import tv.shorthub.common.core.redis.RedisCache;
import com.alibaba.fastjson2.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 充值订单Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-10
 */
@Service
public class AppOrderInfoServiceImpl extends BaseService<AppOrderInfo> implements IAppOrderInfoService
{
    private static final Logger log = LoggerFactory.getLogger(AppOrderInfoServiceImpl.class);

    @Autowired
    private AppOrderInfoMapper appOrderInfoMapper;

    @Autowired
    private IPaypalPaymentLogService paypalPaymentLogService;

    @Autowired
    private IPayermaxPaymentLogService payermaxPaymentLogService;

    @Autowired
    private IAirwallexPaymentLogService airwallexPaymentLogService;

    @Autowired
    private IGooglePlayBillingLogService googlePlayBillingLogService;

    @Autowired
    private RedisCache redisCache;

    @Override
    public AppOrderInfoMapper getMapper() {
        return appOrderInfoMapper;
    }

    /**
     * 查询充值订单
     *
     * @param id 充值订单主键
     * @return 充值订单
     */
    @Override
    public AppOrderInfo getById(Long id)
    {
        return appOrderInfoMapper.selectById(id);
    }

    /**
     * 查询充值订单列表
     *
     * @param query 充值订单
     * @return 充值订单
     */
    @Override
    public List<AppOrderInfo> selectList(AppOrderInfo query)
    {
        QueryWrapper<AppOrderInfo> queryWrapper = new QueryWrapper<>(query);
        if (!SecurityUtils.isSystemAdmin()) {
            if (!SecurityUtils.isBusinessAdmin()) {
                queryWrapper.eq("deliver_username", SecurityUtils.getUsername());
            } else {
                queryWrapper.eq("appid", SecurityUtils.getAppid());
                if (StringUtils.isNotEmpty(query.getDeliverUsername())) {
                    queryWrapper.eq("deliver_username", query.getDeliverUsername());
                }
            }
        }
        if (query.getParams().containsKey("country") && StringUtils.isNotEmpty(query.getParams().get("country").toString())) {
            queryWrapper.apply("JSON_UNQUOTE(JSON_EXTRACT(extend_json, '$.country')) = {0}", query.getParams().get("country"));
        }

        queryWrapper.orderByDesc("create_time");

        log.info("查询条件: {}", JSONObject.toJSONString(query));
        log.info("sql: {}", queryWrapper.getCustomSqlSegment());
        return appOrderInfoMapper.selectList(queryWrapper);
    }


    /**
     * 查询充值订单数据汇总
     *
     * @param query 充值订单
     * @return 充值订单
     */
    @Override
    public AppOrderInfo getSummary(AppOrderInfo query)
    {
        return appOrderInfoMapper.getSummary(query);
    }



    /**
     * 查询自定义分析数据
     *
     * @param query 充值订单
     * @return 充值订单
     */
    @Override
    public List<AppOrderInfo> summary(SummaryRequest query)
    {
        return appOrderInfoMapper.summary(query);
    }

    @Override
    public AppOrderInfo allSummary(SummaryRequest query)
    {
        return appOrderInfoMapper.allSummary(query);
    }

    /**
     * 新增充值订单
     *
     * @param appOrderInfo 充值订单
     * @return 结果
     */
    @Override
    public int insert(AppOrderInfo appOrderInfo)
    {
        appOrderInfo.setCreateTime(DateUtils.getNowDate());
        return appOrderInfoMapper.insert(appOrderInfo);
    }

    /**
     * 修改充值订单
     *
     * @param appOrderInfo 充值订单
     * @return 结果
     */
    @Override
    public int update(AppOrderInfo appOrderInfo)
    {
        appOrderInfo.setUpdateTime(DateUtils.getNowDate());
        return appOrderInfoMapper.updateById(appOrderInfo);
    }

    /**
     * 批量删除充值订单
     *
     * @param ids 需要删除的充值订单主键
     * @return 结果
     */
    @Override
    public int deleteByIds(List<Long> ids)
    {
        return appOrderInfoMapper.deleteBatchIds(ids);
    }

    /**
     * 删除充值订单信息
     *
     * @param id 充值订单主键
     * @return 结果
     */
    @Override
    public int deleteById(Long id)
    {
        return appOrderInfoMapper.deleteById(id);
    }

    /**
     * 更新订单支付状态
     *
     * @param orderNo 订单号
     * @return 更新结果
     */
    @Override
    public int updateOrderStatus(String orderNo) {
        // 查询订单信息
        AppOrderInfo order = new AppOrderInfo();
        order.setOrderNo(orderNo);

        // 关键修复：直接使用Mapper查询，以绕过带有SecurityUtils检查的selectList方法
        QueryWrapper<AppOrderInfo> wrapper = new QueryWrapper<>(order);
        List<AppOrderInfo> orders = appOrderInfoMapper.selectList(wrapper);
        
        if (orders == null || orders.isEmpty()) {
            throw new RuntimeException("未找到对应的订单记录");
        }
        AppOrderInfo appOrderInfo = orders.getFirst();

        try {
            // 如果是PayPal订单，调用PayPal服务更新状态
            JSONObject rawData = null;
            if (OrderChannelEnums.PAYPAL.getValue().equals(appOrderInfo.getOrderChannel())) {
                rawData = paypalPaymentLogService.updateOrderStatus(orderNo);

            } else if (OrderChannelEnums.PAYERMAX.getValue().equals(appOrderInfo.getOrderChannel())) {
                rawData = payermaxPaymentLogService.updateOrderStatus(orderNo);
            }

            // 根据状态更新订单状态
            if (rawData != null) {
                log.info("更新订单状态完成: orderNo={}, rawData={}", orderNo, rawData);
                // 检查订单支付状态
                if (rawData.containsKey("hasSuccess") && rawData.getBoolean("hasSuccess")) {
                    if (appOrderInfo.getOrderStatus().compareTo(0L) == 0) {
                        // TODO 给用户充值
                    }
                    appOrderInfo.setOrderStatus(1L);
                    update(appOrderInfo);
                }

                // 检查是否有退款状态
                if (rawData.containsKey("hasRefund") && rawData.getBoolean("hasRefund")) {
                    appOrderInfo.setRefundStatus(true);
                    update(appOrderInfo);
                }

                // 检查订阅状态
                if (rawData.containsKey("isUnsubscribed") && rawData.getBoolean("isUnsubscribed")) {
                    appOrderInfo.setUnsubscriptionStatus(true);
                    update(appOrderInfo);
                }
            }
            return 1;
        } catch (Exception e) {
            log.error("更新订单状态异常", e);
            throw new RuntimeException("更新订单状态失败: " + e.getMessage());
        }
    }



    /**
     * 退款
     *
     * @param appOrderInfo 充值订单
     * @return 结果
     */
    @Override
    public int refund(AppOrderInfo appOrderInfo) {
        // 检查订单状态
        AppOrderInfo order = getById(appOrderInfo.getId());
        if (order == null) {
            throw new RuntimeException("订单不存在");
        }
        if (order.getOrderStatus().compareTo(1L) != 0) {
            throw new RuntimeException("订单未支付，不能退款");
        }
        if (Boolean.TRUE.equals(order.getRefundStatus())) {
            throw new RuntimeException("订单已退款");
        }
        if (!"0".equals(order.getPayType()) && !"1".equals(order.getPayType())) {
            throw new RuntimeException("只有会员和金币订单可以退款");
        }

        try {
            // 如果是PayPal订单，调用PayPal服务处理退款
            if (OrderChannelEnums.PAYPAL.getValue().equals(order.getOrderChannel())) {
                JSONObject result = paypalPaymentLogService.processRefund(order.getOrderNo(), "refund");

                if (result != null && "COMPLETED".equalsIgnoreCase(result.getString("status"))) {
                    // 更新退款状态
                    appOrderInfo.setRefundStatus(true);
                    update(appOrderInfo);
                    return 1;
                }
            } else if (OrderChannelEnums.PAYERMAX.getValue().equals(order.getOrderChannel())) {
                JSONObject result = payermaxPaymentLogService.processRefund(order.getOrderNo(), "refund");
                // 更新退款状态
                appOrderInfo.setRefundStatus(true);
                update(appOrderInfo);
            }
            return 0;
        } catch (Exception e) {
            log.error("退款操作异常", e);
            throw new RuntimeException("退款操作失败: " + e.getMessage());
        }
    }

    /**
     * 退订
     *
     * @param appOrderInfo 充值订单
     * @return 结果
     */
    @Override
    public int unsubscribe(AppOrderInfo appOrderInfo) {
        // 检查订单状态
        AppOrderInfo order = getById(appOrderInfo.getId());
        if (order == null) {
            log.info("订单不存在: {}", appOrderInfo.getOrderNo());
            return 0;
        }
        if (order.getOrderStatus().compareTo(1L) != 0) {
            log.info("订单未支付，不能退订: {}", appOrderInfo.getOrderNo());
            return 0;
        }
        if (Boolean.TRUE.equals(order.getUnsubscriptionStatus())) {
            log.info("订单已退订: {}", appOrderInfo.getOrderNo());
            return 0;
        }
        if (!"2".equals(order.getPayType())) {
            log.info("只有订阅订单可以退订: {}", appOrderInfo.getOrderNo());
            return 0;
        }

        try {
            // 如果是PayPal订单，调用PayPal服务处理退订
            if (OrderChannelEnums.PAYPAL.getValue().equals(order.getOrderChannel())) {
                JSONObject result = paypalPaymentLogService.processUnsubscribe(order.getOrderNo(), "unsubscribe");

                if (result == null) {
                    // 更新退订状态
                    appOrderInfo.setUnsubscriptionStatus(true);
                    update(appOrderInfo);
                    return 1;
                }
            } else if (OrderChannelEnums.PAYERMAX.getValue().equals(order.getOrderChannel())) {
                JSONObject result = payermaxPaymentLogService.processUnsubscribe(order.getOrderNo(), "unsubscribe");
                if (result == null) {
                    // 更新退订状态
                    appOrderInfo.setUnsubscriptionStatus(true);
                    update(appOrderInfo);
                    return 1;
                }
            } else if (OrderChannelEnums.AIRWALLEX.getValue().equals(order.getOrderChannel())) {
                JSONObject result = airwallexPaymentLogService.processUnsubscribe(order.getOrderNo(), "unsubscribe");
                if (result == null) {
                    // 更新退订状态
                    appOrderInfo.setUnsubscriptionStatus(true);
                    update(appOrderInfo);
                    return 1;
                }
            } else if (OrderChannelEnums.GOOGLEPLAY.getValue().equals(order.getOrderChannel())) {
                JSONObject result = googlePlayBillingLogService.processUnsubscribe(order.getOrderNo(), "unsubscribe");
                if (result == null) {
                    // 更新退订状态
                    appOrderInfo.setUnsubscriptionStatus(true);
                    update(appOrderInfo);
                    return 1;
                }
            }
            return 0;
        } catch (Exception e) {
            log.error("退订操作异常", e);
            return 0;
        }
    }


}
