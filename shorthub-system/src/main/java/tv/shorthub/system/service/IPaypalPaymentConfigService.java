package tv.shorthub.system.service;

import tv.shorthub.system.mapper.PaypalPaymentConfigMapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import tv.shorthub.system.domain.PaypalPaymentConfig;
import tv.shorthub.common.core.service.IBaseService;

/**
 * paypal配置Service接口
 *
 * <AUTHOR>
 * @date 2025-05-22
 */
public interface IPaypalPaymentConfigService extends IBaseService<PaypalPaymentConfig>
{
    /**
     * 查询paypal配置
     *
     * @param id paypal配置主键
     * @return paypal配置
     */
    public PaypalPaymentConfig getById(Long id);

    /**
     * 查询paypal配置数据汇总
     *
     * @param query paypal配置
     * @return paypal配置数据汇总
     */
    public PaypalPaymentConfig getSummary(PaypalPaymentConfig query);

    /**
     * 查询paypal配置列表
     *
     * @param query paypal配置
     * @return paypal配置集合
     */
    public List<PaypalPaymentConfig> selectList(PaypalPaymentConfig query);

    /**
     * 新增paypal配置
     *
     * @param paypalPaymentConfig paypal配置
     * @return 结果
     */
    public int insert(PaypalPaymentConfig paypalPaymentConfig);

    /**
     * 修改paypal配置
     *
     * @param paypalPaymentConfig paypal配置
     * @return 结果
     */
    public int update(PaypalPaymentConfig paypalPaymentConfig);

    /**
     * 批量删除paypal配置
     *
     * @param ids 需要删除的paypal配置主键集合
     * @return 结果
     */
    public int deleteByIds(List<Long> ids);

    /**
     * 删除paypal配置信息
     *
     * @param id paypal配置主键
     * @return 结果
     */
    public int deleteById(Long id);


    /**
     * 查询自定义分析数据
     *
     * @param query paypal配置
     * @return paypal配置集合
     */
    public List<PaypalPaymentConfig> summary(SummaryRequest query);

    PaypalPaymentConfig allSummary(SummaryRequest query);

    PaypalPaymentConfigMapper getMapper();
}
