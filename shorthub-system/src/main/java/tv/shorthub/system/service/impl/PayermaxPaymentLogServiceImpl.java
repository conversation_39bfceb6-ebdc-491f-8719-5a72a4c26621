package tv.shorthub.system.service.impl;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;

import tv.shorthub.common.enums.CloudflareQueueEnum;
import tv.shorthub.common.queue.CloudflareQueueService;
import tv.shorthub.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tv.shorthub.payermax.service.PayermaxOrderService;
import tv.shorthub.payermax.service.PayermaxSubscribeService;
import tv.shorthub.paypal.constant.PayPalConstants;
import tv.shorthub.system.domain.AppOrderInfo;
import tv.shorthub.system.domain.PayermaxPaymentLog;
import tv.shorthub.system.mapper.PayermaxPaymentLogMapper;
import tv.shorthub.system.domain.PayermaxPaymentLog;
import tv.shorthub.system.service.IAppOrderInfoService;
import tv.shorthub.system.service.IPayermaxPaymentLogService;
import tv.shorthub.common.core.service.BaseService;

/**
 * payermax支付日志Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-16
 */
@Service
@Slf4j
public class PayermaxPaymentLogServiceImpl extends BaseService<PayermaxPaymentLog> implements IPayermaxPaymentLogService
{
    @Autowired
    private PayermaxPaymentLogMapper payermaxPaymentLogMapper;

    @Autowired
    @Lazy
    private IAppOrderInfoService appOrderInfoService;

    @Autowired
    PayermaxSubscribeService payermaxSubscribeService;

    @Autowired
    PayermaxOrderService payermaxOrderService;

    @Autowired
    CloudflareQueueService cloudflareQueueService;

    @Override
    public PayermaxPaymentLogMapper getMapper() {
        return payermaxPaymentLogMapper;
    }

    @Override
    public JSONObject processUnsubscribe(String orderNo, String unsubscribe) {
        // 查询Payermax支付记录
        PayermaxPaymentLog paymentLog = new PayermaxPaymentLog();
        paymentLog.setOrderNo(orderNo);
        List<PayermaxPaymentLog> paymentLogs = selectList(paymentLog);
        if (paymentLogs == null || paymentLogs.isEmpty()) {
            throw new RuntimeException("未找到对应的Payermax支付记录");
        }
        PayermaxPaymentLog PayermaxLog = paymentLogs.getFirst();
        JSONObject subscriptionPlan = PayermaxLog.getDetailRawData().getJSONObject("data").getJSONObject("subscriptionPlan");

        if (!"ACTIVE".equalsIgnoreCase(subscriptionPlan.getString("subscriptionStatus"))) {
            throw new RuntimeException("payermax订阅状态不是活跃状态，无法退订");
        }

        try {
            // 调用Payermax退订接口
            JSONObject result = payermaxSubscribeService.subscriptionCancel(subscriptionPlan.getString("subscriptionNo"));
            log.info("退订结果: {}", result);

            // 检查退订结果
            if (null != result) {
                // 更新payermax状态
                updateOrderStatus(orderNo);
                return null;
            } else {
                log.error("Payermax退订失败: {}", result);
                throw new RuntimeException("Payermax退订失败: " + result.getString("message"));
            }
        } catch (Exception e) {
            log.error("退订操作异常", e);
            throw new RuntimeException("退订操作失败: " + e.getMessage());
        }
    }


    /**
     * 处理Payermax退款
     *
     * @param orderNo 订单号
     * @param reason 退款原因
     * @return 退款结果
     */
    @Override
    public JSONObject processRefund(String orderNo, String reason) {
        // 查询Payermax支付记录
        PayermaxPaymentLog query = new PayermaxPaymentLog();
        query.setOrderNo(orderNo);
        List<PayermaxPaymentLog> paymentLogs = selectList(query);
        if (paymentLogs == null || paymentLogs.isEmpty()) {
            throw new RuntimeException("未找到对应的Payermax支付记录");
        }
        PayermaxPaymentLog payermaxLog = paymentLogs.getFirst();
        JSONObject data = payermaxLog.getDetailRawData().getJSONObject("data");

        if (!"SUCCESS".equalsIgnoreCase(data.getString("status"))) {
            throw new RuntimeException("Payermax支付记录状态不正确");
        }

        try {
            // 调用Payermax退款接口
            JSONObject result = payermaxOrderService.refund(
                    "R" + payermaxLog.getOrderNo(),
                    payermaxLog.getTotalAmount(),
                    payermaxLog.getCurrency(),
                    payermaxLog.getOrderNo(),
                    reason
            );
            log.info("退款结果: {}", result);

            // 检查退款结果
            if (result != null) {
                // 更新Payermax状态
                updateOrderStatus(orderNo);
                return result;
            } else {
                log.error("Payermax退款失败: {}", result);
                throw new RuntimeException("Payermax退款失败: " + (result != null ? result.getString("message") : "未知错误"));
            }
        } catch (Exception e) {
            log.error("退款操作异常", e);
            throw new RuntimeException("退款操作失败: " + e.getMessage());
        }
    }


    /**
     * 更新Payermax订单状态并同步到本地订单
     *
     * @param orderNo 订单号
     * @return 更新结果
     */
    @Override
    public JSONObject updateOrderStatus(String orderNo) {
        // 查询Payermax支付记录
        PayermaxPaymentLog paymentLog = new PayermaxPaymentLog();
        paymentLog.setOrderNo(orderNo);
        List<PayermaxPaymentLog> paymentLogs = selectList(paymentLog);
        if (paymentLogs == null || paymentLogs.isEmpty()) {
            throw new RuntimeException("未找到对应的Payermax支付记录");
        }
        PayermaxPaymentLog payermaxLog = paymentLogs.getFirst();

        try {
            JSONObject rawData;

            // 根据支付方式查询不同的状态
            if (PayPalConstants.PaymentMethod.SUBSCRIPTION.equals(payermaxLog.getPaymentMethod())) {
                rawData = payermaxSubscribeService.subscriptionQuery(payermaxLog.getOrderNo());
            } else {
                rawData = payermaxOrderService.orderQuery(payermaxLog.getOrderNo());
            }

            log.info("payermax订单状态2: {}", rawData);

            if (rawData != null) {
                JSONObject data = rawData.getJSONObject("data");
                payermaxLog.setStatus(data.getString("status"));
                if (data.getString("status").equals("SUCCESS")) {
                    rawData.put("hasSuccess", true);
                }

                // 检查退订状态
                if (PayPalConstants.PaymentMethod.SUBSCRIPTION.equals(payermaxLog.getPaymentMethod())) {

                    JSONObject subscriptionPlan = data.getJSONObject("subscriptionPlan");
                    String subscriptionState = subscriptionPlan.getString("subscriptionStatus");
                    payermaxLog.setStatus(subscriptionState);
                    // 订阅支付检查退款
                    if ("CANCEL".equals(subscriptionState)) {
                        rawData.put("isUnsubscribed", true);
                    }
                } else {
                    // 检查退款状态
                    try {
                        JSONObject refundData = payermaxOrderService.refundQuery("R" + payermaxLog.getOrderNo());
                        log.info("退款状态: {}", refundData);
                        String status = refundData.getJSONObject("data").getString("status");
                        if (status.equals("REFUND_SUCCESS") || status.equals("REFUND_PENDING")) {
                            rawData.put("hasRefund", true);
                        }
                        payermaxLog.setStatus(status);
                    } catch (Exception ignored) {

                    }
                }
            }
            if (null == paymentLog.getDetailRawData()) {
                payermaxLog.setDetailRawData(rawData);
            }
            payermaxLog.setRawData(rawData);
            update(payermaxLog);

            JSONObject queueData = new JSONObject();
            queueData.put("orderNo", orderNo);
            cloudflareQueueService.sendMessage(CloudflareQueueEnum.PAYERMAX_ORDER_UPDATE.getValue(), queueData);
            log.info("最新Payermax订单数据:orderNo:{}, rawData:{}", orderNo, rawData);
            return rawData;
        } catch (Exception e) {
            log.error("更新Payermax订单状态异常: {}", orderNo, e);
        }
        return null;
    }

    /**
     * 查询payermax支付日志
     *
     * @param id payermax支付日志主键
     * @return payermax支付日志
     */
    @Override
    public PayermaxPaymentLog getById(Long id)
    {
        return payermaxPaymentLogMapper.selectById(id);
    }

    /**
     * 查询payermax支付日志列表
     *
     * @param query payermax支付日志
     * @return payermax支付日志
     */
    @Override
    public List<PayermaxPaymentLog> selectList(PayermaxPaymentLog query)
    {
        return payermaxPaymentLogMapper.selectList(new QueryWrapper<>(query));
    }


    /**
     * 查询payermax支付日志数据汇总
     *
     * @param query payermax支付日志
     * @return payermax支付日志
     */
    @Override
    public PayermaxPaymentLog getSummary(PayermaxPaymentLog query)
    {
        return payermaxPaymentLogMapper.getSummary(query);
    }



    /**
     * 查询自定义分析数据
     *
     * @param query payermax支付日志
     * @return payermax支付日志
     */
    @Override
    public List<PayermaxPaymentLog> summary(SummaryRequest query)
    {
        return payermaxPaymentLogMapper.summary(query);
    }

    @Override
    public PayermaxPaymentLog allSummary(SummaryRequest query)
    {
        return payermaxPaymentLogMapper.allSummary(query);
    }

    /**
     * 新增payermax支付日志
     *
     * @param payermaxPaymentLog payermax支付日志
     * @return 结果
     */
    @Override
    public int insert(PayermaxPaymentLog payermaxPaymentLog)
    {
        payermaxPaymentLog.setCreateTime(DateUtils.getNowDate());
        return payermaxPaymentLogMapper.insert(payermaxPaymentLog);
    }

    /**
     * 修改payermax支付日志
     *
     * @param payermaxPaymentLog payermax支付日志
     * @return 结果
     */
    @Override
    public int update(PayermaxPaymentLog payermaxPaymentLog)
    {
        payermaxPaymentLog.setUpdateTime(DateUtils.getNowDate());
        return payermaxPaymentLogMapper.updateById(payermaxPaymentLog);
    }

    /**
     * 批量删除payermax支付日志
     *
     * @param ids 需要删除的payermax支付日志主键
     * @return 结果
     */
    @Override
    public int deleteByIds(List<Long> ids)
    {
        return payermaxPaymentLogMapper.deleteBatchIds(ids);
    }

    /**
     * 删除payermax支付日志信息
     *
     * @param id payermax支付日志主键
     * @return 结果
     */
    @Override
    public int deleteById(Long id)
    {
        return payermaxPaymentLogMapper.deleteById(id);
    }

    @Override
    public String getPayermaxUserId(String userId) {
        // 查找当前用户是否存在历史支付成功的订单，然后再查询对应的payermax订单
        QueryWrapper<AppOrderInfo> queryWrapper = new QueryWrapper<AppOrderInfo>()
                .eq("user_id", userId)
                .eq("order_status", "1")
                .last("limit 1");
        AppOrderInfo appOrderInfo = appOrderInfoService.getMapper().selectOne(queryWrapper);
        if (null != appOrderInfo) {
            // 因为第一次创建payermax订单时，userId可能是临时用户ID, 所以这里要查找payermax支付日志, 从这里获取payermax的userId
            QueryWrapper<PayermaxPaymentLog> payermaxPaymentLogQueryWrapper = new QueryWrapper<PayermaxPaymentLog>()
                    .eq("order_no", appOrderInfo.getOrderNo())
                    .last("limit 1");
            PayermaxPaymentLog payermaxPaymentLog = getMapper().selectOne(payermaxPaymentLogQueryWrapper);
            if (null != payermaxPaymentLog) {
                userId = payermaxPaymentLog.getUserId();
                log.info("获取到payermax的userId: {}", userId);
            }
        }
        return userId;
    }
}
