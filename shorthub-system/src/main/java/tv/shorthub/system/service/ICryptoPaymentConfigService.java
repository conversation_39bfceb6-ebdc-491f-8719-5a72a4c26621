package tv.shorthub.system.service;

import tv.shorthub.system.mapper.CryptoPaymentConfigMapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import tv.shorthub.system.domain.CryptoPaymentConfig;
import tv.shorthub.common.core.service.IBaseService;

/**
 * 虚拟货币支付配置Service接口
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
public interface ICryptoPaymentConfigService extends IBaseService<CryptoPaymentConfig>
{
    /**
     * 查询虚拟货币支付配置
     *
     * @param id 虚拟货币支付配置主键
     * @return 虚拟货币支付配置
     */
    public CryptoPaymentConfig getById(Long id);

    /**
     * 查询虚拟货币支付配置数据汇总
     *
     * @param query 虚拟货币支付配置
     * @return 虚拟货币支付配置数据汇总
     */
    public CryptoPaymentConfig getSummary(CryptoPaymentConfig query);

    /**
     * 查询虚拟货币支付配置列表
     *
     * @param query 虚拟货币支付配置
     * @return 虚拟货币支付配置集合
     */
    public List<CryptoPaymentConfig> selectList(CryptoPaymentConfig query);

    /**
     * 新增虚拟货币支付配置
     *
     * @param cryptoPaymentConfig 虚拟货币支付配置
     * @return 结果
     */
    public int insert(CryptoPaymentConfig cryptoPaymentConfig);

    /**
     * 修改虚拟货币支付配置
     *
     * @param cryptoPaymentConfig 虚拟货币支付配置
     * @return 结果
     */
    public int update(CryptoPaymentConfig cryptoPaymentConfig);

    /**
     * 批量删除虚拟货币支付配置
     *
     * @param ids 需要删除的虚拟货币支付配置主键集合
     * @return 结果
     */
    public int deleteByIds(List<Long> ids);

    /**
     * 删除虚拟货币支付配置信息
     *
     * @param id 虚拟货币支付配置主键
     * @return 结果
     */
    public int deleteById(Long id);


    /**
     * 查询自定义分析数据
     *
     * @param query 虚拟货币支付配置
     * @return 虚拟货币支付配置集合
     */
    public List<CryptoPaymentConfig> summary(SummaryRequest query);

    CryptoPaymentConfig allSummary(SummaryRequest query);

    CryptoPaymentConfigMapper getMapper();
}
