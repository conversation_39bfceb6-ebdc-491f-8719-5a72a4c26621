package tv.shorthub.system.service.impl;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

@Component
@Configuration
public class IdWorkerConfig {

    @Bean
    public SnowflakeIdWorker snowflakeIdWorker() {
        // 这里的 workerId 和 datacenterId 可以通过配置文件或者其他方式注入
        return new SnowflakeIdWorker(1, 1);
    }
}
