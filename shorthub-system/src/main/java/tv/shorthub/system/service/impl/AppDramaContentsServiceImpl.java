package tv.shorthub.system.service.impl;

import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import tv.shorthub.common.core.service.BaseService;
import tv.shorthub.common.utils.DateUtils;
import tv.shorthub.system.domain.AppDramaContents;
import tv.shorthub.system.domain.AppDramaContentsSerial;
import tv.shorthub.system.mapper.AppDramaContentsMapper;
import tv.shorthub.system.mapper.AppDramaContentsSerialMapper;
import tv.shorthub.system.service.IAppDramaContentsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 剧目内容Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-05-06
 */
@Service
public class AppDramaContentsServiceImpl extends BaseService<AppDramaContents> implements IAppDramaContentsService
{
    @Autowired
    private AppDramaContentsMapper appDramaContentsMapper;

    @Autowired
    private AppDramaContentsSerialMapper appDramaContentsSerialMapper;

    @Override
    public AppDramaContentsMapper getMapper() {
        return appDramaContentsMapper;
    }

    /**
     * 查询剧目内容
     * 
     * @param id 剧目内容主键
     * @return 剧目内容
     */
    @Override
    public AppDramaContents getById(Long id)
    {
        return appDramaContentsMapper.selectById(id);
    }

    /**
     * 查询剧目内容列表
     * 
     * @param query 剧目内容
     * @return 剧目内容
     */
    @Override
    public List<AppDramaContents> selectList(AppDramaContents query)
    {
        List<AppDramaContents> appDramaContents = appDramaContentsMapper.selectList(new QueryWrapper<>(query));
        for (AppDramaContents appDramaContent : appDramaContents) {
            Long serialCount = appDramaContentsSerialMapper.selectCount(new QueryWrapper<AppDramaContentsSerial>().eq("content_id", appDramaContent.getContentId()));
            appDramaContent.getParams().put("serialCount", serialCount);
        }
        return appDramaContents;
    }


    /**
     * 查询剧目内容数据汇总
     *
     * @param query 剧目内容
     * @return 剧目内容
     */
    @Override
    public AppDramaContents getSummary(AppDramaContents query)
    {
        return appDramaContentsMapper.getSummary(query);
    }



    /**
     * 查询自定义分析数据
     *
     * @param query 剧目内容
     * @return 剧目内容
     */
    @Override
    public List<AppDramaContents> summary(SummaryRequest query)
    {
        return appDramaContentsMapper.summary(query);
    }

    @Override
    public AppDramaContents allSummary(SummaryRequest query)
    {
        return appDramaContentsMapper.allSummary(query);
    }

    /**
     * 新增剧目内容
     * 
     * @param appDramaContents 剧目内容
     * @return 结果
     */
    @Override
    public int insert(AppDramaContents appDramaContents)
    {
        appDramaContents.setContentId(IdUtil.getSnowflakeNextIdStr());
        appDramaContents.setCreateTime(DateUtils.getNowDate());
        return appDramaContentsMapper.insert(appDramaContents);
    }

    /**
     * 修改剧目内容
     * 
     * @param appDramaContents 剧目内容
     * @return 结果
     */
    @Override
    public int update(AppDramaContents appDramaContents)
    {
        appDramaContents.setUpdateTime(DateUtils.getNowDate());
        return appDramaContentsMapper.updateById(appDramaContents);
    }

    /**
     * 批量删除剧目内容
     * 
     * @param ids 需要删除的剧目内容主键
     * @return 结果
     */
    @Override
    public int deleteByIds(List<Long> ids)
    {
        return appDramaContentsMapper.deleteBatchIds(ids);
    }

    /**
     * 删除剧目内容信息
     * 
     * @param id 剧目内容主键
     * @return 结果
     */
    @Override
    public int deleteById(Long id)
    {
        return appDramaContentsMapper.deleteById(id);
    }
}
