package tv.shorthub.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import tv.shorthub.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tv.shorthub.system.mapper.AppDramaContentsSerialUrlMapper;
import tv.shorthub.system.domain.AppDramaContentsSerialUrl;
import tv.shorthub.system.service.IAppDramaContentsSerialUrlService;
import tv.shorthub.common.core.service.BaseService;

/**
 * 剧集链接Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Service
public class AppDramaContentsSerialUrlServiceImpl extends BaseService<AppDramaContentsSerialUrl> implements IAppDramaContentsSerialUrlService
{
    @Autowired
    private AppDramaContentsSerialUrlMapper appDramaContentsSerialUrlMapper;

    @Override
    public AppDramaContentsSerialUrlMapper getMapper() {
        return appDramaContentsSerialUrlMapper;
    }

    /**
     * 查询剧集链接
     *
     * @param id 剧集链接主键
     * @return 剧集链接
     */
    @Override
    public AppDramaContentsSerialUrl getById(Long id)
    {
        return appDramaContentsSerialUrlMapper.selectById(id);
    }

    /**
     * 查询剧集链接列表
     *
     * @param query 剧集链接
     * @return 剧集链接
     */
    @Override
    public List<AppDramaContentsSerialUrl> selectList(AppDramaContentsSerialUrl query)
    {
        return appDramaContentsSerialUrlMapper.selectList(new QueryWrapper<>(query));
    }


    /**
     * 查询剧集链接数据汇总
     *
     * @param query 剧集链接
     * @return 剧集链接
     */
    @Override
    public AppDramaContentsSerialUrl getSummary(AppDramaContentsSerialUrl query)
    {
        return appDramaContentsSerialUrlMapper.getSummary(query);
    }



    /**
     * 查询自定义分析数据
     *
     * @param query 剧集链接
     * @return 剧集链接
     */
    @Override
    public List<AppDramaContentsSerialUrl> summary(SummaryRequest query)
    {
        return appDramaContentsSerialUrlMapper.summary(query);
    }

    @Override
    public AppDramaContentsSerialUrl allSummary(SummaryRequest query)
    {
        return appDramaContentsSerialUrlMapper.allSummary(query);
    }

    /**
     * 新增剧集链接
     *
     * @param appDramaContentsSerialUrl 剧集链接
     * @return 结果
     */
    @Override
    public int insert(AppDramaContentsSerialUrl appDramaContentsSerialUrl)
    {
        appDramaContentsSerialUrl.setCreateTime(DateUtils.getNowDate());
        return appDramaContentsSerialUrlMapper.insert(appDramaContentsSerialUrl);
    }

    /**
     * 修改剧集链接
     *
     * @param appDramaContentsSerialUrl 剧集链接
     * @return 结果
     */
    @Override
    public int update(AppDramaContentsSerialUrl appDramaContentsSerialUrl)
    {
        appDramaContentsSerialUrl.setUpdateTime(DateUtils.getNowDate());
        return appDramaContentsSerialUrlMapper.updateById(appDramaContentsSerialUrl);
    }

    /**
     * 批量删除剧集链接
     *
     * @param ids 需要删除的剧集链接主键
     * @return 结果
     */
    @Override
    public int deleteByIds(List<Long> ids)
    {
        return appDramaContentsSerialUrlMapper.deleteBatchIds(ids);
    }

    /**
     * 删除剧集链接信息
     *
     * @param id 剧集链接主键
     * @return 结果
     */
    @Override
    public int deleteById(Long id)
    {
        return appDramaContentsSerialUrlMapper.deleteById(id);
    }
}
