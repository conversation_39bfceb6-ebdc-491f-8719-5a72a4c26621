package tv.shorthub.system.service;

import tv.shorthub.common.core.domain.entity.SysUser;
import tv.shorthub.common.core.domain.model.UserApp;
import tv.shorthub.system.domain.AppConfig;
import tv.shorthub.system.mapper.SysUserAppMapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import tv.shorthub.system.domain.SysUserApp;
import tv.shorthub.common.core.service.IBaseService;

/**
 * 系统用户app授权Service接口
 *
 * <AUTHOR>
 * @date 2025-07-21
 */
public interface ISysUserAppService extends IBaseService<SysUserApp>
{
    /**
     * 查询系统用户app授权
     *
     * @param id 系统用户app授权主键
     * @return 系统用户app授权
     */
    public SysUserApp getById(Long id);

    List<UserApp> selectAppListByUserName(SysUser user);

    /**
     * 查询系统用户app授权数据汇总
     *
     * @param query 系统用户app授权
     * @return 系统用户app授权数据汇总
     */
    public SysUserApp getSummary(SysUserApp query);

    /**
     * 查询系统用户app授权列表
     *
     * @param query 系统用户app授权
     * @return 系统用户app授权集合
     */
    public List<SysUserApp> selectList(SysUserApp query);

    /**
     * 新增系统用户app授权
     *
     * @param sysUserApp 系统用户app授权
     * @return 结果
     */
    public int insert(SysUserApp sysUserApp);

    /**
     * 修改系统用户app授权
     *
     * @param sysUserApp 系统用户app授权
     * @return 结果
     */
    public int update(SysUserApp sysUserApp);

    /**
     * 批量删除系统用户app授权
     *
     * @param ids 需要删除的系统用户app授权主键集合
     * @return 结果
     */
    public int deleteByIds(List<Long> ids);

    /**
     * 删除系统用户app授权信息
     *
     * @param id 系统用户app授权主键
     * @return 结果
     */
    public int deleteById(Long id);


    /**
     * 查询自定义分析数据
     *
     * @param query 系统用户app授权
     * @return 系统用户app授权集合
     */
    public List<SysUserApp> summary(SummaryRequest query);

    SysUserApp allSummary(SummaryRequest query);

    SysUserAppMapper getMapper();
}
