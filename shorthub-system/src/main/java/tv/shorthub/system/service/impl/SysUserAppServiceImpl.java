package tv.shorthub.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import tv.shorthub.common.core.domain.SummaryRequest;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import tv.shorthub.common.core.domain.entity.SysUser;
import tv.shorthub.common.core.domain.model.UserApp;
import tv.shorthub.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tv.shorthub.system.domain.AppConfig;
import tv.shorthub.system.mapper.SysUserAppMapper;
import tv.shorthub.system.domain.SysUserApp;
import tv.shorthub.system.service.ISysUserAppService;
import tv.shorthub.common.core.service.BaseService;

/**
 * 系统用户app授权Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-21
 */
@Service
public class SysUserAppServiceImpl extends BaseService<SysUserApp> implements ISysUserAppService
{
    @Autowired
    private SysUserAppMapper sysUserAppMapper;

    @Override
    public SysUserAppMapper getMapper() {
        return sysUserAppMapper;
    }

    /**
     * 查询系统用户app授权
     *
     * @param id 系统用户app授权主键
     * @return 系统用户app授权
     */
    @Override
    public SysUserApp getById(Long id)
    {
        return sysUserAppMapper.selectById(id);
    }

    /**
     * 查询系统用户app授权列表
     *
     * @param query 系统用户app授权
     * @return 系统用户app授权
     */
    @Override
    public List<SysUserApp> selectList(SysUserApp query)
    {
        return sysUserAppMapper.selectList(new QueryWrapper<>(query));
    }

    @Override
    public List<UserApp> selectAppListByUserName(SysUser user) {
        if (user.isAdmin()) {
            List<UserApp> appList = sysUserAppMapper.selectAppListByUserName(null);
            return appList.stream()
                .collect(Collectors.collectingAndThen(
                    Collectors.toMap(UserApp::getAppid, u -> u, (u1, u2) -> u1),
                    m -> new ArrayList<>(m.values())
                ));
        }
        return sysUserAppMapper.selectAppListByUserName(user.getUserName());
    }


    /**
     * 查询系统用户app授权数据汇总
     *
     * @param query 系统用户app授权
     * @return 系统用户app授权
     */
    @Override
    public SysUserApp getSummary(SysUserApp query)
    {
        return sysUserAppMapper.getSummary(query);
    }



    /**
     * 查询自定义分析数据
     *
     * @param query 系统用户app授权
     * @return 系统用户app授权
     */
    @Override
    public List<SysUserApp> summary(SummaryRequest query)
    {
        return sysUserAppMapper.summary(query);
    }

    @Override
    public SysUserApp allSummary(SummaryRequest query)
    {
        return sysUserAppMapper.allSummary(query);
    }

    /**
     * 新增系统用户app授权
     *
     * @param sysUserApp 系统用户app授权
     * @return 结果
     */
    @Override
    public int insert(SysUserApp sysUserApp)
    {
        sysUserApp.setCreateTime(DateUtils.getNowDate());
        return sysUserAppMapper.insert(sysUserApp);
    }

    /**
     * 修改系统用户app授权
     *
     * @param sysUserApp 系统用户app授权
     * @return 结果
     */
    @Override
    public int update(SysUserApp sysUserApp)
    {
        sysUserApp.setUpdateTime(DateUtils.getNowDate());
        return sysUserAppMapper.updateById(sysUserApp);
    }

    /**
     * 批量删除系统用户app授权
     *
     * @param ids 需要删除的系统用户app授权主键
     * @return 结果
     */
    @Override
    public int deleteByIds(List<Long> ids)
    {
        return sysUserAppMapper.deleteBatchIds(ids);
    }

    /**
     * 删除系统用户app授权信息
     *
     * @param id 系统用户app授权主键
     * @return 结果
     */
    @Override
    public int deleteById(Long id)
    {
        return sysUserAppMapper.deleteById(id);
    }
}
