package tv.shorthub.system.service;

import tv.shorthub.system.mapper.AppLoginRecordsMapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import tv.shorthub.system.domain.AppLoginRecords;
import tv.shorthub.common.core.service.IBaseService;

/**
 * 登录历史Service接口
 *
 * <AUTHOR>
 * @date 2025-05-13
 */
public interface IAppLoginRecordsService extends IBaseService<AppLoginRecords>
{
    /**
     * 查询登录历史
     *
     * @param id 登录历史主键
     * @return 登录历史
     */
    public AppLoginRecords getById(Long id);

    /**
     * 查询登录历史数据汇总
     *
     * @param query 登录历史
     * @return 登录历史数据汇总
     */
    public AppLoginRecords getSummary(AppLoginRecords query);

    /**
     * 查询登录历史列表
     *
     * @param query 登录历史
     * @return 登录历史集合
     */
    public List<AppLoginRecords> selectList(AppLoginRecords query);

    /**
     * 新增登录历史
     *
     * @param appLoginRecords 登录历史
     * @return 结果
     */
    public int insert(AppLoginRecords appLoginRecords);

    /**
     * 修改登录历史
     *
     * @param appLoginRecords 登录历史
     * @return 结果
     */
    public int update(AppLoginRecords appLoginRecords);

    /**
     * 批量删除登录历史
     *
     * @param ids 需要删除的登录历史主键集合
     * @return 结果
     */
    public int deleteByIds(List<Long> ids);

    /**
     * 删除登录历史信息
     *
     * @param id 登录历史主键
     * @return 结果
     */
    public int deleteById(Long id);


    /**
     * 查询自定义分析数据
     *
     * @param query 登录历史
     * @return 登录历史集合
     */
    public List<AppLoginRecords> summary(SummaryRequest query);

    AppLoginRecords allSummary(SummaryRequest query);

    AppLoginRecordsMapper getMapper();
}
