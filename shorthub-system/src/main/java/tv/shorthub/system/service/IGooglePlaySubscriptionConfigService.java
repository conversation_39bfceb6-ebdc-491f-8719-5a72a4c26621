package tv.shorthub.system.service;

import tv.shorthub.system.mapper.GooglePlaySubscriptionConfigMapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import tv.shorthub.system.domain.GooglePlaySubscriptionConfig;
import tv.shorthub.common.core.service.IBaseService;

/**
 * Google Play订阅产品配置Service接口
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
public interface IGooglePlaySubscriptionConfigService extends IBaseService<GooglePlaySubscriptionConfig>
{
    /**
     * 查询Google Play订阅产品配置
     *
     * @param id Google Play订阅产品配置主键
     * @return Google Play订阅产品配置
     */
    public GooglePlaySubscriptionConfig getById(Long id);

    /**
     * 查询Google Play订阅产品配置数据汇总
     *
     * @param query Google Play订阅产品配置
     * @return Google Play订阅产品配置数据汇总
     */
    public GooglePlaySubscriptionConfig getSummary(GooglePlaySubscriptionConfig query);

    /**
     * 查询Google Play订阅产品配置列表
     *
     * @param query Google Play订阅产品配置
     * @return Google Play订阅产品配置集合
     */
    public List<GooglePlaySubscriptionConfig> selectList(GooglePlaySubscriptionConfig query);

    /**
     * 新增Google Play订阅产品配置
     *
     * @param googlePlaySubscriptionConfig Google Play订阅产品配置
     * @return 结果
     */
    public int insert(GooglePlaySubscriptionConfig googlePlaySubscriptionConfig);

    /**
     * 修改Google Play订阅产品配置
     *
     * @param googlePlaySubscriptionConfig Google Play订阅产品配置
     * @return 结果
     */
    public int update(GooglePlaySubscriptionConfig googlePlaySubscriptionConfig);

    /**
     * 批量删除Google Play订阅产品配置
     *
     * @param ids 需要删除的Google Play订阅产品配置主键集合
     * @return 结果
     */
    public int deleteByIds(List<Long> ids);

    /**
     * 删除Google Play订阅产品配置信息
     *
     * @param id Google Play订阅产品配置主键
     * @return 结果
     */
    public int deleteById(Long id);


    /**
     * 查询自定义分析数据
     *
     * @param query Google Play订阅产品配置
     * @return Google Play订阅产品配置集合
     */
    public List<GooglePlaySubscriptionConfig> summary(SummaryRequest query);

    GooglePlaySubscriptionConfig allSummary(SummaryRequest query);

    GooglePlaySubscriptionConfigMapper getMapper();
}
