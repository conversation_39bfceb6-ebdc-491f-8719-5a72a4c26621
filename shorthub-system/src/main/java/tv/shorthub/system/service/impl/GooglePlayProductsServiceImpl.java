package tv.shorthub.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import tv.shorthub.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tv.shorthub.system.mapper.GooglePlayProductsMapper;
import tv.shorthub.system.domain.GooglePlayProducts;
import tv.shorthub.system.service.IGooglePlayProductsService;
import tv.shorthub.common.core.service.BaseService;

/**
 * Google Play产品基础信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
@Service
public class GooglePlayProductsServiceImpl extends BaseService<GooglePlayProducts> implements IGooglePlayProductsService
{
    @Autowired
    private GooglePlayProductsMapper googlePlayProductsMapper;

    @Override
    public GooglePlayProductsMapper getMapper() {
        return googlePlayProductsMapper;
    }

    /**
     * 查询Google Play产品基础信息
     *
     * @param id Google Play产品基础信息主键
     * @return Google Play产品基础信息
     */
    @Override
    public GooglePlayProducts getById(Long id)
    {
        return googlePlayProductsMapper.selectById(id);
    }

    /**
     * 查询Google Play产品基础信息列表
     *
     * @param query Google Play产品基础信息
     * @return Google Play产品基础信息
     */
    @Override
    public List<GooglePlayProducts> selectList(GooglePlayProducts query)
    {
        return googlePlayProductsMapper.selectList(new QueryWrapper<>(query));
    }


    /**
     * 查询Google Play产品基础信息数据汇总
     *
     * @param query Google Play产品基础信息
     * @return Google Play产品基础信息
     */
    @Override
    public GooglePlayProducts getSummary(GooglePlayProducts query)
    {
        return googlePlayProductsMapper.getSummary(query);
    }



    /**
     * 查询自定义分析数据
     *
     * @param query Google Play产品基础信息
     * @return Google Play产品基础信息
     */
    @Override
    public List<GooglePlayProducts> summary(SummaryRequest query)
    {
        return googlePlayProductsMapper.summary(query);
    }

    @Override
    public GooglePlayProducts allSummary(SummaryRequest query)
    {
        return googlePlayProductsMapper.allSummary(query);
    }

    /**
     * 新增Google Play产品基础信息
     *
     * @param googlePlayProducts Google Play产品基础信息
     * @return 结果
     */
    @Override
    public int insert(GooglePlayProducts googlePlayProducts)
    {
        googlePlayProducts.setCreateTime(DateUtils.getNowDate());
        return googlePlayProductsMapper.insert(googlePlayProducts);
    }

    /**
     * 修改Google Play产品基础信息
     *
     * @param googlePlayProducts Google Play产品基础信息
     * @return 结果
     */
    @Override
    public int update(GooglePlayProducts googlePlayProducts)
    {
        googlePlayProducts.setUpdateTime(DateUtils.getNowDate());
        return googlePlayProductsMapper.updateById(googlePlayProducts);
    }

    /**
     * 批量删除Google Play产品基础信息
     *
     * @param ids 需要删除的Google Play产品基础信息主键
     * @return 结果
     */
    @Override
    public int deleteByIds(List<Long> ids)
    {
        return googlePlayProductsMapper.deleteBatchIds(ids);
    }

    /**
     * 删除Google Play产品基础信息信息
     *
     * @param id Google Play产品基础信息主键
     * @return 结果
     */
    @Override
    public int deleteById(Long id)
    {
        return googlePlayProductsMapper.deleteById(id);
    }
}
