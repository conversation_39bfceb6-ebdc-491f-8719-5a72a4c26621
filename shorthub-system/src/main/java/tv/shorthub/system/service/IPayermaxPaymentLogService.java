package tv.shorthub.system.service;

import com.alibaba.fastjson2.JSONObject;
import tv.shorthub.system.mapper.PayermaxPaymentLogMapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import tv.shorthub.system.domain.PayermaxPaymentLog;
import tv.shorthub.common.core.service.IBaseService;

/**
 * payermax支付日志Service接口
 *
 * <AUTHOR>
 * @date 2025-06-16
 */
public interface IPayermaxPaymentLogService extends IBaseService<PayermaxPaymentLog>
{
    JSONObject processRefund(String orderNo, String reason);

    JSONObject updateOrderStatus(String orderNo);

    /**
     * 查询payermax支付日志
     *
     * @param id payermax支付日志主键
     * @return payermax支付日志
     */
    public PayermaxPaymentLog getById(Long id);

    /**
     * 查询payermax支付日志数据汇总
     *
     * @param query payermax支付日志
     * @return payermax支付日志数据汇总
     */
    public PayermaxPaymentLog getSummary(PayermaxPaymentLog query);

    /**
     * 查询payermax支付日志列表
     *
     * @param query payermax支付日志
     * @return payermax支付日志集合
     */
    public List<PayermaxPaymentLog> selectList(PayermaxPaymentLog query);

    /**
     * 新增payermax支付日志
     *
     * @param payermaxPaymentLog payermax支付日志
     * @return 结果
     */
    public int insert(PayermaxPaymentLog payermaxPaymentLog);

    /**
     * 修改payermax支付日志
     *
     * @param payermaxPaymentLog payermax支付日志
     * @return 结果
     */
    public int update(PayermaxPaymentLog payermaxPaymentLog);

    /**
     * 批量删除payermax支付日志
     *
     * @param ids 需要删除的payermax支付日志主键集合
     * @return 结果
     */
    public int deleteByIds(List<Long> ids);

    /**
     * 删除payermax支付日志信息
     *
     * @param id payermax支付日志主键
     * @return 结果
     */
    public int deleteById(Long id);

    String getPayermaxUserId(String userId);


    /**
     * 查询自定义分析数据
     *
     * @param query payermax支付日志
     * @return payermax支付日志集合
     */
    public List<PayermaxPaymentLog> summary(SummaryRequest query);

    PayermaxPaymentLog allSummary(SummaryRequest query);

    PayermaxPaymentLogMapper getMapper();

    JSONObject processUnsubscribe(String orderNo, String unsubscribe);
}
