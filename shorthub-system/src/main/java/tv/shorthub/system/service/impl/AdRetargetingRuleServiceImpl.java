package tv.shorthub.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import tv.shorthub.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tv.shorthub.system.mapper.AdRetargetingRuleMapper;
import tv.shorthub.system.domain.AdRetargetingRule;
import tv.shorthub.system.service.IAdRetargetingRuleService;
import tv.shorthub.common.core.service.BaseService;

/**
 * 回传规则Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Service
public class AdRetargetingRuleServiceImpl extends BaseService<AdRetargetingRule> implements IAdRetargetingRuleService
{
    @Autowired
    private AdRetargetingRuleMapper adRetargetingRuleMapper;

    @Override
    public AdRetargetingRuleMapper getMapper() {
        return adRetargetingRuleMapper;
    }

    /**
     * 查询回传规则
     *
     * @param id 回传规则主键
     * @return 回传规则
     */
    @Override
    public AdRetargetingRule getById(Long id)
    {
        return adRetargetingRuleMapper.selectById(id);
    }

    /**
     * 查询回传规则列表
     *
     * @param query 回传规则
     * @return 回传规则
     */
    @Override
    public List<AdRetargetingRule> selectList(AdRetargetingRule query)
    {
        return adRetargetingRuleMapper.selectList(new QueryWrapper<>(query));
    }


    /**
     * 查询回传规则数据汇总
     *
     * @param query 回传规则
     * @return 回传规则
     */
    @Override
    public AdRetargetingRule getSummary(AdRetargetingRule query)
    {
        return adRetargetingRuleMapper.getSummary(query);
    }



    /**
     * 查询自定义分析数据
     *
     * @param query 回传规则
     * @return 回传规则
     */
    @Override
    public List<AdRetargetingRule> summary(SummaryRequest query)
    {
        return adRetargetingRuleMapper.summary(query);
    }

    @Override
    public AdRetargetingRule allSummary(SummaryRequest query)
    {
        return adRetargetingRuleMapper.allSummary(query);
    }

    /**
     * 新增回传规则
     *
     * @param adRetargetingRule 回传规则
     * @return 结果
     */
    @Override
    public int insert(AdRetargetingRule adRetargetingRule)
    {
        adRetargetingRule.setCreateTime(DateUtils.getNowDate());
        return adRetargetingRuleMapper.insert(adRetargetingRule);
    }

    /**
     * 修改回传规则
     *
     * @param adRetargetingRule 回传规则
     * @return 结果
     */
    @Override
    public int update(AdRetargetingRule adRetargetingRule)
    {
        adRetargetingRule.setUpdateTime(DateUtils.getNowDate());
        return adRetargetingRuleMapper.updateById(adRetargetingRule);
    }

    /**
     * 批量删除回传规则
     *
     * @param ids 需要删除的回传规则主键
     * @return 结果
     */
    @Override
    public int deleteByIds(List<Long> ids)
    {
        return adRetargetingRuleMapper.deleteBatchIds(ids);
    }

    /**
     * 删除回传规则信息
     *
     * @param id 回传规则主键
     * @return 结果
     */
    @Override
    public int deleteById(Long id)
    {
        return adRetargetingRuleMapper.deleteById(id);
    }
}
