package tv.shorthub.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import tv.shorthub.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tv.shorthub.system.mapper.AirwallexWebhookLogMapper;
import tv.shorthub.system.domain.AirwallexWebhookLog;
import tv.shorthub.system.service.IAirwallexWebhookLogService;
import tv.shorthub.common.core.service.BaseService;

/**
 * webhook事件日志Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-19
 */
@Service
public class AirwallexWebhookLogServiceImpl extends BaseService<AirwallexWebhookLog> implements IAirwallexWebhookLogService
{
    @Autowired
    private AirwallexWebhookLogMapper airwallexWebhookLogMapper;

    @Override
    public AirwallexWebhookLogMapper getMapper() {
        return airwallexWebhookLogMapper;
    }

    /**
     * 查询webhook事件日志
     *
     * @param id webhook事件日志主键
     * @return webhook事件日志
     */
    @Override
    public AirwallexWebhookLog getById(Long id)
    {
        return airwallexWebhookLogMapper.selectById(id);
    }

    /**
     * 查询webhook事件日志列表
     *
     * @param query webhook事件日志
     * @return webhook事件日志
     */
    @Override
    public List<AirwallexWebhookLog> selectList(AirwallexWebhookLog query)
    {
        return airwallexWebhookLogMapper.selectList(new QueryWrapper<>(query));
    }


    /**
     * 查询webhook事件日志数据汇总
     *
     * @param query webhook事件日志
     * @return webhook事件日志
     */
    @Override
    public AirwallexWebhookLog getSummary(AirwallexWebhookLog query)
    {
        return airwallexWebhookLogMapper.getSummary(query);
    }



    /**
     * 查询自定义分析数据
     *
     * @param query webhook事件日志
     * @return webhook事件日志
     */
    @Override
    public List<AirwallexWebhookLog> summary(SummaryRequest query)
    {
        return airwallexWebhookLogMapper.summary(query);
    }

    @Override
    public AirwallexWebhookLog allSummary(SummaryRequest query)
    {
        return airwallexWebhookLogMapper.allSummary(query);
    }

    /**
     * 新增webhook事件日志
     *
     * @param airwallexWebhookLog webhook事件日志
     * @return 结果
     */
    @Override
    public int insert(AirwallexWebhookLog airwallexWebhookLog)
    {
        airwallexWebhookLog.setCreateTime(DateUtils.getNowDate());
        return airwallexWebhookLogMapper.insert(airwallexWebhookLog);
    }

    /**
     * 修改webhook事件日志
     *
     * @param airwallexWebhookLog webhook事件日志
     * @return 结果
     */
    @Override
    public int update(AirwallexWebhookLog airwallexWebhookLog)
    {
        airwallexWebhookLog.setUpdateTime(DateUtils.getNowDate());
        return airwallexWebhookLogMapper.updateById(airwallexWebhookLog);
    }

    /**
     * 批量删除webhook事件日志
     *
     * @param ids 需要删除的webhook事件日志主键
     * @return 结果
     */
    @Override
    public int deleteByIds(List<Long> ids)
    {
        return airwallexWebhookLogMapper.deleteBatchIds(ids);
    }

    /**
     * 删除webhook事件日志信息
     *
     * @param id webhook事件日志主键
     * @return 结果
     */
    @Override
    public int deleteById(Long id)
    {
        return airwallexWebhookLogMapper.deleteById(id);
    }
}
