package tv.shorthub.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import tv.shorthub.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tv.shorthub.system.mapper.PaypalPaymentSubscriptionMapper;
import tv.shorthub.system.domain.PaypalPaymentSubscription;
import tv.shorthub.system.service.IPaypalPaymentSubscriptionService;
import tv.shorthub.common.core.service.BaseService;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import java.time.Instant;
import java.time.format.DateTimeParseException;
import java.util.Date;

/**
 * paypal订阅履行Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-06
 */
@Service
@Slf4j
public class PaypalPaymentSubscriptionServiceImpl extends BaseService<PaypalPaymentSubscription> implements IPaypalPaymentSubscriptionService
{
    @Autowired
    private PaypalPaymentSubscriptionMapper paypalPaymentSubscriptionMapper;

    @Override
    public PaypalPaymentSubscriptionMapper getMapper() {
        return paypalPaymentSubscriptionMapper;
    }

    /**
     * 查询paypal订阅履行
     *
     * @param id paypal订阅履行主键
     * @return paypal订阅履行
     */
    @Override
    public PaypalPaymentSubscription getById(Long id)
    {
        return paypalPaymentSubscriptionMapper.selectById(id);
    }

    /**
     * 查询paypal订阅履行列表
     *
     * @param query paypal订阅履行
     * @return paypal订阅履行
     */
    @Override
    public List<PaypalPaymentSubscription> selectList(PaypalPaymentSubscription query)
    {
        return paypalPaymentSubscriptionMapper.selectList(new QueryWrapper<>(query));
    }


    /**
     * 查询paypal订阅履行数据汇总
     *
     * @param query paypal订阅履行
     * @return paypal订阅履行
     */
    @Override
    public PaypalPaymentSubscription getSummary(PaypalPaymentSubscription query)
    {
        return paypalPaymentSubscriptionMapper.getSummary(query);
    }



    /**
     * 查询自定义分析数据
     *
     * @param query paypal订阅履行
     * @return paypal订阅履行
     */
    @Override
    public List<PaypalPaymentSubscription> summary(SummaryRequest query)
    {
        return paypalPaymentSubscriptionMapper.summary(query);
    }

    @Override
    public PaypalPaymentSubscription allSummary(SummaryRequest query)
    {
        return paypalPaymentSubscriptionMapper.allSummary(query);
    }

    /**
     * 新增paypal订阅履行
     *
     * @param paypalPaymentSubscription paypal订阅履行
     * @return 结果
     */
    @Override
    public int insert(PaypalPaymentSubscription paypalPaymentSubscription)
    {
        paypalPaymentSubscription.setCreateTime(DateUtils.getNowDate());
        return paypalPaymentSubscriptionMapper.insert(paypalPaymentSubscription);
    }

    /**
     * 修改paypal订阅履行
     *
     * @param paypalPaymentSubscription paypal订阅履行
     * @return 结果
     */
    @Override
    public int update(PaypalPaymentSubscription paypalPaymentSubscription)
    {
        paypalPaymentSubscription.setUpdateTime(DateUtils.getNowDate());
        return paypalPaymentSubscriptionMapper.updateById(paypalPaymentSubscription);
    }

    /**
     * 批量删除paypal订阅履行
     *
     * @param ids 需要删除的paypal订阅履行主键
     * @return 结果
     */
    @Override
    public int deleteByIds(List<Long> ids)
    {
        return paypalPaymentSubscriptionMapper.deleteBatchIds(ids);
    }

    /**
     * 删除paypal订阅履行信息
     *
     * @param id paypal订阅履行主键
     * @return 结果
     */
    @Override
    public int deleteById(Long id)
    {
        return paypalPaymentSubscriptionMapper.deleteById(id);
    }
}
