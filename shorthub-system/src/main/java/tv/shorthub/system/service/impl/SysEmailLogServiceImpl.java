package tv.shorthub.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import tv.shorthub.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tv.shorthub.system.mapper.SysEmailLogMapper;
import tv.shorthub.system.domain.SysEmailLog;
import tv.shorthub.system.service.ISysEmailLogService;
import tv.shorthub.common.core.service.BaseService;

/**
 * 邮件发送日志Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-09
 */
@Service
public class SysEmailLogServiceImpl extends BaseService<SysEmailLog> implements ISysEmailLogService
{
    @Autowired
    private SysEmailLogMapper sysEmailLogMapper;

    @Override
    public SysEmailLogMapper getMapper() {
        return sysEmailLogMapper;
    }

    /**
     * 查询邮件发送日志
     *
     * @param id 邮件发送日志主键
     * @return 邮件发送日志
     */
    @Override
    public SysEmailLog getById(String id)
    {
        return sysEmailLogMapper.selectById(id);
    }

    /**
     * 查询邮件发送日志列表
     *
     * @param query 邮件发送日志
     * @return 邮件发送日志
     */
    @Override
    public List<SysEmailLog> selectList(SysEmailLog query)
    {
        return sysEmailLogMapper.selectList(new QueryWrapper<>(query));
    }


    /**
     * 查询邮件发送日志数据汇总
     *
     * @param query 邮件发送日志
     * @return 邮件发送日志
     */
    @Override
    public SysEmailLog getSummary(SysEmailLog query)
    {
        return sysEmailLogMapper.getSummary(query);
    }



    /**
     * 查询自定义分析数据
     *
     * @param query 邮件发送日志
     * @return 邮件发送日志
     */
    @Override
    public List<SysEmailLog> summary(SummaryRequest query)
    {
        return sysEmailLogMapper.summary(query);
    }

    @Override
    public SysEmailLog allSummary(SummaryRequest query)
    {
        return sysEmailLogMapper.allSummary(query);
    }

    /**
     * 新增邮件发送日志
     *
     * @param sysEmailLog 邮件发送日志
     * @return 结果
     */
    @Override
    public int insert(SysEmailLog sysEmailLog)
    {
        sysEmailLog.setCreateTime(DateUtils.getNowDate());
        return sysEmailLogMapper.insert(sysEmailLog);
    }

    /**
     * 修改邮件发送日志
     *
     * @param sysEmailLog 邮件发送日志
     * @return 结果
     */
    @Override
    public int update(SysEmailLog sysEmailLog)
    {
        sysEmailLog.setUpdateTime(DateUtils.getNowDate());
        return sysEmailLogMapper.updateById(sysEmailLog);
    }

    /**
     * 批量删除邮件发送日志
     *
     * @param ids 需要删除的邮件发送日志主键
     * @return 结果
     */
    @Override
    public int deleteByIds(List<String> ids)
    {
        return sysEmailLogMapper.deleteBatchIds(ids);
    }

    /**
     * 删除邮件发送日志信息
     *
     * @param id 邮件发送日志主键
     * @return 结果
     */
    @Override
    public int deleteById(String id)
    {
        return sysEmailLogMapper.deleteById(id);
    }

    @Override
    public boolean hasSent(String orderId, String toEmail, String subject) {
        QueryWrapper<SysEmailLog> query = new QueryWrapper<>();
        query.eq("order_id", orderId)
             .eq("to_email", toEmail)
             .eq("subject", subject)
             .eq("status", "0"); // 只查成功的
        return sysEmailLogMapper.selectCount(query) > 0;
    }
}
