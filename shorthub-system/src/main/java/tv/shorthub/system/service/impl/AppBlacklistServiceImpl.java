package tv.shorthub.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import tv.shorthub.common.core.domain.SummaryRequest;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tv.shorthub.common.core.redis.RedisCache;
import tv.shorthub.system.mapper.AppBlacklistMapper;
import tv.shorthub.system.domain.AppBlacklist;
import tv.shorthub.system.service.IAppBlacklistService;
import tv.shorthub.common.core.service.BaseService;

/**
 * 黑名单Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-06
 */
@Service
@Slf4j
public class AppBlacklistServiceImpl extends BaseService<AppBlacklist> implements IAppBlacklistService
{
    @Autowired
    private AppBlacklistMapper appBlacklistMapper;

    private Set<String> blacklistedDeviceIds = new HashSet<>();
    private Set<String> blacklistedUserIds = new HashSet<>();
    private Set<String> blacklistedIps = new HashSet<>();
    private Set<String> blacklistedEmails = new HashSet<>();

    @Autowired
    RedisCache redisCache;


    @Override
    public AppBlacklistMapper getMapper() {
        return appBlacklistMapper;
    }

    /**
     * 查询黑名单
     *
     * @param id 黑名单主键
     * @return 黑名单
     */
    @Override
    public AppBlacklist getById(Long id)
    {
        return appBlacklistMapper.selectById(id);
    }

    /**
     * 查询黑名单列表
     *
     * @param query 黑名单
     * @return 黑名单
     */
    @Override
    public List<AppBlacklist> selectList(AppBlacklist query)
    {
        return appBlacklistMapper.selectList(new QueryWrapper<>(query));
    }


    /**
     * 查询黑名单数据汇总
     *
     * @param query 黑名单
     * @return 黑名单
     */
    @Override
    public AppBlacklist getSummary(AppBlacklist query)
    {
        return appBlacklistMapper.getSummary(query);
    }



    /**
     * 查询自定义分析数据
     *
     * @param query 黑名单
     * @return 黑名单
     */
    @Override
    public List<AppBlacklist> summary(SummaryRequest query)
    {
        return appBlacklistMapper.summary(query);
    }

    @Override
    public AppBlacklist allSummary(SummaryRequest query)
    {
        return appBlacklistMapper.allSummary(query);
    }

    /**
     * 新增黑名单
     *
     * @param appBlacklist 黑名单
     * @return 结果
     */
    @Override
    public int insert(AppBlacklist appBlacklist)
    {
        return appBlacklistMapper.insert(appBlacklist);
    }

    /**
     * 修改黑名单
     *
     * @param appBlacklist 黑名单
     * @return 结果
     */
    @Override
    public int update(AppBlacklist appBlacklist)
    {
        return appBlacklistMapper.updateById(appBlacklist);
    }

    /**
     * 批量删除黑名单
     *
     * @param ids 需要删除的黑名单主键
     * @return 结果
     */
    @Override
    public int deleteByIds(List<Long> ids)
    {
        return appBlacklistMapper.deleteBatchIds(ids);
    }

    /**
     * 删除黑名单信息
     *
     * @param id 黑名单主键
     * @return 结果
     */
    @Override
    public int deleteById(Long id)
    {
        return appBlacklistMapper.deleteById(id);
    }


    @Override
    public void refresh() {
        AppBlacklist query = new AppBlacklist();
        query.setStatus(true);
        List<AppBlacklist> blacklists = selectList(query);
        // 转为 Map<String, Set<String>> 结构，key为type，value为value的集合
        Map<String, Set<String>> blacklistMap = blacklists.stream()
                .collect(Collectors.groupingBy(
                        AppBlacklist::getType,
                        Collectors.mapping(AppBlacklist::getValue, Collectors.toSet())
                ));
        blacklistedDeviceIds = blacklistMap.get("device_id");
        blacklistedUserIds = blacklistMap.get("user_id");
        blacklistedIps = blacklistMap.get("ip");
        blacklistedEmails = blacklistMap.get("email");

        log.info("刷新黑名单成功: size {}", blacklists.size());
    }



    /**
     * 检查是否被黑名单
     * @param deviceId 设备ID
     * @param userId 用户ID
     * @param ip IP地址
     * @param email 邮箱地址
     * @return 如果任何一项在黑名单中返回true，否则返回false
     */
    @Override
    public boolean isBlacklisted(String deviceId, String userId, String ip, String email) {
        return isDeviceBlacklisted(deviceId) ||
                isUserBlacklisted(userId) ||
                isIpBlacklisted(ip) ||
                isEmailBlacklisted(email);
    }

    /**
     * 检查设备ID是否在黑名单中
     * @param deviceId 设备ID
     * @return 在黑名单中返回true，否则返回false
     */
    public boolean isDeviceBlacklisted(String deviceId) {
        if (deviceId == null || deviceId.trim().isEmpty()) {
            return false;
        }
        return null != blacklistedDeviceIds && blacklistedDeviceIds.contains(deviceId.trim());
    }

    /**
     * 检查用户ID是否在黑名单中
     * @param userId 用户ID
     * @return 在黑名单中返回true，否则返回false
     */
    public boolean isUserBlacklisted(String userId) {
        if (userId == null || userId.trim().isEmpty()) {
            return false;
        }
        return null != blacklistedUserIds && blacklistedUserIds.contains(userId.trim());
    }

    /**
     * 检查IP地址是否在黑名单中
     * @param ip IP地址
     * @return 在黑名单中返回true，否则返回false
     */
    public boolean isIpBlacklisted(String ip) {
        if (ip == null || ip.trim().isEmpty()) {
            return false;
        }
        return null != blacklistedIps && blacklistedIps.contains(ip.trim());
    }

    /**
     * 检查邮箱是否在黑名单中
     * @param email 邮箱地址
     * @return 在黑名单中返回true，否则返回false
     */
    public boolean isEmailBlacklisted(String email) {
        if (email == null || email.trim().isEmpty()) {
            return false;
        }
        return null != blacklistedEmails && blacklistedEmails.contains(email.trim().toLowerCase());
    }

}
