package tv.shorthub.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.Date;
import java.util.List;
import tv.shorthub.common.utils.DateUtils;
import tv.shorthub.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tv.shorthub.system.mapper.StatisticsHourlySubscriptionStatsMapper;
import tv.shorthub.system.domain.StatisticsHourlySubscriptionStats;
import tv.shorthub.system.service.IStatisticsHourlySubscriptionStatsService;
import tv.shorthub.common.core.service.BaseService;
import tv.shorthub.common.utils.SecurityUtils;
import org.springframework.util.CollectionUtils;
import java.util.stream.Collectors;
import java.math.BigDecimal;
import org.springframework.beans.BeanUtils;
import tv.shorthub.system.dto.SubscriptionSummaryDto;
import tv.shorthub.system.utils.StatisticsPermissionUtils;

/**
 * 每小时订单与订阅统计Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-29
 */
@Service
public class StatisticsHourlySubscriptionStatsServiceImpl extends BaseService<StatisticsHourlySubscriptionStats> implements IStatisticsHourlySubscriptionStatsService
{
    @Autowired
    private StatisticsHourlySubscriptionStatsMapper statisticsHourlySubscriptionStatsMapper;

    @Autowired
    private StatisticsPermissionUtils permissionUtils;

    @Override
    public StatisticsHourlySubscriptionStatsMapper getMapper() {
        return statisticsHourlySubscriptionStatsMapper;
    }

    /**
     * 查询每小时订单与订阅统计
     *
     * @param id 每小时订单与订阅统计主键
     * @return 每小时订单与订阅统计
     */
    @Override
    public StatisticsHourlySubscriptionStats getById(Long id)
    {
        return statisticsHourlySubscriptionStatsMapper.selectById(id);
    }

    /**
     * 查询每小时订单与订阅统计列表
     *
     * @param query 每小时订单与订阅统计
     * @return 每小时订单与订阅统计
     */
    @Override
    public List<StatisticsHourlySubscriptionStats> selectList(StatisticsHourlySubscriptionStats query)
    {
        QueryWrapper<StatisticsHourlySubscriptionStats> wrapper = new QueryWrapper<>(query);

        // 按时间倒序排列，最新数据在前
        wrapper.orderByDesc("stat_hour");

        // 如果是投手用户，需要根据tfid权限过滤
        permissionUtils.filterByDeliverPermission(wrapper);

        return statisticsHourlySubscriptionStatsMapper.selectList(wrapper);
    }


    /**
     * 查询每小时订单与订阅统计数据汇总
     *
     * @param query 每小时订单与订阅统计
     * @return 每小时订单与订阅统计
     */
    @Override
    public StatisticsHourlySubscriptionStats getSummary(StatisticsHourlySubscriptionStats query)
    {
        // 如果是业务主账号（但不是系统管理员），强制使用自己的 appid
        if (SecurityUtils.isBusinessAdmin() && !SecurityUtils.isSystemAdmin()) {
            query.setAppid(SecurityUtils.getAppid());
        }
        // 系统管理员可以查看任何 appid 的数据
        return statisticsHourlySubscriptionStatsMapper.getSummary(query);
    }



    /**
     * 查询自定义分析数据
     *
     * @param query 每小时订单与订阅统计
     * @return 每小时订单与订阅统计
     */
    @Override
    public List<StatisticsHourlySubscriptionStats> summary(SummaryRequest query)
    {
        return statisticsHourlySubscriptionStatsMapper.summary(query);
    }

    @Override
    public StatisticsHourlySubscriptionStats allSummary(SummaryRequest query)
    {
        return statisticsHourlySubscriptionStatsMapper.allSummary(query);
    }

    /**
     * 新增每小时订单与订阅统计
     *
     * @param statisticsHourlySubscriptionStats 每小时订单与订阅统计
     * @return 结果
     */
    @Override
    public int insert(StatisticsHourlySubscriptionStats statisticsHourlySubscriptionStats)
    {
        statisticsHourlySubscriptionStats.setCreateTime(DateUtils.getNowDate());
        return statisticsHourlySubscriptionStatsMapper.insert(statisticsHourlySubscriptionStats);
    }

    /**
     * 修改每小时订单与订阅统计
     *
     * @param statisticsHourlySubscriptionStats 每小时订单与订阅统计
     * @return 结果
     */
    @Override
    public int update(StatisticsHourlySubscriptionStats statisticsHourlySubscriptionStats)
    {
        statisticsHourlySubscriptionStats.setUpdateTime(DateUtils.getNowDate());
        return statisticsHourlySubscriptionStatsMapper.updateById(statisticsHourlySubscriptionStats);
    }

    /**
     * 批量删除每小时订单与订阅统计
     *
     * @param ids 需要删除的每小时订单与订阅统计主键
     * @return 结果
     */
    @Override
    public int deleteByIds(List<Long> ids)
    {
        return statisticsHourlySubscriptionStatsMapper.deleteBatchIds(ids);
    }

    /**
     * 删除每小时订单与订阅统计信息
     *
     * @param id 每小时订单与订阅统计主键
     * @return 结果
     */
    @Override
    public int deleteById(Long id)
    {
        return statisticsHourlySubscriptionStatsMapper.deleteById(id);
    }

    /**
     * 根据时间范围获取订阅统计汇总数据
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param appid 应用ID
     * @param tfid 推广链接ID
     * @return 汇总统计数据
     */
    @Override
    public SubscriptionSummaryDto getRangeSummary(Date startTime, Date endTime, String appid, String tfid)
    {
        // 如果是业务主账号（但不是系统管理员），强制使用自己的 appid
        if (SecurityUtils.isBusinessAdmin() && !SecurityUtils.isSystemAdmin()) {
            appid = SecurityUtils.getAppid();
        }

        // 1. 获取按时间范围和权限过滤后的基本汇总数据
        StatisticsHourlySubscriptionStats summary = statisticsHourlySubscriptionStatsMapper.getRangeSummary(startTime, endTime, appid, tfid);

        // 2. 查询待扣款金额数据
        SubscriptionSummaryDto upcomingPaymentsDto = statisticsHourlySubscriptionStatsMapper.selectUpcomingPayments();

        // 3. 创建最终的DTO并聚合数据
        SubscriptionSummaryDto finalDto = new SubscriptionSummaryDto();
        if (summary != null) {
            BeanUtils.copyProperties(summary, finalDto);
        }

        if (upcomingPaymentsDto != null) {
            finalDto.setUpcomingSevenDayAmount(upcomingPaymentsDto.getUpcomingSevenDayAmount());
            finalDto.setUpcomingThirtyDayAmount(upcomingPaymentsDto.getUpcomingThirtyDayAmount());
        } else {
            finalDto.setUpcomingSevenDayAmount(BigDecimal.ZERO);
            finalDto.setUpcomingThirtyDayAmount(BigDecimal.ZERO);
        }

        return finalDto;
    }

    /**
     * 根据时间范围查询订阅统计列表
     *
     * @param query 查询条件
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计数据列表
     */
    @Override
    public List<StatisticsHourlySubscriptionStats> selectListByTimeRange(StatisticsHourlySubscriptionStats query, Date startTime, Date endTime)
    {
        QueryWrapper<StatisticsHourlySubscriptionStats> wrapper = new QueryWrapper<>(query);
        wrapper.ge("stat_hour", startTime);
        wrapper.lt("stat_hour", endTime);
        wrapper.orderByDesc("stat_hour");

        // 如果是投手用户，需要根据tfid权限过滤
        permissionUtils.filterByDeliverPermission(wrapper);

        return statisticsHourlySubscriptionStatsMapper.selectList(wrapper);
    }


    /**
     * 根据 tfid 列表获取汇总数据
     *
     * @param tfids tfid 列表
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @return 汇总统计数据
     */
    @Override
    public StatisticsHourlySubscriptionStats getSummaryByTfids(List<String> tfids, Date startTime, Date endTime) {
        // 如果是业务主账号（但不是系统管理员），强制使用自己的 appid
        String appid = null;
        if (SecurityUtils.isBusinessAdmin() && !SecurityUtils.isSystemAdmin()) {
            appid = SecurityUtils.getAppid();
        }
        return statisticsHourlySubscriptionStatsMapper.getSummaryByTfids(tfids, startTime, endTime, appid);
    }



    @Override
    public SubscriptionSummaryDto getRangeSummaryByCreator(String creatorName, Date startTime, Date endTime) {
        // 如果是主账号查看自己的数据，且没有指定具体的创建者（或创建者就是自己）
        if (SecurityUtils.isBusinessAdmin() && !SecurityUtils.isSystemAdmin() &&
                (StringUtils.isEmpty(creatorName) || SecurityUtils.getUsername().equals(creatorName))) {
            // 直接调用原有的getRangeSummary方法，会自动处理appid权限
            return getRangeSummary(startTime, endTime, SecurityUtils.getAppid(), null);
        }

        // 其他情况：系统管理员查看指定投手，或主账号查看指定子账号
        List<String> tfids = permissionUtils.getUserTfids(creatorName);

        if (CollectionUtils.isEmpty(tfids)) {
            SubscriptionSummaryDto emptyDto = new SubscriptionSummaryDto();
            emptyDto.setUpcomingSevenDayAmount(BigDecimal.ZERO);
            emptyDto.setUpcomingThirtyDayAmount(BigDecimal.ZERO);
            return emptyDto;
        }

        // 1. 获取基本汇总数据
        StatisticsHourlySubscriptionStats summary = getSummaryByTfids(tfids, startTime, endTime);

        // 2. 查询待扣款金额数据（全局，不按创建者过滤）
        SubscriptionSummaryDto upcomingPaymentsDto = statisticsHourlySubscriptionStatsMapper.selectUpcomingPayments();

        // 3. 创建最终的DTO并聚合数据
        SubscriptionSummaryDto finalDto = new SubscriptionSummaryDto();
        if (summary != null) {
            BeanUtils.copyProperties(summary, finalDto);
        }

        if (upcomingPaymentsDto != null) {
            finalDto.setUpcomingSevenDayAmount(upcomingPaymentsDto.getUpcomingSevenDayAmount());
            finalDto.setUpcomingThirtyDayAmount(upcomingPaymentsDto.getUpcomingThirtyDayAmount());
        } else {
            finalDto.setUpcomingSevenDayAmount(BigDecimal.ZERO);
            finalDto.setUpcomingThirtyDayAmount(BigDecimal.ZERO);
        }

        return finalDto;
    }
}
