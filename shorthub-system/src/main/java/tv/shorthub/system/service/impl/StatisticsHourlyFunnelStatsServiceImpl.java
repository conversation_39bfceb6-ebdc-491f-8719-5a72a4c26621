package tv.shorthub.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.Date;
import java.util.List;
import tv.shorthub.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tv.shorthub.common.utils.SecurityUtils;
import tv.shorthub.system.mapper.StatisticsHourlyFunnelStatsMapper;
import tv.shorthub.system.mapper.AppPromotionMapper;
import tv.shorthub.system.domain.StatisticsHourlyFunnelStats;
import tv.shorthub.system.service.IStatisticsHourlyFunnelStatsService;
import tv.shorthub.common.core.service.BaseService;
import tv.shorthub.system.utils.StatisticsPermissionUtils;

/**
 * 每小时漏斗分析统计Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-07
 */
@Service
public class StatisticsHourlyFunnelStatsServiceImpl extends BaseService<StatisticsHourlyFunnelStats> implements IStatisticsHourlyFunnelStatsService
{
    @Autowired
    private StatisticsHourlyFunnelStatsMapper statisticsHourlyFunnelStatsMapper;
    @Autowired
    private StatisticsPermissionUtils permissionUtils;

    @Autowired
    private AppPromotionMapper appPromotionMapper;

    @Override
    public StatisticsHourlyFunnelStatsMapper getMapper() {
        return statisticsHourlyFunnelStatsMapper;
    }

    /**
     * 查询每小时漏斗分析统计
     *
     * @param id 每小时漏斗分析统计主键
     * @return 每小时漏斗分析统计
     */
    @Override
    public StatisticsHourlyFunnelStats getById(Long id)
    {
        return statisticsHourlyFunnelStatsMapper.selectById(id);
    }

    /**
     * 查询每小时漏斗分析统计列表
     *
     * @param query 每小时漏斗分析统计
     * @return 每小时漏斗分析统计
     */
    @Override
    public List<StatisticsHourlyFunnelStats> selectList(StatisticsHourlyFunnelStats query)
    {

        QueryWrapper<StatisticsHourlyFunnelStats> wrapper = new QueryWrapper<>(query);
        // 按时间倒序排列，最新数据在前
        wrapper.orderByDesc("stat_hour");
        // 权限过滤
        permissionUtils.filterByDeliverPermission(wrapper);
        return statisticsHourlyFunnelStatsMapper.selectList(new QueryWrapper<>(query));
    }


    /**
     * 查询每小时漏斗分析统计数据汇总
     *
     * @param query 每小时漏斗分析统计
     * @return 每小时漏斗分析统计
     */
    @Override
    public StatisticsHourlyFunnelStats getSummary(StatisticsHourlyFunnelStats query)
    {
        return statisticsHourlyFunnelStatsMapper.getSummary(query);
    }



    /**
     * 查询自定义分析数据
     *
     * @param query 每小时漏斗分析统计
     * @return 每小时漏斗分析统计
     */
    @Override
    public List<StatisticsHourlyFunnelStats> summary(SummaryRequest query)
    {
        return statisticsHourlyFunnelStatsMapper.summary(query);
    }

    @Override
    public StatisticsHourlyFunnelStats allSummary(SummaryRequest query)
    {
        return statisticsHourlyFunnelStatsMapper.allSummary(query);
    }

    /**
     * 新增每小时漏斗分析统计
     *
     * @param statisticsHourlyFunnelStats 每小时漏斗分析统计
     * @return 结果
     */
    @Override
    public int insert(StatisticsHourlyFunnelStats statisticsHourlyFunnelStats)
    {
        statisticsHourlyFunnelStats.setCreateTime(DateUtils.getNowDate());
        return statisticsHourlyFunnelStatsMapper.insert(statisticsHourlyFunnelStats);
    }

    /**
     * 修改每小时漏斗分析统计
     *
     * @param statisticsHourlyFunnelStats 每小时漏斗分析统计
     * @return 结果
     */
    @Override
    public int update(StatisticsHourlyFunnelStats statisticsHourlyFunnelStats)
    {
        statisticsHourlyFunnelStats.setUpdateTime(DateUtils.getNowDate());
        return statisticsHourlyFunnelStatsMapper.updateById(statisticsHourlyFunnelStats);
    }

    /**
     * 批量删除每小时漏斗分析统计
     *
     * @param ids 需要删除的每小时漏斗分析统计主键
     * @return 结果
     */
    @Override
    public int deleteByIds(List<Long> ids)
    {
        return statisticsHourlyFunnelStatsMapper.deleteBatchIds(ids);
    }

    /**
     * 删除每小时漏斗分析统计信息
     *
     * @param id 每小时漏斗分析统计主键
     * @return 结果
     */
    @Override
    public int deleteById(Long id)
    {
        return statisticsHourlyFunnelStatsMapper.deleteById(id);
    }

    @Override
    public List<StatisticsHourlyFunnelStats> selectListByTimeRange(StatisticsHourlyFunnelStats query, Date startTime, Date endTime) {
        // 这里可以根据需要实现时间范围查询的逻辑
        // 暂时使用基础查询方法
        return selectList(query);
    }

    @Override
    public StatisticsHourlyFunnelStats getRangeSummary(Date startTime, Date endTime, String appid, String tfid) {
        return statisticsHourlyFunnelStatsMapper.getRangeSummary(startTime, endTime, appid, tfid);
    }

    @Override
    public StatisticsHourlyFunnelStats getSummaryByTfids(List<String> tfids, Date startTime, Date endTime) {
        // 如果是业务主账号（但不是系统管理员），强制使用自己的 appid
        String appid = null;
        if (SecurityUtils.isBusinessAdmin() && !SecurityUtils.isSystemAdmin()) {
            appid = SecurityUtils.getAppid();
        }
        return statisticsHourlyFunnelStatsMapper.getSummaryByTfids(tfids, startTime, endTime, appid);
    }

    @Override
    public StatisticsHourlyFunnelStats getSummaryByCreator(String creator, Date startTime, Date endTime) {
        // 主账号查自己
        if (SecurityUtils.isBusinessAdmin() && !SecurityUtils.isSystemAdmin() &&
            (org.apache.commons.lang3.StringUtils.isEmpty(creator) || SecurityUtils.getUsername().equals(creator))) {
            String appid = SecurityUtils.getAppid();
            return statisticsHourlyFunnelStatsMapper.getRangeSummary(startTime, endTime, appid, null);
        }
        // 查别人（投手/子账号），用 tfid 列表隔离
        java.util.List<String> tfids = appPromotionMapper.selectTfidsByCreator(creator);
        if (tfids == null || tfids.isEmpty()) {
            return new StatisticsHourlyFunnelStats();
        }
        return getSummaryByTfids(tfids, startTime, endTime);
    }
}
