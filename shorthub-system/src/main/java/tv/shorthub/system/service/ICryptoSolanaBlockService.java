package tv.shorthub.system.service;

import tv.shorthub.system.mapper.CryptoSolanaBlockMapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import tv.shorthub.system.domain.CryptoSolanaBlock;
import tv.shorthub.common.core.service.IBaseService;

/**
 * SOLANA区块链区块Service接口
 *
 * <AUTHOR>
 * @date 2025-08-05
 */
public interface ICryptoSolanaBlockService extends IBaseService<CryptoSolanaBlock>
{
    /**
     * 查询SOLANA区块链区块
     *
     * @param id SOLANA区块链区块主键
     * @return SOLANA区块链区块
     */
    public CryptoSolanaBlock getById(Long id);

    /**
     * 查询SOLANA区块链区块数据汇总
     *
     * @param query SOLANA区块链区块
     * @return SOLANA区块链区块数据汇总
     */
    public CryptoSolanaBlock getSummary(CryptoSolanaBlock query);

    /**
     * 查询SOLANA区块链区块列表
     *
     * @param query SOLANA区块链区块
     * @return SOLANA区块链区块集合
     */
    public List<CryptoSolanaBlock> selectList(CryptoSolanaBlock query);

    /**
     * 新增SOLANA区块链区块
     *
     * @param cryptoSolanaBlock SOLANA区块链区块
     * @return 结果
     */
    public int insert(CryptoSolanaBlock cryptoSolanaBlock);

    /**
     * 修改SOLANA区块链区块
     *
     * @param cryptoSolanaBlock SOLANA区块链区块
     * @return 结果
     */
    public int update(CryptoSolanaBlock cryptoSolanaBlock);

    /**
     * 批量删除SOLANA区块链区块
     *
     * @param ids 需要删除的SOLANA区块链区块主键集合
     * @return 结果
     */
    public int deleteByIds(List<Long> ids);

    /**
     * 删除SOLANA区块链区块信息
     *
     * @param id SOLANA区块链区块主键
     * @return 结果
     */
    public int deleteById(Long id);


    /**
     * 查询自定义分析数据
     *
     * @param query SOLANA区块链区块
     * @return SOLANA区块链区块集合
     */
    public List<CryptoSolanaBlock> summary(SummaryRequest query);

    CryptoSolanaBlock allSummary(SummaryRequest query);

    CryptoSolanaBlockMapper getMapper();
}
