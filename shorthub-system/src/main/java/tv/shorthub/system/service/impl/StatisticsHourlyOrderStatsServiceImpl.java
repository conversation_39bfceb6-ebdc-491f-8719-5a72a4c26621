package tv.shorthub.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.Date;
import java.util.List;
import tv.shorthub.common.utils.DateUtils;
import tv.shorthub.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tv.shorthub.system.mapper.StatisticsHourlyOrderStatsMapper;
import tv.shorthub.system.domain.StatisticsHourlyOrderStats;
import tv.shorthub.system.service.IStatisticsHourlyOrderStatsService;
import tv.shorthub.common.core.service.BaseService;
import java.math.BigDecimal;
import tv.shorthub.common.utils.SecurityUtils;
import tv.shorthub.system.mapper.AppPromotionMapper;
import tv.shorthub.system.domain.AppPromotion;
import java.util.stream.Collectors;
import org.springframework.util.CollectionUtils;
import tv.shorthub.system.utils.StatisticsPermissionUtils;

/**
 * 每小时核心订单交易统计Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-27
 */
@Service
public class StatisticsHourlyOrderStatsServiceImpl extends BaseService<StatisticsHourlyOrderStats> implements IStatisticsHourlyOrderStatsService
{
    @Autowired
    private StatisticsHourlyOrderStatsMapper statisticsHourlyOrderStatsMapper;

    @Autowired
    private AppPromotionMapper appPromotionMapper;

    @Autowired
    private StatisticsPermissionUtils permissionUtils;

    @Override
    public StatisticsHourlyOrderStatsMapper getMapper() {
        return statisticsHourlyOrderStatsMapper;
    }

    /**
     * 查询每小时核心订单交易统计
     *
     * @param id 每小时核心订单交易统计主键
     * @return 每小时核心订单交易统计
     */
    @Override
    public StatisticsHourlyOrderStats getById(Long id)
    {
        return statisticsHourlyOrderStatsMapper.selectById(id);
    }

    /**
     * 查询每小时核心订单交易统计列表
     *
     * @param query 每小时核心订单交易统计
     * @return 每小时核心订单交易统计
     */
    @Override
    public List<StatisticsHourlyOrderStats> selectList(StatisticsHourlyOrderStats query)
    {
        QueryWrapper<StatisticsHourlyOrderStats> wrapper = new QueryWrapper<>(query);

        // 按时间倒序排列，最新数据在前
        wrapper.orderByDesc("stat_hour");

        // 判断是否需要过滤投手数据
        permissionUtils.filterByDeliverPermission(wrapper);

        return statisticsHourlyOrderStatsMapper.selectList(wrapper);
    }


    /**
     * 查询每小时核心订单交易统计数据汇总
     *
     * @param query 每小时核心订单交易统计
     * @return 每小时核心订单交易统计
     */
    @Override
    public StatisticsHourlyOrderStats getSummary(StatisticsHourlyOrderStats query)
    {
        // 如果是投手，需要根据权限过滤
        if (permissionUtils.isDeliverOnly()) {
            String currentUsername = SecurityUtils.getUsername();
            List<String> tfids = permissionUtils.getUserTfids(currentUsername);

            if (!CollectionUtils.isEmpty(tfids)) {
                // 使用投手的 tfid 列表查询汇总，传入时间范围
                return statisticsHourlyOrderStatsMapper.getSummaryByTfids(tfids, null, null, null, null);
            } else {
                // 没有推广链接，返回空汇总
                return new StatisticsHourlyOrderStats();
            }
        }

        // 管理员或有更高权限的用户，查询所有数据
        return statisticsHourlyOrderStatsMapper.getSummary(query);
    }



    /**
     * 查询自定义分析数据
     *
     * @param query 每小时核心订单交易统计
     * @return 每小时核心订单交易统计
     */
    @Override
    public List<StatisticsHourlyOrderStats> summary(SummaryRequest query)
    {
        return statisticsHourlyOrderStatsMapper.summary(query);
    }

    @Override
    public StatisticsHourlyOrderStats allSummary(SummaryRequest query)
    {
        return statisticsHourlyOrderStatsMapper.allSummary(query);
    }

    /**
     * 新增每小时核心订单交易统计
     *
     * @param statisticsHourlyOrderStats 每小时核心订单交易统计
     * @return 结果
     */
    @Override
    public int insert(StatisticsHourlyOrderStats statisticsHourlyOrderStats)
    {
        statisticsHourlyOrderStats.setCreateTime(DateUtils.getNowDate());
        return statisticsHourlyOrderStatsMapper.insert(statisticsHourlyOrderStats);
    }

    /**
     * 修改每小时核心订单交易统计
     *
     * @param statisticsHourlyOrderStats 每小时核心订单交易统计
     * @return 结果
     */
    @Override
    public int update(StatisticsHourlyOrderStats statisticsHourlyOrderStats)
    {
        statisticsHourlyOrderStats.setUpdateTime(DateUtils.getNowDate());
        return statisticsHourlyOrderStatsMapper.updateById(statisticsHourlyOrderStats);
    }

    /**
     * 批量删除每小时核心订单交易统计
     *
     * @param ids 需要删除的每小时核心订单交易统计主键
     * @return 结果
     */
    @Override
    public int deleteByIds(List<Long> ids)
    {
        return statisticsHourlyOrderStatsMapper.deleteBatchIds(ids);
    }

    /**
     * 删除每小时核心订单交易统计信息
     *
     * @param id 每小时核心订单交易统计主键
     * @return 结果
     */
    @Override
    public int deleteById(Long id)
    {
        return statisticsHourlyOrderStatsMapper.deleteById(id);
    }

    /**
     * 根据时间范围获取订单统计汇总数据
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param appid 应用ID
     * @param tfid 推广链接ID
     * @param orderChannel 订单支付通道
     * @return 汇总统计数据
     */
    @Override
    public StatisticsHourlyOrderStats getRangeSummary(Date startTime, Date endTime, String appid, String tfid, String orderChannel) {
        // 如果是业务主账号（但不是系统管理员），强制使用自己的 appid
        if (SecurityUtils.isBusinessAdmin() && !SecurityUtils.isSystemAdmin()) {
            appid = SecurityUtils.getAppid();
        }
        // 系统管理员可以查看任何 appid 的数据
        return statisticsHourlyOrderStatsMapper.getRangeSummary(startTime, endTime, appid, tfid, orderChannel);
    }

    /**
     * 根据时间范围查询订单统计列表
     *
     * @param query 查询条件
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计数据列表
     */
    @Override
    public List<StatisticsHourlyOrderStats> selectListByTimeRange(StatisticsHourlyOrderStats query, Date startTime, Date endTime)
    {
        QueryWrapper<StatisticsHourlyOrderStats> wrapper = new QueryWrapper<>(query);
        wrapper.ge("stat_hour", startTime);
        wrapper.lt("stat_hour", endTime);
        wrapper.orderByDesc("stat_hour");

        // 判断是否需要过滤投手数据
        permissionUtils.filterByDeliverPermission(wrapper);

        return statisticsHourlyOrderStatsMapper.selectList(wrapper);
    }



    /**
     * 根据 tfid 列表获取汇总数据
     *
     * @param tfids tfid 列表
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @return 汇总统计数据
     */
    @Override
    public StatisticsHourlyOrderStats getSummaryByTfids(List<String> tfids, Date startTime, Date endTime, String appid, String orderChannel) {
        // 如果是业务主账号（但不是系统管理员），强制使用自己的 appid
        if (SecurityUtils.isBusinessAdmin() && !SecurityUtils.isSystemAdmin()) {
            appid = SecurityUtils.getAppid();
        }
        return statisticsHourlyOrderStatsMapper.getSummaryByTfids(tfids, startTime, endTime, appid, orderChannel);
    }


    @Override
    public StatisticsHourlyOrderStats getSummaryByCreator(String creatorName, Date startTime, Date endTime, String orderChannel) {
        // 如果是主账号查看自己的数据，且没有指定具体的创建者（或创建者就是自己）
        if (SecurityUtils.isBusinessAdmin() && !SecurityUtils.isSystemAdmin() &&
                (StringUtils.isEmpty(creatorName) || SecurityUtils.getUsername().equals(creatorName))) {
            // 主账号查看自己appid下的所有数据，包括自然流量
            StatisticsHourlyOrderStats query = new StatisticsHourlyOrderStats();
            query.setAppid(SecurityUtils.getAppid());

            if (startTime != null && endTime != null) {
                return statisticsHourlyOrderStatsMapper.getRangeSummary(startTime, endTime, SecurityUtils.getAppid(), null, orderChannel);
            } else {
                return statisticsHourlyOrderStatsMapper.getSummary(query);
            }
        }

        // 其他情况：系统管理员查看指定投手，或主账号查看指定子账号
        List<String> tfids = permissionUtils.getUserTfids(creatorName);

        if (CollectionUtils.isEmpty(tfids)) {
            return new StatisticsHourlyOrderStats();
        }

        return getSummaryByTfids(tfids, startTime, endTime, null, orderChannel);
    }
}
