package tv.shorthub.system.service.impl;

import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;

import tv.shorthub.common.core.domain.model.UserApp;
import tv.shorthub.common.exception.ServiceException;
import tv.shorthub.common.utils.DateUtils;
import tv.shorthub.common.utils.SecurityUtils;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tv.shorthub.system.mapper.AppRechargeMapper;
import tv.shorthub.system.domain.AppRecharge;
import tv.shorthub.system.service.IAppRechargeService;
import tv.shorthub.common.core.service.BaseService;

/**
 * 充值方案Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-10
 */
@Service
public class AppRechargeServiceImpl extends BaseService<AppRecharge> implements IAppRechargeService
{
    @Autowired
    private AppRechargeMapper appRechargeMapper;

    @Override
    public AppRechargeMapper getMapper() {
        return appRechargeMapper;
    }

    /**
     * 查询充值方案
     *
     * @param id 充值方案主键
     * @return 充值方案
     */
    @Override
    public AppRecharge getById(Long id)
    {
        return appRechargeMapper.selectById(id);
    }

    /**
     * 查询充值方案列表
     *
     * @param query 充值方案
     * @return 充值方案
     */
    @Override
    public List<AppRecharge> selectList(AppRecharge query)
    {
        QueryWrapper<AppRecharge> queryWrapper = new QueryWrapper<>();
        if (!SecurityUtils.isSystemAdmin() || (query.getParams().containsKey("onlyMe") && query.getParams().get("onlyMe").equals("true"))) {
            if (!SecurityUtils.isBusinessAdmin() || (query.getParams().containsKey("onlyMe") && query.getParams().get("onlyMe").equals("true"))) {
                queryWrapper.eq("create_by", SecurityUtils.getUsername()).or().eq("is_default", 1);
            } else {
                queryWrapper.in("appid", SecurityUtils.getAppList().stream().map(UserApp::getAppid).toList()).or().eq("is_default", 1);
            }
        }
        return appRechargeMapper.selectList(queryWrapper.orderByAsc("sort"));
    }


    /**
     * 查询充值方案数据汇总
     *
     * @param query 充值方案
     * @return 充值方案
     */
    @Override
    public AppRecharge getSummary(AppRecharge query)
    {
        return appRechargeMapper.getSummary(query);
    }



    /**
     * 查询自定义分析数据
     *
     * @param query 充值方案
     * @return 充值方案
     */
    @Override
    public List<AppRecharge> summary(SummaryRequest query)
    {
        return appRechargeMapper.summary(query);
    }

    @Override
    public AppRecharge allSummary(SummaryRequest query)
    {
        return appRechargeMapper.allSummary(query);
    }

    public boolean checkDefault(AppRecharge entity) {
        // 如果设置为默认，先清除同appid下其它默认
        if (entity.getIsDefault() != null && entity.getIsDefault()) {
            if (!SecurityUtils.isSystemAdmin()) {
                throw new ServiceException("您没有权限设置默认方案");
            }
            QueryWrapper<AppRecharge> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("is_default", 1);

            if (entity.getAppid() == null) {
                queryWrapper.isNull("appid");
            } else {
                queryWrapper.eq("appid", entity.getAppid());
            }

            // 如果是更新，排除自己
            if (entity.getId() != null) {
                queryWrapper.ne("id", entity.getId());
                // 查询历史数据，如果修改了默认状态，那么要检查权限
                AppRecharge oldEntity = appRechargeMapper.selectById(entity.getId());
                if (oldEntity != null && oldEntity.getIsDefault() && !entity.getIsDefault()) {
                    if (!SecurityUtils.isSystemAdmin()) {
                        throw new ServiceException("您没有权限取消默认方案");
                    }
                }
            }
            Long count = appRechargeMapper.selectCount(queryWrapper);
            return count <= 0;
        }
        return true;
    }
    /**
     * 新增充值方案
     *
     * @param appRecharge 充值方案
     * @return 结果
     */
    @Override
    public int insert(AppRecharge appRecharge)
    {
        if (!checkDefault(appRecharge)) {
            throw new ServiceException("方案保存失败，只允许设置一个默认方案");
        }
        appRecharge.setAppid(SecurityUtils.getAppid());
        appRecharge.setCreateBy(SecurityUtils.getUsername());
        appRecharge.setTemplateId(IdUtil.getSnowflakeNextIdStr());
        appRecharge.setCreateTime(DateUtils.getNowDate());
        return appRechargeMapper.insert(appRecharge);
    }

    /**
     * 修改充值方案
     *
     * @param appRecharge 充值方案
     * @return 结果
     */
    @Override
    public int update(AppRecharge appRecharge)
    {
        if (!checkDefault(appRecharge)) {
            throw new ServiceException("方案保存失败，只允许设置一个默认方案");
        }
        AppRecharge oldEntity = appRechargeMapper.selectById(appRecharge.getId());
        if (oldEntity != null && oldEntity.getIsDefault() && !SecurityUtils.isSystemAdmin()) {
            throw new ServiceException("您没有权限修改默认方案");
        }
        appRecharge.setUpdateTime(DateUtils.getNowDate());
        return appRechargeMapper.updateById(appRecharge);
    }

    /**
     * 批量删除充值方案
     *
     * @param ids 需要删除的充值方案主键
     * @return 结果
     */
    @Override
    public int deleteByIds(List<Long> ids)
    {
        for (Long id : ids) {
            AppRecharge item = appRechargeMapper.selectById(id);
            if (item != null) {
                if (item.getIsDefault() && !SecurityUtils.isSystemAdmin()) {
                    throw new ServiceException("您没有权限删除默认方案");
                }
                if (!SecurityUtils.getUsername().equals(item.getCreateBy()) && !SecurityUtils.isSystemAdmin()) {
                    throw new ServiceException("您没有权限删除该方案");
                }
            }
        }
        return appRechargeMapper.deleteBatchIds(ids);
    }

    /**
     * 删除充值方案信息
     *
     * @param id 充值方案主键
     * @return 结果
     */
    @Override
    public int deleteById(Long id)
    {
        AppRecharge item = appRechargeMapper.selectById(id);
        if (item != null) {
            if (item.getIsDefault() && !SecurityUtils.isSystemAdmin()) {
                throw new ServiceException("您没有权限删除默认方案");
            }
            if (!SecurityUtils.getUsername().equals(item.getCreateBy()) && !SecurityUtils.isSystemAdmin()) {
                throw new ServiceException("您没有权限删除该方案");
            }
        }
        return appRechargeMapper.deleteById(id);
    }
}
