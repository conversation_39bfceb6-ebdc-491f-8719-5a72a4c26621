package tv.shorthub.system.service;

import tv.shorthub.system.mapper.AppRechargeMapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import tv.shorthub.system.domain.AppRecharge;
import tv.shorthub.common.core.service.IBaseService;

/**
 * 充值方案Service接口
 *
 * <AUTHOR>
 * @date 2025-05-10
 */
public interface IAppRechargeService extends IBaseService<AppRecharge>
{
    /**
     * 查询充值方案
     *
     * @param id 充值方案主键
     * @return 充值方案
     */
    public AppRecharge getById(Long id);

    /**
     * 查询充值方案数据汇总
     *
     * @param query 充值方案
     * @return 充值方案数据汇总
     */
    public AppRecharge getSummary(AppRecharge query);

    /**
     * 查询充值方案列表
     *
     * @param query 充值方案
     * @return 充值方案集合
     */
    public List<AppRecharge> selectList(AppRecharge query);

    /**
     * 新增充值方案
     *
     * @param appRecharge 充值方案
     * @return 结果
     */
    public int insert(AppRecharge appRecharge);

    /**
     * 修改充值方案
     *
     * @param appRecharge 充值方案
     * @return 结果
     */
    public int update(AppRecharge appRecharge);

    /**
     * 批量删除充值方案
     *
     * @param ids 需要删除的充值方案主键集合
     * @return 结果
     */
    public int deleteByIds(List<Long> ids);

    /**
     * 删除充值方案信息
     *
     * @param id 充值方案主键
     * @return 结果
     */
    public int deleteById(Long id);


    /**
     * 查询自定义分析数据
     *
     * @param query 充值方案
     * @return 充值方案集合
     */
    public List<AppRecharge> summary(SummaryRequest query);

    AppRecharge allSummary(SummaryRequest query);

    AppRechargeMapper getMapper();
}
