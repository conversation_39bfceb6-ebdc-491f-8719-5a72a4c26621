package tv.shorthub.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import tv.shorthub.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tv.shorthub.system.mapper.AppConsumptionUnlockContentMapper;
import tv.shorthub.system.domain.AppConsumptionUnlockContent;
import tv.shorthub.system.service.IAppConsumptionUnlockContentService;
import tv.shorthub.common.core.service.BaseService;

/**
 * 剧目解锁Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-15
 */
@Service
public class AppConsumptionUnlockContentServiceImpl extends BaseService<AppConsumptionUnlockContent> implements IAppConsumptionUnlockContentService
{
    @Autowired
    private AppConsumptionUnlockContentMapper appConsumptionUnlockContentMapper;

    @Override
    public AppConsumptionUnlockContentMapper getMapper() {
        return appConsumptionUnlockContentMapper;
    }

    /**
     * 查询剧目解锁
     *
     * @param id 剧目解锁主键
     * @return 剧目解锁
     */
    @Override
    public AppConsumptionUnlockContent getById(Long id)
    {
        return appConsumptionUnlockContentMapper.selectById(id);
    }

    /**
     * 查询剧目解锁列表
     *
     * @param query 剧目解锁
     * @return 剧目解锁
     */
    @Override
    public List<AppConsumptionUnlockContent> selectList(AppConsumptionUnlockContent query)
    {
        return appConsumptionUnlockContentMapper.selectList(new QueryWrapper<>(query));
    }


    /**
     * 查询剧目解锁数据汇总
     *
     * @param query 剧目解锁
     * @return 剧目解锁
     */
    @Override
    public AppConsumptionUnlockContent getSummary(AppConsumptionUnlockContent query)
    {
        return appConsumptionUnlockContentMapper.getSummary(query);
    }



    /**
     * 查询自定义分析数据
     *
     * @param query 剧目解锁
     * @return 剧目解锁
     */
    @Override
    public List<AppConsumptionUnlockContent> summary(SummaryRequest query)
    {
        return appConsumptionUnlockContentMapper.summary(query);
    }

    @Override
    public AppConsumptionUnlockContent allSummary(SummaryRequest query)
    {
        return appConsumptionUnlockContentMapper.allSummary(query);
    }

    /**
     * 新增剧目解锁
     *
     * @param appConsumptionUnlockContent 剧目解锁
     * @return 结果
     */
    @Override
    public int insert(AppConsumptionUnlockContent appConsumptionUnlockContent)
    {
        appConsumptionUnlockContent.setCreateTime(DateUtils.getNowDate());
        return appConsumptionUnlockContentMapper.insert(appConsumptionUnlockContent);
    }

    /**
     * 修改剧目解锁
     *
     * @param appConsumptionUnlockContent 剧目解锁
     * @return 结果
     */
    @Override
    public int update(AppConsumptionUnlockContent appConsumptionUnlockContent)
    {
        appConsumptionUnlockContent.setUpdateTime(DateUtils.getNowDate());
        return appConsumptionUnlockContentMapper.updateById(appConsumptionUnlockContent);
    }

    /**
     * 批量删除剧目解锁
     *
     * @param ids 需要删除的剧目解锁主键
     * @return 结果
     */
    @Override
    public int deleteByIds(List<Long> ids)
    {
        return appConsumptionUnlockContentMapper.deleteBatchIds(ids);
    }

    /**
     * 删除剧目解锁信息
     *
     * @param id 剧目解锁主键
     * @return 结果
     */
    @Override
    public int deleteById(Long id)
    {
        return appConsumptionUnlockContentMapper.deleteById(id);
    }
}
