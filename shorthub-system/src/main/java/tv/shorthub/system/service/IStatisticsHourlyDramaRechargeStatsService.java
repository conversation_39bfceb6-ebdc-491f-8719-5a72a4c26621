package tv.shorthub.system.service;

import tv.shorthub.system.mapper.StatisticsHourlyDramaRechargeStatsMapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import java.util.Date;
import tv.shorthub.system.domain.StatisticsHourlyDramaRechargeStats;
import tv.shorthub.system.domain.dto.StatisticsHourlyDramaRechargeStatsDTO;
import tv.shorthub.system.domain.dto.StatisticsHourlyDramaRechargeStatsDetailDTO;
import tv.shorthub.common.core.service.IBaseService;
import org.apache.ibatis.annotations.Param;

/**
 * 小时级剧目充值统计Service接口
 *
 * <AUTHOR>
 * @date 2025-07-15
 */
public interface IStatisticsHourlyDramaRechargeStatsService extends IBaseService<StatisticsHourlyDramaRechargeStats>
{
    /**
     * 查询小时级剧目充值统计
     *
     * @param id 小时级剧目充值统计主键
     * @return 小时级剧目充值统计
     */
    public StatisticsHourlyDramaRechargeStats getById(Long id);

    /**
     * 查询小时级剧目充值统计数据汇总
     *
     * @param query 小时级剧目充值统计
     * @return 小时级剧目充值统计数据汇总
     */
    public StatisticsHourlyDramaRechargeStats getSummary(StatisticsHourlyDramaRechargeStats query);

    /**
     * 查询小时级剧目充值统计列表
     *
     * @param query 小时级剧目充值统计
     * @return 小时级剧目充值统计集合
     */
    public List<StatisticsHourlyDramaRechargeStats> selectList(StatisticsHourlyDramaRechargeStats query);

    /**
     * 新增小时级剧目充值统计
     *
     * @param statisticsHourlyDramaRechargeStats 小时级剧目充值统计
     * @return 结果
     */
    public int insert(StatisticsHourlyDramaRechargeStats statisticsHourlyDramaRechargeStats);

    /**
     * 修改小时级剧目充值统计
     *
     * @param statisticsHourlyDramaRechargeStats 小时级剧目充值统计
     * @return 结果
     */
    public int update(StatisticsHourlyDramaRechargeStats statisticsHourlyDramaRechargeStats);

    /**
     * 批量删除小时级剧目充值统计
     *
     * @param ids 需要删除的小时级剧目充值统计主键集合
     * @return 结果
     */
    public int deleteByIds(List<Long> ids);

    /**
     * 删除小时级剧目充值统计信息
     *
     * @param id 小时级剧目充值统计主键
     * @return 结果
     */
    public int deleteById(Long id);


    /**
     * 查询自定义分析数据
     *
     * @param query 小时级剧目充值统计
     * @return 小时级剧目充值统计集合
     */
    public List<StatisticsHourlyDramaRechargeStats> summary(SummaryRequest query);

    StatisticsHourlyDramaRechargeStats allSummary(SummaryRequest query);

    StatisticsHourlyDramaRechargeStatsMapper getMapper();

    /**
     * 根据时间范围获取剧目充值统计汇总数据
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param appid 应用ID
     * @param tfid 推广链接ID
     * @param contentId 内容ID
     * @param dramaId 剧目ID
     * @return 汇总统计数据
     */
    StatisticsHourlyDramaRechargeStats getRangeSummary(@Param("startTime") Date startTime, 
                                                      @Param("endTime") Date endTime, 
                                                      @Param("appid") String appid, 
                                                      @Param("tfid") String tfid,
                                                      @Param("contentId") String contentId,
                                                      @Param("dramaId") String dramaId);

    /**
     * 根据时间范围获取剧目充值统计详情数据（包含解锁剧集详情）
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param appid 应用ID
     * @param tfid 推广链接ID
     * @param contentId 内容ID
     * @param dramaId 剧目ID
     * @return 详情统计数据
     */
    StatisticsHourlyDramaRechargeStatsDetailDTO getRangeSummaryWithDetails(Date startTime, Date endTime, String appid, String tfid, String contentId, String dramaId);

}
