package tv.shorthub.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import tv.shorthub.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tv.shorthub.system.mapper.AppUserLevelRuleMapper;
import tv.shorthub.system.domain.AppUserLevelRule;
import tv.shorthub.system.service.IAppUserLevelRuleService;
import tv.shorthub.common.core.service.BaseService;

/**
 * 会员权限Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-14
 */
@Service
public class AppUserLevelRuleServiceImpl extends BaseService<AppUserLevelRule> implements IAppUserLevelRuleService
{
    @Autowired
    private AppUserLevelRuleMapper appUserLevelRuleMapper;

    @Override
    public AppUserLevelRuleMapper getMapper() {
        return appUserLevelRuleMapper;
    }

    /**
     * 查询会员权限
     *
     * @param id 会员权限主键
     * @return 会员权限
     */
    @Override
    public AppUserLevelRule getById(Long id)
    {
        return appUserLevelRuleMapper.selectById(id);
    }

    /**
     * 查询会员权限列表
     *
     * @param query 会员权限
     * @return 会员权限
     */
    @Override
    public List<AppUserLevelRule> selectList(AppUserLevelRule query)
    {
        return appUserLevelRuleMapper.selectList(new QueryWrapper<>(query));
    }


    /**
     * 查询会员权限数据汇总
     *
     * @param query 会员权限
     * @return 会员权限
     */
    @Override
    public AppUserLevelRule getSummary(AppUserLevelRule query)
    {
        return appUserLevelRuleMapper.getSummary(query);
    }



    /**
     * 查询自定义分析数据
     *
     * @param query 会员权限
     * @return 会员权限
     */
    @Override
    public List<AppUserLevelRule> summary(SummaryRequest query)
    {
        return appUserLevelRuleMapper.summary(query);
    }

    @Override
    public AppUserLevelRule allSummary(SummaryRequest query)
    {
        return appUserLevelRuleMapper.allSummary(query);
    }

    /**
     * 新增会员权限
     *
     * @param appUserLevelRule 会员权限
     * @return 结果
     */
    @Override
    public int insert(AppUserLevelRule appUserLevelRule)
    {
        appUserLevelRule.setCreateTime(DateUtils.getNowDate());
        return appUserLevelRuleMapper.insert(appUserLevelRule);
    }

    /**
     * 修改会员权限
     *
     * @param appUserLevelRule 会员权限
     * @return 结果
     */
    @Override
    public int update(AppUserLevelRule appUserLevelRule)
    {
        appUserLevelRule.setUpdateTime(DateUtils.getNowDate());
        return appUserLevelRuleMapper.updateById(appUserLevelRule);
    }

    /**
     * 批量删除会员权限
     *
     * @param ids 需要删除的会员权限主键
     * @return 结果
     */
    @Override
    public int deleteByIds(List<Long> ids)
    {
        return appUserLevelRuleMapper.deleteBatchIds(ids);
    }

    /**
     * 删除会员权限信息
     *
     * @param id 会员权限主键
     * @return 结果
     */
    @Override
    public int deleteById(Long id)
    {
        return appUserLevelRuleMapper.deleteById(id);
    }
}
