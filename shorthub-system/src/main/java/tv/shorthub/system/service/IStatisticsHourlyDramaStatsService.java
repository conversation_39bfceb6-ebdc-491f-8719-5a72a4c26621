package tv.shorthub.system.service;

import tv.shorthub.system.mapper.StatisticsHourlyDramaStatsMapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import java.util.Date;
import tv.shorthub.system.domain.StatisticsHourlyDramaStats;
import tv.shorthub.common.core.service.IBaseService;
import org.apache.ibatis.annotations.Param;

/**
 * 每小时剧集分析统计Service接口
 *
 * <AUTHOR>
 * @date 2025-07-05
 */
public interface IStatisticsHourlyDramaStatsService extends IBaseService<StatisticsHourlyDramaStats>
{
    /**
     * 查询每小时剧集分析统计
     *
     * @param id 每小时剧集分析统计主键
     * @return 每小时剧集分析统计
     */
    public StatisticsHourlyDramaStats getById(Long id);

    /**
     * 查询每小时剧集分析统计数据汇总
     *
     * @param query 每小时剧集分析统计
     * @return 每小时剧集分析统计数据汇总
     */
    public StatisticsHourlyDramaStats getSummary(StatisticsHourlyDramaStats query);

    /**
     * 查询每小时剧集分析统计列表
     *
     * @param query 每小时剧集分析统计
     * @return 每小时剧集分析统计集合
     */
    public List<StatisticsHourlyDramaStats> selectList(StatisticsHourlyDramaStats query);

    /**
     * 新增每小时剧集分析统计
     *
     * @param statisticsHourlyDramaStats 每小时剧集分析统计
     * @return 结果
     */
    public int insert(StatisticsHourlyDramaStats statisticsHourlyDramaStats);

    /**
     * 修改每小时剧集分析统计
     *
     * @param statisticsHourlyDramaStats 每小时剧集分析统计
     * @return 结果
     */
    public int update(StatisticsHourlyDramaStats statisticsHourlyDramaStats);

    /**
     * 批量删除每小时剧集分析统计
     *
     * @param ids 需要删除的每小时剧集分析统计主键集合
     * @return 结果
     */
    public int deleteByIds(List<Long> ids);

    /**
     * 删除每小时剧集分析统计信息
     *
     * @param id 每小时剧集分析统计主键
     * @return 结果
     */
    public int deleteById(Long id);


    /**
     * 查询自定义分析数据
     *
     * @param query 每小时剧集分析统计
     * @return 每小时剧集分析统计集合
     */
    public List<StatisticsHourlyDramaStats> summary(SummaryRequest query);

    StatisticsHourlyDramaStats allSummary(SummaryRequest query);

    StatisticsHourlyDramaStatsMapper getMapper();

    /**
     * 根据时间范围获取剧集统计汇总数据
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param appid 应用ID
     * @param tfid 推广链接ID
     * @return 汇总统计数据
     */
    StatisticsHourlyDramaStats getRangeSummary(@Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("appid") String appid, @Param("tfid") String tfid);

    /**
     * 根据时间范围查询剧集统计列表
     *
     * @param query 查询条件
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计数据列表
     */
    List<StatisticsHourlyDramaStats> selectListByTimeRange(StatisticsHourlyDramaStats query, Date startTime, Date endTime);

    /**
     * 根据 tfid 列表获取汇总数据
     *
     * @param tfids tfid 列表
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @return 汇总统计数据
     */
    StatisticsHourlyDramaStats getSummaryByTfids(@Param("tfids") List<String> tfids, @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 根据创建者获取其所有子账号的汇总数据
     *
     * @param creatorName 创建者用户名
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @return 汇总统计数据
     */
    StatisticsHourlyDramaStats getSummaryByCreator(@Param("creatorName") String creatorName, @Param("startTime") Date startTime, @Param("endTime") Date endTime);
}
