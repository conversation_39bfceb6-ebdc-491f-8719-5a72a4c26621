package tv.shorthub.system.service;

import tv.shorthub.system.mapper.AirwallexUserPlanRecordMapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import tv.shorthub.system.domain.AirwallexUserPlanRecord;
import tv.shorthub.common.core.service.IBaseService;

/**
 * 签约续订记录Service接口
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
public interface IAirwallexUserPlanRecordService extends IBaseService<AirwallexUserPlanRecord>
{
    /**
     * 查询签约续订记录
     *
     * @param id 签约续订记录主键
     * @return 签约续订记录
     */
    public AirwallexUserPlanRecord getById(Long id);

    /**
     * 查询签约续订记录数据汇总
     *
     * @param query 签约续订记录
     * @return 签约续订记录数据汇总
     */
    public AirwallexUserPlanRecord getSummary(AirwallexUserPlanRecord query);

    /**
     * 查询签约续订记录列表
     *
     * @param query 签约续订记录
     * @return 签约续订记录集合
     */
    public List<AirwallexUserPlanRecord> selectList(AirwallexUserPlanRecord query);

    /**
     * 新增签约续订记录
     *
     * @param airwallexUserPlanRecord 签约续订记录
     * @return 结果
     */
    public int insert(AirwallexUserPlanRecord airwallexUserPlanRecord);

    /**
     * 修改签约续订记录
     *
     * @param airwallexUserPlanRecord 签约续订记录
     * @return 结果
     */
    public int update(AirwallexUserPlanRecord airwallexUserPlanRecord);

    /**
     * 批量删除签约续订记录
     *
     * @param ids 需要删除的签约续订记录主键集合
     * @return 结果
     */
    public int deleteByIds(List<Long> ids);

    /**
     * 删除签约续订记录信息
     *
     * @param id 签约续订记录主键
     * @return 结果
     */
    public int deleteById(Long id);


    /**
     * 查询自定义分析数据
     *
     * @param query 签约续订记录
     * @return 签约续订记录集合
     */
    public List<AirwallexUserPlanRecord> summary(SummaryRequest query);

    AirwallexUserPlanRecord allSummary(SummaryRequest query);

    AirwallexUserPlanRecordMapper getMapper();
}
