package tv.shorthub.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import tv.shorthub.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tv.shorthub.system.mapper.SysDingtalkLogMapper;
import tv.shorthub.system.domain.SysDingtalkLog;
import tv.shorthub.system.service.ISysDingtalkLogService;
import tv.shorthub.common.core.service.BaseService;

/**
 * 钉钉发送日志Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-17
 */
@Service
public class SysDingtalkLogServiceImpl extends BaseService<SysDingtalkLog> implements ISysDingtalkLogService
{
    @Autowired
    private SysDingtalkLogMapper sysDingtalkLogMapper;

    /**
     * 查询钉钉发送日志
     *
     * @param id 钉钉发送日志主键
     * @return 钉钉发送日志
     */
    @Override
    public SysDingtalkLog getById(Long id)
    {
        return sysDingtalkLogMapper.selectById(id);
    }

    /**
     * 查询钉钉发送日志列表
     *
     * @param query 钉钉发送日志
     * @return 钉钉发送日志
     */
    @Override
    public List<SysDingtalkLog> selectList(SysDingtalkLog query)
    {
        return sysDingtalkLogMapper.selectList(new QueryWrapper<>(query));
    }


    /**
     * 查询钉钉发送日志数据汇总
     *
     * @param query 钉钉发送日志
     * @return 钉钉发送日志
     */
    @Override
    public SysDingtalkLog getSummary(SysDingtalkLog query)
    {
        return sysDingtalkLogMapper.getSummary(query);
    }



    /**
     * 查询自定义分析数据
     *
     * @param query 钉钉发送日志
     * @return 钉钉发送日志
     */
    @Override
    public List<SysDingtalkLog> summary(SummaryRequest query)
    {
        return sysDingtalkLogMapper.summary(query);
    }

    @Override
    public SysDingtalkLog allSummary(SummaryRequest query)
    {
        return sysDingtalkLogMapper.allSummary(query);
    }

    /**
     * 新增钉钉发送日志
     *
     * @param sysDingtalkLog 钉钉发送日志
     * @return 结果
     */
    @Override
    public int insert(SysDingtalkLog sysDingtalkLog)
    {
        sysDingtalkLog.setCreateTime(DateUtils.getNowDate());
        return sysDingtalkLogMapper.insert(sysDingtalkLog);
    }

    /**
     * 修改钉钉发送日志
     *
     * @param sysDingtalkLog 钉钉发送日志
     * @return 结果
     */
    @Override
    public int update(SysDingtalkLog sysDingtalkLog)
    {
        return sysDingtalkLogMapper.updateById(sysDingtalkLog);
    }

    /**
     * 批量删除钉钉发送日志
     *
     * @param ids 需要删除的钉钉发送日志主键
     * @return 结果
     */
    @Override
    public int deleteByIds(List<Long> ids)
    {
        return sysDingtalkLogMapper.deleteBatchIds(ids);
    }

    /**
     * 删除钉钉发送日志信息
     *
     * @param id 钉钉发送日志主键
     * @return 结果
     */
    @Override
    public int deleteById(Long id)
    {
        return sysDingtalkLogMapper.deleteById(id);
    }
}
