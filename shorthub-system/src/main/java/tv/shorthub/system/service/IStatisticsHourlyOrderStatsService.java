package tv.shorthub.system.service;

import tv.shorthub.system.mapper.StatisticsHourlyOrderStatsMapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.Date;
import java.util.List;
import tv.shorthub.system.domain.StatisticsHourlyOrderStats;
import tv.shorthub.common.core.service.IBaseService;
import org.apache.ibatis.annotations.Param;

/**
 * 每小时核心订单交易统计Service接口
 *
 * <AUTHOR>
 * @date 2025-06-27
 */
public interface IStatisticsHourlyOrderStatsService extends IBaseService<StatisticsHourlyOrderStats>
{
    /**
     * 查询每小时核心订单交易统计
     *
     * @param id 每小时核心订单交易统计主键
     * @return 每小时核心订单交易统计
     */
    public StatisticsHourlyOrderStats getById(Long id);

    /**
     * 查询每小时核心订单交易统计数据汇总
     *
     * @param query 每小时核心订单交易统计
     * @return 每小时核心订单交易统计数据汇总
     */
    public StatisticsHourlyOrderStats getSummary(StatisticsHourlyOrderStats query);

    /**
     * 查询每小时核心订单交易统计列表
     *
     * @param query 每小时核心订单交易统计
     * @return 每小时核心订单交易统计集合
     */
    public List<StatisticsHourlyOrderStats> selectList(StatisticsHourlyOrderStats query);

    /**
     * 新增每小时核心订单交易统计
     *
     * @param statisticsHourlyOrderStats 每小时核心订单交易统计
     * @return 结果
     */
    public int insert(StatisticsHourlyOrderStats statisticsHourlyOrderStats);

    /**
     * 修改每小时核心订单交易统计
     *
     * @param statisticsHourlyOrderStats 每小时核心订单交易统计
     * @return 结果
     */
    public int update(StatisticsHourlyOrderStats statisticsHourlyOrderStats);

    /**
     * 批量删除每小时核心订单交易统计
     *
     * @param ids 需要删除的每小时核心订单交易统计主键集合
     * @return 结果
     */
    public int deleteByIds(List<Long> ids);

    /**
     * 删除每小时核心订单交易统计信息
     *
     * @param id 每小时核心订单交易统计主键
     * @return 结果
     */
    public int deleteById(Long id);


    /**
     * 查询自定义分析数据
     *
     * @param query 每小时核心订单交易统计
     * @return 每小时核心订单交易统计集合
     */
    public List<StatisticsHourlyOrderStats> summary(SummaryRequest query);

    StatisticsHourlyOrderStats allSummary(SummaryRequest query);

    StatisticsHourlyOrderStatsMapper getMapper();

    /**
     * 根据时间范围获取订单统计汇总数据
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param appid 应用ID
     * @param tfid 推广链接ID
     * @param orderChannel 订单支付通道
     * @return 汇总统计数据
     */
    StatisticsHourlyOrderStats getRangeSummary(@Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("appid") String appid, @Param("tfid") String tfid, @Param("orderChannel") String orderChannel);

    /**
     * 根据时间范围查询订单统计列表
     *
     * @param query 查询条件
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计数据列表
     */
    List<StatisticsHourlyOrderStats> selectListByTimeRange(StatisticsHourlyOrderStats query, Date startTime, Date endTime);



    /**
     * 根据 tfid 列表获取汇总数据
     *
     * @param tfids tfid 列表
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @param appid 应用ID（可选）
     * @param orderChannel 订单支付通道（可选）
     * @return 汇总统计数据
     */
    StatisticsHourlyOrderStats getSummaryByTfids(List<String> tfids, Date startTime, Date endTime, String appid, String orderChannel);

    /**
     * 根据创建者获取其所有子账号的汇总数据
     *
     * @param creatorName 创建者用户名
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @param orderChannel 订单支付通道（可选）
     * @return 汇总统计数据
     */
    StatisticsHourlyOrderStats getSummaryByCreator(String creatorName, Date startTime, Date endTime, String orderChannel);

}
