package tv.shorthub.system.service;

import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import tv.shorthub.system.domain.AppUserClosePayPageRecord;
import tv.shorthub.common.core.service.IBaseService;
import tv.shorthub.system.mapper.AppUserClosePayPageRecordMapper;

/**
 * 用户关闭支付页面记录Service接口
 *
 * <AUTHOR>
 */
public interface AppUserClosePayPageRecordService extends IBaseService<AppUserClosePayPageRecord>
{
    /**
     * 查询用户关闭支付页面记录
     *
     * @param id 用户关闭支付页面记录主键
     * @return 用户关闭支付页面记录
     */
    AppUserClosePayPageRecord getById(Long id);

    /**
     * 查询用户关闭支付页面记录数据汇总
     *
     * @param query 用户关闭支付页面记录
     * @return 用户关闭支付页面记录数据汇总
     */
    AppUserClosePayPageRecord getSummary(AppUserClosePayPageRecord query);

    /**
     * 查询用户关闭支付页面记录列表
     *
     * @param query 用户关闭支付页面记录
     * @return 用户关闭支付页面记录集合
     */
    List<AppUserClosePayPageRecord> selectList(AppUserClosePayPageRecord query);

    /**
     * 新增用户关闭支付页面记录
     *
     * @param appUserClosePayPage 用户关闭支付页面记录
     * @return 结果
     */
    public int insert(AppUserClosePayPageRecord appUserClosePayPage);

    /**
     * 修改用户关闭支付页面记录
     *
     * @param appUserClosePayPage 用户关闭支付页面记录
     * @return 结果
     */
    int update(AppUserClosePayPageRecord appUserClosePayPage);

    /**
     * 批量删除用户关闭支付页面记录
     *
     * @param ids 需要删除的用户关闭支付页面记录主键集合
     * @return 结果
     */
    int deleteByIds(List<Long> ids);

    /**
     * 删除用户关闭支付页面记录信息
     *
     * @param id 用户关闭支付页面记录主键
     * @return 结果
     */
    int deleteById(Long id);


    /**
     * 查询自定义分析数据
     *
     * @param query 用户关闭支付页面记录
     * @return 用户关闭支付页面记录集合
     */
    List<AppUserClosePayPageRecord> summary(SummaryRequest query);

    AppUserClosePayPageRecord allSummary(SummaryRequest query);

    AppUserClosePayPageRecordMapper getMapper();

    /**
     * 查询用户关闭支付页面最新一条数据
     * @param userId
     * @return
     */
    AppUserClosePayPageRecord queryUserLatestRecordByUserId(String userId);
}