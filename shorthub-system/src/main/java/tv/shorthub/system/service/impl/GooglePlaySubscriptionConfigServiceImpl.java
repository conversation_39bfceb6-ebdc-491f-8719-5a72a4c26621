package tv.shorthub.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import tv.shorthub.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tv.shorthub.system.mapper.GooglePlaySubscriptionConfigMapper;
import tv.shorthub.system.domain.GooglePlaySubscriptionConfig;
import tv.shorthub.system.service.IGooglePlaySubscriptionConfigService;
import tv.shorthub.common.core.service.BaseService;

/**
 * Google Play订阅产品配置Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
@Service
public class GooglePlaySubscriptionConfigServiceImpl extends BaseService<GooglePlaySubscriptionConfig> implements IGooglePlaySubscriptionConfigService
{
    @Autowired
    private GooglePlaySubscriptionConfigMapper googlePlaySubscriptionConfigMapper;

    @Override
    public GooglePlaySubscriptionConfigMapper getMapper() {
        return googlePlaySubscriptionConfigMapper;
    }

    /**
     * 查询Google Play订阅产品配置
     *
     * @param id Google Play订阅产品配置主键
     * @return Google Play订阅产品配置
     */
    @Override
    public GooglePlaySubscriptionConfig getById(Long id)
    {
        return googlePlaySubscriptionConfigMapper.selectById(id);
    }

    /**
     * 查询Google Play订阅产品配置列表
     *
     * @param query Google Play订阅产品配置
     * @return Google Play订阅产品配置
     */
    @Override
    public List<GooglePlaySubscriptionConfig> selectList(GooglePlaySubscriptionConfig query)
    {
        return googlePlaySubscriptionConfigMapper.selectList(new QueryWrapper<>(query));
    }


    /**
     * 查询Google Play订阅产品配置数据汇总
     *
     * @param query Google Play订阅产品配置
     * @return Google Play订阅产品配置
     */
    @Override
    public GooglePlaySubscriptionConfig getSummary(GooglePlaySubscriptionConfig query)
    {
        return googlePlaySubscriptionConfigMapper.getSummary(query);
    }



    /**
     * 查询自定义分析数据
     *
     * @param query Google Play订阅产品配置
     * @return Google Play订阅产品配置
     */
    @Override
    public List<GooglePlaySubscriptionConfig> summary(SummaryRequest query)
    {
        return googlePlaySubscriptionConfigMapper.summary(query);
    }

    @Override
    public GooglePlaySubscriptionConfig allSummary(SummaryRequest query)
    {
        return googlePlaySubscriptionConfigMapper.allSummary(query);
    }

    /**
     * 新增Google Play订阅产品配置
     *
     * @param googlePlaySubscriptionConfig Google Play订阅产品配置
     * @return 结果
     */
    @Override
    public int insert(GooglePlaySubscriptionConfig googlePlaySubscriptionConfig)
    {
        googlePlaySubscriptionConfig.setCreateTime(DateUtils.getNowDate());
        return googlePlaySubscriptionConfigMapper.insert(googlePlaySubscriptionConfig);
    }

    /**
     * 修改Google Play订阅产品配置
     *
     * @param googlePlaySubscriptionConfig Google Play订阅产品配置
     * @return 结果
     */
    @Override
    public int update(GooglePlaySubscriptionConfig googlePlaySubscriptionConfig)
    {
        googlePlaySubscriptionConfig.setUpdateTime(DateUtils.getNowDate());
        return googlePlaySubscriptionConfigMapper.updateById(googlePlaySubscriptionConfig);
    }

    /**
     * 批量删除Google Play订阅产品配置
     *
     * @param ids 需要删除的Google Play订阅产品配置主键
     * @return 结果
     */
    @Override
    public int deleteByIds(List<Long> ids)
    {
        return googlePlaySubscriptionConfigMapper.deleteBatchIds(ids);
    }

    /**
     * 删除Google Play订阅产品配置信息
     *
     * @param id Google Play订阅产品配置主键
     * @return 结果
     */
    @Override
    public int deleteById(Long id)
    {
        return googlePlaySubscriptionConfigMapper.deleteById(id);
    }
}
