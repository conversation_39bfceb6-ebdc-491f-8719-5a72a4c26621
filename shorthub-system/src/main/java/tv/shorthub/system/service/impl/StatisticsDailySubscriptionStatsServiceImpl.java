package tv.shorthub.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import tv.shorthub.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tv.shorthub.system.mapper.StatisticsDailySubscriptionStatsMapper;
import tv.shorthub.system.domain.StatisticsDailySubscriptionStats;
import tv.shorthub.system.service.IStatisticsDailySubscriptionStatsService;
import tv.shorthub.common.core.service.BaseService;
import tv.shorthub.common.utils.SecurityUtils;
import tv.shorthub.system.utils.StatisticsPermissionUtils;
import java.util.Date;

/**
 * 每日订阅统计Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
@Service
public class StatisticsDailySubscriptionStatsServiceImpl extends BaseService<StatisticsDailySubscriptionStats> implements IStatisticsDailySubscriptionStatsService
{
    @Autowired
    private StatisticsDailySubscriptionStatsMapper statisticsDailySubscriptionStatsMapper;

    @Autowired
    private StatisticsPermissionUtils permissionUtils;

    @Override
    public StatisticsDailySubscriptionStatsMapper getMapper() {
        return statisticsDailySubscriptionStatsMapper;
    }

    /**
     * 查询每日订阅统计
     *
     * @param id 每日订阅统计主键
     * @return 每日订阅统计
     */
    @Override
    public StatisticsDailySubscriptionStats getById(Long id)
    {
        return statisticsDailySubscriptionStatsMapper.selectById(id);
    }

    /**
     * 查询每日订阅统计列表
     *
     * @param query 每日订阅统计
     * @return 每日订阅统计
     */
    @Override
    public List<StatisticsDailySubscriptionStats> selectList(StatisticsDailySubscriptionStats query)
    {
        QueryWrapper<StatisticsDailySubscriptionStats> wrapper = new QueryWrapper<>(query);
        wrapper.orderByDesc("stat_date");
        permissionUtils.filterByDeliverPermission(wrapper);
        return statisticsDailySubscriptionStatsMapper.selectList(wrapper);
    }

    /**
     * 根据时间范围查询每日订阅统计列表
     */
    public List<StatisticsDailySubscriptionStats> selectListByTimeRange(StatisticsDailySubscriptionStats query, Date startTime, Date endTime) {
        QueryWrapper<StatisticsDailySubscriptionStats> wrapper = new QueryWrapper<>(query);
        wrapper.ge("stat_date", startTime);
        wrapper.lt("stat_date", endTime);
        wrapper.orderByDesc("stat_date");
        permissionUtils.filterByDeliverPermission(wrapper);
        return statisticsDailySubscriptionStatsMapper.selectList(wrapper);
    }

    /**
     * 查询每日订阅统计数据汇总
     *
     * @param query 每日订阅统计
     * @return 每日订阅统计
     */
    @Override
    public StatisticsDailySubscriptionStats getSummary(StatisticsDailySubscriptionStats query)
    {
        if (permissionUtils.isDeliverOnly()) {
            String currentUsername = SecurityUtils.getUsername();
            List<String> tfids = permissionUtils.getUserTfids(currentUsername);
            if (!tfids.isEmpty()) {
                return statisticsDailySubscriptionStatsMapper.getSummaryByTfids(tfids, null, null);
            } else {
                return new StatisticsDailySubscriptionStats();
            }
        }
        return statisticsDailySubscriptionStatsMapper.getSummary(query);
    }

    @Override
    public StatisticsDailySubscriptionStats getSummaryByTfids(List<String> tfids, Date startTime, Date endTime) {
        // 调用带时区的方法，使用默认时区
        return getSummaryByTfids(tfids, startTime, endTime, "UTC+8");
    }

    @Override
    public StatisticsDailySubscriptionStats getSummaryByTfids(List<String> tfids, Date startTime, Date endTime, String timezone) {
        // 如果是业务主账号（但不是系统管理员），强制使用自己的 appid
        String appid = null;
        if (SecurityUtils.isBusinessAdmin() && !SecurityUtils.isSystemAdmin()) {
            appid = SecurityUtils.getAppid();
        }
        return statisticsDailySubscriptionStatsMapper.getSummaryByTfids(tfids, startTime, endTime, timezone, appid);
    }

    @Override
    public StatisticsDailySubscriptionStats getSummaryByCreator(String creator, Date startTime, Date endTime) {
        // 调用带时区的方法，使用默认时区
        return getSummaryByCreator(creator, startTime, endTime, "UTC+8");
    }

    @Override
    public StatisticsDailySubscriptionStats getSummaryByCreator(String creator, Date startTime, Date endTime, String timezone) {
        if (SecurityUtils.isBusinessAdmin() && !SecurityUtils.isSystemAdmin() &&
                (org.apache.commons.lang3.StringUtils.isEmpty(creator) || SecurityUtils.getUsername().equals(creator))) {
            if (startTime != null && endTime != null) {
                return statisticsDailySubscriptionStatsMapper.getRangeSummary(startTime, endTime, SecurityUtils.getAppid(), null, timezone);
            } else {
                StatisticsDailySubscriptionStats query = new StatisticsDailySubscriptionStats();
                query.setAppid(SecurityUtils.getAppid());
                query.setTimezone(timezone);
                return statisticsDailySubscriptionStatsMapper.getSummary(query);
            }
        }
        List<String> tfids = permissionUtils.getUserTfids(creator);
        if (tfids.isEmpty()) {
            return new StatisticsDailySubscriptionStats();
        }
        return getSummaryByTfids(tfids, startTime, endTime, timezone);
    }

    @Override
    public StatisticsDailySubscriptionStats getRangeSummary(Date startTime, Date endTime, String appid, String tfid, String timezone) {
        if (SecurityUtils.isBusinessAdmin() && !SecurityUtils.isSystemAdmin()) {
            appid = SecurityUtils.getAppid();
        }
        return statisticsDailySubscriptionStatsMapper.getRangeSummary(startTime, endTime, appid, tfid, timezone);
    }

    /**
     * 查询自定义分析数据
     *
     * @param query 每日订阅统计
     * @return 每日订阅统计
     */
    @Override
    public List<StatisticsDailySubscriptionStats> summary(SummaryRequest query)
    {
        return statisticsDailySubscriptionStatsMapper.summary(query);
    }

    @Override
    public StatisticsDailySubscriptionStats allSummary(SummaryRequest query)
    {
        return statisticsDailySubscriptionStatsMapper.allSummary(query);
    }

    /**
     * 新增每日订阅统计
     *
     * @param statisticsDailySubscriptionStats 每日订阅统计
     * @return 结果
     */
    @Override
    public int insert(StatisticsDailySubscriptionStats statisticsDailySubscriptionStats)
    {
        statisticsDailySubscriptionStats.setCreateTime(DateUtils.getNowDate());
        return statisticsDailySubscriptionStatsMapper.insert(statisticsDailySubscriptionStats);
    }

    /**
     * 修改每日订阅统计
     *
     * @param statisticsDailySubscriptionStats 每日订阅统计
     * @return 结果
     */
    @Override
    public int update(StatisticsDailySubscriptionStats statisticsDailySubscriptionStats)
    {
        statisticsDailySubscriptionStats.setUpdateTime(DateUtils.getNowDate());
        return statisticsDailySubscriptionStatsMapper.updateById(statisticsDailySubscriptionStats);
    }

    /**
     * 批量删除每日订阅统计
     *
     * @param ids 需要删除的每日订阅统计主键
     * @return 结果
     */
    @Override
    public int deleteByIds(List<Long> ids)
    {
        return statisticsDailySubscriptionStatsMapper.deleteBatchIds(ids);
    }

    /**
     * 删除每日订阅统计信息
     *
     * @param id 每日订阅统计主键
     * @return 结果
     */
    @Override
    public int deleteById(Long id)
    {
        return statisticsDailySubscriptionStatsMapper.deleteById(id);
    }
}
