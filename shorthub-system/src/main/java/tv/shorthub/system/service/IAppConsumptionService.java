package tv.shorthub.system.service;

import tv.shorthub.system.mapper.AppConsumptionMapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import tv.shorthub.system.domain.AppConsumption;
import tv.shorthub.common.core.service.IBaseService;

/**
 * 金币消费Service接口
 *
 * <AUTHOR>
 * @date 2025-05-10
 */
public interface IAppConsumptionService extends IBaseService<AppConsumption>
{
    /**
     * 查询金币消费
     *
     * @param id 金币消费主键
     * @return 金币消费
     */
    public AppConsumption getById(Long id);

    /**
     * 查询金币消费数据汇总
     *
     * @param query 金币消费
     * @return 金币消费数据汇总
     */
    public AppConsumption getSummary(AppConsumption query);

    /**
     * 查询金币消费列表
     *
     * @param query 金币消费
     * @return 金币消费集合
     */
    public List<AppConsumption> selectList(AppConsumption query);

    /**
     * 新增金币消费
     *
     * @param appConsumption 金币消费
     * @return 结果
     */
    public int insert(AppConsumption appConsumption);

    /**
     * 修改金币消费
     *
     * @param appConsumption 金币消费
     * @return 结果
     */
    public int update(AppConsumption appConsumption);

    /**
     * 批量删除金币消费
     *
     * @param ids 需要删除的金币消费主键集合
     * @return 结果
     */
    public int deleteByIds(List<Long> ids);

    /**
     * 删除金币消费信息
     *
     * @param id 金币消费主键
     * @return 结果
     */
    public int deleteById(Long id);


    /**
     * 查询自定义分析数据
     *
     * @param query 金币消费
     * @return 金币消费集合
     */
    public List<AppConsumption> summary(SummaryRequest query);

    AppConsumption allSummary(SummaryRequest query);

    AppConsumptionMapper getMapper();

    List<AppConsumption> getUserHistory(String userId, Integer page, int pageSize);
}
