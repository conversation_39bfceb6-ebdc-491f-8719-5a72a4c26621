package tv.shorthub.system.service;

import tv.shorthub.system.mapper.AppUserLevelRuleMapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import tv.shorthub.system.domain.AppUserLevelRule;
import tv.shorthub.common.core.service.IBaseService;

/**
 * 会员权限Service接口
 *
 * <AUTHOR>
 * @date 2025-05-14
 */
public interface IAppUserLevelRuleService extends IBaseService<AppUserLevelRule>
{
    /**
     * 查询会员权限
     *
     * @param id 会员权限主键
     * @return 会员权限
     */
    public AppUserLevelRule getById(Long id);

    /**
     * 查询会员权限数据汇总
     *
     * @param query 会员权限
     * @return 会员权限数据汇总
     */
    public AppUserLevelRule getSummary(AppUserLevelRule query);

    /**
     * 查询会员权限列表
     *
     * @param query 会员权限
     * @return 会员权限集合
     */
    public List<AppUserLevelRule> selectList(AppUserLevelRule query);

    /**
     * 新增会员权限
     *
     * @param appUserLevelRule 会员权限
     * @return 结果
     */
    public int insert(AppUserLevelRule appUserLevelRule);

    /**
     * 修改会员权限
     *
     * @param appUserLevelRule 会员权限
     * @return 结果
     */
    public int update(AppUserLevelRule appUserLevelRule);

    /**
     * 批量删除会员权限
     *
     * @param ids 需要删除的会员权限主键集合
     * @return 结果
     */
    public int deleteByIds(List<Long> ids);

    /**
     * 删除会员权限信息
     *
     * @param id 会员权限主键
     * @return 结果
     */
    public int deleteById(Long id);


    /**
     * 查询自定义分析数据
     *
     * @param query 会员权限
     * @return 会员权限集合
     */
    public List<AppUserLevelRule> summary(SummaryRequest query);

    AppUserLevelRule allSummary(SummaryRequest query);

    AppUserLevelRuleMapper getMapper();
}
