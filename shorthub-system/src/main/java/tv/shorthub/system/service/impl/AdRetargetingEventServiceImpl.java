package tv.shorthub.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import tv.shorthub.common.utils.DateUtils;
import tv.shorthub.common.utils.SecurityUtils;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tv.shorthub.system.mapper.AdRetargetingEventMapper;
import tv.shorthub.system.domain.AdRetargetingEvent;
import tv.shorthub.system.service.IAdRetargetingEventService;
import tv.shorthub.common.core.service.BaseService;

/**
 * 回传事件Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Service
public class AdRetargetingEventServiceImpl extends BaseService<AdRetargetingEvent> implements IAdRetargetingEventService
{
    @Autowired
    private AdRetargetingEventMapper adRetargetingEventMapper;

    @Override
    public AdRetargetingEventMapper getMapper() {
        return adRetargetingEventMapper;
    }

    /**
     * 查询回传事件
     *
     * @param id 回传事件主键
     * @return 回传事件
     */
    @Override
    public AdRetargetingEvent getById(Long id)
    {
        return adRetargetingEventMapper.selectById(id);
    }

    /**
     * 查询回传事件列表
     *
     * @param query 回传事件
     * @return 回传事件
     */
    @Override
    public List<AdRetargetingEvent> selectList(AdRetargetingEvent query)
    {
        QueryWrapper<AdRetargetingEvent> queryWrapper = new QueryWrapper<>(query);
        if (!SecurityUtils.isBusinessAdmin()) {
            queryWrapper.eq("create_by", SecurityUtils.getUsername());
        }
        return adRetargetingEventMapper.selectList(queryWrapper);
    }


    /**
     * 查询回传事件数据汇总
     *
     * @param query 回传事件
     * @return 回传事件
     */
    @Override
    public AdRetargetingEvent getSummary(AdRetargetingEvent query)
    {
        return adRetargetingEventMapper.getSummary(query);
    }



    /**
     * 查询自定义分析数据
     *
     * @param query 回传事件
     * @return 回传事件
     */
    @Override
    public List<AdRetargetingEvent> summary(SummaryRequest query)
    {
        return adRetargetingEventMapper.summary(query);
    }

    @Override
    public AdRetargetingEvent allSummary(SummaryRequest query)
    {
        return adRetargetingEventMapper.allSummary(query);
    }

    /**
     * 新增回传事件
     *
     * @param adRetargetingEvent 回传事件
     * @return 结果
     */
    @Override
    public int insert(AdRetargetingEvent adRetargetingEvent)
    {
        adRetargetingEvent.setCreateBy(SecurityUtils.getUsername());
        adRetargetingEvent.setCreateTime(DateUtils.getNowDate());
        return adRetargetingEventMapper.insert(adRetargetingEvent);
    }

    /**
     * 修改回传事件
     *
     * @param adRetargetingEvent 回传事件
     * @return 结果
     */
    @Override
    public int update(AdRetargetingEvent adRetargetingEvent)
    {
        adRetargetingEvent.setUpdateTime(DateUtils.getNowDate());
        return adRetargetingEventMapper.updateById(adRetargetingEvent);
    }

    /**
     * 批量删除回传事件
     *
     * @param ids 需要删除的回传事件主键
     * @return 结果
     */
    @Override
    public int deleteByIds(List<Long> ids)
    {
        return adRetargetingEventMapper.deleteBatchIds(ids);
    }

    /**
     * 删除回传事件信息
     *
     * @param id 回传事件主键
     * @return 结果
     */
    @Override
    public int deleteById(Long id)
    {
        return adRetargetingEventMapper.deleteById(id);
    }
}
