package tv.shorthub.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import java.util.Date;
import tv.shorthub.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tv.shorthub.system.mapper.StatisticsHourlyDramaStatsMapper;
import tv.shorthub.system.domain.StatisticsHourlyDramaStats;
import tv.shorthub.system.service.IStatisticsHourlyDramaStatsService;
import tv.shorthub.common.core.service.BaseService;
import tv.shorthub.common.utils.SecurityUtils;
import tv.shorthub.system.utils.StatisticsPermissionUtils;

/**
 * 每小时剧集分析统计Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-05
 */
@Service
public class StatisticsHourlyDramaStatsServiceImpl extends BaseService<StatisticsHourlyDramaStats> implements IStatisticsHourlyDramaStatsService
{
    @Autowired
    private StatisticsHourlyDramaStatsMapper statisticsHourlyDramaStatsMapper;

    @Override
    public StatisticsHourlyDramaStatsMapper getMapper() {
        return statisticsHourlyDramaStatsMapper;
    }
    @Autowired
    private StatisticsPermissionUtils permissionUtils;

    /**
     * 查询每小时剧集分析统计
     *
     * @param id 每小时剧集分析统计主键
     * @return 每小时剧集分析统计
     */
    @Override
    public StatisticsHourlyDramaStats getById(Long id)
    {
        return statisticsHourlyDramaStatsMapper.selectById(id);
    }

    /**
     * 查询每小时剧集分析统计列表
     *
     * @param query 每小时剧集分析统计
     * @return 每小时剧集分析统计
     */
    @Override
    public List<StatisticsHourlyDramaStats> selectList(StatisticsHourlyDramaStats query)
    {
        QueryWrapper<StatisticsHourlyDramaStats> wrapper = new QueryWrapper<>(query);
        // 按时间倒序排列，最新数据在前
        wrapper.orderByDesc("stat_hour");
        // 权限过滤
        permissionUtils.filterByDeliverPermission(wrapper);

        return statisticsHourlyDramaStatsMapper.selectList(new QueryWrapper<>(query));
    }


    /**
     * 查询每小时剧集分析统计数据汇总
     *
     * @param query 每小时剧集分析统计
     * @return 每小时剧集分析统计
     */
    @Override
    public StatisticsHourlyDramaStats getSummary(StatisticsHourlyDramaStats query)
    {
        return statisticsHourlyDramaStatsMapper.getSummary(query);
    }



    /**
     * 查询自定义分析数据
     *
     * @param query 每小时剧集分析统计
     * @return 每小时剧集分析统计
     */
    @Override
    public List<StatisticsHourlyDramaStats> summary(SummaryRequest query)
    {
        return statisticsHourlyDramaStatsMapper.summary(query);
    }

    @Override
    public StatisticsHourlyDramaStats allSummary(SummaryRequest query)
    {
        return statisticsHourlyDramaStatsMapper.allSummary(query);
    }

    /**
     * 新增每小时剧集分析统计
     *
     * @param statisticsHourlyDramaStats 每小时剧集分析统计
     * @return 结果
     */
    @Override
    public int insert(StatisticsHourlyDramaStats statisticsHourlyDramaStats)
    {
        statisticsHourlyDramaStats.setCreateTime(DateUtils.getNowDate());
        return statisticsHourlyDramaStatsMapper.insert(statisticsHourlyDramaStats);
    }

    /**
     * 修改每小时剧集分析统计
     *
     * @param statisticsHourlyDramaStats 每小时剧集分析统计
     * @return 结果
     */
    @Override
    public int update(StatisticsHourlyDramaStats statisticsHourlyDramaStats)
    {
        statisticsHourlyDramaStats.setUpdateTime(DateUtils.getNowDate());
        return statisticsHourlyDramaStatsMapper.updateById(statisticsHourlyDramaStats);
    }

    /**
     * 批量删除每小时剧集分析统计
     *
     * @param ids 需要删除的每小时剧集分析统计主键
     * @return 结果
     */
    @Override
    public int deleteByIds(List<Long> ids)
    {
        return statisticsHourlyDramaStatsMapper.deleteBatchIds(ids);
    }

    /**
     * 删除每小时剧集分析统计信息
     *
     * @param id 每小时剧集分析统计主键
     * @return 结果
     */
    @Override
    public int deleteById(Long id)
    {
        return statisticsHourlyDramaStatsMapper.deleteById(id);
    }

    /**
     * 根据时间范围获取剧集统计汇总数据
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param appid 应用ID
     * @param tfid 推广链接ID
     * @return 汇总统计数据
     */
    @Override
    public StatisticsHourlyDramaStats getRangeSummary(Date startTime, Date endTime, String appid, String tfid)
    {
        return statisticsHourlyDramaStatsMapper.getRangeSummary(startTime, endTime, appid, tfid);
    }

    /**
     * 根据时间范围查询剧集统计列表
     *
     * @param query 查询条件
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计数据列表
     */
    @Override
    public List<StatisticsHourlyDramaStats> selectListByTimeRange(StatisticsHourlyDramaStats query, Date startTime, Date endTime)
    {
        return statisticsHourlyDramaStatsMapper.selectListByTimeRange(query, startTime, endTime);
    }

    /**
     * 根据 tfid 列表获取汇总数据
     *
     * @param tfids tfid 列表
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @return 汇总统计数据
     */
    @Override
    public StatisticsHourlyDramaStats getSummaryByTfids(List<String> tfids, Date startTime, Date endTime)
    {
        // 如果是业务主账号（但不是系统管理员），强制使用自己的 appid
        String appid = null;
        if (SecurityUtils.isBusinessAdmin() && !SecurityUtils.isSystemAdmin()) {
            appid = SecurityUtils.getAppid();
        }
        return statisticsHourlyDramaStatsMapper.getSummaryByTfids(tfids, startTime, endTime, appid);
    }

    /**
     * 根据创建者获取其所有子账号的汇总数据
     *
     * @param creatorName 创建者用户名
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @return 汇总统计数据
     */
    @Override
    public StatisticsHourlyDramaStats getSummaryByCreator(String creatorName, Date startTime, Date endTime)
    {
        return statisticsHourlyDramaStatsMapper.getSummaryByCreator(creatorName, startTime, endTime);
    }
}
