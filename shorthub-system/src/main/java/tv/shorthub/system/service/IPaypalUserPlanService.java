package tv.shorthub.system.service;

import tv.shorthub.system.mapper.PaypalUserPlanMapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import tv.shorthub.system.domain.PaypalUserPlan;
import tv.shorthub.common.core.service.IBaseService;

/**
 * paypal用户扣费计划Service接口
 *
 * <AUTHOR>
 * @date 2025-06-27
 */
public interface IPaypalUserPlanService extends IBaseService<PaypalUserPlan>
{
    /**
     * 查询paypal用户扣费计划
     *
     * @param id paypal用户扣费计划主键
     * @return paypal用户扣费计划
     */
    public PaypalUserPlan getById(Long id);

    /**
     * 查询paypal用户扣费计划数据汇总
     *
     * @param query paypal用户扣费计划
     * @return paypal用户扣费计划数据汇总
     */
    public PaypalUserPlan getSummary(PaypalUserPlan query);

    /**
     * 查询paypal用户扣费计划列表
     *
     * @param query paypal用户扣费计划
     * @return paypal用户扣费计划集合
     */
    public List<PaypalUserPlan> selectList(PaypalUserPlan query);

    /**
     * 新增paypal用户扣费计划
     *
     * @param paypalUserPlan paypal用户扣费计划
     * @return 结果
     */
    public int insert(PaypalUserPlan paypalUserPlan);

    /**
     * 修改paypal用户扣费计划
     *
     * @param paypalUserPlan paypal用户扣费计划
     * @return 结果
     */
    public int update(PaypalUserPlan paypalUserPlan);

    /**
     * 批量删除paypal用户扣费计划
     *
     * @param ids 需要删除的paypal用户扣费计划主键集合
     * @return 结果
     */
    public int deleteByIds(List<Long> ids);

    /**
     * 删除paypal用户扣费计划信息
     *
     * @param id paypal用户扣费计划主键
     * @return 结果
     */
    public int deleteById(Long id);


    /**
     * 查询自定义分析数据
     *
     * @param query paypal用户扣费计划
     * @return paypal用户扣费计划集合
     */
    public List<PaypalUserPlan> summary(SummaryRequest query);

    PaypalUserPlan allSummary(SummaryRequest query);

    PaypalUserPlanMapper getMapper();
}
