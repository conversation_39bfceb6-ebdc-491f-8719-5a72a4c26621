package tv.shorthub.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import tv.shorthub.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tv.shorthub.system.mapper.CryptoDepositRecordMapper;
import tv.shorthub.system.domain.CryptoDepositRecord;
import tv.shorthub.system.service.ICryptoDepositRecordService;
import tv.shorthub.common.core.service.BaseService;

/**
 * 虚拟货币充值记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
@Service
public class CryptoDepositRecordServiceImpl extends BaseService<CryptoDepositRecord> implements ICryptoDepositRecordService
{
    @Autowired
    private CryptoDepositRecordMapper cryptoDepositRecordMapper;

    @Override
    public CryptoDepositRecordMapper getMapper() {
        return cryptoDepositRecordMapper;
    }

    /**
     * 查询虚拟货币充值记录
     *
     * @param id 虚拟货币充值记录主键
     * @return 虚拟货币充值记录
     */
    @Override
    public CryptoDepositRecord getById(Long id)
    {
        return cryptoDepositRecordMapper.selectById(id);
    }

    /**
     * 查询虚拟货币充值记录列表
     *
     * @param query 虚拟货币充值记录
     * @return 虚拟货币充值记录
     */
    @Override
    public List<CryptoDepositRecord> selectList(CryptoDepositRecord query)
    {
        return cryptoDepositRecordMapper.selectList(new QueryWrapper<>(query));
    }


    /**
     * 查询虚拟货币充值记录数据汇总
     *
     * @param query 虚拟货币充值记录
     * @return 虚拟货币充值记录
     */
    @Override
    public CryptoDepositRecord getSummary(CryptoDepositRecord query)
    {
        return cryptoDepositRecordMapper.getSummary(query);
    }



    /**
     * 查询自定义分析数据
     *
     * @param query 虚拟货币充值记录
     * @return 虚拟货币充值记录
     */
    @Override
    public List<CryptoDepositRecord> summary(SummaryRequest query)
    {
        return cryptoDepositRecordMapper.summary(query);
    }

    @Override
    public CryptoDepositRecord allSummary(SummaryRequest query)
    {
        return cryptoDepositRecordMapper.allSummary(query);
    }

    /**
     * 新增虚拟货币充值记录
     *
     * @param cryptoDepositRecord 虚拟货币充值记录
     * @return 结果
     */
    @Override
    public int insert(CryptoDepositRecord cryptoDepositRecord)
    {
        cryptoDepositRecord.setCreateTime(DateUtils.getNowDate());
        return cryptoDepositRecordMapper.insert(cryptoDepositRecord);
    }

    /**
     * 修改虚拟货币充值记录
     *
     * @param cryptoDepositRecord 虚拟货币充值记录
     * @return 结果
     */
    @Override
    public int update(CryptoDepositRecord cryptoDepositRecord)
    {
        cryptoDepositRecord.setUpdateTime(DateUtils.getNowDate());
        return cryptoDepositRecordMapper.updateById(cryptoDepositRecord);
    }

    /**
     * 批量删除虚拟货币充值记录
     *
     * @param ids 需要删除的虚拟货币充值记录主键
     * @return 结果
     */
    @Override
    public int deleteByIds(List<Long> ids)
    {
        return cryptoDepositRecordMapper.deleteBatchIds(ids);
    }

    /**
     * 删除虚拟货币充值记录信息
     *
     * @param id 虚拟货币充值记录主键
     * @return 结果
     */
    @Override
    public int deleteById(Long id)
    {
        return cryptoDepositRecordMapper.deleteById(id);
    }
}
