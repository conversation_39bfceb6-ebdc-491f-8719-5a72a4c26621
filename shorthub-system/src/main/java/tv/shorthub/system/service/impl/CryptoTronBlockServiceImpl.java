package tv.shorthub.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import tv.shorthub.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tv.shorthub.system.mapper.CryptoTronBlockMapper;
import tv.shorthub.system.domain.CryptoTronBlock;
import tv.shorthub.system.service.ICryptoTronBlockService;
import tv.shorthub.common.core.service.BaseService;

/**
 * TRON区块链区块Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-05
 */
@Service
public class CryptoTronBlockServiceImpl extends BaseService<CryptoTronBlock> implements ICryptoTronBlockService
{
    @Autowired
    private CryptoTronBlockMapper cryptoTronBlockMapper;

    @Override
    public CryptoTronBlockMapper getMapper() {
        return cryptoTronBlockMapper;
    }

    /**
     * 查询TRON区块链区块
     *
     * @param id TRON区块链区块主键
     * @return TRON区块链区块
     */
    @Override
    public CryptoTronBlock getById(Long id)
    {
        return cryptoTronBlockMapper.selectById(id);
    }

    /**
     * 查询TRON区块链区块列表
     *
     * @param query TRON区块链区块
     * @return TRON区块链区块
     */
    @Override
    public List<CryptoTronBlock> selectList(CryptoTronBlock query)
    {
        return cryptoTronBlockMapper.selectList(new QueryWrapper<>(query));
    }


    /**
     * 查询TRON区块链区块数据汇总
     *
     * @param query TRON区块链区块
     * @return TRON区块链区块
     */
    @Override
    public CryptoTronBlock getSummary(CryptoTronBlock query)
    {
        return cryptoTronBlockMapper.getSummary(query);
    }



    /**
     * 查询自定义分析数据
     *
     * @param query TRON区块链区块
     * @return TRON区块链区块
     */
    @Override
    public List<CryptoTronBlock> summary(SummaryRequest query)
    {
        return cryptoTronBlockMapper.summary(query);
    }

    @Override
    public CryptoTronBlock allSummary(SummaryRequest query)
    {
        return cryptoTronBlockMapper.allSummary(query);
    }

    /**
     * 新增TRON区块链区块
     *
     * @param cryptoTronBlock TRON区块链区块
     * @return 结果
     */
    @Override
    public int insert(CryptoTronBlock cryptoTronBlock)
    {
        cryptoTronBlock.setCreateTime(DateUtils.getNowDate());
        return cryptoTronBlockMapper.insert(cryptoTronBlock);
    }

    /**
     * 修改TRON区块链区块
     *
     * @param cryptoTronBlock TRON区块链区块
     * @return 结果
     */
    @Override
    public int update(CryptoTronBlock cryptoTronBlock)
    {
        cryptoTronBlock.setUpdateTime(DateUtils.getNowDate());
        return cryptoTronBlockMapper.updateById(cryptoTronBlock);
    }

    /**
     * 批量删除TRON区块链区块
     *
     * @param ids 需要删除的TRON区块链区块主键
     * @return 结果
     */
    @Override
    public int deleteByIds(List<Long> ids)
    {
        return cryptoTronBlockMapper.deleteBatchIds(ids);
    }

    /**
     * 删除TRON区块链区块信息
     *
     * @param id TRON区块链区块主键
     * @return 结果
     */
    @Override
    public int deleteById(Long id)
    {
        return cryptoTronBlockMapper.deleteById(id);
    }
}
