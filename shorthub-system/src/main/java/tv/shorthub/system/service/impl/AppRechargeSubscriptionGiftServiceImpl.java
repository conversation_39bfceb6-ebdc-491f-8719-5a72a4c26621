package tv.shorthub.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import tv.shorthub.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tv.shorthub.system.mapper.AppRechargeSubscriptionGiftMapper;
import tv.shorthub.system.domain.AppRechargeSubscriptionGift;
import tv.shorthub.system.service.IAppRechargeSubscriptionGiftService;
import tv.shorthub.common.core.service.BaseService;

/**
 * 订阅优惠规则Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-14
 */
@Service
public class AppRechargeSubscriptionGiftServiceImpl extends BaseService<AppRechargeSubscriptionGift> implements IAppRechargeSubscriptionGiftService
{
    @Autowired
    private AppRechargeSubscriptionGiftMapper appRechargeSubscriptionGiftMapper;

    @Override
    public AppRechargeSubscriptionGiftMapper getMapper() {
        return appRechargeSubscriptionGiftMapper;
    }

    /**
     * 查询订阅优惠规则
     *
     * @param id 订阅优惠规则主键
     * @return 订阅优惠规则
     */
    @Override
    public AppRechargeSubscriptionGift getById(Long id)
    {
        return appRechargeSubscriptionGiftMapper.selectById(id);
    }

    /**
     * 查询订阅优惠规则列表
     *
     * @param query 订阅优惠规则
     * @return 订阅优惠规则
     */
    @Override
    public List<AppRechargeSubscriptionGift> selectList(AppRechargeSubscriptionGift query)
    {
        return appRechargeSubscriptionGiftMapper.selectList(new QueryWrapper<>(query));
    }


    /**
     * 查询订阅优惠规则数据汇总
     *
     * @param query 订阅优惠规则
     * @return 订阅优惠规则
     */
    @Override
    public AppRechargeSubscriptionGift getSummary(AppRechargeSubscriptionGift query)
    {
        return appRechargeSubscriptionGiftMapper.getSummary(query);
    }



    /**
     * 查询自定义分析数据
     *
     * @param query 订阅优惠规则
     * @return 订阅优惠规则
     */
    @Override
    public List<AppRechargeSubscriptionGift> summary(SummaryRequest query)
    {
        return appRechargeSubscriptionGiftMapper.summary(query);
    }

    @Override
    public AppRechargeSubscriptionGift allSummary(SummaryRequest query)
    {
        return appRechargeSubscriptionGiftMapper.allSummary(query);
    }

    /**
     * 新增订阅优惠规则
     *
     * @param appRechargeSubscriptionGift 订阅优惠规则
     * @return 结果
     */
    @Override
    public int insert(AppRechargeSubscriptionGift appRechargeSubscriptionGift)
    {
        appRechargeSubscriptionGift.setCreateTime(DateUtils.getNowDate());
        return appRechargeSubscriptionGiftMapper.insert(appRechargeSubscriptionGift);
    }

    /**
     * 修改订阅优惠规则
     *
     * @param appRechargeSubscriptionGift 订阅优惠规则
     * @return 结果
     */
    @Override
    public int update(AppRechargeSubscriptionGift appRechargeSubscriptionGift)
    {
        appRechargeSubscriptionGift.setUpdateTime(DateUtils.getNowDate());
        return appRechargeSubscriptionGiftMapper.updateById(appRechargeSubscriptionGift);
    }

    /**
     * 批量删除订阅优惠规则
     *
     * @param ids 需要删除的订阅优惠规则主键
     * @return 结果
     */
    @Override
    public int deleteByIds(List<Long> ids)
    {
        return appRechargeSubscriptionGiftMapper.deleteBatchIds(ids);
    }

    /**
     * 删除订阅优惠规则信息
     *
     * @param id 订阅优惠规则主键
     * @return 结果
     */
    @Override
    public int deleteById(Long id)
    {
        return appRechargeSubscriptionGiftMapper.deleteById(id);
    }
}
