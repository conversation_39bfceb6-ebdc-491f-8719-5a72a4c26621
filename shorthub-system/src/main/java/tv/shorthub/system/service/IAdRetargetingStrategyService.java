package tv.shorthub.system.service;

import tv.shorthub.system.mapper.AdRetargetingStrategyMapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import tv.shorthub.system.domain.AdRetargetingStrategy;
import tv.shorthub.common.core.service.IBaseService;

/**
 * 回传策略Service接口
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
public interface IAdRetargetingStrategyService extends IBaseService<AdRetargetingStrategy>
{
    /**
     * 查询回传策略
     *
     * @param id 回传策略主键
     * @return 回传策略
     */
    public AdRetargetingStrategy getById(Long id);

    /**
     * 查询回传策略数据汇总
     *
     * @param query 回传策略
     * @return 回传策略数据汇总
     */
    public AdRetargetingStrategy getSummary(AdRetargetingStrategy query);

    /**
     * 查询回传策略列表
     *
     * @param query 回传策略
     * @return 回传策略集合
     */
    public List<AdRetargetingStrategy> selectList(AdRetargetingStrategy query);

    /**
     * 新增回传策略
     *
     * @param adRetargetingStrategy 回传策略
     * @return 结果
     */
    public int insert(AdRetargetingStrategy adRetargetingStrategy);

    /**
     * 新增回传策略
     *
     * @param adRetargetingStrategy 回传策略
     * @return 结果
     */
    public int clone(AdRetargetingStrategy adRetargetingStrategy);

    /**
     * 修改回传策略
     *
     * @param adRetargetingStrategy 回传策略
     * @return 结果
     */
    public int update(AdRetargetingStrategy adRetargetingStrategy);

    /**
     * 批量删除回传策略
     *
     * @param ids 需要删除的回传策略主键集合
     * @return 结果
     */
    public int deleteByIds(List<Long> ids);

    /**
     * 删除回传策略信息
     *
     * @param id 回传策略主键
     * @return 结果
     */
    public int deleteById(Long id);


    /**
     * 查询自定义分析数据
     *
     * @param query 回传策略
     * @return 回传策略集合
     */
    public List<AdRetargetingStrategy> summary(SummaryRequest query);

    AdRetargetingStrategy allSummary(SummaryRequest query);

    AdRetargetingStrategyMapper getMapper();
}
