package tv.shorthub.system.service;

import tv.shorthub.system.mapper.AppDramaContentsSerialMapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import tv.shorthub.system.domain.AppDramaContentsSerial;
import tv.shorthub.common.core.service.IBaseService;

/**
 * 剧集Service接口
 *
 * <AUTHOR>
 * @date 2025-05-08
 */
public interface IAppDramaContentsSerialService extends IBaseService<AppDramaContentsSerial>
{
    /**
     * 查询剧集
     *
     * @param id 剧集主键
     * @return 剧集
     */
    public AppDramaContentsSerial getById(Long id);

    /**
     * 查询剧集数据汇总
     *
     * @param query 剧集
     * @return 剧集数据汇总
     */
    public AppDramaContentsSerial getSummary(AppDramaContentsSerial query);

    /**
     * 查询剧集列表
     *
     * @param query 剧集
     * @return 剧集集合
     */
    public List<AppDramaContentsSerial> selectList(AppDramaContentsSerial query);

    /**
     * 新增剧集
     *
     * @param appDramaContentsSerial 剧集
     * @return 结果
     */
    public int insert(AppDramaContentsSerial appDramaContentsSerial);

    /**
     * 修改剧集
     *
     * @param appDramaContentsSerial 剧集
     * @return 结果
     */
    public int update(AppDramaContentsSerial appDramaContentsSerial);

    /**
     * 批量删除剧集
     *
     * @param ids 需要删除的剧集主键集合
     * @return 结果
     */
    public int deleteByIds(List<Long> ids);

    /**
     * 删除剧集信息
     *
     * @param id 剧集主键
     * @return 结果
     */
    public int deleteById(Long id);


    /**
     * 查询自定义分析数据
     *
     * @param query 剧集
     * @return 剧集集合
     */
    public List<AppDramaContentsSerial> summary(SummaryRequest query);

    AppDramaContentsSerial allSummary(SummaryRequest query);

    AppDramaContentsSerialMapper getMapper();
    int updateOrder(List<AppDramaContentsSerial> list);

    void updateUrl(AppDramaContentsSerial appDramaContentsSerial);
}
