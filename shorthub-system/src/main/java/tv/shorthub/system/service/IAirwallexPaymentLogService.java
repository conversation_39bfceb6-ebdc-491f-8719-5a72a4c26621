package tv.shorthub.system.service;

import com.alibaba.fastjson2.JSONObject;
import tv.shorthub.system.mapper.AirwallexPaymentLogMapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import tv.shorthub.system.domain.AirwallexPaymentLog;
import tv.shorthub.common.core.service.IBaseService;

/**
 * airwallex支付日志Service接口
 *
 * <AUTHOR>
 * @date 2025-07-17
 */
public interface IAirwallexPaymentLogService extends IBaseService<AirwallexPaymentLog>
{
    /**
     * 查询airwallex支付日志
     *
     * @param id airwallex支付日志主键
     * @return airwallex支付日志
     */
    public AirwallexPaymentLog getById(Long id);

    /**
     * 查询airwallex支付日志数据汇总
     *
     * @param query airwallex支付日志
     * @return airwallex支付日志数据汇总
     */
    public AirwallexPaymentLog getSummary(AirwallexPaymentLog query);

    /**
     * 查询airwallex支付日志列表
     *
     * @param query airwallex支付日志
     * @return airwallex支付日志集合
     */
    public List<AirwallexPaymentLog> selectList(AirwallexPaymentLog query);

    /**
     * 新增airwallex支付日志
     *
     * @param airwallexPaymentLog airwallex支付日志
     * @return 结果
     */
    public int insert(AirwallexPaymentLog airwallexPaymentLog);

    /**
     * 修改airwallex支付日志
     *
     * @param airwallexPaymentLog airwallex支付日志
     * @return 结果
     */
    public int update(AirwallexPaymentLog airwallexPaymentLog);

    /**
     * 批量删除airwallex支付日志
     *
     * @param ids 需要删除的airwallex支付日志主键集合
     * @return 结果
     */
    public int deleteByIds(List<Long> ids);

    /**
     * 删除airwallex支付日志信息
     *
     * @param id airwallex支付日志主键
     * @return 结果
     */
    public int deleteById(Long id);


    /**
     * 查询自定义分析数据
     *
     * @param query airwallex支付日志
     * @return airwallex支付日志集合
     */
    public List<AirwallexPaymentLog> summary(SummaryRequest query);

    AirwallexPaymentLog allSummary(SummaryRequest query);

    AirwallexPaymentLogMapper getMapper();

    JSONObject processUnsubscribe(String orderNo, String unsubscribe);
}
