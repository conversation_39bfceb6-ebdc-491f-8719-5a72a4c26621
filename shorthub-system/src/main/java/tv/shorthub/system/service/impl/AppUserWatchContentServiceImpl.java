package tv.shorthub.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import tv.shorthub.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tv.shorthub.system.mapper.AppUserWatchContentMapper;
import tv.shorthub.system.domain.AppUserWatchContent;
import tv.shorthub.system.service.IAppUserWatchContentService;
import tv.shorthub.common.core.service.BaseService;

/**
 * 观看剧目记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-10
 */
@Service
public class AppUserWatchContentServiceImpl extends BaseService<AppUserWatchContent> implements IAppUserWatchContentService
{
    @Autowired
    private AppUserWatchContentMapper appUserWatchContentMapper;

    @Override
    public AppUserWatchContentMapper getMapper() {
        return appUserWatchContentMapper;
    }

    /**
     * 查询观看剧目记录
     *
     * @param id 观看剧目记录主键
     * @return 观看剧目记录
     */
    @Override
    public AppUserWatchContent getById(Long id)
    {
        return appUserWatchContentMapper.selectById(id);
    }

    /**
     * 查询观看剧目记录列表
     *
     * @param query 观看剧目记录
     * @return 观看剧目记录
     */
    @Override
    public List<AppUserWatchContent> selectList(AppUserWatchContent query)
    {
        return appUserWatchContentMapper.selectList(new QueryWrapper<>(query).orderByDesc("update_time"));
    }


    /**
     * 查询观看剧目记录数据汇总
     *
     * @param query 观看剧目记录
     * @return 观看剧目记录
     */
    @Override
    public AppUserWatchContent getSummary(AppUserWatchContent query)
    {
        return appUserWatchContentMapper.getSummary(query);
    }



    /**
     * 查询自定义分析数据
     *
     * @param query 观看剧目记录
     * @return 观看剧目记录
     */
    @Override
    public List<AppUserWatchContent> summary(SummaryRequest query)
    {
        return appUserWatchContentMapper.summary(query);
    }

    @Override
    public AppUserWatchContent allSummary(SummaryRequest query)
    {
        return appUserWatchContentMapper.allSummary(query);
    }

    /**
     * 新增观看剧目记录
     *
     * @param appUserWatchContent 观看剧目记录
     * @return 结果
     */
    @Override
    public int insert(AppUserWatchContent appUserWatchContent)
    {
        appUserWatchContent.setCreateTime(DateUtils.getNowDate());
        return appUserWatchContentMapper.insert(appUserWatchContent);
    }

    /**
     * 修改观看剧目记录
     *
     * @param appUserWatchContent 观看剧目记录
     * @return 结果
     */
    @Override
    public int update(AppUserWatchContent appUserWatchContent)
    {
        appUserWatchContent.setUpdateTime(DateUtils.getNowDate());
        return appUserWatchContentMapper.updateById(appUserWatchContent);
    }

    /**
     * 批量删除观看剧目记录
     *
     * @param ids 需要删除的观看剧目记录主键
     * @return 结果
     */
    @Override
    public int deleteByIds(List<Long> ids)
    {
        return appUserWatchContentMapper.deleteBatchIds(ids);
    }

    /**
     * 删除观看剧目记录信息
     *
     * @param id 观看剧目记录主键
     * @return 结果
     */
    @Override
    public int deleteById(Long id)
    {
        return appUserWatchContentMapper.deleteById(id);
    }
}
