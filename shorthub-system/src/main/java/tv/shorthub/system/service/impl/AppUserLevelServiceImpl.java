package tv.shorthub.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import tv.shorthub.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tv.shorthub.system.mapper.AppUserLevelMapper;
import tv.shorthub.system.domain.AppUserLevel;
import tv.shorthub.system.service.IAppUserLevelService;
import tv.shorthub.common.core.service.BaseService;

/**
 * 会员级别Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-14
 */
@Service
public class AppUserLevelServiceImpl extends BaseService<AppUserLevel> implements IAppUserLevelService
{
    @Autowired
    private AppUserLevelMapper appUserLevelMapper;

    @Override
    public AppUserLevelMapper getMapper() {
        return appUserLevelMapper;
    }

    /**
     * 查询会员级别
     *
     * @param id 会员级别主键
     * @return 会员级别
     */
    @Override
    public AppUserLevel getById(Long id)
    {
        return appUserLevelMapper.selectById(id);
    }

    /**
     * 查询会员级别列表
     *
     * @param query 会员级别
     * @return 会员级别
     */
    @Override
    public List<AppUserLevel> selectList(AppUserLevel query)
    {
        return appUserLevelMapper.selectList(new QueryWrapper<>(query));
    }


    /**
     * 查询会员级别数据汇总
     *
     * @param query 会员级别
     * @return 会员级别
     */
    @Override
    public AppUserLevel getSummary(AppUserLevel query)
    {
        return appUserLevelMapper.getSummary(query);
    }



    /**
     * 查询自定义分析数据
     *
     * @param query 会员级别
     * @return 会员级别
     */
    @Override
    public List<AppUserLevel> summary(SummaryRequest query)
    {
        return appUserLevelMapper.summary(query);
    }

    @Override
    public AppUserLevel allSummary(SummaryRequest query)
    {
        return appUserLevelMapper.allSummary(query);
    }

    /**
     * 新增会员级别
     *
     * @param appUserLevel 会员级别
     * @return 结果
     */
    @Override
    public int insert(AppUserLevel appUserLevel)
    {
        appUserLevel.setCreateTime(DateUtils.getNowDate());
        return appUserLevelMapper.insert(appUserLevel);
    }

    /**
     * 修改会员级别
     *
     * @param appUserLevel 会员级别
     * @return 结果
     */
    @Override
    public int update(AppUserLevel appUserLevel)
    {
        appUserLevel.setUpdateTime(DateUtils.getNowDate());
        return appUserLevelMapper.updateById(appUserLevel);
    }

    /**
     * 批量删除会员级别
     *
     * @param ids 需要删除的会员级别主键
     * @return 结果
     */
    @Override
    public int deleteByIds(List<Long> ids)
    {
        return appUserLevelMapper.deleteBatchIds(ids);
    }

    /**
     * 删除会员级别信息
     *
     * @param id 会员级别主键
     * @return 结果
     */
    @Override
    public int deleteById(Long id)
    {
        return appUserLevelMapper.deleteById(id);
    }
}
