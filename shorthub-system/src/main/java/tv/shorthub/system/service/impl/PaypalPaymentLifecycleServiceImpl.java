package tv.shorthub.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import tv.shorthub.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tv.shorthub.system.mapper.PaypalPaymentLifecycleMapper;
import tv.shorthub.system.domain.PaypalPaymentLifecycle;
import tv.shorthub.system.service.IPaypalPaymentLifecycleService;
import tv.shorthub.common.core.service.BaseService;

/**
 * paypal组件生命周期Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-04
 */
@Service
public class PaypalPaymentLifecycleServiceImpl extends BaseService<PaypalPaymentLifecycle> implements IPaypalPaymentLifecycleService
{
    @Autowired
    private PaypalPaymentLifecycleMapper paypalPaymentLifecycleMapper;

    @Override
    public PaypalPaymentLifecycleMapper getMapper() {
        return paypalPaymentLifecycleMapper;
    }

    /**
     * 查询paypal组件生命周期
     *
     * @param id paypal组件生命周期主键
     * @return paypal组件生命周期
     */
    @Override
    public PaypalPaymentLifecycle getById(Long id)
    {
        return paypalPaymentLifecycleMapper.selectById(id);
    }

    /**
     * 查询paypal组件生命周期列表
     *
     * @param query paypal组件生命周期
     * @return paypal组件生命周期
     */
    @Override
    public List<PaypalPaymentLifecycle> selectList(PaypalPaymentLifecycle query)
    {
        QueryWrapper<PaypalPaymentLifecycle> queryWrapper = new QueryWrapper<>(query);
        queryWrapper.orderByDesc("id");
        return paypalPaymentLifecycleMapper.selectList(queryWrapper);
    }


    /**
     * 查询paypal组件生命周期数据汇总
     *
     * @param query paypal组件生命周期
     * @return paypal组件生命周期
     */
    @Override
    public PaypalPaymentLifecycle getSummary(PaypalPaymentLifecycle query)
    {
        return paypalPaymentLifecycleMapper.getSummary(query);
    }



    /**
     * 查询自定义分析数据
     *
     * @param query paypal组件生命周期
     * @return paypal组件生命周期
     */
    @Override
    public List<PaypalPaymentLifecycle> summary(SummaryRequest query)
    {
        return paypalPaymentLifecycleMapper.summary(query);
    }

    @Override
    public PaypalPaymentLifecycle allSummary(SummaryRequest query)
    {
        return paypalPaymentLifecycleMapper.allSummary(query);
    }

    /**
     * 新增paypal组件生命周期
     *
     * @param paypalPaymentLifecycle paypal组件生命周期
     * @return 结果
     */
    @Override
    public int insert(PaypalPaymentLifecycle paypalPaymentLifecycle)
    {
        paypalPaymentLifecycle.setCreateTime(DateUtils.getNowDate());
        return paypalPaymentLifecycleMapper.insert(paypalPaymentLifecycle);
    }

    /**
     * 修改paypal组件生命周期
     *
     * @param paypalPaymentLifecycle paypal组件生命周期
     * @return 结果
     */
    @Override
    public int update(PaypalPaymentLifecycle paypalPaymentLifecycle)
    {
        paypalPaymentLifecycle.setUpdateTime(DateUtils.getNowDate());
        return paypalPaymentLifecycleMapper.updateById(paypalPaymentLifecycle);
    }

    /**
     * 批量删除paypal组件生命周期
     *
     * @param ids 需要删除的paypal组件生命周期主键
     * @return 结果
     */
    @Override
    public int deleteByIds(List<Long> ids)
    {
        return paypalPaymentLifecycleMapper.deleteBatchIds(ids);
    }

    /**
     * 删除paypal组件生命周期信息
     *
     * @param id paypal组件生命周期主键
     * @return 结果
     */
    @Override
    public int deleteById(Long id)
    {
        return paypalPaymentLifecycleMapper.deleteById(id);
    }
}
