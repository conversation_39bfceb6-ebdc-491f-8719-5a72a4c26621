package tv.shorthub.system.service;

import tv.shorthub.system.mapper.AirwallexUserPlanMapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import tv.shorthub.system.domain.AirwallexUserPlan;
import tv.shorthub.common.core.service.IBaseService;

/**
 * airwallex用户扣费计划Service接口
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
public interface IAirwallexUserPlanService extends IBaseService<AirwallexUserPlan>
{
    /**
     * 查询airwallex用户扣费计划
     *
     * @param id airwallex用户扣费计划主键
     * @return airwallex用户扣费计划
     */
    public AirwallexUserPlan getById(Long id);

    /**
     * 查询airwallex用户扣费计划数据汇总
     *
     * @param query airwallex用户扣费计划
     * @return airwallex用户扣费计划数据汇总
     */
    public AirwallexUserPlan getSummary(AirwallexUserPlan query);

    /**
     * 查询airwallex用户扣费计划列表
     *
     * @param query airwallex用户扣费计划
     * @return airwallex用户扣费计划集合
     */
    public List<AirwallexUserPlan> selectList(AirwallexUserPlan query);

    /**
     * 新增airwallex用户扣费计划
     *
     * @param airwallexUserPlan airwallex用户扣费计划
     * @return 结果
     */
    public int insert(AirwallexUserPlan airwallexUserPlan);

    /**
     * 修改airwallex用户扣费计划
     *
     * @param airwallexUserPlan airwallex用户扣费计划
     * @return 结果
     */
    public int update(AirwallexUserPlan airwallexUserPlan);

    /**
     * 批量删除airwallex用户扣费计划
     *
     * @param ids 需要删除的airwallex用户扣费计划主键集合
     * @return 结果
     */
    public int deleteByIds(List<Long> ids);

    /**
     * 删除airwallex用户扣费计划信息
     *
     * @param id airwallex用户扣费计划主键
     * @return 结果
     */
    public int deleteById(Long id);


    /**
     * 查询自定义分析数据
     *
     * @param query airwallex用户扣费计划
     * @return airwallex用户扣费计划集合
     */
    public List<AirwallexUserPlan> summary(SummaryRequest query);

    AirwallexUserPlan allSummary(SummaryRequest query);

    AirwallexUserPlanMapper getMapper();
}
