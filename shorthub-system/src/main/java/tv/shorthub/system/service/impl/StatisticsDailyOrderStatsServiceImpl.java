package tv.shorthub.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import tv.shorthub.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tv.shorthub.system.mapper.StatisticsDailyOrderStatsMapper;
import tv.shorthub.system.domain.StatisticsDailyOrderStats;
import tv.shorthub.system.service.IStatisticsDailyOrderStatsService;
import tv.shorthub.common.core.service.BaseService;
import java.util.Date;
import tv.shorthub.common.utils.SecurityUtils;
import tv.shorthub.system.utils.StatisticsPermissionUtils;

/**
 * 每日订单统计Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
@Service
public class StatisticsDailyOrderStatsServiceImpl extends BaseService<StatisticsDailyOrderStats> implements IStatisticsDailyOrderStatsService
{
    @Autowired
    private StatisticsDailyOrderStatsMapper statisticsDailyOrderStatsMapper;

    @Autowired
    private StatisticsPermissionUtils permissionUtils;

    @Override
    public StatisticsDailyOrderStatsMapper getMapper() {
        return statisticsDailyOrderStatsMapper;
    }

    /**
     * 查询每日订单统计
     *
     * @param id 每日订单统计主键
     * @return 每日订单统计
     */
    @Override
    public StatisticsDailyOrderStats getById(Long id)
    {
        return statisticsDailyOrderStatsMapper.selectById(id);
    }

    /**
     * 查询每日订单统计列表
     *
     * @param query 每日订单统计
     * @return 每日订单统计
     */
    @Override
    public List<StatisticsDailyOrderStats> selectList(StatisticsDailyOrderStats query)
    {
        QueryWrapper<StatisticsDailyOrderStats> wrapper = new QueryWrapper<>(query);
        // 按时间倒序排列，最新数据在前
        wrapper.orderByDesc("stat_date");
        // 权限过滤
        permissionUtils.filterByDeliverPermission(wrapper);
        return statisticsDailyOrderStatsMapper.selectList(wrapper);
    }


    /**
     * 查询自定义分析数据
     *
     * @param query 每日订单统计
     * @return 每日订单统计
     */
    @Override
    public List<StatisticsDailyOrderStats> summary(SummaryRequest query)
    {
        return statisticsDailyOrderStatsMapper.summary(query);
    }

    @Override
    public StatisticsDailyOrderStats allSummary(SummaryRequest query)
    {
        return statisticsDailyOrderStatsMapper.allSummary(query);
    }

    /**
     * 新增每日订单统计
     *
     * @param statisticsDailyOrderStats 每日订单统计
     * @return 结果
     */
    @Override
    public int insert(StatisticsDailyOrderStats statisticsDailyOrderStats)
    {
        statisticsDailyOrderStats.setCreateTime(DateUtils.getNowDate());
        return statisticsDailyOrderStatsMapper.insert(statisticsDailyOrderStats);
    }

    /**
     * 修改每日订单统计
     *
     * @param statisticsDailyOrderStats 每日订单统计
     * @return 结果
     */
    @Override
    public int update(StatisticsDailyOrderStats statisticsDailyOrderStats)
    {
        statisticsDailyOrderStats.setUpdateTime(DateUtils.getNowDate());
        return statisticsDailyOrderStatsMapper.updateById(statisticsDailyOrderStats);
    }

    /**
     * 批量删除每日订单统计
     *
     * @param ids 需要删除的每日订单统计主键
     * @return 结果
     */
    @Override
    public int deleteByIds(List<Long> ids)
    {
        return statisticsDailyOrderStatsMapper.deleteBatchIds(ids);
    }

    /**
     * 删除每日订单统计信息
     *
     * @param id 每日订单统计主键
     * @return 结果
     */
    @Override
    public int deleteById(Long id)
    {
        return statisticsDailyOrderStatsMapper.deleteById(id);
    }

    /**
     * 根据时间范围查询每日订单统计列表
     * @param query 查询条件
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计数据列表
     */
    public List<StatisticsDailyOrderStats> selectListByTimeRange(StatisticsDailyOrderStats query, Date startTime, Date endTime) {
        QueryWrapper<StatisticsDailyOrderStats> wrapper = new QueryWrapper<>(query);
        wrapper.ge("stat_date", startTime);
        wrapper.lt("stat_date", endTime);
        wrapper.orderByDesc("stat_date");
        permissionUtils.filterByDeliverPermission(wrapper);
        return statisticsDailyOrderStatsMapper.selectList(wrapper);
    }

    @Override
    public StatisticsDailyOrderStats getSummaryByTfids(List<String> tfids, Date startTime, Date endTime, String appid, String orderChannel, String timezone) {
        // 如果是业务主账号（但不是系统管理员），强制使用自己的 appid
        if (SecurityUtils.isBusinessAdmin() && !SecurityUtils.isSystemAdmin()) {
            appid = SecurityUtils.getAppid();
        }
        return statisticsDailyOrderStatsMapper.getSummaryByTfids(tfids, startTime, endTime, appid, orderChannel, timezone);
    }


    @Override
    public StatisticsDailyOrderStats getSummaryByCreator(String creator, Date startTime, Date endTime, String orderChannel, String timezone) {
        // 主账号自己看自己appid下所有数据（含自然流量）
        if (SecurityUtils.isBusinessAdmin() && !SecurityUtils.isSystemAdmin() &&
            (org.apache.commons.lang3.StringUtils.isEmpty(creator) || SecurityUtils.getUsername().equals(creator))) {
            if (startTime != null && endTime != null) {
                return statisticsDailyOrderStatsMapper.getRangeSummary(startTime, endTime, SecurityUtils.getAppid(), null, orderChannel, timezone);
            } else {
                StatisticsDailyOrderStats query = new StatisticsDailyOrderStats();
                query.setAppid(SecurityUtils.getAppid());
                return statisticsDailyOrderStatsMapper.getSummary(query);
            }
        }
        // 其他情况：系统管理员或主账号看指定子账号，调用新的getSummaryByCreator方法
        return statisticsDailyOrderStatsMapper.getSummaryByCreator(creator, startTime, endTime, orderChannel, timezone);
    }

    @Override
    public StatisticsDailyOrderStats getRangeSummary(Date startTime, Date endTime, String appid, String tfid, String orderChannel, String timezone) {
        // 主账号强制限定 appid
        if (SecurityUtils.isBusinessAdmin() && !SecurityUtils.isSystemAdmin()) {
            appid = SecurityUtils.getAppid();
        }
        return statisticsDailyOrderStatsMapper.getRangeSummary(startTime, endTime, appid, tfid, orderChannel, timezone);
    }

}
