package tv.shorthub.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import tv.shorthub.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tv.shorthub.system.mapper.CryptoSolanaBlockMapper;
import tv.shorthub.system.domain.CryptoSolanaBlock;
import tv.shorthub.system.service.ICryptoSolanaBlockService;
import tv.shorthub.common.core.service.BaseService;

/**
 * SOLANA区块链区块Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-05
 */
@Service
public class CryptoSolanaBlockServiceImpl extends BaseService<CryptoSolanaBlock> implements ICryptoSolanaBlockService
{
    @Autowired
    private CryptoSolanaBlockMapper cryptoSolanaBlockMapper;

    @Override
    public CryptoSolanaBlockMapper getMapper() {
        return cryptoSolanaBlockMapper;
    }

    /**
     * 查询SOLANA区块链区块
     *
     * @param id SOLANA区块链区块主键
     * @return SOLANA区块链区块
     */
    @Override
    public CryptoSolanaBlock getById(Long id)
    {
        return cryptoSolanaBlockMapper.selectById(id);
    }

    /**
     * 查询SOLANA区块链区块列表
     *
     * @param query SOLANA区块链区块
     * @return SOLANA区块链区块
     */
    @Override
    public List<CryptoSolanaBlock> selectList(CryptoSolanaBlock query)
    {
        return cryptoSolanaBlockMapper.selectList(new QueryWrapper<>(query));
    }


    /**
     * 查询SOLANA区块链区块数据汇总
     *
     * @param query SOLANA区块链区块
     * @return SOLANA区块链区块
     */
    @Override
    public CryptoSolanaBlock getSummary(CryptoSolanaBlock query)
    {
        return cryptoSolanaBlockMapper.getSummary(query);
    }



    /**
     * 查询自定义分析数据
     *
     * @param query SOLANA区块链区块
     * @return SOLANA区块链区块
     */
    @Override
    public List<CryptoSolanaBlock> summary(SummaryRequest query)
    {
        return cryptoSolanaBlockMapper.summary(query);
    }

    @Override
    public CryptoSolanaBlock allSummary(SummaryRequest query)
    {
        return cryptoSolanaBlockMapper.allSummary(query);
    }

    /**
     * 新增SOLANA区块链区块
     *
     * @param cryptoSolanaBlock SOLANA区块链区块
     * @return 结果
     */
    @Override
    public int insert(CryptoSolanaBlock cryptoSolanaBlock)
    {
        cryptoSolanaBlock.setCreateTime(DateUtils.getNowDate());
        return cryptoSolanaBlockMapper.insert(cryptoSolanaBlock);
    }

    /**
     * 修改SOLANA区块链区块
     *
     * @param cryptoSolanaBlock SOLANA区块链区块
     * @return 结果
     */
    @Override
    public int update(CryptoSolanaBlock cryptoSolanaBlock)
    {
        cryptoSolanaBlock.setUpdateTime(DateUtils.getNowDate());
        return cryptoSolanaBlockMapper.updateById(cryptoSolanaBlock);
    }

    /**
     * 批量删除SOLANA区块链区块
     *
     * @param ids 需要删除的SOLANA区块链区块主键
     * @return 结果
     */
    @Override
    public int deleteByIds(List<Long> ids)
    {
        return cryptoSolanaBlockMapper.deleteBatchIds(ids);
    }

    /**
     * 删除SOLANA区块链区块信息
     *
     * @param id SOLANA区块链区块主键
     * @return 结果
     */
    @Override
    public int deleteById(Long id)
    {
        return cryptoSolanaBlockMapper.deleteById(id);
    }
}
