package tv.shorthub.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import tv.shorthub.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tv.shorthub.system.mapper.CryptoPaymentConfigMapper;
import tv.shorthub.system.domain.CryptoPaymentConfig;
import tv.shorthub.system.service.ICryptoPaymentConfigService;
import tv.shorthub.common.core.service.BaseService;

/**
 * 虚拟货币支付配置Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
@Service
public class CryptoPaymentConfigServiceImpl extends BaseService<CryptoPaymentConfig> implements ICryptoPaymentConfigService
{
    @Autowired
    private CryptoPaymentConfigMapper cryptoPaymentConfigMapper;

    @Override
    public CryptoPaymentConfigMapper getMapper() {
        return cryptoPaymentConfigMapper;
    }

    /**
     * 查询虚拟货币支付配置
     *
     * @param id 虚拟货币支付配置主键
     * @return 虚拟货币支付配置
     */
    @Override
    public CryptoPaymentConfig getById(Long id)
    {
        return cryptoPaymentConfigMapper.selectById(id);
    }

    /**
     * 查询虚拟货币支付配置列表
     *
     * @param query 虚拟货币支付配置
     * @return 虚拟货币支付配置
     */
    @Override
    public List<CryptoPaymentConfig> selectList(CryptoPaymentConfig query)
    {
        return cryptoPaymentConfigMapper.selectList(new QueryWrapper<>(query));
    }


    /**
     * 查询虚拟货币支付配置数据汇总
     *
     * @param query 虚拟货币支付配置
     * @return 虚拟货币支付配置
     */
    @Override
    public CryptoPaymentConfig getSummary(CryptoPaymentConfig query)
    {
        return cryptoPaymentConfigMapper.getSummary(query);
    }



    /**
     * 查询自定义分析数据
     *
     * @param query 虚拟货币支付配置
     * @return 虚拟货币支付配置
     */
    @Override
    public List<CryptoPaymentConfig> summary(SummaryRequest query)
    {
        return cryptoPaymentConfigMapper.summary(query);
    }

    @Override
    public CryptoPaymentConfig allSummary(SummaryRequest query)
    {
        return cryptoPaymentConfigMapper.allSummary(query);
    }

    /**
     * 新增虚拟货币支付配置
     *
     * @param cryptoPaymentConfig 虚拟货币支付配置
     * @return 结果
     */
    @Override
    public int insert(CryptoPaymentConfig cryptoPaymentConfig)
    {
        cryptoPaymentConfig.setCreateTime(DateUtils.getNowDate());
        return cryptoPaymentConfigMapper.insert(cryptoPaymentConfig);
    }

    /**
     * 修改虚拟货币支付配置
     *
     * @param cryptoPaymentConfig 虚拟货币支付配置
     * @return 结果
     */
    @Override
    public int update(CryptoPaymentConfig cryptoPaymentConfig)
    {
        cryptoPaymentConfig.setUpdateTime(DateUtils.getNowDate());
        return cryptoPaymentConfigMapper.updateById(cryptoPaymentConfig);
    }

    /**
     * 批量删除虚拟货币支付配置
     *
     * @param ids 需要删除的虚拟货币支付配置主键
     * @return 结果
     */
    @Override
    public int deleteByIds(List<Long> ids)
    {
        return cryptoPaymentConfigMapper.deleteBatchIds(ids);
    }

    /**
     * 删除虚拟货币支付配置信息
     *
     * @param id 虚拟货币支付配置主键
     * @return 结果
     */
    @Override
    public int deleteById(Long id)
    {
        return cryptoPaymentConfigMapper.deleteById(id);
    }
}
