package tv.shorthub.system.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import tv.shorthub.system.domain.StatisticsHourlySubscriptionStats;

import java.math.BigDecimal;

/**
 * 订阅统计汇总数据传输对象
 * 继承自基础统计实体，并增加了额外的预测性指标
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SubscriptionSummaryDto extends StatisticsHourlySubscriptionStats {

    /**
     * 近7日待扣款金额
     */
    private BigDecimal upcomingSevenDayAmount;

    /**
     * 近30日待扣款金额
     */
    private BigDecimal upcomingThirtyDayAmount;
} 