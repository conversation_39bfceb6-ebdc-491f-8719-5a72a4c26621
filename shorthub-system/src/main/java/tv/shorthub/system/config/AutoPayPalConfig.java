package tv.shorthub.system.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.event.ApplicationStartedEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;
import tv.shorthub.paypal.model.Account;
import tv.shorthub.paypal.service.impl.PayPalAccountServiceImpl;
import tv.shorthub.system.domain.PaypalPaymentConfig;
import tv.shorthub.system.service.IPaypalPaymentConfigService;

import java.util.List;

@Slf4j
@Component
public class AutoPayPalConfig implements ApplicationListener<ApplicationStartedEvent> {

    @Autowired
    IPaypalPaymentConfigService paypalPaymentConfigService;

    @Autowired
    PayPalAccountServiceImpl payPalAccountService;

    public String getBaseUrl(String mode) {
        return "live".equals(mode) ?
                "https://api.paypal.com" :
                "https://api.sandbox.paypal.com";
    }

    public boolean isSandbox(String mode) {
        return !"live".equals(mode);
    }

    @Override
    public void onApplicationEvent(ApplicationStartedEvent event) {

        refresh();
    }

    public void refresh() {
        PaypalPaymentConfig query = new PaypalPaymentConfig();
        query.setEnable(true);
        List<PaypalPaymentConfig> paymentConfigs = paypalPaymentConfigService.selectList(query);

        for (PaypalPaymentConfig paymentConfig : paymentConfigs) {
            Account accountConfig = new Account();
            accountConfig.setId(paymentConfig.getClientId());
            accountConfig.setBaseUrl(getBaseUrl(paymentConfig.getMode()));
            accountConfig.setClientId(paymentConfig.getClientId());
            accountConfig.setClientSecret(paymentConfig.getClientSecret());
            accountConfig.setSandbox(isSandbox(paymentConfig.getMode()));
            accountConfig.setBrandName("ShortHub.TV");
            if (null != paymentConfig.getExtendJson() && paymentConfig.getExtendJson().containsKey("proxy")) {
                accountConfig.setProxyHost(paymentConfig.getExtendJson().getJSONObject("proxy").getString("host"));
                accountConfig.setProxyPort(paymentConfig.getExtendJson().getJSONObject("proxy").getInteger("port"));
                accountConfig.setProxyUser(paymentConfig.getExtendJson().getJSONObject("proxy").getString("user"));
                accountConfig.setProxyPassword(paymentConfig.getExtendJson().getJSONObject("proxy").getString("password"));
                log.info("PayPal {}, 代理配置: {}", paymentConfig.getClientId(), paymentConfig.getExtendJson().getJSONObject("proxy"));
            }

//        accountConfig.setMerchantEmail("<EMAIL>");
            payPalAccountService.registerAccount(accountConfig);
        }
        payPalAccountService.setDefaultAccount(paymentConfigs.stream().filter(f -> f.getMode().equals("sandbox")).findFirst().orElse(paymentConfigs.getFirst()).getClientId());
    }
}
