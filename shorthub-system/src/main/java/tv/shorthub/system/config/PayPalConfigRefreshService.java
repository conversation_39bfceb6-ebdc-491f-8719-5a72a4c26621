package tv.shorthub.system.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tv.shorthub.common.annotation.CacheRefresh;
import tv.shorthub.common.core.cache.refresh.RefreshService;

/**
 * PayPal配置刷新服务
 * 实现RefreshService接口，用于处理PP_REFRESH主题消息
 *
 * <AUTHOR>
 */
@CacheRefresh(key = "PP_REFRESH", seconds = 0)
@Component
@Slf4j
public class PayPalConfigRefreshService implements RefreshService {

    @Autowired
    private AutoPayPalConfig autoPayPalConfig;

    @Override
    public void start() {
        try {
            log.info("开始刷新PayPal配置...");
            
            // 调用AutoPayPalConfig的refresh方法更新PayPal配置
            autoPayPalConfig.refresh();
            
            log.info("PayPal配置刷新完成");
        } catch (Exception e) {
            log.error("PayPal配置刷新失败: {}", e.getMessage(), e);
            throw new RuntimeException("PayPal配置刷新失败", e);
        }
    }
} 