package tv.shorthub.system.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.event.ApplicationStartedEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;
import tv.shorthub.airwallex.config.AirwallexMultiClientConfig;
import tv.shorthub.paypal.model.Account;
import tv.shorthub.paypal.service.impl.PayPalAccountServiceImpl;
import tv.shorthub.system.domain.AirwallexPaymentConfig;
import tv.shorthub.system.domain.PaypalPaymentConfig;
import tv.shorthub.system.service.IAirwallexPaymentConfigService;
import tv.shorthub.system.service.IPaypalPaymentConfigService;

import java.util.List;


@Component
public class AutoAirwallexConfig implements ApplicationListener<ApplicationStartedEvent> {

    @Autowired
    IAirwallexPaymentConfigService airwallexPaymentConfigService;

    @Autowired
    AirwallexMultiClientConfig airwallexMultiClientConfig;

    private String getApiBaseUrl(boolean sandbox) {
        return sandbox ? "https://api-demo.airwallex.com" : "https://api.airwallex.com";
    }


    @Override
    public void onApplicationEvent(ApplicationStartedEvent event) {

        AirwallexPaymentConfig query = new AirwallexPaymentConfig();
        query.setEnable(true);
        List<AirwallexPaymentConfig> paymentConfigs = airwallexPaymentConfigService.selectList(query);

        for (AirwallexPaymentConfig paymentConfig : paymentConfigs) {
            AirwallexMultiClientConfig.ClientConfig accountConfig = new AirwallexMultiClientConfig.ClientConfig();
            accountConfig.setClientId(paymentConfig.getClientId());
            accountConfig.setApiKey(paymentConfig.getApiKey());
            accountConfig.setSandbox(paymentConfig.getSandbox());
            accountConfig.setWebhookSecret(paymentConfig.getWebhookId());
            accountConfig.setApiBaseUrl(getApiBaseUrl(paymentConfig.getSandbox()));
            airwallexMultiClientConfig.registry(accountConfig);
        }

        airwallexMultiClientConfig.setDefaultClientId(paymentConfigs.stream().findFirst().orElse(paymentConfigs.getFirst()).getClientId());
    }
}
