package tv.shorthub.system.mapper;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import tv.shorthub.common.mapper.CommonMapper;
import tv.shorthub.system.domain.AppConsumption;

import java.util.List;
import java.util.Map;

/**
 * 金币消费Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-05-10
 */
public interface AppConsumptionMapper extends CommonMapper<AppConsumption>
{
    @Select({
            "SELECT * FROM app_consumption" +
            " WHERE user_id=#{userId}" +
            " ORDER BY ID DESC" +
            " LIMIT #{offset},#{limit}"
    })
    List<Map<String, Object>> history(@Param("userId") String userId, @Param("offset") Integer offset, @Param("limit") Integer limit);

    @Select({
            "SELECT * FROM app_consumption" +
                    " WHERE user_id=#{userId} AND content_id=#{contentId}" +
                    " ORDER BY ID DESC"
    })
    List<Map<String, Object>> historyByContentId(@Param("userId") String userId, @Param("contentId") String contentId);
}
