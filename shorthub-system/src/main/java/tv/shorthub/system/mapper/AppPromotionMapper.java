package tv.shorthub.system.mapper;

import tv.shorthub.common.mapper.CommonMapper;
import tv.shorthub.system.domain.AppPromotion;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * 推广链接Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-05-15
 */
public interface AppPromotionMapper extends CommonMapper<AppPromotion>
{
    /**
     * 根据创建者用户名查询其创建的所有 tfid
     * @param creatorName 创建者用户名
     * @return tfid 列表
     */
    List<String> selectTfidsByCreator(@Param("creatorName") String creatorName);
}
