package tv.shorthub.system.mapper;

import tv.shorthub.common.mapper.CommonMapper;
import tv.shorthub.system.domain.AppUsers;

import java.util.Date;

/**
 * 社交媒体用户Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-05-06
 */
public interface AppUsersMapper extends CommonMapper<AppUsers>
{
    int updateMemberByAdmin(String userId, String memberLevel, Long value, Date currentTime);

    int updateMember(String userId, String memberId, String memberLevel, Long value, Date currentTime);

    int updateBalanceCoinByAdmin(String userId, Long value);

    int updateBalanceCoin(String userId, Long value);

    int updateBalanceBonusByAdmin(String userId, Long value);

    int updateBalanceBonus(String userId, Long value);
}
