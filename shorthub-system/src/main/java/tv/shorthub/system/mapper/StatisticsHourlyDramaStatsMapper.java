package tv.shorthub.system.mapper;

import tv.shorthub.common.mapper.CommonMapper;
import tv.shorthub.system.domain.StatisticsHourlyDramaStats;
import java.util.Date;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * 每小时剧集分析统计Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-05
 */
public interface StatisticsHourlyDramaStatsMapper extends CommonMapper<StatisticsHourlyDramaStats>
{
    /**
     * 查询指定时间范围内的剧集统计数据
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计数据列表
     */
    List<StatisticsHourlyDramaStats> selectHourlyDramaStats(@Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 根据时间范围获取剧集统计汇总数据
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param appid 应用ID
     * @param tfid 推广链接ID
     * @return 汇总统计数据
     */
    StatisticsHourlyDramaStats getRangeSummary(@Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("appid") String appid, @Param("tfid") String tfid);

    /**
     * 根据时间范围查询剧集统计列表
     *
     * @param query 查询条件
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计数据列表
     */
    List<StatisticsHourlyDramaStats> selectListByTimeRange(@Param("query") StatisticsHourlyDramaStats query, @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 根据 tfid 列表获取汇总数据
     *
     * @param tfids tfid 列表
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @param appid 应用ID（可选）
     * @return 汇总统计数据
     */
    StatisticsHourlyDramaStats getSummaryByTfids(@Param("tfids") List<String> tfids, @Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("appid") String appid);

    /**
     * 根据创建者获取其所有子账号的汇总数据
     *
     * @param creatorName 创建者用户名
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @return 汇总统计数据
     */
    StatisticsHourlyDramaStats getSummaryByCreator(@Param("creatorName") String creatorName, @Param("startTime") Date startTime, @Param("endTime") Date endTime);
}
