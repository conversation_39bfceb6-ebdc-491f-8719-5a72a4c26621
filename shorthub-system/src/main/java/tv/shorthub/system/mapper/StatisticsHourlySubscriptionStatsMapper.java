package tv.shorthub.system.mapper;

import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Param;
import tv.shorthub.common.mapper.CommonMapper;
import tv.shorthub.system.domain.StatisticsHourlySubscriptionStats;
import tv.shorthub.system.dto.SubscriptionSummaryDto;

import java.util.Date;
import java.util.List;

/**
 * 每小时订单与订阅统计Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-29
 */
public interface StatisticsHourlySubscriptionStatsMapper extends CommonMapper<StatisticsHourlySubscriptionStats>
{
    /**
     * 根据时间范围查询每小时订阅统计数据
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计数据列表
     */
    List<StatisticsHourlySubscriptionStats> selectHourlySubscriptionStats(@Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 根据时间范围获取订阅统计汇总数据
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param appid 应用ID
     * @param tfid 推广链接ID
     * @return 汇总统计数据
     */
    StatisticsHourlySubscriptionStats getRangeSummary(@Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("appid") String appid, @Param("tfid") String tfid);

    /**
     * 根据 tfid 列表获取汇总数据（只返回首次订阅数据，投手权限使用）
     *
     * @param tfids tfid 列表
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @param appid 应用ID（可选）
     * @return 汇总统计数据
     */
    StatisticsHourlySubscriptionStats getSummaryByTfids(@Param("tfids") List<String> tfids, @Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("appid") String appid);

    /**
     * 批量插入或更新统计数据（避开拦截器）
     *
     * @param list 统计数据列表
     * @return 影响行数
     */
    int batchInsertOrUpdateStats(@Param("list") List<StatisticsHourlySubscriptionStats> list);

    /**
     * 查询即将到来的待扣款金额
     *
     * @return 包含近7日和近30日待扣款金额的DTO对象
     */
    SubscriptionSummaryDto selectUpcomingPayments();

    /**
     * 查询订阅相关表中的最小和最大时间
     *
     * @return 包含 min_date 和 max_date 的 Map
     */
    @MapKey("min_date")
    java.util.Map<String, java.util.Date> selectTimeRange();
}
