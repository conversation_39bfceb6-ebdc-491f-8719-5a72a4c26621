package tv.shorthub.system.mapper;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import tv.shorthub.common.mapper.CommonMapper;
import tv.shorthub.system.domain.AppUserWatchContent;

import java.util.List;
import java.util.Map;

/**
 * 观看剧目记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-05-10
 */
public interface AppUserWatchContentMapper extends CommonMapper<AppUserWatchContent>
{
    @Select({
            "SELECT * FROM app_user_watch_content" +
                    " WHERE user_id=#{userId}"
    })
    List<Map<String, Object>> selectWatchContent(
            @Param("userId") String userId
    );
}
