package tv.shorthub.system.mapper;

import java.util.Date;
import java.util.List;
import tv.shorthub.common.mapper.CommonMapper;
import tv.shorthub.system.domain.StatisticsDailyDramaStats;
import org.apache.ibatis.annotations.Param;

/**
 * 每日剧集分析统计Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-22
 */
public interface StatisticsDailyDramaStatsMapper extends CommonMapper<StatisticsDailyDramaStats>
{
    /**
     * 根据时间范围查询每日剧集统计数据
     */
    List<StatisticsDailyDramaStats> selectDailyDramaStats(@Param("startTime") Date startTime, 
                                                          @Param("endTime") Date endTime, 
                                                          @Param("timezone") String timezone, 
                                                          @Param("timezoneOffset") int timezoneOffset);

    /**
     * 根据时间范围获取剧集统计汇总数据
     */
    StatisticsDailyDramaStats getRangeSummary(@Param("startTime") Date startTime, 
                                              @Param("endTime") Date endTime, 
                                              @Param("appid") String appid, 
                                              @Param("tfid") String tfid, 
                                              @Param("timezone") String timezone);

    /**
     * 根据时间范围查询剧集统计列表
     */
    List<StatisticsDailyDramaStats> selectListByTimeRange(@Param("startTime") Date startTime, 
                                                          @Param("endTime") Date endTime, 
                                                          @Param("query") StatisticsDailyDramaStats query);

    /**
     * 根据 tfid 列表获取汇总数据
     */
    StatisticsDailyDramaStats getSummaryByTfids(@Param("tfids") List<String> tfids, 
                                                @Param("startTime") Date startTime, 
                                                @Param("endTime") Date endTime, 
                                                @Param("appid") String appid, 
                                                @Param("timezone") String timezone);

    /**
     * 根据创建者获取汇总数据
     */
    StatisticsDailyDramaStats getSummaryByCreator(@Param("creatorName") String creatorName, 
                                                  @Param("startTime") Date startTime, 
                                                  @Param("endTime") Date endTime, 
                                                  @Param("timezone") String timezone);
}
