package tv.shorthub.system.mapper;

import org.apache.ibatis.annotations.Param;
import tv.shorthub.common.mapper.CommonMapper;
import tv.shorthub.system.domain.StatisticsHourlyOrderStats;
import org.apache.ibatis.annotations.MapKey;

import java.util.Date;
import java.util.List;

/**
 * 每小时核心订单交易统计Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-27
 */
public interface StatisticsHourlyOrderStatsMapper extends CommonMapper<StatisticsHourlyOrderStats>
{
    /**
     * 根据时间范围查询每小时订单统计数据
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计数据列表
     */
    List<StatisticsHourlyOrderStats> selectHourlyOrderStats(@Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 根据时间范围获取订单统计汇总数据
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param appid 应用ID
     * @param tfid 推广链接ID
     * @param orderChannel 订单支付通道
     * @return 汇总统计数据
     */
    StatisticsHourlyOrderStats getRangeSummary(@Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("appid") String appid, @Param("tfid") String tfid, @Param("orderChannel") String orderChannel);

    /**
     * 根据 tfid 列表获取汇总数据
     * 
     * @param tfids tfid 列表
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @param appid 应用ID（可选）
     * @param orderChannel 订单支付通道（可选）
     * @return 汇总统计数据
     */
    StatisticsHourlyOrderStats getSummaryByTfids(@Param("tfids") List<String> tfids, @Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("appid") String appid, @Param("orderChannel") String orderChannel);

    /**
     * 批量新增或更新每小时核心订单交易统计数据
     * 
     * @param list 每小时核心订单交易统计数据列表
     * @return 影响行数
     */
    int batchInsertOrUpdateStats(List<StatisticsHourlyOrderStats> list);

    /**
     * 查询 app_order_info 表中的最小和最大创建时间
     *
     * @return 包含 min_date 和 max_date 的 Map
     */
    @MapKey("min_date")
    java.util.Map<String, java.util.Date> selectTimeRange();
}
