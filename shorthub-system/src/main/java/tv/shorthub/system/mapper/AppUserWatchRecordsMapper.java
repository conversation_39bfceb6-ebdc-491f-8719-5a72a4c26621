package tv.shorthub.system.mapper;

import org.apache.ibatis.annotations.Select;
import tv.shorthub.common.mapper.CommonMapper;
import tv.shorthub.system.domain.AppUserWatchRecords;

import java.util.List;
import java.util.Map;

/**
 * 用户观看记录Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-06
 */
public interface AppUserWatchRecordsMapper extends CommonMapper<AppUserWatchRecords>
{
    @Select({
            "SELECT * FROM app_user_watch_records" +
                    " WHERE user_id=#{userId}" +
                    " order by update_time desc" +
                    " limit 1"
    })
    List<Map<String, Object>> selectLastByContentId(String userId, String contentId);

    List<AppUserWatchRecords> selectListByPromotionUser(String userId);


    int batchInsertOrUpdateCustom(List<AppUserWatchRecords> list);
}
