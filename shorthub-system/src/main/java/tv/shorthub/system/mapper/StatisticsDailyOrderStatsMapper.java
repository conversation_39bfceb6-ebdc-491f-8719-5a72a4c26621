package tv.shorthub.system.mapper;

import tv.shorthub.common.mapper.CommonMapper;
import tv.shorthub.system.domain.StatisticsDailyOrderStats;
import org.apache.ibatis.annotations.Param;
import java.util.Date;
import java.util.List;
import java.util.Map;
import org.apache.ibatis.annotations.MapKey;

/**
 * 每日订单统计Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
public interface StatisticsDailyOrderStatsMapper extends CommonMapper<StatisticsDailyOrderStats>
{
    /**
     * 查询指定日期范围内的每日订单统计数据
     */
    List<StatisticsDailyOrderStats> selectDailyOrderStats(@Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("timezone") String timezone, @Param("timezoneOffset") int timezoneOffset);

    /**
     * 查询订单数据的最早和最晚日期
     */
    @MapKey("min_date")
    Map<String, Date> selectTimeRange();

    /**
     * 按多个tfid统计区间汇总（支持时区和orderChannel）
     */
    StatisticsDailyOrderStats getSummaryByTfids(@Param("tfids") List<String> tfids, @Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("appid") String appid, @Param("orderChannel") String orderChannel, @Param("timezone") String timezone);

    /**
     * 按创建者统计区间汇总
     */
    StatisticsDailyOrderStats getSummaryByCreator(@Param("creator") String creator, @Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("orderChannel") String orderChannel, @Param("timezone") String timezone);

    /**
     * 按区间、appid、tfid、orderChannel统计汇总
     */
    StatisticsDailyOrderStats getRangeSummary(@Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("appid") String appid, @Param("tfid") String tfid, @Param("orderChannel") String orderChannel, @Param("timezone") String timezone);
}
