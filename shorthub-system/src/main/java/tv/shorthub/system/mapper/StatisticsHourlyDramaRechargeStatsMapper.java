package tv.shorthub.system.mapper;

import org.apache.ibatis.annotations.Param;
import tv.shorthub.common.mapper.CommonMapper;
import tv.shorthub.system.domain.StatisticsHourlyDramaRechargeStats;
import tv.shorthub.system.domain.dto.StatisticsHourlyDramaRechargeStatsDTO;
import tv.shorthub.system.domain.dto.StatisticsHourlyDramaRechargeStatsDetailDTO;

import java.util.List;
import java.util.Map;
import java.util.Date;

/**
 * 小时级剧目充值统计Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-15
 */
public interface StatisticsHourlyDramaRechargeStatsMapper extends CommonMapper<StatisticsHourlyDramaRechargeStats>
{
    /**
     * 查询指定时间范围内的剧目充值统计数据
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计数据列表
     */
    List<StatisticsHourlyDramaRechargeStats> selectHourlyDramaRechargeStats(
            @Param("startTime") String startTime,
            @Param("endTime") String endTime
    );

    /**
     * 查询订单表的时间范围
     *
     * @return 包含最小和最大时间的Map
     */
    Map<String, Date> selectTimeRange();

    /**
     * 根据时间范围获取剧目充值统计汇总数据
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param appid 应用ID
     * @param tfid 推广链接ID
     * @param contentId 内容ID
     * @param dramaId 剧目ID
     * @return 汇总统计数据
     */
    StatisticsHourlyDramaRechargeStats getRangeSummary(@Param("startTime") Date startTime, 
                                                      @Param("endTime") Date endTime, 
                                                      @Param("appid") String appid, 
                                                      @Param("tfid") String tfid,
                                                      @Param("contentId") String contentId,
                                                      @Param("dramaId") String dramaId);

    /**
     * 根据时间范围获取解锁剧集详情
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param appid 应用ID
     * @param tfid 推广链接ID
     * @param contentId 内容ID
     * @param dramaId 剧目ID
     * @return 解锁剧集详情列表
     */
    List<StatisticsHourlyDramaRechargeStatsDetailDTO.UnlockEpisodeDetail> getUnlockEpisodeDetails(@Param("startTime") Date startTime, 
                                                                                                  @Param("endTime") Date endTime, 
                                                                                                  @Param("appid") String appid, 
                                                                                                  @Param("tfid") String tfid,
                                                                                                  @Param("contentId") String contentId,
                                                                                                  @Param("dramaId") String dramaId);

}
