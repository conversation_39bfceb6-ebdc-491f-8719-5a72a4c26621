package tv.shorthub.system.mapper;

import org.apache.ibatis.annotations.Select;
import tv.shorthub.common.mapper.CommonMapper;
import tv.shorthub.system.domain.AppUserCollect;

import java.util.List;
import java.util.Map;

/**
 * 用户收藏Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-05-10
 */
public interface AppUserCollectMapper extends CommonMapper<AppUserCollect>
{
    /**
     * 查询用户收藏列表
     *
     * @param userId 用户ID
     * @return 用户收藏列表
     */
    @Select({
            "SELECT * FROM app_user_collect" +
                    " WHERE user_id=#{userId}" +
                    " order by id desc" +
                    " limit 100"
    })
    List<Map<String, Object>> selectListByUserId(String userId);
}
