package tv.shorthub.system.mapper;

import lombok.Data;
import org.apache.ibatis.annotations.Param;
import tv.shorthub.common.mapper.CommonMapper;
import tv.shorthub.system.domain.StatisticsDailyDramaEpisodeStats;


import java.util.Date;
import java.util.List;

/**
 * 剧集每日统计Mapper接口
 *
 * <AUTHOR>
 * @date 2025-08-01
 */
public interface StatisticsDailyDramaEpisodeStatsMapper extends CommonMapper<StatisticsDailyDramaEpisodeStats>
{

    List<Long> getWatchSecondsForMedian(
            @Param("contentId") String contentId,
            @Param("serialNumber") Integer serialNumber,
            @Param("startTime") Date startTime,
            @Param("endTime") Date endTime,
            @Param("tfid") String tfid
    );
    /**
     * 查询剧集每日统计
     *
     * @return 剧集每日统计
     */
    public List<StatisticsDailyDramaEpisodeStats> selectDailyDramaEpisodeStats(@Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("timezone") String timezone, @Param("timezoneOffset") int timezoneOffset);

    /**
     * 获取剧集分集统计区间汇总数据
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param appid 应用ID
     * @param tfid 推广链接ID
     * @param contentId 内容ID
     * @param serialNumber 集数序号
     * @param timezone 时区
     * @param delivererName 投手名称
     * @return 汇总统计数据
     */
    StatisticsDailyDramaEpisodeStats getRangeSummary(@Param("startTime") Date startTime,
                                                     @Param("endTime") Date endTime,
                                                     @Param("appid") String appid,
                                                     @Param("tfid") String tfid,
                                                     @Param("contentId") String contentId,
                                                     @Param("serialNumber") Integer serialNumber,
                                                     @Param("timezone") String timezone,
                                                     @Param("delivererName") String delivererName);


}
