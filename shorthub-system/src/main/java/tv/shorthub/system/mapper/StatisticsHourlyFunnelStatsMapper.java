package tv.shorthub.system.mapper;

import tv.shorthub.common.mapper.CommonMapper;
import tv.shorthub.system.domain.StatisticsHourlyFunnelStats;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 每小时漏斗分析统计Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-07
 */
public interface StatisticsHourlyFunnelStatsMapper extends CommonMapper<StatisticsHourlyFunnelStats>
{
    /**
     * 根据时间范围查询每小时漏斗分析统计数据
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计数据列表
     */
    List<StatisticsHourlyFunnelStats> selectHourlyFunnelStats(@Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 批量插入或更新漏斗分析统计数据
     * 
     * @param list 统计数据列表
     * @return 影响行数
     */
    int batchInsertOrUpdate(@Param("list") List<StatisticsHourlyFunnelStats> list);

    /**
     * 根据时间范围获取漏斗分析统计汇总数据
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param appid 应用ID
     * @param tfid 推广链接ID
     * @return 汇总统计数据
     */
    StatisticsHourlyFunnelStats getRangeSummary(@Param("startTime") Date startTime, @Param("endTime") Date endTime, 
                                                @Param("appid") String appid, @Param("tfid") String tfid);

    /**
     * 根据 tfid 列表获取汇总数据
     * 
     * @param tfids tfid列表
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param appid 应用ID（可选）
     * @return 汇总统计数据
     */
    StatisticsHourlyFunnelStats getSummaryByTfids(@Param("tfids") List<String> tfids, 
                                                  @Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("appid") String appid);

    /**
     * 根据创建者获取汇总数据
     * 
     * @param creator 创建者用户名
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 汇总统计数据
     */
    StatisticsHourlyFunnelStats getSummaryByCreator(@Param("creator") String creator, 
                                                   @Param("startTime") Date startTime, @Param("endTime") Date endTime);
}
