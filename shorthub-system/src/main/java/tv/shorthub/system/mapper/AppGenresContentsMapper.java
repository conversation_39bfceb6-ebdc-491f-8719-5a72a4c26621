package tv.shorthub.system.mapper;

import tv.shorthub.common.mapper.CommonMapper;
import tv.shorthub.system.domain.AppGenresContents;

import java.util.List;

/**
 * 剧目类型内容Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-05-06
 */
public interface AppGenresContentsMapper extends CommonMapper<AppGenresContents>
{
    /**
     * 查询剧目类型内容
     * 
     * @param id 剧目类型内容主键
     * @return 剧目类型内容
     */
    public AppGenresContents selectById(Long id);

    /**
     * 查询剧目类型内容列表
     * 
     * @param appGenresContents 剧目类型内容
     * @return 剧目类型内容集合
     */
    public List<AppGenresContents> selectList(AppGenresContents appGenresContents);

    /**
     * 新增剧目类型内容
     * 
     * @param appGenresContents 剧目类型内容
     * @return 结果
     */
    public int insert(AppGenresContents appGenresContents);

    /**
     * 修改剧目类型内容
     * 
     * @param appGenresContents 剧目类型内容
     * @return 结果
     */
    public int update(AppGenresContents appGenresContents);

    /**
     * 删除剧目类型内容
     * 
     * @param id 剧目类型内容主键
     * @return 结果
     */
    public int deleteById(Long id);

    /**
     * 批量删除剧目类型内容
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteByIds(Long[] ids);

    /**
     * 通过类型ID删除剧目类型内容
     * 
     * @param genreId 类型ID
     * @return 结果
     */
    public int deleteByGenreId(Long genreId);
} 