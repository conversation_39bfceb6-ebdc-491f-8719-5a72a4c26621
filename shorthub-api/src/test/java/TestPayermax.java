import com.alibaba.fastjson2.JSONObject;
import com.google.api.services.androidpublisher.model.*;
import com.google.api.services.androidpublisher.AndroidPublisher;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import tv.shorthub.googleplay.model.Product;
import tv.shorthub.googleplay.model.Purchase;
import tv.shorthub.googleplay.service.GooglePlayService;
import tv.shorthub.googleplay.service.impl.GooglePlaySubscriptionService;
import tv.shorthub.googleplay.config.GooglePlayConfig;
import tv.shorthub.payermax.service.PayermaxSubscribeService;

import java.math.BigDecimal;
import java.util.List;
import java.util.Arrays;
import java.util.ArrayList;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = tv.shorthub.ShorthubApiApplication.class)
@Slf4j
public class TestPayermax {

    @Autowired
    private PayermaxSubscribeService payermaxSubscribeService;


    @Autowired
    private GooglePlayService googlePlayService;
    private static final String PACKAGE_NAME = "com.b.shorthub.tv";
    private static final String TEST_PRODUCT_ID = "test_product_001";


    @Autowired
    GooglePlaySubscriptionService googlePlaySubscriptionService;

    @Autowired
    GooglePlayConfig googlePlayConfig;


    @Test
    public void testActivate() throws Exception {
        googlePlaySubscriptionService.activateBasePlan(PACKAGE_NAME, "ss11", "tt123123");
    }

    @Test
    public void testCreateSubscription() throws Exception {
        BigDecimal originalPrice = new BigDecimal("35.99");
        // 正确计算Money对象的units和nanos
        long priceUnits = originalPrice.longValue(); // 整数部分
        int priceNanos = originalPrice.subtract(new BigDecimal(priceUnits))
                .multiply(new BigDecimal("1000000000")).intValue(); // 小数部分转换为纳秒
        BasePlan basePlan = googlePlaySubscriptionService.builderBasePlan(PACKAGE_NAME, "sam2", "P1M", "P1D", "HKD", priceUnits, priceNanos);
        SubscriptionListing listing = new SubscriptionListing();
        listing.setLanguageCode("zh-HK");
        listing.setTitle("测试订阅");
        listing.setDescription("这是一个测试订阅");

        Subscription subscription = googlePlaySubscriptionService.createSubscriptionAndActivate(PACKAGE_NAME, "samsplan2", Arrays.asList(basePlan), Arrays.asList( listing), true);
        log.info("创建订阅产品成功: {}", subscription);
    }

    @Test
    public void checkExistingSubscriptions() throws Exception {
        System.out.println("🔍 检查现有订阅产品...");
        
        try {
            // 获取所有订阅产品
            List<Subscription> subscriptions = googlePlaySubscriptionService.getSubscriptions(PACKAGE_NAME);
            System.out.println("✅ 找到 " + subscriptions.size() + " 个订阅产品:");
            
            for (Subscription subscription : subscriptions) {
                System.out.println("   - Product ID: " + subscription.getProductId());
                System.out.println("   - Package Name: " + subscription.getPackageName());
                System.out.println("   - Base Plans: " + (subscription.getBasePlans() != null ? subscription.getBasePlans().size() : 0));
                if (subscription.getBasePlans() != null) {
                    for (BasePlan basePlan : subscription.getBasePlans()) {
                        System.out.println("     * Base Plan ID: " + basePlan.getBasePlanId());
                        System.out.println("     * State: " + basePlan.getState());
                    }
                }
                System.out.println();
            }
            
            // 检查特定订阅是否存在
            String testSubscriptionId = "test_subscription_00123";
            boolean exists = googlePlaySubscriptionService.subscriptionExists(PACKAGE_NAME, testSubscriptionId);
            System.out.println("订阅 " + testSubscriptionId + " 是否存在: " + exists);
            
        } catch (Exception e) {
            System.err.println("❌ 检查订阅产品失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Test
    public void subscriptionQuery() throws Exception {
        String productId = "test_00123";
        String purchaseToken = "hmaojobaojhmomhdacmmohin.AO-J1OzvoGcTmkA9tHIGmyso-q6eJbayw7Ga1e2XucvaaXIhzR4wl6_7TfIvuPPgUAuFr9h34tfZouLHxsWj-DmQR7exVbZyEg";
        
        // 先验证购买
        Purchase verified = googlePlayService.verifyPurchase(PACKAGE_NAME, productId, purchaseToken);
        System.out.println("✅ 验证结果: " + verified);
        
        // 如果验证成功且未确认，则确认购买
        if (verified != null && !verified.isAcknowledged()) {
            try {
                // 使用原始purchaseToken进行确认，因为返回的可能是null
                googlePlayService.acknowledgePurchase(PACKAGE_NAME, productId, purchaseToken);
                System.out.println("✅ 购买确认成功");
            } catch (Exception e) {
                System.err.println("❌ 购买确认失败: " + e.getMessage());
            }
        } else {
            System.out.println("ℹ️ 购买已确认或验证失败，跳过确认步骤");
        }

//        List<Product> products = googlePlayService.getProducts(PACKAGE_NAME);
//        System.out.println("✅ 产品列表: " + products);
//
//        // 创建测试产品
//        Product product = Product.builder()
//                .productId(TEST_PRODUCT_ID)
//                .name("测试产品")
//                .description("这是一个测试产品")
//                .type("inapp")
//                .defaultPrice(new BigDecimal("9.99"))
//                .defaultPriceCurrencyCode("HKD")
//                .packageName(PACKAGE_NAME)
//                .build();
//
//        Product createdProduct = googlePlayService.createProduct(PACKAGE_NAME, product);
//        System.out.println("✅ 产品创建成功: " + createdProduct);
//        JSONObject jsonObject = payermaxSubscribeService.subscriptionQuery("1937053681148964864");
//        log.info("detail: {}", jsonObject);
    }

    @Test
    public void diagnosePurchaseIssue() throws Exception {
        String productId = "test_00123";
        String purchaseToken = "hmaojobaojhmomhdacmmohin.AO-J1OzvoGcTmkA9tHIGmyso-q6eJbayw7Ga1e2XucvaaXIhzR4wl6_7TfIvuPPgUAuFr9h34tfZouLHxsWj-DmQR7exVbZyEg";
        
        System.out.println("🔍 开始诊断购买问题...");
        System.out.println("📦 包名: " + PACKAGE_NAME);
        System.out.println("🆔 产品ID: " + productId);
        System.out.println("🎫 购买令牌: " + purchaseToken);
        
        try {
            // 1. 先验证购买
            System.out.println("\n1️⃣ 验证购买...");
            Purchase verified = googlePlayService.verifyPurchase(PACKAGE_NAME, productId, purchaseToken);
            System.out.println("✅ 验证成功: " + verified);
            System.out.println("   - 订单ID: " + verified.getOrderId());
            System.out.println("   - 购买状态: " + verified.getPurchaseState());
            System.out.println("   - 消费状态: " + verified.getConsumptionState());
            System.out.println("   - 是否已确认: " + verified.isAcknowledged());
            System.out.println("   - 购买时间: " + verified.getPurchaseTime());
            
            // 2. 检查是否需要确认
            if (!verified.isAcknowledged()) {
                System.out.println("\n2️⃣ 尝试确认购买...");
                try {
                    googlePlayService.acknowledgePurchase(PACKAGE_NAME, productId, purchaseToken);
                    System.out.println("✅ 确认成功");
                } catch (Exception e) {
                    System.err.println("❌ 确认失败: " + e.getMessage());
                    System.out.println("💡 可能的原因:");
                    System.out.println("   - 服务账号权限不足");
                    System.out.println("   - 购买令牌已过期");
                    System.out.println("   - 产品ID不匹配");
                    System.out.println("   - 测试环境限制");
                }
            } else {
                System.out.println("\nℹ️ 购买已确认，跳过确认步骤");
            }
            
            // 3. 检查是否需要消费
            if ("0".equals(verified.getConsumptionState())) {
                System.out.println("\n3️⃣ 尝试消费购买...");
                try {
                    googlePlayService.consumePurchase(PACKAGE_NAME, productId, purchaseToken);
                    System.out.println("✅ 消费成功");
                } catch (Exception e) {
                    System.err.println("❌ 消费失败: " + e.getMessage());
                }
            } else {
                System.out.println("\nℹ️ 购买已消费，跳过消费步骤");
            }
            
        } catch (Exception e) {
            System.err.println("❌ 验证失败: " + e.getMessage());
            System.out.println("💡 可能的原因:");
            System.out.println("   - 购买令牌无效或过期");
            System.out.println("   - 产品ID不存在");
            System.out.println("   - 服务账号权限问题");
        }
    }

    @Test
    public void checkServiceAccountPermissions() throws Exception {
        System.out.println("🔍 检查服务账号权限...");
        
        try {
            // 1. 尝试获取产品列表来测试基本权限
            System.out.println("\n1️⃣ 测试基本API访问权限...");
            List<Product> products = googlePlayService.getProducts(PACKAGE_NAME);
            System.out.println("✅ 基本权限正常，找到 " + products.size() + " 个产品");
            
            // 2. 检查特定产品是否存在
            System.out.println("\n2️⃣ 检查测试产品是否存在...");
            try {
                Product testProduct = googlePlayService.getProduct(PACKAGE_NAME, "test_00123");
                System.out.println("✅ 测试产品存在: " + testProduct.getProductId());
                System.out.println("   - 状态: " + testProduct.getStatus());
                System.out.println("   - 类型: " + testProduct.getType());
            } catch (Exception e) {
                System.err.println("❌ 测试产品不存在: " + e.getMessage());
                System.out.println("💡 建议: 在 Google Play Console 中创建产品 test_00123");
            }
            
            // 3. 测试购买验证权限
            System.out.println("\n3️⃣ 测试购买验证权限...");
            String purchaseToken = "hmaojobaojhmomhdacmmohin.AO-J1OzvoGcTmkA9tHIGmyso-q6eJbayw7Ga1e2XucvaaXIhzR4wl6_7TfIvuPPgUAuFr9h34tfZouLHxsWj-DmQR7exVbZyEg";
            try {
                Purchase verified = googlePlayService.verifyPurchase(PACKAGE_NAME, "test_00123", purchaseToken);
                System.out.println("✅ 购买验证权限正常");
                System.out.println("   - 订单ID: " + verified.getOrderId());
                System.out.println("   - 购买状态: " + verified.getPurchaseState());
                System.out.println("   - 是否已确认: " + verified.isAcknowledged());
            } catch (Exception e) {
                System.err.println("❌ 购买验证失败: " + e.getMessage());
            }
            
        } catch (Exception e) {
            System.err.println("❌ 权限检查失败: " + e.getMessage());
            System.out.println("💡 可能的问题:");
            System.out.println("   - 服务账号密钥文件无效");
            System.out.println("   - 服务账号没有 Android Publisher API 权限");
            System.out.println("   - 应用未在 Google Play Console 中正确配置");
        }
    }

    @Test
    public void createTestProduct() throws Exception {
        System.out.println("🔧 创建测试产品...");
        
        try {
            // 创建测试产品
            Product testProduct = Product.builder()
                    .productId("test_00123")
                    .name("测试产品")
                    .description("这是一个用于测试的Google Play产品")
                    .type("inapp")
                    .defaultPrice(new BigDecimal("9.99"))
                    .defaultPriceCurrencyCode("HKD")
                    .packageName(PACKAGE_NAME)
                    .status("active")
                    .build();
            
            System.out.println("📦 产品信息:");
            System.out.println("   - ID: " + testProduct.getProductId());
            System.out.println("   - 名称: " + testProduct.getName());
            System.out.println("   - 价格: " + testProduct.getDefaultPrice() + " " + testProduct.getDefaultPriceCurrencyCode());
            System.out.println("   - 类型: " + testProduct.getType());
            
            // 尝试创建产品
            Product createdProduct = googlePlayService.createProduct(PACKAGE_NAME, testProduct);
            System.out.println("✅ 产品创建成功: " + createdProduct.getProductId());
            
        } catch (Exception e) {
            System.err.println("❌ 产品创建失败: " + e.getMessage());
            System.out.println("💡 可能的原因:");
            System.out.println("   - 产品已存在");
            System.out.println("   - 服务账号权限不足");
            System.out.println("   - 产品ID格式不正确");
        }
    }

    @Test
    public void checkPurchaseTokenIssue() throws Exception {
        String productId = "test_00123";
        String originalPurchaseToken = "hmaojobaojhmomhdacmmohin.AO-J1OzvoGcTmkA9tHIGmyso-q6eJbayw7Ga1e2XucvaaXIhzR4wl6_7TfIvuPPgUAuFr9h34tfZouLHxsWj-DmQR7exVbZyEg";
        
        System.out.println("🔍 检查购买令牌问题...");
        System.out.println("📦 包名: " + PACKAGE_NAME);
        System.out.println("🆔 产品ID: " + productId);
        System.out.println("🎫 原始购买令牌: " + originalPurchaseToken);
        
        try {
            // 1. 验证购买并检查返回的purchaseToken
            Purchase verified = googlePlayService.verifyPurchase(PACKAGE_NAME, productId, originalPurchaseToken);
            System.out.println("✅ 验证成功");
            System.out.println("   - 订单ID: " + verified.getOrderId());
            System.out.println("   - 返回的purchaseToken: " + verified.getPurchaseToken());
            System.out.println("   - 原始purchaseToken: " + originalPurchaseToken);
            System.out.println("   - 是否已确认: " + verified.isAcknowledged());
            
            // 2. 检查purchaseToken是否匹配
            if (verified.getPurchaseToken() == null) {
                System.err.println("❌ 问题发现: 返回的purchaseToken为null!");
                System.out.println("💡 这可能是因为:");
                System.out.println("   - Google Play API返回的ProductPurchase对象中purchaseToken为null");
                System.out.println("   - 需要直接使用原始purchaseToken进行确认");
            } else if (!originalPurchaseToken.equals(verified.getPurchaseToken())) {
                System.err.println("❌ 问题发现: purchaseToken不匹配!");
                System.out.println("   - 原始: " + originalPurchaseToken);
                System.out.println("   - 返回: " + verified.getPurchaseToken());
            } else {
                System.out.println("✅ purchaseToken匹配正常");
            }
            
            // 3. 尝试使用返回的purchaseToken确认购买
            if (!verified.isAcknowledged()) {
                System.out.println("\n3️⃣ 尝试确认购买...");
                String tokenToUse = verified.getPurchaseToken() != null ? verified.getPurchaseToken() : originalPurchaseToken;
                System.out.println("使用令牌: " + tokenToUse);
                
                try {
                    googlePlayService.acknowledgePurchase(PACKAGE_NAME, productId, tokenToUse);
                    System.out.println("✅ 确认成功");
                } catch (Exception e) {
                    System.err.println("❌ 确认失败: " + e.getMessage());
                    
                    // 如果使用返回的token失败，尝试使用原始token
                    if (verified.getPurchaseToken() != null && !verified.getPurchaseToken().equals(originalPurchaseToken)) {
                        System.out.println("\n🔄 尝试使用原始令牌...");
                        try {
                            googlePlayService.acknowledgePurchase(PACKAGE_NAME, productId, originalPurchaseToken);
                            System.out.println("✅ 使用原始令牌确认成功");
                        } catch (Exception e2) {
                            System.err.println("❌ 使用原始令牌也失败: " + e2.getMessage());
                        }
                    }
                }
            } else {
                System.out.println("ℹ️ 购买已确认，跳过确认步骤");
            }
            
        } catch (Exception e) {
            System.err.println("❌ 验证失败: " + e.getMessage());
        }
    }

    @Test
    public void detailedPermissionDiagnosis() throws Exception {
        System.out.println("🔍 详细权限诊断...");
        
        try {
            // 1. 测试基本API访问
            System.out.println("\n1️⃣ 测试基本API访问...");
            List<Product> products = googlePlayService.getProducts(PACKAGE_NAME);
            System.out.println("✅ 基本API访问正常，找到 " + products.size() + " 个产品");
            
            // 2. 检查应用配置
            System.out.println("\n2️⃣ 检查应用配置...");
            System.out.println("📦 包名: " + PACKAGE_NAME);
            System.out.println("🔑 服务账号: <EMAIL>");
            System.out.println("🌐 项目ID: ace-world-464010-i4");
            
            // 3. 测试产品管理权限
            System.out.println("\n3️⃣ 测试产品管理权限...");
            try {
                Product testProduct = googlePlayService.getProduct(PACKAGE_NAME, "test_00123");
                System.out.println("✅ 产品读取权限正常");
                System.out.println("   - 产品ID: " + testProduct.getProductId());
                System.out.println("   - 状态: " + testProduct.getStatus());
                System.out.println("   - 类型: " + testProduct.getType());
            } catch (Exception e) {
                System.err.println("❌ 产品读取失败: " + e.getMessage());
            }
            
            // 4. 测试购买验证权限
            System.out.println("\n4️⃣ 测试购买验证权限...");
            String purchaseToken = "hmaojobaojhmomhdacmmohin.AO-J1OzvoGcTmkA9tHIGmyso-q6eJbayw7Ga1e2XucvaaXIhzR4wl6_7TfIvuPPgUAuFr9h34tfZouLHxsWj-DmQR7exVbZyEg";
            try {
                Purchase verified = googlePlayService.verifyPurchase(PACKAGE_NAME, "test_00123", purchaseToken);
                System.out.println("✅ 购买验证权限正常");
                System.out.println("   - 订单ID: " + verified.getOrderId());
                System.out.println("   - 购买状态: " + verified.getPurchaseState());
                System.out.println("   - 是否已确认: " + verified.isAcknowledged());
                System.out.println("   - 返回的purchaseToken: " + verified.getPurchaseToken());
            } catch (Exception e) {
                System.err.println("❌ 购买验证失败: " + e.getMessage());
            }
            
            // 5. 提供解决方案建议
            System.out.println("\n5️⃣ 权限问题诊断结果...");
            System.out.println("💡 根据测试结果，建议执行以下步骤:");
            System.out.println("   1. 登录 Google Play Console: https://play.google.com/console");
            System.out.println("   2. 选择应用: com.b.shorthub.tv");
            System.out.println("   3. 进入 '设置' → 'API访问'");
            System.out.println("   4. 确保服务账号有以下权限:");
            System.out.println("      - 查看应用信息");
            System.out.println("      - 管理应用内商品");
            System.out.println("      - 查看财务数据");
            System.out.println("   5. 如果服务账号不存在，点击 '创建服务账号'");
            System.out.println("   6. 添加现有服务账号: <EMAIL>");
            
        } catch (Exception e) {
            System.err.println("❌ 权限诊断失败: " + e.getMessage());
        }
    }

    @Test
    public void testWithoutAcknowledgment() throws Exception {
        String productId = "test_00123";
        String purchaseToken = "hmaojobaojhmomhdacmmohin.AO-J1OzvoGcTmkA9tHIGmyso-q6eJbayw7Ga1e2XucvaaXIhzR4wl6_7TfIvuPPgUAuFr9h34tfZouLHxsWj-DmQR7exVbZyEg";
        
        System.out.println("🧪 测试环境 - 跳过购买确认...");
        System.out.println("📦 包名: " + PACKAGE_NAME);
        System.out.println("🆔 产品ID: " + productId);
        System.out.println("🎫 购买令牌: " + purchaseToken);
        
        try {
            // 1. 验证购买
            Purchase verified = googlePlayService.verifyPurchase(PACKAGE_NAME, productId, purchaseToken);
            System.out.println("✅ 购买验证成功");
            System.out.println("   - 订单ID: " + verified.getOrderId());
            System.out.println("   - 购买状态: " + verified.getPurchaseState());
            System.out.println("   - 消费状态: " + verified.getConsumptionState());
            System.out.println("   - 是否已确认: " + verified.isAcknowledged());
            System.out.println("   - 购买时间: " + verified.getPurchaseTime());
            
            // 2. 模拟业务逻辑（跳过确认）
            if ("1".equals(verified.getPurchaseState())) {
                System.out.println("✅ 购买状态有效，可以处理业务逻辑");
                
                // 模拟为用户提供服务
                System.out.println("🎁 为用户提供服务...");
                System.out.println("   - 解锁高级功能");
                System.out.println("   - 移除广告");
                System.out.println("   - 提供专属内容");
                
                // 3. 尝试消费购买（如果未消费）
                if ("0".equals(verified.getConsumptionState())) {
                    try {
                        googlePlayService.consumePurchase(PACKAGE_NAME, productId, purchaseToken);
                        System.out.println("✅ 购买消费成功");
                    } catch (Exception e) {
                        System.err.println("❌ 购买消费失败: " + e.getMessage());
                        System.out.println("💡 在测试环境中，消费失败是正常的");
                    }
                } else {
                    System.out.println("ℹ️ 购买已消费，跳过消费步骤");
                }
                
            } else {
                System.err.println("❌ 购买状态无效: " + verified.getPurchaseState());
            }
            
        } catch (Exception e) {
            System.err.println("❌ 购买验证失败: " + e.getMessage());
        }
        
        System.out.println("\n💡 测试环境建议:");
        System.out.println("   - 在生产环境中，必须调用 acknowledgePurchase");
        System.out.println("   - 在测试环境中，可以跳过确认步骤");
        System.out.println("   - 确保服务账号有正确的权限");
    }

    @Test
    public void checkGooglePlayConsoleConfig() throws Exception {
        System.out.println("🔍 检查 Google Play Console 配置...");
        
        System.out.println("📋 请按以下步骤检查配置:");
        System.out.println();
        System.out.println("1️⃣ 登录 Google Play Console");
        System.out.println("   URL: https://play.google.com/console");
        System.out.println("   账户: 你的拥有者账户");
        System.out.println();
        
        System.out.println("2️⃣ 选择应用");
        System.out.println("   应用包名: " + PACKAGE_NAME);
        System.out.println("   应用名称: ShortHub TV Test");
        System.out.println();
        
        System.out.println("3️⃣ 检查 API 访问设置");
        System.out.println("   路径: 设置 → API访问");
        System.out.println("   检查项目: ace-world-464010-i4");
        System.out.println();
        
        System.out.println("4️⃣ 验证服务账号权限");
        System.out.println("   服务账号: <EMAIL>");
        System.out.println("   必需权限:");
        System.out.println("   ✅ 查看应用信息");
        System.out.println("   ✅ 管理应用内商品");
        System.out.println("   ✅ 查看财务数据");
        System.out.println("   ✅ 管理订单");
        System.out.println();
        
        System.out.println("5️⃣ 检查应用状态");
        System.out.println("   确保应用状态为以下之一:");
        System.out.println("   - 内部测试");
        System.out.println("   - 封闭测试");
        System.out.println("   - 开放测试");
        System.out.println("   - 生产");
        System.out.println();
        
        System.out.println("6️⃣ 检查产品配置");
        System.out.println("   产品ID: test_00123");
        System.out.println("   确保产品状态为 '活跃'");
        System.out.println();
        
        System.out.println("💡 如果服务账号不存在，请:");
        System.out.println("   1. 点击 '创建服务账号'");
        System.out.println("   2. 选择 '添加现有服务账号'");
        System.out.println("   3. 输入: <EMAIL>");
        System.out.println("   4. 授予上述所有权限");
        System.out.println();
        
        System.out.println("🔧 配置完成后，重新运行测试");
    }

    @Test
    public void testBypassAcknowledgment() throws Exception {
        String productId = "test_00123";
        String purchaseToken = "hmaojobaojhmomhdacmmohin.AO-J1OzvoGcTmkA9tHIGmyso-q6eJbayw7Ga1e2XucvaaXIhzR4wl6_7TfIvuPPgUAuFr9h34tfZouLHxsWj-DmQR7exVbZyEg";
        
        System.out.println("🚀 测试环境 - 绕过购买确认...");
        System.out.println("📦 包名: " + PACKAGE_NAME);
        System.out.println("🆔 产品ID: " + productId);
        System.out.println("🎫 购买令牌: " + purchaseToken);
        
        try {
            // 1. 验证购买
            Purchase verified = googlePlayService.verifyPurchase(PACKAGE_NAME, productId, purchaseToken);
            System.out.println("✅ 购买验证成功");
            System.out.println("   - 订单ID: " + verified.getOrderId());
            System.out.println("   - 购买状态: " + verified.getPurchaseState());
            System.out.println("   - 消费状态: " + verified.getConsumptionState());
            System.out.println("   - 是否已确认: " + verified.isAcknowledged());
            System.out.println("   - 购买时间: " + verified.getPurchaseTime());
            
            // 2. 检查购买状态
            if ("1".equals(verified.getPurchaseState())) {
                System.out.println("✅ 购买状态有效 (已购买)");
                
                // 3. 模拟确认购买（在数据库中标记为已确认）
                System.out.println("🎯 模拟确认购买...");
                System.out.println("   - 在数据库中标记订单为已确认");
                System.out.println("   - 为用户解锁相应功能");
                System.out.println("   - 发送确认邮件/通知");
                
                // 4. 处理业务逻辑
                System.out.println("🎁 处理业务逻辑...");
                System.out.println("   - 解锁高级功能");
                System.out.println("   - 移除广告");
                System.out.println("   - 提供专属内容");
                System.out.println("   - 更新用户权限");
                
                // 5. 尝试消费购买
                if ("0".equals(verified.getConsumptionState())) {
                    try {
                        googlePlayService.consumePurchase(PACKAGE_NAME, productId, purchaseToken);
                        System.out.println("✅ 购买消费成功");
                    } catch (Exception e) {
                        System.err.println("❌ 购买消费失败: " + e.getMessage());
                        System.out.println("💡 在测试环境中，消费失败是正常的");
                    }
                } else {
                    System.out.println("ℹ️ 购买已消费，跳过消费步骤");
                }
                
                System.out.println("✅ 测试完成 - 购买处理成功");
                
            } else {
                System.err.println("❌ 购买状态无效: " + verified.getPurchaseState());
                System.out.println("💡 购买状态说明:");
                System.out.println("   0 = 待处理");
                System.out.println("   1 = 已购买");
                System.out.println("   2 = 已取消");
            }
            
        } catch (Exception e) {
            System.err.println("❌ 购买验证失败: " + e.getMessage());
        }
        
        System.out.println("\n📝 测试环境说明:");
        System.out.println("   - 此方法绕过了 Google Play 的确认步骤");
        System.out.println("   - 仅适用于测试环境");
        System.out.println("   - 生产环境必须正确配置服务账号权限");
        System.out.println("   - 生产环境必须调用 acknowledgePurchase");
    }

    @Test
    public void handleRefundedOrder() throws Exception {
        System.out.println("🔄 处理已退款订单...");
        
        String productId = "test_00123";
        String oldPurchaseToken = "hmaojobaojhmomhdacmmohin.AO-J1OzvoGcTmkA9tHIGmyso-q6eJbayw7Ga1e2XucvaaXIhzR4wl6_7TfIvuPPgUAuFr9h34tfZouLHxsWj-DmQR7exVbZyEg";
        
        System.out.println("📋 当前情况:");
        System.out.println("   - 订单ID: GPA.3391-3920-9094-65903");
        System.out.println("   - 产品ID: " + productId);
        System.out.println("   - 状态: 已退款");
        System.out.println("   - 购买令牌: " + oldPurchaseToken);
        
        System.out.println("\n💡 解决方案:");
        System.out.println("1️⃣ 在 Google Play Console 中:");
        System.out.println("   - 进入 '订单管理'");
        System.out.println("   - 查看订单 GPA.3391-3920-9094-65903");
        System.out.println("   - 确认退款原因");
        System.out.println("   - 检查退款时间");
        
        System.out.println("\n2️⃣ 创建新的测试购买:");
        System.out.println("   - 在测试设备上重新购买产品");
        System.out.println("   - 获取新的购买令牌");
        System.out.println("   - 使用新的令牌进行测试");
        
        System.out.println("\n3️⃣ 检查产品配置:");
        System.out.println("   - 确保产品状态为 '活跃'");
        System.out.println("   - 检查价格设置");
        System.out.println("   - 验证产品类型");
        
        System.out.println("\n4️⃣ 检查测试账户:");
        System.out.println("   - 确保测试账户有效");
        System.out.println("   - 检查支付方式");
        System.out.println("   - 验证账户权限");
        
        System.out.println("\n🔧 建议操作:");
        System.out.println("   - 在测试设备上重新购买产品 test_00123");
        System.out.println("   - 获取新的购买令牌");
        System.out.println("   - 更新测试代码中的令牌");
        System.out.println("   - 重新运行测试");
        
        System.out.println("\n⚠️ 注意事项:");
        System.out.println("   - 已退款的订单无法再次确认");
        System.out.println("   - 需要创建新的购买进行测试");
        System.out.println("   - 检查是否有自动退款策略");
    }

    @Test
    public void testWithNewPurchaseToken() throws Exception {
        System.out.println("🆕 使用新购买令牌测试...");
        
        // 这里需要替换为新的购买令牌
        String productId = "test_00123";
        String newPurchaseToken = "NEW_PURCHASE_TOKEN_HERE"; // 需要替换为实际的令牌
        
        System.out.println("📦 包名: " + PACKAGE_NAME);
        System.out.println("🆔 产品ID: " + productId);
        System.out.println("🎫 新购买令牌: " + newPurchaseToken);
        
        if ("NEW_PURCHASE_TOKEN_HERE".equals(newPurchaseToken)) {
            System.out.println("❌ 请先获取新的购买令牌");
            System.out.println("💡 获取步骤:");
            System.out.println("   1. 在测试设备上安装应用");
            System.out.println("   2. 购买产品 test_00123");
            System.out.println("   3. 从购买响应中获取 purchaseToken");
            System.out.println("   4. 替换代码中的 NEW_PURCHASE_TOKEN_HERE");
            return;
        }
        
        try {
            // 1. 验证新购买
            Purchase verified = googlePlayService.verifyPurchase(PACKAGE_NAME, productId, newPurchaseToken);
            System.out.println("✅ 新购买验证成功");
            System.out.println("   - 订单ID: " + verified.getOrderId());
            System.out.println("   - 购买状态: " + verified.getPurchaseState());
            System.out.println("   - 消费状态: " + verified.getConsumptionState());
            System.out.println("   - 是否已确认: " + verified.isAcknowledged());
            System.out.println("   - 购买时间: " + verified.getPurchaseTime());
            
            // 2. 检查购买状态
            if ("1".equals(verified.getPurchaseState())) {
                System.out.println("✅ 购买状态有效 (已购买)");
                
                // 3. 尝试确认购买
                if (!verified.isAcknowledged()) {
                    try {
                        googlePlayService.acknowledgePurchase(PACKAGE_NAME, productId, newPurchaseToken);
                        System.out.println("✅ 购买确认成功");
                    } catch (Exception e) {
                        System.err.println("❌ 购买确认失败: " + e.getMessage());
                        System.out.println("💡 可能的原因:");
                        System.out.println("   - 服务账号权限不足");
                        System.out.println("   - 购买令牌无效");
                        System.out.println("   - 产品配置问题");
                    }
                } else {
                    System.out.println("ℹ️ 购买已确认，跳过确认步骤");
                }
                
                // 4. 处理业务逻辑
                System.out.println("🎁 处理业务逻辑...");
                System.out.println("   - 解锁高级功能");
                System.out.println("   - 移除广告");
                System.out.println("   - 提供专属内容");
                
                // 5. 尝试消费购买
                if ("0".equals(verified.getConsumptionState())) {
                    try {
                        googlePlayService.consumePurchase(PACKAGE_NAME, productId, newPurchaseToken);
                        System.out.println("✅ 购买消费成功");
                    } catch (Exception e) {
                        System.err.println("❌ 购买消费失败: " + e.getMessage());
                    }
                } else {
                    System.out.println("ℹ️ 购买已消费，跳过消费步骤");
                }
                
            } else {
                System.err.println("❌ 购买状态无效: " + verified.getPurchaseState());
            }
            
        } catch (Exception e) {
            System.err.println("❌ 购买验证失败: " + e.getMessage());
        }
    }

    @Test
    public void handlePendingVerificationOrder() throws Exception {
        System.out.println("⏳ 处理待验证订单...");
        
        String productId = "test_00123";
        String purchaseToken = "hmaojobaojhmomhdacmmohin.AO-J1OzvoGcTmkA9tHIGmyso-q6eJbayw7Ga1e2XucvaaXIhzR4wl6_7TfIvuPPgUAuFr9h34tfZouLHxsWj-DmQR7exVbZyEg";
        
        System.out.println("📋 订单状态分析:");
        System.out.println("   - 状态: 尚待驗證 (Pending Verification)");
        System.out.println("   - 时间: 2025年7月1日 上午6:34:56 (世界標準時間)");
        System.out.println("   - 事件: Order received");
        System.out.println("   - 产品ID: " + productId);
        System.out.println("   - 购买令牌: " + purchaseToken);
        
        System.out.println("\n💡 状态说明:");
        System.out.println("✅ 正常情况:");
        System.out.println("   - 新订单通常需要几分钟到几小时验证");
        System.out.println("   - Google 进行反欺诈和安全检查");
        System.out.println("   - 验证通过后状态会变为 '已购买'");
        
        System.out.println("⚠️ 可能的问题:");
        System.out.println("   - 支付方式有问题");
        System.out.println("   - 账户被标记为可疑");
        System.out.println("   - 地理位置异常");
        System.out.println("   - 购买频率过高");
        
        System.out.println("\n🔧 建议操作:");
        System.out.println("1️⃣ 等待验证完成:");
        System.out.println("   - 通常需要 5-30 分钟");
        System.out.println("   - 最长可能需要 24 小时");
        System.out.println("   - 定期检查订单状态");
        
        System.out.println("2️⃣ 检查测试环境:");
        System.out.println("   - 确保使用正确的测试账户");
        System.out.println("   - 检查支付方式是否有效");
        System.out.println("   - 验证设备是否在允许的地区");
        
        System.out.println("3️⃣ 监控订单状态:");
        System.out.println("   - 在 Google Play Console 中监控");
        System.out.println("   - 检查是否有新的状态更新");
        System.out.println("   - 准备处理验证失败的情况");
        
        System.out.println("\n📊 对收款的影响:");
        System.out.println("   - 验证期间: 收款暂时冻结");
        System.out.println("   - 验证成功: 正常收款");
        System.out.println("   - 验证失败: 订单取消，无收款");
        
        System.out.println("\n🔄 测试建议:");
        System.out.println("   - 等待当前订单验证完成");
        System.out.println("   - 如果验证失败，创建新的测试购买");
        System.out.println("   - 使用不同的测试账户和支付方式");
    }

    @Test
    public void checkCurrentOrderStatus() throws Exception {
        System.out.println("🔍 检查当前订单状态...");
        
        String productId = "test_00123";
        String purchaseToken = "hmaojobaojhmomhdacmmohin.AO-J1OzvoGcTmkA9tHIGmyso-q6eJbayw7Ga1e2XucvaaXIhzR4wl6_7TfIvuPPgUAuFr9h34tfZouLHxsWj-DmQR7exVbZyEg";
        
        System.out.println("📦 包名: " + PACKAGE_NAME);
        System.out.println("🆔 产品ID: " + productId);
        System.out.println("🎫 购买令牌: " + purchaseToken);
        
        try {
            // 验证购买并检查状态
            Purchase verified = googlePlayService.verifyPurchase(PACKAGE_NAME, productId, purchaseToken);
            System.out.println("✅ 购买验证成功");
            System.out.println("   - 订单ID: " + verified.getOrderId());
            System.out.println("   - 购买状态: " + verified.getPurchaseState());
            System.out.println("   - 消费状态: " + verified.getConsumptionState());
            System.out.println("   - 是否已确认: " + verified.isAcknowledged());
            System.out.println("   - 购买时间: " + verified.getPurchaseTime());
            
            // 分析购买状态
            System.out.println("\n📊 状态分析:");
            switch (verified.getPurchaseState()) {
                case "0":
                    System.out.println("⏳ 状态: 待处理 (Pending)");
                    System.out.println("💡 订单正在等待 Google 验证");
                    System.out.println("⏰ 建议等待 5-30 分钟");
                    break;
                case "1":
                    System.out.println("✅ 状态: 已购买 (Purchased)");
                    System.out.println("💡 订单验证成功，可以处理");
                    break;
                case "2":
                    System.out.println("❌ 状态: 已取消 (Cancelled)");
                    System.out.println("💡 订单被取消，无法处理");
                    break;
                default:
                    System.out.println("❓ 状态: 未知 (" + verified.getPurchaseState() + ")");
                    break;
            }
            
            // 检查是否可以处理
            if ("1".equals(verified.getPurchaseState())) {
                System.out.println("\n🎯 可以处理订单:");
                if (!verified.isAcknowledged()) {
                    System.out.println("   - 需要确认购买");
                    try {
                        googlePlayService.acknowledgePurchase(PACKAGE_NAME, productId, purchaseToken);
                        System.out.println("   ✅ 购买确认成功");
                    } catch (Exception e) {
                        System.err.println("   ❌ 购买确认失败: " + e.getMessage());
                    }
                } else {
                    System.out.println("   - 购买已确认");
                }
                
                if ("0".equals(verified.getConsumptionState())) {
                    System.out.println("   - 需要消费购买");
                    try {
                        googlePlayService.consumePurchase(PACKAGE_NAME, productId, purchaseToken);
                        System.out.println("   ✅ 购买消费成功");
                    } catch (Exception e) {
                        System.err.println("   ❌ 购买消费失败: " + e.getMessage());
                    }
                } else {
                    System.out.println("   - 购买已消费");
                }
                
            } else if ("0".equals(verified.getPurchaseState())) {
                System.out.println("\n⏳ 订单待验证:");
                System.out.println("   - 请等待 Google 验证完成");
                System.out.println("   - 通常需要 5-30 分钟");
                System.out.println("   - 验证完成后重新运行此测试");
                
            } else {
                System.out.println("\n❌ 订单无法处理:");
                System.out.println("   - 状态: " + verified.getPurchaseState());
                System.out.println("   - 需要创建新的测试购买");
            }
            
        } catch (Exception e) {
            System.err.println("❌ 购买验证失败: " + e.getMessage());
            System.out.println("💡 可能的原因:");
            System.out.println("   - 订单仍在验证中");
            System.out.println("   - 购买令牌无效");
            System.out.println("   - 服务账号权限问题");
        }
    }

    @Test
    public void testWorkWithExistingSubscription() throws Exception {
        System.out.println("🔧 测试使用现有订阅产品...");
        
        try {
            // 获取现有订阅
            Subscription existingSubscription = googlePlaySubscriptionService.getSubscription(PACKAGE_NAME, "subscription1");
            System.out.println("✅ 获取现有订阅成功: " + existingSubscription.getProductId());
            
            // 尝试为现有订阅添加新的基础计划
            System.out.println("\n🔧 尝试为现有订阅添加新的基础计划...");
            try {
                // 创建新的基础计划
                BasePlan newBasePlan = new BasePlan();
                newBasePlan.setBasePlanId("monthly_plan");
                newBasePlan.setState("ACTIVE");
                
                AutoRenewingBasePlanType autoRenewingType = new AutoRenewingBasePlanType();
                autoRenewingType.setBillingPeriodDuration("P1M"); // 月度订阅
                autoRenewingType.setGracePeriodDuration("P3D");
                newBasePlan.setAutoRenewingBasePlanType(autoRenewingType);
                
                // 创建订阅更新对象
                Subscription updateSubscription = new Subscription();
                updateSubscription.setPackageName(PACKAGE_NAME);
                updateSubscription.setProductId("subscription1"); // 使用现有订阅ID
                updateSubscription.setBasePlans(Arrays.asList(newBasePlan));
                
                System.out.println("更新订阅参数:");
                System.out.println("Package Name: " + updateSubscription.getPackageName());
                System.out.println("Product ID: " + updateSubscription.getProductId());
                System.out.println("Base Plans count: " + (updateSubscription.getBasePlans() != null ? updateSubscription.getBasePlans().size() : 0));
                
                // 尝试更新订阅
                AndroidPublisher publisher = googlePlayConfig.getAndroidPublisher(PACKAGE_NAME);
                Subscription updatedSubscription = publisher.monetization().subscriptions()
                    .patch(PACKAGE_NAME, "subscription1", updateSubscription)
                    .execute();
                
                System.out.println("✅ 更新订阅成功: " + updatedSubscription.getProductId());
                System.out.println("基础计划数量: " + (updatedSubscription.getBasePlans() != null ? updatedSubscription.getBasePlans().size() : 0));
                
            } catch (Exception e) {
                System.err.println("❌ 更新订阅失败: " + e.getMessage());
                if (e instanceof com.google.api.client.googleapis.json.GoogleJsonResponseException) {
                    com.google.api.client.googleapis.json.GoogleJsonResponseException gjre = 
                        (com.google.api.client.googleapis.json.GoogleJsonResponseException) e;
                    System.err.println("Response code: " + gjre.getStatusCode());
                    System.err.println("Response body: " + gjre.getDetails());
                }
            }
            
        } catch (Exception e) {
            System.err.println("❌ 获取现有订阅失败: " + e.getMessage());
        }
    }

    @Test
    public void testUpdateExistingSubscription2() throws Exception {
//        BasePlan basePlan = googlePlaySubscriptionService.createBasePlan(
//                "test_plan_001233",
//                "US",
//                "P1M",
//                "P3D",
//                "USD",
//                1L,
//                1990000000
//        );

//        googlePlaySubscriptionService.createSubscriptionBasePlan(PACKAGE_NAME, "subscription1", basePlan);
    }

    @Test
    public void testUpdateExistingSubscription() throws Exception {
        System.out.println("🔧 测试更新现有订阅...");
        
        try {
            // 获取现有订阅
            Subscription existingSubscription = googlePlaySubscriptionService.getSubscription(PACKAGE_NAME, "subscription1");
            System.out.println("✅ 获取现有订阅成功: " + JSONObject.toJSONString(existingSubscription));
            
            // 尝试更新现有订阅（添加新的基础计划）
            System.out.println("\n🔧 尝试添加新的基础计划到现有订阅...");
            try {
                // 创建新的基础计划
                BasePlan newBasePlan = new BasePlan();
                newBasePlan.setBasePlanId("test_plan_001233");
                newBasePlan.setState("ACTIVE");
                
                AutoRenewingBasePlanType autoRenewingType = new AutoRenewingBasePlanType();
                autoRenewingType.setBillingPeriodDuration("P1M");
                autoRenewingType.setGracePeriodDuration("P3D");
                newBasePlan.setAutoRenewingBasePlanType(autoRenewingType);

                RegionalBasePlanConfig regionalBasePlanConfig = new RegionalBasePlanConfig();
                regionalBasePlanConfig.setRegionCode("HK");
                Money price = new Money();
                price.setCurrencyCode("HKD");
                price.setUnits(1L);
                price.setNanos(990000000);
                regionalBasePlanConfig.setPrice(price);
                newBasePlan.setRegionalConfigs(Arrays.asList(regionalBasePlanConfig));
                
                // 添加到现有订阅
                if (existingSubscription.getBasePlans() == null) {
                    existingSubscription.setBasePlans(Arrays.asList(newBasePlan));
                } else {
                    List<BasePlan> updatedBasePlans = new ArrayList<>(existingSubscription.getBasePlans());
                    updatedBasePlans.add(newBasePlan);
                    existingSubscription.setBasePlans(updatedBasePlans);
                }


                // 尝试更新订阅
                AndroidPublisher publisher = googlePlayConfig.getAndroidPublisher(PACKAGE_NAME);
                
                // 添加updateMask参数，指定要更新的字段
                String updateMask = "basePlans"; // 我们要更新基础计划

                String regionsVersion = googlePlaySubscriptionService.getSubscriptionRegionsVersion(PACKAGE_NAME, "subscription1");
                System.out.println("使用地区版本: " + regionsVersion);

                Subscription updatedSubscription = publisher.monetization().subscriptions()
                    .patch(PACKAGE_NAME, "subscription1", existingSubscription)
                    .setUpdateMask(updateMask)
                    .setRegionsVersionVersion(regionsVersion)
                    .execute();

                System.out.println("✅ 指定regionsVersion更新成功: " + updatedSubscription.getProductId());
                    System.out.println("基础计划数量: " + (updatedSubscription.getBasePlans() != null ? updatedSubscription.getBasePlans().size() : 0));

            } catch (Exception e) {
                System.err.println("❌ 更新订阅失败: " + e.getMessage());
                if (e instanceof com.google.api.client.googleapis.json.GoogleJsonResponseException) {
                    com.google.api.client.googleapis.json.GoogleJsonResponseException gjre = 
                        (com.google.api.client.googleapis.json.GoogleJsonResponseException) e;
                    System.err.println("Response code: " + gjre.getStatusCode());
                    System.err.println("Response body: " + gjre.getDetails());
                }
            }
            
        } catch (Exception e) {
            System.err.println("❌ 获取现有订阅失败: " + e.getMessage());
        }
    }

    @Test
    public void testCreateSubscriptionWithCorrectRegionVersion() throws Exception {
        BigDecimal originalPrice = new BigDecimal("20");
        // 正确计算Money对象的units和nanos
        long priceUnits = originalPrice.longValue(); // 整数部分
        int priceNanos = originalPrice.subtract(new BigDecimal(priceUnits))
                .multiply(new BigDecimal("1000000000")).intValue(); // 小数部分转换为纳秒

        // 使用新方法创建基础计划，同时获取正确的地区版本
        GooglePlaySubscriptionService.BasePlanCreationResult basePlanResult = 
            googlePlaySubscriptionService.builderBasePlanWithRegionVersion(
                PACKAGE_NAME, "tt123456", "P1M", "P1D", "USD", priceUnits, priceNanos);
        
        BasePlan basePlan = basePlanResult.getBasePlan();
        String regionVersion = basePlanResult.getRegionVersion();
        
        log.info("创建的基础计划包含 {} 个地区配置", 
            basePlan.getRegionalConfigs() != null ? basePlan.getRegionalConfigs().size() : 0);
        log.info("使用地区版本: {}", regionVersion);

        // 创建订阅描述
        List<SubscriptionListing> listings = new ArrayList<>();
        
        SubscriptionListing enListing = new SubscriptionListing();
        enListing.setLanguageCode("en-US");
        enListing.setTitle("Test Subscription with Correct Region Version");
        enListing.setDescription("This subscription uses the correct region version from API");
        listings.add(enListing);

        // 手动创建订阅，使用正确的地区版本
        try {
            AndroidPublisher publisher = googlePlayConfig.getAndroidPublisher(PACKAGE_NAME);
            
            // 创建新的订阅产品
            Subscription newSubscription = new Subscription();
            newSubscription.setProductId("ss12");
            newSubscription.setListings(listings);
            newSubscription.setBasePlans(Arrays.asList(basePlan));
            
            // 使用从价格转换API获取的正确地区版本
            Subscription createdSubscription = publisher.monetization().subscriptions()
                    .create(PACKAGE_NAME, newSubscription)
                    .setProductId("ss12")
                    .setRegionsVersionVersion(regionVersion)  // 使用正确的地区版本
                    .execute();
            
            log.info("✅ 创建订阅产品成功: {}", createdSubscription.getProductId());
            log.info("基础计划数量: {}", (createdSubscription.getBasePlans() != null ? createdSubscription.getBasePlans().size() : 0));
            
            // 激活基础计划
            googlePlaySubscriptionService.activateBasePlan(PACKAGE_NAME, "ss12", "tt123456");
            
        } catch (Exception e) {
            log.error("❌ 创建订阅产品失败: {}", e.getMessage(), e);
            throw e;
        }
    }
}
