import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import tv.shorthub.api.queue.PaypalOrderUpdateQueueMessageHandler;
import tv.shorthub.system.domain.AppOrderInfo;
import tv.shorthub.system.domain.PaypalPaymentLog;
import tv.shorthub.system.service.IAppOrderInfoService;
import tv.shorthub.system.service.IPaypalPaymentLogService;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = tv.shorthub.ShorthubApiApplication.class)
public class TestPaypalOrderUpdateQueueMessageHandler {

    @Autowired
    private PaypalOrderUpdateQueueMessageHandler handler;

    @Autowired
    private IAppOrderInfoService appOrderInfoService;

    @Autowired
    private IPaypalPaymentLogService paypalPaymentLogService;

    @Test
    public void testHandleMessage() {
       
        String orderNo = "1931276564690014208";
        AppOrderInfo orderInfo = appOrderInfoService.getMapper().selectOne(new LambdaQueryWrapper<AppOrderInfo>().eq(AppOrderInfo::getOrderNo, orderNo));
        PaypalPaymentLog paypalPaymentLog = paypalPaymentLogService.getMapper().selectOne(new LambdaQueryWrapper<PaypalPaymentLog>().eq(PaypalPaymentLog::getOrderNo, orderNo));

        handler.rechargeFulfill(orderInfo, paypalPaymentLog);
    }
}
