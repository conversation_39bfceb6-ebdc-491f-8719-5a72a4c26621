import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import tv.shorthub.airwallex.model.PaymentIntentRequest;
import tv.shorthub.airwallex.service.AirwallexConfigStorageHolder;
import tv.shorthub.airwallex.service.AirwallexPaymentConsentService;
import tv.shorthub.airwallex.service.AirwallexPaymentService;
import tv.shorthub.api.controller.req.CreateOrderRequest;
import tv.shorthub.api.service.recharge.RechargeService;
import tv.shorthub.common.core.session.UserSessionDTO;
import tv.shorthub.api.session.UserSessionManager;
import tv.shorthub.common.enums.OrderChannelEnums;
import tv.shorthub.paypal.config.PaypalConfigStorageHolder;
import tv.shorthub.paypal.service.PayPalBillingAgreementService;

import java.math.BigDecimal;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = tv.shorthub.ShorthubApiApplication.class)
@Slf4j
public class TestRechargeService {

    @Autowired
    private RechargeService rechargeService;

    @Autowired
    PayPalBillingAgreementService payPalBillingAgreementService;

    @Autowired
    AirwallexPaymentService airwallexPaymentService;

    @Autowired
    AirwallexPaymentConsentService airwallexPaymentConsentService;

    @Test
    public void testConsent() {
        AirwallexConfigStorageHolder.set("2i7p1l6PRZyX0D-yVQKSnQ");

//        JSONObject jsonObject = airwallexPaymentService.queryCustomerList("222333");
//        log.info("查询客户信息: {}", jsonObject);

        JSONObject jsonObject1 = airwallexPaymentConsentService.queryPaymentConsent("cst_hkdmq4gxwh9bsh7g1pf");
        log.info("查询支付同意书: {}", jsonObject1);

        // 验证consent
//        JSONObject jsonObject = airwallexPaymentConsentService.verifyPaymentConsent("cst_hkdm8g52nh9bqo2ar0m", "card", "mtd_hkdm8g52nh9bqoff3nh", "USD");
//        log.info("验证支付同意书: {}", jsonObject);
    }

    @Test
    public void testConfirm() {

        // 获取到前端的payment_consent_id或通过webhook监听
        AirwallexConfigStorageHolder.set("2i7p1l6PRZyX0D-yVQKSnQ");
        PaymentIntentRequest request = new PaymentIntentRequest();
        request.setCustomerId("cus_hkdmq4gxwh9bon8aumw");
        request.setAmount(BigDecimal.valueOf(22));
        request.setCurrency("USD");
        request.setMerchantOrderId(IdUtil.getSnowflakeNextIdStr());
        request.setRequestId(IdUtil.getSnowflakeNextIdStr());
//        request.setReturnUrl(frontCallbackUrl);
        JSONObject paymentIntent = airwallexPaymentService.createPaymentIntent(request);

        // 后续付费
        JSONObject jsonObject = airwallexPaymentService.confirmPaymentIntent(paymentIntent.getString("id"), "cst_hkdm8g52nh9bvne7hiw");
        log.info("确认支付: {}", jsonObject);
    }

    @Test
    public void testGet() throws Exception {
        PaypalConfigStorageHolder.set("AfVRnljc_AK3KGnsLxSP1GDwhsKx16Xl-TYex7nHL9CxtvDJtL17yzoCVvar9C59kHk5QDJNBq8MnNyQ");
        payPalBillingAgreementService.getBillingAgreement("0da67722ka507413b");
    }

    @Test
    public void createOrder() throws Exception {
        UserSessionDTO userSessionDTO = new UserSessionDTO();
        userSessionDTO.setLanguage("zh-TW");
        UserSessionManager.session.set(userSessionDTO);
        CreateOrderRequest request = new CreateOrderRequest();
        request.setPayMethod(OrderChannelEnums.PAYERMAX.getValue());
//        request.setItemId("1932634427532378112");
        request.setItemId("1932628919220072448"); // 订阅
        rechargeService.createOrder(request);
    }
}
