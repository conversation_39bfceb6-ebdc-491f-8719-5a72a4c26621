import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import tv.shorthub.system.domain.PaypalPaymentLifecycle;
import tv.shorthub.system.mapper.PaypalPaymentLifecycleMapper;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = tv.shorthub.ShorthubApiApplication.class)
@Slf4j
public class TestPaypalPaymentLifecycleMapper {

    @Autowired
    PaypalPaymentLifecycleMapper paypalPaymentLifecycleMapper;

    @Test
    public void testActivate() throws Exception {
        List<PaypalPaymentLifecycle> lists = new ArrayList<>();
        {
            PaypalPaymentLifecycle lifecycle = new PaypalPaymentLifecycle();
            lifecycle.setOrderNo("20230701");
            lifecycle.setUserId("1");
            lifecycle.setState("CREATED");
            lifecycle.setCreateTime(new Date());
            lists.add(lifecycle);
        }
        paypalPaymentLifecycleMapper.batchInsertOrUpdate(lists);
    }
}
