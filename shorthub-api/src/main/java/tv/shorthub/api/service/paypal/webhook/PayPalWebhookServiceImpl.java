package tv.shorthub.api.service.paypal.webhook;

import com.alibaba.fastjson2.JSONObject;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import tv.shorthub.event.service.event.PayPalWebhookEventHandler;


import java.util.List;
import java.util.Optional;

/**
 * PayPal Webhook 服务实现类
 * 负责接收和分发 PayPal 的 Webhook 事件
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PayPalWebhookServiceImpl implements PayPalWebhookService {

    private final List<PayPalWebhookEventHandler> eventHandlers;

    @Override
    public void processWebhook(String payload, String transmissionId, String transmissionTime,
                             String certUrl, String transmissionSig) {
        JSONObject event = JSONObject.parseObject(payload);
        String eventType = event.getString("event_type");

        log.info("[PayPal Webhook] 开始处理事件 - 类型: {}, 传输ID: {}", eventType, transmissionId);

        // 查找对应的处理器
        Optional<PayPalWebhookEventHandler> handlerOpt = eventHandlers.stream()
            .filter(h -> h.getEventType().equals(eventType))
            .findFirst();

        if (!handlerOpt.isPresent()) {
            log.warn("[PayPal Webhook] 未找到处理器 - 类型: {}. 可用处理器: {}",
                eventType, eventHandlers.stream().map(h -> h.getEventType()).toList());
            return;
        }

        PayPalWebhookEventHandler handler = handlerOpt.get();
        try {
            log.info("[PayPal Webhook] 处理事件 - 类型: {}, 处理器: {}",
                eventType, handler.getClass().getSimpleName());
            handler.handle(event);
            log.info("[PayPal Webhook] 事件处理成功 - 类型: {}", eventType);
        } catch (Exception e) {
            log.error("[PayPal Webhook] 事件处理失败 - 类型: {}, 传输ID: {}, 错误: {}",
                eventType, transmissionId, e.getMessage(), e);
            // 直接抛出异常，让上层返回非2xx，交给PayPal自动重试
            throw new RuntimeException("PayPal Webhook事件处理失败: " + eventType, e);
        }
    }
}
