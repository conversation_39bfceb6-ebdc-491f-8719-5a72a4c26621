package tv.shorthub.api.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import tv.shorthub.ad.domain.RetargetingEventDataRegistry;
import tv.shorthub.api.controller.req.AppUserClosePayPageRecordRequest;
import tv.shorthub.api.controller.req.HistoryUpdateRequest;
import tv.shorthub.api.controller.res.DiscountItemResponse;
import tv.shorthub.api.schedule.ScheduleClosePayPageRecords;
import tv.shorthub.api.schedule.SchedulePushAppUserWatchRecords;
import tv.shorthub.api.service.ad.RetargetingServiceAdaptor;
import tv.shorthub.api.service.cache.CacheHolder;
import tv.shorthub.api.service.recharge.RechargeService;
import tv.shorthub.api.utils.MessageCode;
import tv.shorthub.common.core.cache.CacheKeyUtils;
import tv.shorthub.common.core.redis.RedisCache;
import tv.shorthub.common.dto.*;
import tv.shorthub.api.session.UserSessionManager;
import tv.shorthub.common.enums.AdPlatformEnum;
import tv.shorthub.common.enums.CloudflareQueueEnum;
import tv.shorthub.common.exception.ApiException;
import tv.shorthub.common.queue.CloudflareQueueService;
import tv.shorthub.common.utils.StringUtils;
import tv.shorthub.paypal.service.PayPalAccountService;
import tv.shorthub.system.domain.*;
import tv.shorthub.system.service.*;

import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class UserService {

    @Autowired
    IAppUsersService appUsersService;

    @Autowired
    IAppUserWatchContentService appUserWatchContentService;

    @Autowired
    IAppUserWatchRecordsService appUserWatchRecordsService;

    @Autowired
    IAppUserCollectService appUserCollectService;

    @Autowired
    IAppLoginRecordsService appLoginRecordsService;

    @Autowired
    CacheHolder cacheHolder;

    @Autowired
    RedisCache redisCache;

    @Autowired
    PayPalAccountService payPalAccountService;

    @Autowired
    RetargetingServiceAdaptor retargetingServiceAdaptor;

    @Autowired
    SchedulePushAppUserWatchRecords schedulePushAppUserWatchRecords;

    @Autowired
    CloudflareQueueService cloudflareQueueService;

    @Autowired
    AppUserClosePayPageRecordService appUserClosePayPageService;

    @Autowired
    RechargeService rechargeService;

    @Autowired
    IAppRechargeItemService appRechargeItemService;

    @Autowired
    ScheduleClosePayPageRecords scheduleClosePayPageRecords;

    public UserInfo getUserInfoWithAccessToken(String accessToken) {
        String userId = cacheHolder.getUserIdWithAccessToken(accessToken);
        if (StringUtils.isNotEmpty(userId)) {
            log.info("login with accessToken: {}, userId={}", accessToken, userId);
            // 续期token时间
            redisCache.setCacheObject(CacheKeyUtils.getUserIdWithAccessToken(accessToken), userId, 30, TimeUnit.DAYS);
            log.info("续期accessToken: {}, userId={}", accessToken, userId);
            return getUserInfo(userId);
        }
        throw new ApiException(MessageCode.USER_NOT_EXIST.getCode());
    }

    public UserInfo getUserInfo() {
        return getUserInfo(UserSessionManager.getUserId());
    }
    public UserInfo getUserInfo(String userId) {
        AppUsers user = appUsersService.getMapper().selectOne(new QueryWrapper<AppUsers>().eq("user_id", userId));
        if (null == user) {
            return null;
//            // 通过设备id查询用户信息
//            user = appUsersService.getMapper().selectOne(new QueryWrapper<AppUsers>().eq("device_id", UserSessionManager.getSession().getDeviceId()).last("limit 1"));
//            if (null == user) {
//                return null;
//            }
//            log.info("通过deviceId查询到用户信息: {}, {}", user.getDeviceId(), user.getUserId());
        }
        return getUserInfo(user);
    }

    public UserInfo getUserInfo(AppUsers user) {
        UserInfo userInfo = new UserInfo();
        userInfo.setLanguage(user.getLanguageCode());
        userInfo.setUserId(user.getUserId());
        userInfo.setUsername(user.getUsername());
        userInfo.setEmail(user.getEmail());
        userInfo.setBalanceCoin(user.getBalanceCoin());
        userInfo.setBalanceBonus(user.getBalanceBonus());
        userInfo.setProvider(user.getProvider());
        userInfo.setMemberTime(user.getMemberTime());
        if (null != UserSessionManager.getSession()) {
            UserSessionManager.getSession().refreshUser(userInfo);
        }
        return userInfo;
    }
    public UserInfo registry(String email, String provider, String providerId, String sessionId, String tfid, String country, String language, String ip, String userAgent, String deviceId) {
        return registry(email, provider, providerId, sessionId, tfid, country, language, ip, userAgent, null, deviceId);
    }

    @Transactional(rollbackFor = Exception.class)
    public UserInfo registry(String email, String provider, String providerId, String sessionId, String tfid, String country, String language, String ip, String userAgent, String password, String deviceId) {
        String userId = IdUtil.getSnowflakeNextIdStr();

        boolean allowRetargeting = false;
        // 先查询临时用户, 因为在充值成功后, 用户可能未登录那么会自动注册一个临时用户
        AppUsers emailUser = appUsersService.getMapper().selectOne(new QueryWrapper<AppUsers>().eq("email", "sid-" + sessionId).last("limit 1"));
        if (emailUser != null) {
            emailUser.setEmail(email);
            emailUser.setTfid(tfid);
            emailUser.setPassword(password);
            emailUser.setProvider(provider);
            emailUser.setProviderId(providerId);
            emailUser.setLanguageCode(language);
            emailUser.setUpdateTime(emailUser.getCreateTime());
            appUsersService.getMapper().updateById(emailUser);
            allowRetargeting = true;
            log.info("更新临时用户登录信息: {}", sessionId);
        } else {
            // 再按email查找
            emailUser = appUsersService.getMapper().selectOne(new QueryWrapper<AppUsers>().eq("email", email));
        }

        if (null != emailUser) {
            userId =  emailUser.getUserId();
            log.info("用户已存在, 不注册新的用户: {}", email);
        } else {

            log.info("用户不存在, 注册新用户: em:{}, pwd:{}, tfid: {}", email, password, tfid);

            // 保存用户信息
            emailUser = new AppUsers();
            emailUser.setUserId(userId);
            emailUser.setUsername(userId);
            emailUser.setTfid(tfid);
            emailUser.setDeviceId(deviceId);
            emailUser.setProvider(provider);
            emailUser.setProviderId(providerId);
            emailUser.setCountryCode(country);
            emailUser.setLanguageCode(language);
            emailUser.setPassword(password);

//            JSONObject extendJson = new JSONObject();
//            extendJson.put("attribution", cacheHolder.getAttributionByIp(ip));
//            emailUser.setExtendJson(extendJson);
            emailUser.setEmail(email);
            emailUser.setCreateTime(new Date());
            emailUser.setUpdateTime(emailUser.getCreateTime());
            appUsersService.getMapper().batchInsertOrUpdate(List.of(emailUser));
            allowRetargeting = !"temp".equals(provider);
            log.info("注册成功: {}", userId);
        }

        UserSessionManager.login(userId, tfid);

        if (allowRetargeting) {
            // 注册事件埋点
            retargetingServiceAdaptor.processEvent(
                    RetargetingEventDataRegistry.builder()
                            .userId(userId)
                            .email(email)
                            .provider(provider)
                            .relationId(userId)
                            .tfid(tfid)
                            .sid(sessionId)
                            .clientIp(ip)
                            .clientUserAgent(userAgent)
                            .build()
            );
        }

        addLoginRecords(userId, provider, true, sessionId, tfid, ip, country);
        return getUserInfo(userId);
    }
    public void addLoginRecords(String userId) {
        addLoginRecords(userId, null, false, UserSessionManager.getSession().getSessionId(), UserSessionManager.getSession().getTfid(), UserSessionManager.getSession().getIp(), UserSessionManager.getSession().getCountry());
    }

    public void addLoginRecords(String userId, String provider, boolean newUser, String sessionId, String tfid, String ip, String country) {

        // 当前是临时登录, 不触发事件
        if ("temp".equals(provider)) {
            return;
        }
        JSONObject queueMessage = new JSONObject();
        queueMessage.put("session-id",  sessionId);
        if (StringUtils.isNotEmpty(provider)) {
            JSONObject extendJson = new JSONObject();
            extendJson.put("provider", provider);
            queueMessage.put("extend-json", extendJson);
        }
        queueMessage.put("tfid", tfid);
        queueMessage.put("userId", userId);
        queueMessage.put("ipAddress", ip);
        queueMessage.put("locationCountry", country);
        queueMessage.put("loginTime", new Date());
        queueMessage.put("createTime", new Date());
        queueMessage.put("newUser", newUser);

        cloudflareQueueService.sendMessage(CloudflareQueueEnum.LOGIN_RECORDS.getValue(), queueMessage);

    }

    public AppUsers getGoogleUser(String email) {
        QueryWrapper<AppUsers> query = new QueryWrapper<AppUsers>().eq("provider", AdPlatformEnum.Google.getValue()).eq("provider_id", email);
        return appUsersService.getMapper().selectOne(query);
    }

    public AppUsers getFacebookUser(String facebookId) {
        QueryWrapper<AppUsers> query = new QueryWrapper<AppUsers>().eq("provider", AdPlatformEnum.Facebook.getValue()).eq("provider_id", facebookId);
        return appUsersService.getMapper().selectOne(query);
    }

    /**
     * 获取用户剧目播放历史
     * @return
     */
    public List<ContentInfo> getContentHistory() {
        QueryWrapper<AppUserWatchContent> query = new QueryWrapper<AppUserWatchContent>().eq("user_id", UserSessionManager.getUserId());
        List<AppUserWatchContent> watchContentList = appUserWatchContentService.getMapper().selectList(query);

        return watchContentList.stream()
                .map(entry -> cacheHolder.getDrama(entry.getContentId()))
                .toList();
    }

    /**
     * 更新播放历史
     * @param request
     * @return
     */
    public boolean uploadContentHistory(HistoryUpdateRequest request) {
        schedulePushAppUserWatchRecords.addWatchRecord(
                UserSessionManager.getSession().getAppid(),
                UserSessionManager.getSession().getTfid(),
                UserSessionManager.getUserId(),
                request.getContentId(),
                request.getNumber(),
                request.getSeconds(),
                request.getWatchSeconds()
        );
        return true;
    }

    /**
     * 查询最后观看到哪一集
     * @param contentId
     * @return
     */
    public UserSerialInfo getRecordsHistoryLast(String contentId) {
        QueryWrapper<AppUserWatchRecords> query = new QueryWrapper<AppUserWatchRecords>()
                .eq("user_id", UserSessionManager.getUserId())
                .eq("content_id", contentId)
                .orderByDesc("update_time")
                .last(" LIMIT 1")
                ;
        AppUserWatchRecords lastRecord = appUserWatchRecordsService.getMapper().selectOne(query);
        if (null != lastRecord) {
            UserSerialInfo userSerialInfo = new UserSerialInfo();
            userSerialInfo.setContentId(lastRecord.getContentId());
            userSerialInfo.setNumber(lastRecord.getSerialNumber());
            userSerialInfo.setSeconds(lastRecord.getSerialSecond());
            return userSerialInfo;
        }
        return null;
    }

    /**
     * 查询用户收藏列表
     * @return
     */
    public List<ContentInfo> getContentCollectList() {
        QueryWrapper<AppUserCollect> queryWrapper = new QueryWrapper<AppUserCollect>()
                .eq("user_id", UserSessionManager.getUserId())
                .orderByDesc("update_time")
                ;
        List<AppUserCollect> appUserCollects = appUserCollectService.getMapper().selectList(queryWrapper);
        return appUserCollects.stream().map(entry -> cacheHolder.getDrama(entry.getContentId())).toList();
    }

    public boolean uploadContentCollect(String contentId) {
        AppUserCollect appUserCollect = new AppUserCollect();
        appUserCollect.setUserId(UserSessionManager.getUserId());
        appUserCollect.setContentId(contentId);
        appUserCollect.setCreateTime(new Date());
        appUserCollect.setUpdateTime(appUserCollect.getCreateTime());
        appUserCollectService.getMapper().batchInsertOrUpdate(List.of(appUserCollect));
        return true;
    }

    public boolean unContentCollect(String contentId) {
        appUserCollectService.getMapper().delete(new QueryWrapper<AppUserCollect>().eq("user_id", UserSessionManager.getUserId()).eq("content_id", contentId));
        return true;
    }

    public boolean deleteContentHistory(String contentId) {
        appUserWatchContentService.getMapper().delete(new QueryWrapper<AppUserWatchContent>().eq("user_id", UserSessionManager.getUserId()).eq("content_id", contentId));
        return true;
    }

    public void activateAccessToken(AppOrderInfo orderInfo) {
        String accessToken = orderInfo.getExtendJson().getString("accessTokenWithLogin");
        if (StringUtils.isNotEmpty(accessToken)) {
            redisCache.setCacheObject(CacheKeyUtils.getUserIdWithAccessToken(accessToken), orderInfo.getUserId(), 30, TimeUnit.DAYS);
            log.info("激活accessToken: {}, userId={}", accessToken, orderInfo.getUserId());
        }
    }

    public String getDeviceUserCache(String deviceId) {
        return redisCache.getCacheObject(CacheKeyUtils.getDeviceIdUserId(deviceId));
    }

    public void bindDeviceCache(String deviceId, String userId) {
        redisCache.setCacheObject(CacheKeyUtils.getDeviceIdUserId(deviceId), userId, 30, TimeUnit.DAYS);
        log.info("绑定设备和用户ID的关系: {}, userId={}", deviceId, userId);
    }

    public void userClosePayPageRecord(AppUserClosePayPageRecordRequest request) {
        AppUserClosePayPageRecord bean = new AppUserClosePayPageRecord( );
        bean.setItemId(request.getItemId());
        bean.setUserId(UserSessionManager.getUserId());
        bean.setCreateBy(UserSessionManager.getUserId());
        bean.setUpdateBy(UserSessionManager.getUserId());
        bean.setCreateTime(new Date());
        bean.setUpdateTime(new Date());
        scheduleClosePayPageRecords.addClosePayPageRecord(bean);
    }

    public DiscountItemResponse queryIsDiscount() {
        DiscountItemResponse discountItemResponse = new DiscountItemResponse();
        //不需要展示直接返回
        discountItemResponse.setIsShow(false);
        //查询用户是否已经支付过,完成支付不需要再弹窗
        List<OrderInfo> orderList = rechargeService.getOrderList();
        if (orderList == null || orderList.isEmpty()) {
            //查询用户是否有叉掉记录,有就返回最新一条数据
            AppUserClosePayPageRecord userClosePayPage =  appUserClosePayPageService.queryUserLatestRecordByUserId(UserSessionManager.getUserId());
            if (null != userClosePayPage) {
                //根据模版id查询模版消息,并且是启动状态的模版
                AppRechargeItem appRechargeItem = appRechargeItemService.getAppRechargeParentItemIsEnableById(userClosePayPage.getItemId());
                if (null != appRechargeItem && appRechargeItem.getId() != null) {
                    //需要展示
                    discountItemResponse.setIsShow(true);
                    discountItemResponse.setRechargeItemDTO(BeanUtil.toBean(appRechargeItem, RechargeItemDTO.class));
                }
            }
        }
        return discountItemResponse;
    }
}
