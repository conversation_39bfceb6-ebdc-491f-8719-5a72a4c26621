//package tv.shorthub.api.service;
//
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//import org.springframework.transaction.annotation.Transactional;
//import org.springframework.web.context.request.RequestContextHolder;
//import org.springframework.web.context.request.ServletRequestAttributes;
//import tv.shorthub.ad.domain.RetargetingEventDataOrderCreate;
//import tv.shorthub.ad.domain.RetargetingEventDataPayment;
//import tv.shorthub.ad.service.RetargetingService;
//import tv.shorthub.api.session.UserSessionManager;
//
//import jakarta.servlet.http.HttpServletRequest;
//import jakarta.servlet.http.Cookie;
//import java.math.BigDecimal;
//
///**
// * 充值服务 - 集成Facebook Cookie归因示例
// *
// * 关键特性：
// * 1. 在同步阶段提取Facebook Cookie数据
// * 2. 将归因数据传递给RetargetingEventData
// * 3. FacebookCallbackHandler从事件数据中获取归因信息
// */
//@Service
//@Slf4j
//public class RechargeServiceTest {
//
//    @Autowired
//    private RetargetingService retargetingService;
//
//    /**
//     * 创建订单事件 - 在同步阶段提取Cookie归因数据
//     */
//    public void sendOrderCreateEvent(String userId, String orderId, BigDecimal amount, String currency) {
//        try {
//            // 在同步阶段提取Facebook归因数据
//            FacebookCookieData facebookData = extractFacebookCookieData();
//
//            RetargetingEventDataOrderCreate orderEvent = RetargetingEventDataOrderCreate.builder()
//                .userId(userId)
//                .orderId(orderId)
//                .amount(amount)
//                .currency(currency)
//                .eventTime(System.currentTimeMillis() / 1000)
//                .sid(UserSessionManager.getSession().getSessionId())
//                .clientIp(UserSessionManager.getSession().getIp())
//                .clientUserAgent(getCurrentUserAgent())
//                .actionSource("website")
//                // 设置Facebook归因数据
////                .fbc(facebookData.getFbc())
////                .fbp(facebookData.getFbp())
////                .fbclid(facebookData.getFbclid())
////                .externalId(UserSessionManager.getSession().getSessionId())
//                .build();
//
//            retargetingService.processEvent(orderEvent);
//
//            log.info("Sent order create event with Facebook attribution - orderId: {}, fbc: {}, fbp: {}",
//                orderId, facebookData.getFbc(), facebookData.getFbp());
//
//        } catch (Exception e) {
//            log.error("Failed to send order create event", e);
//        }
//    }
//
//    /**
//     * 支付成功事件 - 在同步阶段提取Cookie归因数据
//     */
//    @Transactional(rollbackFor = Exception.class)
//    public void sendPaymentSuccessEvent(String userId, String orderId, String email, BigDecimal amount,
//                                      String currency, String paymentMethod) {
//        try {
//            // 在同步阶段提取Facebook归因数据
//            FacebookCookieData facebookData = extractFacebookCookieData();
//
//            RetargetingEventDataPayment paymentEvent = RetargetingEventDataPayment.builder()
//                .userId(userId)
//                .orderId(orderId)
//                .email(email)
//                .amount(amount)
//                .currency(currency)
//                .paymentMethod(paymentMethod)
//                .eventTime(System.currentTimeMillis() / 1000)
//                .sid(UserSessionManager.getSession().getSessionId())
//                .clientIp(UserSessionManager.getSession().getIp())
//                .clientUserAgent(getCurrentUserAgent())
//                .actionSource("website")
//                // 设置Facebook归因数据
////                .fbc(facebookData.getFbc())
////                .fbp(facebookData.getFbp())
////                .fbclid(facebookData.getFbclid())
////                .externalId(UserSessionManager.getSession().getSessionId())
//                .build();
//
//            retargetingService.processEvent(paymentEvent);
//
//            log.info("Sent payment event with Facebook attribution - orderId: {}, amount: {}, fbc: {}, fbp: {}",
//                orderId, amount, facebookData.getFbc(), facebookData.getFbp());
//
//        } catch (Exception e) {
//            log.error("Failed to send payment success event", e);
//        }
//    }
//
//    /**
//     * 从当前HTTP请求中提取Facebook Cookie数据
//     * 必须在同步阶段调用，异步队列中无法获取HTTP上下文
//     */
//    private FacebookCookieData extractFacebookCookieData() {
//        FacebookCookieData cookieData = new FacebookCookieData();
//
//        try {
//            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
//            if (attributes == null) {
//                log.debug("No request attributes found, unable to extract Facebook cookies");
//                return cookieData;
//            }
//
//            HttpServletRequest request = attributes.getRequest();
//            Cookie[] cookies = request.getCookies();
//            if (cookies == null) {
//                log.debug("No cookies found in request");
//                return cookieData;
//            }
//
//            for (Cookie cookie : cookies) {
//                if ("_fbc".equals(cookie.getName())) {
//                    cookieData.setFbc(cookie.getValue());
//                } else if ("_fbp".equals(cookie.getName())) {
//                    cookieData.setFbp(cookie.getValue());
//                }
//            }
//
//            // 从fbc中提取fbclid
//            if (cookieData.getFbc() != null) {
//                String fbclid = extractFbclidFromFbc(cookieData.getFbc());
//                cookieData.setFbclid(fbclid);
//            }
//
//            if (cookieData.getFbc() != null || cookieData.getFbp() != null) {
//                log.info("Extracted Facebook cookies - fbc: {}, fbp: {}, fbclid: {}",
//                    cookieData.getFbc(), cookieData.getFbp(), cookieData.getFbclid());
//            }
//
//        } catch (Exception e) {
//            log.warn("Failed to extract Facebook cookies from request", e);
//        }
//
//        return cookieData;
//    }
//
//    /**
//     * 从fbc中提取fbclid
//     */
//    private String extractFbclidFromFbc(String fbc) {
//        if (fbc == null) {
//            return null;
//        }
//
//        try {
//            String[] parts = fbc.split("\\.");
//            if (parts.length >= 4) {
//                StringBuilder fbclid = new StringBuilder();
//                for (int i = 3; i < parts.length; i++) {
//                    if (i > 3) fbclid.append(".");
//                    fbclid.append(parts[i]);
//                }
//                return fbclid.toString();
//            }
//        } catch (Exception e) {
//            log.warn("Failed to extract fbclid from fbc: {}", fbc);
//        }
//
//        return null;
//    }
//
//    /**
//     * 辅助方法：获取当前用户代理
//     */
//    private String getCurrentUserAgent() {
//        try {
//            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
//            if (attributes != null) {
//                HttpServletRequest request = attributes.getRequest();
//                return request.getHeader("User-Agent");
//            }
//        } catch (Exception e) {
//            log.warn("Failed to get current user agent", e);
//        }
//        return null;
//    }
//
//    /**
//     * 示例：完整的充值流程集成
//     */
//    @Transactional(rollbackFor = Exception.class)
//    public void processRecharge(String userId, String email, BigDecimal amount, String currency, String paymentMethod) {
//        try {
//            // 1. 创建订单
//            String orderId = generateOrderId();
//
//            // 2. 发送订单创建事件（带自动归因）
//            sendOrderCreateEvent(userId, orderId, amount, currency);
//
//            // 3. 处理支付逻辑
//            // ... 你的支付处理代码 ...
//
//            // 4. 支付成功后发送转化事件（带自动归因）
//            sendPaymentSuccessEvent(userId, orderId, email, amount, currency, paymentMethod);
//
//            log.info("Recharge process completed with attribution tracking - userId: {}, amount: {}",
//                userId, amount);
//
//        } catch (Exception e) {
//            log.error("Failed to process recharge with attribution tracking", e);
//            throw e;
//        }
//    }
//
//    /**
//     * 生成订单ID（示例方法）
//     */
//    private String generateOrderId() {
//        return "ORDER_" + System.currentTimeMillis();
//    }
//
//    /**
//     * Facebook Cookie数据内部类
//     */
//    private static class FacebookCookieData {
//        private String fbc;
//        private String fbp;
//        private String fbclid;
//
//        public String getFbc() { return fbc; }
//        public void setFbc(String fbc) { this.fbc = fbc; }
//
//        public String getFbp() { return fbp; }
//        public void setFbp(String fbp) { this.fbp = fbp; }
//
//        public String getFbclid() { return fbclid; }
//        public void setFbclid(String fbclid) { this.fbclid = fbclid; }
//    }
//}
