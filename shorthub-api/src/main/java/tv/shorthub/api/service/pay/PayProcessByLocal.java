package tv.shorthub.api.service.pay;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tv.shorthub.airwallex.service.AirwallexConfigStorageHolder;
import tv.shorthub.api.controller.req.CreateOrderRequest;
import tv.shorthub.api.service.recharge.RechargeService;
import tv.shorthub.api.utils.MessageCode;
import tv.shorthub.common.config.AppConfig;
import tv.shorthub.system.utils.AttributionCacheHolder;
import tv.shorthub.common.core.cache.CacheKeyUtils;
import tv.shorthub.common.core.cache.CacheService;
import tv.shorthub.api.session.UserSessionManager;
import tv.shorthub.common.enums.OrderChannelEnums;
import tv.shorthub.payermax.config.PayermaxConfig;
import tv.shorthub.paypal.config.PaypalConfigStorageHolder;
import tv.shorthub.system.domain.AppOrderInfo;
import tv.shorthub.system.domain.AppPromotion;
import tv.shorthub.system.domain.AppRechargeItem;
import tv.shorthub.system.domain.AppRechargeSubscriptionGift;
import tv.shorthub.system.service.IAppOrderInfoService;
import tv.shorthub.system.service.IAppRechargeSubscriptionGiftService;
import tv.shorthub.common.exception.ApiException;

import java.util.Date;

@Component
public class PayProcessByLocal {

    @Autowired
    private IAppOrderInfoService appOrderInfoService;

    @Autowired
    private IAppRechargeSubscriptionGiftService appRechargeSubscriptionGiftService;

    @Autowired
    private CacheService cacheService;

    @Autowired
    AttributionCacheHolder attributionCacheHolder;


    @Autowired
    public PayermaxConfig payermaxConfig;

    private void checkDuplicateSubscriptionOrder(String userId, String payMchId, String payType) {
        if ("2".equals(payType)) {
            Date now = new Date();
            Date twentyFourHoursAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);
            QueryWrapper<AppOrderInfo> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("user_id", userId)
                        .eq("pay_mch_id", payMchId)
                        .eq("pay_type", "2")
                        .eq("order_status", 1)
                        .ge("pay_time", twentyFourHoursAgo)
                        .orderByDesc("pay_time");
            AppOrderInfo existOrder = appOrderInfoService.getMapper().selectOne(queryWrapper);
            if (existOrder != null) {
                throw new ApiException(MessageCode.ORDER_DUPLICATE_SUBSCRIPTION.getCode());
            }
        }
    }

    public AppOrderInfo create(CreateOrderRequest request, AppRechargeItem appRechargeItem, String currency,AppRechargeItem appRetainItem) {

        AppPromotion appPromotion = cacheService.getCacheObject(CacheKeyUtils.getAppPromotion(UserSessionManager.getSession().getTfid()));


        String orderNo = IdUtil.getSnowflakeNextIdStr();
        AppOrderInfo orderInfo = new AppOrderInfo();
        orderInfo.setAppid(UserSessionManager.getSession().getAppid());
        orderInfo.setUserId(UserSessionManager.getUserId());
        orderInfo.setSid(UserSessionManager.getSession().getSessionId());
        orderInfo.setOrderNo(IdUtil.getSnowflakeNextIdStr());
        orderInfo.setOrderAmount(appRechargeItem.getPrice());
        orderInfo.setOrderAmountOrigin(appRechargeItem.getPrice());
        orderInfo.setOrderNumber(appRechargeItem.getNumber());
        orderInfo.setGiftOrderNumber(appRechargeItem.getBonusNumber());
        orderInfo.setTfid(UserSessionManager.getSession().getTfid());
        orderInfo.setPayType(appRechargeItem.getType().toString());
        switch (OrderChannelEnums.get(request.getPayMethod())) {
            case OrderChannelEnums.PAYERMAX -> orderInfo.setPayMchId(payermaxConfig.getMerchantNo());
            case OrderChannelEnums.GOOGLEPLAY -> orderInfo.setPayMchId(request.getPackageName());
            case OrderChannelEnums.AIRWALLEX -> {
                orderInfo.setPayMchId(AirwallexConfigStorageHolder.get());
            }
            case null, default -> orderInfo.setPayMchId(PaypalConfigStorageHolder.get());
        }
        // 重复支付校验
        checkDuplicateSubscriptionOrder(orderInfo.getUserId(), orderInfo.getPayMchId(), orderInfo.getPayType());
        orderInfo.setFeeItemId(appRechargeItem.getItemId());
        orderInfo.setOrderChannel(request.getPayMethod());
        orderInfo.setContentId(request.getContentId());
        orderInfo.setSeriesIndex(request.getNumber());
        orderInfo.setDeliverUsername(null != appPromotion ?  appPromotion.getCreateBy() : null);
        orderInfo.setOrderNo(orderNo);
        orderInfo.setOrderStatus(0L);
        orderInfo.setCurrency(currency);

        JSONObject extendJson = new JSONObject();
        extendJson.put("clientIp",  UserSessionManager.getSession().getIp());
        extendJson.put("deviceId",  UserSessionManager.getSession().getDeviceId());
        String accessToken = IdUtil.getSnowflakeNextIdStr();
        extendJson.put("accessTokenWithLogin", accessToken);
        extendJson.put("verifyWithLogin", AppConfig.getDomainApi() + "/api/auth/verify?accessToken=" + accessToken);
        extendJson.put("clientUserAgent",  UserSessionManager.getSession().getUserAgent());
        extendJson.put("sid", UserSessionManager.getSession().getSessionId());
        extendJson.put("country", UserSessionManager.getSession().getCountry());
        extendJson.put("language", UserSessionManager.getSession().getLanguage());
        // 获取归因数据
        JSONObject attribution = attributionCacheHolder.getAttribution(UserSessionManager.getSession().getDeviceId(), UserSessionManager.getSession().getIp(), UserSessionManager.getSession().getTfid());
        extendJson.put("attribution", attribution);
        orderInfo.setExtendJson(extendJson);

        // 获取订阅优惠配置
        AppRechargeSubscriptionGift giftConfig = getSubscriptionGift(appRechargeItem.getItemId());
        if (giftConfig != null) {
            // 重新设置订单价格
            orderInfo.setOrderAmount(giftConfig.getGiftPrice());
        }

        //挽留模版存在就替换
        if(appRetainItem != null) {
            orderInfo.setOrderAmount(appRetainItem.getPrice());//优惠模版金额
            orderInfo.setDiscountItemId(appRetainItem.getItemId());//优惠模版id
        }
        appOrderInfoService.insert(orderInfo);
        return orderInfo;
    }


    public AppRechargeSubscriptionGift getSubscriptionGift(String itemId) {
        QueryWrapper<AppRechargeSubscriptionGift> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("item_id", itemId);
        queryWrapper.eq("enable", true);
        return appRechargeSubscriptionGiftService.getMapper().selectOne(queryWrapper);
    }
}
