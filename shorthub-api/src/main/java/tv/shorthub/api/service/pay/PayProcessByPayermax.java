package tv.shorthub.api.service.pay;

import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tv.shorthub.common.config.AppConfig;
import tv.shorthub.common.utils.DateUtils;
import tv.shorthub.common.utils.StringUtils;
import tv.shorthub.payermax.config.PayermaxConfig;
import tv.shorthub.payermax.service.PayermaxOrderService;
import tv.shorthub.payermax.service.PayermaxSubscribeService;
import tv.shorthub.paypal.constant.PayPalConstants;
import tv.shorthub.system.domain.AppOrderInfo;
import tv.shorthub.system.domain.AppRechargeItem;
import tv.shorthub.system.domain.PayermaxPaymentLog;
import tv.shorthub.system.service.IPayermaxPaymentLogService;

import java.math.BigDecimal;
import java.util.Date;

@Component
@Slf4j
public class PayProcessByPayermax {

    @Autowired
    PayermaxConfig payermaxConfig;

    @Autowired
    PayermaxOrderService payermaxOrderService;

    @Autowired
    PayermaxSubscribeService payermaxSubscribeService;

    @Autowired
    IPayermaxPaymentLogService payermaxPaymentLogService;

    public JSONObject create(AppOrderInfo orderInfo, AppRechargeItem appRechargeItem, String currency, String language, String country, JSONObject payermax) throws Exception {
        JSONObject payment;
        String notifyUrl = payermax.containsKey("redirectUrl") ? payermax.getString("redirectUrl") : AppConfig.getDomainWeb();
        String frontCallbackUrl = AppConfig.getDomainApi() + "/api/payermax/success";
        if (payermax.containsKey("redirectUrl") && payermax.getString("redirectUrl").startsWith("https")) {
            frontCallbackUrl = payermax.getString("redirectUrl") + (payermax.getString("redirectUrl").contains("?") ? "&" : "?") + "pay=success";
        }
        String cancelUrl = null;
        String paymentMethod = PayPalConstants.PaymentMethod.SUBSCRIPTION;;
        String description = "Buy";
        JSONObject subscriptionPlan = null;
        String integrate = "Direct_Payment"; // 普通支付
        String userId = payermaxPaymentLogService.getPayermaxUserId(orderInfo.getUserId());


        JSONObject paymentDetail = null;
        if (payermax.containsKey("sessionKey")) {
            paymentDetail = new JSONObject();
        }

        String clientIp = orderInfo.getExtendJson().getString("clientIp");
        if (StringUtils.isNotEmpty(clientIp) && clientIp.length() > 30) {
            clientIp = "127.0.0.1";
        }


        if ("2".equals(appRechargeItem.getType().toString())) {
            String period = appRechargeItem.getSubscriptionPeriod();
            period = switch (period) {
                case "DAY" -> "D";
                case "WEEK" -> "W";
                case "MONTH" -> "M";
                case "YEAR" -> "Y";
                default -> period;
            };

            payment = payermaxSubscribeService.subscriptionCreate(
                    orderInfo.getOrderNo(),
                    userId,
                    language,
                    notifyUrl,
                    appRechargeItem.getSubscriptionPeriodNumber(),
                    period,
                    orderInfo.getOrderAmountOrigin(),
                    currency,
                    orderInfo.getOrderAmount()
            );
            subscriptionPlan = payment.getJSONObject("data").getJSONObject("subscriptionPlan");
            subscriptionPlan.remove("subscriptionStatus");
//            integrate = "Hosted_Checkout"; // 收银台支付

            if (null != paymentDetail) {
                // 前置组件支付参数 - 订阅支付
                paymentDetail.put("sessionKey", payermax.getString("sessionKey"));
                paymentDetail.put("paymentToken", payermax.getString("paymentToken"));
                paymentDetail.put("mitType", "SCHEDULED");
                paymentDetail.put("tokenForFutureUse", true);
                paymentDetail.put("merchantInitiated", false);
                JSONObject buyerInfo = new JSONObject();
                buyerInfo.put("clientIp", clientIp);
                buyerInfo.put("userAgent", "Chrome");
                paymentDetail.put("buyerInfo", buyerInfo);
            }

        } else if (null != paymentDetail){
            // 前置组件支付参数 - 普通支付
            paymentDetail.put("sessionKey", payermax.getString("sessionKey"));
            paymentDetail.put("paymentToken", payermax.getString("paymentToken"));
            JSONObject buyerInfo = new JSONObject();
            buyerInfo.put("clientIp", clientIp);
            buyerInfo.put("userAgent", "Chrome");
            paymentDetail.put("buyerInfo", buyerInfo);
            paymentMethod = PayPalConstants.PaymentMethod.RECHARGE;
        }

        payment = payermaxOrderService.orderAndPay(
                integrate,
                orderInfo.getOrderNo(),
                orderInfo.getOrderAmount(),
                description,
                userId,
                language,
                currency,
                frontCallbackUrl,
                notifyUrl,
                cancelUrl,
                subscriptionPlan,
                paymentDetail,
                country
        );
        JSONObject data = payment.getJSONObject("data");

        // 支付日志
        PayermaxPaymentLog paymentLog = new PayermaxPaymentLog();
        paymentLog.setMcnId(payermaxConfig.getMerchantNo());
        paymentLog.setUserId(userId);
        paymentLog.setTradeToken(data.getString("tradeToken"));
        paymentLog.setOrderNo(orderInfo.getOrderNo());
        paymentLog.setStatus(data.getString("status"));
        paymentLog.setTotalAmount(orderInfo.getOrderAmount());
        paymentLog.setCurrency(currency);
        paymentLog.setDescription(description);
        paymentLog.setReturnUrl(frontCallbackUrl);
        paymentLog.setCancelUrl(cancelUrl);
        paymentLog.setRedirectUrl(data.getString("redirectUrl"));
        paymentLog.setPaymentMethod(paymentMethod);
        paymentLog.setRawData(data);
        paymentLog.setRelatedUserId(orderInfo.getUserId());
        Date createTime = new Date();
        if (payment.containsKey("create_time")) {
            // 从rawData中获取创建时间
            createTime = DateUtils.parseDate(payment.getString("create_time"));
        }
        paymentLog.setCreateTime(createTime);
        payermaxPaymentLogService.insert(paymentLog);

        JSONObject response = new JSONObject();
        response.put("payment", data);
        response.put("orderNo", orderInfo.getOrderNo());
        return response;
    }
}
