package tv.shorthub.api.service;

import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tv.shorthub.api.service.recharge.RechargeService;
import tv.shorthub.common.enums.OrderChannelEnums;
import tv.shorthub.paypal.service.PayPalCallback;
import tv.shorthub.system.domain.AppOrderInfo;
import tv.shorthub.system.domain.AppRechargeItem;
import tv.shorthub.system.domain.PaypalPaymentLog;
import tv.shorthub.system.domain.PaypalUserPlan;
import tv.shorthub.system.mapper.PaypalUserPlanMapper;
import tv.shorthub.system.service.IAppOrderInfoService;
import tv.shorthub.system.service.IAppRechargeItemService;
import tv.shorthub.system.service.IPaypalPaymentLogService;

import java.util.Date;
import java.time.Instant;

@Slf4j
@Service
public class PayPalCallbackExecutePayment implements PayPalCallback {

    @Autowired
    private RechargeService rechargeService;

    @Autowired
    private IPaypalPaymentLogService paypalPaymentLogService;

    @Autowired
    private IAppOrderInfoService appOrderInfoService;

    @Autowired
    private IAppRechargeItemService appRechargeItemService;

    @Autowired
    PaypalUserPlanMapper paypalUserPlanMapper;

    @Override
    public void onSuccess(JSONObject data) {
        String orderId = data.getString("orderId");
        String payerId = data.getString("payerId");
        JSONObject paymentData = data.getJSONObject("paymentData");
        try {
            // 更新PayPal支付日志
            QueryWrapper<PaypalPaymentLog> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("payment_id", orderId);
            PaypalPaymentLog paymentLog = paypalPaymentLogService.getMapper().selectOne(queryWrapper);
            if (paymentLog != null) {
                paymentLog.setState(paymentData.getString("status"));
                paymentLog.setRawData(paymentData);
                paymentLog.setPayerId(payerId);
                paypalPaymentLogService.update(paymentLog);

                // 获取PayPal返回的支付时间
                Date payTime = null;
                JSONArray purchaseUnits = paymentData.getJSONArray("purchase_units");
                if (purchaseUnits != null && !purchaseUnits.isEmpty()) {
                    JSONObject unit = purchaseUnits.getJSONObject(0);
                    JSONObject payments = unit.getJSONObject("payments");
                    if (payments != null) {
                        JSONArray captures = payments.getJSONArray("captures");
                        if (captures != null && !captures.isEmpty()) {
                            JSONObject capture = captures.getJSONObject(0);
                            String createTimeStr = capture.getString("create_time");
                            payTime = Date.from(Instant.parse(createTimeStr));
                        }
                    }
                }
                // 如果没有获取到支付时间，则使用当前时间
                if (payTime == null) {
                    payTime = new Date();
                }
                AppOrderInfo orderInfo = appOrderInfoService.getMapper().selectOne(new QueryWrapper<AppOrderInfo>().eq("order_no", paymentLog.getOrderNo()));

                if (orderInfo.getPayType().equals("2")) {
                    saveSubscriptionPlan(paymentData, orderInfo, paymentLog, orderId);
                }

                // 更新订单状态
                rechargeService.success(paymentLog.getOrderNo(), payTime);
            } else {
                log.error("未找到支付日志: orderId={}", orderId);
            }
        } catch (Exception e) {
            log.error("处理支付成功回调异常: orderId={}", orderId, e);
        }
    }

    /**
     * 保存扣费计划
     * @param paymentData
     * @param orderInfo
     * @param paymentLog
     * @param orderId
     */
    private void saveSubscriptionPlan(JSONObject paymentData, AppOrderInfo orderInfo, PaypalPaymentLog paymentLog, String orderId) {
        JSONObject paymentSource = paymentData.getJSONObject("payment_source");
        if (null != paymentSource && paymentSource.containsKey(OrderChannelEnums.PAYPAL.getValue())) {
            JSONObject paypal = paymentSource.getJSONObject(OrderChannelEnums.PAYPAL.getValue());
            if (paypal.containsKey("attributes")) {
                JSONObject attributes = paypal.getJSONObject("attributes");
                if (attributes.containsKey("vault")) {

                    AppRechargeItem item = appRechargeItemService.getMapper().selectOne(new QueryWrapper<AppRechargeItem>().eq("item_id", orderInfo.getFeeItemId()));
                    PaypalUserPlan paypalUserPlan = new PaypalUserPlan();
                    paypalUserPlan.setOrderNo(paymentLog.getOrderNo());
                    paypalUserPlan.setPaymentId(paymentLog.getPaymentId());
                    paypalUserPlan.setVaultId(attributes.getJSONObject("vault").getString("id"));
                    paypalUserPlan.setExtendData(paymentData);
                    paypalUserPlan.setPeriod(item.getSubscriptionPeriod());
                    paypalUserPlan.setEnabled(true);
                    paypalUserPlan.setCreateTime(new Date());

                    paypalUserPlanMapper.insert(paypalUserPlan);

                    log.info("保存扣费计划: {}", orderId);
                }
            }
        }
    }

    @Override
    public void onFailed(JSONObject error) {
        log.error("处理支付失败回调: {}", error);
        String orderId = error.getString("orderId");
        String payerId = error.getString("payerId");
        String errorMsg = error.getString("error");
        try {
            // 更新PayPal支付日志
            QueryWrapper<PaypalPaymentLog> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("payment_id", orderId);
            PaypalPaymentLog paymentLog = paypalPaymentLogService.getMapper().selectOne(queryWrapper);
            if (paymentLog != null) {
                paymentLog.setState("FAILED");
                paymentLog.setPayerId(payerId);
                paymentLog.setRemark(errorMsg);
                paypalPaymentLogService.update(paymentLog);
            } else {
                log.error("未找到支付日志: orderId={}", orderId);
            }
        } catch (Exception e) {
            log.error("处理支付失败回调异常: orderId={}", orderId, e);
        }
    }
}
