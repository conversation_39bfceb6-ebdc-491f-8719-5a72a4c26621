package tv.shorthub.api.service.pay;

import com.alibaba.fastjson2.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tv.shorthub.common.config.AppConfig;
import tv.shorthub.api.session.UserSessionManager;
import tv.shorthub.common.utils.DateUtils;
import tv.shorthub.common.utils.StringUtils;
import tv.shorthub.paypal.config.PaypalConfigStorageHolder;
import tv.shorthub.paypal.constant.PayPalConstants;
import tv.shorthub.paypal.model.PaypalPaymentRequest;
import tv.shorthub.paypal.service.PayPalPaymentService;
import tv.shorthub.system.domain.AppOrderInfo;
import tv.shorthub.system.domain.AppRechargeItem;
import tv.shorthub.system.domain.PaypalPaymentLog;
import tv.shorthub.system.service.IPaypalPaymentLogService;

import java.util.Date;

@Component
public class PayProcessByPaypal {

    @Autowired
    private IPaypalPaymentLogService paypalPaymentLogService;

    @Autowired
    private PayPalPaymentService payPalPaymentService;

    public JSONObject create(AppOrderInfo orderInfo, AppRechargeItem appRechargeItem, String currency) throws Exception {
        JSONObject payment;
        String returnUrl;
        String cancelUrl;
        String paymentMethod;
        String description;

        // 判断是否为订阅类型
//        if ("2".equals(appRechargeItem.getType().toString())) {
//            // 创建订阅计划
//            PaypalSubscriptionRequest subscriptionRequest = new PaypalSubscriptionRequest();
//            subscriptionRequest.setPlanName("Plan");
//            subscriptionRequest.setDescription("Plan");
//            String language = orderInfo.getExtendJson().getString("language");
//            subscriptionRequest.setLocale(StringUtils.isNotEmpty(language) ? language.replaceFirst("_", "-") : null);
//            // 如果金额相等，就不存在优惠, orderAmount表示的是实际(优惠)支付金额
//            subscriptionRequest.setDiscountAmount(orderInfo.getOrderAmount().compareTo(appRechargeItem.getPrice()) == 0 ? null : orderInfo.getOrderAmount());
//            subscriptionRequest.setAmount(appRechargeItem.getPrice());
//            subscriptionRequest.setBillingCycle(appRechargeItem.getSubscriptionPeriod());
//            subscriptionRequest.setBillingCycles(appRechargeItem.getSubscriptionPeriodNumber().intValue());
//            subscriptionRequest.setReturnUrl(AppConfig.getDomainApi() + "/api/order/execute-subscription");
//            subscriptionRequest.setCancelUrl(AppConfig.getDomainApi() + "/api/order/cancel-subscription");
//            subscriptionRequest.setUserId(UserSessionManager.getUserId());
//
//            // 创建订阅计划
//            JSONObject plan = payPalSubscriptionService.createPlan(subscriptionRequest);
//            String planId = plan.getString("id");
//
//            // 创建订阅
//            payment = payPalSubscriptionService.createSubscription(planId, subscriptionRequest);
//
//            returnUrl = subscriptionRequest.getReturnUrl();
//            cancelUrl = subscriptionRequest.getCancelUrl();
//            paymentMethod = PayPalConstants.PaymentMethod.SUBSCRIPTION;
//            description = subscriptionRequest.getPlanName();
//
//        } else {
//            // 普通支付流程
//            PaypalPaymentRequest paypalPaymentRequest = new PaypalPaymentRequest();
//            paypalPaymentRequest.setAmount(appRechargeItem.getPrice());
//            paypalPaymentRequest.setPaymentMethod(PayPalConstants.PaymentMethod.RECHARGE);
//            paypalPaymentRequest.setItemName("Recharge");
//            paypalPaymentRequest.setReturnUrl(AppConfig.getDomainApi() + "/api/order/execute-payment");
//            paypalPaymentRequest.setCancelUrl(AppConfig.getDomainApi() + "/api/order/cancel-payment");
//            paypalPaymentRequest.setOrderNo(orderInfo.getOrderNo());
//
//            // 发起paypal支付
//            payment = payPalPaymentService.createPayment(paypalPaymentRequest);
//
//            returnUrl = paypalPaymentRequest.getReturnUrl();
//            cancelUrl = paypalPaymentRequest.getCancelUrl();
//            paymentMethod = paypalPaymentRequest.getPaymentMethod();
//            description = paypalPaymentRequest.getItemName();
//        }

        String language = orderInfo.getExtendJson().getString("language");

        // 普通支付流程
        PaypalPaymentRequest paypalPaymentRequest = new PaypalPaymentRequest();
        paypalPaymentRequest.setAmount(orderInfo.getOrderAmount());
        paypalPaymentRequest.setLocale(StringUtils.isNotEmpty(language) ? language.replaceFirst("_", "-") : "en-US");
        paypalPaymentRequest.setPaymentMethod(PayPalConstants.PaymentMethod.RECHARGE);
        paypalPaymentRequest.setItemName("Recharge");
        paypalPaymentRequest.setReturnUrl(AppConfig.getDomainApi() + "/api/order/execute-payment");
        paypalPaymentRequest.setCancelUrl(AppConfig.getDomainApi() + "/api/order/cancel-payment");
        paypalPaymentRequest.setOrderNo(orderInfo.getOrderNo());

        // 发起paypal支付
        payment = payPalPaymentService.createPayment(paypalPaymentRequest);

        returnUrl = paypalPaymentRequest.getReturnUrl();
        cancelUrl = paypalPaymentRequest.getCancelUrl();
        paymentMethod = paypalPaymentRequest.getPaymentMethod();
        description = paypalPaymentRequest.getItemName();

        // 记录PayPal支付日志
        PaypalPaymentLog paymentLog = new PaypalPaymentLog();
        paymentLog.setPayerId(payment.getString("payer_id"));
        paymentLog.setClientId(PaypalConfigStorageHolder.get());
        paymentLog.setPaymentId(payment.getString("id"));
        paymentLog.setOrderNo(orderInfo.getOrderNo());
        paymentLog.setState(payment.getString("status"));
        paymentLog.setTotalAmount(orderInfo.getOrderAmount());
        paymentLog.setCurrency(currency);
        paymentLog.setDescription(description);
        paymentLog.setReturnUrl(returnUrl);
        paymentLog.setCancelUrl(cancelUrl);
        paymentLog.setPaymentMethod(paymentMethod);
        paymentLog.setRawData(payment);
        paymentLog.setRelatedUserId(UserSessionManager.getUserId());
        Date createTime = new Date();
        if (payment.containsKey("create_time")) {
            // 从rawData中获取创建时间
            createTime = DateUtils.parseDate(payment.getString("create_time"));
        }
        paymentLog.setCreateTime(createTime);
        paypalPaymentLogService.insert(paymentLog);
        return payment;
    }
}
