package tv.shorthub.api.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tv.shorthub.system.domain.AppOrderInfo;
import tv.shorthub.system.domain.PaypalPaymentLog;
import tv.shorthub.system.service.IAppOrderInfoService;
import tv.shorthub.system.service.IPaypalPaymentLogService;


@Component
@Slf4j
public class Unsubscription {

    @Autowired
    IPaypalPaymentLogService paypalPaymentLogService;

    @Autowired
    IAppOrderInfoService appOrderInfoService;

    /**
     * 取消订阅
     * @param userId
     * @return
     */
    public boolean unsubscription(String userId) {
        // 1. 查询用户本地订阅订单
        AppOrderInfo orderInfo = appOrderInfoService.getMapper().selectOne(new QueryWrapper<AppOrderInfo>()
                .eq("user_id", userId)
                .eq("order_status", 1)
                .eq("pay_type", "2")
                .eq("unsubscription_status", 0)
                .last("limit 1")
        );
        if (null == orderInfo) {
            log.info("用户没有可取消的订阅. {}", userId);
            return true;
        }

        return appOrderInfoService.unsubscribe(orderInfo) > 0;
    }
}
