package tv.shorthub.api.service.auth;

import com.google.api.client.googleapis.auth.oauth2.GoogleIdToken;
import com.google.api.client.googleapis.auth.oauth2.GoogleIdTokenVerifier;
import com.google.api.client.googleapis.auth.oauth2.GoogleAuthorizationCodeTokenRequest;
import com.google.api.client.http.javanet.NetHttpTransport;
import com.google.api.client.json.gson.GsonFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import tv.shorthub.api.config.GoogleOAuthConfig;
import tv.shorthub.common.enums.SysEnv;
import tv.shorthub.common.utils.StringUtils;
import tv.shorthub.common.utils.spring.SpringUtils;

import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.Proxy;
import java.security.GeneralSecurityException;
import java.util.Collections;
import java.util.HashMap;
import java.util.Objects;

@Slf4j
@Service
public class GoogleAuthService {

    private final GoogleOAuthConfig config;
    private final NetHttpTransport transport;

    public GoogleAuthService(GoogleOAuthConfig config) {
        this.config = config;
        // 配置代理
        Proxy proxy = new Proxy(Proxy.Type.HTTP, new InetSocketAddress("127.0.0.1", 10809));
        this.transport = new NetHttpTransport.Builder()
                .setProxy(Objects.equals(SpringUtils.getActiveProfile(), SysEnv.LOCAL.getValue()) ? proxy : null)
                .build();
    }

    private GoogleIdTokenVerifier createVerifier(String platform, String packageName) {
        if (StringUtils.isNotEmpty(packageName)) {
            return new GoogleIdTokenVerifier.Builder(transport, GsonFactory.getDefaultInstance())
                    .setAudience(Collections.singletonList(config.getOAuthClientId(packageName)))
                    .setIssuer("https://accounts.google.com")
                    .build();
        }

        return new GoogleIdTokenVerifier.Builder(transport, GsonFactory.getDefaultInstance())
                .setAudience(Collections.singletonList(config.getClientId(platform)))
                .setIssuer("https://accounts.google.com")
                .build();
    }

    public GoogleIdToken.Payload verify(String idTokenString, String platform, String packageName) throws GeneralSecurityException, IOException {
        log.info("Attempting to verify Google ID token for platform: {}, packageName: {}...", platform, packageName);
        try {
            GoogleIdTokenVerifier verifier = createVerifier(platform, packageName);
            GoogleIdToken idToken = verifier.verify(idTokenString);
            if (idToken != null) {
                GoogleIdToken.Payload payload = idToken.getPayload();
                log.info("Token verification successful for email: {}", payload.getEmail());
                return payload;
            } else {
                log.error("Token verification failed: Token is null");
                throw new IllegalArgumentException("Invalid ID token: Token verification failed");
            }
        } catch (GeneralSecurityException e) {
            log.error("Security exception during token verification: {}", e.getMessage(), e);
            throw e;
        } catch (IOException e) {
            log.error("IO exception during token verification: {}", e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("Unexpected error during token verification: {}", e.getMessage(), e);
            throw new IllegalArgumentException("Invalid ID token: " + e.getMessage());
        }
    }

    public GoogleIdToken.Payload verifyAuthorizationCode(String code, String redirectUrl, String platform, String packageName) throws IOException {
        log.info("Attempting to exchange authorization code for tokens for platform: {}...", platform);
        try {
            GoogleAuthorizationCodeTokenRequest tokenRequest = new GoogleAuthorizationCodeTokenRequest(
                    transport,
                    GsonFactory.getDefaultInstance(),
                    config.getClientId(platform),
                    config.getClientSecret(platform),
                    code,
                    redirectUrl
            );

            String idTokenString = tokenRequest.execute().getIdToken();
            GoogleIdTokenVerifier verifier = createVerifier(platform, packageName);
            GoogleIdToken idToken = verifier.verify(idTokenString);

            if (idToken != null) {
                GoogleIdToken.Payload payload = idToken.getPayload();
                log.info("Authorization code exchange successful for email: {}", payload.getEmail());
                return payload;
            } else {
                log.error("Token verification failed after authorization code exchange");
                throw new IllegalArgumentException("Invalid ID token: Token verification failed");
            }
        } catch (Exception e) {
            log.error("Error during authorization code exchange: {}", e.getMessage(), e);
            throw new IOException("Failed to exchange authorization code: " + e.getMessage());
        }
    }

    public String getOAuthId(String packageName) {
        return config.getOauth().stream().filter(f -> f.get("key").equals(packageName)).findFirst().orElseGet(HashMap::new).get("id");
    }
}
