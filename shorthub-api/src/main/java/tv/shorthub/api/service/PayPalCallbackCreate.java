package tv.shorthub.api.service;

import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tv.shorthub.api.service.recharge.RechargeService;
import tv.shorthub.paypal.service.PayPalCallback;
import tv.shorthub.system.service.IPaypalPaymentLogService;

@Slf4j
@Service
public class PayPalCallbackCreate implements PayPalCallback {

    @Autowired
    private RechargeService rechargeService;

    @Autowired
    private IPaypalPaymentLogService paypalPaymentLogService;

    @Override
    public void onSuccess(JSONObject order) {

    }

    @Override
    public void onFailed(JSONObject error) {
    }
}
