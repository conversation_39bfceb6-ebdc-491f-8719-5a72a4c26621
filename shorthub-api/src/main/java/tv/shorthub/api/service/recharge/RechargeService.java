package tv.shorthub.api.service.recharge;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import tv.shorthub.ad.domain.RetargetingEventDataOrderCreate;
import tv.shorthub.ad.domain.RetargetingEventDataPayment;
import tv.shorthub.ad.domain.RetargetingEventDataSubscription;
import tv.shorthub.api.controller.req.CreateOrderRequest;
import tv.shorthub.api.service.BalanceOf;
import tv.shorthub.api.service.UserService;
import tv.shorthub.api.service.ad.RetargetingServiceAdaptor;
import tv.shorthub.api.service.pay.*;
import tv.shorthub.api.session.SessionUtils;
import tv.shorthub.api.utils.MessageCode;
import tv.shorthub.common.core.cache.CacheKeyUtils;
import tv.shorthub.common.core.cache.CacheService;
import tv.shorthub.api.session.UserSessionManager;
import tv.shorthub.common.dto.OrderInfo;
import tv.shorthub.common.dto.RechargeItemDTO;
import tv.shorthub.common.dto.UserInfo;
import tv.shorthub.common.enums.OrderChannelEnums;
import tv.shorthub.common.exception.ApiException;
import tv.shorthub.common.utils.DateUtils;
import tv.shorthub.system.domain.*;
import tv.shorthub.system.mapper.AppRechargeItemMapper;
import tv.shorthub.system.service.*;
import tv.shorthub.paypal.service.PayPalPaymentService;
import tv.shorthub.paypal.service.PayPalSubscriptionService;

import java.util.Date;
import java.util.List;

@Slf4j
@Component
public class RechargeService {

    @Autowired
    CacheService cacheService;

    @Autowired
    IAppUsersService appUsersService;

    @Autowired
    IAppOrderInfoService appOrderInfoService;

    @Autowired
    @Lazy
    UserService userService;

    @Autowired
    IAppUserLevelRuleService appUserLevelRuleService;

    @Autowired
    @Lazy
    PayPalPaymentService payPalPaymentService;

    @Autowired
    @Lazy
    PayPalSubscriptionService payPalSubscriptionService;

    @Autowired
    BalanceOf balanceOf;

    @Autowired
    AppRechargeItemMapper appRechargeItemMapper;

    @Autowired
    IAppUserMemberService appUserMemberService;

    @Autowired
    private IPaypalPaymentLogService paypalPaymentLogService;

    @Autowired
    private IAppRechargeSubscriptionGiftService appRechargeSubscriptionGiftService;

    @Autowired
    private RetargetingServiceAdaptor retargetingServiceAdaptor;

    @Autowired
    PayProcessByLocal localProcess;

    @Autowired
    PayProcessByPaypal paypalProcess;

    @Autowired
    PayProcessByPayermax payermaxProcess;

    @Autowired
    PayProcessByAirwallex airwallexProcess;

    @Autowired
    PayProcessByCrypto cryptoProcess;

    @Autowired
    SessionUtils sessionUtils;

    public List<RechargeItemDTO> getRechargeTemplateList(String templateId) {
        if (StringUtils.isEmpty(templateId)) {
            // 先获取推广链接中的充值模板配置
            if (StringUtils.isNotEmpty(UserSessionManager.getSession().getTfid())) {
                AppPromotion  appPromotion = cacheService.getCacheObject(CacheKeyUtils.getAppPromotion(UserSessionManager.getSession().getTfid()));
                if (null != appPromotion && StringUtils.isNotEmpty(appPromotion.getFeeTemplateId())) {
                    templateId = appPromotion.getFeeTemplateId();
                    log.info("获取推广链接中的充值模板: {}", templateId);
                }
            }
        }
        if (StringUtils.isEmpty(templateId)) {
            AppRecharge recharge = cacheService.getCacheObject(CacheKeyUtils.RECHARGE_TEMPLATE_SYSTEM);
            log.info("获取系统默认充值模板: {}", null != recharge);
            templateId = recharge.getTemplateId();
        }
        return cacheService.getCacheList(CacheKeyUtils.getRechargeTemplateItem(templateId, "0"));
    }

    public AppRechargeItem  getAppRechargeItem(String itemId) {
        return appRechargeItemMapper.selectOne(new QueryWrapper<AppRechargeItem>().eq("item_id", itemId));
    }

    public List<OrderInfo> getOrderList() {
        QueryWrapper<AppOrderInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", UserSessionManager.getUserId());
        queryWrapper.eq("order_status", 1);
        queryWrapper.orderByDesc("create_time");
        List<AppOrderInfo> appOrderInfos = appOrderInfoService.getMapper().selectList(queryWrapper);

        return appOrderInfos.stream().map(appOrderInfo -> {
            OrderInfo orderInfo = new OrderInfo();
            orderInfo.setOrderTime(DateUtils.parseDateToStr("yyyy-MM-dd HH:mm", appOrderInfo.getCreateTime()));
            orderInfo.setOrderType(appOrderInfo.getPayType());
            orderInfo.setAmount(appOrderInfo.getOrderAmount());
            orderInfo.setNumber(appOrderInfo.getOrderNumber().toString());
            orderInfo.setUnsubscription(appOrderInfo.getUnsubscriptionStatus());
            return orderInfo;
        }).toList();
    }

    @Transactional(rollbackFor = Exception.class)
    public JSONObject createOrder(CreateOrderRequest request) throws Exception {

        //挽留模版
        AppRechargeItem appRetainItem = getAppRechargeItem(request.getItemId());
        String currency = "USD";
        //充值模版
        AppRechargeItem appRechargeItem;
        //判断是否优惠模版
        if (StrUtil.isNotBlank(appRetainItem.getParentItemId())) {
            appRechargeItem = getAppRechargeItem(appRetainItem.getParentItemId());
        }else {
            appRechargeItem = appRetainItem;
            appRetainItem = null;
        }

        AppOrderInfo orderInfo = localProcess.create(request, appRechargeItem, currency,appRetainItem);

        JSONObject result;
        if (OrderChannelEnums.PAYERMAX.getValue().equalsIgnoreCase(request.getPayMethod())) {
            result = payermaxProcess.create(orderInfo, appRechargeItem, currency, UserSessionManager.getSession().getLanguage(), UserSessionManager.getSession().getCountry(), request.getPayermax());
        } else if (OrderChannelEnums.GOOGLEPLAY.getValue().equals(request.getPayMethod())) {
            result = new JSONObject();
            result.put("orderNo", orderInfo.getOrderNo());
        } else if (OrderChannelEnums.AIRWALLEX.getValue().equals(request.getPayMethod())) {
            result = airwallexProcess.create(orderInfo, appRechargeItem, currency, UserSessionManager.getSession().getLanguage(), UserSessionManager.getSession().getCountry(), request.getAirwallex());
            result.put("orderNo", orderInfo.getOrderNo());
        } else if (OrderChannelEnums.CRYPTO.getValue().equals(request.getPayMethod())) {
            result = cryptoProcess.create(orderInfo, appRechargeItem, currency, UserSessionManager.getSession().getLanguage(), UserSessionManager.getSession().getCountry(), request.getCrypto());
        } else {
            result = paypalProcess.create(orderInfo, appRechargeItem, currency);
            result.put("orderNo", orderInfo.getOrderNo());
        }

        // 触发订单创建回传事件
        retargetingServiceAdaptor.processEventOrder(
                RetargetingEventDataOrderCreate.builder()
                        .userId(orderInfo.getUserId())
                        .orderId(orderInfo.getOrderNo())
                        .amount(orderInfo.getOrderAmount())
                        .relationId(orderInfo.getOrderNo())
                        .currency( orderInfo.getCurrency())
                        .eventTime(orderInfo.getCreateTime().getTime() / 1000)
                        .tfid(UserSessionManager.getSession().getTfid())
                        .sid(orderInfo.getSid())
                        .userAttribution(orderInfo.getExtendJson().containsKey("attribution") ? orderInfo.getExtendJson().getJSONObject("attribution") : null)
                        .deviceId(UserSessionManager.getSession().getDeviceId())
                        .clientIp( UserSessionManager.getSession().getIp())
                        .clientUserAgent( UserSessionManager.getSession().getUserAgent())
                        .build(), orderInfo.getOrderNo()
        );

        return result;
    }


    public String query(String orderNo) {
        // 获取订单信息
        QueryWrapper<AppOrderInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("order_no", orderNo);
        queryWrapper.eq("order_status", 1);
        AppOrderInfo orderInfo = appOrderInfoService.getMapper().selectOne(queryWrapper);
        if (null != orderInfo) {
            return orderInfo.getExtendJson().getString("verifyWithLogin");
        }
        return null;
    }

    @Transactional(rollbackFor = Exception.class)
    public void success(String orderNo, Date payTime) {
        // 获取订单信息
        QueryWrapper<AppOrderInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("order_no", orderNo);
        queryWrapper.eq("order_status", 0);
        AppOrderInfo orderInfo = appOrderInfoService.getMapper().selectOne(queryWrapper);

        if (null == orderInfo) {
            log.info("不存在待处理订单. {}", orderNo);
            throw new ApiException(MessageCode.ORDER_INVALID.getCode());
        }

        UpdateWrapper<AppOrderInfo> updateWrapper = new UpdateWrapper<AppOrderInfo>()
                .eq("order_no", orderNo)
                .set("order_status", 1)
                .set("pay_time", payTime)
                ;
        appOrderInfoService.getMapper().update(updateWrapper);
        if (orderInfo.getUserId().startsWith("sid-")) {
            // 临时用户的订单
            log.info("临时用户的订单 {}, userId={}", orderNo, orderInfo.getUserId());
            // 自动注册
            UserInfo userInfo = userService.registry(
                    orderInfo.getUserId(), "temp", "temp",
                    orderInfo.getExtendJson().getString("sid"),
                    orderInfo.getTfid(),
                    orderInfo.getExtendJson().getString("country"),
                    orderInfo.getExtendJson().getString("language"),
                    orderInfo.getExtendJson().getString("clientIp"),
                    orderInfo.getExtendJson().getString("clientUserAgent"),
                    orderInfo.getExtendJson().getString("deviceId")

            );
            // 创建订单时使用的是临时用户id，这里自动注册后使用的真实userId，所以要修改订单中的用户id
            orderInfo.setUserId(userInfo.getUserId());
            UpdateWrapper<AppOrderInfo> updateUserIdWrapper = new UpdateWrapper<AppOrderInfo>()
                    .eq("order_no", orderNo)
                    .set("user_id", orderInfo.getUserId())
                    ;
            appOrderInfoService.getMapper().update(updateUserIdWrapper);

            // 直接设置用户Redis缓存，更新userId
            sessionUtils.activateUserId(orderInfo.getExtendJson().getString("sid"), orderInfo.getUserId());

            // 绑定设备id和userId的关系
            userService.bindDeviceCache(orderInfo.getExtendJson().getString("deviceId"), userInfo.getUserId());

        }

        doSuccess(orderInfo);
    }

    public void doSuccess(AppOrderInfo orderInfo) {
        AppRechargeItem appRechargeItem = getAppRechargeItem(orderInfo.getFeeItemId());

        AppUsers appUsers =  appUsersService.getMapper().selectOne(new QueryWrapper<AppUsers>().eq("user_id", orderInfo.getUserId()));

        // 根据充值类型更新用户余额或会员有效期
        if ("1".equals(orderInfo.getPayType())) {
            log.info("金币充值参数: userId={}, number={}, gift={}", orderInfo.getUserId(), orderInfo.getOrderNumber(), orderInfo.getGiftOrderNumber());
            // 更新金币余额
            balanceOf.safeUpdateBalanceCoin(orderInfo.getUserId(), orderInfo.getOrderNumber());
            if (orderInfo.getGiftOrderNumber() != null && orderInfo.getGiftOrderNumber().compareTo(0L) > 0) {
                // 更新赠送金币
                balanceOf.safeUpdateBalanceCoin(orderInfo.getUserId(), orderInfo.getGiftOrderNumber());
            }
            log.info("充值金币成功. {}", orderInfo.getUserId());
        } else if ("0".equals(orderInfo.getPayType()) || "2".equals(orderInfo.getPayType())) {
            int memberMinutes = appRechargeItem.getNumber().intValue() + appRechargeItem.getBonusNumber().intValue();
            DateTime expireTime = DateUtil.offsetMinute(new Date(), memberMinutes);
            String memberId = IdUtil.getSnowflakeNextIdStr();

            log.info("更新参数: userId={}, memberId={}, level={}, expireTime={}", orderInfo.getUserId(), memberId, appRechargeItem.getMemberLevel(), memberMinutes);

            // 更新会员有效期
            balanceOf.safeUpdateMember(orderInfo.getUserId(), memberId, appRechargeItem.getMemberLevel(), (long) memberMinutes);
            if (orderInfo.getGiftOrderNumber() != null && orderInfo.getGiftOrderNumber() > 0) {
                // 更新赠送会员时长
                balanceOf.safeUpdateMember(orderInfo.getUserId(), memberId, appRechargeItem.getMemberLevel(), (long) memberMinutes);
            }

            // 添加会员记录
            // 获取会员配置
            List<AppUserLevelRule> rules = appUserLevelRuleService.getMapper().selectList(new QueryWrapper<AppUserLevelRule>().eq("level", appRechargeItem.getMemberLevel()));
            AppUserMember entity = new AppUserMember();
            entity.setMemberId(memberId);
            entity.setMemberLevel(appRechargeItem.getMemberLevel());
            entity.setUserId(orderInfo.getUserId());
            Long unlockContentAll = rules.stream().anyMatch(f -> f.getRuleCode().equals("UNLOCK_CONTENT_ALL")) ? -1L : 0;
            for (AppUserLevelRule rule : rules) {
                if (rule.getRuleCode().equals("UNLOCK_CONTENT_ALL")) {
                    unlockContentAll = -1L;
                    break;
                }
                if (rule.getRuleCode().equals("UNLOCK_CONTENT_NUMBER")) {
                    unlockContentAll = rule.getRuleParams().getLong("number");
                }
            }
            entity.setUnlockNumber(unlockContentAll);
            JSONObject levelConfig = new JSONObject();
            levelConfig.put("rule", JSONObject.toJSONString(rules));
            entity.setLevelConfig(levelConfig);
            entity.setBeginTime(new Date());
            entity.setExpireTime(expireTime);
            entity.setCreateTime(new Date());
            appUserMemberService.getMapper().insert(entity);


            // 触发购物订阅事件
            retargetingServiceAdaptor.processEventOrder(
                    RetargetingEventDataSubscription.builder()
                            .userId( orderInfo.getUserId())
                            .orderId(orderInfo.getOrderNo())
                            .email( appUsers.getEmail())
                            .amount( orderInfo.getOrderAmount())
                            .currency( orderInfo.getCurrency())
                            .paymentMethod( orderInfo.getPayType())
                            .relationId(orderInfo.getOrderNo())
                            .eventTime(orderInfo.getCreateTime().getTime() / 1000)
                            .tfid(orderInfo.getTfid())
                            .sid(orderInfo.getSid())
                            .userAttribution(orderInfo.getExtendJson().containsKey("attribution") ? orderInfo.getExtendJson().getJSONObject("attribution") : null)
                            .deviceId(null != orderInfo.getExtendJson() ? orderInfo.getExtendJson().getString("deviceId") : null)
                            .clientIp( null != orderInfo.getExtendJson() ? orderInfo.getExtendJson().getString("clientIp") : null)
                            .clientUserAgent( null != orderInfo.getExtendJson() ? orderInfo.getExtendJson().getString("clientUserAgent") : null)
                            .build(), orderInfo.getOrderNo()

            );


            log.info("充值会员完成. {}, level={}", orderInfo.getUserId(), appRechargeItem.getMemberLevel());
        } else {
            log.info("充值类型错误. {}", orderInfo.getPayType());
        }


        // 激活访问令牌
        userService.activateAccessToken(orderInfo);

        // 触发购物回传事件
        retargetingServiceAdaptor.processEventOrder(
                RetargetingEventDataPayment.builder()
                        .userId( orderInfo.getUserId())
                        .orderId(orderInfo.getOrderNo())
                        .email( appUsers.getEmail())
                        .amount( orderInfo.getOrderAmount())
                        .currency( orderInfo.getCurrency())
                        .paymentMethod( orderInfo.getPayType())
                        .relationId(orderInfo.getOrderNo())
                        .eventTime(orderInfo.getCreateTime().getTime() / 1000)
                        .tfid(orderInfo.getTfid())
                        .sid(orderInfo.getSid())
                        .userAttribution(orderInfo.getExtendJson().containsKey("attribution") ? orderInfo.getExtendJson().getJSONObject("attribution") : null)
                        .deviceId(null != orderInfo.getExtendJson() ? orderInfo.getExtendJson().getString("deviceId") : null)
                        .clientIp( null != orderInfo.getExtendJson() ? orderInfo.getExtendJson().getString("clientIp") : null)
                        .clientUserAgent( null != orderInfo.getExtendJson() ? orderInfo.getExtendJson().getString("clientUserAgent") : null)
                        .build(), orderInfo.getOrderNo()

        );

    }

    public static void main(String[] args) {
        DateTime expireTime = DateUtil.offsetMonth(new Date(), 1);
        System.out.println(DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", expireTime));
    }

}
