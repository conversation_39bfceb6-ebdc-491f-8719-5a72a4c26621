package tv.shorthub.api.service.paypal.webhook;

/**
 * PayPal Webhook 服务接口
 * 负责处理来自 PayPal 的 Webhook 事件通知
 * 
 * 主要功能：
 * 1. 接收并验证 PayPal Webhook 事件
 * 2. 根据事件类型分发到对应的处理器
 * 3. 提供重试机制确保事件处理的可靠性
 * 
 * 事件处理流程：
 * 1. 接收原始事件数据
 * 2. 解析事件类型和内容
 * 3. 查找对应的处理器
 * 4. 执行事件处理（支持重试）
 * 5. 记录处理结果
 */
public interface PayPalWebhookService {
    /**
     * 处理 PayPal webhook 事件
     * 
     * @param payload webhook 事件数据（JSON 格式）
     * @param transmissionId PayPal 传输 ID，用于追踪和去重
     * @param transmissionTime 事件传输时间
     * @param certUrl PayPal 证书 URL，用于验证事件真实性
     * @param transmissionSig 传输签名，用于验证事件完整性
     * 
     * @throws IllegalArgumentException 当必要参数为空或格式无效时
     * @throws RuntimeException 当事件处理失败且重试耗尽时
     */
    void processWebhook(String payload, String transmissionId, String transmissionTime,
                       String certUrl, String transmissionSig);
}
