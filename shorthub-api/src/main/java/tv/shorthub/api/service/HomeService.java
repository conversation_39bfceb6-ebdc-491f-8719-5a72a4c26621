package tv.shorthub.api.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tv.shorthub.api.controller.res.HomeConfigResponse;
import tv.shorthub.api.service.cache.CacheHolder;
import tv.shorthub.common.dto.BannerConfig;
import tv.shorthub.system.domain.SysConfig;
import tv.shorthub.system.service.ISysConfigService;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.util.ArrayList;
import java.util.List;

@Component
public class HomeService {

    @Autowired
    CacheHolder cacheHolder;


    public HomeConfigResponse getConfig() {
        HomeConfigResponse response = new HomeConfigResponse();
        response.setBanners(cacheHolder.getBanners());
        response.setDramas(cacheHolder.getDramas());
        return response;
    }

}
