package tv.shorthub.api.service.auth;

import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import tv.shorthub.api.config.FacebookOAuthConfig;
import tv.shorthub.common.enums.SysEnv;
import tv.shorthub.common.utils.spring.SpringUtils;

import java.net.InetSocketAddress;
import java.net.Proxy;
import java.util.Objects;

@Slf4j
@Service
public class FacebookAuthService {

    private final FacebookOAuthConfig config;
    private final RestTemplate restTemplate;

    public FacebookAuthService(FacebookOAuthConfig config) {
        this.config = config;

        // Configure proxy for development environment
        Proxy proxy = new Proxy(Proxy.Type.HTTP, new InetSocketAddress("127.0.0.1", 10809));
        SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
        if (Objects.equals(SpringUtils.getActiveProfile(), SysEnv.LOCAL.getValue())) {
            factory.setProxy(proxy);
        }
        this.restTemplate = new RestTemplate(factory);

        log.info("Initialized FacebookAuthService with appId: {}", config.getAppId());
    }

    public JSONObject verifyAccessToken(String accessToken) {
        log.info("Attempting to verify Facebook access token...");
        try {
            // 验证token
            String debugTokenUrl = String.format(
                "https://graph.facebook.com/v18.0/debug_token?input_token=%s&access_token=%s",
                accessToken,
                config.getAppId() + "|" + config.getAppSecret()
            );
            JSONObject debugResponse = restTemplate.getForObject(debugTokenUrl, JSONObject.class);

            if (debugResponse == null || !debugResponse.getJSONObject("data").getBoolean("is_valid")) {
                throw new IllegalArgumentException("Invalid access token");
            }

            // 获取用户信息
            String userInfoUrl = String.format(
                "https://graph.facebook.com/v18.0/me?fields=id,name,email&access_token=%s",
                accessToken
            );
            JSONObject userData = restTemplate.getForObject(userInfoUrl, JSONObject.class);

            if (userData != null && userData.containsKey("id")) {
                log.info("Token verification successful for user: {}", userData.getString("name"));
                return userData;
            } else {
                log.error("Token verification failed: Invalid response");
                throw new IllegalArgumentException("Invalid access token: Token verification failed");
            }
        } catch (Exception e) {
            log.error("Error during token verification: {}", e.getMessage(), e);
            throw new IllegalArgumentException("Invalid access token: " + e.getMessage());
        }
    }
}
