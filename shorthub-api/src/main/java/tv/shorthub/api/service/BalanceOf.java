package tv.shorthub.api.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tv.shorthub.api.utils.MessageCode;
import tv.shorthub.common.exception.ApiException;
import tv.shorthub.system.service.IAppUsersService;

import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.Date;

@Slf4j
@Component
public class BalanceOf {

    @Autowired
    IAppUsersService appUsersService;

    /**
     * 获取Asia/Shanghai时区的当前时间，确保与MySQL的+08:00时区保持一致
     * @return Date 上海时区的当前时间
     */
    private Date getCurrentTimeInShanghai() {
        ZonedDateTime nowInShanghai = ZonedDateTime.now(ZoneId.of("Asia/Shanghai"));
        return Date.from(nowInShanghai.toInstant());
    }

    /**
     * 更新会员有效期安全方法
     * @param userId
     * @param value 过期时间
     * @return
     */
    public boolean safeUpdateMemberByAdmin(String userId, String memberId, String memberLevel, Long value) {
        if (null == value || value.compareTo(0L) == 0) {
            log.info("更新会员有效期的值为0, userId[{}], value[{}]", userId, value);
            return true;
        }
        synchronized ((userId+"safeUpdateMember").intern()) {
            Date currentTime = getCurrentTimeInShanghai();
            if (!(appUsersService.updateMemberByAdmin(userId, memberId, memberLevel, value, currentTime) > 0)) {
                throw new ApiException(MessageCode.UPDATE_MEMBER_ERROR.getCode());
            }
            return true;
        }
    }

    /**
     * 更新会员有效期安全方法
     * @param userId
     * @param value 过期时间
     * @return
     */
    public boolean safeUpdateMember(String userId, String memberId, String memberLevel, Long value) {
        if (null == value || value.compareTo(0L) == 0) {
            log.info("更新会员有效期的值为0, userId[{}], value[{}]", userId, value);
            return true;
        }
        synchronized ((userId+"safeUpdateMember").intern()) {
            Date currentTime = getCurrentTimeInShanghai();
            if (!(appUsersService.updateMember(userId, memberId, memberLevel, value, currentTime) > 0)) {
                throw new ApiException(MessageCode.UPDATE_MEMBER_ERROR.getCode());
            }
            return true;
        }
    }

    /**
     * 更新充值金币安全方法
     * @param userId
     * @param value
     * @return
     */
    public boolean safeUpdateBalanceCoinByAdmin(String userId, Long value) {
        if (null == value || value.compareTo(0L) == 0) {
            log.info("更新余额的值为0, userId[{}], value[{}]", userId, value);
            return true;
        }
        synchronized ((userId+"updateBalance").intern()) {
            if (!(appUsersService.updateBalanceCoinByAdmin(userId, value) > 0)) {
                throw new ApiException(MessageCode.INSUFFICIENT_BALANCE.getCode());
            }
            return true;
        }
    }

    /**
     * 更新充值金币安全方法
     * @param userId
     * @param value
     * @return
     */
    public boolean safeUpdateBalanceCoin(String userId, Long value) {
        if (null == value || value.compareTo(0L) == 0) {
            log.info("更新余额的值为0, userId[{}], value[{}]", userId, value);
            return true;
        }
        synchronized ((userId+"updateBalanceCoin").intern()) {
            if (!(appUsersService.updateBalanceCoin(userId, value) > 0)) {
                throw new ApiException(MessageCode.INSUFFICIENT_BALANCE.getCode());
            }
            return true;
        }
    }


    /**
     * 更新赠送金币安全方法
     * @param userId
     * @param value
     * @return
     */
    public boolean safeUpdateBalanceBonusByAdmin(String userId, Long value) {
        if (null == value || value.compareTo(0L) == 0) {
            log.info("更新余额的值为0, userId[{}], value[{}]", userId, value);
            return true;
        }
        synchronized ((userId+"updateBalance").intern()) {
            if (!(appUsersService.updateBalanceBonusByAdmin(userId, value) > 0)) {
                throw new ApiException(MessageCode.INSUFFICIENT_BALANCE.getCode());
            }
            return true;
        }
    }

    /**
     * 更新赠送金币安全方法
     * @param userId
     * @param value
     * @return
     */
    public boolean safeUpdateBalanceBonus(String userId, Long value) {
        if (null == value || value.compareTo(0L) == 0) {
            log.info("更新余额的值为0, userId[{}], value[{}]", userId, value);
            return true;
        }
        synchronized ((userId+"updateBalanceCoin").intern()) {
            if (!(appUsersService.updateBalanceBonus(userId, value) > 0)) {
                throw new ApiException(MessageCode.INSUFFICIENT_BALANCE.getCode());
            }
            return true;
        }
    }
}
