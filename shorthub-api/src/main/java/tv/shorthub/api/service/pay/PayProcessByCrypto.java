package tv.shorthub.api.service.pay;


import com.alibaba.fastjson2.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tv.shorthub.crypto.dto.CryptoDepositRequest;
import tv.shorthub.crypto.dto.CryptoDepositResponse;
import tv.shorthub.crypto.service.impl.CryptoPaymentService;
import tv.shorthub.system.domain.AppOrderInfo;
import tv.shorthub.system.domain.AppRechargeItem;

@Component
public class PayProcessByCrypto {


    @Autowired
    CryptoPaymentService cryptoPaymentService;

    public JSONObject create(AppOrderInfo orderInfo, AppRechargeItem appRechargeItem, String currency, String language, String country, JSONObject crypto) throws Exception {
        // TODO 不支持用户订阅

        CryptoDepositRequest request = new CryptoDepositRequest();
        request.setBlockchain(crypto.getString("blockchain"));
        request.setTokenType(crypto.getString("tokenType"));
        request.setAmount(orderInfo.getOrderAmount());
        request.setUserId(orderInfo.getUserId());
        request.setOrderNo(orderInfo.getOrderNo());
        CryptoDepositResponse depositOrder = cryptoPaymentService.createDepositOrder(request);
        return JSONObject.parseObject(JSONObject.toJSONString(depositOrder));
    }
}
