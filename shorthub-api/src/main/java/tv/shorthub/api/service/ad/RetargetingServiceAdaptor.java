package tv.shorthub.api.service.ad;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;
import tv.shorthub.ad.domain.FacebookAttributionData;
import tv.shorthub.ad.domain.RetargetingEventData;
import tv.shorthub.ad.rule.FrequencyRule;
import tv.shorthub.ad.rule.RetargetingRuleFactory;
import tv.shorthub.ad.service.FacebookAttributionService;
import tv.shorthub.ad.service.impl.RetargetingServiceImpl;
import tv.shorthub.api.service.cache.CacheHolder;
import tv.shorthub.common.enums.AdPlatformEnum;
import tv.shorthub.system.domain.AppOrderInfo;
import tv.shorthub.system.domain.AppPromotion;
import tv.shorthub.system.service.IAppOrderInfoService;

@Primary
@Component
@Slf4j
public class RetargetingServiceAdaptor extends RetargetingServiceImpl {

    @Autowired
    FacebookAttributionService facebookAttributionService;

    @Autowired
    IAppOrderInfoService appOrderInfoService;

    @Autowired
    CacheHolder cacheHolder;

    public RetargetingServiceAdaptor(RetargetingRuleFactory ruleFactory, FrequencyRule frequencyRule) {
        super(ruleFactory, frequencyRule);
    }


    public void processEventOrder(RetargetingEventData eventData, String orderNo) {
        if (StringUtils.isEmpty(eventData.getTfid())) {
            try {
                reattributionOrder(eventData, orderNo);
            } catch (Exception exception) {
                log.info("订单归因失败: {}", orderNo, exception);
            }
        }
        super.processEvent(eventData);
    }

    private void reattributionOrder(RetargetingEventData eventData, String orderNo) {
        // 为没有推广链接的订单设置归因数据
        AppPromotion promotion = cacheHolder.getAppPromotion(eventData.getTfid());
        if (null != promotion && promotion.getAdChannel().equals(AdPlatformEnum.Facebook.getValue())) {
            reattributionOrderFacebook(eventData, orderNo, promotion);
        }
    }

    private void reattributionOrderFacebook(RetargetingEventData eventData, String orderNo, AppPromotion promotion) {
        FacebookAttributionData attributionData = facebookAttributionService.findUserAttribution(eventData.getSid(), eventData.getClientIp(), eventData.getDeviceId(), eventData.getTfid());
        // 获取到归因信息
        if (null != attributionData) {
            if (null == eventData.getUserAttribution()) {
                eventData.setUserAttribution(new JSONObject());
            }
            AppOrderInfo orderInfo = appOrderInfoService.getMapper().selectOne(new QueryWrapper<AppOrderInfo>().eq("order_no", orderNo));
            // 更新订单归因数据
            orderInfo.setAppid(promotion.getAppid());
            orderInfo.setTfid(attributionData.getTfid());
            orderInfo.setDeliverUsername(promotion.getCreateBy());
            JSONObject attribution = new JSONObject();
            attribution.put(promotion.getAdChannel(), JSONObject.parseObject(JSONObject.toJSONString(attributionData)));
            orderInfo.getExtendJson().put("attribution", attribution);
            log.info("订单归因成功: {}, {}", orderInfo.getOrderNo(), orderInfo.getExtendJson());
            appOrderInfoService.update(orderInfo);

            // 给事件设置当前归因成功的参数
            eventData.getUserAttribution().put("attribution", attribution);
            eventData.setTfid(attributionData.getTfid());
        }
    }

}
