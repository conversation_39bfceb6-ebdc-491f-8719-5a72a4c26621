package tv.shorthub.api.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import tv.shorthub.ad.domain.RetargetingEventDataView;
import tv.shorthub.api.controller.req.BuySerialRequest;
import tv.shorthub.api.schedule.SchedulePushAppUserWatchRecords;
import tv.shorthub.api.service.ad.RetargetingServiceAdaptor;
import tv.shorthub.api.utils.MessageCode;
import tv.shorthub.common.dto.ConsumptionHistory;
import tv.shorthub.common.dto.ContentInfo;
import tv.shorthub.common.exception.ApiException;
import tv.shorthub.api.service.cache.CacheHolder;
import tv.shorthub.api.session.UserSessionManager;
import tv.shorthub.common.utils.StringUtils;
import tv.shorthub.common.utils.ip.IpUtils;
import tv.shorthub.system.domain.*;
import tv.shorthub.system.service.IAppConsumptionService;
import tv.shorthub.system.service.IAppConsumptionUnlockContentService;

import java.util.Date;
import java.util.List;

import lombok.extern.slf4j.Slf4j;
import tv.shorthub.system.service.IAppUserMemberService;

@Slf4j
@Component
public class ConsumptionService {

    @Autowired
    IAppConsumptionService appConsumptionService;

    @Autowired
    IAppConsumptionUnlockContentService appConsumptionUnlockContentService;

    @Autowired
    SchedulePushAppUserWatchRecords schedulePushAppUserWatchRecords;

    @Autowired
    RetargetingServiceAdaptor retargetingServiceAdaptor;

    @Autowired
    IAppUserMemberService appUserMemberService;

    @Autowired
    CacheHolder cacheHolder;

    @Autowired
    BalanceOf balanceOf;

    public List<ConsumptionHistory> getUserHistory(String userId, String contentId, Integer page, Integer pageSize) {
        QueryWrapper<AppConsumption> queryWrapper = new QueryWrapper<AppConsumption>()
                .eq("user_id", userId)
                .orderByDesc("create_time")
                .last(" LIMIT " + (page - 1) * pageSize + ", " + pageSize)
                ;
        if (StringUtils.isNotEmpty(contentId)) {
            queryWrapper.eq("content_id", contentId);
        }
        List<AppConsumption> appConsumptions = appConsumptionService.getMapper().selectList(queryWrapper);
        return appConsumptions.stream().map(entry -> {
            ConsumptionHistory consumptionHistory = new ConsumptionHistory();
            consumptionHistory.setContentId(entry.getContentId());
            consumptionHistory.setNumber(entry.getSerialNumber());
            consumptionHistory.setTitle(cacheHolder.getContent(entry.getContentId()).getTitle());
            return consumptionHistory;
        }).toList();
    }


    @Transactional(rollbackFor = Exception.class)
    public String buy(BuySerialRequest request) {
        // 获取剧集价格, 注意这里取负数(-)
        long serialPrice = -getSerialPrice(request.getContentId(), request.getSerialNumber());
        if (0  == serialPrice) {
            // 返回播放链接
            return cacheHolder.getSerialUrl(request.getContentId(), request.getSerialNumber());
        }

        // 会员扣减
        if(UserSessionManager.getSession().getMemberExpireTime() != null && UserSessionManager.getSession().getMemberExpireTime().before(new Date())) {
            log.info("用户[{}], 会员解锁", UserSessionManager.getUserId());
            return cacheHolder.getSerialUrl(request.getContentId(), request.getSerialNumber());
        }

        // 查询是否已经购买
        QueryWrapper<AppConsumption> queryWrapper = new QueryWrapper<AppConsumption>()
                .eq("user_id", UserSessionManager.getUserId())
                .eq("content_id", request.getContentId())
                .eq("serial_number", request.getSerialNumber())
                ;
        boolean buy = appConsumptionService.getMapper().selectCount(queryWrapper) > 0;
        if (buy) {
            // 返回播放链接
            return cacheHolder.getSerialUrl(request.getContentId(), request.getSerialNumber());
        }

        // 金币扣减
        if (coinConsumption(request, serialPrice)) {
            return cacheHolder.getSerialUrl(request.getContentId(), request.getSerialNumber());
        }
        return null;
    }

    public boolean coinConsumption(BuySerialRequest request, Long serialPrice) {

        // 金币扣减
        log.info("用户[{}], 金币消费:{}", UserSessionManager.getUserId(), serialPrice);
        balanceOf.safeUpdateBalanceCoin(UserSessionManager.getUserId(), serialPrice);

        // 记录消费明细
        AppConsumption appConsumption = new AppConsumption();
        appConsumption.setUserId(UserSessionManager.getUserId());
        appConsumption.setContentId(request.getContentId());
        appConsumption.setSerialNumber(request.getSerialNumber());
        appConsumption.setAmount(serialPrice);
        appConsumption.setCreateBy(IpUtils.getHostIp());
        appConsumption.setCreateTime(new Date());
        appConsumptionService.insert(appConsumption);
        return true;
    }

    public boolean memberConsumption(BuySerialRequest request, Long serialPrice) {

        // 查询是否已经解锁剧目
        QueryWrapper<AppConsumptionUnlockContent> unlockQueryWrapper = new QueryWrapper<AppConsumptionUnlockContent>()
                .eq("member_id", UserSessionManager.getSession().getMemberId())
                .eq("content_id", request.getContentId())
                ;
        Long selectCount = appConsumptionUnlockContentService.getMapper().selectCount(unlockQueryWrapper);
        boolean unlock = selectCount > 0;
        if (unlock) {
            return true;
        }

        // 查询可解锁
        QueryWrapper<AppConsumptionUnlockContent> unlockQueryCountWrapper = new QueryWrapper<AppConsumptionUnlockContent>()
                .eq("member_id", UserSessionManager.getSession().getMemberId())
                .eq("content_id", request.getContentId())
                ;
        Long count = appConsumptionUnlockContentService.getMapper().selectCount(unlockQueryCountWrapper);
        AppUserMember member = appUserMemberService.getMapper().selectOne(new QueryWrapper<AppUserMember>().eq("member_id", UserSessionManager.getSession().getMemberId()));
        if (member.getUnlockNumber().compareTo(-1L) == 0) {
            return true;
        }

        // 解锁成功，增加会员解锁记录
        if (count < member.getUnlockNumber()) {
            AppConsumptionUnlockContent appConsumptionUnlockContent = new AppConsumptionUnlockContent();
            appConsumptionUnlockContent.setMemberId(UserSessionManager.getSession().getMemberId());
            appConsumptionUnlockContent.setContentId(request.getContentId());
            appConsumptionUnlockContent.setUserId(UserSessionManager.getUserId());
            appConsumptionUnlockContent.setCreateTime(new Date());
            appConsumptionUnlockContentService.insert(appConsumptionUnlockContent);
            log.info("会员解锁剧目: {}, {}", UserSessionManager.getUserId(), request.getContentId());
            return true;
        }

        return false;
    }

    public Long getSerialPrice(String contentId, Long serialNumber) {
        ContentInfo content = cacheHolder.getDrama(contentId);
        // 判断当前是否需要收费
        if (content.getFeeBegin() == null || content.getFeeBegin().compareTo(serialNumber) > 0) {
            return 0L;
        }
        return content.getFeeCoin();
    }

    public String immediateSerialUrl(String contentId, Long episode_number) {
        // 判断是否需要付费
        ContentInfo content = cacheHolder.getDrama(contentId);
        if (cacheHolder.isMember() || content.getFeeBegin() == null || episode_number.compareTo(content.getFeeBegin()) < 0 || content.getFeeCoin().compareTo(0L) == 0) {
            log.info("不需要付费: {}, {}, {}", UserSessionManager.getUserId(), contentId, episode_number);
            return cacheHolder.getSerialUrl(contentId, episode_number);
        }

        schedulePushAppUserWatchRecords.addWatchRecord(UserSessionManager.getSession().getAppid(), UserSessionManager.getSession().getTfid(), UserSessionManager.getUserId(), contentId, episode_number, 0L, 0L);

        // 看到收费剧集卡点
        if (content.getFeeBegin().compareTo(episode_number) < 1) {

            // 埋点: 查看付费内容
            RetargetingEventDataView eventData = RetargetingEventDataView.builder()
                    .contentId(contentId)
                    .serialNumber(episode_number)
                    .relationId(UserSessionManager.getUserId() + "_view_" + contentId)
                    .fee(true)
                    .userId(UserSessionManager.getUserId())
                    .email(UserSessionManager.getSession().getEmail())
                    .tfid(UserSessionManager.getSession().getTfid())
                    .sid(UserSessionManager.getSession().getSessionId())
                    .deviceId(UserSessionManager.getSession().getDeviceId())
                    .clientIp(UserSessionManager.getSession().getIp())
                    .clientUserAgent(UserSessionManager.getSession().getUserAgent())
                    .build();
            retargetingServiceAdaptor.processEvent(eventData);
            log.info("查看到付费内容: {}, {}", UserSessionManager.getSession().getTfid(), eventData.getRelationId());
        }

        // 校验付费逻辑
        QueryWrapper<AppConsumption> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", UserSessionManager.getUserId())
                .eq("content_id", contentId)
                .eq("serial_number", episode_number);
        Long count = appConsumptionService.getMapper().selectCount(queryWrapper);
        if (count == 0) {
            log.info("用户没有付费: {}, {}, {}", UserSessionManager.getUserId(), contentId, episode_number);
            throw new ApiException(MessageCode.NOT_PAY.getCode());
        }
        return cacheHolder.getSerialUrl(contentId, episode_number);
    }
}
