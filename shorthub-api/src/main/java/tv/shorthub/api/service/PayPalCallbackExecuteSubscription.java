package tv.shorthub.api.service;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tv.shorthub.api.service.recharge.RechargeService;
import tv.shorthub.paypal.service.PayPalCallback;
import tv.shorthub.system.domain.PaypalPaymentLog;
import tv.shorthub.system.service.IPaypalPaymentLogService;
import tv.shorthub.system.service.IPaypalSubscriptionRenewalService;

import java.util.Date;

@Slf4j
@Service
public class PayPalCallbackExecuteSubscription implements PayPalCallback {

    @Autowired
    private RechargeService rechargeService;

    @Autowired
    private IPaypalPaymentLogService paypalPaymentLogService;

    @Autowired
    private IPaypalSubscriptionRenewalService paypalSubscriptionRenewalService;

    @Override
    public void onSuccess(JSONObject data) {
        String subscriptionId = data.getString("subscriptionId");
        JSONObject rawData = data.getJSONObject("subscriptionData");
        try {
            // 更新PayPal订阅日志
            QueryWrapper<PaypalPaymentLog> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("payment_id", subscriptionId);
            PaypalPaymentLog paymentLog = paypalPaymentLogService.getMapper().selectOne(queryWrapper);
            if (paymentLog != null) {
                paymentLog.setState(rawData.getString("status"));
                paymentLog.setRawData(rawData);
                paypalPaymentLogService.update(paymentLog);

                // 获取PayPal返回的订阅开始时间
                Date startTime = null;
                String startTimeStr = rawData.getString("start_time");
                if (startTimeStr != null) {
                    startTime = Date.from(java.time.Instant.parse(startTimeStr));
                }
                // 如果没有获取到开始时间，则使用当前时间
                if (startTime == null) {
                    startTime = new Date();
                }

                // 更新订单状态
                rechargeService.success(paymentLog.getOrderNo(), startTime);

                // 保存订阅结果
                Long totalCycles = rawData.getJSONObject("billing_info").getJSONArray("cycle_executions").getJSONObject(0).getLong("total_cycles");
                paypalSubscriptionRenewalService.insert(paymentLog.getOrderNo(), rawData, 1L, totalCycles);

                log.info("订阅支付成功: subscriptionId={}", subscriptionId);
            } else {
                log.error("未找到对应的订阅支付记录: subscriptionId={}", subscriptionId);
            }
        } catch (Exception e) {
            log.error("处理订阅支付回调失败: subscriptionId={}", subscriptionId, e);
        }
    }

    @Override
    public void onFailed(JSONObject error) {
        log.error("订阅支付失败: {}", error);
    }
}
