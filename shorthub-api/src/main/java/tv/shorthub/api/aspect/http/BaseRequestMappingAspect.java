package tv.shorthub.api.aspect.http;

import com.alibaba.fastjson2.JSON;
import org.aspectj.lang.ProceedingJoinPoint;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import tv.shorthub.api.session.UserSessionManager;
import tv.shorthub.api.utils.MessageCode;
import tv.shorthub.common.exception.ApiException;
import tv.shorthub.common.utils.ServletUtils;
import tv.shorthub.common.utils.StringUtils;

/**
 * 接口日志输出
 *
 * <AUTHOR>
 */
public class BaseRequestMappingAspect
{
    private static final Logger log = LoggerFactory.getLogger(BaseRequestMappingAspect.class);


    public Object doAround(ProceedingJoinPoint pjp) throws Throwable {
        Object[] args = pjp.getArgs();
        long time = System.currentTimeMillis();

        //执行接口
        Object proceed;
        String requestStr = argsArrayToString(args);
        try {
            proceed = pjp.proceed();
            log.info("正常请求: URI {}, cost: {}ms, sessionId: {}, DETAIL: [ip={}, deviceId={}, userId={}, tfid={}, appid={}], REQ: [{}] RES: [{}]",
                    StringUtils.substring(ServletUtils.getRequest().getRequestURI(), 0, 255),
                    System.currentTimeMillis() - time,
                    null != UserSessionManager.getSession() ? UserSessionManager.getSession().getSessionId() : null,
                    UserSessionManager.getSession().getIp(),
                    UserSessionManager.getSession().getDeviceId(),
                    UserSessionManager.getUserId(),
                    UserSessionManager.getSession().getTfid(),
                    UserSessionManager.getSession().getAppid(),
                    requestStr,
                    StringUtils.substring(JSON.toJSONString(proceed), 0, 2000)
            );
        } catch (Throwable e) {
            // 业务异常日志
            if(e instanceof ApiException) {
                log.error("业务异常: URI {}, cost: {}ms, sessionId: {}, DETAIL: [ip={}, deviceId={}, userId={}, tfid={}, appid={}, exception={}], REQ: [{}], RES: [{}]",
                        StringUtils.substring(ServletUtils.getRequest().getRequestURI(), 0, 255),
                        System.currentTimeMillis() - time,
                        null != UserSessionManager.getSession() ? UserSessionManager.getSession().getSessionId() : null,
                        UserSessionManager.getSession().getIp(),
                        UserSessionManager.getSession().getDeviceId(),
                        UserSessionManager.getUserId(),
                        UserSessionManager.getSession().getTfid(),
                        UserSessionManager.getSession().getAppid(),
                        e.getMessage(),
                        requestStr,
                        ""
                );
            } else {
                log.error("异常请求: URI {}, cost: {}ms, sessionId: {}, DETAIL: [ip={}, deviceId={}, userId={}, tfid={}, appid={}, exception={}], REQ: [{}], RES: [{}]",
                        StringUtils.substring(ServletUtils.getRequest().getRequestURI(), 0, 255),
                        System.currentTimeMillis() - time,
                        null != UserSessionManager.getSession() ? UserSessionManager.getSession().getSessionId() : null,
                        UserSessionManager.getSession().getIp(),
                        UserSessionManager.getSession().getDeviceId(),
                        UserSessionManager.getUserId(),
                        UserSessionManager.getSession().getTfid(),
                        UserSessionManager.getSession().getAppid(),
                        e.getMessage(),
                        requestStr,
                        "",
                        e
                );
            }

            throw e;
        }
        return proceed;
    }

    /**
     * 参数拼装
     */
    private String argsArrayToString(Object[] paramsArray)
    {
        String params = "";
        if (paramsArray != null && paramsArray.length > 0)
        {
            for (Object o : paramsArray)
            {
                if (StringUtils.isNotNull(o))
                {
                    try
                    {
                        String jsonObj = JSON.toJSONString(o);
                        params += jsonObj + " ";
                    }
                    catch (Exception e)
                    {
                    }
                }
            }
        }
        return params.trim();
    }
}
