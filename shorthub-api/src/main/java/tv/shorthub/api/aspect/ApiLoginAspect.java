package tv.shorthub.api.aspect;

import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tv.shorthub.api.utils.MessageCode;
import tv.shorthub.common.core.redis.RedisCacheNotify;
import tv.shorthub.api.session.UserSessionManager;
import tv.shorthub.common.exception.ApiException;

import java.lang.reflect.Method;

/**
 * 操作日志记录处理
 *
 * <AUTHOR>
 */
@Aspect
@Component
@Slf4j
public class ApiLoginAspect
{
    @Autowired
    RedisCacheNotify redisCacheNotify;

    @Around("@annotation(tv.shorthub.api.aspect.ApiLogin)")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        // 先检查用户是否登录
        if (null == UserSessionManager.getUserId()) {
            throw new ApiException(MessageCode.USER_NOT_LOGIN.getCode());
        }

        // 获取方法签名
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        // 获取方法对象
        Method method = signature.getMethod();
        // 获取注解对象
        ApiLogin autoCache = method.getAnnotation(ApiLogin.class);

        // 执行目标方法
        return joinPoint.proceed();
    }
}
