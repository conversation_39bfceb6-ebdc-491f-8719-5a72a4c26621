package tv.shorthub.api.session.channel;

import org.springframework.beans.factory.annotation.Autowired;
import tv.shorthub.airwallex.config.AirwallexMultiClientConfig;
import tv.shorthub.airwallex.service.AirwallexConfigStorageHolder;
import tv.shorthub.api.service.cache.CacheHolder;
import tv.shorthub.common.core.session.UserSessionChannel;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import tv.shorthub.api.session.UserSessionManager;
import tv.shorthub.common.utils.StringUtils;
import tv.shorthub.paypal.config.PaypalConfigStorageHolder;
import tv.shorthub.paypal.service.PayPalAccountService;
import tv.shorthub.system.domain.AppPromotion;

/**
 * Web访问配置
 */
@Component
@Slf4j
public class UserSessionChannelWeb implements UserSessionChannel {


    @Autowired
    PayPalAccountService payPalAccountService;

    @Autowired
    AirwallexMultiClientConfig airwallexMultiClientConfig;

    @Autowired
    CacheHolder cacheHolder;

    @Override
    public void channel(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) {
        String paypalClientId = payPalAccountService.getDefaultAccount().getClientId();
        PaypalConfigStorageHolder.set(paypalClientId);
        AirwallexConfigStorageHolder.set(airwallexMultiClientConfig.getDefaultClientId());
        if (httpServletResponse != null && StringUtils.isNotEmpty(paypalClientId)) {
            // 设置CORS相关响应头
            httpServletResponse.setHeader("Access-Control-Expose-Headers", "pcid");
            // 设置pcid响应头
            httpServletResponse.setHeader("pcid", paypalClientId);
        }
        if (StringUtils.isEmpty(UserSessionManager.getSession().getAppid()) && StringUtils.isNotEmpty(UserSessionManager.getSession().getTfid())) {
            AppPromotion appPromotion = cacheHolder.getAppPromotion(UserSessionManager.getSession().getTfid());
            if (null != appPromotion) {
                UserSessionManager.getSession().setAppid(appPromotion.getAppid());
            }
        }
    }
}
