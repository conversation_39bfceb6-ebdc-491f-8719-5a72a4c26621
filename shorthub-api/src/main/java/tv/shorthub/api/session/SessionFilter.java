package tv.shorthub.api.session;

import com.github.pagehelper.PageHelper;
import tv.shorthub.api.session.channel.UserSessionChannelWeb;
import tv.shorthub.common.constant.FloorTypeConstants;
import tv.shorthub.common.core.session.UserChannelHolder;
import tv.shorthub.common.core.session.UserSessionChannel;
import tv.shorthub.common.enums.SysEnv;
import tv.shorthub.common.utils.spring.SpringUtils;
import jakarta.servlet.*;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;


@Slf4j
@Component
public class SessionFilter implements Filter {

    public SessionFilter() {
        userSessionChannelMap.put(FloorTypeConstants.FL_WEB, SpringUtils.getBean(UserSessionChannelWeb.class));
    }

    @Autowired
    private UserSessionManager userSessionManager;

    public static final Map<String, UserSessionChannel> userSessionChannelMap = new HashMap<>();

    private UserSessionChannel getChannelService(String channel) {
        if (userSessionChannelMap.containsKey(channel)) {
            return userSessionChannelMap.get(channel);
        }
        return userSessionChannelMap.get(FloorTypeConstants.FL_WEB);
    }


    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws IOException, ServletException {
        HttpServletRequest httpServletRequest = (HttpServletRequest) servletRequest;

        if (httpServletRequest.getRequestURI().startsWith("/api")) {
            if (SysEnv.CALLBACK.getValue().equals(SpringUtils.getActiveProfile())) {
                filterChain.doFilter(servletRequest, servletResponse);
            } else {

                // 修复偶尔sql被PageHelper添加limit问题
                PageHelper.clearPage();

                UserChannelHolder.set(httpServletRequest.getHeader("app-channel"));

                if (StringUtils.isEmpty(UserChannelHolder.get())) {
                    UserChannelHolder.set(FloorTypeConstants.FL_WEB);
                }

                userSessionManager.init(
                        httpServletRequest,
                        (HttpServletResponse) servletResponse,
                        getChannelService(UserChannelHolder.get())
                );
            }

        } else {
            // 非/api请求, 这里把线程当前会话信息设置为null
            UserSessionManager.cleanThreadLocalSession();
        }
        filterChain.doFilter(servletRequest, servletResponse);

    }
}
