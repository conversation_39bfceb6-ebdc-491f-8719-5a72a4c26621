package tv.shorthub.api.session;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.session.web.http.CookieSerializer;
import org.springframework.session.web.http.DefaultCookieSerializer;
import tv.shorthub.common.utils.spring.SpringUtils;

@Configuration
public class SessionConfig {

    @Bean
    @ConditionalOnProperty(name = "spring.profiles.active", havingValue = "prod")
    public CookieSerializer prodCookieSerializer() {
        DefaultCookieSerializer serializer = new DefaultCookieSerializer();
        serializer.setCookieName("SESSION");
        serializer.setCookiePath("/");
        // Only set domain in production
        serializer.setDomainName("shorthub.tv");
        serializer.setSameSite("Lax");  // Changed from None to Lax for better compatibility
        serializer.setUseSecureCookie(true);  // Only use secure in production
        return serializer;
    }
}
