package tv.shorthub.api.session;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.session.Session;
import org.springframework.session.data.redis.RedisSessionRepository;
import org.springframework.stereotype.Component;
import tv.shorthub.common.core.session.UserSessionDTO;


@Component
@Slf4j
public class SessionUtils {
    @Autowired
    RedisSessionRepository redisSessionRepository;

    /**
     * 激活并绑定充值用户
     * 使用 RedisTemplate 直接操作 Spring Session 数据
     */
    public void activateUserId(String sid, String userId) {
        Session session = redisSessionRepository.findById(sid);
        UserSessionDTO userSessionDTO = session.getAttribute("user");
        userSessionDTO.setUserId(userId);
        session.setAttribute("user", userSessionDTO);
        log.info("Activated userId: sessionId={}, userId={}", sid, userId);
    }

    public UserSessionDTO getUserSession(String sid) {
        Session session = redisSessionRepository.findById(sid);
        return session.getAttribute("user");
    }
}
