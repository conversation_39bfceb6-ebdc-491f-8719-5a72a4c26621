package tv.shorthub.api.session;

import com.alibaba.fastjson2.JSONObject;
import org.springframework.context.i18n.LocaleContextHolder;

import tv.shorthub.api.utils.MessageCode;
import tv.shorthub.common.core.cache.CacheKeyUtils;
import tv.shorthub.common.core.cache.CacheService;
import tv.shorthub.common.core.session.UserSessionChannel;
import tv.shorthub.common.core.session.UserSessionDTO;
import tv.shorthub.common.enums.SysEnv;
import tv.shorthub.common.exception.ApiException;
import tv.shorthub.common.utils.ip.IpUtils;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tv.shorthub.common.utils.spring.SpringUtils;
import tv.shorthub.system.service.IAppBlacklistService;
import tv.shorthub.system.utils.AttributionCacheHolder;

import java.util.function.Supplier;

/**
 * APP 用户会话接口
 */
@Component
@Slf4j
public class UserSessionManager{
    // 会话时长, 单位/秒
    public static final int SESSION_TIMEOUT = 60 * 60 * 24 * 7;

    public static ThreadLocal<UserSessionDTO> session = new ThreadLocal<>();

    @Autowired
    CacheService cacheService;

    @Autowired
    AttributionCacheHolder attributionCacheHolder;

    @Autowired
    IAppBlacklistService blacklistService;


    /**
     *
     * @param httpServletRequest
     * @param sessionChannel
     */
    public void init(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, UserSessionChannel sessionChannel) {
        initSession(httpServletRequest.getSession());

        // 设置IP
        getSession().setIp(IpUtils.getIpAddr(httpServletRequest));
        getSession().setCountry(httpServletRequest.getHeader("CF-IPCountry"));

        if (SysEnv.LOCAL.getValue().equals(SpringUtils.getActiveProfile())) {
            getSession().setTfid("1928018086459039744");
        }

        // 设置User-Agent
        String userAgent = httpServletRequest.getHeader("User-Agent");
        if (StringUtils.isNotEmpty(userAgent)) {
            getSession().setUserAgent(userAgent);
        }

        // 设置语言
        String language = httpServletRequest.getHeader("Accept-Language");
        if (StringUtils.isEmpty(language)) {
            language = LocaleContextHolder.getLocale().toString();
        } else {
            // Accept-Language可能是如 "en-US,en;q=0.9,zh-CN;q=0.8" 这样的格式
            // 取第一个语言代码
            language = language.split(",")[0].split(";")[0];
            if (language.startsWith("zh-tw") || language.startsWith("zh_hant")) {
                language = "zh_TW";
            } else if (language.startsWith("zh-cn") || language.startsWith("zh_hans")) {
                language = "zh_CN";
            } else if (language.startsWith("en")) {
                language = "en_US";
            }
        }
        getSession().setLanguage(language);

        // 设置请求设备
        String device = httpServletRequest.getHeader("device");
        if (StringUtils.isNotEmpty(device)) {
            getSession().setDevice(device.toLowerCase());
        } else {
            getSession().setDevice("none");
        }

        String deviceId = httpServletRequest.getHeader("X-Device-ID");
        if (StringUtils.isNotEmpty(deviceId)) {
            getSession().setDeviceId(deviceId.toLowerCase());
        }

        String appId = httpServletRequest.getHeader("X-App-Id");
        UserSessionManager.getSession().setAppid(appId);


        if (null == UserSessionManager.getSession().getTfid()) {
            // 从缓存归因用户
            JSONObject attribution = attributionCacheHolder.getAttribution(UserSessionManager.getSession().getDeviceId(), UserSessionManager.getSession().getIp(), UserSessionManager.getSession().getTfid());
            if (null != attribution) {
                UserSessionManager.getSession().setTfid(attribution.getString("tid"));
                log.info("通过归因缓存设置用户tfid: {}, body:{}", UserSessionManager.getSession().getTfid(), attribution);
            }
        }

        if (null != sessionChannel) {
            sessionChannel.channel(httpServletRequest, httpServletResponse);
        }

        if (blacklistService.isBlacklisted(UserSessionManager.getSession().getDeviceId(), UserSessionManager.getSession().getUserId(), UserSessionManager.getSession().getIp(), UserSessionManager.getSession().getEmail())) {
            log.info("用户被黑名单限制，禁止访问: {}, {}, {}, {}",
                UserSessionManager.getSession().getDeviceId(), 
                UserSessionManager.getSession().getUserId(), 
                UserSessionManager.getSession().getIp(), 
                UserSessionManager.getSession().getEmail()
            );
            throw new ApiException(MessageCode.SYSTEM_ERROR.getCode());
        }
    }

    private void initSession(HttpSession httpSession) {
        UserSessionDTO user = (UserSessionDTO) httpSession.getAttribute("user");
        if (null == user) {
            user = new UserSessionDTO();
            user.setSessionId(httpSession.getId());
            httpSession.setAttribute("user", user);
        }
        user.setHttpSession(httpSession);
        session.set(user);
    }
    public static UserSessionDTO getSession() {
        return session.get();
    }

    /**
     * 获取当前授权用户ID
     * @return
     */
    public static String getUserId(){
        UserSessionDTO sessionDTO = getSession();
        if (null == sessionDTO) {
            return null;
        }
        String userId = null == sessionDTO.getUserId() ? "sid-" + sessionDTO.getSessionId()  : sessionDTO.getUserId();
        if (userId.startsWith("sid")) {
            String deviceBindUserId = SpringUtils.getBean(CacheService.class).getCacheObject(CacheKeyUtils.getDeviceIdUserId(sessionDTO.getDeviceId()));
            if (null != deviceBindUserId) {
                userId = deviceBindUserId;
                log.info("临时用户通过设备:{}, 获取到绑定的用户id:{}", UserSessionManager.getSession().getDeviceId(), userId);
            }
        }
        return userId;
    }

    public static void login(String userId, String tfid) {
        if (null == getSession()) {
            return;
        }
        getSession().setUserId(userId);
        getSession().setTfid(tfid);
        log.info("### 登录成功 userId[{}], tfid[{}], sessionId[{}]", userId, tfid, getSession().getSessionId());
        getSession().getHttpSession().setMaxInactiveInterval(SESSION_TIMEOUT);
        getSession().refreshSession();
    }

    /**
     * 注销
     */
    public static void logout(Supplier callback) {
        if (null != getSession().getUserId()) {
            log.info("### 注销登录成功 userId[{}], sessionId[{}]", getSession().getUserId(), getSession().getSessionId());
            getSession().getHttpSession().invalidate();
            if (null != callback) {
                callback.get();
            }
        } else {
            log.info("### 注销登录失败 userId is null, sessionId[{}]", getSession().getSessionId());
        }
    }

    public static void cleanThreadLocalSession() {
        session.remove();
        // 清理语言设置
        LocaleContextHolder.resetLocaleContext();
    }
}
