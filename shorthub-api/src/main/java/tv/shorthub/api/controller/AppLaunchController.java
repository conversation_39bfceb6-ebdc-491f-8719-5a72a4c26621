package tv.shorthub.api.controller;

import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import cn.hutool.json.JSONObject;
import tv.shorthub.api.response.Result;

@RestController
@RequestMapping("/api/app")
@Slf4j
public class AppLaunchController {


    @Operation(summary = "APP启动时上报广告参数", description = "APP启动时上报广告参数", tags = {"APP"})
    @PostMapping("/launch")
    public Result<String> launch(@RequestBody JSONObject request) {
        log.info("APP启动时上报广告参数: {}", request);
        return Result.buildOk();
    }
}

