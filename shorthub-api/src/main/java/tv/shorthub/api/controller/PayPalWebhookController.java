package tv.shorthub.api.controller;

import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import tv.shorthub.api.service.paypal.webhook.PayPalWebhookService;
import tv.shorthub.common.core.cache.CacheKeyUtils;
import tv.shorthub.common.core.cache.CacheService;
import tv.shorthub.common.enums.CloudflareQueueEnum;
import tv.shorthub.common.queue.CloudflareQueueService;
import tv.shorthub.system.domain.PaypalPaymentConfig;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.security.*;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;
import java.util.Base64;
import java.util.zip.CRC32;

@Slf4j
@RestController
@RequestMapping("/api/paypal/202020")
public class PayPalWebhookController {

    @Autowired
    CloudflareQueueService cloudflareQueueService;

    @PostMapping(value = "/webhook/{pid}", consumes = "application/json")
    public ResponseEntity<String> handleWebhook(
            HttpEntity<byte[]> requestEntity,
            @RequestHeader("paypal-transmission-id") String transmissionId,
            @RequestHeader("paypal-transmission-time") String timeStamp,
            @RequestHeader("paypal-transmission-sig") String transmissionSig,
            @RequestHeader("paypal-cert-url") String certUrl,
            @PathVariable String pid
    ) {
        try {
            // Get raw event data
            byte[] eventBytes = requestEntity.getBody();
            log.info("Received PayPal webhook event with transmission ID: {}", transmissionId);
            log.info("Received PayPal Event data: {}", new String(eventBytes));
            log.info("Received PayPal timeStamp: {}", timeStamp);
            log.info("Received PayPal transmissionSig: {}", transmissionSig);
            log.info("Received PayPal certUrl: {}", certUrl);
            log.info("pid: {}", pid);


            JSONObject queueData = new JSONObject();
            queueData.put("eventData", new String(eventBytes));
            queueData.put("transmissionId", transmissionId);
            queueData.put("timeStamp", timeStamp);
            queueData.put("transmissionSig", transmissionSig);
            queueData.put("certUrl", certUrl);
            queueData.put("pid", pid);
            cloudflareQueueService.sendMessage(CloudflareQueueEnum.PAYPAL_WEBHOOK.getValue(), queueData);

            log.info("PayPal WebHook joined Queue: {}", transmissionId);
            return ResponseEntity.ok().build();

        } catch (Exception e) {
            log.error("Error verifying signature: {}", e.getMessage(), e);
            return ResponseEntity.ok().build();
        }
    }

}
