package tv.shorthub.api.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import tv.shorthub.api.controller.req.GoogleIdTokenRequest;
import tv.shorthub.api.response.Result;
import tv.shorthub.common.dto.UserInfo;

@RestController
@RequestMapping("/api/remove")
@Slf4j
public class RemoveUserController {


    @Operation(summary = "删除facebook", description = "删除facebook", tags = {"登录"})
    @GetMapping("/me/fb")
    public Result<String> loginWithGoogle() {
        return Result.buildOk();
    }
}
