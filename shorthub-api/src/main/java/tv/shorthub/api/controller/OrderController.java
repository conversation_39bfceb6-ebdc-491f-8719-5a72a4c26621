package tv.shorthub.api.controller;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson2.JSONObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import tv.shorthub.api.controller.req.CreateOrderRequest;
import tv.shorthub.api.controller.req.PaypalExecutePaymentRequest;
import tv.shorthub.api.service.PayPalCallbackExecutePayment;
import tv.shorthub.api.service.PayPalCallbackExecuteSubscription;
import tv.shorthub.api.service.Unsubscription;
import tv.shorthub.api.service.recharge.RechargeService;
import tv.shorthub.api.session.UserSessionManager;
import tv.shorthub.common.dto.RechargeItemDTO;
import tv.shorthub.common.dto.UserVideoHistoryInfo;
import tv.shorthub.api.response.Result;
import tv.shorthub.api.schedule.SchedulePushPaypalPaymentLifecycle;
import tv.shorthub.api.aspect.ApiLogin;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import tv.shorthub.paypal.service.PayPalPaymentService;
import tv.shorthub.paypal.service.PayPalSubscriptionService;
import tv.shorthub.system.domain.AppRechargeItem;
import tv.shorthub.system.domain.PaypalPaymentLifecycle;
import tv.shorthub.system.service.IAppRechargeItemService;

import java.util.Date;
import java.util.List;

@Tag(name = "订单支付")
@RestController
@RequestMapping("/api/order")
public class OrderController {

    private static final Logger log = LoggerFactory.getLogger(OrderController.class);

    @Autowired
    RechargeService rechargeService;

    @Autowired
    PayPalPaymentService payPalPaymentService;

    @Autowired
    PayPalCallbackExecutePayment paymentCallback;

    @Autowired
    PayPalCallbackExecuteSubscription subscriptionCallback;

    @Autowired
    PayPalSubscriptionService payPalSubscriptionService;

    @Autowired
    Unsubscription unsubscription;

    @Autowired
    SchedulePushPaypalPaymentLifecycle schedulePushPaypalPaymentLifecycle;

    @Autowired
    IAppRechargeItemService appRechargeItemService;

    /**
     * 获取充值模板
     * @return
     */
    @Operation(summary = "获取充值模板", description = "获取充值模板")
    @GetMapping("/template")
    public Result<List<RechargeItemDTO>> template()
    {
        return Result.buildOk(
                rechargeService.getRechargeTemplateList(null)
        );
    }

    /**
     * 获取订单记录
     */
    @Operation(summary = "获取订单记录", description = "获取订单记录")
    @GetMapping("/history")
    @ApiLogin
    public Result<List<UserVideoHistoryInfo>> history()
    {
        return Result.buildOk(
                rechargeService.getOrderList()
        );
    }

    /**
     * 查询订单状态
     */
    @Operation(summary = "查询订单支付结果", description = "查询订单支付结果")
    @GetMapping("/query")
    @ApiLogin
    public Result<Boolean> query(@RequestParam String orderNo) {
        return Result.buildOk(
                null != rechargeService.query(orderNo)
        );
    }

    /**
     * 创建订单
     */
    @Operation(summary = "创建订单", description = "创建订单")
    @PostMapping("/create")
    @ApiLogin
    public Result<JSONObject> createOrder(@RequestBody CreateOrderRequest request) throws Exception {
        return Result.buildOk(
                rechargeService.createOrder(request)
        );
    }

    @Operation(summary = "Paypal支付", description = "Paypal支付")
    @PostMapping("/execute-payment")
    @ApiLogin
    public Result<JSONObject> executePayment(@RequestBody PaypalExecutePaymentRequest request) throws Exception {
        log.info("支付回调POST, {}", request.getPaymentId());
        JSONObject data = payPalPaymentService.executePayment(request.getPaymentId(), request.getPayerId(), paymentCallback);
        if (!data.containsKey("exception")) {
            return Result.buildOk(data);
        }
        return Result.buildFail();
    }

    @GetMapping("/execute-payment")
    public String executePayment(@RequestParam("token") String orderId, @RequestParam("PayerID") String payerId) {
        try {
            log.info("收到支付回调: orderId={}, payerId={}", orderId, payerId);
            JSONObject result = payPalPaymentService.executePayment(orderId, payerId, paymentCallback);
            if ("COMPLETED".equals(result.getString("status"))) {
                log.info("支付成功: orderId={}", orderId);
                return "success！";
            } else {
                log.info("支付处理中: orderId={}, status={}", orderId, result.getString("status"));
                return "processing";
            }
        } catch (Exception e) {
            log.error("执行支付失败: orderId={}", orderId, e);
            return "failure";
        }
    }

    @GetMapping("/cancel-payment")
    public String cancelPayment(@RequestParam(value = "token", required = false) String orderId, @RequestParam(value = "PayerID", required = false) String payerId) {
        log.info("收到取消支付回调: orderId={}, payerId={}", orderId, payerId);
        return "success！";
    }

    @Operation(summary = "Paypal订阅支付回调", description = "Paypal订阅支付回调")
    @PostMapping("/execute-subscription")
    public Result<JSONObject> executeSubscription(@RequestBody PaypalExecutePaymentRequest request) throws Exception {
        log.info("订阅支付回调POST, {}", request.getPaymentId());
        // 查询订阅状态
        JSONObject subscription = payPalSubscriptionService.querySubscription(request.getPaymentId());
        if ("ACTIVE".equals(subscription.getString("status"))) {
            // 调用成功回调
            JSONObject data = new JSONObject();
            data.put("subscriptionId", request.getPaymentId());
            data.put("subscriptionData", subscription);
            subscriptionCallback.onSuccess(data);
        } else {
            // 调用失败回调
            JSONObject error = new JSONObject();
            error.put("subscriptionId", request.getPaymentId());
            error.put("error", "Subscription status is not ACTIVE");
            subscriptionCallback.onFailed(error);
        }
        return Result.buildOk(subscription);
    }

    @GetMapping("/execute-subscription")
    public String executeSubscription(@RequestParam("subscription_id") String subscriptionId,
                                    @RequestParam(value = "ba_token", required = false) String baToken,
                                    @RequestParam(value = "token", required = false) String token) {
        try {
            log.info("收到订阅支付回调: subscriptionId={}, baToken={}, token={}", subscriptionId, baToken, token);
            JSONObject subscription = payPalSubscriptionService.querySubscription(subscriptionId);
            log.info("收到订阅支付回调: subscriptionId={}, response: {}", subscriptionId, subscription.toJSONString());
            if ("ACTIVE".equals(subscription.getString("status"))) {
                log.info("订阅支付成功: subscriptionId={}", subscriptionId);
                return "success！";
            } else {
                log.info("订阅支付处理中: subscriptionId={}, status={}", subscriptionId, subscription.getString("status"));
                return "processing";
            }
        } catch (Exception e) {
            log.error("执行订阅支付失败: subscriptionId={}", subscriptionId, e);
            return "failure";
        }
    }

    @GetMapping("/cancel-subscription")
    public String cancelSubscription(@RequestParam(value = "subscription_id", required = false) String subscriptionId,
                                   @RequestParam(value = "ba_token", required = false) String baToken,
                                   @RequestParam(value = "token", required = false) String token) {
        log.info("收到取消订阅回调: subscriptionId={}, baToken={}, token={}", subscriptionId, baToken, token);
        return "success！";
    }


    @GetMapping("/user-unsubscription")
    @ApiLogin
    public Result<Boolean> userUnsubscription() {
        log.info("用户申请主动取消订阅, {}", UserSessionManager.getUserId());
        return Result.buildOk(
                unsubscription.unsubscription(UserSessionManager.getUserId())
        );
    }





    /**
     * 获取Paypal订阅生命周期
     */
    @Operation(summary = "设置Paypal支付组件生命周期", description = "设置Paypal支付组件生命周期")
    @PostMapping("/paypal/lifecycle")
    public Result<Boolean> paypalLifecycle(@RequestBody JSONObject request) {
        PaypalPaymentLifecycle lifecycle = new PaypalPaymentLifecycle();
        lifecycle.setUserId(UserSessionManager.getUserId());
        lifecycle.setOrderNo(StringUtils.isNotEmpty(request.getString("orderNo")) ? request.getString("orderNo") : UserSessionManager.getUserId());
        lifecycle.setState(request.getString("state"));
        lifecycle.setErrmsg(request.getString("errmsg"));
        lifecycle.setExtendData(request.getJSONObject("extendData"));
        lifecycle.setCreateTime(new Date());
        lifecycle.setUpdateTime(lifecycle.getCreateTime());
        schedulePushPaypalPaymentLifecycle.addLifecycle(lifecycle);
        return Result.buildOk(true);
    }

    /**
     * 根据父级方案id查询优惠方案
     * @return RechargeItemDTO
     */
    @Operation(summary = "根据父级方案id查询优惠方案", description = "根据父级方案id查询优惠方案")
    @GetMapping("/discountItemByItemId/{itemId}")
    public Result<RechargeItemDTO> discountItemByItemId(@PathVariable String itemId)
    {
        AppRechargeItem item = appRechargeItemService.discountItemByItemId(itemId);
        if (null == item || item.getId() == null){
            return Result.buildOk();
        }
        return Result.buildOk(BeanUtil.toBean(item,RechargeItemDTO.class));
    }
}
