package tv.shorthub.api.controller;


import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import tv.shorthub.api.controller.req.AppUserClosePayPageRecordRequest;
import tv.shorthub.api.controller.req.ContentCollectRequest;
import tv.shorthub.api.controller.req.HistoryUpdateRequest;
import tv.shorthub.api.controller.res.DiscountItemResponse;
import tv.shorthub.api.session.UserSessionManager;
import tv.shorthub.common.dto.ContentInfo;
import tv.shorthub.common.dto.UserInfo;
import tv.shorthub.common.dto.UserSerialInfo;
import tv.shorthub.api.response.Result;
import tv.shorthub.api.aspect.ApiLogin;
import tv.shorthub.api.service.UserService;

import java.util.List;

@Tag(name = "用户模块")
@RestController
@RequestMapping("/api/user")
public class UserController {


    @Autowired
    UserService userService;

    /**
     * 获取用户信息
     */
    @Operation(summary = "获取用户信息", description = "获取用户信息")
    @GetMapping("/info")
    @ApiLogin
    public Result<UserInfo> userInfo() {
        return Result.buildOk(userService.getUserInfo());
    }


    /**
     * 退出登录
     */
    @Operation(summary = "退出登录", description = "退出登录")
    @GetMapping("/logout")
    @ApiLogin
    public Result<UserInfo> logout() {
        UserSessionManager.logout(null);
        return Result.buildOk();
    }



    /**
     * 获取剧目播放历史
     */
    @Operation(summary = "获取剧目播放历史", description = "获取剧目播放历史")
    @GetMapping("/content/history")
    @ApiLogin
    public Result<List<ContentInfo>> history()
    {
        return Result.buildOk(
                userService.getContentHistory()
        );
    }

    /**
     * 删除剧目播放历史
     */
    @Operation(summary = "删除剧目播放历史", description = "删除剧目播放历史")
    @GetMapping("/content/history/delete")
    @ApiLogin
    public Result<Boolean> deleteContentHistory(@RequestParam String contentId)
    {
        return Result.buildOk(
                userService.deleteContentHistory(contentId)
        );
    }

    /**
     * 上报播放记录
     * @param request
     * @return
     */
    @Operation(summary = "上报播放记录", description = "退出播放时上报一次, 播放中定时上报")
    @PostMapping("/content/history/update")
    @ApiLogin
    public Result<Boolean> historyUpdate(@RequestBody HistoryUpdateRequest request)
    {
        return Result.buildOk(
                userService.uploadContentHistory(request)
        );
    }

    /**
     * 获取最后播放哪一集
     */
    @Operation(summary = "获取最后播放哪一集", description = "获取最后播放哪一集")
    @GetMapping("/content/history/serial/last")
    @ApiLogin
    public Result<UserSerialInfo> historySerialLast(@RequestParam("contentId") String contentId)
    {

        return Result.buildOk(
                userService.getRecordsHistoryLast(contentId)
        );
    }

    /**
     * 获取收藏列表
     */
    @Operation(summary = "获取收藏列表", description = "获取收藏列表")
    @GetMapping("/content/collect/list")
    @ApiLogin
    public Result<List<ContentInfo>> contentCollectList()
    {
        return Result.buildOk(
                userService.getContentCollectList()
        );
    }


    /**
     * 收藏该剧目
     */
    @Operation(summary = "收藏该剧目", description = "收藏该剧目")
    @PostMapping("/content/collect")
    @ApiLogin
    public Result<Boolean> contentCollect(@RequestBody ContentCollectRequest request)
    {
        return Result.buildOk(
                userService.uploadContentCollect(request.getContentId())
        );
    }

    /**
     * 取消收藏该剧目
     */
    @Operation(summary = "取消收藏该剧目", description = "取消收藏该剧目")
    @PostMapping("/content/uncollect")
    @ApiLogin
    public Result<Boolean> uncontentCollect(@RequestBody ContentCollectRequest request)
    {
        return Result.buildOk(
                userService.unContentCollect(request.getContentId())
        );
    }


    /**
     * 用户关闭支付页面记录
     */
    @Operation(summary = "用户关闭支付页面记录", description = "用户关闭支付页面记录")
    @PostMapping("/content/userClosePayPageRecord")
    @ApiLogin
    public Result<Boolean>userClosePayPageRecord(@RequestBody AppUserClosePayPageRecordRequest request)
    {
        userService.userClosePayPageRecord(request);
        return Result.buildOk( );
    }

    /**
     * 查询用户是否展示优惠方案
     */
    @Operation(summary = "查询用户是否展示优惠方案", description = "查询用户是否展示优惠方案")
    @GetMapping("/content/queryIsDiscount")
    @ApiLogin
    public Result<DiscountItemResponse>queryIsDiscount()
    {
        return Result.buildOk( userService.queryIsDiscount());
    }

}
