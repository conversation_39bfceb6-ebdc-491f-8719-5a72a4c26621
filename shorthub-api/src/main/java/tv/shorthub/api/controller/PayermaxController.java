package tv.shorthub.api.controller;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import tv.shorthub.api.service.recharge.RechargeService;
import tv.shorthub.api.utils.MessageCode;
import tv.shorthub.api.session.UserSessionManager;
import tv.shorthub.payermax.service.PayermaxOrderService;
import tv.shorthub.payermax.service.PayermaxSession;
import tv.shorthub.payermax.service.PayermaxSubscribeService;
import tv.shorthub.system.domain.AppOrderInfo;
import tv.shorthub.system.domain.PayermaxPaymentLog;
import tv.shorthub.system.service.IAppOrderInfoService;
import tv.shorthub.system.service.IPayermaxPaymentLogService;

@Tag(name = "payermax")
@RestController
@RequestMapping("/api/payermax")
@Slf4j
public class PayermaxController {

    @Autowired
    PayermaxSession payermaxSession;

    @Autowired
    PayermaxOrderService payermaxOrderService;

    @Autowired
    PayermaxSubscribeService payermaxSubscribeService;

    @Autowired
    IAppOrderInfoService appOrderInfoService;

    @Autowired
    RechargeService rechargeService;

    @Autowired
    IPayermaxPaymentLogService payermaxPaymentLogService;

    // 国家货币映射
    private static final Map<String, String> COUNTRY_CURRENCY_MAP = new HashMap<>();
    
    static {
        // 主要国家货币映射
        COUNTRY_CURRENCY_MAP.put("US", "USD");  // 美国
        COUNTRY_CURRENCY_MAP.put("CN", "CNY");  // 中国
        COUNTRY_CURRENCY_MAP.put("JP", "JPY");  // 日本
        COUNTRY_CURRENCY_MAP.put("KR", "KRW");  // 韩国
        COUNTRY_CURRENCY_MAP.put("GB", "GBP");  // 英国
        COUNTRY_CURRENCY_MAP.put("EU", "EUR");  // 欧盟
        COUNTRY_CURRENCY_MAP.put("DE", "EUR");  // 德国
        COUNTRY_CURRENCY_MAP.put("FR", "EUR");  // 法国
        COUNTRY_CURRENCY_MAP.put("IT", "EUR");  // 意大利
        COUNTRY_CURRENCY_MAP.put("ES", "EUR");  // 西班牙
        COUNTRY_CURRENCY_MAP.put("CA", "CAD");  // 加拿大
        COUNTRY_CURRENCY_MAP.put("AU", "AUD");  // 澳大利亚
        COUNTRY_CURRENCY_MAP.put("SG", "SGD");  // 新加坡
        COUNTRY_CURRENCY_MAP.put("HK", "HKD");  // 香港
        COUNTRY_CURRENCY_MAP.put("TW", "TWD");  // 台湾
        COUNTRY_CURRENCY_MAP.put("IN", "INR");  // 印度
        COUNTRY_CURRENCY_MAP.put("BR", "BRL");  // 巴西
        COUNTRY_CURRENCY_MAP.put("RU", "RUB");  // 俄罗斯
        COUNTRY_CURRENCY_MAP.put("MX", "MXN");  // 墨西哥
        COUNTRY_CURRENCY_MAP.put("TH", "THB");  // 泰国
        COUNTRY_CURRENCY_MAP.put("MY", "MYR");  // 马来西亚
        COUNTRY_CURRENCY_MAP.put("ID", "IDR");  // 印度尼西亚
        COUNTRY_CURRENCY_MAP.put("PH", "PHP");  // 菲律宾
        COUNTRY_CURRENCY_MAP.put("VN", "VND");  // 越南
        COUNTRY_CURRENCY_MAP.put("TR", "TRY");  // 土耳其
        COUNTRY_CURRENCY_MAP.put("SA", "SAR");  // 沙特阿拉伯
        COUNTRY_CURRENCY_MAP.put("AE", "AED");  // 阿联酋
        COUNTRY_CURRENCY_MAP.put("ZA", "ZAR");  // 南非
        COUNTRY_CURRENCY_MAP.put("NZ", "NZD");  // 新西兰
        COUNTRY_CURRENCY_MAP.put("CH", "CHF");  // 瑞士
        COUNTRY_CURRENCY_MAP.put("SE", "SEK");  // 瑞典
        COUNTRY_CURRENCY_MAP.put("NO", "NOK");  // 挪威
        COUNTRY_CURRENCY_MAP.put("DK", "DKK");  // 丹麦
        COUNTRY_CURRENCY_MAP.put("PL", "PLN");  // 波兰
        COUNTRY_CURRENCY_MAP.put("CZ", "CZK");  // 捷克
        COUNTRY_CURRENCY_MAP.put("HU", "HUF");  // 匈牙利
        COUNTRY_CURRENCY_MAP.put("RO", "RON");  // 罗马尼亚
        COUNTRY_CURRENCY_MAP.put("BG", "BGN");  // 保加利亚
        COUNTRY_CURRENCY_MAP.put("HR", "HRK");  // 克罗地亚
        COUNTRY_CURRENCY_MAP.put("RS", "RSD");  // 塞尔维亚
        COUNTRY_CURRENCY_MAP.put("UA", "UAH");  // 乌克兰
        COUNTRY_CURRENCY_MAP.put("BY", "BYN");  // 白俄罗斯
        COUNTRY_CURRENCY_MAP.put("KZ", "KZT");  // 哈萨克斯坦
        COUNTRY_CURRENCY_MAP.put("UZ", "UZS");  // 乌兹别克斯坦
        COUNTRY_CURRENCY_MAP.put("KG", "KGS");  // 吉尔吉斯斯坦
        COUNTRY_CURRENCY_MAP.put("TJ", "TJS");  // 塔吉克斯坦
        COUNTRY_CURRENCY_MAP.put("TM", "TMT");  // 土库曼斯坦
        COUNTRY_CURRENCY_MAP.put("AZ", "AZN");  // 阿塞拜疆
        COUNTRY_CURRENCY_MAP.put("GE", "GEL");  // 格鲁吉亚
        COUNTRY_CURRENCY_MAP.put("AM", "AMD");  // 亚美尼亚
        COUNTRY_CURRENCY_MAP.put("MD", "MDL");  // 摩尔多瓦
        COUNTRY_CURRENCY_MAP.put("EE", "EUR");  // 爱沙尼亚
        COUNTRY_CURRENCY_MAP.put("LV", "EUR");  // 拉脱维亚
        COUNTRY_CURRENCY_MAP.put("LT", "EUR");  // 立陶宛
        COUNTRY_CURRENCY_MAP.put("SK", "EUR");  // 斯洛伐克
        COUNTRY_CURRENCY_MAP.put("SI", "EUR");  // 斯洛文尼亚
        COUNTRY_CURRENCY_MAP.put("MT", "EUR");  // 马耳他
        COUNTRY_CURRENCY_MAP.put("CY", "EUR");  // 塞浦路斯
        COUNTRY_CURRENCY_MAP.put("LU", "EUR");  // 卢森堡
        COUNTRY_CURRENCY_MAP.put("IE", "EUR");  // 爱尔兰
        COUNTRY_CURRENCY_MAP.put("PT", "EUR");  // 葡萄牙
        COUNTRY_CURRENCY_MAP.put("GR", "EUR");  // 希腊
        COUNTRY_CURRENCY_MAP.put("AT", "EUR");  // 奥地利
        COUNTRY_CURRENCY_MAP.put("BE", "EUR");  // 比利时
        COUNTRY_CURRENCY_MAP.put("NL", "EUR");  // 荷兰
        COUNTRY_CURRENCY_MAP.put("FI", "EUR");  // 芬兰
    }

    /**
     * 根据国家代码获取对应的货币代码
     * @param countryCode 国家代码
     * @return 货币代码，如果未找到则返回USD作为默认值
     */
    private String getCurrencyByCountry(String countryCode) {
        if (countryCode == null || countryCode.trim().isEmpty()) {
            log.warn("国家代码为空，使用默认货币USD");
            return "USD";
        }
        
        String currency = COUNTRY_CURRENCY_MAP.get(countryCode.toUpperCase());
        if (currency == null) {
            log.warn("未找到国家 {} 对应的货币，使用默认货币USD", countryCode);
            return "USD";
        }
        
        log.info("国家 {} 使用货币: {}", countryCode, currency);
        return currency;
    }

    @PostMapping("/session")
    public JSONObject applyDropinSession(@RequestBody JSONObject body) {

        String userId = payermaxPaymentLogService.getPayermaxUserId(UserSessionManager.getUserId());
        String userCountry = UserSessionManager.getSession().getCountry();
        String currency = getCurrencyByCountry(userCountry);

        log.info("user country: {}, selected currency: {}", userCountry, currency);

//        return payermaxSession.applyDropinSession(
//                body.getString("totalAmount"),
//                body.getString("mitType"),
//                currency,
//                userCountry,
//                userId,
//                null,
//                body.getList("componentList", String.class)
//        );


        return payermaxSession.applyDropinSession(
                body.getString("totalAmount"),
                body.getString("mitType"),
                "USD",
                null,
                userId,
                null,
                body.getList("componentList", String.class)
        );
    }

    @GetMapping("/subscription/success")
    public JSONObject subscriptionSuccess(
            @RequestParam String outTradeNo,
            @RequestParam String tradeToken,
            @RequestParam String status
    ) {
        log.info("payermax订阅回调: {}", outTradeNo);

        AppOrderInfo orderInfo = appOrderInfoService.getMapper().selectOne(new QueryWrapper<AppOrderInfo>().eq("order_no", outTradeNo));
        if(null == orderInfo) {
            log.info("没有查询到订单: {}", outTradeNo);
            throw new RuntimeException(MessageCode.ORDER_INVALID.getCode());
        }

        JSONObject jsonObject = payermaxSubscribeService.subscriptionQuery(outTradeNo);
        if (null == jsonObject) {
            log.info("没有查询到payermax订阅结果: {}", outTradeNo);
            return null;
        }

        log.info("订单订阅结果: {}", jsonObject);

        JSONObject result = new JSONObject();
        result.put("code", "SUCCESS");
        result.put("msg", "Success");
        return result;
    }

    @PostMapping("/order/success")
    public JSONObject orderSuccess(@RequestBody JSONObject body) {
        log.info("payermax支付回调: {}", body);
        String orderNo = body.getString("outTradeNo");
        AppOrderInfo orderInfo = appOrderInfoService.getMapper().selectOne(new QueryWrapper<AppOrderInfo>().eq("order_no", orderNo));
        if(null == orderInfo) {
            log.info("没有查询到订单: {}", orderNo);

            JSONObject result = new JSONObject();
            result.put("code", "SUCCESS");
            result.put("msg", "Success");
            return result;
        }
        
        if (orderInfo.getPayType().equals("2")) {
            // 检查订阅支付结果
            JSONObject jsonObject = payermaxSubscribeService.subscriptionQuery(orderNo);
            if (null == jsonObject) {
                log.info("没有查询到payermax订阅结果: {}", orderNo);

                JSONObject result = new JSONObject();
                result.put("code", "SUCCESS");
                result.put("msg", "Success");
                return result;
            }
            log.info("payermax订阅结果: {}", jsonObject);
            PayermaxPaymentLog payermaxPaymentLog = payermaxPaymentLogService.getMapper().selectOne(new QueryWrapper<PayermaxPaymentLog>().eq("order_no", orderNo));
            if (null == payermaxPaymentLog) {
                log.info("没有查询到payermax支付日志: {}", orderNo);

                JSONObject result = new JSONObject();
                result.put("code", "SUCCESS");
                result.put("msg", "Success");
                return result;
            }

            payermaxPaymentLog.setDetailRawData(jsonObject);

            // {"msg":"Success.","code":"APPLY_SUCCESS","data":{"subscriptionPaymentDetails":[{"cardOrg":"VISA","payAmount":{"amount":10.0,"centFactor":100,"cent":1000,"currency":{"symbol":"$","displayName":"US Dollar","defaultFractionDigits":2,"currencyCode":"USD","numericCode":840}},"lastPaymentInfo":{"payTime":"2025-06-23T10:12:21+0000","tradeToken":"T2025062310178846014810"},"periodStartTime":"2025-06-23T10:12:23+0000","periodEndTime":"2025-07-23T10:12:22+0000","subscriptionIndex":0,"paymentMethodType":"CARD","paymentStatus":"SUCCESS"}],"subscriptionRequestId":"1937091101907144704","subscriptionPlan":{"subscriptionStatus":"ACTIVE","subscriptionNo":"SUB25062310112317002872003"},"userId":"sid-fdba33c1-38d4-471d-a76a-18253a6e2fe4","merchantNo":"SDP01010115688123"}}
            JSONObject data = jsonObject.getJSONObject("data");
            JSONObject subscriptionPaymentDetails = data.getJSONArray("subscriptionPaymentDetails").getJSONObject(0);
            String paymentStatus = subscriptionPaymentDetails.getString("paymentStatus");
            payermaxPaymentLog.setStatus(paymentStatus);
            log.info("支付状态: {}", paymentStatus);
            if ("SUCCESS".equals(paymentStatus)) {
                Date payTime = subscriptionPaymentDetails.getJSONObject("lastPaymentInfo").getDate("payTime");
                rechargeService.success(orderNo, payTime);
            }
            payermaxPaymentLogService.getMapper().updateById(payermaxPaymentLog);

            JSONObject result = new JSONObject();
            result.put("code", "SUCCESS");
            result.put("msg", "Success");
            return result;
        }

        // 检查订单支付结果
        JSONObject jsonObject = payermaxOrderService.orderQuery(orderNo);
        if (null == jsonObject) {
            log.info("没有查询到payermax订单结果: {}", orderNo);

            JSONObject result = new JSONObject();
            result.put("code", "SUCCESS");
            result.put("msg", "Success");
            return result;
        }

        log.info("payermax普通支付结果: {}", jsonObject);
        
        PayermaxPaymentLog payermaxPaymentLog = payermaxPaymentLogService.getMapper().selectOne(new QueryWrapper<PayermaxPaymentLog>().eq("order_no", orderNo));
        if (null == payermaxPaymentLog) {
            log.info("没有查询到payermax支付日志: {}", orderNo);

            JSONObject result = new JSONObject();
            result.put("code", "SUCCESS");
            result.put("msg", "Success");
            return result;
        }

        payermaxPaymentLog.setDetailRawData(jsonObject);
        //  {"msg":"Success.","code":"APPLY_SUCCESS","data":{"country":"TW","channelNo":"UPC519100175067397468450030170","thirdChannelNo":"658f5984b0c04962a97552b7da724c6c","outTradeNo":"1937093161499152384","currency":"USD","tradeToken":"T2025062310519146014941","paymentDetails":[{"cardInfo":{"cardOrg":"VISA","country":"US","cardIdentifierNo":"444433******1111","cardIdentifierName":"James******"},"payAmount":10,"payCurrency":"USD","paymentMethodType":"CARD"}],"completeTime":"2025-06-23T10:19:34.749Z","resultMsg":"","totalAmount":10,"status":"SUCCESS"}}
        JSONObject data = jsonObject.getJSONObject("data");
        String status = data.getString("status");
        payermaxPaymentLog.setStatus(status);
        log.info("支付状态: {}", status);
        if ("SUCCESS".equals(status)) {
            Date payTime = data.getDate("completeTime");
            rechargeService.success(orderNo, payTime);
        }
        payermaxPaymentLogService.getMapper().updateById(payermaxPaymentLog);

        JSONObject result = new JSONObject();
        result.put("code", "SUCCESS");
        result.put("msg", "Success");
        return result;
    }


    @RequestMapping("/refund")
    public JSONObject refund(@RequestParam(required = false) Map<String, String> params, @RequestBody(required = false) JSONObject body) {
        log.info("payermax退款结果: params {}, body: {}", params, body);
        JSONObject result = new JSONObject();
        result.put("code", "SUCCESS");
        result.put("msg", "Success");
        return result;
    }

}
