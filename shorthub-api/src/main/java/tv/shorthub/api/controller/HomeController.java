package tv.shorthub.api.controller;


import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import tv.shorthub.api.controller.res.HomeConfigResponse;
import tv.shorthub.api.response.Result;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import tv.shorthub.api.service.HomeService;
import tv.shorthub.common.core.cache.CacheKeyUtils;
import tv.shorthub.common.core.redis.RedisCache;
import tv.shorthub.api.session.UserSessionManager;

/**
 * 首页服务
 *
 * <AUTHOR>
 * @date 2024-10-08
 */
@Tag(name = "系统配置")
@RestController
@RequestMapping("/api")
@Slf4j
public class HomeController
{

    @Autowired
    HomeService homeService;
    /**
     * 查询首页
     */
    @Operation(summary = "查询首页", description = "查询首页, 集成了banner、drama")
    @GetMapping("/home")
    public Result<HomeConfigResponse> home() {
        log.info("sessionId={}", UserSessionManager.getSession().getSessionId());
        // 把首页所有信息集成到这里
        return Result.buildOk(
                homeService.getConfig()
        );
    }


    @Autowired
    RedisCache redisCache;


    @GetMapping("/closePsa")
    public Result<Boolean> closePsa() {
        redisCache.setCacheObject(CacheKeyUtils.LOCK_PAYPAL_AUTO_SUBSCRIPTION, true);
        log.info("关闭paypal自动续订");
        return Result.buildOk(true);
    }
    @GetMapping("/enablePsa")
    public Result<Boolean> enablePsa() {
        redisCache.expire(CacheKeyUtils.LOCK_PAYPAL_AUTO_SUBSCRIPTION, 0);
        log.info("启用paypal自动续订");
        return Result.buildOk(true);
    }

}
