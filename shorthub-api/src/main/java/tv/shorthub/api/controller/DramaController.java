package tv.shorthub.api.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import tv.shorthub.api.service.ConsumptionService;
import tv.shorthub.common.dto.ContentInfo;
import tv.shorthub.api.response.Result;
import tv.shorthub.api.service.cache.CacheHolder;

import java.util.List;

@Tag(name = "剧目模块")
@RestController
@RequestMapping("/api")
public class DramaController {

    @Autowired
    CacheHolder cacheHolder;

    @Autowired
    ConsumptionService consumptionService;


    /**
     * 获取所有剧目
     * @return
     */
    @Operation(summary = "获取所有剧目", description = "获取所有剧目")
    @GetMapping("/dramas")
    public Result<List<ContentInfo>> dramas() {
        return Result.buildOk(cacheHolder.getDramas());
    }

    /**
     * 获取所有剧目
     * @return
     */
    @Operation(summary = "获取所有剧目", description = "获取所有剧目")
    @GetMapping("/dramasMore")
    public Result<List<ContentInfo>> dramasMore() {
        return Result.buildOk(cacheHolder.getDramasAppendClose());
    }

    /**
     * 获取剧目信息
     * @return
     */
    @Operation(summary = "获取剧目信息", description = "获取剧目信息")
    @GetMapping("/drama")
    public Result<ContentInfo> drama(@RequestParam String contentId) {
        return Result.buildOk(cacheHolder.getDrama(contentId));
    }

    /**
     * 获取剧集播放地址
     * @param contentId
     * @param number
     * @return
     */
    @Operation(summary = "获取剧集播放地址", description = "获取剧集播放地址")
    @GetMapping("/episode")
    public Result<String> episode(@RequestParam String contentId, @RequestParam Long number) {
        return Result.buildOk(consumptionService.immediateSerialUrl(contentId, number));
    }

}
