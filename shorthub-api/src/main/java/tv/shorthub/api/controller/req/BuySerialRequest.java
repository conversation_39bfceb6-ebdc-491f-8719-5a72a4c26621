package tv.shorthub.api.controller.req;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;
import lombok.Data;

@Data
public class BuySerialRequest {
    // 要购买的内容
    @NotBlank(message = "{validation.contentId.notBlank}")
    private String contentId;
    // 要购买哪一集
    @Min(value = 1, message = "{validation.serialNumber.min}")
    @Max(value = 999, message = "{validation.serialNumber.max}")
    private Long serialNumber;
}
