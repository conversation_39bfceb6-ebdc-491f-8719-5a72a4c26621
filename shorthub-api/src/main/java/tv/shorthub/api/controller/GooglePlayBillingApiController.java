package tv.shorthub.api.controller;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import tv.shorthub.api.response.Result;
import tv.shorthub.api.service.recharge.RechargeService;
import tv.shorthub.googleplay.model.Purchase;
import tv.shorthub.googleplay.service.GooglePlayService;
import tv.shorthub.googleplay.service.impl.GooglePlaySubscriptionService;
import tv.shorthub.system.domain.AppOrderInfo;
import tv.shorthub.system.domain.GooglePlayBillingLog;
import tv.shorthub.system.service.IAppOrderInfoService;
import tv.shorthub.system.service.IGooglePlayBillingLogService;

import java.time.ZoneId;
import java.util.Date;

@RestController
@RequestMapping("/api")
@Slf4j
public class GooglePlayBillingApiController {

    @Autowired
    GooglePlayService googlePlayService;

    @Autowired
    GooglePlaySubscriptionService googlePlaySubscriptionService;

    @Autowired
    RechargeService rechargeService;

    @Autowired
    IGooglePlayBillingLogService googlePlayBillingLogService;

    @Autowired
    IAppOrderInfoService appOrderInfoService;


    /**
     * 验证购买
     */
    @PostMapping("/{packageName}/purchases/verify")
    public Result<Boolean> verifyPurchase(@PathVariable String packageName, @RequestBody JSONObject body) {
        try {
            String orderNo = body.getString("orderId");
            JSONObject purchaseBody = body.getJSONObject("purchase");
            
            // 新格式：从 transactionReceipt 或 dataAndroid 中解析购买信息
            String productId;
            String purchaseToken;
            
            if (purchaseBody.containsKey("purchaseTokenAndroid")) {
                productId = purchaseBody.getString("id"); // 使用 id 字段作为 productId
                purchaseToken = purchaseBody.getString("purchaseTokenAndroid");
            } else if (purchaseBody.containsKey("transactionReceipt")) {
                JSONObject transactionData = JSONObject.parseObject(purchaseBody.getString("transactionReceipt"));
                productId = transactionData.getString("productId");
                purchaseToken = transactionData.getString("purchaseToken");
            } else if (purchaseBody.containsKey("dataAndroid")) {
                JSONObject androidData = JSONObject.parseObject(purchaseBody.getString("dataAndroid"));
                productId = androidData.getString("productId");
                purchaseToken = androidData.getString("purchaseToken");
            } else {
                // 兼容旧格式
                productId = purchaseBody.getString("productId");
                purchaseToken = purchaseBody.getString("purchaseToken");
            }

            

            AppOrderInfo appOrderInfo = appOrderInfoService.getMapper().selectOne(new LambdaQueryWrapper<AppOrderInfo>().eq(AppOrderInfo::getOrderNo, orderNo));
            if (appOrderInfo == null) {
                log.error("订单不存在: {}", orderNo);
                return Result.buildFail();
            }
            
            
            // 检测产品类型：如果产品ID包含"subscription"或"sub"，则认为是订阅产品
            boolean isSubscription = appOrderInfo.getPayType().equals("2");
            
            Purchase purchase;
            if (isSubscription) {
                // 使用订阅服务验证订阅购买
                log.info("检测到订阅产品，使用订阅服务验证: {}", productId);
                purchase = googlePlaySubscriptionService.verifySubscriptionPurchase(packageName, productId, purchaseToken);
            } else {
                // 使用应用内购买服务验证
                log.info("检测到应用内购买产品，使用标准服务验证: {}", productId);
                purchase = googlePlayService.verifyPurchase(packageName, productId, purchaseToken);
            }

            if (purchase == null) {
                log.error("验证购买失败: purchase为null");
                return Result.buildFail();
            }
            
            // 区分订阅和普通购买的成功状态判断
            boolean isValidPurchase;
            if (isSubscription) {
                // 订阅：paymentState=1表示已收到付款（成功）
                isValidPurchase = "1".equals(purchase.getPurchaseState());
                log.info("订阅验证 - paymentState: {}, 是否有效: {}", purchase.getPurchaseState(), isValidPurchase);
            } else {
                // 普通购买：purchaseState=0表示已购买（成功）
                isValidPurchase = "0".equals(purchase.getPurchaseState());
                log.info("普通购买验证 - purchaseState: {}, 是否有效: {}", purchase.getPurchaseState(), isValidPurchase);
            }
            
            if (!isValidPurchase) {
                log.error("验证购买失败: 购买状态无效 - {}", purchase);
                return Result.buildFail();
            }

            log.info("googleplay验证支付成功结果: {}", purchase);
            
            // 检查购买是否已经被确认过了
            if (!purchase.isAcknowledged()) {
                try {
                    if (isSubscription) {
                        googlePlaySubscriptionService.acknowledgeSubscriptionPurchase(packageName, productId, purchaseToken);
                        log.info("订阅购买确认成功: {}", purchase);
                    } else {
                        googlePlayService.acknowledgePurchase(packageName, productId, purchaseToken);
                        log.info("应用内购买确认成功: {}", purchase);
                    }
                } catch (Exception e) {
                    // 检查是否是409 Conflict错误（购买已经被确认过了）
                    if (e.getMessage() != null && e.getMessage().contains("409")) {
                        log.warn("购买已经被确认过了，跳过确认步骤: {}", e.getMessage());
                    } else {
                        log.error("确认购买失败: {}", e.getMessage(), e);
                        // 对于其他错误，继续处理业务逻辑，但记录错误
                    }
                }
            } else {
                log.info("购买已确认，跳过确认步骤");
            }
            // 订阅产品不需要消费，只有应用内购买需要消费
            if (!isSubscription && !appOrderInfo.getPayType().equals("2")) {
                // 检查购买是否已经被消费过了
                if ("0".equals(purchase.getConsumptionState())) {
                    try {
                        googlePlayService.consumePurchase(packageName, productId, purchaseToken);
                        log.info("googleplay消费购买成功结果: {}", purchase);
                    } catch (Exception e) {
                        // 检查是否是400错误（购买已经被消费过了或其他原因）
                        if (e.getMessage() != null && e.getMessage().contains("400")) {
                            log.warn("购买可能已经被消费过了，跳过消费步骤: {}", e.getMessage());
                        } else {
                            log.error("消费购买失败: {}", e.getMessage(), e);
                            // 对于其他错误，继续处理业务逻辑，但记录错误
                        }
                    }
                } else {
                    log.info("购买已消费，跳过消费步骤，消费状态: {}", purchase.getConsumptionState());
                }
            } else if (isSubscription) {
                log.info("订阅产品无需消费，跳过消费步骤");
            }

            rechargeService.success(orderNo, Date.from(purchase.getPurchaseTime().atZone(ZoneId.systemDefault()).toInstant()));

            GooglePlayBillingLog googlePlayBillingLog = new GooglePlayBillingLog();
            googlePlayBillingLog.setOrderNo(orderNo);
            googlePlayBillingLog.setPackageName(packageName);
            googlePlayBillingLog.setRawData(purchaseBody);
            googlePlayBillingLog.setCreateTime(new Date());

            googlePlayBillingLogService.insert(googlePlayBillingLog);
            return Result.buildOk(true);
        } catch (Exception e) {
            log.error("验证购买失败: {}", e.getMessage(), e);
        }
        return Result.buildFail();
    }
}
