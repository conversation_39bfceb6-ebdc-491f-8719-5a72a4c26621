package tv.shorthub.api.controller;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.URLUtil;
import com.alibaba.fastjson2.JSONObject;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import tv.shorthub.api.queue.QueueFastJoin;
import tv.shorthub.api.service.cache.CacheHolder;
import tv.shorthub.common.config.AppConfig;
import tv.shorthub.common.core.cache.CacheKeyUtils;
import tv.shorthub.common.core.redis.RedisCache;
import tv.shorthub.api.session.UserSessionManager;
import tv.shorthub.common.enums.AdPlatformEnum;
import tv.shorthub.common.enums.CloudflareQueueEnum;
import tv.shorthub.common.utils.StringUtils;
import tv.shorthub.system.domain.AppPromotion;
import org.springframework.http.HttpStatus;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.Cookie;
import java.io.IOException;
import java.nio.charset.Charset;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Tag(name = "推广投放模块")
@RestController
@RequestMapping("/api")
@Slf4j
public class PromotionController {

    @Autowired
    private CacheHolder cacheHolder;

    @Autowired
    RedisCache redisCache;

    @Autowired
    QueueFastJoin queueFastJoin;

    private boolean isAdTraffic(Map<String, String> headers, Map<String, String> params) {
        // Check referer for Facebook/Google domains
        String referer = headers.get("referer");
        if (referer != null) {
            if (referer.contains("facebook.com") || referer.contains("google.com")) {
                return true;
            }
        }

        // Check for Facebook/Google specific parameters
        String utmSource = params.get("utm_source");
        if (utmSource != null) {
            if (utmSource.equalsIgnoreCase(AdPlatformEnum.Facebook.getValue()) || utmSource.equalsIgnoreCase(AdPlatformEnum.Google.getValue())) {
                return true;
            }
        }

        // Check for Facebook specific parameters
        if (params.containsKey("fbclid")) {
            return true;
        }

        // Check for Google specific parameters
        if (params.containsKey("gclid")) {
            return true;
        }

        return false;
    }

    /**
     * 快速处理Facebook基础归因数据（仅设置Cookie）
     */
    private void processFacebookCookies(HttpServletRequest request, HttpServletResponse response,
                                       Map<String, String> params) {
        try {
            long currentTimestamp = System.currentTimeMillis();

            // 1. 处理fbclid参数，设置fbc cookie
            String fbclid = params.get("fbclid");
            if (fbclid != null && !fbclid.trim().isEmpty()) {
                String fbc = generateFbcValue(request, fbclid, currentTimestamp);

                Cookie fbcCookie = new Cookie("_fbc", fbc);
                fbcCookie.setMaxAge(90 * 24 * 60 * 60); // 90 days
                fbcCookie.setPath("/");
                fbcCookie.setHttpOnly(false);
                fbcCookie.setSecure(request.isSecure());
                response.addCookie(fbcCookie);

                log.debug("Set fbc cookie: {}", fbc);
            }

            // 2. 检查并设置fbp cookie
            boolean hasFbp = false;
            Cookie[] cookies = request.getCookies();
            if (cookies != null) {
                for (Cookie cookie : cookies) {
                    if ("_fbp".equals(cookie.getName())) {
                        hasFbp = true;
                        break;
                    }
                }
            }

            if (!hasFbp) {
                String fbp = generateFbpValue(request, currentTimestamp);
                Cookie fbpCookie = new Cookie("_fbp", fbp);
                fbpCookie.setMaxAge(90 * 24 * 60 * 60); // 90 days
                fbpCookie.setPath("/");
                fbpCookie.setHttpOnly(false);
                fbpCookie.setSecure(request.isSecure());
                response.addCookie(fbpCookie);

                log.debug("Generated new fbp cookie: {}", fbp);
            }
        } catch (Exception e) {
            log.error("Error processing Facebook cookies", e);
            // 不影响主流程，继续执行
        }
    }

    /**
     * 生成fbc值
     */
    private String generateFbcValue(HttpServletRequest request, String fbclid, long timestamp) {
        String serverName = request.getServerName();
        int subdomainIndex = calculateSubdomainIndex(serverName);
        return String.format("fb.%d.%d.%s", subdomainIndex, timestamp, fbclid);
    }

    /**
     * 生成fbp值
     */
    private String generateFbpValue(HttpServletRequest request, long timestamp) {
        String serverName = request.getServerName();
        int subdomainIndex = calculateSubdomainIndex(serverName);
        long randomNumber = (long) (Math.random() * 10000000000L);
        return String.format("fb.%d.%d.%d", subdomainIndex, timestamp, randomNumber);
    }

    /**
     * 计算子域名索引
     */
    private int calculateSubdomainIndex(String serverName) {
        if (serverName == null) return 1;

        String[] parts = serverName.split("\\.");
        return switch (parts.length) {
            case 1 -> 0; // localhost
            case 2 -> 1; // example.com
            case 3 -> 2; // www.example.com
            default -> parts.length - 1;
        };
    }

    private String determineLanguage(HttpServletRequest request) {
        String acceptLanguage = request.getHeader("Accept-Language");
        if (acceptLanguage == null || acceptLanguage.isEmpty()) {
            return "en_US";
        }

        // 解析Accept-Language头，获取用户首选语言
        String[] languages = acceptLanguage.split(",");
        for (String lang : languages) {
            // 移除权重信息并转换为小写进行比较
            String language = lang.split(";")[0].trim().toLowerCase();

            log.info("Checking language: {}", language);

            // 检查是否匹配支持的语言（忽略大小写）
            if (language.startsWith("zh-tw") || language.startsWith("zh_hant") || language.startsWith("zh-hant")) {
                return "zh_TW";
            } else if (language.startsWith("zh-cn") || language.startsWith("zh_hans") || language.startsWith("zh-hans")) {
                return "zh_TW";
            } else if (language.startsWith("en") || language.startsWith("en-us") ||
                      language.startsWith("en_us")) {
                return "en_US";
            }
        }

        return "en_US"; // 默认返回英文
    }

    @GetMapping("/jump")
    public void jump(
            @RequestParam Map<String, String> params,
            @RequestHeader Map<String, String> headers,
            @RequestBody(required = false) String body,
            HttpServletRequest request,
            HttpServletResponse response) throws IOException {

        String tid = params.get("tid");
        if (tid == null) {
            response.sendError(HttpStatus.BAD_REQUEST.value(), "tid parameter is required");
            return;
        }
        String uid = IdUtil.getSnowflakeNextIdStr();
        if (headers.containsKey("user-agent") && headers.get("user-agent").contains("facebookexternalhit")) {
            // facebook爬虫
            log.info("{}, facebook crawler", uid);
            this.executePromotion(uid, "facebookexternalhit", System.currentTimeMillis(), request, response, 0, params, headers, body, tid);
            return;
        }

        JSONObject data = new JSONObject();
        data.put("params", params);
        data.put("headers", headers);
        data.put("body", body);
        redisCache.setCacheObject(CacheKeyUtils.jumpTempConfig(uid), data, 15, TimeUnit.MINUTES);
        String secondRedirectUrl = AppConfig.getDomainApi() + "/api/jump/floor?uid=" + uid + "&ts=" + System.currentTimeMillis();
        log.info("{}, Redirecting to: {}", uid, secondRedirectUrl);
        response.sendRedirect(AppConfig.getDomainWeb() + "/tracker?redirect=" + secondRedirectUrl);
    }



    @GetMapping("/jump/floor")
    public void jumpFloor(
            @RequestParam String uid,
            @RequestParam String deviceId,
            @RequestParam Long ts,
            HttpServletRequest request,
            HttpServletResponse response) throws IOException {

        if (StringUtils.isEmpty(uid)) {
            response.sendError(HttpStatus.BAD_REQUEST.value(), "uid parameter is required");
            return;
        }

        long cost = System.currentTimeMillis() - ts;

        JSONObject data = redisCache.getCacheObject(CacheKeyUtils.jumpTempConfig(uid));
        if (null == data) {
            return;
        }

        Map<String, String> params = data.getObject("params", Map.class);
        Map<String, String> headers = data.getObject("headers", Map.class);
        String body = data.getString("body");

        String tid = params.get("tid");
        if (tid == null) {
            response.sendError(HttpStatus.BAD_REQUEST.value(), "tid parameter is required");
            return;
        }


        executePromotion(uid, deviceId, ts, request, response, cost, params, headers, body, tid);
    }

    private void executePromotion(String uid, String deviceId, Long ts, HttpServletRequest request, HttpServletResponse response, long cost, Map<String, String> params, Map<String, String> headers, String body, String tid) throws IOException {
        // Log all incoming parameters for tracking
        log.info("Facebook Ad Jump Request - sid:{}, uid:{}, cost: {}ms, params: {}, headers: {}, body: {}",
                UserSessionManager.getSession().getSessionId(), uid, cost, params, headers, body);

        // 快速处理Facebook Cookie设置（不影响性能）
        processFacebookCookies(request, response, params);

        AppPromotion appPromotion = cacheHolder.getAppPromotion(tid);

        if (appPromotion == null) {
            response.sendError(HttpStatus.NOT_FOUND.value(), "system error");
            return;
        }

        // Store ad tracking parameters in session
        UserSessionManager.getSession().setTfid(tid);
        UserSessionManager.getSession().refreshSession();

        // 根据用户语言偏好设置响应头
        String userLanguage = determineLanguage(request);

        // Construct the jump URL with series number and content ID
        String jumpUrl;
        tv.shorthub.system.domain.AppConfig appConfig = cacheHolder.getAppConfig(appPromotion.getAppid());
        if (appConfig.getChannel().equals("Android")) {
            String referrer = URLUtil.buildQuery(params, Charset.forName("UTF-8"));
            String downloadUrl = "https://play.google.com/store/apps/details?id=" + appConfig.getAppid() + "&referrer=" + referrer;
            jumpUrl = String.format(AppConfig.getDomainWeb() + "/download?url=%s&contentId=%s",
                    URLUtil.encode(downloadUrl),
                    appPromotion.getContentId()
                    );
        } else {
            jumpUrl = String.format(AppConfig.getDomainWeb() + "/episodes?episodeNum=%s&id=%s&path=&lang=%s",
                    appPromotion.getSeriesNumber(),
                    appPromotion.getContentId(),
                    userLanguage
            );
        }

        // 构建队列数据（简化版本，复杂处理在队列中进行）
        JSONObject queueData = new JSONObject();
        queueData.put("tid", tid);
        queueData.put("deviceId", deviceId);
        queueData.put("appid", appPromotion.getAppid());
        queueData.put("sid", UserSessionManager.getSession().getSessionId());
        queueData.put("ip", UserSessionManager.getSession().getIp());
        queueData.put("userAgent", request.getHeader("User-Agent"));
        queueData.put("isAdTraffic", isAdTraffic(headers, params));
        queueData.put("timestamp", ts);
        JSONObject extendJson = new JSONObject();
        extendJson.put("cost", cost);
        extendJson.put("cost2", System.currentTimeMillis() - ts);
        extendJson.put("ts", ts);
        queueData.put("extendJson", extendJson);

        // 使用 HttpServletRequest 获取完整的参数
        JSONObject paramsJson = new JSONObject();
        java.util.Enumeration<String> paramNames = request.getParameterNames();
        while (paramNames.hasMoreElements()) {
            String paramName = paramNames.nextElement();
            paramsJson.put(paramName, request.getParameter(paramName));
        }
        paramsJson.putAll(params);
        queueData.put("params", paramsJson);

        // 使用 HttpServletRequest 获取完整的 headers
        JSONObject headersJson = new JSONObject();
        java.util.Enumeration<String> headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            headersJson.put(headerName, request.getHeader(headerName));
        }
        headersJson.putAll(headers);
        queueData.put("headers", headersJson);

        queueData.put("body", body);
        queueFastJoin.addToQueue(CloudflareQueueEnum.PROMOTION_MESSAGE, queueData);

        log.info("Redirecting to URL: {}", jumpUrl);
        response.sendRedirect(jumpUrl);
    }
}
