package tv.shorthub.api.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import tv.shorthub.api.controller.req.BuySerialRequest;
import tv.shorthub.common.dto.UserSerialInfo;
import tv.shorthub.api.response.Result;
import tv.shorthub.api.aspect.ApiLogin;
import tv.shorthub.api.service.ConsumptionService;
import tv.shorthub.api.session.UserSessionManager;

import java.util.List;

@Tag(name = "消费模块")
@RestController
@RequestMapping("/api/consumption")
public class ConsumptionController {

    @Autowired
    ConsumptionService consumptionService;
    /**
     * 获取消费记录
     */
    @Operation(summary = "获取消费记录", description = "获取消费记录")
    @GetMapping("/history")
    @ApiLogin
    public Result<List<UserSerialInfo>> history(
            @RequestParam(required = false, defaultValue = "1") Integer page,
            @RequestParam(required = false, defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) String contentId
    )
    {
        return Result.buildOk(consumptionService.getUserHistory(UserSessionManager.getUserId(), contentId, page, pageSize));
    }


    /**
     * 解锁某一集
     * @return
     */
    @Operation(summary = "购买剧集", description = "购买剧集")
    @PostMapping("/buy")
    @ApiLogin
    public Result<String> buy(@Valid @RequestBody BuySerialRequest request)
    {
        return Result.buildOk(consumptionService.buy(request));
    }


}
