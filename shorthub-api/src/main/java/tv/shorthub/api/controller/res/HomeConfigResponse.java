package tv.shorthub.api.controller.res;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import tv.shorthub.common.dto.BannerConfig;
import tv.shorthub.common.dto.ContentInfo;

import java.util.List;

@Data
public class HomeConfigResponse {
    // 剧目列表
    @Schema(description = "剧目列表")
    private List<ContentInfo> dramas;
    // 轮播图
    @Schema(description = "轮播图")
    private List<BannerConfig> banners;
}
