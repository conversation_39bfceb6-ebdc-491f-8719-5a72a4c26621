package tv.shorthub.api.controller;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import tv.shorthub.api.response.Result;
import tv.shorthub.api.service.UserService;
import tv.shorthub.api.utils.MessageCode;
import tv.shorthub.common.annotation.RateLimiter;
import tv.shorthub.api.session.UserSessionManager;
import tv.shorthub.common.dto.UserInfo;
import tv.shorthub.common.enums.LimitType;
import tv.shorthub.common.utils.MessageUtils;
import tv.shorthub.common.utils.StringUtils;
import tv.shorthub.system.domain.AppUsers;
import tv.shorthub.system.service.IAppUsersService;

@Tag(name = "登录")
@RestController
@RequestMapping("/api/auth")
@Slf4j
public class AuthLoginEmailController {

    @Autowired
    IAppUsersService appUsersService;

    @Autowired
    UserService userService;

    /**
     * 注册邮箱用户
     * @param request
     * @return
     */
    @PostMapping("/registry")
    @RateLimiter(limitType = LimitType.IP, count = 10)
    public Result<UserInfo> registry(@RequestBody JSONObject request) {
        String email = request.getString("email");
        String password = request.getString("password");

        // 检查必填参数
        if (StringUtils.isEmpty(email)) {
            return Result.buildFail(MessageUtils.message(MessageCode.EMAIL_CANNOT_BE_EMPTY.getCode()));
        }
        if (StringUtils.isEmpty(password)) {
            return Result.buildFail(MessageUtils.message(MessageCode.PASSWORD_CANNOT_BE_EMPTY.getCode()));
        }

        // 去除首尾空格
        email = email.trim();
        password = password.trim();

        MessageCode messageCode = safeCheck(email, password);
        if (null != messageCode) {
            return Result.buildFail(MessageUtils.message(messageCode.getCode()));
        }

        // 检查用户是否存在
        AppUsers emailUser = appUsersService.getMapper().selectOne(new QueryWrapper<AppUsers>().eq("email", email));
        if (null != emailUser) {
            return Result.buildFail(MessageUtils.message(MessageCode.USER_EXIST.getCode()));
        }

        UserInfo userInfo = userService.registry(
                email,
                "email",
                email,
                UserSessionManager.getSession().getSessionId(),
                UserSessionManager.getSession().getTfid(),
                UserSessionManager.getSession().getCountry(),
                UserSessionManager.getSession().getLanguage(),
                UserSessionManager.getSession().getIp(),
                UserSessionManager.getSession().getUserAgent(),
                password,
                UserSessionManager.getSession().getDeviceId()
        );

        return Result.buildOk(userInfo);
    }

    public static void main(String[] args) {
        System.out.println(new AuthLoginEmailController().safeCheck("<EMAIL>", "123456"));
    }

    private MessageCode safeCheck(String email, String password) {
        // 验证邮箱格式和长度
        String emailRegex = "^[A-Za-z0-9+_.-]+@([A-Za-z0-9-]+\\.)+[A-Za-z]{2,6}$";
        if (!email.matches(emailRegex) || email.length() > 100) {
            return MessageCode.INVALID_EMAIL_FORMAT;
        }

        // 验证密码强度和长度
        if (password.length() < 6 || password.length() > 50) {
            return MessageCode.PASSWORD_MUST_BE_AT_LEAST_6_CHARACTERS;
        }

        // 邮箱已通过正则验证，只需检查密码的安全性
        // 检查密码中的SQL注入攻击相关字符
        String[] sqlInjectionPatterns = {
                "'", "\"", ";", "--", "/*", "*/", "@@",
                "union", "select", "insert", "update", "delete", "drop", 
                "alter", "create", "exec", "execute", "cast", "char",
                "varchar", "nchar", "nvarchar", "declare", "cursor",
                "sys", "sysobjects", "syscolumns", "table", "waitfor"
        };

        String[] xssPatterns = {
                "<script", "</script", "javascript:", "vbscript:", 
                "onload=", "onerror=", "onclick=", "onmouseover=", 
                "onsubmit=", "onchange=", "eval(", "alert(", "confirm("
        };

        // 转换为小写进行检查
        String passwordLower = password.toLowerCase();

        // 检查密码中的SQL注入模式
        for (String pattern : sqlInjectionPatterns) {
            if (passwordLower.contains(pattern)) {
                return MessageCode.INVALID_EMAIL_FORMAT;
            }
        }

        // 检查密码中的XSS攻击模式
        for (String pattern : xssPatterns) {
            if (passwordLower.contains(pattern)) {
                return MessageCode.INVALID_EMAIL_FORMAT;
            }
        }

        // 检查危险的编码绕过（更精确的检测）
        if (password.contains("\\u00") || password.contains("\\x")) {
            return MessageCode.INVALID_EMAIL_FORMAT;
        }

        // 检查URL编码绕过（排除正常的编码字符）
        if (password.matches(".*%[0-9a-fA-F]{2}.*")) {
            return MessageCode.INVALID_EMAIL_FORMAT;
        }

        // 检查一些特殊的危险字符组合
        if (password.contains("||") || password.contains("&&") || 
            password.contains("../") || password.contains("..\\")) {
            return MessageCode.INVALID_EMAIL_FORMAT;
        }

        return null;
    }


    /**
     * 邮箱用户登录
     * @param request
     * @return
     */
    @PostMapping("/login")
    @RateLimiter(limitType = LimitType.IP, count = 10)
    public Result<UserInfo> login(@RequestBody JSONObject request) {
        String email = request.getString("email");
        String password = request.getString("password");

        // 检查必填参数
        if (StringUtils.isEmpty(email)) {
            return Result.buildFail(MessageUtils.message(MessageCode.EMAIL_CANNOT_BE_EMPTY.getCode()));
        }
        if (StringUtils.isEmpty(password)) {
            return Result.buildFail(MessageUtils.message(MessageCode.PASSWORD_CANNOT_BE_EMPTY.getCode()));
        }

        // 去除首尾空格
        email = email.trim();
        password = password.trim();

        MessageCode messageCode = safeCheck(email, password);
        if (null != messageCode) {
            return Result.buildFail(MessageUtils.message(messageCode.getCode()));
        }

        // 检查用户是否存在
        AppUsers emailUser = appUsersService.getMapper().selectOne(new QueryWrapper<AppUsers>().eq("email", email).eq("password", password));
        if (null == emailUser) {
            return Result.buildFail(MessageUtils.message(MessageCode.USER_NOT_EXIST.getCode()));
        }

        return Result.buildOk(userService.getUserInfo(emailUser));
    }

}
