package tv.shorthub.api.controller.req;

import com.alibaba.fastjson2.JSONObject;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class CreateOrderRequest {

    @Schema(description = "支付类型，paypal, payermax, googleplay, airwallex", example = "paypal, payermax, googleplay, airwallex")
    private String payMethod;

    @Schema(description = "应用包名", example = "tv.shorthub.shorthub")
    private String packageName;

    @Schema(description = "充值模板ID", example = "1")
    private String itemId;

    @Schema(description = "在哪部剧", example = "1")
    private String contentId;

    @Schema(description = "在第几集", example = "1")
    private Long number;

    @Schema(description = "payermax参数", example = "{}")
    private JSONObject payermax;

    @Schema(description = "airwallex参数", example = "{}")
    private JSONObject airwallex;

    @Schema(description = "crypto", example = "{}")
    private JSONObject crypto;
}
