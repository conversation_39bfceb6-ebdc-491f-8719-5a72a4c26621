package tv.shorthub.api.controller;

import com.google.api.client.googleapis.auth.oauth2.GoogleIdToken;
import com.alibaba.fastjson2.JSONObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.server.ResponseStatusException;
import tv.shorthub.api.controller.req.GoogleIdTokenRequest;
import tv.shorthub.api.response.Result;
import tv.shorthub.api.service.auth.GoogleAuthService;
import tv.shorthub.api.service.auth.FacebookAuthService;
import tv.shorthub.api.service.UserService;
import tv.shorthub.common.config.AppConfig;
import tv.shorthub.api.session.UserSessionManager;
import tv.shorthub.common.dto.UserInfo;
import tv.shorthub.common.enums.AdPlatformEnum;
import tv.shorthub.common.utils.StringUtils;
import tv.shorthub.system.domain.AppUsers;
import tv.shorthub.api.config.GoogleOAuthConfig;

@Tag(name = "登录")
@RestController
@RequestMapping("/api/auth")
@Slf4j
public class AuthLoginController {

    @Autowired
    UserService userService;


    @Autowired
    GoogleAuthService googleAuthService;

    @Autowired
    FacebookAuthService facebookAuthService;

    @Autowired
    GoogleOAuthConfig config;


    @Operation(summary = "accessToken登录", description = "accessToken登录", tags = {"登录"})
    @GetMapping("/verify")
    public void loginWithAccessToken(@RequestParam String accessToken,
                                     @RequestParam(required = false) String redirect,
                                     HttpServletResponse httpServletResponse) throws IOException {
        userService.getUserInfoWithAccessToken(accessToken);
        if (StringUtils.isNotEmpty(redirect) && redirect.startsWith("https")) {
            httpServletResponse.sendRedirect(redirect);
        } else {
            httpServletResponse.sendRedirect(AppConfig.getDomainWeb());
        }
    }

    @Operation(summary = "谷歌登录", description = "谷歌登录", tags = {"登录"})
    @PostMapping("/google-login")
    public Result<UserInfo> loginWithGoogle(@RequestBody GoogleIdTokenRequest request) {
        try {
            long time = System.currentTimeMillis();
            log.info("开始谷歌登录... idToken={}, platform={}, packageName={}", request.getIdToken(), request.getPlatform(), request.getPackageName());
            GoogleIdToken.Payload payload = googleAuthService.verify(request.getIdToken(), request.getPlatform(), request.getPackageName());
            log.info("谷歌登录成功...{}, cost:{}ms", payload.getEmail(), System.currentTimeMillis() - time);

            // 检查用户是否存在
            AppUsers appUsers = userService.getGoogleUser(payload.getSubject());
            if (appUsers != null) {
                userService.addLoginRecords(appUsers.getUserId());
                UserSessionManager.login(appUsers.getUserId(), appUsers.getTfid());
                return Result.buildOk(userService.getUserInfo(appUsers.getUserId()));
            }

            // 注册
            return Result.buildOk(
                    userService.registry(
                            payload.getEmail(), AdPlatformEnum.Google.getValue(), payload.getSubject(),
                            UserSessionManager.getSession().getSessionId(),
                            UserSessionManager.getSession().getTfid(),
                            UserSessionManager.getSession().getCountry(),
                            UserSessionManager.getSession().getLanguage(),
                            UserSessionManager.getSession().getIp(),
                            UserSessionManager.getSession().getUserAgent(),
                            UserSessionManager.getSession().getDeviceId()
                            )
            );
        } catch (Exception e) {
            throw new ResponseStatusException(HttpStatus.UNAUTHORIZED, "Invalid Google token", e);
        }
    }

    @Operation(summary = "Facebook登录", description = "Facebook登录", tags = {"登录"})
    @GetMapping("/facebook-login")
    public Result<UserInfo> loginWithFacebook(@RequestParam String accessToken) {
        try {
            long time = System.currentTimeMillis();
            log.info("开始Facebook登录... accessToken={}", accessToken);
            JSONObject userData = facebookAuthService.verifyAccessToken(accessToken);
            log.info("Facebook登录成功...{}, cost:{}ms", userData.toJSONString(), System.currentTimeMillis() - time);

            // 检查用户是否存在
            AppUsers appUsers = userService.getFacebookUser(userData.getString("id"));
            if (appUsers != null) {
                userService.addLoginRecords(appUsers.getUserId());
                UserSessionManager.login(appUsers.getUserId(), appUsers.getTfid());
                return Result.buildOk(userService.getUserInfo(appUsers.getUserId()));
            }

            // 注册
            return Result.buildOk(
                    userService.registry(
                            userData.getString("email"), AdPlatformEnum.Facebook.getValue(), userData.getString("id"),
                            UserSessionManager.getSession().getSessionId(),
                            UserSessionManager.getSession().getTfid(),
                            UserSessionManager.getSession().getCountry(),
                            UserSessionManager.getSession().getLanguage(),
                            UserSessionManager.getSession().getIp(),
                            UserSessionManager.getSession().getUserAgent(),
                            UserSessionManager.getSession().getDeviceId()
                            )
            );
        } catch (Exception e) {
            throw new ResponseStatusException(HttpStatus.UNAUTHORIZED, "Invalid Facebook token", e);
        }
    }

    @Operation(summary = "谷歌授权码登录", description = "使用OAuth 2.0授权码进行谷歌登录", tags = {"登录"})
    @PostMapping("/google-auth-code-login")
    public Result<UserInfo> loginWithGoogleAuthCode(@RequestBody JSONObject request) {
        try {
            String code = request.getString("code");
            if (code == null || code.trim().isEmpty()) {
                throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Authorization code is required");
            }

            String redirectUrl = request.getString("redirectUrl");
            if (redirectUrl == null || redirectUrl.trim().isEmpty()) {
                throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Redirect URI is required");
            }

            String platform = request.getString("platform");
            String packageName = request.getString("packageName");
            if (platform == null || platform.trim().isEmpty()) {
                platform = "web"; // 默认为web平台
            }

            // 处理 storagerelay:// 协议
            if (redirectUrl.startsWith("storagerelay://")) {
                log.info("检测到弹窗模式登录，使用前端传入的redirectUrl: {}", redirectUrl);
                // 在弹窗模式下，直接使用前端传入的 redirectUrl
                // 因为这是 Google 实际使用的回调地址
            } else if (!redirectUrl.startsWith("http://") && !redirectUrl.startsWith("https://")) {
                throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Invalid redirect URI format. Must be a valid HTTP/HTTPS URL or storagerelay:// URL");
            }

            long time = System.currentTimeMillis();
            log.info("开始谷歌授权码登录... code={}, redirectUrl={}, platform={}", code, redirectUrl, platform);
            GoogleIdToken.Payload payload = googleAuthService.verifyAuthorizationCode(code, redirectUrl, platform, packageName);
            log.info("谷歌授权码登录成功...{}, cost:{}ms", payload.getEmail(), System.currentTimeMillis() - time);

            // 检查用户是否存在
            AppUsers appUsers = userService.getGoogleUser(payload.getSubject());
            if (appUsers != null) {
                userService.addLoginRecords(appUsers.getUserId());
                UserSessionManager.login(appUsers.getUserId(), appUsers.getTfid());
                return Result.buildOk(userService.getUserInfo(appUsers.getUserId()));
            }

            // 注册
            return Result.buildOk(
                    userService.registry(
                            payload.getEmail(), AdPlatformEnum.Google.getValue(), payload.getSubject(),
                            UserSessionManager.getSession().getSessionId(),
                            UserSessionManager.getSession().getTfid(),
                            UserSessionManager.getSession().getCountry(),
                            UserSessionManager.getSession().getLanguage(),
                            UserSessionManager.getSession().getIp(),
                            UserSessionManager.getSession().getUserAgent(),
                            UserSessionManager.getSession().getDeviceId()
                    )
            );
        } catch (ResponseStatusException e) {
            throw e;
        } catch (Exception e) {
            log.error("Error during Google authorization code login: {}", e.getMessage(), e);
            throw new ResponseStatusException(HttpStatus.UNAUTHORIZED, "Invalid Google authorization code", e);
        }
    }



    /**
     * 获取客户端id
     * @return
     */
    @GetMapping("/{packageName}/client")
    public Result<String> clientPackageName(@PathVariable String packageName) {
        return Result.buildOk(googleAuthService.getOAuthId(packageName));
    }

}
