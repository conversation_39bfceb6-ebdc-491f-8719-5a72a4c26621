package tv.shorthub.api.controller;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import tv.shorthub.airwallex.constant.AirwallexConstants;
import tv.shorthub.airwallex.service.AirwallexConfigStorageHolder;
import tv.shorthub.airwallex.service.AirwallexPaymentService;
import tv.shorthub.airwallex.service.AirwallexWebhookService;
import tv.shorthub.api.queue.OrderListenQueryMessageHandler;
import tv.shorthub.api.response.Result;
import tv.shorthub.api.service.cache.CacheHolder;
import tv.shorthub.api.service.recharge.RechargeService;
import tv.shorthub.common.core.domain.AjaxResult;
import tv.shorthub.system.domain.*;
import tv.shorthub.system.mapper.AirwallexPaymentLogMapper;
import tv.shorthub.system.mapper.AirwallexUserPlanMapper;
import tv.shorthub.system.mapper.AirwallexWebhookLogMapper;
import tv.shorthub.system.service.IAirwallexUserPlanService;
import tv.shorthub.system.service.IAppOrderInfoService;
import tv.shorthub.system.service.IAppRechargeItemService;

@Tag(name = "airwallex")
@RestController
@RequestMapping("/api/airwallex")
@Slf4j
public class AirwallexController {


    @Autowired
    OrderListenQueryMessageHandler orderListenQueryMessageHandler;

    @Autowired
    AirwallexPaymentLogMapper airwallexPaymentLogMapper;

    @Autowired
    IAirwallexUserPlanService airwallexUserPlanService;

    @Autowired
    IAppOrderInfoService appOrderInfoService;

    @Autowired
    IAppRechargeItemService appRechargeItemService;

    @Autowired
    AirwallexWebhookService webhookService;

    @Autowired
    CacheHolder cacheHolder;

    @Autowired
    AirwallexWebhookLogMapper airwallexWebhookLogMapper;



    @PostMapping("/webhook/{id}")
    public Result webhook(
            @PathVariable Long id,
            @RequestBody String payload,
            @RequestHeader(value = "x-signature", required = false) String signature,
            @RequestHeader(value = "x-timestamp", required = false) String timestamp
    ) {
        try {
            log.info("收到通用Webhook回调: payload={}, signature={}, timestamp={}", payload, signature, timestamp);

            addWebhookLog(id, payload, signature, timestamp);

            AirwallexConfigStorageHolder.set(cacheHolder.getAirwallexPaymentConfig(id).getClientId());

            // 验证签名
            if (signature != null && !webhookService.verifySignature(payload, signature, timestamp)) {
                log.error("Webhook签名验证失败");
                return Result.buildFail();
            }

            JSONObject webhookData = JSON.parseObject(payload);
            String eventType = webhookData.getString("name");  // Airwallex webhook使用"name"字段而不是"event_type"

            // 根据事件类型处理
            switch (eventType) {
                case AirwallexConstants.WebhookEventType.PAYMENT_INTENT_CREATED:
                    webhookService.handlePaymentIntentCreated(webhookData);
                    break;
                case AirwallexConstants.WebhookEventType.PAYMENT_INTENT_SUCCEEDED:
                    webhookService.handlePaymentIntentSucceeded(webhookData);
                    break;
                case AirwallexConstants.WebhookEventType.PAYMENT_INTENT_FAILED:
                    webhookService.handlePaymentIntentFailed(webhookData);
                    break;
                case AirwallexConstants.WebhookEventType.PAYMENT_INTENT_CANCELLED:
                    webhookService.handlePaymentIntentCancelled(webhookData);
                    break;
                case AirwallexConstants.WebhookEventType.PAYMENT_INTENT_REQUIRES_ACTION:
                    webhookService.handlePaymentIntentRequiresAction(webhookData);
                    break;
                case AirwallexConstants.WebhookEventType.PAYMENT_CONSENT_VERIFIED:
                    webhookService.handlePaymentConsentVerified(webhookData);
                    break;
                case AirwallexConstants.WebhookEventType.PAYMENT_CONSENT_FAILED:
                    webhookService.handlePaymentConsentFailed(webhookData);
                    break;
                case AirwallexConstants.WebhookEventType.PAYMENT_CONSENT_DISABLED:
                    webhookService.handlePaymentConsentDisabled(webhookData);
                    break;
                default:
                    log.warn("未知的事件类型: {}", eventType);
            }

        } catch (Exception e) {
            log.error("处理通用Webhook回调失败", e);
        }
        return Result.buildOk();
    }

    private void addWebhookLog(Long id, String payload, String signature, String timestamp) {
        AirwallexWebhookLog airwallexWebhookLog = new AirwallexWebhookLog();
        airwallexWebhookLog.setEventData(JSONObject.parseObject(payload));
        airwallexWebhookLog.setPid(id);
        airwallexWebhookLog.setTimestamp(timestamp);
        airwallexWebhookLog.setSignature(signature);
        airwallexWebhookLog.setEventId(airwallexWebhookLog.getEventData().getString("id"));
        airwallexWebhookLog.setCreateTime(new Date());
        airwallexWebhookLogMapper.insert(airwallexWebhookLog);
    }

    @PostMapping("/callback")
    public Result callback(@RequestBody JSONObject body) {
        log.info("airwallex支付回调: {}", body);
        String orderNo = body.getString("orderNo");
        if (body.containsKey("consent")) {
            AirwallexPaymentLog paymentLog = airwallexPaymentLogMapper.selectOne(new QueryWrapper<AirwallexPaymentLog>().eq("order_no", orderNo).last("limit 1"));
            if (null == paymentLog) {
                log.info("未找到airwallex支付日志: {}", orderNo);
                return Result.buildFail();
            }
            JSONObject consent = body.getJSONObject("consent");
            paymentLog.setConsentMit(consent);
            airwallexPaymentLogMapper.updateById(paymentLog);

            // 创建扣费计划
            AppOrderInfo orderInfo = appOrderInfoService.getMapper().selectOne(new QueryWrapper<AppOrderInfo>().eq("order_no", orderNo));
            AppRechargeItem item = appRechargeItemService.getMapper().selectOne(new QueryWrapper<AppRechargeItem>().eq("item_id", orderInfo.getFeeItemId()));
            AirwallexUserPlan airwallexUserPlan = new AirwallexUserPlan();
            airwallexUserPlan.setOrderNo(orderNo);
            airwallexUserPlan.setConsumerId(paymentLog.getUserId());
            airwallexUserPlan.setPaymentId(paymentLog.getPaymentId());
            airwallexUserPlan.setPeriod(item.getSubscriptionPeriod());
            airwallexUserPlan.setPaymentConsentId(consent.getString("payment_consent_id"));
            airwallexUserPlan.setEnabled(true);
            airwallexUserPlan.setExtendData(consent);
            airwallexUserPlan.setCreateTime(new Date());
            airwallexUserPlanService.insert(airwallexUserPlan);
            log.info("保存airwallex扣费计划: {}", orderNo);

        }
        return Result.buildOk(orderListenQueryMessageHandler.airwallex(orderNo));
    }
}
