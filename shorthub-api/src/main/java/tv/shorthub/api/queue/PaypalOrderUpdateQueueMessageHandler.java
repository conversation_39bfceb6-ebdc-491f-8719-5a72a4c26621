package tv.shorthub.api.queue;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tv.shorthub.api.service.PayPalCallbackExecutePayment;
import tv.shorthub.api.service.recharge.RechargeService;
import tv.shorthub.common.queue.QueueMessageHandler;
import tv.shorthub.common.utils.DateUtils;
import tv.shorthub.paypal.constant.PayPalConstants;
import tv.shorthub.paypal.service.PayPalPaymentService;
import tv.shorthub.system.domain.AppOrderInfo;
import tv.shorthub.system.domain.PaypalPaymentLog;
import tv.shorthub.system.domain.PaypalSubscriptionRenewal;
import tv.shorthub.system.service.IAppOrderInfoService;
import tv.shorthub.system.service.IPaypalPaymentLogService;
import tv.shorthub.system.service.IPaypalSubscriptionRenewalService;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

@Component
@Slf4j
public class PaypalOrderUpdateQueueMessageHandler implements QueueMessageHandler {

    @Autowired
    IPaypalPaymentLogService paypalPaymentLogService;

    @Autowired
    IAppOrderInfoService appOrderInfoService;

    @Autowired
    RechargeService rechargeService;

    @Autowired
    IPaypalSubscriptionRenewalService paypalSubscriptionRenewalService;

    @Autowired
    PayPalPaymentService payPalPaymentService;

    @Autowired
    PayPalCallbackExecutePayment paymentCallback;

    @Override
    public boolean handleMessage(JSONObject message) {
        log.info("handler paypal update message: {}", message);
        try {
            String orderNo = message.getString("orderNo");
            AppOrderInfo orderInfo = appOrderInfoService.getMapper().selectOne(new QueryWrapper<AppOrderInfo>().eq("order_no", orderNo));
            if (null == orderInfo) {
                log.info("要履行的订单不存在. {}", orderNo);
                return true;
            }
            PaypalPaymentLog paymentLog = paypalPaymentLogService.getMapper().selectOne(new QueryWrapper<PaypalPaymentLog>().eq("order_no", orderNo));

            // 充值履行
            rechargeFulfill(orderInfo, paymentLog);

            // 退款履行
            refundFulfill(orderInfo, paymentLog);

            return true;
        } catch (Exception e) {
            log.error("handle paypal update message error: {}", e.getMessage(), e);
        }
        return false;
    }

    public void rechargeFulfill(AppOrderInfo orderInfo, PaypalPaymentLog paymentLog) {

        JSONObject rawData = paymentLog.getRawData();
        log.info("处理充值履行逻辑: {}, rawData: {}", orderInfo.getOrderNo(), rawData);

        // 订阅支付/续订
        if (isSubscriptionPayment(rawData)) {
            handleSubscriptionPayment(orderInfo, rawData);
            return;
        }

        // 普通支付
        if (orderInfo.getOrderStatus().compareTo(1L) == 0) {
            log.info("订单已履行. {}", orderInfo.getOrderNo());
            return;
        }

        if (rawData.containsKey("status") && rawData.getString("status").equals("COMPLETED")) {
            if (rawData.containsKey("purchase_units")) {
                JSONArray purchase_units = rawData.getJSONArray("purchase_units");
                if (!purchase_units.isEmpty() && purchase_units.getJSONObject(0).containsKey("payments")) {
                    JSONObject payments = purchase_units.getJSONObject(0).getJSONObject("payments");
                    // 已捕获并且不包含退款单
                    if (payments.containsKey("captures") && !payments.containsKey("refunds")) {
                        Date payTime = payments.getJSONArray("captures").getJSONObject(0).getDate("create_time");
                        // 充值处理
                        rechargeService.success(orderInfo.getOrderNo(), payTime);
                        orderInfo = appOrderInfoService.getMapper().selectOne(new QueryWrapper<AppOrderInfo>().eq("order_no", orderInfo.getOrderNo()));
                        log.info("订单履行完成. {}", orderInfo.getOrderNo());
                    }
                }
            }
        } else if (rawData.containsKey("status") && rawData.getString("status").equals("APPROVED")) {
            log.info("[更新]PayPal订单状态为APPROVED，开始执行支付捕获: orderNo={}, paymentId={}", orderInfo.getOrderNo(), paymentLog.getPaymentId());

            // 从rawData中提取payerId
            String payerId = null;
            if (rawData.containsKey("payer") && rawData.getJSONObject("payer").containsKey("payer_id")) {
                payerId = rawData.getJSONObject("payer").getString("payer_id");
            }

            if (payerId != null) {
                try {
                    payPalPaymentService.executePayment(paymentLog.getPaymentId(), payerId, paymentCallback);
                } catch (Exception exception) {
                    log.error("订单捕获异常: {}", orderInfo.getOrderNo(), exception);
                }
            } else {
                log.warn("无法从PayPal响应中获取payerId: orderNo={}, paymentId={}", orderInfo.getOrderNo(), paymentLog.getPaymentId());
            }

            PaypalPaymentLog updatePaypalLog = new PaypalPaymentLog();
            updatePaypalLog.setId(paymentLog.getId());
            updatePaypalLog.setRawData(rawData);
            paypalPaymentLogService.update(updatePaypalLog);
        }

    }

    private boolean isSubscriptionPayment(JSONObject rawData) {
        return rawData.containsKey("billing_info") && rawData.containsKey("status") &&
               rawData.getString("status").equals("ACTIVE");
    }

    private void handleSubscriptionPayment(AppOrderInfo orderInfo, JSONObject rawData) {
        try {

            // 当前是否可以订阅充值
            boolean subscription = subscription(rawData, orderInfo.getOrderNo());
            if (subscription) {
                // 处理订阅支付
                rechargeService.doSuccess(orderInfo);
            }

            log.info("订阅订单履行完成. orderNo: {}, subscriptionId: {}, 本次是否履行: {}",
                orderInfo.getOrderNo(), rawData.getString("id"), subscription);
        } catch (Exception e) {
            log.error("处理订阅支付失败: {}", orderInfo.getOrderNo(), e);
        }
    }

    private boolean subscription(JSONObject rawData, String orderNo) throws ParseException {
        JSONObject billingInfo = rawData.getJSONObject("billing_info");
        // 检查付款失败次数
        int failedPaymentsCount = billingInfo.getIntValue("failed_payments_count", 0);
        if (failedPaymentsCount > 2) {
            log.info("订单号：{}，存在{}次付款失败记录", orderNo, failedPaymentsCount);
            return false;
        }

        // 检查最后付款状态
        JSONObject lastPayment = billingInfo.getJSONObject("last_payment");
        if (lastPayment == null) {
            log.info("订单号：{}，未找到last_payment字段，可能未完成付款", orderNo);
            return false;
        }

        // 检查订阅周期执行情况
        JSONArray cycleExecutions = billingInfo.getJSONArray("cycle_executions");
        if (cycleExecutions == null || cycleExecutions.isEmpty()) {
            log.warn("订单号：{}，未找到cycle_executions信息", orderNo);
            return false;
        }

        for (int i = 0; i < cycleExecutions.size(); i++) {
            JSONObject currentCycle = cycleExecutions.getJSONObject(0);

            long cyclesCompleted = currentCycle.getIntValue("cycles_completed", 0);
            long cyclesRemaining = currentCycle.getIntValue("cycles_remaining", 0);

            if (cyclesCompleted == 0) {
                log.info("订单号：{}，尚未完成任何付款周期: {}/{}", orderNo, (i+1), cycleExecutions.size());
                continue;
            }

            // 检查当前周期是否已充值
            long count = paypalSubscriptionRenewalService.getMapper().selectCount(
                    new QueryWrapper<PaypalSubscriptionRenewal>()
                            .eq("order_no", orderNo)
                            .eq("cycle_number", cyclesCompleted)
            );

            if (count > 0) {
                log.info("订单号：{}，周期{}已经充值过，跳过处理", orderNo, cyclesCompleted);
                continue;
            }

            paypalSubscriptionRenewalService.insert(orderNo, rawData, cyclesCompleted, cyclesCompleted + cyclesRemaining);
            log.info("准备充值, 订阅ID:{}, 轮次:{}/{}, 周期:{}/{}", rawData.getString("id"), i+1, cycleExecutions.size(), cyclesCompleted, cyclesRemaining + cyclesCompleted);

            return true;
        }


        return false;
    }

    private void refundFulfill(AppOrderInfo orderInfo, PaypalPaymentLog paymentLog) {
        if (orderInfo.getRefundStatus()) {
            log.info("订单已退款. {}", orderInfo.getOrderNo());
            return;
        } else if (orderInfo.getOrderStatus().compareTo(0L) == 0) {
            log.info("订单未履行, 无需退款. {}", orderInfo.getOrderNo());
//            UpdateWrapper<AppOrderInfo> updateWrapper = new UpdateWrapper<>();
//            updateWrapper.eq("order_no", orderInfo.getOrderNo());
//            updateWrapper.set("refund_status", true);
//            appOrderInfoService.getMapper().update(updateWrapper);
            return;
        }

        JSONObject rawData = paymentLog.getRawData();
        boolean hasRefund = false;

        // 检查退款状态
        if (PayPalConstants.PaymentMethod.SUBSCRIPTION.equals(paymentLog.getPaymentMethod())) {
            // 订阅支付检查退款
            if (rawData.containsKey("billing_info")) {
                JSONObject billingInfo = rawData.getJSONObject("billing_info");
                if (billingInfo.containsKey("last_payment")) {
                    JSONObject lastPayment = billingInfo.getJSONObject("last_payment");
                    if (lastPayment.containsKey("status") &&
                        "REFUNDED".equalsIgnoreCase(lastPayment.getString("status"))) {
                        hasRefund = true;
                    }
                }
            }
        } else {
            // 普通支付检查退款
            if (rawData.containsKey("purchase_units")) {
                JSONArray purchaseUnits = rawData.getJSONArray("purchase_units");
                if (!purchaseUnits.isEmpty()) {
                    JSONObject purchaseUnit = purchaseUnits.getJSONObject(0);
                    if (purchaseUnit.containsKey("payments")) {
                        JSONObject payments = purchaseUnit.getJSONObject("payments");
                        if (payments.containsKey("refunds") &&
                            !payments.getJSONArray("refunds").isEmpty()) {
                            hasRefund = true;
                        }
                    }
                }
            }
        }

        // 如果检测到退款，更新订单状态
        if (hasRefund) {
            UpdateWrapper<AppOrderInfo> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("order_no", orderInfo.getOrderNo());
            updateWrapper.set("refund_status", true);
            appOrderInfoService.getMapper().update(updateWrapper);
            log.info("处理订单退款完成: {}", orderInfo.getOrderNo());
        }
    }
}
