//package tv.shorthub.api.queue.redis;
//
//import com.alibaba.fastjson2.JSONObject;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.CommandLineRunner;
//import org.springframework.stereotype.Component;
//
///**
// * Redis队列服务使用示例
// * 展示如何使用Redis队列服务进行消息发布和消费
// */
//@Component
//@Slf4j
//public class QueueServiceExample implements CommandLineRunner {
//
//    @Autowired
//    private RedisQueue redisQueue;
//
//    @Autowired
//    private RedisQueueConsumer queueConsumer;
//
//    @Override
//    public void run(String... args) throws Exception {
//        // 演示基本的队列操作
//        demonstrateBasicQueueOperations();
//
//        // 演示延迟消息
//        demonstrateDelayedMessages();
//
//        // 演示消费者
//        demonstrateConsumerOperations();
//
//        // 演示批量操作
//        demonstrateBatchOperations();
//    }
//
//    /**
//     * 演示基本的队列操作
//     */
//    private void demonstrateBasicQueueOperations() {
//        log.info("=== 演示基本队列操作 ===");
//
//        try {
//            String queueName = "test-queue";
//
//            // 创建测试数据
//            JSONObject data1 = new JSONObject();
//            data1.put("type", "user_registration");
//            data1.put("userId", 12345);
//            data1.put("email", "<EMAIL>");
//            data1.put("timestamp", System.currentTimeMillis());
//
//            JSONObject data2 = new JSONObject();
//            data2.put("type", "order_created");
//            data2.put("orderId", 67890);
//            data2.put("amount", 99.99);
//            data2.put("currency", "USD");
//
//            // 发布消息
//            String messageId1 = redisQueue.publish(queueName, data1);
//            String messageId2 = redisQueue.publish(queueName, data2);
//
//            log.info("Published messages: {} and {}", messageId1, messageId2);
//
//            // 获取队列状态
//            JSONObject stats = redisQueue.getQueueStats(queueName);
//            log.info("Queue stats: {}", stats.toJSONString());
//
//            // 消费消息
//            RedisQueueMessage message1 = redisQueue.consumeNoWait(queueName);
//            if (message1 != null) {
//                log.info("Consumed message: {}", message1.getData().toJSONString());
//
//                // 模拟处理消息
//                boolean processed = processMessage(message1);
//
//                if (processed) {
//                    // 确认消息
//                    redisQueue.ack(queueName, message1.getId());
//                    log.info("Message acknowledged: {}", message1.getId());
//                } else {
//                    // 拒绝消息
//                    redisQueue.nack(queueName, message1.getId(), true);
//                    log.info("Message rejected and requeued: {}", message1.getId());
//                }
//            }
//
//        } catch (Exception e) {
//            log.error("Error in basic queue operations", e);
//        }
//    }
//
//    /**
//     * 演示延迟消息
//     */
//    private void demonstrateDelayedMessages() {
//        log.info("=== 演示延迟消息 ===");
//
//        try {
//            String queueName = "delayed-queue";
//
//            // 创建延迟消息数据
//            JSONObject delayedData = new JSONObject();
//            delayedData.put("type", "reminder");
//            delayedData.put("message", "This is a delayed message");
//            delayedData.put("scheduleTime", System.currentTimeMillis() + 30 * 1000); // 30秒后
//
//            // 发布延迟消息（30秒后执行）
//            String delayedMessageId = redisQueue.publishDelay(queueName, delayedData, 30);
//            log.info("Published delayed message: {}", delayedMessageId);
//
//            // 查看延迟队列状态
//            JSONObject stats = redisQueue.getQueueStats(queueName);
//            log.info("Delayed queue stats: {}", stats.toJSONString());
//
//            // 立即尝试消费（应该没有消息）
//            RedisQueueMessage immediateMessage = redisQueue.consumeNoWait(queueName);
//            if (immediateMessage == null) {
//                log.info("No immediate message available (as expected for delayed message)");
//            }
//
//        } catch (Exception e) {
//            log.error("Error in delayed message operations", e);
//        }
//    }
//
//    /**
//     * 演示消费者操作
//     */
//    private void demonstrateConsumerOperations() {
//        log.info("=== 演示消费者操作 ===");
//
//        try {
//            String queueName = "consumer-queue";
//
//            // 创建消息处理器
//            RedisQueueConsumer.MessageHandler handler = message -> {
//                try {
//                    log.info("Processing message: {}", message.getData().toJSONString());
//
//                    // 模拟业务处理
//                    JSONObject data = message.getData();
//                    String messageType = data.getString("type");
//
//                    return switch (messageType) {
//                        case "email" -> processEmailMessage(data);
//                        case "sms" -> processSmsMessage(data);
//                        case "push" -> processPushMessage(data);
//                        default -> {
//                            log.warn("Unknown message type: {}", messageType);
//                            yield false;
//                        }
//                    };
//
//                } catch (Exception e) {
//                    log.error("Error processing message", e);
//                    return false;
//                }
//            };
//
//            // 启动消费者
//            queueConsumer.startConsumerNoWait(queueName, handler, 1000);
//
//            // 发布一些测试消息
//            JSONObject emailData = new JSONObject();
//            emailData.put("type", "email");
//            emailData.put("to", "<EMAIL>");
//            emailData.put("subject", "Welcome!");
//            emailData.put("body", "Welcome to our service!");
//
//            JSONObject smsData = new JSONObject();
//            smsData.put("type", "sms");
//            smsData.put("to", "+1234567890");
//            smsData.put("message", "Your verification code is 123456");
//
//            JSONObject pushData = new JSONObject();
//            pushData.put("type", "push");
//            pushData.put("deviceId", "device123");
//            pushData.put("title", "New Message");
//            pushData.put("body", "You have a new message");
//
//            redisQueue.publish(queueName, emailData);
//            redisQueue.publish(queueName, smsData);
//            redisQueue.publish(queueName, pushData);
//
//            log.info("Published test messages for consumer");
//
//        } catch (Exception e) {
//            log.error("Error in consumer operations", e);
//        }
//    }
//
//    /**
//     * 演示批量操作
//     */
//    private void demonstrateBatchOperations() {
//        log.info("=== 演示批量操作 ===");
//
//        try {
//            String queueName = "batch-queue";
//
//            // 批量发布消息
//            for (int i = 0; i < 10; i++) {
//                JSONObject batchData = new JSONObject();
//                batchData.put("type", "batch_item");
//                batchData.put("itemId", i);
//                batchData.put("data", "Batch item " + i);
//
//                redisQueue.publish(queueName, batchData);
//            }
//
//            log.info("Published 10 batch messages");
//
//            // 创建批量处理器
//            RedisQueueConsumer.MessageHandler batchHandler = message -> {
//                try {
//                    JSONObject data = message.getData();
//                    log.info("Batch processing item: {}", data.getInteger("itemId"));
//
//                    // 模拟批量处理
//                    Thread.sleep(100); // 模拟处理时间
//
//                    return true;
//                } catch (Exception e) {
//                    log.error("Error in batch processing", e);
//                    return false;
//                }
//            };
//
//            // 启动批量消费者
//            queueConsumer.startBatchConsumer(queueName, batchHandler, 5, 2);
//
//            log.info("Started batch consumer");
//
//        } catch (Exception e) {
//            log.error("Error in batch operations", e);
//        }
//    }
//
//    /**
//     * 模拟处理消息
//     */
//    private boolean processMessage(RedisQueueMessage message) {
//        try {
//            JSONObject data = message.getData();
//            String type = data.getString("type");
//
//            // 模拟不同的处理逻辑
//            switch (type) {
//                case "user_registration":
//                    log.info("Processing user registration for user: {}", data.getInteger("userId"));
//                    // 模拟发送欢迎邮件
//                    Thread.sleep(100);
//                    return true;
//
//                case "order_created":
//                    log.info("Processing order creation for order: {}", data.getInteger("orderId"));
//                    // 模拟更新库存
//                    Thread.sleep(150);
//                    return true;
//
//                default:
//                    log.warn("Unknown message type: {}", type);
//                    return false;
//            }
//
//        } catch (Exception e) {
//            log.error("Error processing message", e);
//            return false;
//        }
//    }
//
//    /**
//     * 处理邮件消息
//     */
//    private boolean processEmailMessage(JSONObject data) {
//        try {
//            log.info("Sending email to: {}, subject: {}",
//                data.getString("to"), data.getString("subject"));
//
//            // 模拟发送邮件
//            Thread.sleep(200);
//
//            return true;
//        } catch (Exception e) {
//            log.error("Error sending email", e);
//            return false;
//        }
//    }
//
//    /**
//     * 处理短信消息
//     */
//    private boolean processSmsMessage(JSONObject data) {
//        try {
//            log.info("Sending SMS to: {}, message: {}",
//                data.getString("to"), data.getString("message"));
//
//            // 模拟发送短信
//            Thread.sleep(300);
//
//            return true;
//        } catch (Exception e) {
//            log.error("Error sending SMS", e);
//            return false;
//        }
//    }
//
//    /**
//     * 处理推送消息
//     */
//    private boolean processPushMessage(JSONObject data) {
//        try {
//            log.info("Sending push to device: {}, title: {}",
//                data.getString("deviceId"), data.getString("title"));
//
//            // 模拟发送推送
//            Thread.sleep(100);
//
//            return true;
//        } catch (Exception e) {
//            log.error("Error sending push", e);
//            return false;
//        }
//    }
//}