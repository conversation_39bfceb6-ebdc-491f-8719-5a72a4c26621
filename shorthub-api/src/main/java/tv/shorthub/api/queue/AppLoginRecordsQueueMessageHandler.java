package tv.shorthub.api.queue;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tv.shorthub.api.service.BalanceOf;
import tv.shorthub.api.service.recharge.RechargeService;
import tv.shorthub.common.queue.QueueMessageHandler;
import tv.shorthub.system.domain.*;
import tv.shorthub.system.service.*;

import java.util.Date;
import java.util.List;

/**
 * 推广链接访问消息队列消息处理
 */
@Component
@Slf4j
public class AppLoginRecordsQueueMessageHandler implements QueueMessageHandler {

    @Autowired
    IAppLoginRecordsService appLoginRecordsService;

    @Autowired
    IAppOrderInfoService appOrderInfoService;

    @Autowired
    IAppUserWatchRecordsService appUserWatchRecordsService;

    @Autowired
    IAppUserCollectService appUserCollectService;

    @Autowired
    IAppConsumptionService appConsumptionService;

    @Autowired
    IAppUserMemberService appUserMemberService;

    @Autowired
    BalanceOf balanceOf;

    @Autowired
    RechargeService rechargeService;

    @Autowired
    IPaypalPaymentLogService paypalPaymentLogService;

    @Autowired
    IPayermaxPaymentLogService payermaxPaymentLogService;

    @Autowired
    IAppUsersService appUsersService;

    @Override
    public boolean handleMessage(JSONObject message) {
        log.info("handler login records : {}", message);
        AppLoginRecords appLoginRecords = new AppLoginRecords();
        appLoginRecords.setTfid(message.getString("tfid"));
        appLoginRecords.setUserId(message.getString("userId"));
        appLoginRecords.setIpAddress(message.getString("ipAddress"));
        appLoginRecords.setLocationCountry(message.getString("locationCountry"));
        appLoginRecords.setLoginTime(message.getDate("loginTime"));
        appLoginRecords.setCreateTime(message.getDate("createTime"));
        appLoginRecordsService.insert(appLoginRecords);

        // 登录完成之后，更新用户在登录前的订单数据、观看记录、收藏数据、充值记录，并充值已支付订单到余额
        String userId = message.getString("userId");
        String tempUserId = "sid-" + message.getString("session-id");

        log.info("正在处理临时用户登录后的数据，临时用户ID：{}，用户ID：{}", tempUserId, userId);

        // 1. 处理临时用户的订单数据
        QueryWrapper<AppOrderInfo> orderQuery = new QueryWrapper<>();
        orderQuery.eq("user_id", tempUserId)
                .or()
                .apply("JSON_EXTRACT(extend_json, '$.clientIp') = {0}", message.getString("ipAddress"))
                .or()
                .apply("sid = {0}", message.getString("session-id"))
//                    .eq("order_status", 1)
            ;
        if (StringUtils.isNotEmpty(message.getString("tfid"))) {
            orderQuery.eq("tfid", message.getString("tfid"));
        }
        // 所有订单
        List<AppOrderInfo> tempOrders = appOrderInfoService.getMapper().selectList(orderQuery);
        log.info("找到{}个已支付的订单", tempOrders.size());

        for (AppOrderInfo order : tempOrders) {
           // 判断当前订单所属用户是否是临时用户
           AppUsers orderAppUsers = appUsersService.getMapper().selectOne(new QueryWrapper<AppUsers>().eq("user_id", order.getUserId()));
           if (orderAppUsers == null) {
               log.info("订单对应的用户信息不存在: {}", order.getOrderNo());
               continue;
           }

            if (!orderAppUsers.getProvider().equals("temp")) {
                log.info("订单所属用户不是临时用户，不需要重复充值：{}, {}", orderAppUsers.getUserId(), orderAppUsers.getProvider());
                continue;
            }

            // 更新订单用户ID
            order.setUserId(userId);
            appOrderInfoService.update(order);

            if (order.getOrderStatus().compareTo(1L) != 0) {
                log.info("未支付订单, 不需要进行充值: {}", order.getOrderNo());
                continue;
            }

            // 撤销临时用户充值
            orderAppUsers.setBalanceCoin(0L);
            orderAppUsers.setBalanceBonus(0L);
            appUsersService.update(orderAppUsers);
            log.info("撤销临时用户充值：{}", orderAppUsers.getUserId());

            // 使用RechargeService处理已支付的订单
            try {
                log.info("正在处理订单：{}", order.getOrderNo());
                rechargeService.doSuccess(order);
                log.info("订单处理成功：{}", order.getOrderNo());
            } catch (Exception e) {
                log.error("处理订单失败: orderNo={}, error={}", order.getOrderNo(), e.getMessage());
            }
        }

        int size;
        if (message.containsKey("newUser") && message.getBooleanValue("newUser")) {

            UpdateWrapper<AppUserWatchRecords> watchQuery = new UpdateWrapper<>();
            watchQuery.eq("user_id", tempUserId);
            watchQuery.set("user_id", userId);
            size = appUserWatchRecordsService.getMapper().update(watchQuery);
            log.info("更新{}个观看记录", size);

            // 3. 处理临时用户的收藏数据
            UpdateWrapper<AppUserCollect> collectQuery = new UpdateWrapper<>();
            collectQuery.eq("user_id", tempUserId);
            collectQuery.set("user_id", userId);
            size = appUserCollectService.getMapper().update(collectQuery);
            log.info("更新{}个收藏记录", size);

            // 4. 处理临时用户的消费记录
            UpdateWrapper<AppConsumption> consumptionQuery = new UpdateWrapper<>();
            consumptionQuery.eq("user_id", tempUserId);
            consumptionQuery.set("user_id", userId);
            size = appConsumptionService.getMapper().update(consumptionQuery);
            log.info("更新{}个消费记录", size);
        }


        // 5. 处理临时用户的会员记录
        UpdateWrapper<AppUserMember> memberQuery = new UpdateWrapper<>();
        memberQuery.eq("user_id", tempUserId);
        memberQuery.set("user_id", userId);
        size = appUserMemberService.getMapper().update(memberQuery);
        log.info("更新{}个会员记录", size);

        // 6. 处理临时用户的paypal日志
        UpdateWrapper<PaypalPaymentLog> paypalLogQuery = new UpdateWrapper<>();
        paypalLogQuery.eq("related_user_id", tempUserId);
        paypalLogQuery.set("related_user_id", userId);
        size = paypalPaymentLogService.getMapper().update(paypalLogQuery);
        log.info("更新{}个paypal日志", size);

        // 7. 处理临时用户的payermax日志
        UpdateWrapper<PayermaxPaymentLog> payermaxLogQuery = new UpdateWrapper<>();
        payermaxLogQuery.eq("related_user_id", tempUserId);
        payermaxLogQuery.set("related_user_id", userId);
        size = payermaxPaymentLogService.getMapper().update(payermaxLogQuery);
        log.info("更新{}个payermax日志", size);
        return true;
    }
}
