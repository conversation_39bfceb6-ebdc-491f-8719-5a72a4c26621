package tv.shorthub.api.queue;

import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tv.shorthub.ad.domain.FacebookAttributionData;
import tv.shorthub.system.utils.AttributionCacheHolder;
import tv.shorthub.common.enums.AdPlatformEnum;
import tv.shorthub.common.queue.QueueMessageHandler;
import tv.shorthub.system.domain.AppPromotionRequestLog;
import tv.shorthub.system.service.IAppPromotionRequestLogService;

import java.util.Date;

/**
 * 推广链接访问消息队列消息处理
 */
@Component
@Slf4j
public class PromotionMessageQueueMessageHandler implements QueueMessageHandler {

    @Autowired
    IAppPromotionRequestLogService appPromotionRequestLogService;

    @Autowired
    AttributionCacheHolder attributionCacheHolder;

    @Override
    public boolean handleMessage(JSONObject message) {
        log.info("Processing promotion message: tid={}, sid={}",
            message.getString("tid"), message.getString("sid"));

        try {
            // 1. 处理Facebook归因数据
            FacebookAttributionData facebookAttribution = processFacebookAttribution(message);

            JSONObject cacheAttribution = attributionCacheHolder.getAttribution(message.getString("deviceId"), message.getString("ip"), message.getString("tid"));
            if (null == cacheAttribution) {
                cacheAttribution = new JSONObject();
            }

            cacheAttribution.put("tid", message.getString("tid"));
            cacheAttribution.put("deviceId", message.getString("deviceId"));
            cacheAttribution.put("appid", message.getString("appid"));

            // 2. 构建日志记录
            JSONObject extendJson = message.getJSONObject("extendJson");
            if (null == extendJson) {
                extendJson = new JSONObject();
            }
            AppPromotionRequestLog requestLog = new AppPromotionRequestLog();
            requestLog.setDeviceId(message.getString("deviceId"));
            requestLog.setAppid(message.getString("appid"));
            requestLog.setTid(message.getString("tid"));
            requestLog.setIp(message.getString("ip"));
            requestLog.setSid(message.getString("sid"));
            requestLog.setExtendJson(extendJson);
            requestLog.setUserAgent(message.getString("userAgent"));
            requestLog.setHeaders(message.getJSONObject("headers"));
            requestLog.setReqParams(message.getJSONObject("params"));
            requestLog.setBody(message.getString("body"));
            requestLog.setCreateTime(new Date());

            // 3. 将Facebook归因数据添加到请求参数中
            if (facebookAttribution != null) {
                JSONObject enrichedParams = requestLog.getReqParams() != null ?
                    requestLog.getReqParams() : new JSONObject();

                // 添加Facebook归因字段
                if (facebookAttribution.getFbc() != null) {
                    enrichedParams.put("fbc", facebookAttribution.getFbc());
                }
                if (facebookAttribution.getFbp() != null) {
                    enrichedParams.put("fbp", facebookAttribution.getFbp());
                }
                if (facebookAttribution.getFbclid() != null) {
                    enrichedParams.put("fbclid", facebookAttribution.getFbclid());
                }

                enrichedParams.put("isAdTraffic", facebookAttribution.isAdTraffic());
                enrichedParams.put("attributionTimestamp", facebookAttribution.getAttributionTimestamp());

                requestLog.setReqParams(enrichedParams);

                log.info("Facebook attribution processed - fbc: {}, fbp: {}, fbclid: {}, isAdTraffic: {}",
                    facebookAttribution.getFbc(), facebookAttribution.getFbp(),
                    facebookAttribution.getFbclid(), facebookAttribution.isAdTraffic());

                cacheAttribution.put(AdPlatformEnum.Facebook.getValue(), enrichedParams);
            }

            // 4. 设置处理状态
            requestLog.setState("PROCESSED");

            // 5. 保存到数据库
            appPromotionRequestLogService.getMapper().insert(requestLog);

            // 设置归因缓存
            cacheAttribution.put("attribution_id", requestLog.getId());
            attributionCacheHolder.addAttribution(requestLog.getDeviceId(), requestLog.getIp(), message.getString("tid"), cacheAttribution);

            log.info("Promotion request log saved successfully for tid: {}", requestLog.getTid());
            return true;

        } catch (Exception e) {
            log.error("Failed to handle promotion message: {}", message, e);
            // 保存错误日志
            saveErrorLog(message, e);
            return false;
        }
    }

    /**
     * 处理Facebook归因数据
     */
    private FacebookAttributionData processFacebookAttribution(JSONObject message) {
        try {
            JSONObject params = message.getJSONObject("params");
            JSONObject headers = message.getJSONObject("headers");

            if (params == null) {
                return null;
            }

            FacebookAttributionData attribution = new FacebookAttributionData();
            attribution.setAttributionTimestamp(message.getLong("timestamp"));
            attribution.setTfid(message.getString("tid"));
            attribution.setSessionId(message.getString("sid"));
            attribution.setClientIp(message.getString("ip"));
            attribution.setAdTraffic(message.getBoolean("isAdTraffic"));

            // 1. 处理fbclid参数
            String fbclid = params.getString("fbclid");
            if (fbclid != null && !fbclid.trim().isEmpty()) {
                attribution.setFbclid(fbclid);
                attribution.setAdTraffic(true);

                // 生成fbc值（模拟前端设置的格式）
                String fbc = generateFbcValue(fbclid, attribution.getAttributionTimestamp());
                attribution.setFbc(fbc);

                log.debug("Processed fbclid: {}, generated fbc: {}", fbclid, fbc);
            } else {
                // 尝试从Cookie header中获取fbc
                String cookieHeader = headers.getString("cookie");
                if (cookieHeader != null) {
                    String fbc = extractCookieValue(cookieHeader, "_fbc");
                    if (fbc != null) {
                        attribution.setFbc(fbc);
                        // 从fbc中提取fbclid
                        String extractedFbclid = extractFbclidFromFbc(fbc);
                        if (extractedFbclid != null) {
                            attribution.setFbclid(extractedFbclid);
                            attribution.setAdTraffic(true);
                        }
                    }
                }
            }

            // 2. 处理fbp参数
            String cookieHeader = headers.getString("cookie");
            if (cookieHeader != null) {
                String fbp = extractCookieValue(cookieHeader, "_fbp");
                if (fbp != null) {
                    attribution.setFbp(fbp);
                } else {
                    // 生成新的fbp
                    String newFbp = generateFbpValue(attribution.getAttributionTimestamp());
                    attribution.setFbp(newFbp);
                    log.debug("Generated new fbp: {}", newFbp);
                }
            }

            return attribution;

        } catch (Exception e) {
            log.error("Error processing Facebook attribution", e);
            return null;
        }
    }

    /**
     * 生成fbc值
     */
    private String generateFbcValue(String fbclid, long timestamp) {
        // 格式: fb.subdomainIndex.creationTime.fbclid
        return String.format("fb.1.%d.%s", timestamp, fbclid);
    }

    /**
     * 生成fbp值
     */
    private String generateFbpValue(long timestamp) {
        // 格式: fb.subdomainIndex.creationTime.randomNumber
        long randomNumber = (long) (Math.random() * 10000000000L);
        return String.format("fb.1.%d.%d", timestamp, randomNumber);
    }

    /**
     * 从Cookie字符串中提取指定cookie的值
     */
    private String extractCookieValue(String cookieHeader, String cookieName) {
        if (cookieHeader == null || cookieName == null) {
            return null;
        }

        String[] cookies = cookieHeader.split(";");
        for (String cookie : cookies) {
            String trimmedCookie = cookie.trim();
            if (trimmedCookie.startsWith(cookieName + "=")) {
                return trimmedCookie.substring(cookieName.length() + 1);
            }
        }
        return null;
    }

    /**
     * 从fbc值中提取fbclid
     */
    private String extractFbclidFromFbc(String fbc) {
        if (fbc == null) {
            return null;
        }

        try {
            // fbc格式: fb.subdomainIndex.creationTime.fbclid
            String[] parts = fbc.split("\\.");
            if (parts.length >= 4) {
                // fbclid是第4部分及之后的所有部分（因为fbclid本身可能包含点号）
                StringBuilder fbclid = new StringBuilder();
                for (int i = 3; i < parts.length; i++) {
                    if (i > 3) {
                        fbclid.append(".");
                    }
                    fbclid.append(parts[i]);
                }
                return fbclid.toString();
            }
        } catch (Exception e) {
            log.warn("Failed to extract fbclid from fbc: {}", fbc, e);
        }

        return null;
    }

    /**
     * 保存错误日志
     */
    private void saveErrorLog(JSONObject message, Exception e) {
        try {
            AppPromotionRequestLog errorLog = new AppPromotionRequestLog();
            errorLog.setAppid(message.getString("appid"));
            errorLog.setTid(message.getString("tid"));
            errorLog.setIp(message.getString("ip"));
            errorLog.setSid(message.getString("sid"));
            errorLog.setHeaders(message.getJSONObject("headers"));
            errorLog.setReqParams(message.getJSONObject("params"));
            errorLog.setBody(message.getString("body"));
            errorLog.setState("ERROR: " + e.getMessage().substring(0, 20));
            errorLog.setCreateTime(new Date());

            appPromotionRequestLogService.getMapper().insert(errorLog);
        } catch (Exception saveError) {
            log.error("Failed to save error log", saveError);
        }
    }
}
