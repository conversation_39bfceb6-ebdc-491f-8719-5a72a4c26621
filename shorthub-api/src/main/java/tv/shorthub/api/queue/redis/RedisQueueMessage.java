package tv.shorthub.api.queue.redis;

import com.alibaba.fastjson2.JSONObject;

import java.time.Instant;
import java.util.UUID;

/**
 * 队列事件消息
 */
public class RedisQueueMessage {

    private String id;
    private JSONObject data;
    private long timestamp;
    private int retryCount;
    private String queueName;

    public RedisQueueMessage() {
        this.id = UUID.randomUUID().toString();
        this.timestamp = Instant.now().toEpochMilli();
        this.retryCount = 0;
    }

    public RedisQueueMessage(String queueName, JSONObject data) {
        this();
        this.queueName = queueName;
        this.data = data;
    }

    // Getters and Setters
    public String getId() { return id; }
    public void setId(String id) { this.id = id; }

    public JSONObject getData() { return data; }
    public void setData(JSONObject data) { this.data = data; }

    public long getTimestamp() { return timestamp; }
    public void setTimestamp(long timestamp) { this.timestamp = timestamp; }

    public int getRetryCount() { return retryCount; }
    public void setRetryCount(int retryCount) { this.retryCount = retryCount; }

    public String getQueueName() { return queueName; }
    public void setQueueName(String queueName) { this.queueName = queueName; }

    public void incrementRetryCount() { this.retryCount++; }

}
