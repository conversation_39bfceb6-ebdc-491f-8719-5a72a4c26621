package tv.shorthub.api.queue;

import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import tv.shorthub.common.enums.CloudflareQueueEnum;
import tv.shorthub.common.queue.CloudflareQueueService;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicReference;

@Slf4j
@Component
public class QueueFastJoin {

    public static final Map<CloudflareQueueEnum, List<JSONObject>> queueMap = new ConcurrentHashMap<>();

    @Autowired
    private CloudflareQueueService cloudflareQueueService;

    /**
     * 添加数据到队列
     * @param queueType 队列类型
     * @param data 要添加的数据
     */
    public void addToQueue(CloudflareQueueEnum queueType, JSONObject data) {
        queueMap.computeIfAbsent(queueType, k -> new ArrayList<>()).add(data);
        log.info("Added data to queue {}: {}", queueType.getValue(), data);
    }

    /**
     * 每秒执行一次，将收集的数据发送到Cloudflare队列
     */
    @Async
    @Scheduled(fixedRate = 1000)
    public void processQueue() {
        try {
            queueMap.forEach((queueType, dataList) -> {
                if (dataList.isEmpty()) {
                    return;
                }

                // 使用原子引用确保线程安全
                AtomicReference<List<JSONObject>> batchRef = new AtomicReference<>();
                List<JSONObject> currentBatch = new ArrayList<>(dataList);
                dataList.clear();
                batchRef.set(currentBatch);

                // 批量发送数据
                for (JSONObject data : batchRef.get()) {
                    try {
                        cloudflareQueueService.sendMessage(queueType.getValue(), data);
                        log.info("Successfully sent data to queue {}: {}", queueType.getValue(), data);
                    } catch (Exception e) {
                        log.error("Failed to send data to queue {}: {}", queueType.getValue(), data, e);
                        // 发送失败时，将数据重新添加到队列
                        dataList.add(data);
                    }
                }
            });
        } catch (Exception e) {
            log.error("Error processing queue: {}", e.getMessage(), e);
        }
    }
}
