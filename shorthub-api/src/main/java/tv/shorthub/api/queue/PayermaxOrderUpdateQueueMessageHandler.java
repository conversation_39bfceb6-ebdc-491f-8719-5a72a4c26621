package tv.shorthub.api.queue;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tv.shorthub.api.service.recharge.RechargeService;
import tv.shorthub.common.queue.QueueMessageHandler;
import tv.shorthub.payermax.service.PayermaxOrderService;
import tv.shorthub.payermax.service.PayermaxSubscribeService;
import tv.shorthub.paypal.constant.PayPalConstants;
import tv.shorthub.system.domain.AppOrderInfo;
import tv.shorthub.system.domain.PayermaxPaymentLog;
import tv.shorthub.system.domain.PaypalPaymentLog;
import tv.shorthub.system.domain.PaypalSubscriptionRenewal;
import tv.shorthub.system.service.IAppOrderInfoService;
import tv.shorthub.system.service.IPayermaxPaymentLogService;
import tv.shorthub.system.service.IPaypalPaymentLogService;
import tv.shorthub.system.service.IPaypalSubscriptionRenewalService;

import java.text.ParseException;
import java.util.Date;

@Component
@Slf4j
public class PayermaxOrderUpdateQueueMessageHandler implements QueueMessageHandler {

    @Autowired
    IPayermaxPaymentLogService payermaxPaymentLogService;

    @Autowired
    IAppOrderInfoService appOrderInfoService;

    @Autowired
    RechargeService rechargeService;

    @Autowired
    PayermaxSubscribeService payermaxSubscribeService;

    @Autowired
    PayermaxOrderService payermaxOrderService;
    @Override
    public boolean handleMessage(JSONObject message) {
        log.info("handler payermax update message: {}", message);
        try {
            String orderNo = message.getString("orderNo");
            executePayment(orderNo);
            return true;
        } catch (Exception e) {
            log.error("handle payermax update message error: {}", e.getMessage(), e);
        }
        return false;
    }

    public boolean executePayment(String orderNo) {
        AppOrderInfo orderInfo = appOrderInfoService.getMapper().selectOne(new QueryWrapper<AppOrderInfo>().eq("order_no", orderNo));
        if (null == orderInfo) {
            log.info("要履行的订单不存在. {}", orderNo);
            return true;
        }
        PayermaxPaymentLog paymentLog = payermaxPaymentLogService.getMapper().selectOne(new QueryWrapper<PayermaxPaymentLog>().eq("order_no", orderNo));
        // 充值履行
        rechargeFulfill(orderInfo, paymentLog);
        return true;
    }

    public void rechargeFulfill(AppOrderInfo orderInfo, PayermaxPaymentLog payermaxPaymentLog) {

        if (orderInfo.getOrderStatus().compareTo(1L) == 0) {
            log.info("订单已履行. {}", orderInfo.getOrderNo());
            return;
        }

        log.info("处理充值履行逻辑: {}", orderInfo.getOrderNo());

        // 订阅支付
        if (orderInfo.getPayType().equals("2")) {
            handleSubscriptionPayment(orderInfo, payermaxPaymentLog);
            return;
        }

        // 普通支付
        JSONObject rawData = payermaxOrderService.orderQuery(orderInfo.getOrderNo());
        if (null == rawData) {
            log.info("没有查询到payermax订单结果: {}", orderInfo.getOrderNo());
            return;
        }

        log.info("payermax普通支付结果: {}", rawData);
        payermaxPaymentLog.setDetailRawData(rawData);
        JSONObject data = rawData.getJSONObject("data");
        String status = data.getString("status");
        payermaxPaymentLog.setStatus(status);
        log.info("支付状态: {}", status);
        if ("SUCCESS".equals(status)) {
            Date payTime = data.getDate("completeTime");
            rechargeService.success(orderInfo.getOrderNo(), payTime);
            log.info("payermax 充值成功: {}", orderInfo.getOrderNo());
        }
        payermaxPaymentLogService.getMapper().updateById(payermaxPaymentLog);

    }

    private void handleSubscriptionPayment(AppOrderInfo orderInfo, PayermaxPaymentLog payermaxPaymentLog) {
        // 检查订阅支付结果
        JSONObject rawData = payermaxSubscribeService.subscriptionQuery(orderInfo.getOrderNo());
        if (null == rawData) {
            log.info("没有查询到payermax订阅结果: {}", orderInfo.getOrderNo());
            return;
        }
        log.info("payermax订阅结果: {}", rawData);
        payermaxPaymentLog.setDetailRawData(rawData);
        JSONObject data = rawData.getJSONObject("data");
        JSONObject subscriptionPaymentDetails = data.getJSONArray("subscriptionPaymentDetails").getJSONObject(0);
        String paymentStatus = subscriptionPaymentDetails.getString("paymentStatus");
        payermaxPaymentLog.setStatus(paymentStatus);
        log.info("支付状态: {}", paymentStatus);
        if ("SUCCESS".equals(paymentStatus)) {
            Date payTime = subscriptionPaymentDetails.getJSONObject("lastPaymentInfo").getDate("payTime");
            rechargeService.success(orderInfo.getOrderNo(), payTime);
            log.info("payermax 订阅成功: {}", orderInfo.getOrderNo());
        }
        payermaxPaymentLogService.getMapper().updateById(payermaxPaymentLog);

    }
}
