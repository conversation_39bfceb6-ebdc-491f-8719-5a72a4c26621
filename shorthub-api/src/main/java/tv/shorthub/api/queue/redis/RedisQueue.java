package tv.shorthub.api.queue.redis;

import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.stereotype.Component;
import tv.shorthub.common.core.redis.RedisCache;

import java.time.Instant;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * Redis队列服务 - 简化版本
 * 特性：
 * 1. 线程安全
 * 2. 消息确认机制
 * 3. 支持JSONObject格式
 * 4. 支持延迟队列
 * 5. 支持死信队列
 */
@Component
@Slf4j
public class RedisQueue {
    
    @Autowired
    private RedisCache redisCache;
    
    // 队列key前缀
    private static final String QUEUE_PREFIX = "queue:";
    // 处理中队列key前缀
    private static final String PROCESSING_PREFIX = "processing:";
    // 死信队列key前缀
    private static final String DEAD_LETTER_PREFIX = "dead:";
    // 延迟队列key前缀
    private static final String DELAY_PREFIX = "delay:";
    // 消息元数据key前缀
    private static final String MESSAGE_META_PREFIX = "meta:";
    
    // 默认消息超时时间（秒）
    private static final int DEFAULT_MESSAGE_TIMEOUT = 300;
    // 默认最大重试次数
    private static final int DEFAULT_MAX_RETRIES = 3;
    
    /**
     * 发布消息到队列
     * @param queueName 队列名称
     * @param data 消息数据
     * @return 消息ID
     */
    public String publish(String queueName, JSONObject data) {
        RedisQueueMessage message = new RedisQueueMessage(queueName, data);
        
        try {
            // 存储消息到队列
            redisCache.redisTemplate.opsForList().rightPush(QUEUE_PREFIX + queueName, message);
            
            // 存储消息元数据
            redisCache.redisTemplate.opsForHash().put(MESSAGE_META_PREFIX + queueName, 
                message.getId(), message);
            
            log.info("Published message to queue [{}], messageId: {}", queueName, message.getId());
            return message.getId();
            
        } catch (Exception e) {
            log.error("Failed to publish message to queue [{}]", queueName, e);
            throw new RuntimeException("Failed to publish message", e);
        }
    }
    
    /**
     * 发布延迟消息
     * @param queueName 队列名称
     * @param data 消息数据
     * @param delaySeconds 延迟秒数
     * @return 消息ID
     */
    public String publishDelay(String queueName, JSONObject data, long delaySeconds) {
        RedisQueueMessage message = new RedisQueueMessage(queueName, data);
        
        try {
            long executeTime = Instant.now().toEpochMilli() + delaySeconds * 1000;
            
            // 存储到延迟队列（使用sorted set，以执行时间为分数）
            redisCache.redisTemplate.opsForZSet().add(DELAY_PREFIX + queueName, message, executeTime);
            
            // 存储消息元数据
            redisCache.redisTemplate.opsForHash().put(MESSAGE_META_PREFIX + queueName, 
                message.getId(), message);
            
            log.info("Published delay message to queue [{}], messageId: {}, delaySeconds: {}", 
                queueName, message.getId(), delaySeconds);
            return message.getId();
            
        } catch (Exception e) {
            log.error("Failed to publish delay message to queue [{}]", queueName, e);
            throw new RuntimeException("Failed to publish delay message", e);
        }
    }
    
    /**
     * 消费消息（阻塞）
     * @param queueName 队列名称
     * @param timeoutSeconds 超时时间（秒）
     * @return 消息，如果超时返回null
     */
    public RedisQueueMessage consume(String queueName, int timeoutSeconds) {
        try {
            // 先处理延迟队列
            processDelayQueue(queueName);
            
            // 阻塞弹出消息
            Object result = redisCache.redisTemplate.opsForList().leftPop(QUEUE_PREFIX + queueName, 
                timeoutSeconds, TimeUnit.SECONDS);
            
            if (result == null) {
                return null;
            }
            
            RedisQueueMessage message = (RedisQueueMessage) result;
            
            // 移动到处理中队列
            redisCache.redisTemplate.opsForHash().put(PROCESSING_PREFIX + queueName, 
                message.getId(), message);
            
            // 设置消息超时
            redisCache.redisTemplate.expire(PROCESSING_PREFIX + queueName, DEFAULT_MESSAGE_TIMEOUT, TimeUnit.SECONDS);
            
            log.info("Consumed message from queue [{}], messageId: {}", queueName, message.getId());
            return message;
            
        } catch (Exception e) {
            log.error("Failed to consume message from queue [{}]", queueName, e);
            throw new RuntimeException("Failed to consume message", e);
        }
    }
    
    /**
     * 非阻塞消费消息
     * @param queueName 队列名称
     * @return 消息，如果没有消息返回null
     */
    public RedisQueueMessage consumeNoWait(String queueName) {
        try {
            // 先处理延迟队列
            processDelayQueue(queueName);
            
            // 非阻塞弹出消息
            Object result = redisCache.redisTemplate.opsForList().leftPop(QUEUE_PREFIX + queueName);
            
            if (result == null) {
                return null;
            }
            
            RedisQueueMessage message = (RedisQueueMessage) result;
            
            // 移动到处理中队列
            redisCache.redisTemplate.opsForHash().put(PROCESSING_PREFIX + queueName, 
                message.getId(), message);
            
            // 设置消息超时
            redisCache.redisTemplate.expire(PROCESSING_PREFIX + queueName, DEFAULT_MESSAGE_TIMEOUT, TimeUnit.SECONDS);
            
            log.info("Consumed message from queue [{}], messageId: {}", queueName, message.getId());
            return message;
            
        } catch (Exception e) {
            log.error("Failed to consume message from queue [{}]", queueName, e);
            throw new RuntimeException("Failed to consume message", e);
        }
    }
    
    /**
     * 确认消息处理完成
     * @param queueName 队列名称
     * @param messageId 消息ID
     * @return 是否成功
     */
    public boolean ack(String queueName, String messageId) {
        try {
            // 从处理中队列移除
            redisCache.redisTemplate.opsForHash().delete(PROCESSING_PREFIX + queueName, messageId);
            
            // 从元数据中移除
            redisCache.redisTemplate.opsForHash().delete(MESSAGE_META_PREFIX + queueName, messageId);
            
            log.info("Acknowledged message in queue [{}], messageId: {}", queueName, messageId);
            return true;
            
        } catch (Exception e) {
            log.error("Failed to acknowledge message in queue [{}], messageId: {}", queueName, messageId, e);
            return false;
        }
    }
    
    /**
     * 拒绝消息（重新入队或进入死信队列）
     * @param queueName 队列名称
     * @param messageId 消息ID
     * @param requeue 是否重新入队
     * @return 是否成功
     */
    public boolean nack(String queueName, String messageId, boolean requeue) {
        try {
            // 从处理中队列获取消息
            RedisQueueMessage message = (RedisQueueMessage) redisCache.redisTemplate.opsForHash()
                .get(PROCESSING_PREFIX + queueName, messageId);
            
            if (message == null) {
                log.warn("Message not found in processing queue [{}], messageId: {}", queueName, messageId);
                return false;
            }
            
            // 从处理中队列移除
            redisCache.redisTemplate.opsForHash().delete(PROCESSING_PREFIX + queueName, messageId);
            
            if (requeue && message.getRetryCount() < DEFAULT_MAX_RETRIES) {
                // 重新入队
                message.incrementRetryCount();
                redisCache.redisTemplate.opsForList().rightPush(QUEUE_PREFIX + queueName, message);
                
                // 更新元数据
                redisCache.redisTemplate.opsForHash().put(MESSAGE_META_PREFIX + queueName, 
                    message.getId(), message);
                
                log.info("Requeued message in queue [{}], messageId: {}, retryCount: {}", 
                    queueName, messageId, message.getRetryCount());
            } else {
                // 移到死信队列
                redisCache.redisTemplate.opsForList().rightPush(DEAD_LETTER_PREFIX + queueName, message);
                
                // 从元数据中移除
                redisCache.redisTemplate.opsForHash().delete(MESSAGE_META_PREFIX + queueName, messageId);
                
                log.info("Moved message to dead letter queue [{}], messageId: {}", queueName, messageId);
            }
            
            return true;
            
        } catch (Exception e) {
            log.error("Failed to nack message in queue [{}], messageId: {}", queueName, messageId, e);
            return false;
        }
    }
    
    /**
     * 处理延迟队列
     * @param queueName 队列名称
     */
    private void processDelayQueue(String queueName) {
        try {
            long currentTime = Instant.now().toEpochMilli();
            
            // 获取到期的延迟消息
            Set<Object> expiredMessages = redisCache.redisTemplate.opsForZSet()
                .rangeByScore(DELAY_PREFIX + queueName, 0, currentTime);
            
            if (expiredMessages != null && !expiredMessages.isEmpty()) {
                for (Object messageObj : expiredMessages) {
                    RedisQueueMessage message = (RedisQueueMessage) messageObj;
                    
                    // 从延迟队列移除
                    redisCache.redisTemplate.opsForZSet().remove(DELAY_PREFIX + queueName, message);
                    
                    // 加入正常队列
                    redisCache.redisTemplate.opsForList().rightPush(QUEUE_PREFIX + queueName, message);
                    
                    log.info("Moved delay message to normal queue [{}], messageId: {}", 
                        queueName, message.getId());
                }
            }
            
        } catch (Exception e) {
            log.error("Failed to process delay queue [{}]", queueName, e);
        }
    }
    
    /**
     * 获取队列长度
     * @param queueName 队列名称
     * @return 队列长度
     */
    public long getQueueSize(String queueName) {
        try {
            Long size = redisCache.redisTemplate.opsForList().size(QUEUE_PREFIX + queueName);
            return size != null ? size : 0;
        } catch (Exception e) {
            log.error("Failed to get queue size [{}]", queueName, e);
            return 0;
        }
    }
    
    /**
     * 获取处理中队列长度
     * @param queueName 队列名称
     * @return 处理中队列长度
     */
    public long getProcessingSize(String queueName) {
        try {
            Long size = redisCache.redisTemplate.opsForHash().size(PROCESSING_PREFIX + queueName);
            return size != null ? size : 0;
        } catch (Exception e) {
            log.error("Failed to get processing queue size [{}]", queueName, e);
            return 0;
        }
    }
    
    /**
     * 获取死信队列长度
     * @param queueName 队列名称
     * @return 死信队列长度
     */
    public long getDeadLetterSize(String queueName) {
        try {
            Long size = redisCache.redisTemplate.opsForList().size(DEAD_LETTER_PREFIX + queueName);
            return size != null ? size : 0;
        } catch (Exception e) {
            log.error("Failed to get dead letter queue size [{}]", queueName, e);
            return 0;
        }
    }
    
    /**
     * 获取延迟队列长度
     * @param queueName 队列名称
     * @return 延迟队列长度
     */
    public long getDelaySize(String queueName) {
        try {
            Long size = redisCache.redisTemplate.opsForZSet().size(DELAY_PREFIX + queueName);
            return size != null ? size : 0;
        } catch (Exception e) {
            log.error("Failed to get delay queue size [{}]", queueName, e);
            return 0;
        }
    }
    
    /**
     * 清空队列
     * @param queueName 队列名称
     * @return 是否成功
     */
    public boolean clearQueue(String queueName) {
        try {
            redisCache.redisTemplate.delete(QUEUE_PREFIX + queueName);
            redisCache.redisTemplate.delete(PROCESSING_PREFIX + queueName);
            redisCache.redisTemplate.delete(DEAD_LETTER_PREFIX + queueName);
            redisCache.redisTemplate.delete(DELAY_PREFIX + queueName);
            redisCache.redisTemplate.delete(MESSAGE_META_PREFIX + queueName);
            
            log.info("Cleared queue [{}]", queueName);
            return true;
            
        } catch (Exception e) {
            log.error("Failed to clear queue [{}]", queueName, e);
            return false;
        }
    }
    
    /**
     * 获取队列统计信息
     * @param queueName 队列名称
     * @return 统计信息
     */
    public JSONObject getQueueStats(String queueName) {
        JSONObject stats = new JSONObject();
        
        try {
            stats.put("queueName", queueName);
            stats.put("queueSize", getQueueSize(queueName));
            stats.put("processingSize", getProcessingSize(queueName));
            stats.put("deadLetterSize", getDeadLetterSize(queueName));
            stats.put("delaySize", getDelaySize(queueName));
            stats.put("timestamp", Instant.now().toEpochMilli());
            
        } catch (Exception e) {
            log.error("Failed to get queue stats [{}]", queueName, e);
        }
        
        return stats;
    }
}
