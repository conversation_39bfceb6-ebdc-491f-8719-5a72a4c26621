package tv.shorthub.api.queue;

import cn.hutool.core.thread.ThreadUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import lombok.extern.slf4j.Slf4j;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import tv.shorthub.airwallex.service.AirwallexConfigStorageHolder;
import tv.shorthub.airwallex.service.AirwallexPaymentConsentService;
import tv.shorthub.airwallex.service.AirwallexPaymentService;
import tv.shorthub.api.service.PayPalCallbackExecutePayment;
import tv.shorthub.api.service.recharge.RechargeService;
import tv.shorthub.common.enums.OrderChannelEnums;
import tv.shorthub.common.queue.QueueMessageHandler;
import tv.shorthub.common.utils.StringUtils;
import tv.shorthub.crypto.service.impl.CryptoPaymentService;
import tv.shorthub.paypal.config.PaypalConfigStorageHolder;
import tv.shorthub.paypal.service.PayPalPaymentService;
import tv.shorthub.paypal.service.PayPalSubscriptionService;
import tv.shorthub.system.domain.AirwallexPaymentLog;
import tv.shorthub.system.domain.AppOrderInfo;
import tv.shorthub.system.domain.CryptoDepositRecord;
import tv.shorthub.system.domain.PaypalPaymentLog;
import tv.shorthub.system.mapper.AirwallexPaymentLogMapper;
import tv.shorthub.system.mapper.CryptoDepositRecordMapper;
import tv.shorthub.system.service.IAppOrderInfoService;
import tv.shorthub.system.service.IPayermaxPaymentLogService;
import tv.shorthub.system.service.IPaypalPaymentLogService;

@Component
@Slf4j
public class OrderListenQueryMessageHandler implements QueueMessageHandler {

    @Autowired
    IAppOrderInfoService appOrderInfoService;

    @Autowired
    IPaypalPaymentLogService paypalPaymentLogService;

    @Autowired
    PayPalPaymentService payPalPaymentService;

    
    @Autowired
    PayPalCallbackExecutePayment paymentCallback;

    @Autowired
    PayermaxOrderUpdateQueueMessageHandler payermaxOrderUpdateQueueMessageHandler;

    @Autowired
    AirwallexPaymentService airwallexPaymentService;

    @Autowired
    AirwallexPaymentConsentService airwallexPaymentConsentService;

    @Autowired
    AirwallexPaymentLogMapper airwallexPaymentLogMapper;

    @Autowired
    RechargeService rechargeService;

    @Autowired
    CryptoPaymentService cryptoPaymentService;

    @Autowired
    CryptoDepositRecordMapper cryptoDepositRecordMapper;

    @Override
    public boolean handleMessage(JSONObject message) {
        log.info("handler paypal listen query message: {}", message);
        String orderNo = message.getString("orderNo");
        AppOrderInfo orderInfo = appOrderInfoService.getMapper().selectOne(new QueryWrapper<AppOrderInfo>().eq("order_no", orderNo));
        if (null == orderInfo) {
            log.info("订单不存在: {}", orderNo);
            return true;
        }

        try {
            if (orderInfo.getOrderChannel().equals(OrderChannelEnums.PAYPAL.getValue())) {
                paypal(orderNo);
            } else if (orderInfo.getOrderChannel().equals(OrderChannelEnums.PAYERMAX.getValue())) {
                payermaxOrderUpdateQueueMessageHandler.executePayment(orderNo);
            } else if (orderInfo.getOrderChannel().equals(OrderChannelEnums.AIRWALLEX.getValue())) {
                airwallex(orderNo);
            } else if (orderInfo.getOrderChannel().equals(OrderChannelEnums.CRYPTO.getValue())) {
                crypto(orderNo);
            }
        } catch (Exception e) {
            log.error("监测订单状态异常: {}", orderNo, e);
        }

        return true;
    }

    public boolean crypto(String orderNo) {
        CryptoDepositRecord cryptoDepositRecord = cryptoPaymentService.queryDepositOrder(orderNo);
        if (null == cryptoDepositRecord) {
            log.info("未找到Crypto充值订单: {}", orderNo);
            return false;
        }
        if (null != cryptoDepositRecord.getRechargeTime()) {
            log.info("Crypto充值订单已处理: {}", orderNo);
            return false;
        }
        if (cryptoDepositRecord.getProcessStatus() != 1) {
            log.info("Crypto待支付: {}", orderNo);
            return false;
        }
        cryptoDepositRecord.setRechargeTime(new Date());
        cryptoDepositRecordMapper.updateById(cryptoDepositRecord);
        rechargeService.success(orderNo, cryptoDepositRecord.getConfirmedTime());
        log.info("Crypto充值成功: {}", orderNo);
        return true;
    }

    public boolean airwallex(String orderNo) {
        AirwallexPaymentLog paymentLog = airwallexPaymentLogMapper.selectOne(new QueryWrapper<AirwallexPaymentLog>().eq("order_no", orderNo).last("limit 1"));
        if (null == paymentLog) {
            log.info("未找到airwallex支付日志: {}", orderNo);
            return false;
        }
        AirwallexConfigStorageHolder.set(paymentLog.getMcnId());
        JSONObject rawData = airwallexPaymentService.queryPaymentIntent(paymentLog.getPaymentId());
        String status = rawData.getString("status");
        paymentLog.setStatus(status);
        paymentLog.setRawData(rawData);
        airwallexPaymentLogMapper.updateById(paymentLog);
        if (status.equals("SUCCEEDED")) {
            // 支付成功
            Date payTime = rawData.getDate("updated_at");
            rechargeService.success(paymentLog.getOrderNo(), payTime);
        } else if (status.equals("REQUIRES_CAPTURE")) {
            // TODO 需要执行捕获动作
            log.info("准备捕获订单: {}", orderNo);
        }
        return true;
    }

    private boolean paypal(String orderNo) {
        // 查询PayPal支付记录
        PaypalPaymentLog paymentLog = new PaypalPaymentLog();
        paymentLog.setOrderNo(orderNo);
        List<PaypalPaymentLog> paymentLogs = paypalPaymentLogService.selectList(paymentLog);
        if (paymentLogs == null || paymentLogs.isEmpty()) {
            return true;
        }
        PaypalPaymentLog paypalLog = paymentLogs.getFirst();
        if (StringUtils.isNotEmpty(paypalLog.getQueryState()) && "NO_UPDATE".equals(paypalLog.getQueryState())) {
            log.info("订单停止更新: {}", orderNo);
            return true;
        }

        try {
            PaypalConfigStorageHolder.set(paypalLog.getClientId());
            JSONObject rawData;

//            // 根据支付方式查询不同的状态
//            if (PayPalConstants.PaymentMethod.SUBSCRIPTION.equals(paypalLog.getPaymentMethod())) {
//                rawData = payPalSubscriptionService.querySubscription(paypalLog.getPaymentId());
//            } else {
//                rawData = payPalPaymentService.queryOrder(paypalLog.getPaymentId());
//            }

            // 签约支付
            rawData = payPalPaymentService.queryOrder(paypalLog.getPaymentId());

            // 检查支付状态，如果是APPROVED，调用executePayment
            String status = rawData.getString("status");
            String queryState = paypalLog.getQueryState();
            if ("APPROVED".equals(status)) {
                if (StringUtils.isNotEmpty(paypalLog.getQueryState()) && "NO_CAPTURED_PAYMENT".equals(paypalLog.getQueryState())) {
                    log.info("订单停止捕获: {}", orderNo);
                    return true;
                }
                log.info("PayPal订单状态为APPROVED，开始执行支付捕获: orderNo={}, paymentId={}", orderNo, paypalLog.getPaymentId());

                // 从rawData中提取payerId
                String payerId = null;
                if (rawData.containsKey("payer") && rawData.getJSONObject("payer").containsKey("payer_id")) {
                    payerId = rawData.getJSONObject("payer").getString("payer_id");
                }

                if (payerId != null) {
                    JSONObject result = payPalPaymentService.executePayment(paypalLog.getPaymentId(), payerId, paymentCallback);
                    if (result.containsKey("exception") && result.getString("exception").contains("MAX_NUMBER_OF_PAYMENT_ATTEMPTS_EXCEEDED")) {
                        log.info("停止捕获: {}", orderNo);
                        queryState = "NO_CAPTURED_PAYMENT";
                    }
                } else {
                    log.warn("无法从PayPal响应中获取payerId: orderNo={}, paymentId={}", orderNo, paypalLog.getPaymentId());
                }
            }

            PaypalPaymentLog updatePaypalLog = new PaypalPaymentLog();
            updatePaypalLog.setId(paypalLog.getId());
            updatePaypalLog.setRawData(rawData);
            updatePaypalLog.setQueryState(queryState);
            paypalPaymentLogService.update(updatePaypalLog);

            ThreadUtil.sleep(1000);

            log.info("开始更新未支付订单状态: {}, {}", orderNo, rawData);
        } catch (Exception e) {
            log.error("更新未支付订单状态失败: {}", orderNo, e);
        }
        return false;
    }
}
