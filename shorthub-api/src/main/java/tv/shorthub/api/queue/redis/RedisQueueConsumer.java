package tv.shorthub.api.queue.redis;

import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * 队列消费者示例
 * 展示如何安全地消费消息并确保消息被成功处理
 */
@Component
@Slf4j
public class RedisQueueConsumer {

    @Autowired
    private RedisQueue redisQueue;

    // 消费者线程池
    private final ExecutorService executorService = Executors.newFixedThreadPool(10);

    /**
     * 消息处理器接口
     */
    public interface MessageHandler {
        /**
         * 处理消息
         * @param message 消息
         * @return 是否处理成功
         */
        boolean handle(RedisQueueMessage message);
    }

    /**
     * 启动消费者（阻塞模式）
     * @param queueName 队列名称
     * @param handler 消息处理器
     * @param timeoutSeconds 超时时间（秒）
     */
    public void startConsumer(String queueName, MessageHandler handler, int timeoutSeconds) {
        CompletableFuture.runAsync(() -> {
            log.info("Starting consumer for queue [{}]", queueName);
            
            while (!Thread.currentThread().isInterrupted()) {
                try {
                    // 消费消息
                    RedisQueueMessage message = redisQueue.consume(queueName, timeoutSeconds);
                    
                    if (message != null) {
                        // 处理消息
                        processMessage(queueName, message, handler);
                    }
                    
                } catch (Exception e) {
                    log.error("Error consuming message from queue [{}]", queueName, e);
                    
                    // 发生错误时稍作等待
                    try {
                        Thread.sleep(1000);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            }
            
            log.info("Consumer for queue [{}] stopped", queueName);
        }, executorService);
    }

    /**
     * 启动消费者（非阻塞模式）
     * @param queueName 队列名称
     * @param handler 消息处理器
     * @param pollingIntervalMs 轮询间隔（毫秒）
     */
    public void startConsumerNoWait(String queueName, MessageHandler handler, long pollingIntervalMs) {
        CompletableFuture.runAsync(() -> {
            log.info("Starting no-wait consumer for queue [{}]", queueName);
            
            while (!Thread.currentThread().isInterrupted()) {
                try {
                    // 非阻塞消费消息
                    RedisQueueMessage message = redisQueue.consumeNoWait(queueName);
                    
                    if (message != null) {
                        // 处理消息
                        processMessage(queueName, message, handler);
                    } else {
                        // 没有消息时等待
                        Thread.sleep(pollingIntervalMs);
                    }
                    
                } catch (Exception e) {
                    log.error("Error consuming message from queue [{}]", queueName, e);
                    
                    // 发生错误时稍作等待
                    try {
                        Thread.sleep(1000);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            }
            
            log.info("No-wait consumer for queue [{}] stopped", queueName);
        }, executorService);
    }

    /**
     * 处理消息
     * @param queueName 队列名称
     * @param message 消息
     * @param handler 消息处理器
     */
    private void processMessage(String queueName, RedisQueueMessage message, MessageHandler handler) {
        try {
            log.info("Processing message [{}] from queue [{}]", message.getId(), queueName);
            
            // 调用处理器处理消息
            boolean success = handler.handle(message);
            
            if (success) {
                // 处理成功，确认消息
                if (redisQueue.ack(queueName, message.getId())) {
                    log.info("Successfully processed and acknowledged message [{}] from queue [{}]", 
                        message.getId(), queueName);
                } else {
                    log.warn("Failed to acknowledge message [{}] from queue [{}]", 
                        message.getId(), queueName);
                }
            } else {
                // 处理失败，拒绝消息并重新入队
                if (redisQueue.nack(queueName, message.getId(), true)) {
                    log.warn("Failed to process message [{}] from queue [{}], requeued", 
                        message.getId(), queueName);
                } else {
                    log.error("Failed to process and nack message [{}] from queue [{}]", 
                        message.getId(), queueName);
                }
            }
            
        } catch (Exception e) {
            log.error("Error processing message [{}] from queue [{}]", message.getId(), queueName, e);
            
            // 发生异常，拒绝消息并重新入队
            try {
                redisQueue.nack(queueName, message.getId(), true);
            } catch (Exception nackException) {
                log.error("Failed to nack message [{}] from queue [{}] after processing error", 
                    message.getId(), queueName, nackException);
            }
        }
    }

    /**
     * 批量消费消息
     * @param queueName 队列名称
     * @param handler 消息处理器
     * @param batchSize 批量大小
     * @param timeoutSeconds 超时时间（秒）
     */
    public void startBatchConsumer(String queueName, MessageHandler handler, int batchSize, int timeoutSeconds) {
        CompletableFuture.runAsync(() -> {
            log.info("Starting batch consumer for queue [{}], batchSize: {}", queueName, batchSize);
            
            while (!Thread.currentThread().isInterrupted()) {
                try {
                    // 批量消费消息
                    for (int i = 0; i < batchSize; i++) {
                        RedisQueueMessage message = redisQueue.consumeNoWait(queueName);
                        
                        if (message != null) {
                            // 异步处理消息
                            CompletableFuture.runAsync(() -> processMessage(queueName, message, handler), executorService);
                        } else {
                            // 没有更多消息，退出批量处理
                            break;
                        }
                    }
                    
                    // 等待一段时间再进行下一批处理
                    Thread.sleep(timeoutSeconds * 1000);
                    
                } catch (Exception e) {
                    log.error("Error in batch consumer for queue [{}]", queueName, e);
                    
                    // 发生错误时稍作等待
                    try {
                        Thread.sleep(1000);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            }
            
            log.info("Batch consumer for queue [{}] stopped", queueName);
        }, executorService);
    }

    /**
     * 停止所有消费者
     */
    public void shutdown() {
        try {
            log.info("Shutting down queue consumers...");
            
            executorService.shutdown();
            
            if (!executorService.awaitTermination(30, TimeUnit.SECONDS)) {
                executorService.shutdownNow();
            }
            
            log.info("Queue consumers shutdown completed");
            
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            executorService.shutdownNow();
            log.warn("Queue consumers shutdown interrupted");
        }
    }

    /**
     * 获取消费者统计信息
     * @return 统计信息
     */
    public JSONObject getConsumerStats() {
        JSONObject stats = new JSONObject();
        
        try {
            stats.put("activeThreads", executorService.toString());
            stats.put("isShutdown", executorService.isShutdown());
            stats.put("isTerminated", executorService.isTerminated());
            
        } catch (Exception e) {
            log.error("Failed to get consumer stats", e);
        }
        
        return stats;
    }
} 