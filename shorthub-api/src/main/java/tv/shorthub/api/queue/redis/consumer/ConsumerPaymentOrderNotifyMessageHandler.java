package tv.shorthub.api.queue.redis.consumer;

import com.alibaba.fastjson2.JSONObject;
import org.springframework.stereotype.Component;
import tv.shorthub.api.queue.redis.RedisQueueConsumer;
import tv.shorthub.api.queue.redis.RedisQueueMessage;
import tv.shorthub.email.dto.EmailTemplateRequest;
import tv.shorthub.email.enums.EmailTemplateType;

@Component
public class ConsumerPaymentOrderNotifyMessageHandler implements RedisQueueConsumer.MessageHandler {
    @Override
    public boolean handle(RedisQueueMessage message) {
        JSONObject data = message.getData();
        // 方法内部做了幂等处理，避免重复发送邮件
        EmailTemplateRequest emailRequest = EmailTemplateRequest.builder()
                .orderNo(data.getString("orderNo"))
                .templateType(EmailTemplateType.valueOf(data.getString("templateType")))
                .build();
        return true;
    }
}
