package tv.shorthub.api.config.mybatis;

import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedTypes;
import tv.shorthub.common.utils.StringUtils;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

@MappedTypes(Long.class)
@Slf4j
public class CustomerLongTypeHandler extends BaseTypeHandler<Long> {
    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, Long parameter, JdbcType jdbcType) throws SQLException {
        ps.setLong(i, parameter);
    }

    @Override
    public Long getNullableResult(ResultSet rs, String columnName) throws SQLException {
        return convertLong(rs.getString(columnName));
    }


    @Override
    public Long getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        return convertLong(rs.getString(columnIndex));
    }

    @Override
    public Long getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        return convertLong(cs.getString(columnIndex));
    }


    private Long convertLong(String rs) {
        if (StringUtils.isEmpty(rs)) {
            return null;
        }
        String value = rs.trim();
        if (StringUtils.isEmpty(value)) {
            return 0L;
        }
        return Long.valueOf(value);
    }
}