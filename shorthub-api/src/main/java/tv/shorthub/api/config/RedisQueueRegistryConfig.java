package tv.shorthub.api.config;

import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.event.ApplicationStartedEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;
import tv.shorthub.api.queue.redis.RedisQueueConstant;
import tv.shorthub.api.queue.redis.RedisQueueConsumer;
import tv.shorthub.api.queue.redis.consumer.ConsumerPaymentOrderNotifyMessageHandler;

@Component
@Slf4j
public class RedisQueueRegistryConfig  implements ApplicationListener<ApplicationStartedEvent> {


    @Autowired
    RedisQueueConsumer redisQueueConsumer;

    @Autowired
    ConsumerPaymentOrderNotifyMessageHandler consumerPaymentOrderNotifyMessageHandler;

    @Override
    public void onApplicationEvent(ApplicationStartedEvent event) {
//        redisQueueConsumer.startConsumerNoWait(RedisQueueConstant.QUEUE_NAME_PAYMENT_NOTIFY, consumerPaymentOrderNotifyMessageHandler, 1000);

    }
}
