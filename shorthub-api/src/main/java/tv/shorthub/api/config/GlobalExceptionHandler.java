package tv.shorthub.api.config;

import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.core.AuthenticationException;
import org.springframework.validation.BindException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingPathVariableException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import tv.shorthub.api.response.Result;
import tv.shorthub.api.utils.MessageCode;
import tv.shorthub.common.core.domain.AjaxResult;
import tv.shorthub.common.exception.ApiException;
import tv.shorthub.common.exception.DemoModeException;
import tv.shorthub.common.exception.ServiceException;
import tv.shorthub.common.utils.MessageUtils;
import tv.shorthub.common.utils.StringUtils;

/**
 * 全局异常处理器
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    /**
     * 处理自定义异常
     */
    @ExceptionHandler(ApiException.class)
    public Result handleApiException(ApiException e) {
        String message = MessageUtils.message(e.getCode(), e.getMessage());
        return Result.buildFail(e.getCode(), message);
    }

    /**
     * 处理自定义异常
     */
    @ExceptionHandler(ServiceException.class)
    public Result handleServiceException(ServiceException e) {
        String message = MessageUtils.message(e.getMessage());
        return Result.buildFail(e.getMessage(), message);
    }

    /**
     * 请求路径中缺少必需的路径变量
     */
    @ExceptionHandler(MissingPathVariableException.class)
    public Result handleMissingPathVariableException(MissingPathVariableException e, HttpServletRequest request)
    {
        String requestURI = request.getRequestURI();
        log.error("请求路径中缺少必需的路径变量'{}',发生系统异常.", requestURI, e);
        String message = MessageUtils.message(MessageCode.SYSTEM_ERROR.getCode());
        return Result.buildFail(MessageCode.SYSTEM_ERROR.getCode(), message);
    }

    /**
     * 请求参数类型不匹配
     */
    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    public Result handleMethodArgumentTypeMismatchException(MethodArgumentTypeMismatchException e, HttpServletRequest request)
    {
        String requestURI = request.getRequestURI();
        log.error("请求参数类型不匹配'{}',发生系统异常.", requestURI, e);
        String message = MessageUtils.message(MessageCode.SYSTEM_ERROR.getCode());
        return Result.buildFail(MessageCode.SYSTEM_ERROR.getCode(), message);
    }
    /**
     * 拦截未知的运行时异常
     */
    @ExceptionHandler(RuntimeException.class)
    public Result handleRuntimeException(RuntimeException e, HttpServletRequest request)
    {
        String requestURI = request.getRequestURI();
        log.error("请求地址'{}',发生未知异常.", requestURI, e);
        String message = MessageUtils.message(MessageCode.SYSTEM_ERROR.getCode());
        return Result.buildFail(MessageCode.SYSTEM_ERROR.getCode(), message);
    }


    /**
     * 处理参数绑定异常
     */
    @ExceptionHandler(BindException.class)
    public Result<Void> handleBindException(BindException e, HttpServletRequest request) {
        log.error("请求地址'{}',发生参数绑定异常.", request.getRequestURI(), e);
        String message = MessageUtils.message(MessageCode.SYSTEM_ERROR.getCode());
        return Result.buildFail(MessageCode.SYSTEM_ERROR.getCode(), message);
    }

    /**
     * 处理其他异常
     */
    @ExceptionHandler(Exception.class)
    public Result<Void> handleException(Exception e, HttpServletRequest request) {
        log.error("请求地址'{}',发生未知异常.", request.getRequestURI(), e);
        String message = MessageUtils.message(MessageCode.SYSTEM_ERROR.getCode());
        return Result.buildFail(MessageCode.SYSTEM_ERROR.getCode(), message);
    }
} 