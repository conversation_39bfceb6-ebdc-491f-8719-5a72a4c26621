package tv.shorthub.api.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.event.ApplicationStartedEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;
import tv.shorthub.api.queue.*;
import tv.shorthub.common.enums.CloudflareQueueEnum;
import tv.shorthub.common.queue.CloudflareQueueConfig;
import tv.shorthub.common.queue.CloudflareQueueService;
import tv.shorthub.common.utils.spring.SpringUtils;

@Component
@Slf4j
public class QueueRegistryConfig implements ApplicationListener<ApplicationStartedEvent> {

    @Autowired
    CloudflareQueueService cloudflareQueueService;

    @Autowired
    CloudflareQueueConfig config;

    @Override
    public void onApplicationEvent(ApplicationStartedEvent event) {
        if (!config.getEnabled()) {
            log.info("本节点不启动cloudflare队列任务");
            return;
        }

        cloudflareQueueService.registerQueueHandler(CloudflareQueueEnum.PAYPAL_ORDER_UPDATE.getValue(), SpringUtils.getBean(PaypalOrderUpdateQueueMessageHandler.class));
        cloudflareQueueService.registerQueueHandler(CloudflareQueueEnum.PAYERMAX_ORDER_UPDATE.getValue(), SpringUtils.getBean(PayermaxOrderUpdateQueueMessageHandler.class));
        cloudflareQueueService.registerQueueHandler(CloudflareQueueEnum.PAYPAL_WEBHOOK.getValue(), SpringUtils.getBean(PaypalWebhookMessageHandler.class));
        cloudflareQueueService.registerQueueHandler(CloudflareQueueEnum.PROMOTION_MESSAGE.getValue(), SpringUtils.getBean(PromotionMessageQueueMessageHandler.class));
        cloudflareQueueService.registerQueueHandler(CloudflareQueueEnum.LOGIN_RECORDS.getValue(), SpringUtils.getBean(AppLoginRecordsQueueMessageHandler.class));
        cloudflareQueueService.registerQueueHandler(CloudflareQueueEnum.ORDER_LISTEN.getValue(), SpringUtils.getBean(OrderListenQueryMessageHandler.class));
    }
}
