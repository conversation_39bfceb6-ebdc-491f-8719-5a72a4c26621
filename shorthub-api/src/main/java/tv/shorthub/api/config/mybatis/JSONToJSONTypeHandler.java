package tv.shorthub.api.config.mybatis;

import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

@MappedTypes(JSONObject.class)
@MappedJdbcTypes(JdbcType.VARCHAR) // MySQL JSON 通常映射为 VARCHAR
@Slf4j
public class JSONToJSONTypeHandler extends BaseTypeHandler<JSONObject> {
    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, JSONObject parameter, JdbcType jdbcType)
            throws SQLException {
        // 将 JSONObject 转换为 JSON 字符串存储到数据库
        ps.setString(i, parameter.toJSONString());
    }

    @Override
    public JSONObject getNullableResult(ResultSet rs, String columnName) throws SQLException {
        // 从 ResultSet 中获取 JSON 字符串并转换为 JSONObject
        String jsonStr = rs.getString(columnName);
        return jsonStr == null ? null : JSONObject.parseObject(jsonStr);
    }

    @Override
    public JSONObject getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        // 根据列索引获取 JSON 字符串并转换为 JSONObject
        String jsonStr = rs.getString(columnIndex);
        return jsonStr == null ? null : JSONObject.parseObject(jsonStr);
    }

    @Override
    public JSONObject getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        // 从 CallableStatement 中获取 JSON 字符串并转换为 JSONObject
        String jsonStr = cs.getString(columnIndex);
        return jsonStr == null ? null : JSONObject.parseObject(jsonStr);
    }
}