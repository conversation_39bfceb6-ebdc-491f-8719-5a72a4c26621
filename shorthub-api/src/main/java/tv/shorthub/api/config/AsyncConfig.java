package tv.shorthub.api.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.task.TaskExecutor;
import org.springframework.scheduling.annotation.AsyncConfigurer;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ConcurrentTaskExecutor;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Configuration
@EnableAsync
@Slf4j
public class AsyncConfig implements AsyncConfigurer {

    @Bean
    public TaskExecutor taskExecutor() {
        ExecutorService executorService = Executors.newVirtualThreadPerTaskExecutor();
        log.info("Configured async task executor to use virtual threads.");
        return new ConcurrentTaskExecutor(executorService);
    }

    @Bean
    public ThreadPoolTaskScheduler taskScheduler() {
        ThreadPoolTaskScheduler scheduler = new ThreadPoolTaskScheduler();
        scheduler.setPoolSize(1);
        scheduler.setThreadFactory(Thread.ofVirtual().factory());
        scheduler.setRemoveOnCancelPolicy(true);
        scheduler.setErrorHandler(throwable ->
            log.error("Error in scheduled task: {}", throwable.getMessage(), throwable));
        log.info("Configured scheduler to use a single virtual thread for all scheduled tasks.");
        return scheduler;
    }

    @Override
    public TaskExecutor getAsyncExecutor() {
        return taskExecutor();
    }
}
