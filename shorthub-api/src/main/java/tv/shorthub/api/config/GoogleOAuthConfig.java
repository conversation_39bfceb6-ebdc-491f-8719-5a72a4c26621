package tv.shorthub.api.config;

import lombok.Data;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConfigurationProperties(prefix = "google")
@Data
public class GoogleOAuthConfig {

    private Client client;
    private Android android;
    private List<Map<String, String>> oauth;

    @Data
    public static class Client {
        private String id;
        private String secret;
    }

    @Data
    public static class Android {
        private String id;
        private String secret;
    }

    public String getOAuthClientId(String packageName) {
        return getOauth().stream().filter(f -> f.get("key").equals(packageName)).findFirst().orElseGet(HashMap::new).get("id");
    }

    public String getClientId(String platform) {
        return "android".equalsIgnoreCase(platform) ? android.getId() : client.getId();
    }

    public String getClientSecret(String platform) {
        return "android".equalsIgnoreCase(platform) ? android.getSecret() : client.getSecret();
    }
}
