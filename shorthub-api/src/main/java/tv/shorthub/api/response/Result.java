package tv.shorthub.api.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class Result<T> {
    private int code;
    private boolean success;
    private String errorCode;
    private String errorMsg;
    private T data;

    public static Result  buildOk() {
        return buildOk(null);
    }

    public static Result buildOk(Object data) {
        return buildOk(data, 200);
    }

    public static Result buildOk(Object data, int code) {
        Result result = new Result();
        result.setCode(code);
        result.setSuccess(true);
        result.setData(data);
        return result;
    }

    public static Result  buildFail() {
        return buildFail(null, null);
    }

    public static Result buildFail(String errorCode, String errorMsg) {
        Result result = new Result();
        result.setCode(500);
        result.setSuccess(false);
        result.setErrorCode(errorCode);
        result.setErrorMsg(errorMsg);
        return result;
    }

    public static Result buildFail(String message) {
        return buildFail("error",  message);
    }
}
