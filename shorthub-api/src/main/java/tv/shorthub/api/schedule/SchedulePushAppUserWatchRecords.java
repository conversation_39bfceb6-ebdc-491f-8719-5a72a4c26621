package tv.shorthub.api.schedule;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import tv.shorthub.system.domain.AppUserWatchContent;
import tv.shorthub.system.domain.AppUserWatchRecords;
import tv.shorthub.system.service.IAppUserWatchContentService;
import tv.shorthub.system.service.IAppUserWatchRecordsService;

import java.util.*;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.locks.ReentrantLock;

@Component
@Slf4j
public class SchedulePushAppUserWatchRecords {

    @Autowired
    private IAppUserWatchRecordsService appUserWatchRecordsService;

    @Autowired
    private IAppUserWatchContentService appUserWatchContentService;

    private final ConcurrentLinkedQueue<AppUserWatchRecords> recordsQueue = new ConcurrentLinkedQueue<>();
    private final ReentrantLock processingLock = new ReentrantLock();
    private final AtomicBoolean isProcessing = new AtomicBoolean(false);
    private static final int BATCH_SIZE = 100;

    /**
     * 添加观看记录到队列
     * @param record 观看记录
     */
    public void addWatchRecord(AppUserWatchRecords record) {
        recordsQueue.offer(record);
    }

    public void addWatchRecord(String appid, String tfid, String userId, String contentId, Long serialNumber, Long serialSecond, Long watchSecond) {
        AppUserWatchRecords record = new AppUserWatchRecords();
        record.setUserId(userId);
        record.setAppid(appid);
        record.setTfid(tfid);
        record.setContentId(contentId);
        record.setSerialNumber(serialNumber);
        record.setSerialSecond(serialSecond);
        record.setMaxWatchSecond(watchSecond);
        record.setWatchedAt(new Date());
        record.setCreateTime(record.getWatchedAt());
        record.setUpdateTime(record.getWatchedAt());
        addWatchRecord(record);
    }

    /**
     * 批量处理观看记录
     * 每秒执行一次
     */
    @Scheduled(fixedRate = 1000)
    public void processWatchRecords() {
        if (isProcessing.get() || recordsQueue.isEmpty()) {
            return;
        }

        if (!processingLock.tryLock()) {
            return;
        }

        try {
            isProcessing.set(true);
            List<AppUserWatchRecords> batchRecords = new ArrayList<>(BATCH_SIZE);
            Map<String, AppUserWatchContent> uniqueContents = new HashMap<>();

            // 从队列中获取一批数据
            while (!recordsQueue.isEmpty() && batchRecords.size() < BATCH_SIZE) {
                AppUserWatchRecords record = recordsQueue.poll();
                if (record != null) {
                    batchRecords.add(record);

                    // 创建对应的观看内容记录，使用userId + contentId作为key进行去重
                    String uniqueKey = record.getUserId() + "_" + record.getContentId();
                    if (!uniqueContents.containsKey(uniqueKey)) {
                        AppUserWatchContent content = new AppUserWatchContent();
                        content.setAppid(record.getAppid());
                        content.setUserId(record.getUserId());
                        content.setContentId(record.getContentId());
                        content.setWatchedAt(record.getWatchedAt());
                        content.setCreateTime(record.getCreateTime());
                        content.setUpdateTime(record.getUpdateTime());
                        uniqueContents.put(uniqueKey, content);
                    }
                }
            }

            if (!batchRecords.isEmpty()) {
                // 批量保存观看记录
                appUserWatchRecordsService.getMapper().batchInsertOrUpdateCustom(batchRecords);
                // 批量保存去重后的观看内容记录
                if (!uniqueContents.isEmpty()) {
                    appUserWatchContentService.getMapper().batchInsertOrUpdate(new ArrayList<>(uniqueContents.values()));
                }
            }
        } finally {
            isProcessing.set(false);
            processingLock.unlock();
        }
    }
}
