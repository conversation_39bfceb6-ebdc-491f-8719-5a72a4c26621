package tv.shorthub.api.schedule;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import tv.shorthub.system.service.IAppBlacklistService;

@Component
public class ScheduleRefreshBlacklist {

    @Autowired
    IAppBlacklistService appBlacklistService;

    /**
     * 更新黑名单缓存
     */
    @Scheduled(fixedRate = 30000)
    public void processWatchRecords() {
        appBlacklistService.refresh();
    }
}
