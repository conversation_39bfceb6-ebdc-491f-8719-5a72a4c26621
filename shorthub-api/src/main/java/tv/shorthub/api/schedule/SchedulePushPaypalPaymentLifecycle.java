package tv.shorthub.api.schedule;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson2.JSONObject;

import tv.shorthub.system.domain.PaypalPaymentLifecycle;
import tv.shorthub.system.mapper.PaypalPaymentLifecycleMapper;

import java.util.*;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.locks.ReentrantLock;

@Component
@Slf4j
public class SchedulePushPaypalPaymentLifecycle {

    @Autowired
    private PaypalPaymentLifecycleMapper paymentLifecycleMapper;

    private final ConcurrentLinkedQueue<PaypalPaymentLifecycle> recordsQueue = new ConcurrentLinkedQueue<>();
    private final ReentrantLock processingLock = new ReentrantLock();
    private final AtomicBoolean isProcessing = new AtomicBoolean(false);
    private static final int BATCH_SIZE = 100;

    /**
     * 添加paypal支付组件生命周期到队列
     * @param record 支付组件生命周期
     */
    public void addLifecycle(PaypalPaymentLifecycle record) {
        log.info("添加paypal支付组件生命周期到队列: {}", JSONObject.toJSONString(record));
        recordsQueue.offer(record);
    }

    /**
     * 添加paypal支付组件生命周期到队列
     * @param orderNo 订单号
     * @param state 状态
     * @param errmsg 错误信息
     * @param extendData 扩展数据
     */
    public void addLifecycle(String orderNo, String userId, String state, String errmsg, JSONObject extendData) {
        PaypalPaymentLifecycle record = new PaypalPaymentLifecycle();
        record.setUserId(userId);
        record.setErrmsg(errmsg);
        record.setExtendData(extendData);
        record.setOrderNo(orderNo);
        record.setState(state);
        record.setCreateTime(new Date());
        record.setUpdateTime(record.getCreateTime());
        addLifecycle(record);
    }

    /**
     * 批量处理paypal支付组件生命周期
     * 每秒执行一次
     */
    @Scheduled(fixedRate = 1000)
    public void processPaypalPaymentLifecycle() {
        if (isProcessing.get() || recordsQueue.isEmpty()) {
            return;
        }

        if (!processingLock.tryLock()) {
            return;
        }

        try {
            isProcessing.set(true);
            List<PaypalPaymentLifecycle> batchRecords = new ArrayList<>(BATCH_SIZE);

            // 从队列中获取一批数据
            while (!recordsQueue.isEmpty() && batchRecords.size() < BATCH_SIZE) {
                PaypalPaymentLifecycle record = recordsQueue.poll();
                if (record != null) {
                    batchRecords.add(record);
                }
            }

            if (!batchRecords.isEmpty()) {
                // 批量保存paypal支付组件生命周期
                try {
                    paymentLifecycleMapper.batchInsertOrUpdate(batchRecords);
                } catch (Exception exception) {
                    log.error("批量保存paypal支付组件生命周期失败: {}", JSONObject.toJSONString(batchRecords), exception);
                }
            }
        } finally {
            isProcessing.set(false);
            processingLock.unlock();
        }
    }
}
