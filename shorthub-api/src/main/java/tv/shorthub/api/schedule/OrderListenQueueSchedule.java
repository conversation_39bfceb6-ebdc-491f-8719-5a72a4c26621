package tv.shorthub.api.schedule;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.TimeUnit;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import tv.shorthub.api.queue.QueueFastJoin;
import tv.shorthub.common.core.cache.CacheKeyUtils;
import tv.shorthub.common.core.redis.RedisCache;
import tv.shorthub.common.enums.CloudflareQueueEnum;
import tv.shorthub.paypal.service.PayPalSubscriptionService;
import tv.shorthub.system.domain.AppOrderInfo;
import tv.shorthub.system.service.IAppOrderInfoService;


/**
 * 订单监听队列定时任务
 */
@Component
@Slf4j
public class OrderListenQueueSchedule {

    @Autowired
    IAppOrderInfoService appOrderInfoService;

    @Autowired
    QueueFastJoin queueFastJoin;

    @Autowired
    RedisCache redisCache;


    // 5秒钟执行一次
    @Scheduled(cron = "0/5 * * * * ?")
    public void execute() {
        int time = 10;
        QueryWrapper<AppOrderInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("order_status", 0)
                .ge("create_time", LocalDateTime.now().minusMinutes(time))
        ;
        List<AppOrderInfo> appOrderInfos = appOrderInfoService.getMapper().selectList(queryWrapper);
        log.info("近{}分钟未支付订单数...{}", time, appOrderInfos.size());
        for (AppOrderInfo appOrderInfo : appOrderInfos) {
            boolean locked = redisCache.redisTemplate.opsForValue().setIfAbsent(CacheKeyUtils.getOrderListenQueue(appOrderInfo.getOrderNo()), "true", 20, TimeUnit.SECONDS);
            if (!locked) {
                continue;
            }
            log.info("订单监听队列添加: {}", appOrderInfo.getOrderNo());
            // 每笔订单查询限流
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("orderNo", appOrderInfo.getOrderNo());
            queueFastJoin.addToQueue(CloudflareQueueEnum.ORDER_LISTEN, jsonObject);
        }
    }
}
