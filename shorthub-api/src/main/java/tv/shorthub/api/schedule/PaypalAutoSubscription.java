package tv.shorthub.api.schedule;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import cn.hutool.core.thread.ThreadUtil;
import com.alibaba.fastjson2.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import lombok.extern.slf4j.Slf4j;
import tv.shorthub.api.service.recharge.RechargeService;
import tv.shorthub.common.config.AppConfig;
import tv.shorthub.common.core.cache.CacheKeyUtils;
import tv.shorthub.common.core.cache.CacheService;
import tv.shorthub.common.core.redis.RedisCache;
import tv.shorthub.common.enums.OrderChannelEnums;
import tv.shorthub.common.utils.DateUtils;
import tv.shorthub.paypal.config.PaypalConfigStorageHolder;
import tv.shorthub.paypal.constant.PayPalConstants;
import tv.shorthub.paypal.model.PaypalPaymentRequest;
import tv.shorthub.paypal.service.PayPalBillingAgreementService;
import tv.shorthub.paypal.service.PayPalPaymentService;
import tv.shorthub.system.domain.AppOrderInfo;
import tv.shorthub.system.domain.PaypalPaymentLog;
import tv.shorthub.system.domain.PaypalUserPlan;
import tv.shorthub.system.domain.PaypalUserPlanRecord;
import tv.shorthub.system.service.IAppOrderInfoService;
import tv.shorthub.system.service.IPaypalPaymentLogService;
import tv.shorthub.system.service.IPaypalUserPlanRecordService;
import tv.shorthub.system.service.IPaypalUserPlanService;

/**
 * paypal自动扣款服务
 */
@Component
@Slf4j
public class PaypalAutoSubscription {

    @Autowired
    IPaypalUserPlanService paypalUserPlanService;

    @Autowired
    IPaypalUserPlanRecordService paypalUserPlanRecordService;

    @Autowired
    IAppOrderInfoService appOrderInfoService;

    @Autowired
    IPaypalPaymentLogService paypalPaymentLogService;

    @Autowired
    PayPalPaymentService payPalPaymentService;

    @Autowired
    RechargeService rechargeService;

    
    @Autowired
    RedisCache redisCache;

    @Autowired
    PayPalBillingAgreementService payPalBillingAgreementService;

    @Autowired
    AirwallexAutoSubscription airwallexAutoSubscription;

    // 每20秒执行一次
    @Scheduled(cron = "0/20 * * * * ?")
    public void autoSubscription() {
        String lockKey = CacheKeyUtils.LOCK_PAYPAL_AUTO_SUBSCRIPTION;
        boolean locked = redisCache.redisTemplate.opsForValue().setIfAbsent(lockKey, "true", 20, TimeUnit.SECONDS);
        if (!locked) {
            return;
        }

        // 查询所有未支付的订单
        List<PaypalUserPlan> paypalUserPlans = paypalUserPlanService.getMapper().selectList(new LambdaQueryWrapper<PaypalUserPlan>()
                .eq(PaypalUserPlan::getEnabled, 1));
        log.info("查询到{}个已签约的订单", paypalUserPlans.size());
        List<Date> nextTimes = new ArrayList<>();
        for (PaypalUserPlan paypalUserPlan : paypalUserPlans) {
            try {
                Date nextTime = start(paypalUserPlan);
                nextTimes.add(nextTime);
            } catch (Exception exception) {
                log.info("自动续期失败: {}", paypalUserPlan.getOrderNo(), exception);
            }
        }

        // 输出最近的一个续期时间
        if (!nextTimes.isEmpty()) {
            Date latestNextTime = Collections.min(nextTimes);
            log.info("最近的续期时间: {}", DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, latestNextTime));
        }
        
    }

    private Date start(PaypalUserPlan paypalUserPlan) throws Exception {
        long count = paypalUserPlanRecordService.getMapper().selectCount(new LambdaQueryWrapper<PaypalUserPlanRecord>()
            .eq(PaypalUserPlanRecord::getOrderNo, paypalUserPlan.getOrderNo()));

        // 因为count是从0开始，所以需要加1
        count = count + 1;

        // 查询最后一次扣款记录
        PaypalUserPlanRecord latestRecord = paypalUserPlanRecordService.getMapper().selectOne(new LambdaQueryWrapper<PaypalUserPlanRecord>()
                .eq(PaypalUserPlanRecord::getOrderNo, paypalUserPlan.getOrderNo())
                .orderByDesc(PaypalUserPlanRecord::getCreateTime)
                .last("limit 1"));
        Date lastPayTime;
        String period = paypalUserPlan.getPeriod();
        // 查询订单
        lastPayTime = Objects.requireNonNullElse(latestRecord, paypalUserPlan).getCreateTime();

        AppOrderInfo appOrderInfo = appOrderInfoService.getMapper().selectOne(new LambdaQueryWrapper<AppOrderInfo>()
                .eq(AppOrderInfo::getOrderNo, paypalUserPlan.getOrderNo())
                .last("limit 1"));

//        PaypalConfigStorageHolder.set(appOrderInfo.getPayMchId());
//        payPalBillingAgreementService.getBillingAgreement(paypalUserPlan.getPaymentId());
//        ThreadUtil.sleep(1000L);

        // 根据计费周期计算下一次扣费时间
        Date nextPayTime = switch (period) {
            case "DAY" -> DateUtils.addDays(lastPayTime, 1);
            case "WEEK" -> DateUtils.addDays(lastPayTime, 7);
            case "MONTH" -> DateUtils.addDays(lastPayTime, 30);
            case "QUARTER" -> DateUtils.addDays(lastPayTime, 90);
            case "YEAR" -> DateUtils.addDays(lastPayTime, 365);
            default -> null;
        };

        // 如果下一次扣费时间小于当前时间，则进行扣款
        if (null != nextPayTime && nextPayTime.before(new Date())) {
            doSubscription(paypalUserPlan, appOrderInfo, count, period);
        } else {
            airwallexAutoSubscription.sendRemind(count, lastPayTime, appOrderInfo, nextPayTime);
        }
        return nextPayTime;
    }

    private void doSubscription(PaypalUserPlan paypalUserPlan, AppOrderInfo appOrderInfo, long count, String period) {
        // 发起扣款
        log.info("开始自动续期: {}, 续期金额: {}", paypalUserPlan.getOrderNo(), appOrderInfo.getOrderAmountOrigin());

        PaypalPaymentLog paypalPaymentLog = paypalPaymentLogService.getMapper().selectOne(new LambdaQueryWrapper<PaypalPaymentLog>()
                .eq(PaypalPaymentLog::getOrderNo, paypalUserPlan.getOrderNo())
                .last("limit 1"));

        // 普通支付流程
        PaypalPaymentRequest paypalPaymentRequest = new PaypalPaymentRequest();
        paypalPaymentRequest.setAmount(appOrderInfo.getOrderAmountOrigin());
        paypalPaymentRequest.setPaymentMethod(PayPalConstants.PaymentMethod.RECHARGE);
        paypalPaymentRequest.setItemName("Recharge");
        paypalPaymentRequest.setReturnUrl(AppConfig.getDomainApi() + "/api/order/execute-payment");
        paypalPaymentRequest.setCancelUrl(AppConfig.getDomainApi() + "/api/order/cancel-payment");
        paypalPaymentRequest.setOrderNo(paypalPaymentLog.getOrderNo());

        PaypalConfigStorageHolder.set(paypalPaymentLog.getClientId());

        JSONObject paymentSource = paypalPaymentLog.getRawData().getJSONObject("payment_source");
        if (null != paymentSource && paymentSource.containsKey(OrderChannelEnums.PAYPAL.getValue())) {
            JSONObject paypal = paymentSource.getJSONObject(OrderChannelEnums.PAYPAL.getValue());
            if (paypal.containsKey("attributes")) {
                JSONObject attributes = paypal.getJSONObject("attributes");
                if (attributes.containsKey("vault")) {
                    paypalPaymentRequest.setVaultId(attributes.getJSONObject("vault").getString("id"));
                }
            }
        }

        // 发起paypal支付
        JSONObject payment = null;
        String error = null;
        try {
            payment = payPalPaymentService.createPayment(paypalPaymentRequest);
            if (payment.containsKey("status") && "COMPLETED".equals(payment.getString("status"))) {
                rechargeService.doSuccess(appOrderInfo);
                // 发起扣款
                log.info("自动续期完成，订单号：{}, 第{}次续费, 续期结果: {}", paypalUserPlan.getOrderNo(), count, payment);
            } else {
                error = "true";
                log.info("自动续期失败，订单号：{}, 第{}次续费", paypalUserPlan.getOrderNo(), count);
            }
        } catch (Exception e) {
            error = "true";
            log.error("自动续期失败，订单号：{}, 第{}次续费", paypalUserPlan.getOrderNo(), count, e);
            paypalUserPlan.setEnabled(false);
            paypalUserPlanService.update(paypalUserPlan);
        }

        // 保存续期结果
        PaypalUserPlanRecord currentRecord = new PaypalUserPlanRecord();
        currentRecord.setOrderNo(paypalUserPlan.getOrderNo());
        currentRecord.setAmount(paypalPaymentRequest.getAmount());
        currentRecord.setCurrency(appOrderInfo.getCurrency());
        currentRecord.setExtendData(payment);
        currentRecord.setPaymentId(paypalPaymentLog.getPaymentId());
        currentRecord.setPeriod(period);
        currentRecord.setPeriodNum(count);
        currentRecord.setCreateTime(new Date());
        currentRecord.setUpdateBy(error);
        paypalUserPlanRecordService.getMapper().insert(currentRecord);
    }

}
