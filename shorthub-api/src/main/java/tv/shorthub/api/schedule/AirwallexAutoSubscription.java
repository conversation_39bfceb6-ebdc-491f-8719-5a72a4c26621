package tv.shorthub.api.schedule;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import tv.shorthub.airwallex.model.PaymentIntentRequest;
import tv.shorthub.airwallex.service.AirwallexConfigStorageHolder;
import tv.shorthub.airwallex.service.AirwallexPaymentService;
import tv.shorthub.api.service.recharge.RechargeService;
import tv.shorthub.common.config.AppConfig;
import tv.shorthub.common.core.cache.CacheKeyUtils;
import tv.shorthub.common.core.cache.CacheService;
import tv.shorthub.common.core.redis.RedisCache;
import tv.shorthub.common.enums.OrderChannelEnums;
import tv.shorthub.common.utils.DateUtils;
import tv.shorthub.email.service.impl.ResendEmailServiceImpl;
import tv.shorthub.system.domain.AppOrderInfo;
import tv.shorthub.system.domain.AirwallexPaymentLog;
import tv.shorthub.system.domain.AirwallexUserPlan;
import tv.shorthub.system.domain.AirwallexUserPlanRecord;
import tv.shorthub.system.service.*;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * airwallex自动扣款服务
 */
@Component
@Slf4j
public class AirwallexAutoSubscription {

    @Autowired
    IAirwallexUserPlanService airwallexUserPlanService;

    @Autowired
    IAirwallexUserPlanRecordService airwallexUserPlanRecordService;

    @Autowired
    IAppOrderInfoService appOrderInfoService;

    @Autowired
    IAirwallexPaymentLogService airwallexPaymentLogService;

    @Autowired
    AirwallexPaymentService airwallexPaymentService;


    @Autowired
    RechargeService rechargeService;

    @Autowired
    ResendEmailServiceImpl resendEmailService;

    
    @Autowired
    RedisCache redisCache;

    @Autowired
    CacheService cacheService;

    // 每20秒执行一次
    @Scheduled(cron = "0/20 * * * * ?")
    public void autoSubscription() {
        String lockKey = CacheKeyUtils.LOCK_AIRWALLEX_AUTO_SUBSCRIPTION;
        boolean locked = redisCache.redisTemplate.opsForValue().setIfAbsent(lockKey, "true", 20, TimeUnit.SECONDS);
        if (!locked) {
            return;
        }

        // 查询所有未支付的订单
        List<AirwallexUserPlan> airwallexUserPlans = airwallexUserPlanService.getMapper().selectList(new LambdaQueryWrapper<AirwallexUserPlan>()
                .eq(AirwallexUserPlan::getEnabled, 1));
        log.info("查询到{}个已签约的订单", airwallexUserPlans.size());
        List<Date> nextTimes = new ArrayList<>();
        for (AirwallexUserPlan airwallexUserPlan : airwallexUserPlans) {
            try {
                Date nextTime = start(airwallexUserPlan);
                nextTimes.add(nextTime);
            } catch (Exception exception) {
                log.info("自动续期失败: {}", airwallexUserPlan.getOrderNo(), exception);
            }
        }

        // 输出最近的一个续期时间
        if (!nextTimes.isEmpty()) {
            Date latestNextTime = Collections.min(nextTimes);
            log.info("最近的续期时间: {}", DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, latestNextTime));
        }
        
    }

    private Date start(AirwallexUserPlan airwallexUserPlan) throws Exception {
        long count = airwallexUserPlanRecordService.getMapper().selectCount(new LambdaQueryWrapper<AirwallexUserPlanRecord>()
            .eq(AirwallexUserPlanRecord::getOrderNo, airwallexUserPlan.getOrderNo()));

        // 因为count是从0开始，所以需要加1
        count = count + 1;

        // 查询最后一次扣款记录
        AirwallexUserPlanRecord latestRecord = airwallexUserPlanRecordService.getMapper().selectOne(new LambdaQueryWrapper<AirwallexUserPlanRecord>()
                .eq(AirwallexUserPlanRecord::getOrderNo, airwallexUserPlan.getOrderNo())
                .orderByDesc(AirwallexUserPlanRecord::getCreateTime)
                .last("limit 1"));
        Date lastPayTime;
        String period = airwallexUserPlan.getPeriod();
        // 查询订单
        lastPayTime = Objects.requireNonNullElse(latestRecord, airwallexUserPlan).getCreateTime();

        AppOrderInfo appOrderInfo = appOrderInfoService.getMapper().selectOne(new LambdaQueryWrapper<AppOrderInfo>()
                .eq(AppOrderInfo::getOrderNo, airwallexUserPlan.getOrderNo())
                .last("limit 1"));

        // 根据计费周期计算下一次扣费时间
        Date nextPayTime = switch (period) {
            case "DAY" -> DateUtils.addDays(lastPayTime, 1);
            case "WEEK" -> DateUtils.addDays(lastPayTime, 7);
            case "MONTH" -> DateUtils.addDays(lastPayTime, 30);
            case "QUARTER" -> DateUtils.addDays(lastPayTime, 90);
            case "YEAR" -> DateUtils.addDays(lastPayTime, 365);
            default -> null;
        };

        // 如果下一次扣费时间小于当前时间，则进行扣款
        if (null != nextPayTime && nextPayTime.before(new Date())) {
            doSubscription(airwallexUserPlan, appOrderInfo, count, period);
        } else {
            sendRemind(count, lastPayTime, appOrderInfo, nextPayTime);
        }
        return nextPayTime;
    }

    private void doSubscription(AirwallexUserPlan airwallexUserPlan, AppOrderInfo appOrderInfo, long count, String period) {
        // 发起扣款
        log.info("开始自动续期: {}, 续期金额: {}", airwallexUserPlan.getOrderNo(), appOrderInfo.getOrderAmountOrigin());

        AirwallexPaymentLog airwallexPaymentLog = airwallexPaymentLogService.getMapper().selectOne(new LambdaQueryWrapper<AirwallexPaymentLog>()
                .eq(AirwallexPaymentLog::getOrderNo, airwallexUserPlan.getOrderNo())
                .last("limit 1"));
        JSONObject payment = null;
        JSONObject confirm = null;
        boolean success = false;
        try {
            // 获取到前端的payment_consent_id或通过webhook监听
            AirwallexConfigStorageHolder.set(airwallexPaymentLog.getMcnId());
            PaymentIntentRequest request = new PaymentIntentRequest();
            request.setCustomerId(airwallexUserPlan.getConsumerId());
            request.setAmount(appOrderInfo.getOrderAmountOrigin());
            request.setCurrency(appOrderInfo.getCurrency());
            request.setMerchantOrderId(IdUtil.getSnowflakeNextIdStr());
            request.setRequestId(IdUtil.getSnowflakeNextIdStr());
            payment = airwallexPaymentService.createPaymentIntent(request);

            // 后续付费
            confirm = airwallexPaymentService.confirmPaymentIntent(payment.getString("id"), airwallexUserPlan.getPaymentConsentId());
            log.info("确认支付: {}", confirm);
            success = confirm.getString("status").equals("SUCCEEDED");
            if (success) {
                rechargeService.doSuccess(appOrderInfo);
                // 发起扣款
                log.info("自动续期完成，订单号：{}, 第{}次续费", airwallexUserPlan.getOrderNo(), count);
            }
        } catch (Exception e) {
            log.error("自动续期失败，订单号：{}, 第{}次续费", airwallexUserPlan.getOrderNo(), count, e);
            airwallexUserPlan.setEnabled(false);
            airwallexUserPlanService.update(airwallexUserPlan);
        }

        JSONObject extendData = new JSONObject();
        extendData.put("payment", payment);
        extendData.put("confirm", confirm);

        // 保存续期结果
        AirwallexUserPlanRecord currentRecord = new AirwallexUserPlanRecord();
        currentRecord.setOrderNo(airwallexUserPlan.getOrderNo());
        currentRecord.setAmount(appOrderInfo.getOrderAmountOrigin());
        currentRecord.setCurrency(appOrderInfo.getCurrency());
        currentRecord.setExtendData(extendData);
        currentRecord.setSuccess(success);
        currentRecord.setFailureCnt(!success ? 0L : 1L);
        currentRecord.setPaymentId(airwallexPaymentLog.getPaymentId());
        currentRecord.setPeriod(period);
        currentRecord.setPeriodNum(count);
        currentRecord.setCreateTime(new Date());
        airwallexUserPlanRecordService.getMapper().insert(currentRecord);
    }

    public void sendRemind(long count, Date lastPayTime, AppOrderInfo appOrderInfo, Date nextPayTime) {
        int remindDays = getRemindDays();
        // 判断是否在近7天
        if (DateUtils.addDays(lastPayTime, remindDays).before(new Date())) {
            // 判断是否已经发送过邮件
            String key = CacheKeyUtils.getAutoSubscriptionRemindEmail(appOrderInfo.getOrderNo(), count);
            if (!redisCache.hasKey(key)) {
                // 发送邮件
//                resendEmailService.sendEmail(
//                    EmailTemplateRequest.builder()
//                        .orderNo(appOrderInfo.getOrderNo())
//                        .amount(appOrderInfo.getOrderAmountOrigin())
//                        .paymentTime(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, nextPayTime))
//                        .templateType(EmailTemplateType.SUBSCRIPTION_CANCELLED)
//                        .build()
//                    );
                redisCache.setCacheObject(key, "true", remindDays, TimeUnit.DAYS);
                log.info("自动续期提醒邮件发送成功: {}, 当前续期次数: {}", appOrderInfo.getOrderNo(), count);
            }
        }
    }

    private int getRemindDays() {
        try {
            if (cacheService.getCacheObject(CacheKeyUtils.REMIND_DAYS) != null) {
                return cacheService.getCacheObject(CacheKeyUtils.REMIND_DAYS);
            }
        } catch (Exception e) {
            log.error("获取自动续期提醒天数失败", e);
        }
        return 7;
    }
}
