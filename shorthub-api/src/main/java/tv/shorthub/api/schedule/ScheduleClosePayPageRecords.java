package tv.shorthub.api.schedule;

import cn.hutool.core.bean.BeanUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import tv.shorthub.system.domain.AppUserClosePayPageRecord;
import tv.shorthub.system.domain.AppUserWatchContent;
import tv.shorthub.system.domain.AppUserWatchRecords;
import tv.shorthub.system.service.AppUserClosePayPageRecordService;
import tv.shorthub.system.service.IAppUserWatchContentService;
import tv.shorthub.system.service.IAppUserWatchRecordsService;

import java.util.*;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.locks.ReentrantLock;

@Component
@Slf4j
public class ScheduleClosePayPageRecords {

    @Autowired
    AppUserClosePayPageRecordService appUserClosePayPageService;

    private final ConcurrentLinkedQueue<AppUserClosePayPageRecord> recordsQueue = new ConcurrentLinkedQueue<>();
    private final ReentrantLock processingLock = new ReentrantLock();
    private final AtomicBoolean isProcessing = new AtomicBoolean(false);
    private static final int BATCH_SIZE = 100;

    /**
     * 添加关闭支付页面记录到队列
     * @param record 关闭支付记录
     */
    public void addClosePayPageRecord(AppUserClosePayPageRecord record) {
        recordsQueue.offer(record);
    }

    /**
     * 批量处理观看记录
     * 每秒执行一次
     */
    @Scheduled(fixedRate = 1000)
    public void processWatchRecords() {
        if (isProcessing.get() || recordsQueue.isEmpty()) {
            return;
        }

        if (!processingLock.tryLock()) {
            return;
        }

        try {
            isProcessing.set(true);
            List<AppUserClosePayPageRecord> batchRecords = new ArrayList<>(BATCH_SIZE);
            Map<String, AppUserClosePayPageRecord> uniqueContents = new HashMap<>();

            // 从队列中获取一批数据
            while (!recordsQueue.isEmpty() && batchRecords.size() < BATCH_SIZE) {
                AppUserClosePayPageRecord record = recordsQueue.poll();
                if (record != null) {
                    batchRecords.add(record);

                    // 创建对应的关闭支付记录，使用userId + itemId作为key进行去重
                    String uniqueKey = record.getUserId() + "_" + record.getItemId();
                    if (!uniqueContents.containsKey(uniqueKey)) {
                        AppUserClosePayPageRecord bean = BeanUtil.toBean(record, AppUserClosePayPageRecord.class);
                        uniqueContents.put(uniqueKey, bean);
                    }
                }
            }

            if (!batchRecords.isEmpty()) {
                // 批量保存观看记录
                appUserClosePayPageService.getMapper().batchInsertOrUpdate(new ArrayList<>(uniqueContents.values()));
            }
        } finally {
            isProcessing.set(false);
            processingLock.unlock();
        }
    }
}
