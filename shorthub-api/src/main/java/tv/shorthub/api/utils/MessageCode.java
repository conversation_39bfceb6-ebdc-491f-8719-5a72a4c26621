package tv.shorthub.api.utils;

import lombok.Getter;
import tv.shorthub.common.utils.MessageUtils;

@Getter
public enum MessageCode {
    SYSTEM_ERROR("system.error", "System Error"),
    USER_NOT_EXIST("user.not.exist", "User does not exist"),
    USER_NOT_LOGIN("user.not.login", "User not logged in"),
    ORDER_INVALID("order.invalid", "Invalid order"),
    NOT_PAY("not.pay", "Not paid"),
    INSUFFICIENT_BALANCE("insufficient.balance", "Insufficient balance"),
    UPDATE_MEMBER_ERROR("update.member.error", "Failed to update member"),
    PAYPAL_EXECUTE_PAYMENT_FAILED("paypal.execute.payment.failed" , "Payment failed"),
    PAYPAL_PAYMENT_FAILED("paypal.payment.failed", "PayPal payment failed"),
    PAYPAL_AUTH_FAILED("paypal.auth.failed", "PayPal authentication failed"),
    PAYPAL_QUERY_FAILED("paypal.query.failed", "PayPal query failed"),
    USER_EXIST("user.exist", "User already exists"),
    EMAIL_CANNOT_BE_EMPTY("email.cannot.be.empty", "Email cannot be empty"),
    PASSWORD_CANNOT_BE_EMPTY("password.cannot.be.empty", "Password cannot be empty"),
    INVALID_EMAIL_FORMAT("invalid.email.format", "Invalid email format"),
    ORDER_DUPLICATE_SUBSCRIPTION("order.duplicate.subscription", "You have already paid for a subscription. Please refresh the page."),
    PASSWORD_MUST_BE_AT_LEAST_6_CHARACTERS("password.must.be.at.least.6.characters", "Password must be at least 6 characters");

    private final String code;
    private final String defaultMessage;

    MessageCode(String code, String defaultMessage) {
        this.code = code;
        this.defaultMessage = defaultMessage;
    }

    public String getMessage(Object... args) {
        return MessageUtils.message(code, args);
    }
}
