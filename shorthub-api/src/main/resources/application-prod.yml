# 数据源配置
spring:
    datasource:
        type: com.alibaba.druid.pool.DruidDataSource
        driver-class-name: com.mysql.cj.jdbc.Driver
        url: **********************************************************************************************************************************************************************************************************
        username: root
        password: ShortHub2
        druid:
            # 初始连接数
            initial-size: 5
            # 最小连接池数量
            min-idle: 10
            # 最大连接池数量
            max-active: 20
            # 配置获取连接等待超时的时间
            max-wait: 60000
            # 配置连接超时时间
            connect-timeout: 30000
            # 配置网络超时时间
            socket-timeout: 60000
            # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
            time-between-eviction-runs-millis: 60000
            # 配置一个连接在池中最小生存的时间，单位是毫秒
            min-evictable-idle-time-millis: 300000
            # 配置一个连接在池中最大生存的时间，单位是毫秒
            max-evictable-idle-time-millis: 900000
            # 配置检测连接是否有效
            validation-query: SELECT 1 FROM DUAL
            test-while-idle: true
            test-on-borrow: false
            test-on-return: false
            web-stat-filter:
                enabled: true
            stat-view-servlet:
                enabled: true
                # 设置白名单，不填则允许所有访问
                allow:
                url-pattern: /druid/*
                # 控制台管理用户名和密码
                login-username: admin
                login-password: 123456
            filter:
                stat:
                    enabled: true
                    # 慢SQL记录
                    log-slow-sql: true
                    slow-sql-millis: 1000
                    merge-sql: true
                wall:
                    config:
                        multi-statement-allow: true
    # redis 配置
    data:
        redis:
            host: r-rj9xwy3hgezi624uoh.redis.rds.aliyuncs.com
            # 端口，默认为6379
            port: 6379
            # 数据库索引
            database: 0
            # 密码
            password: Short20251
            # 连接超时时间
            timeout: 30s
            lettuce:
                pool:
                    # 连接池中的最小空闲连接
                    min-idle: 0
                    # 连接池中的最大空闲连接
                    max-idle: 8
                    # 连接池的最大数据库连接数
                    max-active: 8
                    # #连接池最大阻塞等待时间（使用负值表示没有限制）
                    max-wait: -1ms
xxl:
    job:
        executor:
            logretentiondays: 30
            logpath: /app/docker/xxl/applogs/xxl-job/jobhandler
            port: 9999
            ip:
            appname: shorthub
        accessToken: default_token

        admin:
            addresses: http://*************:8220/xxl-job-admin

cloudflare:
    queue:
        enabled: true
        account-id: 921b1a87b997fb26a0b276ba9f7c37ce
        api-token: FqHcsPNN-M_N_q5p6eHtu1CR_Zb6c5TNUfrr0_p8
    r2:
        account-id: 921b1a87b997fb26a0b276ba9f7c37ce
        access:
            key:
                id: 385e5aa5fbc7451eb66ab07823775fb9
                secret: 22cr0pY51JyzujYMT8-weH3zUhzxzapFsTqur4W7
        url:
            endpoint: https://${cloudflare.r2.account-id}.r2.cloudflarestorage.com/
            bucket-of-sign: https://%s.${cloudflare.r2.account-id}.r2.cloudflarestorage.com/
        bucket:
            private:
                name: shorthub
                domain: https://video.shorthub.tv/
            public:
                name: shorthub-public
                domain: https://res.shorthub.tv/
# 钉钉机器人配置
dingtalk:
  webhooks:
    payment: https://connector.dingtalk.com/webhook/flow/10324bd655f80b5238500002

payermax:
    merchantNo: P01010119042165
    appId: d06223d7c6e74cdba13eabbdfa44ddb6
    privateKey: MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCQzhYx2UXB7PFBPfI0UMGiW7mro212Wr6JLSeWrZ4aN+WwygKd8JIXUjKIr/O/ZeiyMpbTRx3RGKlrC9ogDWn9cNBY2uZ3r8fKvXNAiPw4kLYemzjRLdmfcsvvay4NjZ3aoYaflXaPW4//3rOLoFe/ebVPbaGtmmJiwPs4CFthHaeLceZDeZO/cuYJXXVbmifEhy8lh+I4JhVt10tiGSLbSELoKlmnVdZcTLwo1mYFmE68RE1JeBoIxSPAF9062Vcc3V4mIrTyxsiGZFSRtdE79b0cckzLaIWJMiAC/iwCVPvsrcfKXfdw+Ms/w6VB0LjHT4/pVp1LCJlwkM8yT63vAgMBAAECggEATTWVn/65sy0jdiYRftOQa4F8/oelVfYMohfxg+YO5uvnDNypiI09WDH7v+YRkXxFFr7irMS+oT8FFo67P1vnp7r/XGFbgXW4keFyEnb4BTEj6i3Zna9U2i/bjU0YwSFBidGWdoC7NrUbktKr6UcVgoLiqfdKvcYTr5Q2XyoVjzHwu6MRInw1zsVJX65OANRMsCMEa4sEcAPGA9djjal4NwN6bJab6xs1t4LUFLDMdfmURLtGBQbi+x8t5s32iSakHXFtFxCV+Rdyy8PH19nb5N5atIaJcQ1kvGi7EpYIApFXA7KRS35C4hO12953WP8VmoDZ5Ltu56KDP3iTdZ8gAQKBgQDhnQ61iZmMBoCI76W5Cf6CVCuwNEdy+V5xVvpOjigE6kE5Gd0wiSNSULbdOGV7asZFrWCHnoeEW8i14Ys2tYtOPC9qRnhPmv7blgyvXRjvcX49yT+kNTxrljNQwnvSJ4rEiBLnnlLIaZr+noVnNL1ECgiNOBwsPkmK5eG21s+UzwKBgQCkTtQX1ejCuAuBZqwl79U3sS4Zj51jF1XHqs8aGEz4qcqpViW2s9SAoEUkKKtIF/aIIf/TQznnPlF2Q9q8Y1U0c77BLJOPeETKdCmNj1NcLIBormIHW92kMUFzY4x4npbsOHYmhBOEmBR7KvC9LQdknErYsCJiuhk7xcqelUTc4QKBgQCVjdn5RsxNAOZlX46YeWKHnCoVtKIEOf973C8ysZMfpvUzV8zC5rVOVIjTXhYPWLkz6PkLDXBBseH7hxYUXwWIB+daaHpKMrFseCvSd0EXQEFxzZztSgjSPI+pojIYHfQj0RvPA6lWhoKi9Av0XQZ1Or1ud1pdjbCMbVRRMlYKCwKBgHCcimavuDDEnTYaHYB+yN67s8HXGWKR2rvXSMw0vYRsMrcukPiQdeOWdOzcZtAphFE3JDQyThw8LO7mgKM/XWVust2I5LWfE17CkLwx8EsrQPL+Gbe7ccQX8ijYoK0G3J+X7vpk6U+mP9tOyeKOWxPMvsuWpGqP6714yW85FRJhAoGAZSWSSzReZF3xs2dzzxrl1QKa9tIDrhonptnybGZYC4XlAYmXzeTJJ7Tiq1YCzbR9WppvavnHlqM+3vvKt1koXyIWRTQ7WB69+uqUdMS7EA8lKMn+oVte62T1+VhQJlSwt5ClVoUMZhJ7AAXDxvg+buzNeG1v/hTi0N83TEwcQKc=
    publicKey: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAkM4WMdlFwezxQT3yNFDBolu5q6Ntdlq+iS0nlq2eGjflsMoCnfCSF1IyiK/zv2XosjKW00cd0RipawvaIA1p/XDQWNrmd6/Hyr1zQIj8OJC2Hps40S3Zn3LL72suDY2d2qGGn5V2j1uP/96zi6BXv3m1T22hrZpiYsD7OAhbYR2ni3HmQ3mTv3LmCV11W5onxIcvJYfiOCYVbddLYhki20hC6CpZp1XWXEy8KNZmBZhOvERNSXgaCMUjwBfdOtlXHN1eJiK08sbIhmRUkbXRO/W9HHJMy2iFiTIgAv4sAlT77K3Hyl33cPjLP8OlQdC4x0+P6VadSwiZcJDPMk+t7wIDAQAB
    gatewayUrl: https://pay-gate.payermax.com
    notifyUrl: ${shorthub.domainApi}/payermax/payment/notify
    returnUrl: ${shorthub.domainApi}/payment/result