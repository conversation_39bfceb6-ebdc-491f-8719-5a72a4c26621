#!/bin/bash

# 应用名称
APP_NAME="shorthub-api"
# 应用路径
APP_PATH=$(cd $(dirname $0); pwd)
# 应用端口
APP_PORT=8082
# 应用参数
APP_PARAM="-Dspring.profiles.active=prod -Dserver.port=$APP_PORT"
# 日志文件
APP_LOG_FILE="../logs/sys-info.log"
# JAR包路径
JAR_PATH="./${APP_NAME}.jar"
# JVM参数
JVM_PARAM="-javaagent:/app/arms/AliyunJavaAgent/aliyun-java-agent.jar -Darms.licenseKey=azw8yd69re@efe5e2014e279a9 -Darms.appName=shorthub-api -Xms4g -Xmx4g -Xmn1g -XX:MetaspaceSize=256m -XX:MaxMetaspaceSize=512m -XX:MaxDirectMemorySize=1g -XX:ReservedCodeCacheSize=256m -XX:+UseG1GC -XX:+PrintGCDetails -Xloggc:gc.log"
# PID文件
PID_FILE="../pid/${APP_NAME}_${APP_PORT}.pid"
# 启动成功关键字
STARTUP_KEYWORDS=("Application started successfully" "Started ShorthubApiApplication" "Tomcat started on port(s)")
# 最大等待时间（秒）
MAX_WAIT_TIME=120

# 确保日志和PID目录存在
mkdir -p ../logs
mkdir -p ../pid

# 检查进程是否在运行
check_running() {
    if [ -f "$PID_FILE" ]; then
        pid=$(cat "$PID_FILE")
        if ps -p "$pid" > /dev/null 2>&1; then
            return 0
        fi
    fi
    return 1
}

# 检查应用是否成功启动
check_startup() {
    local start_time=$(date +%s)
    local current_time=$start_time
    
    while [ $((current_time - start_time)) -lt $MAX_WAIT_TIME ]; do
        # 检查进程是否还在运行
        if ! check_running; then
            echo "Application process died unexpectedly"
            return 1
        fi
        
        # 检查端口是否已经监听
        if netstat -tuln | grep -q ":$APP_PORT "; then
            echo "Application is listening on port $APP_PORT"
            return 0
        fi
        
        # 检查日志中的启动关键字
        for keyword in "${STARTUP_KEYWORDS[@]}"; do
            if grep -q "$keyword" "$APP_LOG_FILE" 2>/dev/null; then
                echo "Found startup keyword: $keyword"
                return 0
            fi
        done
        
        sleep 2
        current_time=$(date +%s)
    done
    
    echo "Application startup timed out after $MAX_WAIT_TIME seconds"
    return 1
}

# 启动应用
start() {
    echo "Starting $APP_NAME..."
    nohup java $JVM_PARAM $APP_PARAM -jar $JAR_PATH > $APP_LOG_FILE 2>&1 &
    echo "java $JVM_PARAM $APP_PARAM -jar $JAR_PATH > $APP_LOG_FILE 2>&1 &"
    echo $! > $PID_FILE
    echo "$APP_NAME started with PID $(cat $PID_FILE)"

    # 等待应用启动
    if check_startup; then
        echo "$APP_NAME started successfully."
    else
        echo "$APP_NAME failed to start."
        exit 1
    fi
}

# 停止应用
stop() {
    echo "Stopping $APP_NAME..."
    if [ -f "$PID_FILE" ]; then
        pid=$(cat "$PID_FILE")
        if ps -p "$pid" > /dev/null 2>&1; then
            kill $pid
            rm -f "$PID_FILE"
            echo "$APP_NAME stopped."
        else
            echo "$APP_NAME is not running."
        fi
    else
        echo "$APP_NAME is not running."
    fi
}

# 重启应用
restart() {
    stop
    sleep 2
    start
}

# 查看状态
status() {
    if check_running; then
        echo "$APP_NAME is running with PID $(cat $PID_FILE)"
    else
        echo "$APP_NAME is not running"
    fi
}

# 主程序
case "$1" in
    start)
        start
        ;;
    stop)
        stop
        ;;
    restart)
        restart
        ;;
    status)
        status
        ;;
    *)
        echo "Usage: $0 {start|stop|restart|status}"
        exit 1
esac 
